# JavaScript AST节点处理详解

## 概述

UniMERNet的JavaScript处理器使用KaTeX解析LaTeX为AST，然后通过`groupTypes`对象中的处理函数将AST重新渲染为标准化的LaTeX字符串。每种AST节点类型都有专门的处理逻辑。

## 完整的AST节点类型处理

### 1. 基础字符节点

#### mathord (数学普通字符)
```javascript
// 处理: 变量、数字等数学字符
// 输入: {type: "mathord", value: "x", font: "normal"}
// 输出: "x "
// 特殊处理: mathrm字体中每个字符单独处理
```

#### textord (文本字符)
```javascript
// 处理: 普通文本字符
// 输入: {type: "textord", value: "a"}
// 输出: "a "
```

### 2. 运算符节点

#### bin (二元运算符)
```javascript
// 处理: +, -, *, ÷ 等二元运算符
// 输入: {type: "bin", value: "+"}
// 输出: "+ "
```

#### rel (关系运算符)
```javascript
// 处理: =, <, >, ≤, ≥ 等关系符号
// 输入: {type: "rel", value: "="}
// 输出: "= "
```

### 3. 括号和定界符节点

#### open/close (开闭括号)
```javascript
// 处理: (, [, { 等开括号和 ), ], } 等闭括号
// 输入: {type: "open", value: "("}
// 输出: "( "
```

#### leftright (自动调整大小的定界符)
```javascript
// 处理: \left( ... \right) 结构
// 输入: {left: "(", right: ")", body: [...]}
// 输出: "\left( 内容 \right) "
```

#### delimsizing (手动调整大小的定界符)
```javascript
// 处理: \big(, \Big[, \bigg{ 等
// 输入: {funcName: "\\big", value: "("}
// 输出: "\big( "
```

### 4. 结构化节点

#### ordgroup (有序组)
```javascript
// 处理: {...} 包围的表达式组
// 输入: {type: "ordgroup", value: [...]}
// 输出: "{ 内容 } "
```

#### supsub (上下标)
```javascript
// 处理: 上标和下标结构
// 输入: {base: node, sup: node, sub: node}
// 输出: "base ^ { sup } _ { sub }"
// 智能括号: 非组表达式自动添加{}
```

#### genfrac (广义分数)
```javascript
// 处理: 分数和二项式系数
// 输入: {numer: node, denom: node, hasBarLine: true/false}
// 输出: "\frac { 分子 } { 分母 }" 或 "\binom { 上 } { 下 }"
```

#### sqrt (根号)
```javascript
// 处理: 平方根和n次根
// 输入: {body: node, index: node}
// 输出: "\sqrt { expr }" 或 "\sqrt [ n ] { expr }"
```

### 5. 数组和矩阵节点

#### array (数组/矩阵)
```javascript
// 处理: 矩阵、表格等二维结构
// 输入: {cols: [...], body: [[cell, cell], [cell, cell]]}
// 输出: "\begin{array} { l c r } a & b & c \\ d & e & f \end{array}"
// 特殊处理:
// - 自动生成列对齐 (默认左对齐)
// - 清理空的{} 组
// - 处理行列分隔符
```

### 6. 字体和样式节点

#### font (字体)
```javascript
// 处理: \mathbf, \mathrm, \mathcal 等字体命令
// 输入: {font: "mathbf", body: node}
// 输出: "\mathbf 内容"
// 特殊处理: mbox和hbox统一为mathrm
```

#### text (文本模式)
```javascript
// 处理: \text{...} 文本块
// 输入: {body: [...]}
// 输出: "\mathrm { 内容 }"
```

#### styling (数学样式)
```javascript
// 处理: \displaystyle, \textstyle 等样式命令
// 输入: {original: "\\displaystyle", value: [...]}
// 输出: " \displaystyle 内容"
```

#### sizing (大小调整)
```javascript
// 处理: \tiny, \small, \large 等大小命令
// 输入: {original: "\\large", value: [...]}
// 输出: " \large 内容"
// 特殊处理: \rm转换为\mathrm
```

### 7. 装饰节点

#### accent (重音符号)
```javascript
// 处理: \hat, \tilde, \bar 等重音符号
// 输入: {accent: "\\hat", base: node}
// 输出: "\hat { base }" 或 "\hat base"
// 智能括号: 非组表达式自动添加{}
```

#### overline/underline (上下划线)
```javascript
// 处理: \overline{...} 和 \underline{...}
// 输入: {body: node}
// 输出: "\overline { 内容 }" 或 "\underline { 内容 }"
```

### 8. 操作符节点

#### op (操作符)
```javascript
// 处理: 求和、积分、极限等大型操作符
// 输入: {symbol: true/false, body: "∑", limits: true/false}
// 输出: "∑ " 或 "\operatorname { name }"
// 区分: 符号操作符直接输出，命名操作符用\operatorname包装
```

### 9. 间距和布局节点

#### spacing (间距)
```javascript
// 处理: 各种间距命令
// 输入: {value: " "} 或 {value: "\\,"}
// 输出: "~ " 或 "\, "
// 特殊处理: 普通空格转换为不换行空格~
```

#### rule (规则线)
```javascript
// 处理: \rule{宽度}{高度} 线条
// 输入: {width: {number: 1, unit: "pt"}, height: {number: 2, unit: "pt"}}
// 输出: "\rule { 1 pt } { 2 pt }"
```

#### phantom (幻影)
```javascript
// 处理: \phantom{...} 占位但不显示
// 输入: {value: [...]}
// 输出: "\phantom { 内容 }"
```

#### llap/rlap (重叠)
```javascript
// 处理: 左右重叠布局
// 输入: {body: node}
// 输出: "\llap 内容" 或 "\rlap 内容"
```

### 10. 特殊节点

#### color (颜色)
```javascript
// 处理: 颜色命令 (在LaTeX规范化中很少使用)
// 输入: {color: "red", value: [...]}
// 输出: MathML节点 (不常用于LaTeX输出)
```

#### katex (KaTeX标识)
```javascript
// 处理: KaTeX标识符 (通常不在规范化中使用)
// 输出: MathML文本节点
```

## 核心处理函数

### buildExpression (表达式构建)
```javascript
// 功能: 遍历AST节点数组，逐个处理
// 输入: AST节点数组 + 渲染选项
// 输出: 累积到全局norm_str变量
```

### buildGroup (节点分发)
```javascript
// 功能: 根据节点类型调用相应的groupTypes处理函数
// 输入: 单个AST节点 + 渲染选项
// 输出: 调用对应处理函数更新norm_str
```

## 处理特点

1. **递归处理**: 复杂节点递归调用buildGroup/buildExpression
2. **智能括号**: 自动为需要的表达式添加{}括号
3. **空格规范**: 所有输出后都添加空格分隔
4. **类型统一**: 将不同写法统一为标准形式
5. **结构保持**: 保持LaTeX的逻辑结构完整性

这套AST处理系统确保了从任意LaTeX输入到标准化输出的一致性转换。
