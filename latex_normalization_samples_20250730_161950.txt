================================================================================
LaTeX规范化处理结果对比
生成时间: 2025-07-30 16:20:54
总样本数: 1000
发生变化的样本: 857
未发生变化的样本: 143
================================================================================

样本 1: UniMER-1M_0000308
------------------------------------------------------------
输入:
1 / k _ { L } w _ { 0 } \gg \omega _ { p } ^ { 2 } / \omega _ { L } ^ { 2 }

输出:
1/k_{L} w_{0} \gg \omega_{p}^{2}/\omega_{L}^{2}

状态: 已规范化 ✓
================================================================================

样本 2: UniMER-1M_0002034
------------------------------------------------------------
输入:
d = 2 0 . 3 6 2 0 0 0 + 2 9 . 5 3 0 5 8 8 8 6 1 \times N + 1 0 2 . 0 2 6 \times 1 0 ^ { - 1 2 } \times N ^ { 2 }

输出:
d=2 0.3 6 2 0 0 0+2 9.5 3 0 5 8 8 8 6 1 \times N+1 0 2.0 2 6 \times 1 0^{-1 2} \times N^{2}

状态: 已规范化 ✓
================================================================================

样本 3: UniMER-1M_0004042
------------------------------------------------------------
输入:
\mathbb { C }

输出:
\mathbb{C}

状态: 已规范化 ✓
================================================================================

样本 4: UniMER-1M_0006094
------------------------------------------------------------
输入:
t _ { \textrm { s } } ^ { * } = 4

输出:
t_{\textrm{s}}^{*}=4

状态: 已规范化 ✓
================================================================================

样本 5: UniMER-1M_0008110
------------------------------------------------------------
输入:
| { \vec { h } } | = \sqrt { h _ { x } ^ { 2 } + h _ { y } ^ { 2 } + h _ { z } ^ { 2 } }

输出:
|{\vec{h}} |=\sqrt{h_{x}^{2}+h_{y}^{2}+h_{z}^{2}}

状态: 已规范化 ✓
================================================================================

样本 6: UniMER-1M_0010085
------------------------------------------------------------
输入:

状态: 已规范化 ✓
================================================================================

样本 7: UniMER-1M_0012129
------------------------------------------------------------
输入:
\left[ \nabla ^ { 2 } + \left( \frac { n ^ { 2 } \omega ^ { 2 } } { c ^ { 2 } } - \mu _ { \gamma } ^ { 2 } \right) \right] { \bf A } = \left( 1 - n ^ { 2 } \right) \nabla \left( \nabla \cdot { \bf A } \right) \, ,

输出:
\left[\nabla^{2}+\left(\frac{n^{2} \omega^{2}}{c^{2}}-\mu_{\gamma}^{2} \right) \right]{\bf A}=\left(1-n^{2} \right) \nabla \left(\nabla \cdot{\bf A} \right) \,,

状态: 已规范化 ✓
================================================================================

样本 8: UniMER-1M_0014189
------------------------------------------------------------
输入:
\sqrt { n }

输出:
\sqrt{n}

状态: 已规范化 ✓
================================================================================

样本 9: UniMER-1M_0016147
------------------------------------------------------------
输入:
\Gamma _ { 2 , \epsilon } ^ { \mathrm { O s c } , \theta }

输出:
\Gamma_{2,\epsilon}^{\mathrm{Osc},\theta}

状态: 已规范化 ✓
================================================================================

样本 10: UniMER-1M_0018201
------------------------------------------------------------
输入:
\sim 1 . 3 \times 1 0 ^ { 1 2 }

输出:
\sim 1.3 \times 1 0^{1 2}

状态: 已规范化 ✓
================================================================================

样本 11: UniMER-1M_0020121
------------------------------------------------------------
输入:
\begin{array} { r l } & { \mathbb { E } _ { t } \left[ \mathcal { L } ( \omega ^ { t + 1 } ) \; \middle | \; u ^ { t + 1 } \right] - \mathcal { L } ( \omega ^ { t } ) } \\ & { = - \delta _ { \omega } ^ { t } \mathbb { R } \left( \mathbb { E } _ { t } \left[ \left\langle \nabla _ { \omega } \mathcal { L } ( \omega ^ { t } ) , \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \right\rangle \; \middle | \; u ^ { t + 1 } \right] \right) + \frac { L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } } { 2 } \mathbb { E } _ { t } \left[ \| \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \| _ { 2 } ^ { 2 } \; \middle | \; u ^ { t + 1 } \right] } \\ & { = - \delta _ { \omega } ^ { t } \mathbb { R } \left( \left\langle \nabla _ { \omega } \mathcal { L } ( \omega ^ { t } ) , \mathbb { E } _ { t } \left[ \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \; \middle | \; u ^ { t + 1 } \right] \right\rangle \right) + \frac { L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } } { 2 } \mathbb { E } _ { t } \left[ \| \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \| _ { 2 } ^ { 2 } \; \middle | \; u ^ { t + 1 } \right] } \\ & { \leq \frac { - 2 \delta _ { \omega } ^ { t } + L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } } { 2 } \| \nabla _ { \omega } \mathcal { L } ( \omega ^ { t } ) \| _ { 2 } ^ { 2 } + \frac { L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } \sigma ^ { 2 } } { 2 } . } \end{array}

输出:
\begin{array}{rl} &{{\mathbb{E}_{t} \left[\mathcal{L}(\omega^{t+1}) \;\middle | \;u^{t+1} \right]-\mathcal{L}(\omega^{t})}} \&{{=-\delta_{\omega}^{t} \mathbb{R} \left(\mathbb{E}_{t} \left[\left\langle \nabla_{\omega} \mathcal{L}(\omega^{t}),\tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \right\rangle \;\middle | \;u^{t+1} \right] \right)+\frac{L_{\omega}(\delta_{\omega}^{t})^{2}}{2} \mathbb{E}_{t} \left[\| \tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \|_{2}^{2} \;\middle | \;u^{t+1} \right]}} \&{{=-\delta_{\omega}^{t} \mathbb{R} \left(\left\langle \nabla_{\omega} \mathcal{L}(\omega^{t}),\mathbb{E}_{t} \left[\tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \;\middle | \;u^{t+1} \right] \right\rangle \right)+\frac{L_{\omega}(\delta_{\omega}^{t})^{2}}{2} \mathbb{E}_{t} \left[\| \tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \|_{2}^{2} \;\middle | \;u^{t+1} \right]}} \&{{\leq \frac{-2 \delta_{\omega}^{t}+L_{\omega}(\delta_{\omega}^{t})^{2}}{2} \| \nabla_{\omega} \mathcal{L}(\omega^{t}) \|_{2}^{2}+\frac{L_{\omega}(\delta_{\omega}^{t})^{2} \sigma^{2}}{2}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 12: UniMER-1M_0022248
------------------------------------------------------------
输入:
D ( R _ { 1 } , R _ { 2 } ) = W _ { 1 } ( f _ { R _ { 1 } } , f _ { R _ { 2 } } ) .

输出:
D(R_{1},R_{2})=W_{1}(f_{R_{1}},f_{R_{2}}).

状态: 已规范化 ✓
================================================================================

样本 13: UniMER-1M_0024242
------------------------------------------------------------
输入:
x = - R

输出:
x=-R

状态: 已规范化 ✓
================================================================================

样本 14: UniMER-1M_0026279
------------------------------------------------------------
输入:
v _ { T }

输出:
v_{T}

状态: 已规范化 ✓
================================================================================

样本 15: UniMER-1M_0028277
------------------------------------------------------------
输入:
k _ { 2 }

输出:
k_{2}

状态: 已规范化 ✓
================================================================================

样本 16: UniMER-1M_0030355
------------------------------------------------------------
输入:
v

输出:
v

状态: 无变化
================================================================================

样本 17: UniMER-1M_0032370
------------------------------------------------------------
输入:
\begin{array} { r l } { H / \hbar = } & { { } \ \omega _ { c } c ^ { \dagger } c + \omega _ { m } m ^ { \dagger } m + \frac { \omega _ { a } } { 2 } S _ { z } + \frac { \omega _ { b } } { 2 } \left( q ^ { 2 } + p ^ { 2 } \right) } \end{array}

输出:
\begin{array}{rl}{{H/\hbar=}} &{{ \omega_{c} c^{\dagger} c+\omega_{m} m^{\dagger} m+\frac{\omega_{a}}{2} S_{z}+\frac{\omega_{b}}{2} \left(q^{2}+p^{2} \right)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 18: UniMER-1M_0034342
------------------------------------------------------------
输入:
r _ { i } ^ { c r o p } = \frac { N _ { { \mathrm { s h o c k } } , i } ^ { c r o p } } { N _ { \mathrm { I d e n t i f y } } }

输出:
r_{i}^{c r o p}=\frac{N_{{\mathrm{shock}},i}^{c r o p}}{N_{\mathrm{Identify}}}

状态: 已规范化 ✓
================================================================================

样本 19: UniMER-1M_0036372
------------------------------------------------------------
输入:
\begin{array} { r l } { p _ { 1 } } & { = x + y , p _ { 2 } = x - y , } \\ { z _ { x } } & { \le x ^ { 2 } , z _ { y } \le y ^ { 2 } , } \\ { z _ { p _ { 1 } } } & { \ge p _ { 1 } ^ { 2 } , z _ { p _ { 2 } } \ge p _ { 2 } ^ { 2 } , } \\ { z } & { \le z _ { x } + z _ { y } - z _ { p _ { 1 } } , \, z \ge z _ { p _ { 2 } } - z _ { x } - z _ { y } . } \end{array}

输出:
\begin{array}{rl}{{p_{1}}} &{{=x+y,p_{2}=x-y,}} \{{z_{x}}} &{{\le x^{2},z_{y} \le y^{2},}} \{{z_{p_{1}}}} &{{\ge p_{1}^{2},z_{p_{2}} \ge p_{2}^{2},}} \{{z}} &{{\le z_{x}+z_{y}-z_{p_{1}},\,z \ge z_{p_{2}}-z_{x}-z_{y}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 20: UniMER-1M_0038391
------------------------------------------------------------
输入:
\mathcal { M } _ { \mathcal { K } } t _ { n - 1 }

输出:
\mathcal{M}_{\mathcal{K}} t_{n-1}

状态: 已规范化 ✓
================================================================================

样本 21: UniMER-1M_0040441
------------------------------------------------------------
输入:
R _ { e }

输出:
R_{e}

状态: 已规范化 ✓
================================================================================

样本 22: UniMER-1M_0042389
------------------------------------------------------------
输入:
\begin{array} { r l } { r _ { 3 } ( t _ { 0 } + \varepsilon ) = } & { \varepsilon ^ { 3 / 2 } \sum _ { i \in S _ { 1 } } \sum _ { { ( j _ { 1 } , j _ { 2 } ) \in S _ { 2 } } } [ f _ { i } , f _ { j _ { 1 } } ] ( x ^ { 0 } , t _ { 0 } ) a _ { i } ( x ^ { 0 } , t _ { 0 } ) \sqrt { \frac { | a _ { j _ { 1 } j _ { 2 } } ( x ^ { 0 } , t _ { 0 } ) | } { \pi \kappa _ { j _ { 1 } j _ { 2 } } } } } \\ & { + \frac { \varepsilon ^ { 2 } } { 2 } \sum _ { i _ { 1 } , i _ { 2 } \in S _ { 1 } } L _ { f _ { i _ { 2 } } } f _ { i _ { 1 } } ( x ^ { 0 } , t _ { 0 } ) a _ { i 1 } ( x ^ { 0 } , t _ { 0 } ) a _ { i 2 } ( x ^ { 0 } , t _ { 0 } ) . } \end{array}

输出:
\begin{array}{rl}{{r_{3}(t_{0}+\varepsilon)=}} &{{\varepsilon^{3/2} \sum_{i \in S_{1}} \sum_{{(j_{1},j_{2}) \in S_{2}}}[f_{i},f_{j_{1}}](x^{0},t_{0}) a_{i}(x^{0},t_{0}) \sqrt{\frac{| a_{j_{1} j_{2}}(x^{0},t_{0}) |}{\pi \kappa_{j_{1} j_{2}}}}}} \&{{+\frac{\varepsilon^{2}}{2} \sum_{i_{1},i_{2} \in S_{1}} L_{f_{i_{2}}} f_{i_{1}}(x^{0},t_{0}) a_{i 1}(x^{0},t_{0}) a_{i 2}(x^{0},t_{0}).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 23: UniMER-1M_0044491
------------------------------------------------------------
输入:
^ { \circ }

输出:
^{\circ}

状态: 已规范化 ✓
================================================================================

样本 24: UniMER-1M_0046496
------------------------------------------------------------
输入:
\begin{array} { r l } { - \frac { \epsilon } { 2 } \int _ { \mathcal { D } _ { \epsilon } } \partial _ { R } \bigl ( W _ { \epsilon } ( 1 + \epsilon R ) \bigr ) \tilde { \zeta } ^ { 2 } \, \mathrm { d } X \, } & { = \, - \frac { \epsilon ^ { 2 } } { 2 } \int _ { \mathcal { D } _ { \epsilon } } W _ { \epsilon } \tilde { \zeta } ^ { 2 } \, \mathrm { d } X - \frac { \epsilon } { 2 } \int _ { \mathcal { D } _ { \epsilon } } \bigl ( \partial _ { R } W _ { \epsilon } \bigr ) \tilde { \zeta } \tilde { \eta } \, \mathrm { d } X } \\ { \, } & { \le \, - \frac { \epsilon ^ { 2 } } { 4 } \int _ { \mathcal { D } _ { \epsilon } } W _ { \epsilon } \tilde { \zeta } ^ { 2 } \, \mathrm { d } X + \frac { 1 } { 4 } \int _ { \mathcal { D } _ { \epsilon } } \frac { ( \partial _ { R } W _ { \epsilon } ) ^ { 2 } } { W _ { \epsilon } } \, \tilde { \eta } ^ { 2 } \, \mathrm { d } X \, , } \end{array}

输出:
\begin{array}{rl}{{-\frac{\epsilon}{2} \int_{\mathcal{D}_{\epsilon}} \partial_{R} \bigl(W_{\epsilon}(1+\epsilon R) \bigr) \tilde{\zeta}^{2} \,\mathrm{d} X \,}} &{{=\,-\frac{\epsilon^{2}}{2} \int_{\mathcal{D}_{\epsilon}} W_{\epsilon} \tilde{\zeta}^{2} \,\mathrm{d} X-\frac{\epsilon}{2} \int_{\mathcal{D}_{\epsilon}} \bigl(\partial_{R} W_{\epsilon} \bigr) \tilde{\zeta} \tilde{\eta} \,\mathrm{d} X}} \{{\,}} &{{\le \,-\frac{\epsilon^{2}}{4} \int_{\mathcal{D}_{\epsilon}} W_{\epsilon} \tilde{\zeta}^{2} \,\mathrm{d} X+\frac{1}{4} \int_{\mathcal{D}_{\epsilon}} \frac{(\partial_{R} W_{\epsilon})^{2}}{W_{\epsilon}} \,\tilde{\eta}^{2} \,\mathrm{d} X \,,}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 25: UniMER-1M_0048562
------------------------------------------------------------
输入:
n _ { y } , n _ { z } \in \mathbb { N } _ { > 0 }

输出:
n_{y},n_{z} \in \mathbb{N}_{>0}

状态: 已规范化 ✓
================================================================================

样本 26: UniMER-1M_0050569
------------------------------------------------------------
输入:
\begin{array} { r } { K ( t , f , \dot { \mathrm { H } } ^ { s , p } ( \mathbb { R } _ { + } ^ { n } ) , \dot { \mathrm { D } } _ { p } ^ { s } ( \mathring { \Delta } _ { \mathcal { J } } ) ) \leqslant \lVert a \rVert _ { \dot { \mathrm { H } } ^ { s , p } ( \mathbb { R } _ { + } ^ { n } ) } + t \lVert { \Delta } _ { \mathcal { J } } b \rVert _ { \dot { \mathrm { H } } ^ { s , p } ( \mathbb { R } _ { + } ^ { n } ) } \mathrm { . ~ } } \end{array}

输出:
\begin{array}{r}{{K(t,f,\dot{\mathrm{H}}^{s,p}(\mathbb{R}_{+}^{n}),\dot{\mathrm{D}}_{p}^{s}(\mathring{\Delta}_{\mathcal{J}})) \leqslant \lVert a \rVert_{\dot{\mathrm{H}}^{s,p}(\mathbb{R}_{+}^{n})}+t \lVert{\Delta}_{\mathcal{J}} b \rVert_{\dot{\mathrm{H}}^{s,p}(\mathbb{R}_{+}^{n})} \mathrm{.~}}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 27: UniMER-1M_0052628
------------------------------------------------------------
输入:
J _ { \phi }

输出:
J_{\phi}

状态: 已规范化 ✓
================================================================================

样本 28: UniMER-1M_0054534
------------------------------------------------------------
输入:
\begin{array} { r l } { \bar { U } _ { n } } & { = R _ { n } \tilde { U } _ { n } = \left( \left[ \begin{array} { l l l } { 0 _ { 4 \times 4 } } & { 0 _ { 4 \times 4 } } & { \left[ \begin{array} { l l l l } { 0 } & { 0 } & { 0 } & { 1 } \\ { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 1 } & { 0 } \\ { 0 } & { 1 } & { 0 } & { 0 } \end{array} \right] } \\ { \left[ \begin{array} { l l l l } { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 1 } & { 0 } \\ { 0 } & { 1 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 1 } \end{array} \right] } & { 0 _ { 4 \times 4 } } & { 0 _ { 4 \times 4 } } \\ { 0 _ { 4 \times 4 } } & { \left[ \begin{array} { l l l l } { 0 } & { 1 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 1 } \\ { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 1 } & { 0 } \end{array} \right] } & { 0 _ { 4 \times 4 } } \end{array} \right] + \alpha _ { n } ^ { \ell } P _ { \alpha _ { n } } ( \ell ) \right) \times \left[ \begin{array} { l } { 0 } \\ { 0 } \\ { U _ { n } ^ { [ 2 , 2 ] } } \\ { 0 } \\ { U _ { n } ^ { [ 3 , 3 ] } } \\ { 0 } \\ { 0 } \\ { 0 } \\ { U _ { n } ^ { [ 2 , 1 ] } } \\ { 0 } \\ { 0 } \\ { 0 } \end{array} \right] } \\ & { = [ 0 , U _ { n } ^ { [ 2 , 1 ] } , 0 , 0 , 0 , U _ { n } ^ { [ 2 , 2 ] } , 0 , 0 , 0 , 0 , U _ { n } ^ { [ 3 , 3 ] } , 0 ] ^ { T } + P _ { \alpha _ { n } } ( 2 \ell ) } \\ & { = \left[ 0 , \sum _ { i = 1 } ^ { \ell } \frac { 1 } { \alpha _ { n } ^ { i } } \Delta _ { i } ^ { [ 2 , 1 ] } , 0 , 0 , 0 , \sum _ { i = 1 } ^ { \ell } \frac { 1 } { \alpha _ { n } ^ { i } } \Delta _ { i } ^ { [ 2 , 2 ] } , 0 , 0 , 0 , 0 , \sum _ { i = 1 } ^ { \ell } \frac { 1 } { \alpha _ { n } ^ { i } } \Delta _ { i } ^ { [ 3 , 3 ] } , 0 \right] ^ { T } + P _ { \alpha _ { n } } ( 2 \ell ) } \end{array}

输出:
\begin{array}{rl}{{\bar{U}_{n}}} &{{=R_{n} \tilde{U}_{n}=\left(\left[\begin{array}{lll}{{0_{4 \times 4}}} &{{0_{4 \times 4}}} &{{\left[\begin{array}{llll}{{0}} &{{0}} &{{0}} &{{1}} \{{1}} &{{0}} &{{0}} &{{0}} \{{0}} &{{0}} &{{1}} &{{0}} \{{0}} &{{1}} &{{0}} &{{0}} \end{array} \right]}} \{{\left[\begin{array}{llll}{{1}} &{{0}} &{{0}} &{{0}} \{{0}} &{{0}} &{{1}} &{{0}} \{{0}} &{{1}} &{{0}} &{{0}} \{{0}} &{{0}} &{{0}} &{{1}} \end{array} \right]}} &{{0_{4 \times 4}}} &{{0_{4 \times 4}}} \{{0_{4 \times 4}}} &{{\left[\begin{array}{llll}{{0}} &{{1}} &{{0}} &{{0}} \{{0}} &{{0}} &{{0}} &{{1}} \{{1}} &{{0}} &{{0}} &{{0}} \{{0}} &{{0}} &{{1}} &{{0}} \end{array} \right]}} &{{0_{4 \times 4}}} \end{array} \right]+\alpha_{n}^{\ell} P_{\alpha_{n}}(\ell) \right) \times \left[\begin{array}{l}{{0}} \{{0}} \{{U_{n}^{[2,2]}}} \{{0}} \{{U_{n}^{[3,3]}}} \{{0}} \{{0}} \{{0}} \{{U_{n}^{[2,1]}}} \{{0}} \{{0}} \{{0}} \end{array} \right]}} \&{{=[0,U_{n}^{[2,1]},0,0,0,U_{n}^{[2,2]},0,0,0,0,U_{n}^{[3,3]},0]^{T}+P_{\alpha_{n}}(2 \ell)}} \&{{=\left[0,\sum_{i=1}^{\ell} \frac{1}{\alpha_{n}^{i}} \Delta_{i}^{[2,1]},0,0,0,\sum_{i=1}^{\ell} \frac{1}{\alpha_{n}^{i}} \Delta_{i}^{[2,2]},0,0,0,0,\sum_{i=1}^{\ell} \frac{1}{\alpha_{n}^{i}} \Delta_{i}^{[3,3]},0 \right]^{T}+P_{\alpha_{n}}(2 \ell)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 29: UniMER-1M_0056704
------------------------------------------------------------
输入:
\omega

输出:
\omega

状态: 无变化
================================================================================

样本 30: UniMER-1M_0058724
------------------------------------------------------------
输入:
d = 2

输出:
d=2

状态: 已规范化 ✓
================================================================================

样本 31: UniMER-1M_0060729
------------------------------------------------------------
输入:
\mathbb { E } _ { 0 } = \frac { 1 } { 2 } \| u _ { 0 } \| _ { L ^ { 2 } } ^ { 2 }

输出:
\mathbb{E}_{0}=\frac{1}{2} \| u_{0} \|_{L^{2}}^{2}

状态: 已规范化 ✓
================================================================================

样本 32: UniMER-1M_0062767
------------------------------------------------------------
输入:
y = 0

输出:
y=0

状态: 已规范化 ✓
================================================================================

样本 33: UniMER-1M_0064765
------------------------------------------------------------
输入:
\mathrm { I n d }

输出:
\mathrm{Ind}

状态: 已规范化 ✓
================================================================================

样本 34: UniMER-1M_0066840
------------------------------------------------------------
输入:
e _ { \mathrm { x } } = - { \frac { ( v _ { \mathrm { x f } } - r \omega _ { \mathrm { f } } ) - ( u _ { \mathrm { x f } } - R \Omega _ { \mathrm { f } } ) } { ( v _ { \mathrm { x i } } - r \omega _ { \mathrm { i } } ) - ( u _ { \mathrm { x i } } - R \Omega _ { \mathrm { i } } ) } } ,

输出:
e_{\mathrm{x}}=-{\frac{(v_{\mathrm{xf}}-r \omega_{\mathrm{f}})-(u_{\mathrm{xf}}-R \Omega_{\mathrm{f}})}{(v_{\mathrm{xi}}-r \omega_{\mathrm{i}})-(u_{\mathrm{xi}}-R \Omega_{\mathrm{i}})}},

状态: 已规范化 ✓
================================================================================

样本 35: UniMER-1M_0068833
------------------------------------------------------------
输入:
\begin{array} { r l } & { \frac { \omega _ { p j } ^ { 2 } p _ { \perp } ^ { 2 } \overleftrightarrow { \mathbf { S } } f _ { j } } { \left[ p _ { \parallel } - \frac { m _ { j } ( \omega - n \Omega _ { j } ) } { k _ { \parallel } } \right] \omega ^ { 2 } } } \\ & { = \frac { \omega _ { p j } ^ { 2 } p _ { \perp } ^ { 2 } \overleftrightarrow { \mathbf { S } } f _ { j } \left[ p _ { \parallel } + \frac { m _ { j } ( \omega + n \Omega _ { j } ) } { k _ { \parallel } } \right] } { \left[ p _ { \parallel } - \frac { m _ { j } ( \omega - n \Omega _ { j } ) } { k _ { \parallel } } \right] \left[ p _ { \parallel } + \frac { m _ { j } ( \omega + n \Omega _ { j } ) } { k _ { \parallel } } \right] \omega ^ { 2 } } . } \end{array}

输出:
\begin{array}{rl} &{{\frac{\omega_{p j}^{2} p_{\perp}^{2} \overleftrightarrow{\mathbf{S}} f_{j}}{\left[p_{\parallel}-\frac{m_{j}(\omega-n \Omega_{j})}{k_{\parallel}} \right] \omega^{2}}}} \&{{=\frac{\omega_{p j}^{2} p_{\perp}^{2} \overleftrightarrow{\mathbf{S}} f_{j} \left[p_{\parallel}+\frac{m_{j}(\omega+n \Omega_{j})}{k_{\parallel}} \right]}{\left[p_{\parallel}-\frac{m_{j}(\omega-n \Omega_{j})}{k_{\parallel}} \right] \left[p_{\parallel}+\frac{m_{j}(\omega+n \Omega_{j})}{k_{\parallel}} \right] \omega^{2}}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 36: UniMER-1M_0070855
------------------------------------------------------------
输入:
\mathsf { A } = I _ { 1 } \dot { \theta } _ { 1 } + I _ { 2 } \dot { \theta } _ { 2 } \, ,

输出:
\mathsf{A}=I_{1} \dot{\theta}_{1}+I_{2} \dot{\theta}_{2} \,,

状态: 已规范化 ✓
================================================================================

样本 37: UniMER-1M_0072906
------------------------------------------------------------
输入:
S

输出:
S

状态: 无变化
================================================================================

样本 38: UniMER-1M_0074932
------------------------------------------------------------
输入:
1 . 6 \times 1 0 ^ { - 9 }

输出:
1.6 \times 1 0^{-9}

状态: 已规范化 ✓
================================================================================

样本 39: UniMER-1M_0076951
------------------------------------------------------------
输入:
i

输出:
i

状态: 无变化
================================================================================

样本 40: UniMER-1M_0078974
------------------------------------------------------------
输入:
x _ { \star }

输出:
x_{\star}

状态: 已规范化 ✓
================================================================================

样本 41: UniMER-1M_0081000
------------------------------------------------------------
输入:
1 2

输出:
1 2

状态: 无变化
================================================================================

样本 42: UniMER-1M_0082981
------------------------------------------------------------
输入:
3 a ^ { 2 } b ^ { 3 } + 5 a ^ { 3 } b ^ { 2 } - \frac { a ^ { 5 } b ^ { 8 } } { 2 }

输出:
3 a^{2} b^{3}+5 a^{3} b^{2}-\frac{a^{5} b^{8}}{2}

状态: 已规范化 ✓
================================================================================

样本 43: UniMER-1M_0085022
------------------------------------------------------------
输入:
{ \cal Z } ( s ) = { \cal N } \int D B D B ^ { \dagger } e ^ { - \left( S ( B , B ^ { \dagger } ) \; + \; \int d ^ { 3 } x s _ { \mu } J ^ { \mu } \right) \; } .

输出:
{\cal Z}(s)={\cal N} \int D B D B^{\dagger} e^{-\left(S(B,B^{\dagger}) \;+\;\int d^{3} x s_{\mu} J^{\mu} \right) \;}.

状态: 已规范化 ✓
================================================================================

样本 44: UniMER-1M_0087081
------------------------------------------------------------
输入:
\delta \psi _ { A \vert \mu } = \mathrm { d e r i v a t i v e ~ t e r m s } \, + \, S _ { A B } \left( \phi \right) \, \gamma _ { \mu } \, \epsilon ^ { B } ~ ,

输出:
\delta \psi_{A \vert \mu}=\mathrm{derivative~terms} \,+\,S_{A B} \left(\phi \right) \,\gamma_{\mu} \,\epsilon^{B} ~,

状态: 已规范化 ✓
================================================================================

样本 45: UniMER-1M_0089099
------------------------------------------------------------
输入:
\Sigma _ { e } \geq \Lambda _ { e } ^ { - } \qquad \mathrm { a n d } \qquad 1 - \Sigma _ { \mu } \geq \Lambda _ { \mu } ^ { - } \, ,

输出:
\Sigma_{e} \geq \Lambda_{e}^{-} \qquad \mathrm{and} \qquad 1-\Sigma_{\mu} \geq \Lambda_{\mu}^{-} \,,

状态: 已规范化 ✓
================================================================================

样本 46: UniMER-1M_0091078
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathbf { f } ( \lambda ) } & { = \left( \begin{array} { l } { f _ { 1 } ( \lambda ) } \\ { f _ { 2 } ( \lambda ) } \end{array} \right) = \left( \begin{array} { l } { D _ { \nu } \left( \sqrt { 2 } ( \lambda + x ) \right) } \\ { - D _ { \nu - 1 } \left( \sqrt { 2 } ( \lambda + x ) \right) } \end{array} \right) , } \\ { \mathbf { h } ( \lambda ) } & { = \left( \begin{array} { l } { h _ { 1 } ( \lambda ) } \\ { h _ { 2 } ( \lambda ) } \end{array} \right) = \gamma \left( \begin{array} { l } { D _ { \nu - 1 } \left( \sqrt { 2 } ( \lambda + x ) \right) } \\ { D _ { \nu } \left( \sqrt { 2 } ( \lambda + x ) \right) } \end{array} \right) , } \end{array}

输出:
\begin{array}{rl}{{\mathbf{f}(\lambda)}} &{{=\left(\begin{array}{l}{{f_{1}(\lambda)}} \{{f_{2}(\lambda)}} \end{array} \right)=\left(\begin{array}{l}{{D_{\nu} \left(\sqrt{2}(\lambda+x) \right)}} \{{-D_{\nu-1} \left(\sqrt{2}(\lambda+x) \right)}} \end{array} \right),}} \{{\mathbf{h}(\lambda)}} &{{=\left(\begin{array}{l}{{h_{1}(\lambda)}} \{{h_{2}(\lambda)}} \end{array} \right)=\gamma \left(\begin{array}{l}{{D_{\nu-1} \left(\sqrt{2}(\lambda+x) \right)}} \{{D_{\nu} \left(\sqrt{2}(\lambda+x) \right)}} \end{array} \right),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 47: UniMER-1M_0093164
------------------------------------------------------------
输入:
^ *

输出:
^*

状态: 已规范化 ✓
================================================================================

样本 48: UniMER-1M_0095178
------------------------------------------------------------
输入:
\begin{array} { r l r } { \hat { \mathcal { M } } _ { 3 } ( \delta \phi ) } & { { } \equiv } & { \hat { \mathcal { M } } ( { \bf \hat { n } _ { 3 } } , \delta \phi ) } \end{array}

输出:
\begin{array}{rlr}{{\hat{\mathcal{M}}_{3}(\delta \phi)}} &{{ \equiv}} &{{\hat{\mathcal{M}}({\bf \hat{n}_{3}},\delta \phi)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 49: UniMER-1M_0097224
------------------------------------------------------------
输入:
2 . 1 K

输出:
2.1 K

状态: 已规范化 ✓
================================================================================

样本 50: UniMER-1M_0099191
------------------------------------------------------------
输入:
x _ { 3 } ( t )

输出:
x_{3}(t)

状态: 已规范化 ✓
================================================================================

样本 51: UniMER-1M_0101241
------------------------------------------------------------
输入:
\begin{array} { r } { \begin{array} { r } { d { \mathbf { A } } ^ { * } = \mathcal { F } ( { \mathbf { A } } ^ { * } ) d t ^ { * } + d { \mathbf { F } } ^ { * } . } \end{array} } \end{array}

输出:
\begin{array}{r}{{\begin{array}{r}{{d{\mathbf{A}}^{*}=\mathcal{F}({\mathbf{A}}^{*}) d t^{*}+d{\mathbf{F}}^{*}.}} \end{array}}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 52: UniMER-1M_0103282
------------------------------------------------------------
输入:
\mathrm { C a } = { \frac { \mu V } { \gamma } }

输出:
\mathrm{Ca}={\frac{\mu V}{\gamma}}

状态: 已规范化 ✓
================================================================================

样本 53: UniMER-1M_0105301
------------------------------------------------------------
输入:
( m \delta _ { I J } - \Theta _ { \alpha \beta } \mathcal { A } _ { I } ^ { \alpha } \mathcal { A } _ { J } ^ { \beta } ) \ddot { r } ^ { J } - \frac { 1 } { R } \Theta _ { 3 \beta } \mathcal { A } _ { K } ^ { \beta } \dot { r } ^ { K } \mathcal { A } _ { J } ^ { \bar { I } } \dot { r } ^ { J } = 0 , \ \ I = 4 , 5

输出:
(m \delta_{I J}-\Theta_{\alpha \beta} \mathcal{A}_{I}^{\alpha} \mathcal{A}_{J}^{\beta}) \ddot{r}^{J}-\frac{1}{R} \Theta_{3 \beta} \mathcal{A}_{K}^{\beta} \dot{r}^{K} \mathcal{A}_{J}^{\bar{I}} \dot{r}^{J}=0,I=4,5

状态: 已规范化 ✓
================================================================================

样本 54: UniMER-1M_0107352
------------------------------------------------------------
输入:
\sum _ { l = 0 } ^ { \infty } \beta _ { l } x ^ { l } = \zeta ( x ) \exp { ( - x ^ { 2 } / 4 ) } ,

输出:
\sum_{l=0}^{\infty} \beta_{l} x^{l}=\zeta(x) \mathrm{exp}{(-x^{2}/4)},

状态: 已规范化 ✓
================================================================================

样本 55: UniMER-1M_0109353
------------------------------------------------------------
输入:
\alpha _ { f }

输出:
\alpha_{f}

状态: 已规范化 ✓
================================================================================

样本 56: UniMER-1M_0111373
------------------------------------------------------------
输入:
f

输出:
f

状态: 无变化
================================================================================

样本 57: UniMER-1M_0113441
------------------------------------------------------------
输入:
G

输出:
G

状态: 无变化
================================================================================

样本 58: UniMER-1M_0115440
------------------------------------------------------------
输入:
\varepsilon _ { 2 } = 1 6

输出:
\varepsilon_{2}=1 6

状态: 已规范化 ✓
================================================================================

样本 59: UniMER-1M_0117423
------------------------------------------------------------
输入:
\frac { \delta } { \delta \phi _ { 1 } ( \textbf { x } ) } \mathcal { S } _ { g y } ^ { p } \circ \hat { \chi } ( \textbf { x } ) = \int \frac { \delta } { \delta \phi _ { 1 } ( \textbf { x } ) } d \Omega _ { g y } d t \mathcal { H } F _ { e } .

输出:
\frac{\delta}{\delta \phi_{1}(\textbf{x})} \mathcal{S}_{g y}^{p} \circ \hat{\chi}(\textbf{x})=\int \frac{\delta}{\delta \phi_{1}(\textbf{x})} d \Omega_{g y} d t \mathcal{H} F_{e}.

状态: 已规范化 ✓
================================================================================

样本 60: UniMER-1M_0119489
------------------------------------------------------------
输入:
{ \frac { P _ { 1 } } { T _ { 1 } } } = { \frac { P _ { 2 } } { T _ { 2 } } } \qquad { \mathrm { o r } } \qquad P _ { 1 } T _ { 2 } = P _ { 2 } T _ { 1 } .

输出:
{\frac{P_{1}}{T_{1}}}={\frac{P_{2}}{T_{2}}} \qquad{\mathrm{or}} \qquad P_{1} T_{2}=P_{2} T_{1}.

状态: 已规范化 ✓
================================================================================

样本 61: UniMER-1M_0121501
------------------------------------------------------------
输入:
\Phi _ { n } ( i x ) = i ^ { n } \Psi _ { n } ( x )

输出:
\Phi_{n}(i x)=i^{n} \Psi_{n}(x)

状态: 已规范化 ✓
================================================================================

样本 62: UniMER-1M_0123503
------------------------------------------------------------
输入:
D

输出:
D

状态: 无变化
================================================================================

样本 63: UniMER-1M_0125568
------------------------------------------------------------
输入:
q < 1

输出:
q<1

状态: 已规范化 ✓
================================================================================

样本 64: UniMER-1M_0127598
------------------------------------------------------------
输入:
\Psi _ { \{ N _ { \mathrm { ~ \bf ~ k ~ } \alpha } \} } ( \{ Q _ { \mathrm { ~ \bf ~ k ~ } \alpha } \} ) = \prod _ { \mathrm { ~ \bf ~ k ~ } \alpha } \psi _ { N _ { \mathrm { ~ \bf ~ k ~ } \alpha } } ( Q _ { \mathrm { ~ \bf ~ k ~ } \alpha } ) \; \; \; , \; \; \; N _ { \mathrm { ~ \bf ~ k ~ } \alpha } = 0 , 1 , 2 , 3 , . . .

输出:
\Psi_{\{N_{\mathrm{~\bf~k~} \alpha} \}}(\{Q_{\mathrm{~\bf~k~} \alpha} \})=\prod_{\mathrm{~\bf~k~} \alpha} \psi_{N_{\mathrm{~\bf~k~} \alpha}}(Q_{\mathrm{~\bf~k~} \alpha}) \;\;\;,\;\;\;N_{\mathrm{~\bf~k~} \alpha}=0,1,2,3,...

状态: 已规范化 ✓
================================================================================

样本 65: UniMER-1M_0129579
------------------------------------------------------------
输入:
T ^ { \mu \nu }

输出:
T^{\mu \nu}

状态: 已规范化 ✓
================================================================================

样本 66: UniMER-1M_0131683
------------------------------------------------------------
输入:
c \neq 0

输出:
c \neq 0

状态: 无变化
================================================================================

样本 67: UniMER-1M_0133714
------------------------------------------------------------
输入:
7 \%

输出:
7 \

状态: 已规范化 ✓
================================================================================

样本 68: UniMER-1M_0135685
------------------------------------------------------------
输入:
{ \bf A } _ { 2 } ( { \bf r } ) = { \bf E } ^ { ( 2 ) } ( { \bf r } ) / | { \bf E } ^ { ( 2 ) } ( { \bf r } ) |

输出:
{\bf A}_{2}({\bf r})={\bf E}^{(2)}({\bf r})/|{\bf E}^{(2)}({\bf r}) |

状态: 已规范化 ✓
================================================================================

样本 69: UniMER-1M_0137743
------------------------------------------------------------
输入:
T _ { X \rightarrow Y } = \sum { p ( y _ { i + 1 } , y ^ { i } , x _ { i } ) \log \frac { p ( y _ { i + 1 } | y ^ { i } , x _ { i } ) } { p ( y _ { i + 1 } | y ^ { i } ) } } ,

输出:
T_{X \rightarrow Y}=\sum{p(y_{i+1},y^{i},x_{i}) \mathrm{log} \frac{p(y_{i+1} | y^{i},x_{i})}{p(y_{i+1} | y^{i})}},

状态: 已规范化 ✓
================================================================================

样本 70: UniMER-1M_0139772
------------------------------------------------------------
输入:
1 5 \%

输出:
1 5 \

状态: 已规范化 ✓
================================================================================

样本 71: UniMER-1M_0141776
------------------------------------------------------------
输入:
\mu

输出:
\mu

状态: 无变化
================================================================================

样本 72: UniMER-1M_0143805
------------------------------------------------------------
输入:
Q _ { m - 1 / 2 } \left( z \right)

输出:
Q_{m-1/2} \left(z \right)

状态: 已规范化 ✓
================================================================================

样本 73: UniMER-1M_0145794
------------------------------------------------------------
输入:
I _ { j } ^ { 2 } = I _ { \infty } ^ { 2 } + \operatorname* { m a x } _ { i } \left( \frac { A _ { w } } { A } I _ { i } ^ { + } \right) ^ { 2 } ,

输出:
I_{j}^{2}=I_{\infty}^{2}+\mathrm{max}_{i} \left(\frac{A_{w}}{A} I_{i}^{+} \right)^{2},

状态: 已规范化 ✓
================================================================================

样本 74: UniMER-1M_0147839
------------------------------------------------------------
输入:
\xi ^ { 1 0 * } \left( x \right) = \pi _ { \psi } \left( x \right)

输出:
\xi^{1 0*} \left(x \right)=\pi_{\psi} \left(x \right)

状态: 已规范化 ✓
================================================================================

样本 75: UniMER-1M_0149837
------------------------------------------------------------
输入:
\begin{array} { r } { \vec { E } ( x , y ) = E _ { 0 } ( \hat { x } \cos { \theta } + \hat { y } \sin { \theta } ) , } \end{array}

输出:
\begin{array}{r}{{\vec{E}(x,y)=E_{0}(\hat{x} \mathrm{cos}{\theta}+\hat{y} \mathrm{sin}{\theta}),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 76: UniMER-1M_0151882
------------------------------------------------------------
输入:
\tau _ { 1 }

输出:
\tau_{1}

状态: 已规范化 ✓
================================================================================

样本 77: UniMER-1M_0153884
------------------------------------------------------------
输入:
\bar { R } = \sum _ { k = 1 } ^ { M } p _ { k } \operatorname* { m a x } \{ 0 , R _ { k } \} ,

输出:
\bar{R}=\sum_{k=1}^{M} p_{k} \mathrm{max} \{0,R_{k} \},

状态: 已规范化 ✓
================================================================================

样本 78: UniMER-1M_0155936
------------------------------------------------------------
输入:
l

输出:
l

状态: 无变化
================================================================================

样本 79: UniMER-1M_0157924
------------------------------------------------------------
输入:
\infty ,

输出:
\infty,

状态: 已规范化 ✓
================================================================================

样本 80: UniMER-1M_0159981
------------------------------------------------------------
输入:
g _ { \mathrm { t a r g e t } } ^ { \mathrm { ( m i n ) } } = \lambda _ { \mathrm { b } } v ^ { + } + d

输出:
g_{\mathrm{target}}^{\mathrm{(min)}}=\lambda_{\mathrm{b}} v^{+}+d

状态: 已规范化 ✓
================================================================================

样本 81: UniMER-1M_0162031
------------------------------------------------------------
输入:
| u | = 2

输出:
| u |=2

状态: 已规范化 ✓
================================================================================

样本 82: UniMER-1M_0163984
------------------------------------------------------------
输入:
\boldsymbol { \mathrm J }

输出:
\boldsymbol{\mathrm{J}}

状态: 已规范化 ✓
================================================================================

样本 83: UniMER-1M_0166089
------------------------------------------------------------
输入:
I = ( 0 , \; 1 ) .

输出:
I=(0,\;1).

状态: 已规范化 ✓
================================================================================

样本 84: UniMER-1M_0168106
------------------------------------------------------------
输入:
\times _ { \mathrm { ~ n ~ e ~ w ~ } }

输出:
\times_{\mathrm{~n~e~w~}}

状态: 已规范化 ✓
================================================================================

样本 85: UniMER-1M_0170113
------------------------------------------------------------
输入:
L _ { i }

输出:
L_{i}

状态: 已规范化 ✓
================================================================================

样本 86: UniMER-1M_0172120
------------------------------------------------------------
输入:
< r _ { V } ^ { 2 } > = \frac { 6 } { \pi } \int _ { 4 m _ { \pi } ^ { 2 } } ^ { \infty } \frac { I m V ( z ) d z } { z ^ { 2 } }

输出:
<r_{V}^{2}>=\frac{6}{\pi} \int_{4 m_{\pi}^{2}}^{\infty} \frac{I m V(z) d z}{z^{2}}

状态: 已规范化 ✓
================================================================================

样本 87: UniMER-1M_0174122
------------------------------------------------------------
输入:
m \le 3

输出:
m \le 3

状态: 无变化
================================================================================

样本 88: UniMER-1M_0176173
------------------------------------------------------------
输入:
\left( T _ { 1 } , T _ { 2 } , \cdots T _ { n } \right)

输出:
\left(T_{1},T_{2},. . . T_{n} \right)

状态: 已规范化 ✓
================================================================================

样本 89: UniMER-1M_0178199
------------------------------------------------------------
输入:
q _ { x } ( 0 ) = q _ { y } ( 0 ) = 0 . 6 5

输出:
q_{x}(0)=q_{y}(0)=0.6 5

状态: 已规范化 ✓
================================================================================

样本 90: UniMER-1M_0180239
------------------------------------------------------------
输入:
K _ { \mu } ( k ) = \frac { \exp [ i a k _ { \mu } - 1 ] } { i a }

输出:
K_{\mu}(k)=\frac{\mathrm{exp}[i a k_{\mu}-1]}{i a}

状态: 已规范化 ✓
================================================================================

样本 91: UniMER-1M_0182247
------------------------------------------------------------
输入:
t / t _ { \mathrm { t a n k } }

输出:
t/t_{\mathrm{tank}}

状态: 已规范化 ✓
================================================================================

样本 92: UniMER-1M_0184124
------------------------------------------------------------
输入:
\begin{array} { r l } { d V } & { { } = ( \partial _ { \sigma } x \cdot ( \partial _ { s _ { 1 } } x \times \partial _ { s _ { 2 } } x ) ) \, d \sigma \, d s _ { 1 } \, d s _ { 2 } , } \end{array}

输出:
\begin{array}{rl}{{d V}} &{{=(\partial_{\sigma} x \cdot(\partial_{s_{1}} x \times \partial_{s_{2}} x)) \,d \sigma \,d s_{1} \,d s_{2},}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 93: UniMER-1M_0186324
------------------------------------------------------------
输入:
I { = } 6 { \times } 1 0 ^ { 1 4 } \; \mathrm { W } \ \mathrm { c m } ^ { - 2 }

输出:
I{=} 6{\times} 1 0^{1 4} \;\mathrm{W} \mathrm{cm}^{-2}

状态: 已规范化 ✓
================================================================================

样本 94: UniMER-1M_0188272
------------------------------------------------------------
输入:
\times \, \frac { \omega } { 2 i \pi \hbar \sin \omega \Delta t } \, e ^ { - i \frac { \pi } { 2 } | \ell | } \, e ^ { \frac { i \omega } { 2 \hbar } \frac { \cos \omega \Delta t } { \sin \omega \Delta t } ( r _ { f } ^ { 2 } + r _ { i } ^ { 2 } ) } \, J _ { | \ell | } \left( \frac { \omega r _ { f } \, r _ { i } } { \hbar \sin \omega \Delta t } \right) \ \ \ .

输出:
\times \,\frac{\omega}{2 i \pi \hbar \mathrm{sin} \omega \Delta t} \,e^{-i \frac{\pi}{2} | \ell |} \,e^{\frac{i \omega}{2 \hbar} \frac{\mathrm{cos} \omega \Delta t}{\mathrm{sin} \omega \Delta t}(r_{f}^{2}+r_{i}^{2})} \,J_{| \ell |} \left(\frac{\omega r_{f} \,r_{i}}{\hbar \mathrm{sin} \omega \Delta t} \right).

状态: 已规范化 ✓
================================================================================

样本 95: UniMER-1M_0190351
------------------------------------------------------------
输入:
T _ { \lambda \mu \nu } ^ { A V V } = 2 \varepsilon _ { \lambda \mu \nu \xi } ( k _ { 1 } + k _ { 2 } ) _ { \sigma } ( \triangle _ { \xi \sigma } ) + N A T

输出:
T_{\lambda \mu \nu}^{A V V}=2 \varepsilon_{\lambda \mu \nu \xi}(k_{1}+k_{2})_{\sigma}(\triangle_{\xi \sigma})+N A T

状态: 已规范化 ✓
================================================================================

样本 96: UniMER-1M_0192380
------------------------------------------------------------
输入:
\left( \bigcup _ { i \in I } A _ { i } \right) ^ { 0 } = \bigcap _ { i \in I } A _ { i } ^ { 0 } .

输出:
\left(\bigcup_{i \in I} A_{i} \right)^{0}=\bigcap_{i \in I} A_{i}^{0}.

状态: 已规范化 ✓
================================================================================

样本 97: UniMER-1M_0194411
------------------------------------------------------------
输入:
- R = 2 \alpha ^ { 2 } \left( - N ( \psi ^ { \prime } ) ^ { 2 } - { \frac { 4 } { x ^ { 2 } } } F ^ { - 1 } E _ { V } - { \frac { 2 } { x ^ { 2 } } } E _ { H } \right) \ .

输出:
-R=2 \alpha^{2} \left(-N(\psi^{\prime})^{2}-{\frac{4}{x^{2}}} F^{-1} E_{V}-{\frac{2}{x^{2}}} E_{H} \right).

状态: 已规范化 ✓
================================================================================

样本 98: UniMER-1M_0196412
------------------------------------------------------------
输入:
p

输出:
p

状态: 无变化
================================================================================

样本 99: UniMER-1M_0198432
------------------------------------------------------------
输入:
k = g = 0 . 8 7 \pm 0 . 0 2

输出:
k=g=0.8 7 \pm 0.0 2

状态: 已规范化 ✓
================================================================================

样本 100: UniMER-1M_0200465
------------------------------------------------------------
输入:
O ( q _ { c } ^ { 2 } ) \sim O ( N ^ { - 2 / 3 } )

输出:
O(q_{c}^{2}) \sim O(N^{-2/3})

状态: 已规范化 ✓
================================================================================

样本 101: UniMER-1M_0202461
------------------------------------------------------------
输入:
P _ { e e } ^ { \mathrm { J S } } \simeq 1 - \sin ^ { 2 } 2 \omega \sin ^ { 2 } ( \pi L / L _ { \mathrm { o s c } } ) \ .

输出:
P_{e e}^{\mathrm{JS}} \simeq 1-\mathrm{sin}^{2} 2 \omega \mathrm{sin}^{2}(\pi L/L_{\mathrm{osc}}).

状态: 已规范化 ✓
================================================================================

样本 102: UniMER-1M_0204519
------------------------------------------------------------
输入:
K n

输出:
K n

状态: 无变化
================================================================================

样本 103: UniMER-1M_0206453
------------------------------------------------------------
输入:
\begin{array} { r l r } { o _ { r } ( x , y ) } & { = } & { \textrm { R e L U } ( W _ { 3 , r } \star \textrm { R e L U } ( W _ { 2 , r } \star \textrm { R e L U } ( W _ { 1 , r } \star v _ { r } ( x , y ) ) ) ) } \\ { o _ { a } ( x , y ) } & { = } & { \textrm { R e L U } ( W _ { 3 , a } \star \textrm { R e L U } ( W _ { 2 , a } \star \textrm { R e L U } ( W _ { 1 , a } \star \{ s ( x , y ) , c ( x , y ) \} ) ) ) } \\ { \pi ( x , y ) } & { = } & { \sigma ( W _ { 3 } \star \textrm { R e L U } ( W _ { 2 } \star \textrm { R e L U } ( W _ { 1 } \star \{ o _ { r } ( x , y ) , o _ { a } ( x , y ) \} ) ) ) } \end{array}

输出:
\begin{array}{rlr}{{o_{r}(x,y)}} &{{=}} &{{\textrm{R e L U}(W_{3,r} \star \textrm{R e L U}(W_{2,r} \star \textrm{R e L U}(W_{1,r} \star v_{r}(x,y))))}} \{{o_{a}(x,y)}} &{{=}} &{{\textrm{R e L U}(W_{3,a} \star \textrm{R e L U}(W_{2,a} \star \textrm{R e L U}(W_{1,a} \star \{s(x,y),c(x,y) \})))}} \{{\pi(x,y)}} &{{=}} &{{\sigma(W_{3} \star \textrm{R e L U}(W_{2} \star \textrm{R e L U}(W_{1} \star \{o_{r}(x,y),o_{a}(x,y) \})))}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 104: UniMER-1M_0208557
------------------------------------------------------------
输入:
h ( x , t ) = Z ( t ) + \theta ( t ) x ,

输出:
h(x,t)=Z(t)+\theta(t) x,

状态: 已规范化 ✓
================================================================================

样本 105: UniMER-1M_0210566
------------------------------------------------------------
输入:
i = 0 , 1 , \cdots , 2 9

输出:
i=0,1,. . .,2 9

状态: 已规范化 ✓
================================================================================

样本 106: UniMER-1M_0212623
------------------------------------------------------------
输入:
- { A ^ { \alpha ; \beta } } _ { ; \beta } + { R ^ { \alpha } } _ { \beta } A ^ { \beta } = 0

输出:
-{A^{\alpha;\beta}}_{;\beta}+{R^{\alpha}}_{\beta} A^{\beta}=0

状态: 已规范化 ✓
================================================================================

样本 107: UniMER-1M_0214630
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathbb { N } } & { = \hat { p } _ { 0 , \psi } \mathbb { N } ^ { p _ { 0 } } + \hat { T } _ { 0 , \psi } \mathbb { N } ^ { T _ { 0 } } , } \\ { \mathbb { T } } & { = \hat { p } _ { 0 , \psi } \mathbb { T } ^ { p _ { 0 } } + \hat { T } _ { 0 , \psi } \mathbb { T } ^ { T _ { 0 } } , } \\ { \mathbb { U } } & { = \hat { p } _ { 0 , \psi } \mathbb { U } ^ { p _ { 0 } } + \hat { T } _ { 0 , \psi } \mathbb { U } ^ { T _ { 0 } } , } \end{array}

输出:
\begin{array}{rl}{{\mathbb{N}}} &{{=\hat{p}_{0,\psi} \mathbb{N}^{p_{0}}+\hat{T}_{0,\psi} \mathbb{N}^{T_{0}},}} \{{\mathbb{T}}} &{{=\hat{p}_{0,\psi} \mathbb{T}^{p_{0}}+\hat{T}_{0,\psi} \mathbb{T}^{T_{0}},}} \{{\mathbb{U}}} &{{=\hat{p}_{0,\psi} \mathbb{U}^{p_{0}}+\hat{T}_{0,\psi} \mathbb{U}^{T_{0}},}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 108: UniMER-1M_0216693
------------------------------------------------------------
输入:
1 . 0 5

输出:
1.0 5

状态: 已规范化 ✓
================================================================================

样本 109: UniMER-1M_0218713
------------------------------------------------------------
输入:
1 0 4 . 6

输出:
1 0 4.6

状态: 已规范化 ✓
================================================================================

样本 110: UniMER-1M_0220710
------------------------------------------------------------
输入:
- E _ { i } ( - z ) = \int _ { z } ^ { \infty } \! \! d t \, { \mathrm e } ^ { - t } t ^ { - 1 } = \int _ { 1 } ^ { \infty } \! \! d t { \mathrm e } ^ { - t z } t ^ { - 1 } \ ,

输出:
-E_{i}(-z)=\int_{z}^{\infty} \! \! d t \,{\mathrm{e}}^{-t} t^{-1}=\int_{1}^{\infty} \! \! d t{\mathrm{e}}^{-t z} t^{-1},

状态: 已规范化 ✓
================================================================================

样本 111: UniMER-1M_0222692
------------------------------------------------------------
输入:
r _ { \delta } ( x ) = x / ( x + \delta ) \quad ,

输出:
r_{\delta}(x)=x/(x+\delta) \quad,

状态: 已规范化 ✓
================================================================================

样本 112: UniMER-1M_0224772
------------------------------------------------------------
输入:
\partial _ { m } J _ { i j } = - \frac { 2 } { x ^ { 2 } } ( x _ { m } J _ { i j } - x _ { m } \delta _ { i j } + x _ { i } \delta _ { j m } + x _ { j } \delta _ { i m } ) ,

输出:
\partial_{m} J_{i j}=-\frac{2}{x^{2}}(x_{m} J_{i j}-x_{m} \delta_{i j}+x_{i} \delta_{j m}+x_{j} \delta_{i m}),

状态: 已规范化 ✓
================================================================================

样本 113: UniMER-1M_0226807
------------------------------------------------------------
输入:
\looparrowright

输出:
\looparrowright

状态: 无变化
================================================================================

样本 114: UniMER-1M_0228804
------------------------------------------------------------
输入:
\lambda = \frac { 2 \pi } { k } e ^ { P \tau + h / 2 } = \frac { \pi P } { k } e ^ { - 2 P \tau } l _ { z } .

输出:
\lambda=\frac{2 \pi}{k} e^{P \tau+h/2}=\frac{\pi P}{k} e^{-2 P \tau} l_{z}.

状态: 已规范化 ✓
================================================================================

样本 115: UniMER-1M_0230789
------------------------------------------------------------
输入:
\begin{array} { r } { \beta _ { 3 i + 2 } = \left\{ \begin{array} { l l } { ( s ( \alpha _ { 3 i + 1 } ) \oplus \beta _ { 3 i } ) \cdot a _ { 3 i + 2 } } & { \mathrm { i f ~ } | \alpha _ { 3 i + 1 } | \geq } \\ & { | \alpha _ { 3 i + 2 } | } \\ { ( s ( \alpha _ { 3 i + 2 } ) \oplus \beta _ { 3 i } \oplus \beta _ { 3 i + 1 } ) \cdot a _ { 3 i + 2 } } & { \mathrm { o t h e r w i s e , } } \end{array} \right. } \end{array}

输出:
\begin{array}{r}{{\beta_{3 i+2}=\left\{\begin{array}{ll}{{(s(\alpha_{3 i+1}) \oplus \beta_{3 i}) \cdot a_{3 i+2}}} &{{\mathrm{if~} | \alpha_{3 i+1} | \geq}} \&{{| \alpha_{3 i+2} |}} \{{(s(\alpha_{3 i+2}) \oplus \beta_{3 i} \oplus \beta_{3 i+1}) \cdot a_{3 i+2}}} &{{\mathrm{otherwise,}}} \end{array} \right.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 116: UniMER-1M_0232814
------------------------------------------------------------
输入:
\begin{array} { r l } { \langle f , \mathbf { p } _ { \chi } ^ { \prime } | \Delta H _ { \chi T } | i , \mathbf { p } _ { \chi } \rangle } & { \equiv \! \int \! \frac { d ^ { 3 } \mathbf { q } } { ( 2 \pi ) ^ { 3 } } \, \langle \mathbf { p } _ { \chi } ^ { \prime } | \mathcal { O } _ { \chi } ( \mathbf { q } ) | \mathbf { p } _ { \chi } \rangle \times \langle f | \mathcal { O } _ { T } ( \mathbf { q } ) | i \rangle } \\ & { = \frac { 1 } { V } \sqrt { \frac { \pi \bar { \sigma } ( q ) } { \mu _ { \chi } ^ { 2 } } } \langle f | \mathcal { O } _ { T } ( \mathbf { q } ) | i \rangle , } \end{array}

输出:
\begin{array}{rl}{{\langle f,\mathbf{p}_{\chi}^{\prime} | \Delta H_{\chi T} | i,\mathbf{p}_{\chi} \rangle}} &{{\equiv \! \int \! \frac{d^{3} \mathbf{q}}{(2 \pi)^{3}} \,\langle \mathbf{p}_{\chi}^{\prime} | \mathcal{O}_{\chi}(\mathbf{q}) | \mathbf{p}_{\chi} \rangle \times \langle f | \mathcal{O}_{T}(\mathbf{q}) | i \rangle}} \&{{=\frac{1}{V} \sqrt{\frac{\pi \bar{\sigma}(q)}{\mu_{\chi}^{2}}} \langle f | \mathcal{O}_{T}(\mathbf{q}) | i \rangle,}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 117: UniMER-1M_0234928
------------------------------------------------------------
输入:
M

输出:
M

状态: 无变化
================================================================================

样本 118: UniMER-1M_0236931
------------------------------------------------------------
输入:
V

输出:
V

状态: 无变化
================================================================================

样本 119: UniMER-1M_0238924
------------------------------------------------------------
输入:
I _ { j , m } \left( \varphi \right) = - T r \ln \left( - i \partial _ { \mu } \gamma _ { \mu } + r m r + r \varphi _ { a } \Gamma _ { a } r \right) - \frac 1 2 \varphi \left( V - j \right) ^ { - 1 } \varphi

输出:
I_{j,m} \left(\varphi \right)=-T r \mathrm{ln} \left(-i \partial_{\mu} \gamma_{\mu}+r m r+r \varphi_{a} \Gamma_{a} r \right)-\frac{1}{2} \varphi \left(V-j \right)^{-1} \varphi

状态: 已规范化 ✓
================================================================================

样本 120: UniMER-1M_0240951
------------------------------------------------------------
输入:
G = \frac { 1 } { 2 } \left[ \frac { \mathbb { C } _ { 1 1 } - \mathbb { C } _ { 1 2 } + 3 \mathbb { C } _ { 4 4 } } { 5 } + \frac { 5 \mathbb { C } _ { 4 4 } \left( \mathbb { C } _ { 1 1 } - \mathbb { C } _ { 1 2 } \right) } { 4 \mathbb { C } _ { 4 4 } + 3 \left( \mathbb { C } _ { 1 1 } - \mathbb { C } _ { 1 2 } \right) } \right]

输出:
G=\frac{1}{2} \left[\frac{\mathbb{C}_{1 1}-\mathbb{C}_{1 2}+3 \mathbb{C}_{4 4}}{5}+\frac{5 \mathbb{C}_{4 4} \left(\mathbb{C}_{1 1}-\mathbb{C}_{1 2} \right)}{4 \mathbb{C}_{4 4}+3 \left(\mathbb{C}_{1 1}-\mathbb{C}_{1 2} \right)} \right]

状态: 已规范化 ✓
================================================================================

样本 121: UniMER-1M_0242981
------------------------------------------------------------
输入:
^ 6

输出:
^6

状态: 已规范化 ✓
================================================================================

样本 122: UniMER-1M_0244986
------------------------------------------------------------
输入:
U _ { \lambda } = { \bf C } \cdot { \bf 1 } \oplus [ U _ { \lambda } , U _ { \lambda } ] ,

输出:
U_{\lambda}={\bf C} \cdot{\bf 1} \oplus[U_{\lambda},U_{\lambda}],

状态: 已规范化 ✓
================================================================================

样本 123: UniMER-1M_0247021
------------------------------------------------------------
输入:
R = 0

输出:
R=0

状态: 已规范化 ✓
================================================================================

样本 124: UniMER-1M_0249039
------------------------------------------------------------
输入:
N _ { + }

输出:
N_{+}

状态: 已规范化 ✓
================================================================================

样本 125: UniMER-1M_0251026
------------------------------------------------------------
输入:
\begin{array} { r l } { \left\| \mathrm { t r _ { 0 } } { \mathcal { V } } \right\| _ { L ^ { 2 } ( \Omega ) } ^ { 2 } } & { \lesssim \int _ { 0 } ^ { y _ { 0 } } y ^ { \alpha } \| \nabla \mathcal { V } ( y ) \| _ { L ^ { 2 } ( \Omega ) } ^ { 2 } d y } \\ & { \qquad + \int _ { \Omega } \int _ { 0 } ^ { y _ { 0 } } y ^ { 2 + 3 \alpha } | \partial _ { y } \chi | ^ { 2 } \mathcal { U } ^ { 2 } d y d x + \int _ { \Omega } \int _ { 0 } ^ { y _ { 0 } } y ^ { \alpha } \Big | \int _ { y } ^ { \mathcal { Y } } { \tau ^ { \alpha } \mathcal { U } ( x , \tau ) \, d \tau } \Big | ^ { 2 } d y d x . } \end{array}

输出:
\begin{array}{rl}{{\left\| \mathrm{tr_{0}}{\mathcal{V}} \right\|_{L^{2}(\Omega)}^{2}}} &{{\lesssim \int_{0}^{y_{0}} y^{\alpha} \| \nabla \mathcal{V}(y) \|_{L^{2}(\Omega)}^{2} d y}} \&{{\qquad+\int_{\Omega} \int_{0}^{y_{0}} y^{2+3 \alpha} | \partial_{y} \chi |^{2} \mathcal{U}^{2} d y d x+\int_{\Omega} \int_{0}^{y_{0}} y^{\alpha} \Big| \int_{y}^{\mathcal{Y}}{\tau^{\alpha} \mathcal{U}(x,\tau) \,d \tau} \Big|^{2} d y d x.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 126: UniMER-1M_0253097
------------------------------------------------------------
输入:
4 0 9 6

输出:
4 0 9 6

状态: 无变化
================================================================================

样本 127: UniMER-1M_0255114
------------------------------------------------------------
输入:
K _ { \perp \theta } = 0 . 0 2 K _ { \parallel } f _ { \perp \theta } .

输出:
K_{\perp \theta}=0.0 2 K_{\parallel} f_{\perp \theta}.

状态: 已规范化 ✓
================================================================================

样本 128: UniMER-1M_0257150
------------------------------------------------------------
输入:
\sigma _ { k } ( - x , x , x _ { 1 } , \ldots , x _ { n } ) = \sigma _ { k } ( x _ { 1 } , \ldots , x _ { n } ) - x ^ { 2 } \sigma _ { k - 2 } ( x _ { 1 } , \ldots , x _ { n } ) \, \, ,

输出:
\sigma_{k}(-x,x,x_{1},. . .,x_{n})=\sigma_{k}(x_{1},. . .,x_{n})-x^{2} \sigma_{k-2}(x_{1},. . .,x_{n}) \,\,,

状态: 已规范化 ✓
================================================================================

样本 129: UniMER-1M_0259144
------------------------------------------------------------
输入:
\Pi _ { i j } ^ { \mu \nu } = - i g ^ { \mu \nu } ( A _ { i j } + q ^ { 2 } F _ { i j } ( q ^ { 2 } ) ) + q ^ { \mu } q ^ { \nu } \ \mathrm { t e r m s } ,

输出:
\Pi_{i j}^{\mu \nu}=-i g^{\mu \nu}(A_{i j}+q^{2} F_{i j}(q^{2}))+q^{\mu} q^{\nu} \mathrm{terms},

状态: 已规范化 ✓
================================================================================

样本 130: UniMER-1M_0261181
------------------------------------------------------------
输入:
B = 4

输出:
B=4

状态: 已规范化 ✓
================================================================================

样本 131: UniMER-1M_0263206
------------------------------------------------------------
输入:
[ \sqrt { f } ]

输出:
[\sqrt{f}]

状态: 已规范化 ✓
================================================================================

样本 132: UniMER-1M_0265189
------------------------------------------------------------
输入:
\tilde { \nu } _ { t _ { 3 } t _ { 4 } } ^ { t _ { 1 } t _ { 2 } } = \tilde { v } _ { t _ { 3 } t _ { 4 } } ^ { t _ { 1 } t _ { 2 } }

输出:
\tilde{\nu}_{t_{3} t_{4}}^{t_{1} t_{2}}=\tilde{v}_{t_{3} t_{4}}^{t_{1} t_{2}}

状态: 已规范化 ✓
================================================================================

样本 133: UniMER-1M_0267223
------------------------------------------------------------
输入:
w _ { j k } ( \theta _ { j } ) = ( 1 + \cos \theta _ { j } ) s _ { j k } + \mathcal O ( s _ { j k } ^ { 2 } )

输出:
w_{j k}(\theta_{j})=(1+\mathrm{cos} \theta_{j}) s_{j k}+\mathcal{O}(s_{j k}^{2})

状态: 已规范化 ✓
================================================================================

样本 134: UniMER-1M_0269186
------------------------------------------------------------
输入:
k = 2 / L

输出:
k=2/L

状态: 已规范化 ✓
================================================================================

样本 135: UniMER-1M_0271261
------------------------------------------------------------
输入:
- 3 / 2

输出:
-3/2

状态: 已规范化 ✓
================================================================================

样本 136: UniMER-1M_0273298
------------------------------------------------------------
输入:
N

输出:
N

状态: 无变化
================================================================================

样本 137: UniMER-1M_0275326
------------------------------------------------------------
输入:
\sqrt { 2 }

输出:
\sqrt{2}

状态: 已规范化 ✓
================================================================================

样本 138: UniMER-1M_0277340
------------------------------------------------------------
输入:
r = R _ { \mathrm { ~ m ~ a ~ x ~ } }

输出:
r=R_{\mathrm{~m~a~x~}}

状态: 已规范化 ✓
================================================================================

样本 139: UniMER-1M_0279326
------------------------------------------------------------
输入:
( { \pmb S } _ { \alpha } ( { \pmb x } , { \pmb \xi } ) , \mu { \pmb \Sigma } _ { \alpha } ( { \pmb x } , { \pmb \xi } ) )

输出:
({\pmb{S}}_{\alpha}({\pmb{x}},{\pmb{\xi}}),\mu{\pmb{\Sigma}}_{\alpha}({\pmb{x}},{\pmb{\xi}}))

状态: 已规范化 ✓
================================================================================

样本 140: UniMER-1M_0281354
------------------------------------------------------------
输入:
\left. \begin{array} { l } { \displaystyle ( \hat { x } , \hat { z } , \hat { \xi } , \hat { \zeta } ) = \hat { R } _ { 0 } ( x , \epsilon z , \epsilon \xi , \epsilon \zeta ) , \quad \hat { t } = \frac { \hat { R } _ { 0 } } { { \hat { U } } } t ; } \\ { \displaystyle \hat { \sigma } = \hat { \sigma } _ { 0 } \sigma , \quad ( \hat { p } _ { l } , \hat { p } _ { s } , \hat { \Pi } ) = \frac { \hat { \eta } _ { l } { \hat { U } } } { \epsilon ^ { 2 } \hat { R } _ { 0 } } ( p _ { l } , p _ { s } , \Pi ) ; } \\ { \displaystyle ( \hat { u } _ { x } , \hat { u } _ { z } ) = \hat { R } _ { 0 } ( u _ { x } , \epsilon u _ { z } ) , \quad ( \hat { v } _ { x } , \hat { v } _ { z } ) = \hat { U } ( v _ { x } , \epsilon v _ { z } ) ; } \\ { \displaystyle \hat { T } = \hat { T } _ { r e f } + T \Delta \hat { T } , \quad \hat { J } = \frac { \hat { \lambda } \Delta \hat { T } } { \hat { L } _ { v } \hat { h } _ { 0 } } J , \quad \hat { \rho ^ { v } } = \hat { \rho } _ { r e f } ^ { v } \rho ^ { v } . } \end{array} \right\}

输出:
\left.\begin{array}{l}{{\displaystyle(\hat{x},\hat{z},\hat{\xi},\hat{\zeta})=\hat{R}_{0}(x,\epsilon z,\epsilon \xi,\epsilon \zeta),\quad \hat{t}=\frac{\hat{R}_{0}}{{\hat{U}}} t;}} \{{\displaystyle \hat{\sigma}=\hat{\sigma}_{0} \sigma,\quad(\hat{p}_{l},\hat{p}_{s},\hat{\Pi})=\frac{\hat{\eta}_{l}{\hat{U}}}{\epsilon^{2} \hat{R}_{0}}(p_{l},p_{s},\Pi);}} \{{\displaystyle(\hat{u}_{x},\hat{u}_{z})=\hat{R}_{0}(u_{x},\epsilon u_{z}),\quad(\hat{v}_{x},\hat{v}_{z})=\hat{U}(v_{x},\epsilon v_{z});}} \{{\displaystyle \hat{T}=\hat{T}_{r e f}+T \Delta \hat{T},\quad \hat{J}=\frac{\hat{\lambda} \Delta \hat{T}}{\hat{L}_{v} \hat{h}_{0}} J,\quad \hat{\rho^{v}}=\hat{\rho}_{r e f}^{v} \rho^{v}.}} \end{array} \right\}

状态: 已规范化 ✓
================================================================================

样本 141: UniMER-1M_0283405
------------------------------------------------------------
输入:
\phi _ { 0 } \simeq M _ { 3 } \: \mathrm { e x p } \left[ { \frac { \pi ^ { 2 } m ^ { 2 } } { 2 g _ { 3 } ^ { 2 } M _ { 3 } ^ { 2 } } } \right] .

输出:
\phi_{0} \simeq M_{3} \:\mathrm{exp} \left[{\frac{\pi^{2} m^{2}}{2 g_{3}^{2} M_{3}^{2}}} \right].

状态: 已规范化 ✓
================================================================================

样本 142: UniMER-1M_0285396
------------------------------------------------------------
输入:
\mathrm { ~ W ~ } _ { 3 } = \frac { 1 } { \sqrt { 3 } } \Big ( \vert 0 0 1 \rangle + \vert 0 1 0 \rangle + \vert 1 0 0 \rangle \Big ) .

输出:
\mathrm{~W~}_{3}=\frac{1}{\sqrt{3}} \Big(\vert 0 0 1 \rangle+\vert 0 1 0 \rangle+\vert 1 0 0 \rangle \Big).

状态: 已规范化 ✓
================================================================================

样本 143: UniMER-1M_0287487
------------------------------------------------------------
输入:
\approx

输出:
\approx

状态: 无变化
================================================================================

样本 144: UniMER-1M_0289422
------------------------------------------------------------
输入:
B \gamma _ { s s } - a ^ { 2 } K \gamma = 0

输出:
B \gamma_{s s}-a^{2} K \gamma=0

状态: 已规范化 ✓
================================================================================

样本 145: UniMER-1M_0291520
------------------------------------------------------------
输入:
a

输出:
a

状态: 无变化
================================================================================

样本 146: UniMER-1M_0293481
------------------------------------------------------------
输入:
\begin{array} { r l } { \hat { x } _ { k } } & { { } = \sum _ { j = 0 } ^ { N - 1 } x _ { j } \Psi ^ { j k / N } , } \\ { \hat { x } _ { k _ { 2 } , k _ { 1 } } } & { { } = \sum _ { j _ { 1 } = 0 } ^ { n _ { 1 } - 1 } \sum _ { j _ { 2 } = 0 } ^ { n _ { 2 } - 1 } x _ { j _ { 1 } , j 2 } \Psi ^ { j _ { 2 } k _ { 2 } / n _ { 2 } } \Psi ^ { j _ { 1 } k _ { 2 } / N } \Psi ^ { j _ { 1 } k _ { 1 } / n _ { 1 } } . } \end{array}

输出:
\begin{array}{rl}{{\hat{x}_{k}}} &{{=\sum_{j=0}^{N-1} x_{j} \Psi^{j k/N},}} \{{\hat{x}_{k_{2},k_{1}}}} &{{=\sum_{j_{1}=0}^{n_{1}-1} \sum_{j_{2}=0}^{n_{2}-1} x_{j_{1},j 2} \Psi^{j_{2} k_{2}/n_{2}} \Psi^{j_{1} k_{2}/N} \Psi^{j_{1} k_{1}/n_{1}}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 147: UniMER-1M_0295493
------------------------------------------------------------
输入:
\alpha _ { n } ( \omega , k _ { z } ) = \frac { \varepsilon _ { 0 } } { \varepsilon _ { 1 } - \varepsilon _ { 0 } } + \frac { 1 } { 2 } \sum _ { l = \pm 1 } \left[ 1 + \frac { | \lambda _ { 1 } | } { | \lambda _ { 0 } | } \frac { I _ { n + l } ( | \lambda _ { 0 } | r _ { c } ) K _ { n } ( | \lambda _ { 1 } | r _ { c } ) } { I _ { n } ( | \lambda _ { 0 } | r _ { c } ) K _ { n + l } ( | \lambda _ { 1 } | r _ { c } ) } \right] ^ { - 1 } .

输出:
\alpha_{n}(\omega,k_{z})=\frac{\varepsilon_{0}}{\varepsilon_{1}-\varepsilon_{0}}+\frac{1}{2} \sum_{l=\pm 1} \left[1+\frac{| \lambda_{1} |}{| \lambda_{0} |} \frac{I_{n+l}(| \lambda_{0} | r_{c}) K_{n}(| \lambda_{1} | r_{c})}{I_{n}(| \lambda_{0} | r_{c}) K_{n+l}(| \lambda_{1} | r_{c})} \right]^{-1}.

状态: 已规范化 ✓
================================================================================

样本 148: UniMER-1M_0297605
------------------------------------------------------------
输入:
\lambda

输出:
\lambda

状态: 无变化
================================================================================

样本 149: UniMER-1M_0299426
------------------------------------------------------------
输入:
\langle \hat { g } _ { x } \rangle = \langle \hat { g } _ { x } ^ { c l } \rangle , ~ ~ ~ ~ \langle \hat { g } _ { x } ^ { n c } \rangle = 0 ,

输出:
\langle \hat{g}_{x} \rangle=\langle \hat{g}_{x}^{c l} \rangle,~ ~ ~ ~ \langle \hat{g}_{x}^{n c} \rangle=0,

状态: 已规范化 ✓
================================================================================

样本 150: UniMER-1M_0301639
------------------------------------------------------------
输入:
\delta

输出:
\delta

状态: 无变化
================================================================================

样本 151: UniMER-1M_0303652
------------------------------------------------------------
输入:
d s ^ { 2 } = g _ { \alpha \beta } d x ^ { \alpha } d x ^ { \beta } + g _ { \iota \iota } d x ^ { \iota } d x ^ { \iota } \; .

输出:
d s^{2}=g_{\alpha \beta} d x^{\alpha} d x^{\beta}+g_{\iota \iota} d x^{\iota} d x^{\iota} \;.

状态: 已规范化 ✓
================================================================================

样本 152: UniMER-1M_0305654
------------------------------------------------------------
输入:
\begin{array} { r } { \mathbf { H } = \left[ \begin{array} { l l l } { H _ { x } } & { H _ { x y } } & { H _ { x z } } \\ { 0 } & { H _ { y } } & { H _ { y z } } \\ { 0 } & { 0 } & { H _ { z } } \end{array} \right] } \end{array}

输出:
\begin{array}{r}{{\mathbf{H}=\left[\begin{array}{lll}{{H_{x}}} &{{H_{x y}}} &{{H_{x z}}} \{{0}} &{{H_{y}}} &{{H_{y z}}} \{{0}} &{{0}} &{{H_{z}}} \end{array} \right]}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 153: UniMER-1M_0307719
------------------------------------------------------------
输入:
p _ { 2 }

输出:
p_{2}

状态: 已规范化 ✓
================================================================================

样本 154: UniMER-1M_0309736
------------------------------------------------------------
输入:
T _ { b } ^ { \prime } = | T _ { b } - 8 T _ { c } | = 2 . 7 \; n s

输出:
T_{b}^{\prime}=| T_{b}-8 T_{c} |=2.7 \;n s

状态: 已规范化 ✓
================================================================================

样本 155: UniMER-1M_0311696
------------------------------------------------------------
输入:
( \sqrt { \mathrm { ~ \textit ~ { ~ N ~ M ~ S ~ E ~ } ~ } } < 1 \

输出:
(\sqrt{\mathrm{~\textit~{~N~M~S~E~} ~}}<1 \

状态: 已规范化 ✓
================================================================================

样本 156: UniMER-1M_0313680
------------------------------------------------------------
输入:
\angle A A ^ { \prime } D = \angle A A ^ { \prime } E = 9 0 ^ { \circ }

输出:
\angle A A^{\prime} D=\angle A A^{\prime} E=9 0^{\circ}

状态: 已规范化 ✓
================================================================================

样本 157: UniMER-1M_0315836
------------------------------------------------------------
输入:
n

输出:
n

状态: 无变化
================================================================================

样本 158: UniMER-1M_0317830
------------------------------------------------------------
输入:
\Lambda

输出:
\Lambda

状态: 无变化
================================================================================

样本 159: UniMER-1M_0319886
------------------------------------------------------------
输入:
x

输出:
x

状态: 无变化
================================================================================

样本 160: UniMER-1M_0321863
------------------------------------------------------------
输入:
\begin{array} { r } { \left( \nabla \times { \mathbf B } \right) \times { \mathbf B } = - \mu _ { 0 } \nabla \cdot \boldsymbol { \pi } . } \end{array}

输出:
\begin{array}{r}{{\left(\nabla \times{\mathbf{B}} \right) \times{\mathbf{B}}=-\mu_{0} \nabla \cdot \boldsymbol{\pi}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 161: UniMER-1M_0323874
------------------------------------------------------------
输入:
\begin{array} { r l } { I _ { d } \left( \boldsymbol { q } _ { 0 } , \boldsymbol { s } \right) } & { = \left| \int P \left( \boldsymbol { x } \right) O \left( \boldsymbol { x } - \boldsymbol { s } \right) \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \boldsymbol { x } \right] d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \boldsymbol { s } \right] \int P \left( \boldsymbol { x } \right) O \left( \boldsymbol { x } - \boldsymbol { s } \right) \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \left( \boldsymbol { x } - \boldsymbol { s } \right) \right] d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| \int P \left( \boldsymbol { x } \right) O \left( \boldsymbol { x } - \boldsymbol { s } \right) \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \left( \boldsymbol { x } - \boldsymbol { s } \right) \right] d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| \int P \left( \boldsymbol { x } \right) O _ { \boldsymbol { q } _ { 0 } } ^ { \prime } \left( \boldsymbol { s } - \boldsymbol { x } \right) d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| P \left( \boldsymbol { s } \right) \otimes O _ { \boldsymbol { q } _ { 0 } } ^ { \prime } \left( \boldsymbol { s } \right) \right| ^ { 2 } , } \end{array}

输出:
\begin{array}{rl}{{I_{d} \left(\boldsymbol{q}_{0},\boldsymbol{s} \right)}} &{{=\left| \int P \left(\boldsymbol{x} \right) O \left(\boldsymbol{x}-\boldsymbol{s} \right) \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \boldsymbol{x} \right] d \boldsymbol{x} \right|^{2}}} \&{{=\left| \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \boldsymbol{s} \right] \int P \left(\boldsymbol{x} \right) O \left(\boldsymbol{x}-\boldsymbol{s} \right) \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \left(\boldsymbol{x}-\boldsymbol{s} \right) \right] d \boldsymbol{x} \right|^{2}}} \&{{=\left| \int P \left(\boldsymbol{x} \right) O \left(\boldsymbol{x}-\boldsymbol{s} \right) \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \left(\boldsymbol{x}-\boldsymbol{s} \right) \right] d \boldsymbol{x} \right|^{2}}} \&{{=\left| \int P \left(\boldsymbol{x} \right) O_{\boldsymbol{q}_{0}}^{\prime} \left(\boldsymbol{s}-\boldsymbol{x} \right) d \boldsymbol{x} \right|^{2}}} \&{{=\left| P \left(\boldsymbol{s} \right) \otimes O_{\boldsymbol{q}_{0}}^{\prime} \left(\boldsymbol{s} \right) \right|^{2},}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 162: UniMER-1M_0325915
------------------------------------------------------------
输入:
\begin{array} { r l } { E ^ { ( 1 ) } ( x , z ) } & { { } = 1 + \sum _ { n } r _ { n } \cos ( 2 \pi n x / L ) \mathrm { ~ , ~ } } \\ { H ^ { ( 1 ) } ( x , z ) } & { { } = \alpha _ { 0 } - \sum _ { n } r _ { n } \alpha _ { n } \cos ( 2 \pi n x / L ) \mathrm { ~ , ~ } } \end{array}

输出:
\begin{array}{rl}{{E^{(1)}(x,z)}} &{{=1+\sum_{n} r_{n} \mathrm{cos}(2 \pi n x/L) \mathrm{~,~}}} \{{H^{(1)}(x,z)}} &{{=\alpha_{0}-\sum_{n} r_{n} \alpha_{n} \mathrm{cos}(2 \pi n x/L) \mathrm{~,~}}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 163: UniMER-1M_0328000
------------------------------------------------------------
输入:
| \phi _ { 3 } \rangle = ( | t _ { 0 } \rangle - | t _ { 1 } \rangle - | t _ { 2 } \rangle + | t _ { 3 } \rangle ) / 2

输出:
| \phi_{3} \rangle=(| t_{0} \rangle-| t_{1} \rangle-| t_{2} \rangle+| t_{3} \rangle)/2

状态: 已规范化 ✓
================================================================================

样本 164: UniMER-1M_0330048
------------------------------------------------------------
输入:
A _ { \gamma }

输出:
A_{\gamma}

状态: 已规范化 ✓
================================================================================

样本 165: UniMER-1M_0332014
------------------------------------------------------------
输入:
\epsilon = \delta / H

输出:
\epsilon=\delta/H

状态: 已规范化 ✓
================================================================================

样本 166: UniMER-1M_0334106
------------------------------------------------------------
输入:
( d = 3 )

输出:
(d=3)

状态: 已规范化 ✓
================================================================================

样本 167: UniMER-1M_0336039
------------------------------------------------------------
输入:
\Phi ( \theta ) = \chi ^ { 2 } ( y | \theta ) / 2

输出:
\Phi(\theta)=\chi^{2}(y | \theta)/2

状态: 已规范化 ✓
================================================================================

样本 168: UniMER-1M_0338175
------------------------------------------------------------
输入:
Y

输出:
Y

状态: 无变化
================================================================================

样本 169: UniMER-1M_0340201
------------------------------------------------------------
输入:
M = B

输出:
M=B

状态: 已规范化 ✓
================================================================================

样本 170: UniMER-1M_0342210
------------------------------------------------------------
输入:
\overline { { \mathcal { I } } } _ { \mathrm { o p t } } ( \mathcal { A } )

输出:
\overline{{{\mathcal{I}}}}_{\mathrm{opt}}(\mathcal{A})

状态: 已规范化 ✓
================================================================================

样本 171: UniMER-1M_0344177
------------------------------------------------------------
输入:
N = n _ { c } \mathcal { P } ,

输出:
N=n_{c} \mathcal{P},

状态: 已规范化 ✓
================================================================================

样本 172: UniMER-1M_0346212
------------------------------------------------------------
输入:
I _ { B 0 , 0 } ^ { L }

输出:
I_{B 0,0}^{L}

状态: 已规范化 ✓
================================================================================

样本 173: UniMER-1M_0348237
------------------------------------------------------------
输入:
_ { a } ^ { A B } D _ { t } ^ { - \alpha } f ( t ) = _ { a } ^ { A B } I _ { t } ^ { \alpha } f ( t ) = { \frac { 1 - \alpha } { A B ( \alpha ) } } f ( t ) + { \frac { \alpha } { A B ( \alpha ) \Gamma ( \alpha ) } } \int _ { a } ^ { t } \left( t - \tau \right) ^ { \alpha - 1 } f ( \tau ) \, d \tau ,

输出:
_{a}^{A B} D_{t}^{-\alpha} f(t)=_{a}^{A B} I_{t}^{\alpha} f(t)={\frac{1-\alpha}{A B(\alpha)}} f(t)+{\frac{\alpha}{A B(\alpha) \Gamma(\alpha)}} \int_{a}^{t} \left(t-\tau \right)^{\alpha-1} f(\tau) \,d \tau,

状态: 已规范化 ✓
================================================================================

样本 174: UniMER-1M_0350277
------------------------------------------------------------
输入:
N _ { v }

输出:
N_{v}

状态: 已规范化 ✓
================================================================================

样本 175: UniMER-1M_0352254
------------------------------------------------------------
输入:
f ^ { ( 6 ) } ( x ) = 7 2 0

输出:
f^{(6)}(x)=7 2 0

状态: 已规范化 ✓
================================================================================

样本 176: UniMER-1M_0354314
------------------------------------------------------------
输入:
\nu _ { T } = \frac { - ( \hat { \partial } _ { k } \widetilde { u } _ { i } ) ( \hat { \partial } _ { k } \widetilde { u } _ { j } ) \widetilde { S } _ { i j } } { ( \partial _ { l } \widetilde { u } _ { m } ) ( \partial _ { l } \widetilde { u } _ { m } ) } ,

输出:
\nu_{T}=\frac{-(\hat{\partial}_{k} \widetilde{u}_{i})(\hat{\partial}_{k} \widetilde{u}_{j}) \widetilde{S}_{i j}}{(\partial_{l} \widetilde{u}_{m})(\partial_{l} \widetilde{u}_{m})},

状态: 已规范化 ✓
================================================================================

样本 177: UniMER-1M_0356216
------------------------------------------------------------
输入:
E _ { \mathrm { c } } = \frac { e ^ { 2 } } { \pi } \int _ { 0 } ^ { \infty } d q \left[ S ( q ) - S ^ { ( 0 ) } ( q ) \right] .

输出:
E_{\mathrm{c}}=\frac{e^{2}}{\pi} \int_{0}^{\infty} d q \left[S(q)-S^{(0)}(q) \right].

状态: 已规范化 ✓
================================================================================

样本 178: UniMER-1M_0358325
------------------------------------------------------------
输入:
\hat { H } ^ { \mathrm { ~ e ~ l ~ } } ( \eta ) = \hat { H } ^ { \mathrm { ~ e ~ l ~ } } - i \, \eta \, \hat { W } ( r ) ~ .

输出:
\hat{H}^{\mathrm{~e~l~}}(\eta)=\hat{H}^{\mathrm{~e~l~}}-i \,\eta \,\hat{W}(r) ~.

状态: 已规范化 ✓
================================================================================

样本 179: UniMER-1M_0360327
------------------------------------------------------------
输入:
u ( x , t ) = \rho _ { i } ( t ) u _ { i } ^ { * } ( x , t )

输出:
u(x,t)=\rho_{i}(t) u_{i}^{*}(x,t)

状态: 已规范化 ✓
================================================================================

样本 180: UniMER-1M_0362399
------------------------------------------------------------
输入:
\hat { T }

输出:
\hat{T}

状态: 已规范化 ✓
================================================================================

样本 181: UniMER-1M_0364341
------------------------------------------------------------
输入:
\int \limits _ { 0 } ^ { R } \frac { 2 x d x } { 1 + x ^ { 2 } } = \log ( 1 + R ^ { 2 } )

输出:
\int_{0}^{R} \frac{2 x d x}{1+x^{2}}=\mathrm{log}(1+R^{2})

状态: 已规范化 ✓
================================================================================

样本 182: UniMER-1M_0366410
------------------------------------------------------------
输入:
A ^ { \prime } = g A g ^ { - 1 } + g d g ^ { - 1 } , \ \ \ \omega ^ { \prime } = \omega , \ \ \ F ^ { \prime } = g F g ^ { - 1 } , \ \ \ \ a n d \ \ \ \ \Omega ^ { \prime } = \Omega .

输出:
A^{\prime}=g A g^{-1}+g d g^{-1},\omega^{\prime}=\omega,F^{\prime}=g F g^{-1},a n d \Omega^{\prime}=\Omega.

状态: 已规范化 ✓
================================================================================

样本 183: UniMER-1M_0368417
------------------------------------------------------------
输入:
v _ { r e l } = v _ { r e l a t i v e } = v _ { S ^ { \prime } / S } \equiv d x ^ { \prime } / d t ^ { \prime }

输出:
v_{r e l}=v_{r e l a t i v e}=v_{S^{\prime}/S} \equiv d x^{\prime}/d t^{\prime}

状态: 已规范化 ✓
================================================================================

样本 184: UniMER-1M_0370401
------------------------------------------------------------
输入:
{ \frac { B } { s } } ~ = ~ { \frac { 4 5 c _ { n } c _ { s } } { \pi g _ { * } } } { \frac { A } { 4 \lambda T _ { b } } } \epsilon ~ \simeq ~ { \frac { 0 . 0 1 \epsilon A } { \lambda T _ { b } } } .

输出:
{\frac{B}{s}} ~=~{\frac{4 5 c_{n} c_{s}}{\pi g_{*}}}{\frac{A}{4 \lambda T_{b}}} \epsilon ~ \simeq ~{\frac{0.0 1 \epsilon A}{\lambda T_{b}}}.

状态: 已规范化 ✓
================================================================================

样本 185: UniMER-1M_0372546
------------------------------------------------------------
输入:
Z

输出:
Z

状态: 无变化
================================================================================

样本 186: UniMER-1M_0374544
------------------------------------------------------------
输入:
8 9 9 9

输出:
8 9 9 9

状态: 无变化
================================================================================

样本 187: UniMER-1M_0376607
------------------------------------------------------------
输入:
^ { 8 7 }

输出:
^{8 7}

状态: 已规范化 ✓
================================================================================

样本 188: UniMER-1M_0378634
------------------------------------------------------------
输入:
1 0 ^ { - 2 }

输出:
1 0^{-2}

状态: 已规范化 ✓
================================================================================

样本 189: UniMER-1M_0380643
------------------------------------------------------------
输入:
w \in \mathcal { O } _ { v , M }

输出:
w \in \mathcal{O}_{v,M}

状态: 已规范化 ✓
================================================================================

样本 190: UniMER-1M_0382705
------------------------------------------------------------
输入:
n - 1

输出:
n-1

状态: 已规范化 ✓
================================================================================

样本 191: UniMER-1M_0384716
------------------------------------------------------------
输入:
\Vert \hat { T } _ { * } \phi _ { 0 } \Vert _ { L ^ { 2 } } \leq \Vert \hat { T } _ { * } \Vert _ { L ^ { 2 } } \Vert \phi _ { 0 } \Vert _ { L ^ { 2 } } = \Vert \hat { T } _ { * } \Vert _ { L ^ { 2 } }

输出:
\Vert \hat{T}_{*} \phi_{0} \Vert_{L^{2}} \leq \Vert \hat{T}_{*} \Vert_{L^{2}} \Vert \phi_{0} \Vert_{L^{2}}=\Vert \hat{T}_{*} \Vert_{L^{2}}

状态: 已规范化 ✓
================================================================================

样本 192: UniMER-1M_0386729
------------------------------------------------------------
输入:
\begin{array} { r l } { W _ { 2 } ^ { 2 } \big ( \nu ^ { m , t } , \exp ( \nabla h _ { \delta } ^ { n , t } ) _ { \# } \mu ^ { n , t } \big ) } & { = W _ { 2 } ^ { 2 } \big ( \phi ( 1 , \cdot ) _ { \# } \mu ^ { n , t } , \exp ( \nabla h _ { \delta } ^ { n , t } ) _ { \# } \mu ^ { n , t } \big ) } \\ & { \lesssim \int _ { \mathcal { M } } \Big ( \vert \rho _ { \delta } - \rho \vert + \vert \rho _ { t } - \rho \vert + \frac { 1 } { \log ^ { \upsilon } ( n ) } \Big ) ^ { 2 } \vert \nabla h _ { \delta } ^ { n , t } \vert ^ { 2 } } \\ & { \leq \big ( \| \rho _ { \delta } - \rho \| _ { \mathrm { L } ^ { 2 ( \frac { \bar { q } } { 2 } ) ^ { \prime } } } ^ { 2 } + \| \rho _ { t } - \rho \| _ { \mathrm { L } ^ { 2 ( \frac { \bar { q } } { 2 } ) ^ { \prime } } } ^ { 2 } + \frac { 1 } { \log ^ { \upsilon } ( n ) } \big ) \Big ( \int _ { \mathcal { M } } \vert \nabla h _ { \delta } ^ { n , t } \vert ^ { \bar { q } } \Big ) ^ { \frac { 2 } { \bar { q } } } . } \end{array}

输出:
\begin{array}{rl}{{W_{2}^{2} \big(\nu^{m,t},\mathrm{exp}(\nabla h_{\delta}^{n,t})_{\#} \mu^{n,t} \big)}} &{{=W_{2}^{2} \big(\phi(1,\cdot)_{\#} \mu^{n,t},\mathrm{exp}(\nabla h_{\delta}^{n,t})_{\#} \mu^{n,t} \big)}} \&{{\lesssim \int_{\mathcal{M}} \Big(\vert \rho_{\delta}-\rho \vert+\vert \rho_{t}-\rho \vert+\frac{1}{\mathrm{log}^{\upsilon}(n)} \Big)^{2} \vert \nabla h_{\delta}^{n,t} \vert^{2}}} \&{{\leq \big(\| \rho_{\delta}-\rho \|_{\mathrm{L}^{2(\frac{\bar{q}}{2})^{\prime}}}^{2}+\| \rho_{t}-\rho \|_{\mathrm{L}^{2(\frac{\bar{q}}{2})^{\prime}}}^{2}+\frac{1}{\mathrm{log}^{\upsilon}(n)} \big) \Big(\int_{\mathcal{M}} \vert \nabla h_{\delta}^{n,t} \vert^{\bar{q}} \Big)^{\frac{2}{\bar{q}}}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 193: UniMER-1M_0388760
------------------------------------------------------------
输入:
N ^ { \mathrm { X } } ( \tau ^ { 1 } ) = N ^ { \mathrm { X } } ( \tau _ { - } ^ { 1 } ) - 2

输出:
N^{\mathrm{X}}(\tau^{1})=N^{\mathrm{X}}(\tau_{-}^{1})-2

状态: 已规范化 ✓
================================================================================

样本 194: UniMER-1M_0390733
------------------------------------------------------------
输入:
\begin{array} { r l } & { \boldsymbol { b } _ { 1 } = [ \boldsymbol { h } ^ { \top } , \boldsymbol { f } _ { 1 } ^ { \top } , \boldsymbol { 0 } _ { ( ( N _ { t } - 1 ) ( Q _ { t } + 1 ) Q _ { x } ) } ^ { \top } ] ^ { \top } , } \\ & { \boldsymbol { b } _ { i } = [ \boldsymbol { 0 } _ { ( ( ( i - 1 ) ( Q _ { t } + 1 ) + 1 ) Q _ { x } ) } ^ { \top } , \boldsymbol { f } _ { i } ^ { \top } , \boldsymbol { 0 } _ { ( ( N _ { t } - i ) ( Q _ { t } + 1 ) Q _ { x } ) } ^ { \top } ] , \quad \mathrm { f o r } \; i = 2 , \cdots , N _ { t } . } \end{array}

输出:
\begin{array}{rl} &{{\boldsymbol{b}_{1}=[\boldsymbol{h}^{\top},\boldsymbol{f}_{1}^{\top},\boldsymbol{0}_{((N_{t}-1)(Q_{t}+1) Q_{x})}^{\top}]^{\top},}} \&{{\boldsymbol{b}_{i}=[\boldsymbol{0}_{(((i-1)(Q_{t}+1)+1) Q_{x})}^{\top},\boldsymbol{f}_{i}^{\top},\boldsymbol{0}_{((N_{t}-i)(Q_{t}+1) Q_{x})}^{\top}],\quad \mathrm{for} \;i=2,. . .,N_{t}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 195: UniMER-1M_0392852
------------------------------------------------------------
输入:
^ { 4 0 }

输出:
^{4 0}

状态: 已规范化 ✓
================================================================================

样本 196: UniMER-1M_0394755
------------------------------------------------------------
输入:
\left\{ \begin{array} { c c c } { { \lambda x ^ { + } } } & { { = } } & { { e ^ { \lambda \sigma ^ { + } } } } \\ { { \lambda \left( x ^ { - } + \Delta _ { q } \right) } } & { { = } } & { { - e ^ { - \lambda \sigma ^ { - } } } } \end{array} \ \ \ \ , \right.

输出:
\left\{\begin{array}{ccc}{{{\lambda x^{+}}}} &{{{=}}} &{{{e^{\lambda \sigma^{+}}}}} \{{{\lambda \left(x^{-}+\Delta_{q} \right)}}} &{{{=}}} &{{{-e^{-\lambda \sigma^{-}}}}} \end{array},\right.

状态: 已规范化 ✓
================================================================================

样本 197: UniMER-1M_0396919
------------------------------------------------------------
输入:
4 6

输出:
4 6

状态: 无变化
================================================================================

样本 198: UniMER-1M_0398965
------------------------------------------------------------
输入:
\chi ^ { 2 }

输出:
\chi^{2}

状态: 已规范化 ✓
================================================================================

样本 199: UniMER-1M_0400882
------------------------------------------------------------
输入:
\begin{array} { r l } { \frac { | T _ { n } + B ( 0 , \| h \| _ { 2 } ) | - | T _ { n } | } { | T _ { n } | } } & { = \sum _ { j = 0 } ^ { d - 1 } \frac { \mu _ { j } ( T _ { n } ) \| h \| _ { 2 } ^ { d - j } } { | T _ { n } | } } \\ & { = \sum _ { j = 0 } ^ { d - 1 } \mu _ { j } \left( \frac { T _ { n } } { | T _ { n } | ^ { 1 / d } } \right) \cdot \left( \frac { \| h \| _ { 2 } } { | T _ { n } | ^ { 1 / d } } \right) ^ { d - j } , } \end{array}

输出:
\begin{array}{rl}{{\frac{| T_{n}+B(0,\| h \|_{2}) |-| T_{n} |}{| T_{n} |}}} &{{=\sum_{j=0}^{d-1} \frac{\mu_{j}(T_{n}) \| h \|_{2}^{d-j}}{| T_{n} |}}} \&{{=\sum_{j=0}^{d-1} \mu_{j} \left(\frac{T_{n}}{| T_{n} |^{1/d}} \right) \cdot \left(\frac{\| h \|_{2}}{| T_{n} |^{1/d}} \right)^{d-j},}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 200: UniMER-1M_0403014
------------------------------------------------------------
输入:
x

输出:
x

状态: 无变化
================================================================================

样本 201: UniMER-1M_0405048
------------------------------------------------------------
输入:
^ { - 1 }

输出:
^{-1}

状态: 已规范化 ✓
================================================================================

样本 202: UniMER-1M_0407051
------------------------------------------------------------
输入:
\sigma

输出:
\sigma

状态: 无变化
================================================================================

样本 203: UniMER-1M_0409032
------------------------------------------------------------
输入:
\begin{array} { r l } { \hat { \boldsymbol { \Phi } } _ { \alpha } = } & { ~ \hat { \boldsymbol { \Phi } } _ { \alpha } \left( \phi _ { \alpha } , \nabla \phi _ { \alpha } , \mathrm { d i v } \mathbf { v } _ { \alpha } , \mathbf { q } _ { \alpha } , \gamma _ { \alpha } \right) , } \\ { \hat { s } _ { \alpha } = } & { ~ \hat { s } _ { \alpha } \left( r _ { \alpha } \right) , } \\ { \hat { \mathbf { T } } _ { \alpha } = } & { ~ \hat { \mathbf { T } } _ { \alpha } ( \phi _ { \alpha } , \nabla \phi _ { \alpha } , \mathbf { D } _ { \alpha } , \pi _ { \alpha } , p ) , } \\ { \hat { \gamma } _ { \alpha } = } & { ~ \hat { \gamma } _ { \alpha } \left( \phi _ { \alpha } , \nabla \phi _ { \alpha } , p , \left\{ \psi _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } , \left\{ \mu _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } \right) , } \\ { \hat { \boldsymbol { \pi } } _ { \alpha } = } & { ~ \hat { \boldsymbol { \pi } } _ { \alpha } \left( \phi _ { \alpha } , \nabla \phi _ { \alpha } , \left\{ \mathbf { v } _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } , \left\{ \gamma _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } \right) , } \end{array}

输出:
\begin{array}{rl}{{\hat{\boldsymbol{\Phi}}_{\alpha}=}} &{{~ \hat{\boldsymbol{\Phi}}_{\alpha} \left(\phi_{\alpha},\nabla \phi_{\alpha},\mathrm{div} \mathbf{v}_{\alpha},\mathbf{q}_{\alpha},\gamma_{\alpha} \right),}} \{{\hat{s}_{\alpha}=}} &{{~ \hat{s}_{\alpha} \left(r_{\alpha} \right),}} \{{\hat{\mathbf{T}}_{\alpha}=}} &{{~ \hat{\mathbf{T}}_{\alpha}(\phi_{\alpha},\nabla \phi_{\alpha},\mathbf{D}_{\alpha},\pi_{\alpha},p),}} \{{\hat{\gamma}_{\alpha}=}} &{{~ \hat{\gamma}_{\alpha} \left(\phi_{\alpha},\nabla \phi_{\alpha},p,\left\{\psi_{\beta} \right\}_{{\beta}=1,. . .,N},\left\{\mu_{\beta} \right\}_{{\beta}=1,. . .,N} \right),}} \{{\hat{\boldsymbol{\pi}}_{\alpha}=}} &{{~ \hat{\boldsymbol{\pi}}_{\alpha} \left(\phi_{\alpha},\nabla \phi_{\alpha},\left\{\mathbf{v}_{\beta} \right\}_{{\beta}=1,. . .,N},\left\{\gamma_{\beta} \right\}_{{\beta}=1,. . .,N} \right),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 204: UniMER-1M_0411145
------------------------------------------------------------
输入:
p = 0

输出:
p=0

状态: 已规范化 ✓
================================================================================

样本 205: UniMER-1M_0413145
------------------------------------------------------------
输入:
\sim

输出:
\sim

状态: 无变化
================================================================================

样本 206: UniMER-1M_0415084
------------------------------------------------------------
输入:
{ \cal L } = \bar { \psi } ^ { ( i ) } \mathrm { i } \partial \! \! \! / \, \psi ^ { ( i ) } + \frac { g ^ { 2 } } { 2 } ( \bar { \psi } ^ { ( i ) } \psi ^ { ( i ) } ) ^ { 2 } \ ,

输出:
{\cal L}=\bar{\psi}^{(i)} \mathrm{i} \partial \! \! \!/\,\psi^{(i)}+\frac{g^{2}}{2}(\bar{\psi}^{(i)} \psi^{(i)})^{2},

状态: 已规范化 ✓
================================================================================

样本 207: UniMER-1M_0417209
------------------------------------------------------------
输入:
{ I } = \{ k _ { 0 } , k _ { 0 } + 1 , \ldots , k _ { 0 } + N _ { s } - 1 \} ,

输出:
{I}=\{k_{0},k_{0}+1,. . .,k_{0}+N_{s}-1 \},

状态: 已规范化 ✓
================================================================================

样本 208: UniMER-1M_0419136
------------------------------------------------------------
输入:
n < 0

输出:
n<0

状态: 已规范化 ✓
================================================================================

样本 209: UniMER-1M_0421250
------------------------------------------------------------
输入:
\sim 2 0 . 5

输出:
\sim 2 0.5

状态: 已规范化 ✓
================================================================================

样本 210: UniMER-1M_0423263
------------------------------------------------------------
输入:
\mathcal { V } = e ^ { K } \left( ( W _ { I } ^ { * } + K _ { I } W ^ { * } ) ( K ^ { - 1 } ) _ { J } ^ { I } ( W ^ { J } + K ^ { J } W ) - 3 | W | ^ { 2 } \right)

输出:
\mathcal{V}=e^{K} \left((W_{I}^{*}+K_{I} W^{*})(K^{-1})_{J}^{I}(W^{J}+K^{J} W)-3 | W |^{2} \right)

状态: 已规范化 ✓
================================================================================

样本 211: UniMER-1M_0425300
------------------------------------------------------------
输入:
3 0 \, \mathrm { \ u p m u m } \times 5 \, \mathrm { \ u p m u m }

输出:
3 0 \,\mathrm{upmum} \times 5 \,\mathrm{upmum}

状态: 已规范化 ✓
================================================================================

样本 212: UniMER-1M_0427321
------------------------------------------------------------
输入:
V _ { 2 } = 4 . 0 ( 1 ) E _ { r } ^ { ( 7 5 2 ) }

输出:
V_{2}=4.0(1) E_{r}^{(7 5 2)}

状态: 已规范化 ✓
================================================================================

样本 213: UniMER-1M_0429305
------------------------------------------------------------
输入:
\chi _ { x y z , \mathrm { n o r m } } ^ { ( 2 ) }

输出:
\chi_{x y z,\mathrm{norm}}^{(2)}

状态: 已规范化 ✓
================================================================================

样本 214: UniMER-1M_0431308
------------------------------------------------------------
输入:
\delta > 0

输出:
\delta>0

状态: 已规范化 ✓
================================================================================

样本 215: UniMER-1M_0433378
------------------------------------------------------------
输入:
1 6

输出:
1 6

状态: 无变化
================================================================================

样本 216: UniMER-1M_0435398
------------------------------------------------------------
输入:
\begin{array} { r } { f ( x ) + g ( x ) = \left( \begin{array} { l } { q _ { 1 } + k _ { 1 } } \\ { q _ { 2 } + k _ { 2 } } \\ { \cdots } \\ { q _ { n } + k _ { n } } \end{array} \right) } \end{array}

输出:
\begin{array}{r}{{f(x)+g(x)=\left(\begin{array}{l}{{q_{1}+k_{1}}} \{{q_{2}+k_{2}}} \{{. . .}} \{{q_{n}+k_{n}}} \end{array} \right)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 217: UniMER-1M_0437360
------------------------------------------------------------
输入:
\theta = \operatorname { a r c c o s } ( 1 / \sqrt { 3 } ) = 0 . 9 5 5

输出:
\theta=\mathrm{arccos}(1/\sqrt{3})=0.9 5 5

状态: 已规范化 ✓
================================================================================

样本 218: UniMER-1M_0439353
------------------------------------------------------------
输入:
k \in \left\lbrace 1 , \dots , N _ { \mathrm { v e r } } \right\rbrace

输出:
k \in \left\lbrace 1,. . .,N_{\mathrm{ver}} \right\rbrace

状态: 已规范化 ✓
================================================================================

样本 219: UniMER-1M_0441433
------------------------------------------------------------
输入:
L = \lambda

输出:
L=\lambda

状态: 已规范化 ✓
================================================================================

样本 220: UniMER-1M_0443449
------------------------------------------------------------
输入:
4 ^ { t h }

输出:
4^{t h}

状态: 已规范化 ✓
================================================================================

样本 221: UniMER-1M_0445350
------------------------------------------------------------
输入:
\begin{array} { r } { \langle \hat { f } _ { k } ^ { \mathrm { ~ \tiny ~ B ~ } } ( t ) \hat { f } _ { k ^ { \prime } } ^ { \mathrm { ~ \tiny ~ B ~ } } ( 0 ) \rangle _ { \mathrm { ~ \tiny ~ B ~ } } = \delta _ { k k ^ { \prime } } \eta _ { k } e ^ { - \gamma _ { k } t } , } \\ { \langle \hat { f } _ { k } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( 0 ) \hat { f } _ { k ^ { \prime } } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( t ) \rangle _ { \mathrm { ~ \tiny ~ B ~ } } = \delta _ { k k ^ { \prime } } \eta _ { k } ^ { * } e ^ { - \gamma _ { \bar { k } } t } , } \\ { \langle \hat { f } _ { \bar { k } } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( 0 ) \hat { f } _ { \bar { k } ^ { \prime } } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( t ) \rangle _ { \mathrm { ~ \tiny ~ B ~ } } = \delta _ { k k ^ { \prime } } \eta _ { \bar { k } } ^ { * } e ^ { - \gamma _ { k } t } , } \end{array}

输出:
\begin{array}{r}{{\langle \hat{f}_{k}^{\mathrm{~\tiny~B~}}(t) \hat{f}_{k^{\prime}}^{\mathrm{~\tiny~B~}}(0) \rangle_{\mathrm{~\tiny~B~}}=\delta_{k k^{\prime}} \eta_{k} e^{-\gamma_{k} t},}} \{{\langle \hat{f}_{k}^{\dag \mathrm{~\tiny~B~}}(0) \hat{f}_{k^{\prime}}^{\dag \mathrm{~\tiny~B~}}(t) \rangle_{\mathrm{~\tiny~B~}}=\delta_{k k^{\prime}} \eta_{k}^{*} e^{-\gamma_{\bar{k}} t},}} \{{\langle \hat{f}_{\bar{k}}^{\dag \mathrm{~\tiny~B~}}(0) \hat{f}_{\bar{k}^{\prime}}^{\dag \mathrm{~\tiny~B~}}(t) \rangle_{\mathrm{~\tiny~B~}}=\delta_{k k^{\prime}} \eta_{\bar{k}}^{*} e^{-\gamma_{k} t},}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 222: UniMER-1M_0447525
------------------------------------------------------------
输入:
\begin{array} { r l } { D _ { A } ^ { z } \equiv } & { { } ~ \sigma _ { A } ^ { \dagger } \sigma _ { A } - \sigma _ { A } \sigma _ { A } ^ { \dagger } + \sigma _ { 4 4 } - \sigma _ { 3 3 } + \sigma _ { 2 2 } - \sigma _ { 1 1 } } \\ { = } & { { } D _ { A } ^ { \dagger } D _ { A } - D _ { A } D _ { A } ^ { \dagger } } \\ { D _ { B } ^ { z } \equiv } & { { } ~ \sigma _ { B } ^ { \dagger } \sigma _ { B } - \sigma _ { B } \sigma _ { B } ^ { \dagger } + \sigma _ { 4 4 } - \sigma _ { 2 2 } + \sigma _ { 3 3 } - \sigma _ { 1 1 } } \\ { = } & { { } D _ { B } ^ { \dagger } D _ { B } - D _ { B } D _ { B } ^ { \dagger } . } \end{array}

输出:
\begin{array}{rl}{{D_{A}^{z} \equiv}} &{{ ~ \sigma_{A}^{\dagger} \sigma_{A}-\sigma_{A} \sigma_{A}^{\dagger}+\sigma_{4 4}-\sigma_{3 3}+\sigma_{2 2}-\sigma_{1 1}}} \{{=}} &{{ D_{A}^{\dagger} D_{A}-D_{A} D_{A}^{\dagger}}} \{{D_{B}^{z} \equiv}} &{{ ~ \sigma_{B}^{\dagger} \sigma_{B}-\sigma_{B} \sigma_{B}^{\dagger}+\sigma_{4 4}-\sigma_{2 2}+\sigma_{3 3}-\sigma_{1 1}}} \{{=}} &{{ D_{B}^{\dagger} D_{B}-D_{B} D_{B}^{\dagger}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 223: UniMER-1M_0449527
------------------------------------------------------------
输入:
\gamma _ { p } \approx 2 \pi \times 4 2 . 5 8

输出:
\gamma_{p} \approx 2 \pi \times 4 2.5 8

状态: 已规范化 ✓
================================================================================

样本 224: UniMER-1M_0451607
------------------------------------------------------------
输入:
k

输出:
k

状态: 无变化
================================================================================

样本 225: UniMER-1M_0453618
------------------------------------------------------------
输入:
N _ { s }

输出:
N_{s}

状态: 已规范化 ✓
================================================================================

样本 226: UniMER-1M_0455592
------------------------------------------------------------
输入:
p _ { a } ( t ) = \frac { t _ { r } ^ { a _ { r } } e ^ { - t _ { r } } } { a _ { r } ! } , \quad \alpha _ { r } \to \infty .

输出:
p_{a}(t)=\frac{t_{r}^{a_{r}} e^{-t_{r}}}{a_{r} !},\quad \alpha_{r} \to \infty.

状态: 已规范化 ✓
================================================================================

样本 227: UniMER-1M_0457608
------------------------------------------------------------
输入:
\alpha < 3 / 2

输出:
\alpha<3/2

状态: 已规范化 ✓
================================================================================

样本 228: UniMER-1M_0459592
------------------------------------------------------------
输入:
\boldsymbol { v } = \frac { F } { 8 \pi \eta } \left( \boldsymbol { G } ^ { \infty } + \boldsymbol { G } \right) \, , \qquad p = \frac { F } { 4 \pi } \left( P ^ { \infty } + P \right) \, ,

输出:
\boldsymbol{v}=\frac{F}{8 \pi \eta} \left(\boldsymbol{G}^{\infty}+\boldsymbol{G} \right) \,,\qquad p=\frac{F}{4 \pi} \left(P^{\infty}+P \right) \,,

状态: 已规范化 ✓
================================================================================

样本 229: UniMER-1M_0461658
------------------------------------------------------------
输入:
\gamma

输出:
\gamma

状态: 无变化
================================================================================

样本 230: UniMER-1M_0463623
------------------------------------------------------------
输入:
\bar { P } _ { z , \mathrm { N S } } = P _ { 0 } ( 1 + 1 / G T _ { 1 } )

输出:
\bar{P}_{z,\mathrm{NS}}=P_{0}(1+1/G T_{1})

状态: 已规范化 ✓
================================================================================

样本 231: UniMER-1M_0465616
------------------------------------------------------------
输入:
\widetilde { p } = { \mathbf { p } } \cdot d { \mathbf { x } } \otimes d ^ { 3 } x \in \Lambda ^ { 1 } ( \mathbb { R } ^ { 3 } ) \otimes \mathrm { D e n } ( \mathbb { R } ^ { 3 } ) )

输出:
\widetilde{p}={\mathbf{p}} \cdot d{\mathbf{x}} \otimes d^{3} x \in \Lambda^{1}(\mathbb{R}^{3}) \otimes \mathrm{Den}(\mathbb{R}^{3}))

状态: 已规范化 ✓
================================================================================

样本 232: UniMER-1M_0467546
------------------------------------------------------------
输入:
H ( x _ { + } ) = \int _ { - \infty } ^ { \infty } { \frac { d k _ { + } } { 2 \pi } } \int { \frac { d ^ { 2 } \widetilde { k } } { ( 2 \pi ) ^ { 2 } } } \sqrt { 2 } \Psi ^ { \dagger } \left( x _ { + } , k _ { + } , \widetilde { k } \right) i \partial _ { + } \Psi \left( x _ { + } , k _ { + } , \widetilde { k } \right) \; .

输出:
H(x_{+})=\int_{-\infty}^{\infty}{\frac{d k_{+}}{2 \pi}} \int{\frac{d^{2} \widetilde{k}}{(2 \pi)^{2}}} \sqrt{2} \Psi^{\dagger} \left(x_{+},k_{+},\widetilde{k} \right) i \partial_{+} \Psi \left(x_{+},k_{+},\widetilde{k} \right) \;.

状态: 已规范化 ✓
================================================================================

样本 233: UniMER-1M_0469755
------------------------------------------------------------
输入:
\eta

输出:
\eta

状态: 无变化
================================================================================

样本 234: UniMER-1M_0471757
------------------------------------------------------------
输入:
Q _ { i n t } = ( 4 . 7 - 5 . 7 ) \times 1 0 ^ { 5 }

输出:
Q_{i n t}=(4.7-5.7) \times 1 0^{5}

状态: 已规范化 ✓
================================================================================

样本 235: UniMER-1M_0473776
------------------------------------------------------------
输入:
g ( \omega ) = \sum _ { n = 0 } ^ { \infty } \tilde { a } _ { n } \omega ^ { n } ,

输出:
g(\omega)=\sum_{n=0}^{\infty} \tilde{a}_{n} \omega^{n},

状态: 已规范化 ✓
================================================================================

样本 236: UniMER-1M_0475812
------------------------------------------------------------
输入:
y = L / 2

输出:
y=L/2

状态: 已规范化 ✓
================================================================================

样本 237: UniMER-1M_0477851
------------------------------------------------------------
输入:
k _ { i }

输出:
k_{i}

状态: 已规范化 ✓
================================================================================

样本 238: UniMER-1M_0479856
------------------------------------------------------------
输入:
\begin{array} { r l } & { v _ { 4 k - 3 - 2 i , 2 k + 6 i } ^ { A , i + 1 } \otimes v _ { 1 , 3 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k + 2 + 6 i } ^ { A , i + 1 } \otimes v _ { 1 , 1 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k + 1 + 6 i } ^ { A , i + 1 } \otimes v _ { 1 , 2 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k - 1 + 6 i } ^ { A , i } \otimes v _ { 1 , 4 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k - 1 + 6 i } ^ { B , i } \otimes v _ { 1 , 4 } } \end{array}

输出:
\begin{array}{rl} &{{v_{4 k-3-2 i,2 k+6 i}^{A,i+1} \otimes v_{1,3}}} \&{{+v_{4 k-3-2 i,2 k+2+6 i}^{A,i+1} \otimes v_{1,1}}} \&{{+v_{4 k-3-2 i,2 k+1+6 i}^{A,i+1} \otimes v_{1,2}}} \&{{+v_{4 k-3-2 i,2 k-1+6 i}^{A,i} \otimes v_{1,4}}} \&{{+v_{4 k-3-2 i,2 k-1+6 i}^{B,i} \otimes v_{1,4}}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 239: UniMER-1M_0481870
------------------------------------------------------------
输入:
\mathcal D ~ \circ ~ \mathcal E ( \mathcal X )

输出:
\mathcal{D} ~ \circ ~ \mathcal{E}(\mathcal{X})

状态: 已规范化 ✓
================================================================================

样本 240: UniMER-1M_0483904
------------------------------------------------------------
输入:
M \times M

输出:
M \times M

状态: 无变化
================================================================================

样本 241: UniMER-1M_0485878
------------------------------------------------------------
输入:
\begin{array} { r } { { \overline { { \mathbf { e } _ { j } } } } ( s ) = \mathbf { r } ^ { ( j ) } ( s ) - \sum _ { i = 1 } ^ { j - 1 } \langle \mathbf { r } ^ { ( j ) } ( s ) , \mathbf { e } _ { i } ( s ) \rangle \, \mathbf { e } _ { i } ( s ) . } \end{array}

输出:
\begin{array}{r}{{{\overline{{{\mathbf{e}_{j}}}}}(s)=\mathbf{r}^{(j)}(s)-\sum_{i=1}^{j-1} \langle \mathbf{r}^{(j)}(s),\mathbf{e}_{i}(s) \rangle \,\mathbf{e}_{i}(s).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 242: UniMER-1M_0487986
------------------------------------------------------------
输入:
\widehat \delta ^ { 2 } = 0 . 5

输出:
\widehat{\delta}^{2}=0.5

状态: 已规范化 ✓
================================================================================

样本 243: UniMER-1M_0489953
------------------------------------------------------------
输入:
\lambda = \bar { U } ^ { - 1 } p ^ { - 1 } \bar { m } , ~ ~ ~ ~ ~ ( \bar { m } = m ^ { - 1 } )

输出:
\lambda=\bar{U}^{-1} p^{-1} \bar{m},~ ~ ~ ~ ~(\bar{m}=m^{-1})

状态: 已规范化 ✓
================================================================================

样本 244: UniMER-1M_0492006
------------------------------------------------------------
输入:
\mathbf { u } = [ \rho , \boldsymbol { \rho } \mathbf { v } , E ] ^ { T } = [ \rho , \rho u , \rho v , \rho w , E ] ^ { T }

输出:
\mathbf{u}=[\rho,\boldsymbol{\rho} \mathbf{v},E]^{T}=[\rho,\rho u,\rho v,\rho w,E]^{T}

状态: 已规范化 ✓
================================================================================

样本 245: UniMER-1M_0494062
------------------------------------------------------------
输入:
\begin{array} { r l r } { \sum \vert M _ { g } ^ { \mathrm { ~ v ~ i ~ o ~ l ~ } } \vert ^ { 2 } } & { { } = } & { g _ { S } ^ { 2 n - 4 } ( Q ^ { 2 } ) ~ N ^ { n - 2 } ( N ^ { 2 } - 1 ) } \end{array}

输出:
\begin{array}{rlr}{{\sum \vert M_{g}^{\mathrm{~v~i~o~l~}} \vert^{2}}} &{{=}} &{{g_{S}^{2 n-4}(Q^{2}) ~ N^{n-2}(N^{2}-1)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 246: UniMER-1M_0496079
------------------------------------------------------------
输入:
s n u . c

输出:
s n u.c

状态: 已规范化 ✓
================================================================================

样本 247: UniMER-1M_0498104
------------------------------------------------------------
输入:
{ \begin{array} { r l } { c _ { T } ( k ) } & { = A k ^ { a } } \\ { c _ { T - 1 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b } } } \\ { c _ { T - 2 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } } } } \\ & { \dots } \\ { c _ { 2 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } + \ldots + a ^ { T - 2 } b ^ { T - 2 } } } } \\ { c _ { 1 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } + \ldots + a ^ { T - 2 } b ^ { T - 2 } + a ^ { T - 1 } b ^ { T - 1 } } } } \\ { c _ { 0 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } + \ldots + a ^ { T - 2 } b ^ { T - 2 } + a ^ { T - 1 } b ^ { T - 1 } + a ^ { T } b ^ { T } } } } \end{array} }

输出:
{\begin{array}{rl}{{c_{T}(k)}} &{{=A k^{a}}} \{{c_{T-1}(k)}} &{{={\frac{A k^{a}}{1+a b}}}} \{{c_{T-2}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}}}}} \&{{. . .}} \{{c_{2}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}+. . .+a^{T-2} b^{T-2}}}}} \{{c_{1}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}+. . .+a^{T-2} b^{T-2}+a^{T-1} b^{T-1}}}}} \{{c_{0}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}+. . .+a^{T-2} b^{T-2}+a^{T-1} b^{T-1}+a^{T} b^{T}}}}} \end{array}}

状态: 已规范化 ✓
================================================================================

样本 248: UniMER-1M_0500109
------------------------------------------------------------
输入:
\begin{array} { r l } { \hat { R } ( t ) } & { { } = \hat { R } _ { 1 } ( t ) + \hat { R } _ { 2 } ( t ) + \hat { R } _ { 3 } ( t ) + \dots . } \end{array}

输出:
\begin{array}{rl}{{\hat{R}(t)}} &{{=\hat{R}_{1}(t)+\hat{R}_{2}(t)+\hat{R}_{3}(t)+. . ..}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 249: UniMER-1M_0502167
------------------------------------------------------------
输入:
N - 1

输出:
N-1

状态: 已规范化 ✓
================================================================================

样本 250: UniMER-1M_0504137
------------------------------------------------------------
输入:
2 \times 1 0 ^ { 1 8 } m ^ { - 3 }

输出:
2 \times 1 0^{1 8} m^{-3}

状态: 已规范化 ✓
================================================================================

样本 251: UniMER-1M_0506211
------------------------------------------------------------
输入:
\begin{array} { r } { p _ { v v ^ { \prime } } ( \Omega ) = - \langle f _ { v } ^ { \Omega } ( r ) | \frac { d U _ { \mathrm { ~ t ~ o ~ t ~ } } ^ { \Omega } ( r ) / d \Omega } { E _ { v } ^ { \Omega } - E _ { v ^ { \prime } } ^ { \Omega } } | f _ { v ^ { \prime } } ^ { \Omega } ( r ) \rangle } \end{array}

输出:
\begin{array}{r}{{p_{v v^{\prime}}(\Omega)=-\langle f_{v}^{\Omega}(r) | \frac{d U_{\mathrm{~t~o~t~}}^{\Omega}(r)/d \Omega}{E_{v}^{\Omega}-E_{v^{\prime}}^{\Omega}} | f_{v^{\prime}}^{\Omega}(r) \rangle}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 252: UniMER-1M_0508235
------------------------------------------------------------
输入:
f ( g , \alpha _ { \mathrm { ~ s ~ o ~ c ~ i ~ a ~ l ~ } } )

输出:
f(g,\alpha_{\mathrm{~s~o~c~i~a~l~}})

状态: 已规范化 ✓
================================================================================

样本 253: UniMER-1M_0510292
------------------------------------------------------------
输入:
1 / 1 4

输出:
1/1 4

状态: 已规范化 ✓
================================================================================

样本 254: UniMER-1M_0512150
------------------------------------------------------------
输入:
N \to \infty \ , \quad \mathrm { ~ g _ { \mathrm { ~ s } } ~ , ~ \ a l p h a ^ { \prime } ~ , a n d ~ s ~ f i x e d \ , } \quad R = ( 4 \pi \alpha ^ { 2 } g _ { \mathrm { s } } N ) ^ { 1 / 4 } \ , \quad \omega = \frac { 1 } { 2 } R s ^ { 1 / 2 } \ .

输出:
N \to \infty,\quad \mathrm{~g_{\mathrm{~s}} ~,~ a l p h a^{\prime} ~,a n d ~ s ~ f i x e d,} \quad R=(4 \pi \alpha^{2} g_{\mathrm{s}} N)^{1/4},\quad \omega=\frac{1}{2} R s^{1/2}.

状态: 已规范化 ✓
================================================================================

样本 255: UniMER-1M_0514360
------------------------------------------------------------
输入:
^ { * \dagger }

输出:
^{*\dagger}

状态: 已规范化 ✓
================================================================================

样本 256: UniMER-1M_0516352
------------------------------------------------------------
输入:
\ddot { \Sigma } _ { n } ^ { \phi } + ( n ^ { 2 } + \frac { m ^ { 2 } } { l ^ { 2 } } ) \Sigma _ { n } ^ { \phi } = \tilde { U } _ { n } ^ { \phi } ,

输出:
\ddot{\Sigma}_{n}^{\phi}+(n^{2}+\frac{m^{2}}{l^{2}}) \Sigma_{n}^{\phi}=\tilde{U}_{n}^{\phi},

状态: 已规范化 ✓
================================================================================

样本 257: UniMER-1M_0518417
------------------------------------------------------------
输入:
n e a r

输出:
n e a r

状态: 无变化
================================================================================

样本 258: UniMER-1M_0520426
------------------------------------------------------------
输入:
[ \lambda _ { \mathrm { F } } , \lambda _ { \mathrm { u b } } ]

输出:
[\lambda_{\mathrm{F}},\lambda_{\mathrm{ub}}]

状态: 已规范化 ✓
================================================================================

样本 259: UniMER-1M_0522463
------------------------------------------------------------
输入:
\sqrt { ( \ell + 1 ) ^ { 2 } - m ^ { 2 } } K _ { \ell + 1 } ^ { m } ( x ) = ( 2 \ell + 1 ) x K _ { \ell } ^ { m } ( x ) - \sqrt { \ell ^ { 2 } - m ^ { 2 } } K _ { \ell - 1 } ^ { m } ( x )

输出:
\sqrt{(\ell+1)^{2}-m^{2}} K_{\ell+1}^{m}(x)=(2 \ell+1) x K_{\ell}^{m}(x)-\sqrt{\ell^{2}-m^{2}} K_{\ell-1}^{m}(x)

状态: 已规范化 ✓
================================================================================

样本 260: UniMER-1M_0524485
------------------------------------------------------------
输入:
\alpha _ { \mathrm { ~ h ~ o ~ m ~ o ~ p ~ h ~ i ~ l ~ y ~ } } > 0 . 5 )

输出:
\alpha_{\mathrm{~h~o~m~o~p~h~i~l~y~}}>0.5)

状态: 已规范化 ✓
================================================================================

样本 261: UniMER-1M_0526538
------------------------------------------------------------
输入:
N > 2

输出:
N>2

状态: 已规范化 ✓
================================================================================

样本 262: UniMER-1M_0528554
------------------------------------------------------------
输入:
\sim 1 0

输出:
\sim 1 0

状态: 无变化
================================================================================

样本 263: UniMER-1M_0530570
------------------------------------------------------------
输入:
\overline { { \mathcal { E } } } _ { r } ^ { ( 0 ) }

输出:
\overline{{{\mathcal{E}}}}_{r}^{(0)}

状态: 已规范化 ✓
================================================================================

样本 264: UniMER-1M_0532560
------------------------------------------------------------
输入:
S = \int d ^ { 4 } x \sqrt { | g _ { E } | } \left[ \hat { R } ( \hat { g } _ { E } ) - { \textstyle \frac { 1 } { 2 } } \frac { \partial _ { \hat { \mu } } \hat { \lambda } \partial ^ { \hat { \mu } } \bar { \hat { \lambda } } } { \left( \Im \mathrm { m } \hat { \lambda } \right) ^ { 2 } } + { \textstyle \frac { 1 } { 4 } } \hat { F } ^ { I } \ { } ^ { \star } \tilde { \hat { F } ^ { I } } \right] \, .

输出:
S=\int d^{4} x \sqrt{| g_{E} |} \left[\hat{R}(\hat{g}_{E})-{\textstyle \frac{1}{2}} \frac{\partial_{\hat{\mu}} \hat{\lambda} \partial^{\hat{\mu}} \bar{\hat{\lambda}}}{\left(\Im \mathrm{m} \hat{\lambda} \right)^{2}}+{\textstyle \frac{1}{4}} \hat{F}^{I}^{\star} \tilde{\hat{F}^{I}} \right] \,.

状态: 已规范化 ✓
================================================================================

样本 265: UniMER-1M_0534580
------------------------------------------------------------
输入:
R = X ( \alpha ) Y ( \beta ) Z ( \gamma )

输出:
R=X(\alpha) Y(\beta) Z(\gamma)

状态: 已规范化 ✓
================================================================================

样本 266: UniMER-1M_0536568
------------------------------------------------------------
输入:
\delta

输出:
\delta

状态: 无变化
================================================================================

样本 267: UniMER-1M_0538656
------------------------------------------------------------
输入:
\vec { \sigma } = \frac { \vec { \sigma } _ { E } } { 1 - \left( \displaystyle \frac { \vec { \sigma } _ { E } } { c } \right) ^ { 2 } } ,

输出:
\vec{\sigma}=\frac{\vec{\sigma}_{E}}{1-\left(\displaystyle \frac{\vec{\sigma}_{E}}{c} \right)^{2}},

状态: 已规范化 ✓
================================================================================

样本 268: UniMER-1M_0540706
------------------------------------------------------------
输入:
P _ { F } ( q ) = P ( q ) \equiv q ^ { 2 } \left( 1 + r _ { F } ( q ) \right) ^ { 2 } \; .

输出:
P_{F}(q)=P(q) \equiv q^{2} \left(1+r_{F}(q) \right)^{2} \;.

状态: 已规范化 ✓
================================================================================

样本 269: UniMER-1M_0542715
------------------------------------------------------------
输入:
p = 3 0

输出:
p=3 0

状态: 已规范化 ✓
================================================================================

样本 270: UniMER-1M_0544677
------------------------------------------------------------
输入:
\begin{array} { r } { r ( t _ { 1 } ) < \frac { \alpha p _ { r } } { \alpha p _ { r } + l _ { i } } < r ( t _ { 2 } ) . } \end{array}

输出:
\begin{array}{r}{{r(t_{1})<\frac{\alpha p_{r}}{\alpha p_{r}+l_{i}}<r(t_{2}).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 271: UniMER-1M_0546718
------------------------------------------------------------
输入:
\begin{array} { r l r } { x \oplus y } & { = } & { f ( f ^ { - 1 } ( x ) + f ^ { - 1 } ( y ) ) , } \\ { x \ominus y } & { = } & { f ( f ^ { - 1 } ( x ) - f ^ { - 1 } ( y ) ) , } \\ { x \otimes y } & { = } & { f ( f ^ { - 1 } ( x ) f ^ { - 1 } ( y ) ) , } \\ { x \oslash y } & { = } & { f ( f ^ { - 1 } ( x ) / f ^ { - 1 } ( y ) ) . } \end{array}

输出:
\begin{array}{rlr}{{x \oplus y}} &{{=}} &{{f(f^{-1}(x)+f^{-1}(y)),}} \{{x \ominus y}} &{{=}} &{{f(f^{-1}(x)-f^{-1}(y)),}} \{{x \otimes y}} &{{=}} &{{f(f^{-1}(x) f^{-1}(y)),}} \{{x \oslash y}} &{{=}} &{{f(f^{-1}(x)/f^{-1}(y)).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 272: UniMER-1M_0548761
------------------------------------------------------------
输入:
( 1 + 2 \rho ) ^ { - 2 }

输出:
(1+2 \rho)^{-2}

状态: 已规范化 ✓
================================================================================

样本 273: UniMER-1M_0550755
------------------------------------------------------------
输入:
\mathbb { E } ( X _ { j } ) = 0

输出:
\mathbb{E}(X_{j})=0

状态: 已规范化 ✓
================================================================================

样本 274: UniMER-1M_0552736
------------------------------------------------------------
输入:
Z ( \beta ) = \int _ { q ( 0 ) = q ( \beta ) } [ { \cal D } q ] \ \exp ( - S [ q ; \beta ] )

输出:
Z(\beta)=\int_{q(0)=q(\beta)}[{\cal D} q] \mathrm{exp}(-S[q;\beta])

状态: 已规范化 ✓
================================================================================

样本 275: UniMER-1M_0554816
------------------------------------------------------------
输入:
X = 1

输出:
X=1

状态: 已规范化 ✓
================================================================================

样本 276: UniMER-1M_0556864
------------------------------------------------------------
输入:
k = 0 . 6

输出:
k=0.6

状态: 已规范化 ✓
================================================================================

样本 277: UniMER-1M_0558862
------------------------------------------------------------
输入:
\frac { A } { 2 \pi } \int d ^ { 2 } x ~ \epsilon ^ { \mu \nu } ~ \partial _ { \mu } \omega _ { \nu } + \frac { B } { 2 \pi } \int d ^ { 2 } x ~ \epsilon ^ { \mu \nu } ~ \partial _ { \mu } a _ { \nu } = ( 2 - 2 g ) A + \frac { B V } { 2 \pi } = N ,

输出:
\frac{A}{2 \pi} \int d^{2} x ~ \epsilon^{\mu \nu} ~ \partial_{\mu} \omega_{\nu}+\frac{B}{2 \pi} \int d^{2} x ~ \epsilon^{\mu \nu} ~ \partial_{\mu} a_{\nu}=(2-2 g) A+\frac{B V}{2 \pi}=N,

状态: 已规范化 ✓
================================================================================

样本 278: UniMER-1M_0560841
------------------------------------------------------------
输入:
\frac { d B _ { p } ( t ) } { d t } = \delta _ { H } ( t ) H ( t ) - h ( t ) .

输出:
\frac{d B_{p}(t)}{d t}=\delta_{H}(t) H(t)-h(t).

状态: 已规范化 ✓
================================================================================

样本 279: UniMER-1M_0562938
------------------------------------------------------------
输入:
- 1

输出:
-1

状态: 已规范化 ✓
================================================================================

样本 280: UniMER-1M_0565003
------------------------------------------------------------
输入:
\beta _ { 0 }

输出:
\beta_{0}

状态: 已规范化 ✓
================================================================================

样本 281: UniMER-1M_0566996
------------------------------------------------------------
输入:
\sum F _ { i } = m { \frac { d V } { d t } } + v _ { \mathrm { e } } { \frac { d m } { d t } }

输出:
\sum F_{i}=m{\frac{d V}{d t}}+v_{\mathrm{e}}{\frac{d m}{d t}}

状态: 已规范化 ✓
================================================================================

样本 282: UniMER-1M_0569064
------------------------------------------------------------
输入:
\lambda

输出:
\lambda

状态: 无变化
================================================================================

样本 283: UniMER-1M_0571023
------------------------------------------------------------
输入:
[ \nabla _ { T } ^ { 2 } + ( \frac { \Omega _ { m l k } ^ { 2 } } { v _ { L } ^ { 2 } } - q _ { m l } ^ { 2 } ) ] { \delta \rho _ { k } ^ { \: m l } } = 0 .

输出:
[\nabla_{T}^{2}+(\frac{\Omega_{m l k}^{2}}{v_{L}^{2}}-q_{m l}^{2})]{\delta \rho_{k}^{\:m l}}=0.

状态: 已规范化 ✓
================================================================================

样本 284: UniMER-1M_0573088
------------------------------------------------------------
输入:
\kappa ( t )

输出:
\kappa(t)

状态: 已规范化 ✓
================================================================================

样本 285: UniMER-1M_0575100
------------------------------------------------------------
输入:
R = \left( \begin{array} { l l } { 0 } & { - 1 } \\ { 1 } & { 0 } \end{array} \right) , \quad Q = \left( \begin{array} { l l } { 0 } & { R } \\ { R } & { 0 } \end{array} \right) , \quad \mathbf { s } _ { t } = ( \hat { \mathbf { J } } _ { t } ^ { s } , \hat { \mathbf { M } } _ { t } ^ { s } ) , \quad \mathbf { s } _ { x } = ( \hat { J } _ { x } ^ { s } , \hat { M } _ { x } ^ { s } )

输出:
R=\left(\begin{array}{ll}{{0}} &{{-1}} \{{1}} &{{0}} \end{array} \right),\quad Q=\left(\begin{array}{ll}{{0}} &{{R}} \{{R}} &{{0}} \end{array} \right),\quad \mathbf{s}_{t}=(\hat{\mathbf{J}}_{t}^{s},\hat{\mathbf{M}}_{t}^{s}),\quad \mathbf{s}_{x}=(\hat{J}_{x}^{s},\hat{M}_{x}^{s})

状态: 已规范化 ✓
================================================================================

样本 286: UniMER-1M_0577105
------------------------------------------------------------
输入:
0 . 3

输出:
0.3

状态: 已规范化 ✓
================================================================================

样本 287: UniMER-1M_0579136
------------------------------------------------------------
输入:
. . .

输出:
...

状态: 已规范化 ✓
================================================================================

样本 288: UniMER-1M_0581157
------------------------------------------------------------
输入:
\Omega

输出:
\Omega

状态: 无变化
================================================================================

样本 289: UniMER-1M_0583188
------------------------------------------------------------
输入:
\Delta = - ( - ) ^ { \epsilon _ { A } } \frac { \delta ^ { R } } { \delta \Phi ^ { A } } \frac { \delta ^ { R } } { \delta \Phi _ { A } ^ { * } }

输出:
\Delta=-(-)^{\epsilon_{A}} \frac{\delta^{R}}{\delta \Phi^{A}} \frac{\delta^{R}}{\delta \Phi_{A}^{*}}

状态: 已规范化 ✓
================================================================================

样本 290: UniMER-1M_0585241
------------------------------------------------------------
输入:
\mathcal { L } _ { \mathrm { ~ d ~ } }

输出:
\mathcal{L}_{\mathrm{~d~}}

状态: 已规范化 ✓
================================================================================

样本 291: UniMER-1M_0587266
------------------------------------------------------------
输入:
\bigstar

输出:
\bigstar

状态: 无变化
================================================================================

样本 292: UniMER-1M_0589284
------------------------------------------------------------
输入:
\operatorname { G a l } ( K _ { \infty } / K ) \simeq \mathbb { Z } _ { p } .

输出:
\operatorname{G a l}(K_{\infty}/K) \simeq \mathbb{Z}_{p}.

状态: 已规范化 ✓
================================================================================

样本 293: UniMER-1M_0591302
------------------------------------------------------------
输入:
Q _ { \gamma } \ = \ { \frac { 2 ^ { n + 3 } \Gamma ( { \frac { n } { 2 } } + 3 ) \Gamma ( { \frac { n } { 2 } } + 4 ) \zeta ( { \frac { n } { 2 } } + 3 ) \zeta ( { \frac { n } { 2 } } + 4 ) } { ( n + 4 ) \pi ^ { 2 } } } { \frac { T ^ { n + 7 } } { M _ { S } ^ { n + 2 } } } \ ,

输出:
Q_{\gamma}={\frac{2^{n+3} \Gamma({\frac{n}{2}}+3) \Gamma({\frac{n}{2}}+4) \zeta({\frac{n}{2}}+3) \zeta({\frac{n}{2}}+4)}{(n+4) \pi^{2}}}{\frac{T^{n+7}}{M_{S}^{n+2}}},

状态: 已规范化 ✓
================================================================================

样本 294: UniMER-1M_0593194
------------------------------------------------------------
输入:
\operatorname* { s u p } _ { t \in [ 0 , T ] } \lVert \partial _ { x } \tilde { u } _ { n } \rVert _ { L ^ { q } ( \Omega ^ { * } ) } \leq C _ { 1 } ( \varphi _ { 0 } , g _ { 0 } , T , q ) , \ \forall q \in [ 1 , + \infty )

输出:
\operatorname{s u p}_{t \in[0,T]} \lVert \partial_{x} \tilde{u}_{n} \rVert_{L^{q}(\Omega^{*})} \leq C_{1}(\varphi_{0},g_{0},T,q),\forall q \in[1,+\infty)

状态: 已规范化 ✓
================================================================================

样本 295: UniMER-1M_0595329
------------------------------------------------------------
输入:
\frac { u _ { B } } { u _ { A } } = \, \frac { \left( 3 \ c _ { s 3 } c _ { s 1 } \rho _ { 3 } \rho _ { 1 } - c _ { s 3 } \rho _ { 2 } \rho _ { 3 } c _ { s 2 } + c _ { s 1 } \rho _ { 2 } \rho _ { 1 } c _ { s 2 } + \rho _ { 2 } ^ { 2 } c _ { s 2 } ^ { 2 } \right) } { ( c _ { s 3 } \rho _ { 3 } + \rho _ { 2 } c _ { s 2 } ) ( c _ { s 1 } \rho _ { 1 } + \rho _ { 2 } c _ { s 2 } ) } \ ,

输出:
\frac{u_{B}}{u_{A}}=\,\frac{\left(3 c_{s 3} c_{s 1} \rho_{3} \rho_{1}-c_{s 3} \rho_{2} \rho_{3} c_{s 2}+c_{s 1} \rho_{2} \rho_{1} c_{s 2}+\rho_{2}^{2} c_{s 2}^{2} \right)}{(c_{s 3} \rho_{3}+\rho_{2} c_{s 2})(c_{s 1} \rho_{1}+\rho_{2} c_{s 2})},

状态: 已规范化 ✓
================================================================================

样本 296: UniMER-1M_0597369
------------------------------------------------------------
输入:
\vec { c }

输出:
\vec{c}

状态: 已规范化 ✓
================================================================================

样本 297: UniMER-1M_0599349
------------------------------------------------------------
输入:
\gamma

输出:
\gamma

状态: 无变化
================================================================================

样本 298: UniMER-1M_0601401
------------------------------------------------------------
输入:
\kappa _ { L } \equiv \kappa _ { 0 } + \kappa _ { 1 } + \kappa _ { 2 }

输出:
\kappa_{L} \equiv \kappa_{0}+\kappa_{1}+\kappa_{2}

状态: 已规范化 ✓
================================================================================

样本 299: UniMER-1M_0603365
------------------------------------------------------------
输入:
\sigma \equiv \sigma _ { \mathrm { i } } \equiv \frac { B ^ { 2 } } { 4 \pi n _ { \mathrm { i } } m _ { \mathrm { i } } c ^ { 2 } } \qquad \beta \equiv \beta _ { \mathrm { i } } \equiv \frac { 8 \pi n _ { \mathrm { i } } k T _ { \mathrm { i } } } { B ^ { 2 } } \, ,

输出:
\sigma \equiv \sigma_{\mathrm{i}} \equiv \frac{B^{2}}{4 \pi n_{\mathrm{i}} m_{\mathrm{i}} c^{2}} \qquad \beta \equiv \beta_{\mathrm{i}} \equiv \frac{8 \pi n_{\mathrm{i}} k T_{\mathrm{i}}}{B^{2}} \,,

状态: 已规范化 ✓
================================================================================

样本 300: UniMER-1M_0605306
------------------------------------------------------------
输入:
\int _ { - \infty } ^ { x } d x ^ { \prime } \, \rho _ { \mathrm { p } } ( \eta ; x ^ { \prime } , t )

输出:
\int_{-\infty}^{x} d x^{\prime} \,\rho_{\mathrm{p}}(\eta;x^{\prime},t)

状态: 已规范化 ✓
================================================================================

样本 301: UniMER-1M_0607433
------------------------------------------------------------
输入:
\begin{array} { r } { H _ { 2 } = \epsilon _ { L } \sum _ { \nu = 1 } ^ { N _ { f } } a _ { L \nu } ^ { \dag } a _ { L \nu } + \epsilon _ { f } ^ { 0 } \sum _ { \nu = 1 } ^ { N _ { f } } a _ { f \nu } ^ { \dag } a _ { f \nu } + \epsilon _ { c } a _ { c } ^ { \dag } a _ { c } + \frac { V } { \sqrt { N _ { f } } } \sum _ { \nu = 1 } ^ { N _ { f } } ( a _ { L \nu } ^ { \dag } a _ { f \nu } + a _ { L \nu } a _ { f \nu } ^ { \dag } ) - U _ { f c } \sum _ { \nu = 1 } ^ { N _ { f } } a _ { f \nu } ^ { \dag } a _ { f \nu } ( 1 - a _ { c } ^ { \dag } a _ { c } ) , } \end{array}

输出:
\begin{array}{r}{{H_{2}=\epsilon_{L} \sum_{\nu=1}^{N_{f}} a_{L \nu}^{\dag} a_{L \nu}+\epsilon_{f}^{0} \sum_{\nu=1}^{N_{f}} a_{f \nu}^{\dag} a_{f \nu}+\epsilon_{c} a_{c}^{\dag} a_{c}+\frac{V}{\sqrt{N_{f}}} \sum_{\nu=1}^{N_{f}}(a_{L \nu}^{\dag} a_{f \nu}+a_{L \nu} a_{f \nu}^{\dag})-U_{f c} \sum_{\nu=1}^{N_{f}} a_{f \nu}^{\dag} a_{f \nu}(1-a_{c}^{\dag} a_{c}),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 302: UniMER-1M_0609488
------------------------------------------------------------
输入:
1 5

输出:
1 5

状态: 无变化
================================================================================

样本 303: UniMER-1M_0611517
------------------------------------------------------------
输入:
F ( x , y , B )

输出:
F(x,y,B)

状态: 已规范化 ✓
================================================================================

样本 304: UniMER-1M_0613503
------------------------------------------------------------
输入:
\Psi _ { i n } ^ { * } = S \Psi _ { o u t } ^ { * } , \quad \Psi _ { o u t } = S \Psi _ { i n }

输出:
\Psi_{i n}^{*}=S \Psi_{o u t}^{*},\quad \Psi_{o u t}=S \Psi_{i n}

状态: 已规范化 ✓
================================================================================

样本 305: UniMER-1M_0615554
------------------------------------------------------------
输入:
\langle j ( x _ { 1 } ) j _ { \nu } ( x _ { 2 } ) \rangle \ne 0 ,

输出:
\langle j(x_{1}) j_{\nu}(x_{2}) \rangle \ne 0,

状态: 已规范化 ✓
================================================================================

样本 306: UniMER-1M_0617548
------------------------------------------------------------
输入:
F ^ { 2 } \, \tilde { A } _ { \pi K } ^ { \mathrm { C D } } = \tilde { \Gamma } _ { \pi } ( 2 M _ { K } ^ { 2 } ) + \tilde { \Delta } _ { \pi K } ~ .

输出:
F^{2} \,\tilde{A}_{\pi K}^{\mathrm{CD}}=\tilde{\Gamma}_{\pi}(2 M_{K}^{2})+\tilde{\Delta}_{\pi K} ~.

状态: 已规范化 ✓
================================================================================

样本 307: UniMER-1M_0619606
------------------------------------------------------------
输入:
\phi

输出:
\phi

状态: 无变化
================================================================================

样本 308: UniMER-1M_0621539
------------------------------------------------------------
输入:
H _ { 3 } P O _ { 4 } : H _ { 2 } O _ { 2 } : H _ { 2 } O

输出:
H_{3} P O_{4}:H_{2} O_{2}:H_{2} O

状态: 已规范化 ✓
================================================================================

样本 309: UniMER-1M_0623644
------------------------------------------------------------
输入:
\mu

输出:
\mu

状态: 无变化
================================================================================

样本 310: UniMER-1M_0625680
------------------------------------------------------------
输入:
M

输出:
M

状态: 无变化
================================================================================

样本 311: UniMER-1M_0627694
------------------------------------------------------------
输入:
| F _ { p } ( z ) | \le 1

输出:
| F_{p}(z) | \le 1

状态: 已规范化 ✓
================================================================================

样本 312: UniMER-1M_0629691
------------------------------------------------------------
输入:
U \neq 0

输出:
U \neq 0

状态: 无变化
================================================================================

样本 313: UniMER-1M_0631741
------------------------------------------------------------
输入:
T _ { y }

输出:
T_{y}

状态: 已规范化 ✓
================================================================================

样本 314: UniMER-1M_0633758
------------------------------------------------------------
输入:
u _ { i }

输出:
u_{i}

状态: 已规范化 ✓
================================================================================

样本 315: UniMER-1M_0635779
------------------------------------------------------------
输入:
Z Z Z Z

输出:
Z Z Z Z

状态: 无变化
================================================================================

样本 316: UniMER-1M_0637777
------------------------------------------------------------
输入:
\mathrm { k } _ { a } \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu } A _ { \sigma } ^ { a } = \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu }

输出:
\mathrm{k}_{a} \frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu} A_{\sigma}^{a}=\frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu}

状态: 已规范化 ✓
================================================================================

样本 317: UniMER-1M_0639805
------------------------------------------------------------
输入:
( \Delta _ { \theta , j } ) _ { 1 , \nu } = \delta _ { \nu - 1 , j , \theta } = \frac { 1 } { N } \sum _ { k = 0 } ^ { N - 1 } ( - J _ { 0 , k } ) ^ { \theta - \nu } \frac { \operatorname* { d e t } ( M _ { \nu - 1 , k } ) } { \operatorname* { d e t } ( M _ { \theta , k } ) } \exp ( \mathrm { i } 2 \pi k j / N ) , \qquad \mathrm { f o r ~ } \nu = 1 , 2 , \ldots , \theta ,

输出:
(\Delta_{\theta,j})_{1,\nu}=\delta_{\nu-1,j,\theta}=\frac{1}{N} \sum_{k=0}^{N-1}(-J_{0,k})^{\theta-\nu} \frac{\operatorname{d e t}(M_{\nu-1,k})}{\operatorname{d e t}(M_{\theta,k})} \mathrm{exp}(\mathrm{i} 2 \pi k j/N),\qquad \mathrm{for~} \nu=1,2,. . .,\theta,

状态: 已规范化 ✓
================================================================================

样本 318: UniMER-1M_0641840
------------------------------------------------------------
输入:
\gamma _ { R }

输出:
\gamma_{R}

状态: 已规范化 ✓
================================================================================

样本 319: UniMER-1M_0643848
------------------------------------------------------------
输入:
4

输出:
4

状态: 无变化
================================================================================

样本 320: UniMER-1M_0645882
------------------------------------------------------------
输入:
P = \sum _ { j = - \infty } ^ { \mathrm { f i n i t e } } a _ { j } ( x ) \partial ^ { j }

输出:
P=\sum_{j=-\infty}^{\mathrm{finite}} a_{j}(x) \partial^{j}

状态: 已规范化 ✓
================================================================================

样本 321: UniMER-1M_0647921
------------------------------------------------------------
输入:
\begin{array} { r } { \dot { \bar { \mathbf { r } } } _ { 1 } = \frac { 5 } { 1 6 \pi \eta _ { \mathrm { s } } } \left[ \left( \mathbf { I } - \frac { 2 } { 5 } \, \mu \boldsymbol { \epsilon } \right) \cdot \left( \beta \mathbf { f } _ { 1 } + \ln \frac { \left| \mathbf { K } _ { 1 2 } ^ { - } \right| } { \left| \mathbf { K } _ { 1 2 } ^ { + } \right| } \mathbf { f } _ { 2 } \right) + \frac { 3 } { 5 } \left( \frac { \mathbf { K } _ { 1 2 } ^ { + } \mathbf { K } _ { 1 2 } ^ { + } } { \left| \mathbf { K } _ { 1 2 } ^ { + } \right| ^ { 2 } } - \frac { \mathbf { K } _ { 1 2 } ^ { - } \mathbf { K } _ { 1 2 } ^ { - } } { \left| \mathbf { K } _ { 1 2 } ^ { - } \right| ^ { 2 } } \right) \cdot \mathbf { f } _ { 2 } \right] + \mathbf { U } _ { 1 2 } \, , } \end{array}

输出:
\begin{array}{r}{{\dot{\bar{\mathbf{r}}}_{1}=\frac{5}{1 6 \pi \eta_{\mathrm{s}}} \left[\left(\mathbf{I}-\frac{2}{5} \,\mu \boldsymbol{\epsilon} \right) \cdot \left(\beta \mathbf{f}_{1}+\mathrm{ln} \frac{\left| \mathbf{K}_{1 2}^{-} \right|}{\left| \mathbf{K}_{1 2}^{+} \right|} \mathbf{f}_{2} \right)+\frac{3}{5} \left(\frac{\mathbf{K}_{1 2}^{+} \mathbf{K}_{1 2}^{+}}{\left| \mathbf{K}_{1 2}^{+} \right|^{2}}-\frac{\mathbf{K}_{1 2}^{-} \mathbf{K}_{1 2}^{-}}{\left| \mathbf{K}_{1 2}^{-} \right|^{2}} \right) \cdot \mathbf{f}_{2} \right]+\mathbf{U}_{1 2} \,,}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 322: UniMER-1M_0649959
------------------------------------------------------------
输入:
\mathcal { T } _ { 2 } \big [ \mathcal { T } _ { N } ^ { - 1 } [ f ] \big ] = f

输出:
\mathcal{T}_{2} \big[\mathcal{T}_{N}^{-1}[f] \big]=f

状态: 已规范化 ✓
================================================================================

样本 323: UniMER-1M_0651936
------------------------------------------------------------
输入:
\displaystyle \frac { e _ { 2 } } { e _ { 1 } + 2 e _ { 2 } - 2 e _ { 1 } e _ { 2 } }

输出:
\displaystyle \frac{e_{2}}{e_{1}+2 e_{2}-2 e_{1} e_{2}}

状态: 已规范化 ✓
================================================================================

样本 324: UniMER-1M_0653999
------------------------------------------------------------
输入:
F T

输出:
F T

状态: 无变化
================================================================================

样本 325: UniMER-1M_0656029
------------------------------------------------------------
输入:
V ( r , J ) \ \sim ( { \frac { J ( J + 1 ) } { r ^ { 2 } } } ) \ + \ C r ^ { ( 2 p - 1 ) } .

输出:
V(r,J) \sim({\frac{J(J+1)}{r^{2}}})+C r^{(2 p-1)}.

状态: 已规范化 ✓
================================================================================

样本 326: UniMER-1M_0658040
------------------------------------------------------------
输入:
\begin{array} { r } { R _ { \widehat { L } _ { \mathrm { t s } } } ( \widehat { L } , t ; t _ { f } | \widehat { L } _ { 0 } ) = \int _ { - \infty } ^ { t } d t _ { 0 } \rho _ { \widehat { L } _ { \mathrm { t s } } } ( t _ { f } | \widehat { L } _ { 0 } , t _ { 0 } ) R _ { \widehat { L } _ { \mathrm { t s } } } ( \widehat { L } , t | \widehat { L } _ { 0 } , t _ { 0 } ; t _ { f } ) \ , } \end{array}

输出:
\begin{array}{r}{{R_{\widehat{L}_{\mathrm{ts}}}(\widehat{L},t;t_{f} | \widehat{L}_{0})=\int_{-\infty}^{t} d t_{0} \rho_{\widehat{L}_{\mathrm{ts}}}(t_{f} | \widehat{L}_{0},t_{0}) R_{\widehat{L}_{\mathrm{ts}}}(\widehat{L},t | \widehat{L}_{0},t_{0};t_{f}),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 327: UniMER-1M_0660102
------------------------------------------------------------
输入:
f ( x ) = x _ { 0 } ^ { - 1 } \left( x / x _ { 0 } \right) ^ { - \alpha - 1 }

输出:
f(x)=x_{0}^{-1} \left(x/x_{0} \right)^{-\alpha-1}

状态: 已规范化 ✓
================================================================================

样本 328: UniMER-1M_0662153
------------------------------------------------------------
输入:
P

输出:
P

状态: 无变化
================================================================================

样本 329: UniMER-1M_0664183
------------------------------------------------------------
输入:
\ell _ { m + N }

输出:
\ell_{m+N}

状态: 已规范化 ✓
================================================================================

样本 330: UniMER-1M_0666179
------------------------------------------------------------
输入:
\zeta ( s ) = \sum _ { n = 1 } ^ { \infty } n ^ { - s }

输出:
\zeta(s)=\sum_{n=1}^{\infty} n^{-s}

状态: 已规范化 ✓
================================================================================

样本 331: UniMER-1M_0668192
------------------------------------------------------------
输入:
Z [ e , \pi ] = \int [ D e _ { i } ^ { a } ] [ D \pi _ { c } ^ { j } ] \ J _ { 0 } \ d e t M _ { \alpha \beta } \, d e l t a ( H _ { \perp } ) \delta ( H _ { i } ) \delta ( J _ { a b } ) \ e x p \frac { i } { \hbar } S ,

输出:
Z[e,\pi]=\int[D e_{i}^{a}][D \pi_{c}^{j}] J_{0} d e t M_{\alpha \beta} \,d e l t a(H_{\perp}) \delta(H_{i}) \delta(J_{a b}) e x p \frac{i}{\hbar} S,

状态: 已规范化 ✓
================================================================================

样本 332: UniMER-1M_0670222
------------------------------------------------------------
输入:
a = \frac { 1 } { B _ { 0 } } , \; \; \; \; \; \; b = \frac { 1 } { B _ { 0 } } \left( - B _ { 1 } + \frac { 1 } { 2 } \right) \; .

输出:
a=\frac{1}{B_{0}},\;\;\;\;\;\;b=\frac{1}{B_{0}} \left(-B_{1}+\frac{1}{2} \right) \;.

状态: 已规范化 ✓
================================================================================

样本 333: UniMER-1M_0672274
------------------------------------------------------------
输入:
D = \left( \begin{array} { l l l } { D _ { x x } } & { i D _ { x y } } & { D _ { x z } } \\ { - i D _ { x y } } & { D _ { y y } } & { i D _ { y z } } \\ { D _ { x z } } & { - i D _ { y z } } & { D _ { z z } } \end{array} \right) ,

输出:
D=\left(\begin{array}{lll}{{D_{x x}}} &{{i D_{x y}}} &{{D_{x z}}} \{{-i D_{x y}}} &{{D_{y y}}} &{{i D_{y z}}} \{{D_{x z}}} &{{-i D_{y z}}} &{{D_{z z}}} \end{array} \right),

状态: 已规范化 ✓
================================================================================

样本 334: UniMER-1M_0674273
------------------------------------------------------------
输入:
c _ { N V ^ { - } } \Gamma _ { N V ^ { - } } = c _ { N V ^ { 0 } } \Gamma _ { N V ^ { 0 } }

输出:
c_{N V^{-}} \Gamma_{N V^{-}}=c_{N V^{0}} \Gamma_{N V^{0}}

状态: 已规范化 ✓
================================================================================

样本 335: UniMER-1M_0676298
------------------------------------------------------------
输入:
( x , y )

输出:
(x,y)

状态: 已规范化 ✓
================================================================================

样本 336: UniMER-1M_0678257
------------------------------------------------------------
输入:
\begin{array} { r l } { b = \, } & { \varsigma ( x _ { 2 } ) + \varsigma ( y _ { 1 } ) + \varsigma ( y _ { 2 } ) - \varsigma ( x _ { 1 } y _ { 1 } ) + \varsigma ( x _ { 2 } y _ { 1 } ) + \varsigma ( x _ { 1 } y _ { 2 } ) + \varsigma ( x _ { 2 } y _ { 2 } ) } \\ & { - \varsigma ( x _ { 1 } ) \varsigma ( y _ { 1 } ) - \varsigma ( x _ { 2 } ) \varsigma ( y _ { 1 } ) - \varsigma ( x _ { 2 } ) \varsigma ( y _ { 2 } ) - \varsigma ( x _ { 1 } ) ^ { 2 } - \varsigma ( y _ { 2 } ) ^ { 2 } \, . } \end{array}

输出:
\begin{array}{rl}{{b=\,}} &{{\varsigma(x_{2})+\varsigma(y_{1})+\varsigma(y_{2})-\varsigma(x_{1} y_{1})+\varsigma(x_{2} y_{1})+\varsigma(x_{1} y_{2})+\varsigma(x_{2} y_{2})}} \&{{-\varsigma(x_{1}) \varsigma(y_{1})-\varsigma(x_{2}) \varsigma(y_{1})-\varsigma(x_{2}) \varsigma(y_{2})-\varsigma(x_{1})^{2}-\varsigma(y_{2})^{2} \,.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 337: UniMER-1M_0680382
------------------------------------------------------------
输入:
n

输出:
n

状态: 无变化
================================================================================

样本 338: UniMER-1M_0682392
------------------------------------------------------------
输入:
i

输出:
i

状态: 无变化
================================================================================

样本 339: UniMER-1M_0684411
------------------------------------------------------------
输入:
\begin{array} { r } { \tau _ { \Pi } = \frac { 2 p T ( D - 3 ) } { 3 D \zeta } , \quad \eta = \frac { 2 ( D - 3 ) } { 3 D } p \tau _ { \Pi } , \quad p = \frac { k _ { \textrm { B } } } { m } \rho T , \quad e = \frac { D } { 2 } \frac { k _ { \textrm { B } } } { m } T , \quad \zeta = \zeta ( \rho , T ) > 0 \ \ \textrm { f o r a n y } \ \ \{ \rho , T \} . } \end{array}

输出:
\begin{array}{r}{{\tau_{\Pi}=\frac{2 p T(D-3)}{3 D \zeta},\quad \eta=\frac{2(D-3)}{3 D} p \tau_{\Pi},\quad p=\frac{k_{\textrm{B}}}{m} \rho T,\quad e=\frac{D}{2} \frac{k_{\textrm{B}}}{m} T,\quad \zeta=\zeta(\rho,T)>0 \textrm{f o r a n y} \{\rho,T \}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 340: UniMER-1M_0686445
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathscr { F } _ { \varepsilon } ( q _ { \varepsilon } ) } & { = \int _ { \Omega } W ^ { \varepsilon } [ q _ { \varepsilon } ] \, \mathrm { d } x , \qquad W ^ { \varepsilon } [ q _ { \varepsilon } ] = W _ { \mathrm { e l a s t } } ( \boldsymbol { F } , \psi ) + W _ { \mathrm { p h a s e } } ^ { \varepsilon } ( \psi , \boldsymbol { F } ^ { - T } \nabla \psi ) , } \end{array}

输出:
\begin{array}{rl}{{\mathscr{F}_{\varepsilon}(q_{\varepsilon})}} &{{=\int_{\Omega} W^{\varepsilon}[q_{\varepsilon}] \,\mathrm{d} x,\qquad W^{\varepsilon}[q_{\varepsilon}]=W_{\mathrm{elast}}(\boldsymbol{F},\psi)+W_{\mathrm{phase}}^{\varepsilon}(\psi,\boldsymbol{F}^{-T} \nabla \psi),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 341: UniMER-1M_0688491
------------------------------------------------------------
输入:
< 5

输出:
<5

状态: 已规范化 ✓
================================================================================

样本 342: UniMER-1M_0690504
------------------------------------------------------------
输入:
a _ { k }

输出:
a_{k}

状态: 已规范化 ✓
================================================================================

样本 343: UniMER-1M_0692503
------------------------------------------------------------
输入:
S _ { n n } = \exp \left( + \pi \sum _ { m \neq n } \frac { | { \cal G } _ { n m } | ^ { 2 } } { | b _ { n } - b _ { m } | } \right) .

输出:
S_{n n}=\mathrm{exp} \left(+\pi \sum_{m \neq n} \frac{|{\cal G}_{n m} |^{2}}{| b_{n}-b_{m} |} \right).

状态: 已规范化 ✓
================================================================================

样本 344: UniMER-1M_0694584
------------------------------------------------------------
输入:
^ c

输出:
^c

状态: 已规范化 ✓
================================================================================

样本 345: UniMER-1M_0696616
------------------------------------------------------------
输入:
e = 1

输出:
e=1

状态: 已规范化 ✓
================================================================================

样本 346: UniMER-1M_0698639
------------------------------------------------------------
输入:
\cal { R }

输出:
\cal{R}

状态: 已规范化 ✓
================================================================================

样本 347: UniMER-1M_0700613
------------------------------------------------------------
输入:
\tau _ { k } A _ { k i } ^ { t o t }

输出:
\tau_{k} A_{k i}^{t o t}

状态: 已规范化 ✓
================================================================================

样本 348: UniMER-1M_0702667
------------------------------------------------------------
输入:
x , y

输出:
x,y

状态: 已规范化 ✓
================================================================================

样本 349: UniMER-1M_0704701
------------------------------------------------------------
输入:
0 . 0 6

输出:
0.0 6

状态: 已规范化 ✓
================================================================================

样本 350: UniMER-1M_0706725
------------------------------------------------------------
输入:
I _ { T }

输出:
I_{T}

状态: 已规范化 ✓
================================================================================

样本 351: UniMER-1M_0708715
------------------------------------------------------------
输入:
Z = 1 3 5

输出:
Z=1 3 5

状态: 已规范化 ✓
================================================================================

样本 352: UniMER-1M_0710805
------------------------------------------------------------
输入:
\Omega

输出:
\Omega

状态: 无变化
================================================================================

样本 353: UniMER-1M_0712824
------------------------------------------------------------
输入:
6 . 5 \times 1 0 ^ { - 3 }

输出:
6.5 \times 1 0^{-3}

状态: 已规范化 ✓
================================================================================

样本 354: UniMER-1M_0714879
------------------------------------------------------------
输入:
\mathrm { E }

输出:
\mathrm{E}

状态: 已规范化 ✓
================================================================================

样本 355: UniMER-1M_0716889
------------------------------------------------------------
输入:
m = n + 4

输出:
m=n+4

状态: 已规范化 ✓
================================================================================

样本 356: UniMER-1M_0718878
------------------------------------------------------------
输入:
\bar { \Theta } ( \mathrm { d o w n } ) \sim \left[ 9 . 0 \times 1 0 ^ { - 3 } \mathrm { I m } X _ { s d } ^ { I } + 6 . 7 \mathrm { I m } X _ { b d } ^ { I } + 2 . 6 \mathrm { I m } X _ { b s } ^ { I } \right] \times 1 0 ^ { - 7 } \ .

输出:
\bar{\Theta}(\mathrm{down}) \sim \left[9.0 \times 1 0^{-3} \mathrm{Im} X_{s d}^{I}+6.7 \mathrm{Im} X_{b d}^{I}+2.6 \mathrm{Im} X_{b s}^{I} \right] \times 1 0^{-7}.

状态: 已规范化 ✓
================================================================================

样本 357: UniMER-1M_0720942
------------------------------------------------------------
输入:
^ { 1 2 }

输出:
^{1 2}

状态: 已规范化 ✓
================================================================================

样本 358: UniMER-1M_0722974
------------------------------------------------------------
输入:
S + ( w )

输出:
S+(w)

状态: 已规范化 ✓
================================================================================

样本 359: UniMER-1M_0724962
------------------------------------------------------------
输入:
\begin{array} { r l } { \operatorname* { m i n } _ { \mathbf { X } } \quad } & { \operatorname { T r } ( \mathbf { X } ) } \\ { \mathrm { s . t . } \quad } & { \operatorname { T r } \left( \mathbf { A } _ { j } \mathbf { X } \right) = y _ { j } , } \\ & { \sum _ { r } \left( \sum _ { s } \left| \mathbf { X } _ { r s } \right| ^ { 2 } \right) ^ { \frac { 1 } { 2 } } < \eta , ~ \mathbf { X } \succeq 0 . } \end{array}

输出:
\begin{array}{rl}{{\mathrm{min}_{\mathbf{X}} \quad}} &{{\operatorname{T r}(\mathbf{X})}} \{{\mathrm{s.t.} \quad}} &{{\operatorname{T r} \left(\mathbf{A}_{j} \mathbf{X} \right)=y_{j},}} \&{{\sum_{r} \left(\sum_{s} \left| \mathbf{X}_{r s} \right|^{2} \right)^{\frac{1}{2}}<\eta,~ \mathbf{X} \succeq 0.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 360: UniMER-1M_0727014
------------------------------------------------------------
输入:
\zeta ( x )

输出:
\zeta(x)

状态: 已规范化 ✓
================================================================================

样本 361: UniMER-1M_0729017
------------------------------------------------------------
输入:
\Delta _ { x } ^ { H C } / \Delta _ { 0 }

输出:
\Delta_{x}^{H C}/\Delta_{0}

状态: 已规范化 ✓
================================================================================

样本 362: UniMER-1M_0731019
------------------------------------------------------------
输入:
\phi

输出:
\phi

状态: 无变化
================================================================================

样本 363: UniMER-1M_0732966
------------------------------------------------------------
输入:
\delta \omega

输出:
\delta \omega

状态: 无变化
================================================================================

样本 364: UniMER-1M_0734988
------------------------------------------------------------
输入:
\begin{array} { r } { { _ 2 F _ { 1 } } ( 1 + m , 1 + q ; 1 ; z ) = \sum _ { k = 0 } ^ { + \infty } \frac { \Gamma ( m + k + 1 ) ^ { 2 } ( - 1 ) ^ { m + q + 1 } } { \Gamma ( 1 + m ) \Gamma ( 1 + q ) \Gamma ( m - q + k + 1 ) } \frac { 1 } { k ! } \big ( \frac { 1 } { z } \big ) ^ { k + m + 1 } . } \end{array}

输出:
\begin{array}{r}{{{_2 F_{1}}(1+m,1+q;1;z)=\sum_{k=0}^{+\infty} \frac{\Gamma(m+k+1)^{2}(-1)^{m+q+1}}{\Gamma(1+m) \Gamma(1+q) \Gamma(m-q+k+1)} \frac{1}{k !} \big(\frac{1}{z} \big)^{k+m+1}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 365: UniMER-1M_0737034
------------------------------------------------------------
输入:
o

输出:
o

状态: 无变化
================================================================================

样本 366: UniMER-1M_0739059
------------------------------------------------------------
输入:
6 , 2 9 9

输出:
6,2 9 9

状态: 已规范化 ✓
================================================================================

样本 367: UniMER-1M_0741083
------------------------------------------------------------
输入:
\varepsilon ^ { i k j } G _ { j m } \gamma _ { p } + \varepsilon ^ { i k j } E _ { j m } = 2 ( - \delta ^ { a i } \gamma _ { p } + 2 \varepsilon ^ { a i n } \Phi ^ { n } ) G _ { a b } \varepsilon ^ { b k m } + ( \delta ^ { a i } - 2 \gamma _ { p } \varepsilon ^ { a i n } \Phi ^ { n } ) ( \Gamma _ { a b m } - \gamma _ { p } S _ { a b m } ) ( \delta ^ { b k } - 2 \gamma _ { p } \varepsilon ^ { b k s } \Phi ^ { s } ) ,

输出:
\varepsilon^{i k j} G_{j m} \gamma_{p}+\varepsilon^{i k j} E_{j m}=2(-\delta^{a i} \gamma_{p}+2 \varepsilon^{a i n} \Phi^{n}) G_{a b} \varepsilon^{b k m}+(\delta^{a i}-2 \gamma_{p} \varepsilon^{a i n} \Phi^{n})(\Gamma_{a b m}-\gamma_{p} S_{a b m})(\delta^{b k}-2 \gamma_{p} \varepsilon^{b k s} \Phi^{s}),

状态: 已规范化 ✓
================================================================================

样本 368: UniMER-1M_0742964
------------------------------------------------------------
输入:
\mathrm { d } T = \frac { T } { c _ { p } } \mathrm { d } \eta - \frac { T } { c _ { p } } \frac { \partial \hat { \mu } } { \partial T } \mathrm { d } S + \Gamma \mathrm { d } p

输出:
\mathrm{d} T=\frac{T}{c_{p}} \mathrm{d} \eta-\frac{T}{c_{p}} \frac{\partial \hat{\mu}}{\partial T} \mathrm{d} S+\Gamma \mathrm{d} p

状态: 已规范化 ✓
================================================================================

样本 369: UniMER-1M_0745049
------------------------------------------------------------
输入:
a ^ { - 2 } + b ^ { - 2 } = d ^ { - 2 }

输出:
a^{-2}+b^{-2}=d^{-2}

状态: 已规范化 ✓
================================================================================

样本 370: UniMER-1M_0747144
------------------------------------------------------------
输入:
\psi _ { n } ( x , t )

输出:
\psi_{n}(x,t)

状态: 已规范化 ✓
================================================================================

样本 371: UniMER-1M_0749209
------------------------------------------------------------
输入:
\beta = 4 \beta _ { c }

输出:
\beta=4 \beta_{c}

状态: 已规范化 ✓
================================================================================

样本 372: UniMER-1M_0751237
------------------------------------------------------------
输入:
\rho _ { \infty } = 1 1 . 4 7

输出:
\rho_{\infty}=1 1.4 7

状态: 已规范化 ✓
================================================================================

样本 373: UniMER-1M_0753246
------------------------------------------------------------
输入:
\left( \begin{array} { c } { E _ { x } } \\ { H _ { y } } \\ { E _ { y } } \\ { H _ { x } } \end{array} \right) _ { z = z _ { 0 } + d } = \mathbf M ( d ) \left( \begin{array} { c } { E _ { x } } \\ { H _ { y } } \\ { E _ { y } } \\ { H _ { x } } \end{array} \right) _ { z = z _ { 0 } } .

输出:
\left(\begin{array}{c}{{E_{x}}} \{{H_{y}}} \{{E_{y}}} \{{H_{x}}} \end{array} \right)_{z=z_{0}+d}=\mathbf{M}(d) \left(\begin{array}{c}{{E_{x}}} \{{H_{y}}} \{{E_{y}}} \{{H_{x}}} \end{array} \right)_{z=z_{0}}.

状态: 已规范化 ✓
================================================================================

样本 374: UniMER-1M_0755253
------------------------------------------------------------
输入:
R = \lvert \rho \rvert - \frac { ( 1 - \rho ) \Omega _ { E } } { \omega _ { L , 1 3 1 } } ( \sin ^ { 2 } \beta + \cos ^ { 2 } \beta \cos \alpha ) .

输出:
R=\lvert \rho \rvert-\frac{(1-\rho) \Omega_{E}}{\omega_{L,1 3 1}}(\mathrm{sin}^{2} \beta+\mathrm{cos}^{2} \beta \mathrm{cos} \alpha).

状态: 已规范化 ✓
================================================================================

样本 375: UniMER-1M_0757264
------------------------------------------------------------
输入:
A _ { 1 } ^ { 2 } + A _ { 2 } ^ { 2 } + A _ { 3 } ^ { 2 } = M ( x _ { 1 } ^ { 2 } ( 2 { \theta } ) + x _ { 2 } ^ { 2 } ( 2 { \theta } ) )

输出:
A_{1}^{2}+A_{2}^{2}+A_{3}^{2}=M(x_{1}^{2}(2{\theta})+x_{2}^{2}(2{\theta}))

状态: 已规范化 ✓
================================================================================

样本 376: UniMER-1M_0759312
------------------------------------------------------------
输入:
\begin{array} { r l } & { h ( t ) = - A _ { 1 } \frac { \pi ^ { \frac { d } { 2 } } t ^ { r } \Gamma ( 2 - \frac { r + d } { 2 } ) } { \Gamma ( 2 - \frac { r } { 2 } ) } { _ 2 F _ { 1 } } ( - \frac { r } { 2 } , 1 - \frac { r + d } { 2 } ; 2 - \frac { r } { 2 } ; \frac { 1 } { t ^ { 2 } } ) + \frac { t ^ { 2 } } { 2 \tau } } \\ & { = - \frac { \Gamma ( 2 - \frac { r } { 2 } ) } { \pi ^ { \frac { d } { 2 } } \Gamma ( 2 - \frac { r + d } { 2 } ) } \frac { \pi ^ { \frac { d } { 2 } } t ^ { r } \Gamma ( 2 - \frac { r + d } { 2 } ) } { \Gamma ( 2 - \frac { r } { 2 } ) } { _ 2 F _ { 1 } } ( - \frac { r } { 2 } , 1 - \frac { r + d } { 2 } ; 2 - \frac { r } { 2 } ; \frac { 1 } { t ^ { 2 } } ) + \frac { r \Gamma ( 2 - \frac { r } 2 ) \Gamma ( \frac { d + r } { 2 } ) t ^ { 2 } } { 2 \Gamma ( 1 + \frac { d } { 2 } ) } } \\ & { = - t ^ { r } { _ 2 F _ { 1 } } ( - \frac { r } { 2 } , 1 - \frac { r + d } { 2 } ; 2 - \frac { r } { 2 } ; \frac { 1 } { t ^ { 2 } } ) + \frac { r \Gamma ( 2 - \frac { r } 2 ) \Gamma ( \frac { d + r } { 2 } ) t ^ { 2 } } { 2 \Gamma ( 1 + \frac { d } { 2 } ) } . } \end{array}

输出:
\begin{array}{rl} &{{h(t)=-A_{1} \frac{\pi^{\frac{d}{2}} t^{r} \Gamma(2-\frac{r+d}{2})}{\Gamma(2-\frac{r}{2})}{_2 F_{1}}(-\frac{r}{2},1-\frac{r+d}{2};2-\frac{r}{2};\frac{1}{t^{2}})+\frac{t^{2}}{2 \tau}}} \&{{=-\frac{\Gamma(2-\frac{r}{2})}{\pi^{\frac{d}{2}} \Gamma(2-\frac{r+d}{2})} \frac{\pi^{\frac{d}{2}} t^{r} \Gamma(2-\frac{r+d}{2})}{\Gamma(2-\frac{r}{2})}{_2 F_{1}}(-\frac{r}{2},1-\frac{r+d}{2};2-\frac{r}{2};\frac{1}{t^{2}})+\frac{r \Gamma(2-\frac{r} 2) \Gamma(\frac{d+r}{2}) t^{2}}{2 \Gamma(1+\frac{d}{2})}}} \&{{=-t^{r}{_2 F_{1}}(-\frac{r}{2},1-\frac{r+d}{2};2-\frac{r}{2};\frac{1}{t^{2}})+\frac{r \Gamma(2-\frac{r} 2) \Gamma(\frac{d+r}{2}) t^{2}}{2 \Gamma(1+\frac{d}{2})}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 377: UniMER-1M_0761367
------------------------------------------------------------
输入:
^ { - 1 }

输出:
^{-1}

状态: 已规范化 ✓
================================================================================

样本 378: UniMER-1M_0763389
------------------------------------------------------------
输入:
n

输出:
n

状态: 无变化
================================================================================

样本 379: UniMER-1M_0765401
------------------------------------------------------------
输入:
5 ~ \mathrm { m d e g }

输出:
5 ~ \mathrm{mdeg}

状态: 已规范化 ✓
================================================================================

样本 380: UniMER-1M_0767382
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathcal { G } \; : } & { \; \mathbb { Z } \times \mathbb { L } ^ { \infty } ( \mathcal { U } ) \longrightarrow \mathbb { L } ^ { \infty } ( \mathcal { U } ) } \\ & { ( z , u ) \mapsto ( \textbf { g } _ { 1 } ( z , u _ { 1 } ) , \textbf { g } _ { 2 } ( z , u _ { 2 } ) , \textbf { g } _ { 3 } ( z , u _ { 3 } ) ) , } \end{array}

输出:
\begin{array}{rl}{{\mathcal{G} \;:}} &{{\;\mathbb{Z} \times \mathbb{L}^{\infty}(\mathcal{U}) \longrightarrow \mathbb{L}^{\infty}(\mathcal{U})}} \&{{(z,u) \mapsto(\textbf{g}_{1}(z,u_{1}),\textbf{g}_{2}(z,u_{2}),\textbf{g}_{3}(z,u_{3})),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 381: UniMER-1M_0769458
------------------------------------------------------------
输入:
0 . 0 5

输出:
0.0 5

状态: 已规范化 ✓
================================================================================

样本 382: UniMER-1M_0771509
------------------------------------------------------------
输入:
\begin{array} { r l } { G _ { S } } & { { } = \frac { 1 } { 2 \alpha \rho \left| \chi _ { \mathrm { ~ r ~ f ~ l ~ } } \right| ^ { 2 } Y ^ { \mathrm { ~ b ~ g ~ } } S _ { \mathrm { ~ m ~ c ~ } } } \biggl [ 2 N _ { c } ^ { b } ( 1 - Y ^ { \mathrm { ~ b ~ g ~ } } ) + 2 N _ { r } ^ { b } \biggr . } \end{array}

输出:
\begin{array}{rl}{{G_{S}}} &{{=\frac{1}{2 \alpha \rho \left| \chi_{\mathrm{~r~f~l~}} \right|^{2} Y^{\mathrm{~b~g~}} S_{\mathrm{~m~c~}}} \biggl[2 N_{c}^{b}(1-Y^{\mathrm{~b~g~}})+2 N_{r}^{b} \biggr.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 383: UniMER-1M_0773575
------------------------------------------------------------
输入:
t = 0

输出:
t=0

状态: 已规范化 ✓
================================================================================

样本 384: UniMER-1M_0775548
------------------------------------------------------------
输入:
\mathbf { b }

输出:
\mathbf{b}

状态: 已规范化 ✓
================================================================================

样本 385: UniMER-1M_0777584
------------------------------------------------------------
输入:
M _ { + 1 } ^ { 2 } = \frac { \lambda _ { 7 } } { 2 } ( v _ { \eta } ^ { 2 } + v _ { \rho } ^ { 2 } ) - \frac { A } { \sqrt 2 } \left( \frac { 1 } { v _ { \rho } ^ { 2 } } + \frac { 1 } { v _ { \chi } ^ { 2 } } \right) , \; M _ { + 2 } ^ { 2 } = \frac { \lambda _ { 8 } } { 2 } ( v _ { \eta } ^ { 2 } + v _ { \chi } ^ { 2 } ) - \frac { A } { \sqrt 2 } \left( \frac { 1 } { v _ { \rho } ^ { 2 } } + \frac { 1 } { v _ { \chi } ^ { 2 } } \right) , \;

输出:
M_{+1}^{2}=\frac{\lambda_{7}}{2}(v_{\eta}^{2}+v_{\rho}^{2})-\frac{A}{\sqrt 2} \left(\frac{1}{v_{\rho}^{2}}+\frac{1}{v_{\chi}^{2}} \right),\;M_{+2}^{2}=\frac{\lambda_{8}}{2}(v_{\eta}^{2}+v_{\chi}^{2})-\frac{A}{\sqrt 2} \left(\frac{1}{v_{\rho}^{2}}+\frac{1}{v_{\chi}^{2}} \right),\;

状态: 已规范化 ✓
================================================================================

样本 386: UniMER-1M_0779628
------------------------------------------------------------
输入:
- ( 1 / 2 ) \operatorname { R e } \int _ { V } \mathbf { J } ^ { * } \cdot \mathbf { E }

输出:
-(1/2) \operatorname{R e} \int_{V} \mathbf{J}^{*} \cdot \mathbf{E}

状态: 已规范化 ✓
================================================================================

样本 387: UniMER-1M_0781669
------------------------------------------------------------
输入:
\langle a _ { n } ^ { 2 } \rangle = \frac { 2 k _ { B } T } { \gamma \pi ^ { 2 } } \frac { L _ { x } } { L _ { y } } \frac { 1 } { n ^ { 2 } } ,

输出:
\langle a_{n}^{2} \rangle=\frac{2 k_{B} T}{\gamma \pi^{2}} \frac{L_{x}}{L_{y}} \frac{1}{n^{2}},

状态: 已规范化 ✓
================================================================================

样本 388: UniMER-1M_0783662
------------------------------------------------------------
输入:
\begin{array} { r l } { \bigg | \sum _ { y _ { \alpha } \in B _ { 1 } \setminus B _ { r } } } & { \int _ { Q _ { \alpha } } ( J _ { p } ( \phi ( x + y ) - \phi ( x ) ) - J _ { p } ( \phi ( x + y _ { \alpha } ) - \phi ( x ) ) ) \frac { \, \mathrm { d } y } { | y | ^ { d + s p } } \bigg | } \\ & { \leq C h \sum _ { y _ { \alpha } \in B _ { 1 } \setminus B _ { r } } \int _ { Q _ { \alpha } } ( | \phi ( x + y ) - \phi ( x ) | ^ { p - 2 } + | \phi ( x + y _ { \alpha } ) - \phi ( x ) | ^ { p - 2 } ) \frac { \, \mathrm { d } y } { | y | ^ { d + s p } } } \\ & { \leq C h \int _ { B _ { 3 / 2 } \setminus B _ { r / 2 } } \ | y | ^ { p - 2 } \frac { \, \mathrm { d } y } { | y | ^ { d + s p } } \leq C h ( 1 + r ^ { p - 2 - s p } ) . } \end{array}

输出:
\begin{array}{rl}{{\bigg| \sum_{y_{\alpha} \in B_{1} \setminus B_{r}}}} &{{\int_{Q_{\alpha}}(J_{p}(\phi(x+y)-\phi(x))-J_{p}(\phi(x+y_{\alpha})-\phi(x))) \frac{\,\mathrm{d} y}{| y |^{d+s p}} \bigg|}} \&{{\leq C h \sum_{y_{\alpha} \in B_{1} \setminus B_{r}} \int_{Q_{\alpha}}(| \phi(x+y)-\phi(x) |^{p-2}+| \phi(x+y_{\alpha})-\phi(x) |^{p-2}) \frac{\,\mathrm{d} y}{| y |^{d+s p}}}} \&{{\leq C h \int_{B_{3/2} \setminus B_{r/2}} | y |^{p-2} \frac{\,\mathrm{d} y}{| y |^{d+s p}} \leq C h(1+r^{p-2-s p}).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 389: UniMER-1M_0785710
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathrm { F e } + \frac { 1 } { 2 } \mathrm { O } _ { 2 } } & { { } = \mathrm { F e O } , } \\ { \mathrm { F e O } + \frac { 1 } { 4 } \mathrm { O } _ { 2 } } & { { } = \mathrm { F e O } _ { 1 . 5 } . } \end{array}

输出:
\begin{array}{rl}{{\mathrm{Fe}+\frac{1}{2} \mathrm{O}_{2}}} &{{=\mathrm{FeO},}} \{{\mathrm{FeO}+\frac{1}{4} \mathrm{O}_{2}}} &{{=\mathrm{FeO}_{1.5}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 390: UniMER-1M_0787739
------------------------------------------------------------
输入:
- { \frac { S _ { 1 } } { c ^ { 2 } | S | ^ { 2 } } } , { \frac { S _ { 2 } } { c ^ { 2 } | S | ^ { 2 } } } , c G

输出:
-{\frac{S_{1}}{c^{2} | S |^{2}}},{\frac{S_{2}}{c^{2} | S |^{2}}},c G

状态: 已规范化 ✓
================================================================================

样本 391: UniMER-1M_0789701
------------------------------------------------------------
输入:
\| u \| _ { C ^ { 0 , \alpha } ( B _ { r } ) } \le \frac { C ( N , p , \mu , \alpha , \omega _ { A _ { \pm } , r ^ { * } } ) } { ( 1 - r ) ^ { \alpha } } \Bigg ( \| u \| _ { L ^ { \infty } ( B _ { 1 } ) } + \| f _ { + } \| _ { L ^ { N } ( B _ { 1 } ) } ^ { \frac { 1 } { p - 1 } } + \| f _ { - } \| _ { L ^ { N } ( B _ { 1 } ) } ^ { \frac { 1 } { p - 1 } } \Bigg )

输出:
\| u \|_{C^{0,\alpha}(B_{r})} \le \frac{C(N,p,\mu,\alpha,\omega_{A_{\pm},r^{*}})}{(1-r)^{\alpha}} \Bigg(\| u \|_{L^{\infty}(B_{1})}+\| f_{+} \|_{L^{N}(B_{1})}^{\frac{1}{p-1}}+\| f_{-} \|_{L^{N}(B_{1})}^{\frac{1}{p-1}} \Bigg)

状态: 已规范化 ✓
================================================================================

样本 392: UniMER-1M_0791787
------------------------------------------------------------
输入:
J _ { c o l l i s i o n } = { \frac { 1 } { 4 } } n { \bar { v } } = { \frac { n } { 4 } } { \sqrt { \frac { 8 k _ { B } T } { \pi m } } } .

输出:
J_{c o l l i s i o n}={\frac{1}{4}} n{\bar{v}}={\frac{n}{4}}{\sqrt{\frac{8 k_{B} T}{\pi m}}}.

状态: 已规范化 ✓
================================================================================

样本 393: UniMER-1M_0793813
------------------------------------------------------------
输入:
\nabla _ { \mathbf { v } } \mathbf { u }

输出:
\nabla_{\mathbf{v}} \mathbf{u}

状态: 已规范化 ✓
================================================================================

样本 394: UniMER-1M_0795852
------------------------------------------------------------
输入:
P \sim 1

输出:
P \sim 1

状态: 无变化
================================================================================

样本 395: UniMER-1M_0797886
------------------------------------------------------------
输入:
0 . 6

输出:
0.6

状态: 已规范化 ✓
================================================================================

样本 396: UniMER-1M_0799940
------------------------------------------------------------
输入:
\beta

输出:
\beta

状态: 无变化
================================================================================

样本 397: UniMER-1M_0801920
------------------------------------------------------------
输入:
\textrm { D a } _ { d } \gg 1

输出:
\textrm{D a}_{d} \gg 1

状态: 已规范化 ✓
================================================================================

样本 398: UniMER-1M_0803962
------------------------------------------------------------
输入:
\emptyset

输出:
\em ptyset

状态: 已规范化 ✓
================================================================================

样本 399: UniMER-1M_0805963
------------------------------------------------------------
输入:
\begin{array} { r } { S _ { U U } ( f ) = \int _ { - \infty } ^ { \infty } R _ { U U } ( \tau ) e ^ { - 2 \pi i f \tau } d \tau = g _ { \mathrm { ~ e ~ l ~ } } ^ { 2 } \eta ^ { 2 } \langle \hat { S } _ { y , \mathrm { ~ i ~ n ~ } } \rangle ^ { 2 } \chi ^ { 2 } S _ { z z } ( f ) + \frac { 1 } { 2 } g _ { \mathrm { ~ e ~ l ~ } } ^ { 2 } \eta \cos ^ { 2 } \alpha \langle \hat { S } _ { 0 , \mathrm { ~ i ~ n ~ } } \rangle . } \end{array}

输出:
\begin{array}{r}{{S_{U U}(f)=\int_{-\infty}^{\infty} R_{U U}(\tau) e^{-2 \pi i f \tau} d \tau=g_{\mathrm{~e~l~}}^{2} \eta^{2} \langle \hat{S}_{y,\mathrm{~i~n~}} \rangle^{2} \chi^{2} S_{z z}(f)+\frac{1}{2} g_{\mathrm{~e~l~}}^{2} \eta \mathrm{cos}^{2} \alpha \langle \hat{S}_{0,\mathrm{~i~n~}} \rangle.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 400: UniMER-1M_0807953
------------------------------------------------------------
输入:
\hat { \Gamma } = \left( \begin{array} { l l l l } { \gamma } & { 0 } & { 0 } & { 0 } \\ { 0 } & { \gamma } & { 0 } & { 0 } \\ { 0 } & { 0 } & { \gamma } & { 0 } \\ { 0 } & { 0 } & { 0 } & { \gamma + \Gamma } \end{array} \right) ,

输出:
\hat{\Gamma}=\left(\begin{array}{llll}{{\gamma}} &{{0}} &{{0}} &{{0}} \{{0}} &{{\gamma}} &{{0}} &{{0}} \{{0}} &{{0}} &{{\gamma}} &{{0}} \{{0}} &{{0}} &{{0}} &{{\gamma+\Gamma}} \end{array} \right),

状态: 已规范化 ✓
================================================================================

样本 401: UniMER-1M_0810001
------------------------------------------------------------
输入:
\hat { f } _ { i j k } ^ { l , - 1 } = \hat { f } _ { i j k } ^ { l , 0 } ; \: \hat { f } _ { i j k } ^ { l , N _ { x } + 2 } = \hat { f } _ { i j k } ^ { l , N _ { x } + 1 } .

输出:
\hat{f}_{i j k}^{l,-1}=\hat{f}_{i j k}^{l,0};\:\hat{f}_{i j k}^{l,N_{x}+2}=\hat{f}_{i j k}^{l,N_{x}+1}.

状态: 已规范化 ✓
================================================================================

样本 402: UniMER-1M_0812006
------------------------------------------------------------
输入:
\begin{array} { r l } { S _ { 3 } ^ { u } = } & { { \nu _ { \tau } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } \\ { \overline { { b ^ { r } } } { C _ { 1 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } & { \overline { { b ^ { g } } } { C _ { 2 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + \overline { { b ^ { b } } } { C _ { 3 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } \\ { t ^ { r } { C _ { 3 } ^ { \dagger } } { C _ { 2 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } & { t ^ { g } { C _ { 1 } ^ { \dagger } } { C _ { 3 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + t ^ { b } { C _ { 2 } ^ { \dagger } } { C _ { 1 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } \\ & { \tau ^ { + } { C _ { 3 } ^ { \dagger } } { C _ { 2 } ^ { \dagger } } { C _ { 1 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } } \end{array} \qquad \begin{array} { r l } { S _ { 3 } ^ { d } = } & { { \overline { { \nu } } _ { \tau } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } \\ { { b } ^ { r } { C _ { 1 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } & { { b } ^ { g } { C _ { 2 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + { b } ^ { b } { C _ { 3 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } \\ { \overline { { t ^ { r } } } { C _ { 3 } } { C _ { 2 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } & { \overline { { t ^ { g } } } { C _ { 1 } } { C _ { 3 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + \overline { { t ^ { b } } } { C _ { 2 } } { C _ { 1 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } \\ & { \tau ^ { - } { C _ { 3 } } { C _ { 2 } } { C _ { 1 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } } \end{array}

输出:
\begin{array}{rl}{{S_{3}^{u}=}} &{{{\nu_{\tau}} \omega_{3} \omega_{3}^{\dagger}+}} \{{\overline{{{b^{r}}}}{C_{1}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} &{{\overline{{{b^{g}}}}{C_{2}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+\overline{{{b^{b}}}}{C_{3}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} \{{t^{r}{C_{3}^{\dagger}}{C_{2}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} &{{t^{g}{C_{1}^{\dagger}}{C_{3}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+t^{b}{C_{2}^{\dagger}}{C_{1}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} \&{{\tau^{+}{C_{3}^{\dagger}}{C_{2}^{\dagger}}{C_{1}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}}} \end{array} \qquad \begin{array}{rl}{{S_{3}^{d}=}} &{{{\overline{{{\nu}}}_{\tau}} \omega_{3}^{\dagger} \omega_{3}+}} \{{{b}^{r}{C_{1}} \omega_{3}^{\dagger} \omega_{3}+}} &{{{b}^{g}{C_{2}} \omega_{3}^{\dagger} \omega_{3}+{b}^{b}{C_{3}} \omega_{3}^{\dagger} \omega_{3}+}} \{{\overline{{{t^{r}}}}{C_{3}}{C_{2}} \omega_{3}^{\dagger} \omega_{3}+}} &{{\overline{{{t^{g}}}}{C_{1}}{C_{3}} \omega_{3}^{\dagger} \omega_{3}+\overline{{{t^{b}}}}{C_{2}}{C_{1}} \omega_{3}^{\dagger} \omega_{3}+}} \&{{\tau^{-}{C_{3}}{C_{2}}{C_{1}} \omega_{3}^{\dagger} \omega_{3}}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 403: UniMER-1M_0814022
------------------------------------------------------------
输入:
\widehat { \cal M } _ { \chi } \ \approx \ \mathrm { d i a g } \, \bigg ( \, m , \ \sqrt { m ^ { 2 } + \frac { 1 } { R ^ { 2 } } } \ , \ \sqrt { m ^ { 2 } + \frac { 1 } { R ^ { 2 } } } \ , \ \cdots , \ \sqrt { m ^ { 2 } + \frac { n ^ { 2 } } { R ^ { 2 } } } \ , \ \sqrt { m ^ { 2 } + \frac { n ^ { 2 } } { R ^ { 2 } } } \ , \ \cdots \, \bigg ) \, .

输出:
\widehat{\cal M}_{\chi} \approx \mathrm{diag} \,\bigg(\,m,\sqrt{m^{2}+\frac{1}{R^{2}}},\sqrt{m^{2}+\frac{1}{R^{2}}},. . .,\sqrt{m^{2}+\frac{n^{2}}{R^{2}}},\sqrt{m^{2}+\frac{n^{2}}{R^{2}}},. . . \,\bigg) \,.

状态: 已规范化 ✓
================================================================================

样本 404: UniMER-1M_0816044
------------------------------------------------------------
输入:
\tilde { B } _ { \mu } ^ { ( j ; m ) } = \frac { 1 } { 1 - | u | ^ { 2 } } ( \partial _ { \mu } u P _ { m } ^ { ( j ) } + \partial _ { \mu } \bar { u } P _ { - m } ^ { ( j ) } )

输出:
\tilde{B}_{\mu}^{(j;m)}=\frac{1}{1-| u |^{2}}(\partial_{\mu} u P_{m}^{(j)}+\partial_{\mu} \bar{u} P_{-m}^{(j)})

状态: 已规范化 ✓
================================================================================

样本 405: UniMER-1M_0818065
------------------------------------------------------------
输入:
\boldsymbol { u }

输出:
\boldsymbol{u}

状态: 已规范化 ✓
================================================================================

样本 406: UniMER-1M_0820056
------------------------------------------------------------
输入:
\sigma = 1

输出:
\sigma=1

状态: 已规范化 ✓
================================================================================

样本 407: UniMER-1M_0822114
------------------------------------------------------------
输入:
\varepsilon ^ { 2 }

输出:
\varepsilon^{2}

状态: 已规范化 ✓
================================================================================

样本 408: UniMER-1M_0824149
------------------------------------------------------------
输入:
\mathbb { E }

输出:
\mathbb{E}

状态: 已规范化 ✓
================================================================================

样本 409: UniMER-1M_0826167
------------------------------------------------------------
输入:
B _ { 0 }

输出:
B_{0}

状态: 已规范化 ✓
================================================================================

样本 410: UniMER-1M_0828136
------------------------------------------------------------
输入:
\Phi ( x ) = \oint { \frac { d z } { z } } \exp \left( \sum _ { m \neq 0 } { \frac { i x _ { m } } { 2 Q m z ^ { m } } } \right) .

输出:
\Phi(x)=\oint{\frac{d z}{z}} \mathrm{exp} \left(\sum_{m \neq 0}{\frac{i x_{m}}{2 Q m z^{m}}} \right).

状态: 已规范化 ✓
================================================================================

样本 411: UniMER-1M_0830163
------------------------------------------------------------
输入:
x z

输出:
x z

状态: 无变化
================================================================================

样本 412: UniMER-1M_0832210
------------------------------------------------------------
输入:
L

输出:
L

状态: 无变化
================================================================================

样本 413: UniMER-1M_0834205
------------------------------------------------------------
输入:
x = \pm 5 0

输出:
x=\pm 5 0

状态: 已规范化 ✓
================================================================================

样本 414: UniMER-1M_0836281
------------------------------------------------------------
输入:
\psi

输出:
\psi

状态: 无变化
================================================================================

样本 415: UniMER-1M_0838281
------------------------------------------------------------
输入:
\epsilon \to 0

输出:
\epsilon \to 0

状态: 无变化
================================================================================

样本 416: UniMER-1M_0840291
------------------------------------------------------------
输入:
K _ { h }

输出:
K_{h}

状态: 已规范化 ✓
================================================================================

样本 417: UniMER-1M_0842311
------------------------------------------------------------
输入:
G ( f ) _ { \beta } ^ { ( n ) } = \Sigma _ { m = 0 } ^ { n } \{ \tilde { \Theta } _ { \beta } ^ { ( n - m ) } , \tilde { f } ^ { ( m ) } \} _ { ( q ) } + \Sigma _ { m = 0 } ^ { ( n - 2 ) } \{ \tilde { \Theta } _ { \beta } ^ { ( n - m ) } , \tilde { f } ^ { ( m + 2 ) } \} _ { ( \phi ) } + \{ \tilde { \Theta } _ { \beta } ^ { ( n + 1 ) } , \tilde { f } ^ { ( 1 ) } \} _ { ( \phi ) }

输出:
G(f)_{\beta}^{(n)}=\Sigma_{m=0}^{n} \{\tilde{\Theta}_{\beta}^{(n-m)},\tilde{f}^{(m)} \}_{(q)}+\Sigma_{m=0}^{(n-2)} \{\tilde{\Theta}_{\beta}^{(n-m)},\tilde{f}^{(m+2)} \}_{(\phi)}+\{\tilde{\Theta}_{\beta}^{(n+1)},\tilde{f}^{(1)} \}_{(\phi)}

状态: 已规范化 ✓
================================================================================

样本 418: UniMER-1M_0844386
------------------------------------------------------------
输入:
d

输出:
d

状态: 无变化
================================================================================

样本 419: UniMER-1M_0846405
------------------------------------------------------------
输入:
q _ { \mathrm { l } } ^ { * } = \frac { D q _ { \mathrm { t r } } ^ { * } } { \left( D - 1 \right) \phi + 1 } \, ,

输出:
q_{\mathrm{l}}^{*}=\frac{D q_{\mathrm{tr}}^{*}}{\left(D-1 \right) \phi+1} \,,

状态: 已规范化 ✓
================================================================================

样本 420: UniMER-1M_0848420
------------------------------------------------------------
输入:
\pi / 2

输出:
\pi/2

状态: 已规范化 ✓
================================================================================

样本 421: UniMER-1M_0850454
------------------------------------------------------------
输入:
f ( \epsilon ) \equiv [ 1 + ( \epsilon T _ { 2 } / 2 ) ^ { 2 } ]

输出:
f(\epsilon) \equiv[1+(\epsilon T_{2}/2)^{2}]

状态: 已规范化 ✓
================================================================================

样本 422: UniMER-1M_0852470
------------------------------------------------------------
输入:
b _ { m _ { 1 } , m _ { 2 } } ^ { 2 } ( f _ { 1 } , f _ { 2 } )

输出:
b_{m_{1},m_{2}}^{2}(f_{1},f_{2})

状态: 已规范化 ✓
================================================================================

样本 423: UniMER-1M_0854523
------------------------------------------------------------
输入:
t = \mathrm { i } \sqrt { 1 - r ^ { 2 } }

输出:
t=\mathrm{i} \sqrt{1-r^{2}}

状态: 已规范化 ✓
================================================================================

样本 424: UniMER-1M_0856529
------------------------------------------------------------
输入:
\tau = \operatorname* { m a x } \{ \tau _ { x } , \tau _ { y } \}

输出:
\tau=\mathrm{max} \{\tau_{x},\tau_{y} \}

状态: 已规范化 ✓
================================================================================

样本 425: UniMER-1M_0858594
------------------------------------------------------------
输入:
N = 5 1 2

输出:
N=5 1 2

状态: 已规范化 ✓
================================================================================

样本 426: UniMER-1M_0860578
------------------------------------------------------------
输入:
Z _ { M } ( f ( \rho ) , g ( \lambda ) ) = Z _ { W } ( \lambda , \rho ) \; \; ,

输出:
Z_{M}(f(\rho),g(\lambda))=Z_{W}(\lambda,\rho) \;\;,

状态: 已规范化 ✓
================================================================================

样本 427: UniMER-1M_0862561
------------------------------------------------------------
输入:
\tau _ { i j } ^ { N } = \mu \dot { \gamma } _ { i j } – p \delta _ { i j }

输出:
\tau_{i j}^{N}=\mu \dot{\gamma}_{i j} – p \delta_{i j}

状态: 已规范化 ✓
================================================================================

样本 428: UniMER-1M_0864659
------------------------------------------------------------
输入:
\hat { H } = \frac { h \nu _ { c } } { 2 } \left( \hat { X } ^ { 2 } + \hat { Y } ^ { 2 } \right)

输出:
\hat{H}=\frac{h \nu_{c}}{2} \left(\hat{X}^{2}+\hat{Y}^{2} \right)

状态: 已规范化 ✓
================================================================================

样本 429: UniMER-1M_0866666
------------------------------------------------------------
输入:
w ^ { \mathrm { B L M } } ( \mu ) = \int _ { 0 } ^ { \infty } \; \frac { \mathrm { d } \lambda ^ { 2 } } { \lambda ^ { 2 } } \; W ( \lambda ^ { 2 } ) \, \phi ^ { \mathrm { B L M } } ( \lambda ^ { 2 } ; \mu ) \; \; .

输出:
w^{\mathrm{BLM}}(\mu)=\int_{0}^{\infty} \;\frac{\mathrm{d} \lambda^{2}}{\lambda^{2}} \;W(\lambda^{2}) \,\phi^{\mathrm{BLM}}(\lambda^{2};\mu) \;\;.

状态: 已规范化 ✓
================================================================================

样本 430: UniMER-1M_0868689
------------------------------------------------------------
输入:
\xi \sim 3 . 5 - 4

输出:
\xi \sim 3.5-4

状态: 已规范化 ✓
================================================================================

样本 431: UniMER-1M_0870750
------------------------------------------------------------
输入:
^ \circ

输出:
^\circ

状态: 已规范化 ✓
================================================================================

样本 432: UniMER-1M_0872742
------------------------------------------------------------
输入:
C _ { 0 }

输出:
C_{0}

状态: 已规范化 ✓
================================================================================

样本 433: UniMER-1M_0874772
------------------------------------------------------------
输入:
\frac { C _ { \alpha } } { D _ { \alpha } } h ^ { 2 } \sum _ { j _ { 1 } = 0 } ^ { N - 1 } \sum _ { j _ { 2 } = 0 } ^ { N - 1 } \sum _ { \mathbf { m } \in \mathbb { Z } ^ { 2 } } \left[ \frac { u _ { \epsilon } ( \mathbf { y } ) - u _ { \epsilon } ( \mathbf { x } ) } { | \mathbf { x } - ( \mathbf { y } + \mathbf { m } ) | ^ { 2 + 2 \alpha } } \right] + 1 = 0 \, .

输出:
\frac{C_{\alpha}}{D_{\alpha}} h^{2} \sum_{j_{1}=0}^{N-1} \sum_{j_{2}=0}^{N-1} \sum_{\mathbf{m} \in \mathbb{Z}^{2}} \left[\frac{u_{\epsilon}(\mathbf{y})-u_{\epsilon}(\mathbf{x})}{| \mathbf{x}-(\mathbf{y}+\mathbf{m}) |^{2+2 \alpha}} \right]+1=0 \,.

状态: 已规范化 ✓
================================================================================

样本 434: UniMER-1M_0876805
------------------------------------------------------------
输入:
M = 1 8

输出:
M=1 8

状态: 已规范化 ✓
================================================================================

样本 435: UniMER-1M_0878802
------------------------------------------------------------
输入:
a _ { 1 } = - n _ { 1 } \, , \quad a _ { 2 } = - n _ { 2 } \, , \quad n _ { 1 } , \, n _ { 2 } = 0 , 1 , 2 , . . . \, .

输出:
a_{1}=-n_{1} \,,\quad a_{2}=-n_{2} \,,\quad n_{1},\,n_{2}=0,1,2,...\,.

状态: 已规范化 ✓
================================================================================

样本 436: UniMER-1M_0880763
------------------------------------------------------------
输入:
1 0 ^ { 6 } \le R a \le 1 0 ^ { 9 }

输出:
1 0^{6} \le R a \le 1 0^{9}

状态: 已规范化 ✓
================================================================================

样本 437: UniMER-1M_0882866
------------------------------------------------------------
输入:
\sigma _ { 0 } ^ { n } ( \lambda ) = s ( \lambda - { \frac { ( - ) ^ { n } } { \alpha _ { 0 } } } ) = { \frac { 1 } { 2 \cosh \Bigl ( \pi ( \lambda - { \frac { ( - ) ^ { n } } { \alpha _ { 0 } } } ) \Bigr ) } } \, ,

输出:
\sigma_{0}^{n}(\lambda)=s(\lambda-{\frac{(-)^{n}}{\alpha_{0}}})={\frac{1}{2 \mathrm{cosh} \Bigl(\pi(\lambda-{\frac{(-)^{n}}{\alpha_{0}}}) \Bigr)}} \,,

状态: 已规范化 ✓
================================================================================

样本 438: UniMER-1M_0884914
------------------------------------------------------------
输入:
{ \tau _ { i j } } = \overline { { { u _ { i } } { u _ { j } } } } - { { \bar { u } } _ { i } } { { \bar { u } } _ { j } }

输出:
{\tau_{i j}}=\overline{{{{u_{i}}{u_{j}}}}}-{{\bar{u}}_{i}}{{\bar{u}}_{j}}

状态: 已规范化 ✓
================================================================================

样本 439: UniMER-1M_0886947
------------------------------------------------------------
输入:
t

输出:
t

状态: 无变化
================================================================================

样本 440: UniMER-1M_0888953
------------------------------------------------------------
输入:
8 0 \times 8 0 \times 3 2

输出:
8 0 \times 8 0 \times 3 2

状态: 无变化
================================================================================

样本 441: UniMER-1M_0890987
------------------------------------------------------------
输入:
\ln \, P _ { \mathrm { { s } } } ^ { \mathrm { { s u b } } } = \ln \, P _ { \mathrm { { l } } } ^ { \mathrm { { s u b } } } - { \frac { \Delta _ { \mathrm { { f u s } } } H } { R } } \left( { \frac { 1 } { T _ { \mathrm { { s u b } } } } } - { \frac { 1 } { T _ { \mathrm { { f u s } } } } } \right)

输出:
\mathrm{ln} \,P_{\mathrm{{s}}}^{\mathrm{{sub}}}=\mathrm{ln} \,P_{\mathrm{{l}}}^{\mathrm{{sub}}}-{\frac{\Delta_{\mathrm{{fus}}} H}{R}} \left({\frac{1}{T_{\mathrm{{sub}}}}}-{\frac{1}{T_{\mathrm{{fus}}}}} \right)

状态: 已规范化 ✓
================================================================================

样本 442: UniMER-1M_0892988
------------------------------------------------------------
输入:
N _ { \mathrm { ~ r ~ a ~ d ~ } } \approx \frac { \mu _ { 0 } \pi ^ { 2 } r _ { 1 } ^ { 2 } n ^ { 2 } } { 2 l c } \Big ( \frac { r _ { 2 } \omega _ { 0 } } { c _ { 3 } } \Big ) ^ { 2 } ,

输出:
N_{\mathrm{~r~a~d~}} \approx \frac{\mu_{0} \pi^{2} r_{1}^{2} n^{2}}{2 l c} \Big(\frac{r_{2} \omega_{0}}{c_{3}} \Big)^{2},

状态: 已规范化 ✓
================================================================================

样本 443: UniMER-1M_0895031
------------------------------------------------------------
输入:
\begin{array} { r l r } { \bigtriangleup \phi } & { { } = 0 } & { ( x , z ) \in \Omega } \\ { \partial _ { z } \phi } & { { } = 0 } & { z = - H } \\ { \partial _ { x } \phi } & { { } = 0 } & { ( x , z ) \in \Gamma } \\ { \partial _ { z } \phi } & { { } = \frac { \omega ^ { 2 } } { g } \phi } & { z = 0 } \\ { \left( \frac { \partial } { \partial | x | } - \mathrm { i } k _ { 0 } \right) ( \phi - \phi ^ { \mathrm { I n } } ) } & { { } \to 0 } & { \mathrm { ~ a ~ s ~ \ } x \to \infty } \\ { \sqrt { x ^ { 2 } + ( z + d ) ^ { 2 } } \| \nabla \phi \| } & { { } \to 0 } & { \mathrm { ~ a ~ s ~ \ } \sqrt { x ^ { 2 } + ( z + d ) ^ { 2 } } \to 0 , } \end{array}

输出:
\begin{array}{rlr}{{\bigtriangleup \phi}} &{{=0}} &{{(x,z) \in \Omega}} \{{\partial_{z} \phi}} &{{=0}} &{{z=-H}} \{{\partial_{x} \phi}} &{{=0}} &{{(x,z) \in \Gamma}} \{{\partial_{z} \phi}} &{{=\frac{\omega^{2}}{g} \phi}} &{{z=0}} \{{\left(\frac{\partial}{\partial | x |}-\mathrm{i} k_{0} \right)(\phi-\phi^{\mathrm{In}})}} &{{ \to 0}} &{{\mathrm{~a~s~} x \to \infty}} \{{\sqrt{x^{2}+(z+d)^{2}} \| \nabla \phi \|}} &{{ \to 0}} &{{\mathrm{~a~s~} \sqrt{x^{2}+(z+d)^{2}} \to 0,}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 444: UniMER-1M_0897072
------------------------------------------------------------
输入:
A _ { \mathrm { J } }

输出:
A_{\mathrm{J}}

状态: 已规范化 ✓
================================================================================

样本 445: UniMER-1M_0899005
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathrm { R e a l } \left\{ r _ { k } ( y ) \right\} } & { = \frac { 2 } { D _ { k } ^ { 2 } } \left( C _ { 3 } ( y ) \cos \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \cosh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right. } \\ & { \quad \quad \quad \quad \quad \left. + C _ { 4 } ( y ) \sin \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \sinh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right) , } \\ { \mathrm { I m a g } \left\{ r _ { k } ( y ) \right\} } & { = \frac { 2 } { D _ { k } ^ { 2 } } \left( C _ { 4 } ( y ) \cos \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \cosh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right. } \\ & { \quad \quad \quad \quad \quad \left. - C _ { 4 } 3 y ) \sin \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \sinh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right) , } \\ { \mathrm { R e a l } \left\{ s _ { k } \right\} } & { = \frac { \ell ^ { 2 } r _ { \ell } } { D _ { k } ^ { 1 } } \left( C _ { k } ^ { 1 } \sin \left( \frac { h } { a } r _ { \ell } \sin \left( \frac { \theta _ { \ell } } { 2 } \right) \right) + C _ { k } ^ { 2 } \sinh \left( \frac { h } { a } r _ { \ell } \cos \left( \frac { \theta _ { \ell } } { 2 } \right) \right) \right) , } \\ { \mathrm { I m a g } \left\{ s _ { k } \right\} } & { = \frac { \ell ^ { 2 } r _ { \ell } } { D _ { k } ^ { 1 } } \left( C _ { k } ^ { 2 } \sin \left( \frac { h } { a } r _ { \ell } \sin \left( \frac { \theta _ { \ell } } { 2 } \right) \right) - C _ { k } ^ { 1 } \sinh \left( \frac { h } { a } r _ { \ell } \cos \left( \frac { \theta _ { \ell } } { 2 } \right) \right) \right) , } \end{array}

输出:
\begin{array}{rl}{{\mathrm{Real} \left\{r_{k}(y) \right\}}} &{{=\frac{2}{D_{k}^{2}} \left(C_{3}(y) \mathrm{cos} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{cosh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right.}} \&{{\quad \quad \quad \quad \quad \left.+C_{4}(y) \mathrm{sin} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{sinh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right),}} \{{\mathrm{Imag} \left\{r_{k}(y) \right\}}} &{{=\frac{2}{D_{k}^{2}} \left(C_{4}(y) \mathrm{cos} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{cosh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right.}} \&{{\quad \quad \quad \quad \quad \left.-C_{4} 3 y) \mathrm{sin} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{sinh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right),}} \{{\mathrm{Real} \left\{s_{k} \right\}}} &{{=\frac{\ell^{2} r_{\ell}}{D_{k}^{1}} \left(C_{k}^{1} \mathrm{sin} \left(\frac{h}{a} r_{\ell} \mathrm{sin} \left(\frac{\theta_{\ell}}{2} \right) \right)+C_{k}^{2} \mathrm{sinh} \left(\frac{h}{a} r_{\ell} \mathrm{cos} \left(\frac{\theta_{\ell}}{2} \right) \right) \right),}} \{{\mathrm{Imag} \left\{s_{k} \right\}}} &{{=\frac{\ell^{2} r_{\ell}}{D_{k}^{1}} \left(C_{k}^{2} \mathrm{sin} \left(\frac{h}{a} r_{\ell} \mathrm{sin} \left(\frac{\theta_{\ell}}{2} \right) \right)-C_{k}^{1} \mathrm{sinh} \left(\frac{h}{a} r_{\ell} \mathrm{cos} \left(\frac{\theta_{\ell}}{2} \right) \right) \right),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 446: UniMER-1M_0901063
------------------------------------------------------------
输入:
\langle \mathbf { R } \cdot \mathbf { R } \rangle = 3 N b ^ { 2 }

输出:
\langle \mathbf{R} \cdot \mathbf{R} \rangle=3 N b^{2}

状态: 已规范化 ✓
================================================================================

样本 447: UniMER-1M_0903157
------------------------------------------------------------
输入:
T <

输出:
T<

状态: 已规范化 ✓
================================================================================

样本 448: UniMER-1M_0905173
------------------------------------------------------------
输入:
k _ { 2 } \! = \! \frac { k _ { \mathrm { o } } } { \beta _ { v } } ( 1 - \frac { k _ { \mathrm { o } } } { k _ { 1 } } )

输出:
k_{2} \!=\! \frac{k_{\mathrm{o}}}{\beta_{v}}(1-\frac{k_{\mathrm{o}}}{k_{1}})

状态: 已规范化 ✓
================================================================================

样本 449: UniMER-1M_0907196
------------------------------------------------------------
输入:
\| \cdot \|

输出:
\| \cdot \|

状态: 无变化
================================================================================

样本 450: UniMER-1M_0909185
------------------------------------------------------------
输入:
\begin{array} { r l } { \frac { 2 + \sqrt { b c } } { \sqrt { b c } + a } } & { = \frac { \sqrt { [ a ( b + c + b c ) + b c ] [ 4 a ( b + c + b c ) + ( b + c + b c - a ) ^ { 2 } ] } + \sqrt { b c } ( a + b + c + b c ) } { \left( \sqrt { b c } + a \right) ( a + b + c + b c ) } } \\ & { \ge \frac { 2 a ( b + c + b c ) + \sqrt { b c } ( b + c + b c - a ) + \sqrt { b c } ( a + b + c + b c ) } { \left( \sqrt { b c } + a \right) ( a + b + c + b c ) } } \\ & { = \frac { 2 ( b + c + b c ) } { a + b + c + b c } . } \end{array}

输出:
\begin{array}{rl}{{\frac{2+\sqrt{b c}}{\sqrt{b c}+a}}} &{{=\frac{\sqrt{[a(b+c+b c)+b c][4 a(b+c+b c)+(b+c+b c-a)^{2}]}+\sqrt{b c}(a+b+c+b c)}{\left(\sqrt{b c}+a \right)(a+b+c+b c)}}} \&{{\ge \frac{2 a(b+c+b c)+\sqrt{b c}(b+c+b c-a)+\sqrt{b c}(a+b+c+b c)}{\left(\sqrt{b c}+a \right)(a+b+c+b c)}}} \&{{=\frac{2(b+c+b c)}{a+b+c+b c}.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 451: UniMER-1M_0911203
------------------------------------------------------------
输入:
W _ { 1 - 2 } = \int P d V ,

输出:
W_{1-2}=\int P d V,

状态: 已规范化 ✓
================================================================================

样本 452: UniMER-1M_0913260
------------------------------------------------------------
输入:
E \neq V

输出:
E \neq V

状态: 无变化
================================================================================

样本 453: UniMER-1M_0915274
------------------------------------------------------------
输入:
\langle \cdot \rangle _ { \mathrm { ~ d ~ a ~ t ~ a ~ } }

输出:
\langle \cdot \rangle_{\mathrm{~d~a~t~a~}}

状态: 已规范化 ✓
================================================================================

样本 454: UniMER-1M_0917314
------------------------------------------------------------
输入:
\begin{array} { r l } { \hat { \rho } } & { { } = \otimes _ { \lambda } \, \hat { \rho } _ { \lambda } \, , } \\ { \hat { \rho } _ { \lambda } } & { { } = \frac { 1 } { \cosh \xi _ { \lambda } } \sum _ { n = 0 } ^ { \infty } \left( \operatorname { t a n h } \xi _ { \lambda } \right) ^ { 2 n } \left( \frac { ( 2 n ) ! } { 2 ^ { n } n ! } \right) ^ { 2 } \sum _ { k = 0 } ^ { n } \frac { \eta _ { \lambda } ^ { 2 ( 2 n - k ) } ( 1 - \eta _ { \lambda } ^ { 2 } ) ^ { k } } { k ! ( 2 n - k ) ! } | 2 n - k \rangle _ { \lambda } \langle 2 n - k | _ { \lambda } \, . } \end{array}

输出:
\begin{array}{rl}{{\hat{\rho}}} &{{=\otimes_{\lambda} \,\hat{\rho}_{\lambda} \,,}} \{{\hat{\rho}_{\lambda}}} &{{=\frac{1}{\mathrm{cosh} \xi_{\lambda}} \sum_{n=0}^{\infty} \left(\mathrm{tanh} \xi_{\lambda} \right)^{2 n} \left(\frac{(2 n) !}{2^{n} n !} \right)^{2} \sum_{k=0}^{n} \frac{\eta_{\lambda}^{2(2 n-k)}(1-\eta_{\lambda}^{2})^{k}}{k !(2 n-k) !} | 2 n-k \rangle_{\lambda} \langle 2 n-k |_{\lambda} \,.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 455: UniMER-1M_0919347
------------------------------------------------------------
输入:
1 6

输出:
1 6

状态: 无变化
================================================================================

样本 456: UniMER-1M_0921371
------------------------------------------------------------
输入:
\Sigma ^ { \mathrm { i n e l } } ( \varepsilon ) = \Sigma ^ { \mathrm { t o t a l } } ( \varepsilon ) - \Sigma ^ { \mathrm { t r } } ( \varepsilon )

输出:
\Sigma^{\mathrm{inel}}(\varepsilon)=\Sigma^{\mathrm{total}}(\varepsilon)-\Sigma^{\mathrm{tr}}(\varepsilon)

状态: 已规范化 ✓
================================================================================

样本 457: UniMER-1M_0923403
------------------------------------------------------------
输入:
0 . 6

输出:
0.6

状态: 已规范化 ✓
================================================================================

样本 458: UniMER-1M_0925448
------------------------------------------------------------
输入:
N _ { A }

输出:
N_{A}

状态: 已规范化 ✓
================================================================================

样本 459: UniMER-1M_0927463
------------------------------------------------------------
输入:
\mathcal { J _ { \mathrm { L T 1 } } } = \left( \begin{array} { l l l } { * * * } & { 0 } & { 0 } \\ { 0 } & { * * * } & { 0 } \\ { 0 } & { 0 } & { * * * } \end{array} \right) , \quad \mathcal { J _ { \mathrm { L T 2 } } } = \left( \begin{array} { l l l } { * * * } & { - 1 7 } & { 2 4 } \\ { - 1 6 } & { * * * } & { - 1 4 } \\ { 2 4 } & { - 1 4 } & { * * * } \end{array} \right) ,

输出:
\mathcal{J_{\mathrm{LT1}}}=\left(\begin{array}{lll}{{***}} &{{0}} &{{0}} \{{0}} &{{***}} &{{0}} \{{0}} &{{0}} &{{***}} \end{array} \right),\quad \mathcal{J_{\mathrm{LT2}}}=\left(\begin{array}{lll}{{***}} &{{-1 7}} &{{2 4}} \{{-1 6}} &{{***}} &{{-1 4}} \{{2 4}} &{{-1 4}} &{{***}} \end{array} \right),

状态: 已规范化 ✓
================================================================================

样本 460: UniMER-1M_0929454
------------------------------------------------------------
输入:
d s ^ { 2 } = e ^ { A } \left( - d t ^ { 2 } e ^ { 2 f } + d z ^ { 2 } \right) + e ^ { - A } \left( d r ^ { 2 } e ^ { - 2 f } + r ^ { 2 } d \Omega _ { 3 } ^ { 2 } \right) ,

输出:
d s^{2}=e^{A} \left(-d t^{2} e^{2 f}+d z^{2} \right)+e^{-A} \left(d r^{2} e^{-2 f}+r^{2} d \Omega_{3}^{2} \right),

状态: 已规范化 ✓
================================================================================

样本 461: UniMER-1M_0931535
------------------------------------------------------------
输入:
^ { \mathrm { ~ t ~ h ~ } }

输出:
^{\mathrm{~t~h~}}

状态: 已规范化 ✓
================================================================================

样本 462: UniMER-1M_0933605
------------------------------------------------------------
输入:
2 \mu _ { \mathrm { B } }

输出:
2 \mu_{\mathrm{B}}

状态: 已规范化 ✓
================================================================================

样本 463: UniMER-1M_0935567
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathcal { U } _ { n , m } ^ { \epsilon } ( x , y , z ) \approx } & { \mathrm { i } ^ { m } \bigg ( \mathcal { U } _ { n , m } - \mathrm { i } \frac { \epsilon } { 2 } \Big ( A _ { n } \mathcal { U } _ { n + 2 , m } + B _ { n } \mathcal { U } _ { n - 2 , m } } \\ & { - A _ { m } \mathcal { U } _ { n , m + 2 } - B _ { m } \mathcal { U } _ { n , m - 2 } \Big ) \bigg ) } \end{array}

输出:
\begin{array}{rl}{{\mathcal{U}_{n,m}^{\epsilon}(x,y,z) \approx}} &{{\mathrm{i}^{m} \bigg(\mathcal{U}_{n,m}-\mathrm{i} \frac{\epsilon}{2} \Big(A_{n} \mathcal{U}_{n+2,m}+B_{n} \mathcal{U}_{n-2,m}}} \&{{-A_{m} \mathcal{U}_{n,m+2}-B_{m} \mathcal{U}_{n,m-2} \Big) \bigg)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 464: UniMER-1M_0937590
------------------------------------------------------------
输入:
\begin{array} { r l } { E ^ { ( 5 , 0 ) } } & { { } = \langle \chi \lvert { \cal { E } } ^ { ( 5 , 0 ) } ( R ) \rvert \chi \rangle } \end{array}

输出:
\begin{array}{rl}{{E^{(5,0)}}} &{{=\langle \chi \lvert{\cal{E}}^{(5,0)}(R) \rvert \chi \rangle}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 465: UniMER-1M_0939634
------------------------------------------------------------
输入:
\pi / 2

输出:
\pi/2

状态: 已规范化 ✓
================================================================================

样本 466: UniMER-1M_0941640
------------------------------------------------------------
输入:
J

输出:
J

状态: 无变化
================================================================================

样本 467: UniMER-1M_0943654
------------------------------------------------------------
输入:
\overline { { u _ { i } ^ { \prime } u _ { j } ^ { \prime } } }

输出:
\overline{{{u_{i}^{\prime} u_{j}^{\prime}}}}

状态: 已规范化 ✓
================================================================================

样本 468: UniMER-1M_0945686
------------------------------------------------------------
输入:
\Delta = - b _ { 2 } ^ { 2 } b _ { 8 } + 9 b _ { 2 } b _ { 4 } b _ { 6 } - 8 b _ { 4 } ^ { 3 } - 2 7 b _ { 6 } ^ { 2 } .

输出:
\Delta=-b_{2}^{2} b_{8}+9 b_{2} b_{4} b_{6}-8 b_{4}^{3}-2 7 b_{6}^{2}.

状态: 已规范化 ✓
================================================================================

样本 469: UniMER-1M_0947559
------------------------------------------------------------
输入:
{ \bf r } _ { v } = ( X , 0 )

输出:
{\bf r}_{v}=(X,0)

状态: 已规范化 ✓
================================================================================

样本 470: UniMER-1M_0949698
------------------------------------------------------------
输入:
{ \sqrt [ [object Object] ] { | a _ { n } | } } \leq k < 1 ,

输出:
{\sqrt[[o b j e c t O b j e c t]]{| a_{n} |}} \leq k<1,

状态: 已规范化 ✓
================================================================================

样本 471: UniMER-1M_0951770
------------------------------------------------------------
输入:
\sum _ { T _ { L } } s t r ( T _ { L } ^ { a _ { 1 } } \ldots T _ { L } ^ { a _ { N } } ) - \sum _ { T _ { R } } s t r ( T _ { R } ^ { a _ { 1 } } \ldots T _ { R } ^ { a _ { N } } ) ,

输出:
\sum_{T_{L}} s t r(T_{L}^{a_{1}} . . . T_{L}^{a_{N}})-\sum_{T_{R}} s t r(T_{R}^{a_{1}} . . . T_{R}^{a_{N}}),

状态: 已规范化 ✓
================================================================================

样本 472: UniMER-1M_0953738
------------------------------------------------------------
输入:
\begin{array} { r l r } & { } & { P _ { C _ { k , m } \rightarrow C _ { k , m - 1 } } ( k , m ) = \sum _ { k _ { i } } P _ { C - D } ( k _ { i } ) \left( \frac { 1 } { N _ { D - C } ( k _ { i } ) } \right. } \\ & { } & { \left. \times \phi ( \pi _ { C - D } ( k _ { i } ) , \pi _ { C } ( k , m ) ) + \frac { N _ { D - C } ( k _ { i } ) - 1 } { N _ { D - C } ( k _ { i } ) } \right. } \\ & { } & { \left. \times \sum _ { k _ { j } } P _ { D - C } ( k _ { j } ) \phi ( \pi _ { C - D } ( k _ { i } ) , \pi _ { D - C } ( k _ { j } ) ) \right) . } \end{array}

输出:
\begin{array}{rlr} & &{{P_{C_{k,m} \rightarrow C_{k,m-1}}(k,m)=\sum_{k_{i}} P_{C-D}(k_{i}) \left(\frac{1}{N_{D-C}(k_{i})} \right.}} \& &{{\left.\times \phi(\pi_{C-D}(k_{i}),\pi_{C}(k,m))+\frac{N_{D-C}(k_{i})-1}{N_{D-C}(k_{i})} \right.}} \& &{{\left.\times \sum_{k_{j}} P_{D-C}(k_{j}) \phi(\pi_{C-D}(k_{i}),\pi_{D-C}(k_{j})) \right).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 473: UniMER-1M_0955781
------------------------------------------------------------
输入:
R _ { 1 2 } d K _ { 1 } R _ { 2 1 } K _ { 2 } + R _ { 1 2 } K _ { 1 } R _ { 2 1 } d K _ { 2 } = d K _ { 2 } R _ { 1 2 } K _ { 1 } R _ { 2 1 } + K _ { 2 } R _ { 1 2 } d K _ { 1 } R _ { 2 1 } \quad .

输出:
R_{1 2} d K_{1} R_{2 1} K_{2}+R_{1 2} K_{1} R_{2 1} d K_{2}=d K_{2} R_{1 2} K_{1} R_{2 1}+K_{2} R_{1 2} d K_{1} R_{2 1} \quad.

状态: 已规范化 ✓
================================================================================

样本 474: UniMER-1M_0957810
------------------------------------------------------------
输入:
\ell ( t ) \sim t ^ { \alpha }

输出:
\ell(t) \sim t^{\alpha}

状态: 已规范化 ✓
================================================================================

样本 475: UniMER-1M_0959819
------------------------------------------------------------
输入:
\Omega \left( \mathbf { R } \right) = c P \left( \mathbf { R } \right)

输出:
\Omega \left(\mathbf{R} \right)=c P \left(\mathbf{R} \right)

状态: 已规范化 ✓
================================================================================

样本 476: UniMER-1M_0961863
------------------------------------------------------------
输入:
{ \mathrm { B S _ { 1 } } }

输出:
{\mathrm{BS_{1}}}

状态: 已规范化 ✓
================================================================================

样本 477: UniMER-1M_0963865
------------------------------------------------------------
输入:
G _ { B } ^ { C } ( \lambda ) \equiv \, i \, \langle C | [ Q , B ( 0 ) ] _ { \mathrm { E T } } | C \rangle _ { \lambda } \; ,

输出:
G_{B}^{C}(\lambda) \equiv \,i \,\langle C |[Q,B(0)]_{\mathrm{ET}} | C \rangle_{\lambda} \;,

状态: 已规范化 ✓
================================================================================

样本 478: UniMER-1M_0965864
------------------------------------------------------------
输入:
{ \mathcal P } ,

输出:
{\mathcal{P}},

状态: 已规范化 ✓
================================================================================

样本 479: UniMER-1M_0967903
------------------------------------------------------------
输入:
\sim 1

输出:
\sim 1

状态: 无变化
================================================================================

样本 480: UniMER-1M_0969879
------------------------------------------------------------
输入:
\mathrm { 2 s ^ { 2 } \, 2 p ^ { 2 } ( ^ { 3 } P ) \, 3 s ~ ^ { 2 } P _ { 1 / 2 } }

输出:
\mathrm{2s^{2} \,2 p^{2}(^{3} P) \,3 s ~^{2} P_{1/2}}

状态: 已规范化 ✓
================================================================================

样本 481: UniMER-1M_0971956
------------------------------------------------------------
输入:
\alpha _ { T }

输出:
\alpha_{T}

状态: 已规范化 ✓
================================================================================

样本 482: UniMER-1M_0973960
------------------------------------------------------------
输入:
^ { 8 5 }

输出:
^{8 5}

状态: 已规范化 ✓
================================================================================

样本 483: UniMER-1M_0975938
------------------------------------------------------------
输入:
{ ( 3 ) }

输出:
{(3)}

状态: 已规范化 ✓
================================================================================

样本 484: UniMER-1M_0977945
------------------------------------------------------------
输入:
I ( \epsilon ) \sim e ^ { - b / \epsilon } \epsilon ^ { m } \sum _ { n = 0 } ^ { \infty } a _ { n } \epsilon ^ { n } \mathrm { ~ a ~ s ~ } \epsilon \to 0 ,

输出:
I(\epsilon) \sim e^{-b/\epsilon} \epsilon^{m} \sum_{n=0}^{\infty} a_{n} \epsilon^{n} \mathrm{~a~s~} \epsilon \to 0,

状态: 已规范化 ✓
================================================================================

样本 485: UniMER-1M_0980043
------------------------------------------------------------
输入:
\alpha

输出:
\alpha

状态: 无变化
================================================================================

样本 486: UniMER-1M_0982039
------------------------------------------------------------
输入:
x , y

输出:
x,y

状态: 已规范化 ✓
================================================================================

样本 487: UniMER-1M_0984082
------------------------------------------------------------
输入:
\dot { \varphi } _ { a } ( k ) \mp \omega \epsilon _ { a b } \varphi _ { b } ( k ) = 0

输出:
\dot{\varphi}_{a}(k) \mp \omega \epsilon_{a b} \varphi_{b}(k)=0

状态: 已规范化 ✓
================================================================================

样本 488: UniMER-1M_0986109
------------------------------------------------------------
输入:
\langle . \rangle

输出:
\langle.\rangle

状态: 已规范化 ✓
================================================================================

样本 489: UniMER-1M_0988128
------------------------------------------------------------
输入:
\left( \begin{array} { l l l l } { P _ { 1 1 } } & { P _ { 1 2 } } & { P _ { 1 3 } } & { P _ { 1 p } } \\ { P _ { 2 1 } } & { P _ { 2 2 } } & { P _ { 2 3 } } & { P _ { 2 p } } \\ { P _ { 3 1 } } & { P _ { 3 1 } } & { P _ { 3 2 } } & { P _ { 3 3 } } \\ { P _ { p 1 } } & { P _ { p 2 } } & { P _ { p 3 } } & { P _ { p p } } \end{array} \right) \left( \begin{array} { l } { \eta _ { 1 } } \\ { \eta _ { 2 } } \\ { \eta _ { 3 } } \\ { \eta _ { p } } \end{array} \right) = \left( \begin{array} { l } { 0 } \\ { 0 } \\ { 0 } \\ { 0 } \end{array} \right)

输出:
\left(\begin{array}{llll}{{P_{1 1}}} &{{P_{1 2}}} &{{P_{1 3}}} &{{P_{1 p}}} \{{P_{2 1}}} &{{P_{2 2}}} &{{P_{2 3}}} &{{P_{2 p}}} \{{P_{3 1}}} &{{P_{3 1}}} &{{P_{3 2}}} &{{P_{3 3}}} \{{P_{p 1}}} &{{P_{p 2}}} &{{P_{p 3}}} &{{P_{p p}}} \end{array} \right) \left(\begin{array}{l}{{\eta_{1}}} \{{\eta_{2}}} \{{\eta_{3}}} \{{\eta_{p}}} \end{array} \right)=\left(\begin{array}{l}{{0}} \{{0}} \{{0}} \{{0}} \end{array} \right)

状态: 已规范化 ✓
================================================================================

样本 490: UniMER-1M_0990145
------------------------------------------------------------
输入:
\ell \leq 5

输出:
\ell \leq 5

状态: 无变化
================================================================================

样本 491: UniMER-1M_0992206
------------------------------------------------------------
输入:
\begin{array} { r l } { \langle \Delta t \rangle _ { l o n g } } & { \approx S _ { 1 } ( t _ { o n } , m = 0 ) S _ { 2 } ( t _ { e n d } , m = 0 ) \frac { 1 } { \alpha } \ln { \bigg ( 1 + \frac { m ( e ^ { \alpha \tau } - r ) } { f _ { e n d } } \bigg ) } \bigg ( 1 - \frac { \mu ( t _ { e n d } , m = 0 ) } { \beta } \ln { \bigg ( 1 + \frac { \beta } { \mu ( t _ { e n d } , m = 0 ) } \bigg ) } \bigg ) . } \end{array}

输出:
\begin{array}{rl}{{\langle \Delta t \rangle_{l o n g}}} &{{\approx S_{1}(t_{o n},m=0) S_{2}(t_{e n d},m=0) \frac{1}{\alpha} \mathrm{ln}{\bigg(1+\frac{m(e^{\alpha \tau}-r)}{f_{e n d}} \bigg)} \bigg(1-\frac{\mu(t_{e n d},m=0)}{\beta} \mathrm{ln}{\bigg(1+\frac{\beta}{\mu(t_{e n d},m=0)} \bigg)} \bigg).}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 492: UniMER-1M_0994247
------------------------------------------------------------
输入:
\theta

输出:
\theta

状态: 无变化
================================================================================

样本 493: UniMER-1M_0996273
------------------------------------------------------------
输入:
t _ { 1 } , \ldots , t _ { n - 1 }

输出:
t_{1},. . .,t_{n-1}

状态: 已规范化 ✓
================================================================================

样本 494: UniMER-1M_0998261
------------------------------------------------------------
输入:
\begin{array} { r l r } { \delta \left< \hat { \mathcal X } \right> } & { { } = } & { \Omega _ { a } \frac { d } { d \Omega _ { a } } \left. \left< \hat { \mathcal X } \right> \right| _ { \Omega _ { a } = 0 } } \end{array}

输出:
\begin{array}{rlr}{{\delta \left<\hat{\mathcal{X}} \right>}} &{{=}} &{{\Omega_{a} \frac{d}{d \Omega_{a}} \left.\left<\hat{\mathcal{X}} \right>\right|_{\Omega_{a}=0}}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 495: UniMER-1M_1000325
------------------------------------------------------------
输入:
A ^ { 4 } = { \frac { 1 } { \lambda ^ { 2 } { R _ { A } } ^ { 2 } } } { \frac { ( R _ { A } - D ) ( D - R _ { A } + R _ { B } ) } { D ( D + R _ { B } ) } } \, ,

输出:
A^{4}={\frac{1}{\lambda^{2}{R_{A}}^{2}}}{\frac{(R_{A}-D)(D-R_{A}+R_{B})}{D(D+R_{B})}} \,,

状态: 已规范化 ✓
================================================================================

样本 496: UniMER-1M_1002318
------------------------------------------------------------
输入:
a _ { 2 } ( x ) = \frac { 1 - \alpha ^ { 2 } } { r ^ { 4 } } \left[ ( \alpha ^ { 2 } - 1 ) \left( \frac 1 { 9 0 } + 2 \left( \frac 1 6 - \xi \right) ^ { 2 } \right) + \frac 2 3 \left( \frac 1 5 - \xi \right) \alpha ^ { 2 } \right] + \frac { q ^ { 2 } } { 6 r ^ { 4 } } \ .

输出:
a_{2}(x)=\frac{1-\alpha^{2}}{r^{4}} \left[(\alpha^{2}-1) \left(\frac{1}{9 0}+2 \left(\frac{1}{6}-\xi \right)^{2} \right)+\frac{2}{3} \left(\frac{1}{5}-\xi \right) \alpha^{2} \right]+\frac{q^{2}}{6 r^{4}}.

状态: 已规范化 ✓
================================================================================

样本 497: UniMER-1M_1004392
------------------------------------------------------------
输入:
4 . 5

输出:
4.5

状态: 已规范化 ✓
================================================================================

样本 498: UniMER-1M_1006403
------------------------------------------------------------
输入:
\xi ( t )

输出:
\xi(t)

状态: 已规范化 ✓
================================================================================

样本 499: UniMER-1M_1008417
------------------------------------------------------------
输入:
{ \frac { { \overline { { I A } } } \cdot { \overline { { I A } } } } { { \overline { { C A } } } \cdot { \overline { { A B } } } } } + { \frac { { \overline { { I B } } } \cdot { \overline { { I B } } } } { { \overline { { A B } } } \cdot { \overline { { B C } } } } } + { \frac { { \overline { { I C } } } \cdot { \overline { { I C } } } } { { \overline { { B C } } } \cdot { \overline { { C A } } } } } = 1

输出:
{\frac{{\overline{{{I A}}}} \cdot{\overline{{{I A}}}}}{{\overline{{{C A}}}} \cdot{\overline{{{A B}}}}}}+{\frac{{\overline{{{I B}}}} \cdot{\overline{{{I B}}}}}{{\overline{{{A B}}}} \cdot{\overline{{{B C}}}}}}+{\frac{{\overline{{{I C}}}} \cdot{\overline{{{I C}}}}}{{\overline{{{B C}}}} \cdot{\overline{{{C A}}}}}}=1

状态: 已规范化 ✓
================================================================================

样本 500: UniMER-1M_1010450
------------------------------------------------------------
输入:
- 5 7 . 2

输出:
-5 7.2

状态: 已规范化 ✓
================================================================================

样本 501: UniMER-1M_1012463
------------------------------------------------------------
输入:
S

输出:
S

状态: 无变化
================================================================================

样本 502: UniMER-1M_1014498
------------------------------------------------------------
输入:
V ^ { ( 4 ) } ( q _ { \mathrm { ~ r ~ e ~ f ~ } } ) _ { i j k l }

输出:
V^{(4)}(q_{\mathrm{~r~e~f~}})_{i j k l}

状态: 已规范化 ✓
================================================================================

样本 503: UniMER-1M_1016511
------------------------------------------------------------
输入:
\nabla _ { s }

输出:
\nabla_{s}

状态: 已规范化 ✓
================================================================================

样本 504: UniMER-1M_1018545
------------------------------------------------------------
输入:
\beta _ { r m } = R e ( \sqrt { n _ { m } ^ { 2 } \omega ^ { 2 } / c ^ { 2 } - h ^ { 2 } } )

输出:
\beta_{r m}=R e(\sqrt{n_{m}^{2} \omega^{2}/c^{2}-h^{2}})

状态: 已规范化 ✓
================================================================================

样本 505: UniMER-1M_1020607
------------------------------------------------------------
输入:
^ +

输出:
^+

状态: 已规范化 ✓
================================================================================

样本 506: UniMER-1M_1022593
------------------------------------------------------------
输入:
J = A ( x ) + i \theta ^ { \alpha } \lambda _ { \alpha } ( x ) + i \theta ^ { \alpha } \theta _ { \alpha } B ( x ) \; ,

输出:
J=A(x)+i \theta^{\alpha} \lambda_{\alpha}(x)+i \theta^{\alpha} \theta_{\alpha} B(x) \;,

状态: 已规范化 ✓
================================================================================

样本 507: UniMER-1M_1024618
------------------------------------------------------------
输入:
d s ^ { 2 } | _ { M 2 } = - 4 ( | y | ^ { 2 } + y _ { 3 } ^ { 2 } ) ( d x _ { + } ) ^ { 2 } - { \frac { 2 } { i } } ( W ^ { \prime } d ( e ^ { - 2 i x _ { + } } y ) - \overline { { { W ^ { \prime } } } } d ( e ^ { 2 i x _ { + } } \overline { { { y } } } ) ) d x _ { + } + d y d \overline { { { y } } } + ( d y _ { 3 } ) ^ { 2 }

输出:
d s^{2} |_{M 2}=-4(| y |^{2}+y_{3}^{2})(d x_{+})^{2}-{\frac{2}{i}}(W^{\prime} d(e^{-2 i x_{+}} y)-\overline{{{{W^{\prime}}}}} d(e^{2 i x_{+}} \overline{{{{y}}}})) d x_{+}+d y d \overline{{{{y}}}}+(d y_{3})^{2}

状态: 已规范化 ✓
================================================================================

样本 508: UniMER-1M_1026638
------------------------------------------------------------
输入:
w _ { i } = \frac { \partial } { \partial z ^ { i } } \ .

输出:
w_{i}=\frac{\partial}{\partial z^{i}}.

状态: 已规范化 ✓
================================================================================

样本 509: UniMER-1M_1028673
------------------------------------------------------------
输入:
\alpha ^ { \prime } m ^ { 2 } \simeq 4 n - 7 \, H ^ { 2 } \alpha ^ { \prime } n ^ { 2 } + \cdots \; .

输出:
\alpha^{\prime} m^{2} \simeq 4 n-7 \,H^{2} \alpha^{\prime} n^{2}+. . . \;.

状态: 已规范化 ✓
================================================================================

样本 510: UniMER-1M_1030649
------------------------------------------------------------
输入:
\Sigma _ { \mathrm { m i n } } ^ { ( 2 ) } = \frac { k _ { \mathrm { B } } } { 2 } \ln \frac { T _ { \mathrm { R } } + T _ { f } } { T _ { \mathrm { R } } + T _ { i } } - \frac { k _ { \mathrm { B } } \ln \frac { T _ { \mathrm { R } } + T _ { f } } { T _ { \mathrm { R } } + T _ { i } } } { 2 \left( 1 + \frac { \tau } { 2 \Delta t } \ln \frac { T _ { \mathrm { R } } + T _ { f } } { T _ { \mathrm { R } } + T _ { i } } \right) } .

输出:
\Sigma_{\mathrm{min}}^{(2)}=\frac{k_{\mathrm{B}}}{2} \mathrm{ln} \frac{T_{\mathrm{R}}+T_{f}}{T_{\mathrm{R}}+T_{i}}-\frac{k_{\mathrm{B}} \mathrm{ln} \frac{T_{\mathrm{R}}+T_{f}}{T_{\mathrm{R}}+T_{i}}}{2 \left(1+\frac{\tau}{2 \Delta t} \mathrm{ln} \frac{T_{\mathrm{R}}+T_{f}}{T_{\mathrm{R}}+T_{i}} \right)}.

状态: 已规范化 ✓
================================================================================

样本 511: UniMER-1M_1032679
------------------------------------------------------------
输入:
\ensuremath { f _ { \mathrm { G W } } } = 0 . 2 1

输出:
\ensuremath{f_{\mathrm{GW}}}=0.2 1

状态: 已规范化 ✓
================================================================================

样本 512: UniMER-1M_1034699
------------------------------------------------------------
输入:
S ^ { 2 } P \mapsto T M / P

输出:
S^{2} P \mapsto T M/P

状态: 已规范化 ✓
================================================================================

样本 513: UniMER-1M_1036628
------------------------------------------------------------
输入:
\mathcal { M } _ { i } = 1 + \frac { \pi } { 4 } \chi _ { \rho } \chi _ { c } \chi _ { h } K \sin ^ { 2 } \theta _ { i } , ~ \mathcal { I } = 1 + \frac { 3 \pi } { 1 6 } \chi _ { \rho } \chi _ { h } K , ~ \widehat { \mathcal { M } } _ { i } = \frac { 3 \pi } { 8 } \frac { \chi _ { \rho } \chi _ { h } } { \chi _ { c } } K \sin \theta _ { i } , ~ \widehat { \mathcal { I } } _ { i } = \frac { \pi } { 8 } \chi _ { \rho } \chi _ { c } ^ { 2 } \chi _ { h } K \sin \theta _ { i } ,

输出:
\mathcal{M}_{i}=1+\frac{\pi}{4} \chi_{\rho} \chi_{c} \chi_{h} K \mathrm{sin}^{2} \theta_{i},~ \mathcal{I}=1+\frac{3 \pi}{1 6} \chi_{\rho} \chi_{h} K,~ \widehat{\mathcal{M}}_{i}=\frac{3 \pi}{8} \frac{\chi_{\rho} \chi_{h}}{\chi_{c}} K \mathrm{sin} \theta_{i},~ \widehat{\mathcal{I}}_{i}=\frac{\pi}{8} \chi_{\rho} \chi_{c}^{2} \chi_{h} K \mathrm{sin} \theta_{i},

状态: 已规范化 ✓
================================================================================

样本 514: UniMER-1M_1038730
------------------------------------------------------------
输入:
\begin{array} { r l } { X } & { = \frac { 1 } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } } \, , } \\ { Y _ { 0 } } & { = \frac { 2 p _ { 0 } \bar { d } _ { 0 } } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } \sum _ { i } \vert M _ { 0 , i } ( 0 ) \vert ^ { 2 } } } \\ { Y _ { 1 } } & { = \frac { 2 p _ { 1 } \bar { d } _ { 0 } } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } \sum _ { i } \vert M _ { 0 , i } ( 0 ) \vert ^ { 2 } } } \\ { Z _ { i } } & { = \frac { 4 d _ { 0 } p _ { i } \bar { \hat { e } } ^ { * } \hat { e } _ { i } } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } \sum _ { i } \vert M _ { 0 , i } ( 0 ) \vert ^ { 2 } } \, . } \end{array}

输出:
\begin{array}{rl}{{X}} &{{=\frac{1}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2}} \,,}} \{{Y_{0}}} &{{=\frac{2 p_{0} \bar{d}_{0}}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2} \sum_{i} \vert M_{0,i}(0) \vert^{2}}}} \{{Y_{1}}} &{{=\frac{2 p_{1} \bar{d}_{0}}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2} \sum_{i} \vert M_{0,i}(0) \vert^{2}}}} \{{Z_{i}}} &{{=\frac{4 d_{0} p_{i} \bar{\hat{e}}^{*} \hat{e}_{i}}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2} \sum_{i} \vert M_{0,i}(0) \vert^{2}} \,.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 515: UniMER-1M_1040794
------------------------------------------------------------
输入:
k = 2 . 3

输出:
k=2.3

状态: 已规范化 ✓
================================================================================

样本 516: UniMER-1M_1042823
------------------------------------------------------------
输入:
\Delta x

输出:
\Delta x

状态: 无变化
================================================================================

样本 517: UniMER-1M_1044767
------------------------------------------------------------
输入:
H _ { e B H }

输出:
H_{e B H}

状态: 已规范化 ✓
================================================================================

样本 518: UniMER-1M_1046869
------------------------------------------------------------
输入:
6

输出:
6

状态: 无变化
================================================================================

样本 519: UniMER-1M_1048865
------------------------------------------------------------
输入:
G ^ { ( n ) }

输出:
G^{(n)}

状态: 已规范化 ✓
================================================================================

样本 520: UniMER-1M_1050921
------------------------------------------------------------
输入:
p _ { \parallel } ( v ) : = s _ { \infty } ( | v | )

输出:
p_{\parallel}(v):=s_{\infty}(| v |)

状态: 已规范化 ✓
================================================================================

样本 521: UniMER-1M_1052946
------------------------------------------------------------
输入:
\langle \Phi , H _ { B } \Phi \rangle \; \geq \; E _ { 0 } \Vert \Phi \Vert ^ { 2 } \ ,

输出:
\langle \Phi,H_{B} \Phi \rangle \;\geq \;E_{0} \Vert \Phi \Vert^{2},

状态: 已规范化 ✓
================================================================================

样本 522: UniMER-1M_1054949
------------------------------------------------------------
输入:
_ 2

输出:
_2

状态: 已规范化 ✓
================================================================================

样本 523: UniMER-1M_1057006
------------------------------------------------------------
输入:
\alpha < \pi / 2

输出:
\alpha<\pi/2

状态: 已规范化 ✓
================================================================================

样本 524: UniMER-1M_1059031
------------------------------------------------------------
输入:
\begin{array} { r l } { \left| \Phi \left( 2 \right) \right\rangle = } & { { } a \left| 0 \right\rangle \otimes \left| \textrm { A l i c e m e a s u r e d 0 } \right\rangle \otimes \left| \textrm { B o b r e c e i v e d 0 } \right\rangle + } \end{array}

输出:
\begin{array}{rl}{{\left| \Phi \left(2 \right) \right\rangle=}} &{{ a \left| 0 \right\rangle \otimes \left| \textrm{A l i c e m e a s u r e d 0} \right\rangle \otimes \left| \textrm{B o b r e c e i v e d 0} \right\rangle+}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 525: UniMER-1M_1061058
------------------------------------------------------------
输入:
\begin{array} { r l } { \mathcal { U } _ { n } ( x , z ) } & { = \left( \frac { 2 } { \pi } \right) ^ { 1 / 4 } \left( \frac { \exp ( \mathrm { i } ( 2 n + 1 ) \Psi ( z ) ) } { 2 ^ { n } n ! w ( z ) } \right) ^ { 1 / 2 } } \\ & { \times H _ { n } \left( \frac { \sqrt { 2 } x } { w ( z ) } \right) \exp \left( - \mathrm { i } \frac { k x ^ { 2 } } { 2 R _ { c } ( z ) } - \frac { x ^ { 2 } } { w ^ { 2 } ( z ) } \right) \, , } \end{array}

输出:
\begin{array}{rl}{{\mathcal{U}_{n}(x,z)}} &{{=\left(\frac{2}{\pi} \right)^{1/4} \left(\frac{\mathrm{exp}(\mathrm{i}(2 n+1) \Psi(z))}{2^{n} n ! w(z)} \right)^{1/2}}} \&{{\times H_{n} \left(\frac{\sqrt{2} x}{w(z)} \right) \mathrm{exp} \left(-\mathrm{i} \frac{k x^{2}}{2 R_{c}(z)}-\frac{x^{2}}{w^{2}(z)} \right) \,,}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 526: UniMER-Test_0001196
------------------------------------------------------------
输入:
\mathcal { E } _ { t } : = \sum _ { x \in \mathbb { T } _ { L } } P _ { \mathbf { a } } ( t , x ) ^ { 2 } , ~ ~ \mathcal { D } _ { t } = \sum _ { e \in E \left( \mathbb { T } _ { L } \right) } \mathbf { a } ( t , e ) ( \nabla P _ { \mathbf { a } } ( t , e ) ) ^ { 2 } ~ ~ \mathrm { a n d } ~ ~ \mathcal { N } _ { t } : = \sum _ { x \in \mathbb { T } _ { L } } | x | _ { * } ^ { p } P _ { \mathbf { a } } ( t , x ) ^ { 2 } ,

输出:
\mathcal{E}_{t}:=\sum_{x \in \mathbb{T}_{L}} P_{\mathbf{a}}(t,x)^{2},~ ~ \mathcal{D}_{t}=\sum_{e \in E \left(\mathbb{T}_{L} \right)} \mathbf{a}(t,e)(\nabla P_{\mathbf{a}}(t,e))^{2} ~ ~ \mathrm{and} ~ ~ \mathcal{N}_{t}:=\sum_{x \in \mathbb{T}_{L}} | x |_{*}^{p} P_{\mathbf{a}}(t,x)^{2},

状态: 已规范化 ✓
================================================================================

样本 527: UniMER-Test_0003077
------------------------------------------------------------
输入:
L ( \underline { { \theta } } , \underline { { a } } ) = p _ { 1 } W \left( \frac { a _ { 1 } } { \theta _ { 1 } } - 1 \right) + p _ { 2 } W \left( \frac { a _ { 2 } } { \theta _ { 2 } } - 1 \right) , \; \underline { { \theta } } = ( \theta _ { 1 } , \theta _ { 2 } ) \in \Theta _ { 0 } , \; \underline { { a } } = ( a _ { 1 } , a _ { 2 } ) \in \mathcal { A } = \Re _ { + + } ^ { 2 } ,

输出:
L(\underline{{{\theta}}},\underline{{{a}}})=p_{1} W \left(\frac{a_{1}}{\theta_{1}}-1 \right)+p_{2} W \left(\frac{a_{2}}{\theta_{2}}-1 \right),\;\underline{{{\theta}}}=(\theta_{1},\theta_{2}) \in \Theta_{0},\;\underline{{{a}}}=(a_{1},a_{2}) \in \mathcal{A}=\Re_{++}^{2},

状态: 已规范化 ✓
================================================================================

样本 528: UniMER-Test_0004933
------------------------------------------------------------
输入:
{ \begin{array} { r l } { \mathbf { B } = \mathbf { T } \times \mathbf { N } } & { = { \frac { 1 } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \left( b \sin { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { i } - b \cos { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { j } + a \mathbf { k } \right) } \\ { { \frac { d \mathbf { B } } { d s } } } & { = { \frac { 1 } { a ^ { 2 } + b ^ { 2 } } } \left( b \cos { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { i } + b \sin { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { j } + 0 \mathbf { k } \right) } \end{array} }

输出:
{\begin{array}{rl}{{\mathbf{B}=\mathbf{T} \times \mathbf{N}}} &{{={\frac{1}{\sqrt{a^{2}+b^{2}}}} \left(b \mathrm{sin}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{i}-b \mathrm{cos}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{j}+a \mathbf{k} \right)}} \{{{\frac{d \mathbf{B}}{d s}}}} &{{={\frac{1}{a^{2}+b^{2}}} \left(b \mathrm{cos}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{i}+b \mathrm{sin}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{j}+0 \mathbf{k} \right)}} \end{array}}

状态: 已规范化 ✓
================================================================================

样本 529: UniMER-Test_0006856
------------------------------------------------------------
输入:
c = \lim \limits _ { k \rightarrow + \infty } \Delta ( k )

输出:
c=\operatorname*{l i m}_{k \rightarrow+\infty} \Delta(k)

状态: 已规范化 ✓
================================================================================

样本 530: UniMER-Test_0008704
------------------------------------------------------------
输入:
\frac { C B } { B F } = \frac { D B } { P C }

输出:
\frac{C B}{B F}=\frac{D B}{P C}

状态: 已规范化 ✓
================================================================================

样本 531: UniMER-Test_0010579
------------------------------------------------------------
输入:
2 H _ { 2 } O \cong 2 H _ { 2 } \uparrow + O _ { 2 } \uparrow

输出:
2 H_{2} O \cong 2 H_{2} \uparrow+O_{2} \uparrow

状态: 已规范化 ✓
================================================================================

样本 532: Latex-OCR-full_0000220
------------------------------------------------------------
输入:
F _ { \star } = - ( \beta r _ { 0 } ) r _ { 0 } \cos 2 \theta _ { 0 } ,

输出:
F_{\star}=-(\beta r_{0}) r_{0} \mathrm{cos} 2 \theta_{0},

状态: 已规范化 ✓
================================================================================

样本 533: Latex-OCR-full_0002103
------------------------------------------------------------
输入:
\int _ { 0 } ^ { \infty } \! d q e ^ { - q } q ^ { \alpha } p _ { k _ { 1 } } ^ { \alpha } ( q ) p _ { k _ { 2 } } ^ { \alpha } ( q ) = \Gamma ( k _ { 1 } + 1 ) \Gamma ( k _ { 1 } + \alpha + 1 ) \delta _ { k _ { 1 } k _ { 2 } }

输出:
\int_{0}^{\infty} \! d q e^{-q} q^{\alpha} p_{k_{1}}^{\alpha}(q) p_{k_{2}}^{\alpha}(q)=\Gamma(k_{1}+1) \Gamma(k_{1}+\alpha+1) \delta_{k_{1} k_{2}}

状态: 已规范化 ✓
================================================================================

样本 534: Latex-OCR-full_0003977
------------------------------------------------------------
输入:
\int { \cal D } A { \cal D } [ \Lambda ] { \cal D } \lambda \quad e ^ { F ( A , \psi , \overline { \psi } , \phi ) } e ^ { ( A , d \lambda + i \ast d \Lambda ) } = \int { \cal D } A _ { h } e ^ { F ( A _ { h } , \psi , \overline { \psi } , \phi ) } .

输出:
\int{\cal D} A{\cal D}[\Lambda]{\cal D} \lambda \quad e^{F(A,\psi,\overline{{\psi}},\phi)} e^{(A,d \lambda+i \ast d \Lambda)}=\int{\cal D} A_{h} e^{F(A_{h},\psi,\overline{{\psi}},\phi)}.

状态: 已规范化 ✓
================================================================================

样本 535: Latex-OCR-full_0005864
------------------------------------------------------------
输入:
{ \frac { d \rho } { d t } } + 3 ( \rho + p ) { \frac { d a } { d t } } / a = 0 .

输出:
{\frac{d \rho}{d t}}+3(\rho+p){\frac{d a}{d t}}/a=0.

状态: 已规范化 ✓
================================================================================

样本 536: Latex-OCR-full_0007749
------------------------------------------------------------
输入:
{ \frac { S O ( 1 , 5 ) } { S O ( 5 ) } } \times { \frac { S O ( 6 ) } { S O ( 6 ) } }

输出:
{\frac{S O(1,5)}{S O(5)}} \times{\frac{S O(6)}{S O(6)}}

状态: 已规范化 ✓
================================================================================

样本 537: Latex-OCR-full_0009630
------------------------------------------------------------
输入:
z \left( X _ { i _ { 1 } } \right) = \left\{ X _ { i _ { 1 } , 1 } , \ldots , X _ { i _ { 1 } , m _ { 2 } \left( i _ { 1 } \right) } \right\} \quad .

输出:
z \left(X_{i_{1}} \right)=\left\{X_{i_{1},1},. . .,X_{i_{1},m_{2} \left(i_{1} \right)} \right\} \quad.

状态: 已规范化 ✓
================================================================================

样本 538: Latex-OCR-full_0011506
------------------------------------------------------------
输入:
- \hat { h } _ { \mu \nu } ^ { \prime \prime } + { \frac { ( a ^ { 3 / 2 } ) ^ { \prime \prime } } { a ^ { 3 / 2 } } } \hat { h } _ { \mu \nu } = { \frac { m ^ { 2 } } { H ^ { 2 } } } \hat { h } _ { \mu \nu } ,

输出:
-\hat{h}_{\mu \nu}^{\prime \prime}+{\frac{(a^{3/2})^{\prime \prime}}{a^{3/2}}} \hat{h}_{\mu \nu}={\frac{m^{2}}{H^{2}}} \hat{h}_{\mu \nu},

状态: 已规范化 ✓
================================================================================

样本 539: Latex-OCR-full_0013387
------------------------------------------------------------
输入:
{ \frac { e } { \pi } } J _ { \mu } = i \epsilon _ { \mu \nu \lambda } \partial _ { \nu } ( V ^ { \ast } \partial _ { \lambda } V )

输出:
{\frac{e}{\pi}} J_{\mu}=i \epsilon_{\mu \nu \lambda} \partial_{\nu}(V^{\ast} \partial_{\lambda} V)

状态: 已规范化 ✓
================================================================================

样本 540: Latex-OCR-full_0015270
------------------------------------------------------------
输入:
\vec { x } = \vec { z } _ { i } ( t ) \,

输出:
\vec{x}=\vec{z}_{i}(t) \,

状态: 已规范化 ✓
================================================================================

样本 541: Latex-OCR-full_0017152
------------------------------------------------------------
输入:
s l ( 3 | 2 ) \sim s \widetilde { { \cal H } } / \{ B _ { n } ^ { ( 2 ) } , V _ { r } ^ { ( 5 / 2 ) } , \bar { V } _ { r } ^ { ( 5 / 2 ) } , \ldots \} .

输出:
s l(3 | 2) \sim s \widetilde{{\cal H}}/\{B_{n}^{(2)},V_{r}^{(5/2)},\bar{V}_{r}^{(5/2)},. . . \}.

状态: 已规范化 ✓
================================================================================

样本 542: Latex-OCR-full_0019032
------------------------------------------------------------
输入:
d \tilde { \tau } = { \frac { d \tilde { t } } { \prod _ { k } e ^ { \tilde { \alpha } _ { k } } } }

输出:
d \tilde{\tau}={\frac{d \tilde{t}}{\prod_{k} e^{\tilde{\alpha}_{k}}}}

状态: 已规范化 ✓
================================================================================

样本 543: Latex-OCR-full_0020906
------------------------------------------------------------
输入:
{ \cal L } ( \Psi _ { Q } , \Psi _ { \bar { Q } } , \lambda _ { N _ { c } } ) + \bar { { \cal L } } ( T ) \sim { \cal L } ( \Psi _ { q } , \Psi _ { \bar { q } } , \lambda _ { \tilde { N } _ { c } } )

输出:
{\cal L}(\Psi_{Q},\Psi_{\bar{Q}},\lambda_{N_{c}})+\bar{{\cal L}}(T) \sim{\cal L}(\Psi_{q},\Psi_{\bar{q}},\lambda_{\tilde{N}_{c}})

状态: 已规范化 ✓
================================================================================

样本 544: Latex-OCR-full_0022791
------------------------------------------------------------
输入:
S = \int _ { M ^ { 2 } } ^ { } U ( \rho , q ^ { 2 } ) { \frac { 1 } { 2 } } e ^ { a } \varepsilon _ { a b } \wedge e ^ { b }

输出:
S=\int_{M^{2}}^ U(\rho,q^{2}){\frac{1}{2}} e^{a} \varepsilon_{a b} \wedge e^{b}

状态: 已规范化 ✓
================================================================================

样本 545: Latex-OCR-full_0024673
------------------------------------------------------------
输入:
w = \frac { N _ { 0 } + 6 N _ { 1 / 2 } + 1 2 N _ { 1 } } { 1 2 0 \cdot ( 4 \pi ) ^ { 2 } } , b = - \,

输出:
w=\frac{N_{0}+6 N_{1/2}+1 2 N_{1}}{1 2 0 \cdot(4 \pi)^{2}},b=-\,

状态: 已规范化 ✓
================================================================================

样本 546: Latex-OCR-full_0026548
------------------------------------------------------------
输入:
{ \cal L } _ { \mathrm { Y M } } = - \frac { 1 } { 4 \Omega _ { D - 1 } \alpha } \mathrm { t r }

输出:
{\cal L}_{\mathrm{YM}}=-\frac{1}{4 \Omega_{D-1} \alpha} \mathrm{tr}

状态: 已规范化 ✓
================================================================================

样本 547: Latex-OCR-full_0028435
------------------------------------------------------------
输入:
\hat { K } _ { \hat { a } \hat { b } \hat { c } } = { \textstyle \frac { 1 } { 2 } } \left( \hat { T } _ { \hat { a } \hat { c } \hat { b } } + \hat { T } _ { \hat { b } \hat { c } \hat { a } } - \hat { T } _ { \hat { a } \hat { b } \hat { c } } \right) ,

输出:
\hat{K}_{\hat{a} \hat{b} \hat{c}}={\textstyle \frac{1}{2}} \left(\hat{T}_{\hat{a} \hat{c} \hat{b}}+\hat{T}_{\hat{b} \hat{c} \hat{a}}-\hat{T}_{\hat{a} \hat{b} \hat{c}} \right),

状态: 已规范化 ✓
================================================================================

样本 548: Latex-OCR-full_0030313
------------------------------------------------------------
输入:
\varphi _ { A _ { 1 } . . . A _ { n } a _ { 1 } . . . a _ { m } } ( y ) = \int _ { { \cal F } \times S ^ { 2 } } \omega ( p , \zeta ) \ \widetilde { \varphi } ( p , \zeta ) \Phi ( p , \zeta | y ) _ { A _ { 1 } . . . A _ { n } a _ { 1 } . . . a _ { m } } \ \in { \cal H } _ { E _ { o } , s }

输出:
\varphi_{A_{1}...A_{n} a_{1}...a_{m}}(y)=\int_{{\cal F} \times S^{2}} \omega(p,\zeta) \widetilde{\varphi}(p,\zeta) \Phi(p,\zeta | y)_{A_{1}...A_{n} a_{1}...a_{m}} \in{\cal H}_{E_{o},s}

状态: 已规范化 ✓
================================================================================

样本 549: Latex-OCR-full_0032195
------------------------------------------------------------
输入:
\frac { \gamma _ { n _ { 1 } } } { d _ { \beta = [ R _ { 0 } ] } } = \frac { 2 \cdot 4 ^ { n _ { 1 } } { \cal F } _ { n _ { 1 } } } { \Gamma ( 4 n _ { 1 } - 2 ) } .

输出:
\frac{\gamma_{n_{1}}}{d_{\beta=[R_{0}]}}=\frac{2 \cdot 4^{n_{1}}{\cal F}_{n_{1}}}{\Gamma(4 n_{1}-2)}.

状态: 已规范化 ✓
================================================================================

样本 550: Latex-OCR-full_0034073
------------------------------------------------------------
输入:
\vec { b } _ { V V } ^ { \prime } = R _ { x } ( \phi ) R _ { z } ( v ) \hat { x } .

输出:
\vec{b}_{V V}^{\prime}=R_{x}(\phi) R_{z}(v) \hat{x}.

状态: 已规范化 ✓
================================================================================

样本 551: Latex-OCR-full_0035942
------------------------------------------------------------
输入:
| V _ { B } \rangle = \mathrm { e x p } \left( \frac { 1 } { 2 } \sum _ { r , s = 1 } ^ { 3 } \sum _ { m , n = - \infty } ^ { \infty } \sum _ { I = 1 } ^ { 8 } a _ { r m } ^ { I \dagger } \overline { { N } } _ { m n } ^ { r s } a _ { s n } ^ { I \dagger } \right) | 0 \rangle .

输出:
| V_{B} \rangle=\mathrm{exp} \left(\frac{1}{2} \sum_{r,s=1}^{3} \sum_{m,n=-\infty}^{\infty} \sum_{I=1}^{8} a_{r m}^{I \dagger} \overline{{{N}}}_{m n}^{r s} a_{s n}^{I \dagger} \right) | 0 \rangle.

状态: 已规范化 ✓
================================================================================

样本 552: Latex-OCR-full_0037844
------------------------------------------------------------
输入:
| \psi ( \alpha ) \rangle = e ^ { - \alpha ^ { 2 } / 2 } \sum _ { n = 0 } ^ { \infty } { \frac { \alpha ^ { n } } { \sqrt { n ! } } } | n \rangle .

输出:
| \psi(\alpha) \rangle=e^{-\alpha^{2}/2} \sum_{n=0}^{\infty}{\frac{\alpha^{n}}{\sqrt{n !}}} | n \rangle.

状态: 已规范化 ✓
================================================================================

样本 553: Latex-OCR-full_0039725
------------------------------------------------------------
输入:
\tilde { \Pi } _ { a b } \equiv \frac { 2 } { \sqrt { - \gamma } } \frac { \delta S _ { c t } } { \delta \gamma ^ { a b } }

输出:
\tilde{\Pi}_{a b} \equiv \frac{2}{\sqrt{-\gamma}} \frac{\delta S_{c t}}{\delta \gamma^{a b}}

状态: 已规范化 ✓
================================================================================

样本 554: Latex-OCR-full_0041605
------------------------------------------------------------
输入:
\partial ( h ^ { - 1 } \overline { { \partial } } h \wedge \omega ) = 0

输出:
\partial(h^{-1} \overline{{{\partial}}} h \wedge \omega)=0

状态: 已规范化 ✓
================================================================================

样本 555: Latex-OCR-full_0043478
------------------------------------------------------------
输入:
\phi _ { L } ^ { a b } = \frac 1 2 ( \phi ^ { a b } + \frac 1 2 \epsilon ^ { a b c d } \phi ^ { c d } ) \quad \quad \phi _ { R } ^ { a b } = \frac 1 2 ( \phi ^ { a b } - \frac 1 2 \epsilon ^ { a b c d } \phi ^ { c d } )

输出:
\phi_{L}^{a b}=\frac{1}{2}(\phi^{a b}+\frac{1}{2} \epsilon^{a b c d} \phi^{c d}) \quad \quad \phi_{R}^{a b}=\frac{1}{2}(\phi^{a b}-\frac{1}{2} \epsilon^{a b c d} \phi^{c d})

状态: 已规范化 ✓
================================================================================

样本 556: Latex-OCR-full_0045370
------------------------------------------------------------
输入:
{ \cal A } = { \frac { 3 } { 2 } } \sqrt { \pi } \lambda \int d t { \frac { c } { a ^ { i } a ^ { i } } } .

输出:
{\cal A}={\frac{3}{2}} \sqrt{\pi} \lambda \int d t{\frac{c}{a^{i} a^{i}}}.

状态: 已规范化 ✓
================================================================================

样本 557: Latex-OCR-full_0047236
------------------------------------------------------------
输入:
\int _ { \sum D _ { k } } { \bf n } d a = 0 ,

输出:
\int_{\sum D_{k}}{\bf n} d a=0,

状态: 已规范化 ✓
================================================================================

样本 558: Latex-OCR-full_0049106
------------------------------------------------------------
输入:
\{ D _ { F } ( x _ { 1 } - x _ { 4 } ) , D _ { F } ( x _ { 2 } - x _ { 4 } ) , D _ { F } ( x _ { 3 } - x _ { 4 } ) \} _ { \star } , \mathrm { f o r } \theta _ { 0 i } = 0 ,

输出:
\{D_{F}(x_{1}-x_{4}),D_{F}(x_{2}-x_{4}),D_{F}(x_{3}-x_{4}) \}_{\star},\mathrm{for} \theta_{0 i}=0,

状态: 已规范化 ✓
================================================================================

样本 559: Latex-OCR-full_0051000
------------------------------------------------------------
输入:
\mathrm { \ k a p p a } \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu } A _ { \sigma } = \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu }

输出:
\mathrm{kappa} \frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu} A_{\sigma}=\frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu}

状态: 已规范化 ✓
================================================================================

样本 560: Latex-OCR-full_0052811
------------------------------------------------------------
输入:
\tilde { L _ { 1 } } ( t ) ( \alpha ^ { * } , \alpha ) = \frac { g ^ { 2 } } { 2 } \left( \frac { 1 } { 3 ! } V _ { a b c } W ^ { a b c } + \alpha _ { a } ^ { * } ( V _ { c d } ^ { a } W _ { b } ^ { c d } + V _ { b c d } W ^ { c d a } ) \alpha ^ { b } \right)

输出:
\tilde{L_{1}}(t)(\alpha^{*},\alpha)=\frac{g^{2}}{2} \left(\frac{1}{3 !} V_{a b c} W^{a b c}+\alpha_{a}^{*}(V_{c d}^{a} W_{b}^{c d}+V_{b c d} W^{c d a}) \alpha^{b} \right)

状态: 已规范化 ✓
================================================================================

样本 561: Latex-OCR-full_0054761
------------------------------------------------------------
输入:
{ \cal A } _ { e h } ( \tau ) = ( 1 - e ^ { - \tau } ) { \cal A } _ { d S } ,

输出:
{\cal A}_{e h}(\tau)=(1-e^{-\tau}){\cal A}_{d S},

状态: 已规范化 ✓
================================================================================

样本 562: Latex-OCR-full_0056660
------------------------------------------------------------
输入:
\left( \begin{matrix} { a ^ { \prime \prime } } & { b ^ { \prime \prime } } \\ { c ^ { \prime \prime } } & { d ^ { \prime \prime } } \\ \end{matrix} \right) = \left( \begin{matrix} { a } & { b } \\ { c } & { d } \\ \end{matrix} \right) + \left( \begin{matrix} { a ^ { \prime } } & { b ^ { \prime } } \\ { c ^ { \prime } } & { d ^ { \prime } } \\ \end{matrix} \right)

输出:
\left(\begin{matrix}{{a^{\prime \prime}}} &{{b^{\prime \prime}}} \{{c^{\prime \prime}}} &{{d^{\prime \prime}}} \\ \end{matrix} \right)=\left(\begin{matrix}{{a}} &{{b}} \{{c}} &{{d}} \\ \end{matrix} \right)+\left(\begin{matrix}{{a^{\prime}}} &{{b^{\prime}}} \{{c^{\prime}}} &{{d^{\prime}}} \\ \end{matrix} \right)

状态: 已规范化 ✓
================================================================================

样本 563: Latex-OCR-full_0058521
------------------------------------------------------------
输入:
m _ { 1 } \ldots m _ { n } ( a _ { 1 } b _ { 1 } ^ { - 1 } a _ { 1 } ^ { - 1 } b _ { 1 } ) \ldots ( a _ { g } b _ { g } ^ { - 1 } a _ { g } ^ { - 1 } b _ { g } ) = i d .

输出:
m_{1} . . . m_{n}(a_{1} b_{1}^{-1} a_{1}^{-1} b_{1}) . . .(a_{g} b_{g}^{-1} a_{g}^{-1} b_{g})=i d.

状态: 已规范化 ✓
================================================================================

样本 564: Latex-OCR-full_0060403
------------------------------------------------------------
输入:
\eta = 4 \alpha [ - m _ { 1 } + m _ { 2 } + m _ { 3 } + s _ { 1 } + s _ { 2 } + s _ { 3 } + s _ { 4 } + t - 2 N ( r _ { 2 } + 2 s _ { 3 } + s _ { 4 } ) ] .

输出:
\eta=4 \alpha[-m_{1}+m_{2}+m_{3}+s_{1}+s_{2}+s_{3}+s_{4}+t-2 N(r_{2}+2 s_{3}+s_{4})].

状态: 已规范化 ✓
================================================================================

样本 565: Latex-OCR-full_0062292
------------------------------------------------------------
输入:
\big \langle { \cal V } _ { h _ { \mu \nu } } \big \rangle _ { \mathrm { d i s k } _ { p } } = \langle h _ { \mu \nu } | \mathrm { D } p \rangle ,

输出:
\big\langle{\cal V}_{h_{\mu \nu}} \big\rangle_{\mathrm{disk}_{p}}=\langle h_{\mu \nu} | \mathrm{D} p \rangle,

状态: 已规范化 ✓
================================================================================

样本 566: Latex-OCR-full_0064169
------------------------------------------------------------
输入:
[ D _ { i } , \hat { F } _ { i k } ] = - [ D _ { 0 } , \hat { F } _ { 0 k } ]

输出:
[D_{i},\hat{F}_{i k}]=-[D_{0},\hat{F}_{0 k}]

状态: 已规范化 ✓
================================================================================

样本 567: Latex-OCR-full_0066047
------------------------------------------------------------
输入:
\gamma _ { S _ { 1 } } ^ { 2 } = \gamma _ { S _ { 2 } } ^ { 2 } = \gamma _ { S _ { 3 } } ^ { 2 } .

输出:
\gamma_{S_{1}}^{2}=\gamma_{S_{2}}^{2}=\gamma_{S_{3}}^{2}.

状态: 已规范化 ✓
================================================================================

样本 568: Latex-OCR-full_0067921
------------------------------------------------------------
输入:
\left( \begin{array} { c } { F } \\ { * F } \\ \end{array} \right) \rightarrow \left( \begin{array} { l l } { \cos \alpha } & { \sin \alpha } \\ { - \sin \alpha } & { \cos \alpha } \\ \end{array} \right) \left( \begin{array} { c } { F } \\ { * F } \\ \end{array} \right) .

输出:
\left(\begin{array}{c}{{F}} \{{*F}} \end{array} \right) \rightarrow \left(\begin{array}{ll}{{\mathrm{cos} \alpha}} &{{\mathrm{sin} \alpha}} \{{-\mathrm{sin} \alpha}} &{{\mathrm{cos} \alpha}} \end{array} \right) \left(\begin{array}{c}{{F}} \{{*F}} \end{array} \right).

状态: 已规范化 ✓
================================================================================

样本 569: Latex-OCR-full_0069808
------------------------------------------------------------
输入:
L _ { n } = \sum _ { m } \frac 1 2 \alpha _ { n - m } \alpha _ { m } , \tilde { L } _ { n } = \sum _ { m } \frac 1 2 \tilde { \alpha } _ { n - m } \tilde { \alpha } _ { m } ,

输出:
L_{n}=\sum_{m} \frac{1}{2} \alpha_{n-m} \alpha_{m},\tilde{L}_{n}=\sum_{m} \frac{1}{2} \tilde{\alpha}_{n-m} \tilde{\alpha}_{m},

状态: 已规范化 ✓
================================================================================

样本 570: Latex-OCR-full_0071688
------------------------------------------------------------
输入:
d s ^ { 2 } = - d t ^ { 2 } + a ( t ) ^ { 2 } \left( d \chi ^ { 2 } + \sinh ^ { 2 } \chi d \Omega _ { 2 } ^ { 2 } \right) ,

输出:
d s^{2}=-d t^{2}+a(t)^{2} \left(d \chi^{2}+\mathrm{sinh}^{2} \chi d \Omega_{2}^{2} \right),

状态: 已规范化 ✓
================================================================================

样本 571: Latex-OCR-full_0073566
------------------------------------------------------------
输入:
\nu _ { T O T A L } ( p ) = \nu _ { 1 } ( p ) + \nu _ { 2 } ( p ) + \nu _ { 3 } ( p ) = \frac { p } { 1 2 } ( p ^ { 2 } + 2 p + 2 )

输出:
\nu_{T O T A L}(p)=\nu_{1}(p)+\nu_{2}(p)+\nu_{3}(p)=\frac{p}{1 2}(p^{2}+2 p+2)

状态: 已规范化 ✓
================================================================================

样本 572: Latex-OCR-full_0075455
------------------------------------------------------------
输入:
\mathrm { \ r h o } _ { 0 } = \mathcal { J } ^ { 0 } \left( y \right) ,

输出:
\mathrm{rho}_{0}=\mathcal{J}^{0} \left(y \right),

状态: 已规范化 ✓
================================================================================

样本 573: Latex-OCR-full_0077329
------------------------------------------------------------
输入:
k _ { 0 \pm } ^ { 2 } = \frac { 1 } { 2 } \left[ \left( s ^ { 2 } + 2 \overrightarrow { k } ^ { 2 } \right) \pm \sqrt { s ^ { 4 } + 4 v _ { o } ^ { 2 } \overrightarrow { k } ^ { 2 } } \right] ,

输出:
k_{0 \pm}^{2}=\frac{1}{2} \left[\left(s^{2}+2 \overrightarrow{k}^{2} \right) \pm \sqrt{s^{4}+4 v_{o}^{2} \overrightarrow{k}^{2}} \right],

状态: 已规范化 ✓
================================================================================

样本 574: Latex-OCR-full_0079208
------------------------------------------------------------
输入:
\partial _ { \mu } = \frac { \partial } { \partial \xi ^ { \mu } }

输出:
\partial_{\mu}=\frac{\partial}{\partial \xi^{\mu}}

状态: 已规范化 ✓
================================================================================

样本 575: Latex-OCR-full_0081093
------------------------------------------------------------
输入:
\frac { \kappa + \alpha } { ( \kappa - a _ { 1 } ) ( \kappa - \kappa _ { 1 } ) ( \kappa - \kappa _ { 2 } ) }

输出:
\frac{\kappa+\alpha}{(\kappa-a_{1})(\kappa-\kappa_{1})(\kappa-\kappa_{2})}

状态: 已规范化 ✓
================================================================================

样本 576: Latex-OCR-full_0082969
------------------------------------------------------------
输入:
T _ { F } = { \frac { i } { 2 } } \sum _ { a = 0 } ^ { 3 } \psi ^ { a } \partial X ^ { a } + { \mathrm { h . c . } } .

输出:
T_{F}={\frac{i}{2}} \sum_{a=0}^{3} \psi^{a} \partial X^{a}+{\mathrm{h.c.}}.

状态: 已规范化 ✓
================================================================================

样本 577: Latex-OCR-full_0084850
------------------------------------------------------------
输入:
\overline { { \sigma } } ^ { \prime } ( s ) = \overline { { \sigma } } _ { a } ^ { \prime } ( s ) \Theta ( s - 4 ) + \overline { { \sigma } } _ { b } ^ { \prime } ( s ) \Theta ( s - 9 ) .

输出:
\overline{{{\sigma}}}^{\prime}(s)=\overline{{{\sigma}}}_{a}^{\prime}(s) \Theta(s-4)+\overline{{{\sigma}}}_{b}^{\prime}(s) \Theta(s-9).

状态: 已规范化 ✓
================================================================================

样本 578: Latex-OCR-full_0086735
------------------------------------------------------------
输入:
\left( \begin{array} { c } { n } \\ { m } \\ \end{array} \right) = \frac { n ( n - 1 ) . . . ( n - m + 1 ) } { m ! } , { } { } \left( \begin{array} { c } { n } \\ { 0 } \\ \end{array} \right) = 1 ,

输出:
\left(\begin{array}{c}{{n}} \{{m}} \end{array} \right)=\frac{n(n-1)...(n-m+1)}{m !}, \left(\begin{array}{c}{{n}} \{{0}} \end{array} \right)=1,

状态: 已规范化 ✓
================================================================================

样本 579: Latex-OCR-full_0088616
------------------------------------------------------------
输入:
M _ { n } = \sqrt { m ^ { 2 } + { \mathcal { B } } ( 2 n + 1 ) } .

输出:
M_{n}=\sqrt{m^{2}+{\mathcal{B}}(2 n+1)}.

状态: 已规范化 ✓
================================================================================

样本 580: Latex-OCR-full_0090491
------------------------------------------------------------
输入:
x _ { \alpha ^ { \vee } } = Z _ { \alpha } - i \pi - x _ { \alpha }

输出:
x_{\alpha^{\vee}}=Z_{\alpha}-i \pi-x_{\alpha}

状态: 已规范化 ✓
================================================================================

样本 581: Latex-OCR-full_0092376
------------------------------------------------------------
输入:
\left\{ - \frac { d ^ { 2 } } { d r ^ { 2 } } + \frac { \gamma } { r } + V ( r ) \right\} u = k ^ { 2 } u

输出:
\left\{-\frac{d^{2}}{d r^{2}}+\frac{\gamma}{r}+V(r) \right\} u=k^{2} u

状态: 已规范化 ✓
================================================================================

样本 582: Latex-OCR-handwrite_0000025
------------------------------------------------------------
输入:
7 \times 1 5 4 \neq - 1 3 6 2

输出:
7 \times 1 5 4 \neq-1 3 6 2

状态: 已规范化 ✓
================================================================================

样本 583: TexTeller_0000580
------------------------------------------------------------
输入:
\begin{align*}\frac{4q^2+12q+15+ 8p(q+2)+4p^2}{4\{2\}}b^2\ + \frac{(3+2p)[q^2+3q+3+2p(q+1)]}{2}b\bigg]\ ,\end{align*}

输出:
\begin{align*}\frac{4q^2+12q+15+8p(q+2)+4p^2}{4\{2\}}b^2+\frac{(3+2p)[q^2+3q+3+2p(q+1)]}{2}b\bigg],\end{align*}

状态: 已规范化 ✓
================================================================================

样本 584: TexTeller_0002441
------------------------------------------------------------
输入:
\begin{align*}\dot{x}=e^{2S(x)}S'(x)+e^{S(x)}\eta\end{align*}

输出:
\begin{align*}\dot{x}=e^{2S(x)}S'(x)+e^{S(x)}\eta\end{align*}

状态: 无变化
================================================================================

样本 585: TexTeller_0004309
------------------------------------------------------------
输入:
\begin{align*}\hat{H}=\sqrt{\frac{2\Omega}{\mu}}[\hat{a}_+^{\dag} \hat{a}_+ +\hat{a}_-^{\dag}\hat{a}_- +1 ] - \Omega \theta [\hat{a}_-^{\dag}\hat{a}_- - \hat{a}_+^{\dag} \hat{a}_+].\end{align*}

输出:
\begin{align*}\hat{H}=\sqrt{\frac{2\Omega}{\mu}}[\hat{a}_+^{\dag} \hat{a}_++\hat{a}_-^{\dag}\hat{a}_-+1]-\Omega \theta[\hat{a}_-^{\dag}\hat{a}_- -\hat{a}_+^{\dag} \hat{a}_+].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 586: TexTeller_0006211
------------------------------------------------------------
输入:
\begin{align*}\sum_{i<j} \sqrt{l_i l_j} \leq \frac{n-1}{2}.\end{align*}

输出:
\begin{align*}\sum_{i<j} \sqrt{l_i l_j} \leq \frac{n-1}{2}.\end{align*}

状态: 无变化
================================================================================

样本 587: TexTeller_0008088
------------------------------------------------------------
输入:
\begin{align*}g_{\mu \nu} = g^{(0)}_{\mu \nu} + g^{(1)}_{\mu \nu} + \cdots\end{align*}

输出:
\begin{align*}g_{\mu \nu}=g^{(0)}_{\mu \nu}+g^{(1)}_{\mu \nu}+. . .\end{align*}

状态: 已规范化 ✓
================================================================================

样本 588: TexTeller_0009975
------------------------------------------------------------
输入:
\begin{align*}{\textstyle \left(L,\left\{ \prod^{p,q}\right\} _{p+q=n}\right)}\end{align*}

输出:
\begin{align*}{\textstyle \left(L,\left\{\prod^{p,q}\right\}_{p+q=n}\right)}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 589: TexTeller_0011844
------------------------------------------------------------
输入:
\begin{align*}L_Xf\omega = fL_X\omega+i_{[X,f]}\omega.\end{align*}

输出:
\begin{align*}L_Xf\omega=fL_X\omega+i_{[X,f]}\omega.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 590: TexTeller_0013729
------------------------------------------------------------
输入:
\begin{align*}^3R_{mn} = \frac {1}{2}Tr[J^P_mJ^P_n + J^Q_mJ^Q_n],\end{align*}

输出:
\begin{align*}^3R_{mn}=\frac{{1}{2}Tr[J^P_mJ^P_n}{+} J^Q_mJ^Q_n],\end{align*}

状态: 已规范化 ✓
================================================================================

样本 591: TexTeller_0015617
------------------------------------------------------------
输入:
\begin{align*}\left(\chi_{1}, \chi_{2}, \chi_{3}, \chi_{4} \right) = (z, t, P, E ) .\end{align*}

输出:
\begin{align*}\left(\chi_{1},\chi_{2},\chi_{3},\chi_{4} \right)=(z,t,P,E).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 592: TexTeller_0017393
------------------------------------------------------------
输入:
\begin{align*}\frac{\sqrt{-\hat{g}}}{\hat{g}_{str}} \frac{ \hat{g}^{00} \hat{g}^{11} F_{01} } {\sqrt{ 1+(2\pi\alpha^\prime)^2 \hat{g}^{00}\hat{g}^{11} F_{01}^2 }} = B_{23} =\frac{s}{2\pi R^2},\end{align*}

输出:
\begin{align*}\frac{\sqrt{-\hat{g}}}{\hat{g}_{str}} \frac{\hat{g}^{00} \hat{g}^{11} F_{01}}{\sqrt{1+(2\pi\alpha^\prime)^2 \hat{g}^{00}\hat{g}^{11} F_{01}^2}}=B_{23}=\frac{s}{2\pi R^2},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 593: TexTeller_0019383
------------------------------------------------------------
输入:
\begin{align*}G(\Psi):=(4-2d)\omega Q(\Psi)+(3-d)\mathbf{c}\cdot \mathbf{P}(\Psi).\end{align*}

输出:
\begin{align*}G(\Psi):=(4-2d)\omega Q(\Psi)+(3-d)\mathbf{c}\cdot \mathbf{P}(\Psi).\end{align*}

状态: 无变化
================================================================================

样本 594: TexTeller_0021255
------------------------------------------------------------
输入:
\begin{align*}\left\vert f\left( z\right) \right\vert \geq\left\vert g\left( z\right)\right\vert -\left\vert g\left( z\right) -f\left( z\right) \right\vert>s-\left( s-n\right) =n\end{align*}

输出:
\begin{align*}\left\vert f\left(z\right) \right\vert \geq\left\vert g\left(z\right)\right\vert-\left\vert g\left(z\right)-f\left(z\right) \right\vert>s-\left(s-n\right)=n\end{align*}

状态: 已规范化 ✓
================================================================================

样本 595: TexTeller_0023129
------------------------------------------------------------
输入:
\begin{align*}U(I)=\{u|u_{1:k}\ge 0 \ \ u_{1:k}<0\ \}.\end{align*}

输出:
\begin{align*}U(I)=\{u|u_{1:k}\ge 0 \ \ u_{1:k}<0\ \}.\end{align*}

状态: 无变化
================================================================================

样本 596: TexTeller_0024997
------------------------------------------------------------
输入:
\begin{align*} \int_0^T g(X_t^\dagger,t)\,{\rm d}X_t^\dagger = \lim_{\Delta t\to 0} \sum_{i=1}^L g(X_{t_n}^\dagger,t_n)(X_{t_{n+1}}^\dagger -X_{t_n}^\dagger )\end{align*}

输出:
\begin{align*} \int_0^T g(X_t^\dagger,t)\,{\rm d}X_t^\dagger=\lim_{\Delta t\to 0} \sum_{i=1}^L g(X_{t_n}^\dagger,t_n)(X_{t_{n+1}}^\dagger-X_{t_n}^\dagger)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 597: TexTeller_0026893
------------------------------------------------------------
输入:
\begin{align*}E_{22}(t)\dot x_2=A_{22}(t)x_2\end{align*}

输出:
\begin{align*}E_{22}(t)\dot x_2=A_{22}(t)x_2\end{align*}

状态: 无变化
================================================================================

样本 598: TexTeller_0028761
------------------------------------------------------------
输入:
\begin{align*}\ell_{j}^{\ast}\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}}=\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}},j=1,2.\end{align*}

输出:
\begin{align*}\ell_{j}^{\ast}\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}}=\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}},j=1,2.\end{align*}

状态: 无变化
================================================================================

样本 599: TexTeller_0030641
------------------------------------------------------------
输入:
\begin{align*}\mu_g(A)\le \frac{C}{\lambda} |z|^{\frac{n-2}{2}} \mu_g(E)^{\frac{1}{p_2}}\mu_g(A)^{\frac{1}{q_2'}}= \frac{C}{\lambda} |z|^{-\frac{1}{n+1}}\mu_g(E)^{\frac{n^2+4n-1}{2n(n+1)}} \mu_g(A)^{\frac{n+1}{2n}} R_0^\frac{n-1}{2} \,.\end{align*}

输出:
\begin{align*}\mu_g(A)\le \frac{C}{\lambda} |z|^{\frac{n-2}{2}} \mu_g(E)^{\frac{1}{p_2}}\mu_g(A)^{\frac{1}{q_2'}}=\frac{C}{\lambda} |z|^{-\frac{1}{n+1}}\mu_g(E)^{\frac{n^2+4n-1}{2n(n+1)}} \mu_g(A)^{\frac{n+1}{2n}} R_0^\frac{n-1}{2} \,.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 600: TexTeller_0032507
------------------------------------------------------------
输入:
\begin{align*}\Phi\left(\Psi(\theta)\right)= \begin{pmatrix} -XZ+L_{1}(1-Z-\mu(1-Y)) \\ -L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y) \\ \frac{1}{L_{2}}((-XZ+1-Z-\mu(1-Y))+\mu)-L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y))) \end{pmatrix} .\end{align*}

输出:
\begin{align*}\Phi\left(\Psi(\theta)\right)=\begin{pmatrix}-XZ+L_{1}(1-Z-\mu(1-Y)) \-L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y) \\ \frac{1}{L_{2}}((-XZ+1-Z-\mu(1-Y))+\mu)-L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y))) \end{pmatrix}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 601: TexTeller_0034421
------------------------------------------------------------
输入:
\begin{align*}j(x,\xi) := i\xi_n + F(x',\xi') \end{align*}

输出:
\begin{align*}j(x,\xi):=i\xi_n+F(x',\xi') \end{align*}

状态: 已规范化 ✓
================================================================================

样本 602: TexTeller_0036279
------------------------------------------------------------
输入:
\begin{align*}w_r(\tau) = e^{\tau \Delta}w_r(0) &+ \int_0^\tau e^{(\tau - s)\Delta}\big(e^u \Delta \phi_r - 2\nabla \cdot (e^u \nabla \phi_r)\big) ds \\&+ \int_0^\tau e^{(\tau - s)\Delta}e^u\phi_r (\partial_\tau u - \Delta u - |\nabla u|^2)ds,\end{align*}

输出:
\begin{align*}w_r(\tau)=e^{\tau \Delta}w_r(0) &+\int_0^\tau e^{(\tau-s)\Delta}\big(e^u \Delta \phi_r-2\nabla \cdot(e^u \nabla \phi_r)\big) ds \\&+\int_0^\tau e^{(\tau-s)\Delta}e^u\phi_r(\partial_\tau u-\Delta u-|\nabla u|^2)ds,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 603: TexTeller_0038179
------------------------------------------------------------
输入:
\begin{align*}\left[V_{v}\right]:=\bigoplus_{\varepsilon_v}\left[V_{v}\right]^{\varepsilon_v}\end{align*}

输出:
\begin{align*}\left[V_{v}\right]:=\bigoplus_{\varepsilon_v}\left[V_{v}\right]^{\varepsilon_v}\end{align*}

状态: 无变化
================================================================================

样本 604: TexTeller_0040066
------------------------------------------------------------
输入:
\begin{align*} \Big|\omega(P_E(n)) -\sum_{i=1}^r \omega(P_i(n)) \Big| \leq C'.\end{align*}

输出:
\begin{align*} \Big|\omega(P_E(n))-\sum_{i=1}^r \omega(P_i(n)) \Big| \leq C'.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 605: TexTeller_0041945
------------------------------------------------------------
输入:
\begin{align*}s:=\dim_{A}(K_{v}(\omega))<t:=\frac{-\log(\mathfrak{P}(\epsilon))}{\log(\epsilon)}.\end{align*}

输出:
\begin{align*}s:=\dim_{A}(K_{v}(\omega))<t:=\frac{-\log(\mathfrak{P}(\epsilon))}{\log(\epsilon)}.\end{align*}

状态: 无变化
================================================================================

样本 606: TexTeller_0043816
------------------------------------------------------------
输入:
\begin{align*} p = \pi_{W, T}.\end{align*}

输出:
\begin{align*} p=\pi_{W,T}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 607: TexTeller_0045707
------------------------------------------------------------
输入:
\begin{align*} 0 \to \mathbb{Q}_p \to B_{max,\mathbb{Q}_p}^{\phi_p = 1} \to B_{dR}/B^+_{dR} \to 0\end{align*}

输出:
\begin{align*} 0 \to \mathbb{Q}_p \to B_{max,\mathbb{Q}_p}^{\phi_p=1} \to B_{dR}/B^+_{dR} \to 0\end{align*}

状态: 已规范化 ✓
================================================================================

样本 608: TexTeller_0047565
------------------------------------------------------------
输入:
\begin{align*}A\|f\|^2\leq \sum_{m=1}^L\sum_{(j, k) \in \mathbb Z \times \mathbb N_0}\left|<f, D^j_{\mathfrak p}T_{k}\psi_m>\right|^2 \leq B\|f\|^2,\end{align*}

输出:
\begin{align*}A\|f\|^2\leq \sum_{m=1}^L\sum_{(j,k) \in \mathbb{Z} \times \mathbb{N_0}\left|<f,} D^j_{\mathfrak p}T_{k}\psi_m>\right|^2 \leq B\|f\|^2,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 609: TexTeller_0049466
------------------------------------------------------------
输入:
\begin{align*} |c_M|_1 &\leq \binom{n}{n-1} \cdot |c_N|_1 \cdot |c|_1 \leq n \cdot |c_N|_1 \cdot \epsilon.\end{align*}

输出:
\begin{align*} |c_M|_1 &\leq \binom{n}{n-1} \cdot |c_N|_1 \cdot |c|_1 \leq n \cdot |c_N|_1 \cdot \epsilon.\end{align*}

状态: 无变化
================================================================================

样本 610: TexTeller_0051347
------------------------------------------------------------
输入:
\begin{align*}\lambda=\lim_{n\to\infty} \lambda_n.\end{align*}

输出:
\begin{align*}\lambda=\lim_{n\to\infty} \lambda_n.\end{align*}

状态: 无变化
================================================================================

样本 611: TexTeller_0053231
------------------------------------------------------------
输入:
\begin{align*}\sum_{n \geq 1} x_n z^n =\frac{\sum_{j \in J}z^j}{1-\sum_{j \in J}z^j},\end{align*}

输出:
\begin{align*}\sum_{n \geq 1} x_n z^n=\frac{\sum_{j \in J}z^j}{1-\sum_{j \in J}z^j},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 612: TexTeller_0055098
------------------------------------------------------------
输入:
\begin{align*}u(x,t)=\sum_{k=1}^{\infty} u_k(t) \sin(k \pi x), \end{align*}

输出:
\begin{align*}u(x,t)=\sum_{k=1}^{\infty} u_k(t) \sin(k \pi x),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 613: TexTeller_0056975
------------------------------------------------------------
输入:
\begin{align*} \| e^{(t-s) \Delta } F_1 (s) \| _{ L^1 } & \le \| F_1 (s) \| _{ L^1 }= \int _{ \{ |x|<\sqrt s \} } s^{-\frac {\alpha +1} {\alpha }} \Bigl| f \Bigl( \frac {x} {\sqrt s} \Bigr) \Bigr|^{\alpha +1} dx \\ & = \int _{ \{ |x|< 1 \} } |f(x)|^{\alpha +1}dx \end{align*}

输出:
\begin{align*} \| e^{(t-s) \Delta} F_1(s) \|_{L^1} & \le \| F_1(s) \|_{L^1}=\int_{\{|x|<\sqrt s \}} s^{-\frac{\alpha+1}{\alpha}} \Bigl| f \Bigl(\frac{{x}}{{\sqrt} s} \Bigr) \Bigr|^{\alpha+1} dx \&=\int_{\{|x|<1 \}} |f(x)|^{\alpha+1}dx \end{align*}

状态: 已规范化 ✓
================================================================================

样本 614: TexTeller_0058844
------------------------------------------------------------
输入:
\begin{align*} \begin{cases} v_i(x,t)=S_{d,i}(t,t_{1,ex}(x,t))S_{c,i}(t_{i,ex}(x,t),t_{1,en}(x,t))S_{d,i}(t_{i,en}(x,t),0)v_{i,0}(x) \forall\:i\in[1,p], \\ v_i(x,t)=S_{d,i}(t,0)v_{i,0}(x) \forall\:i\in[p+1,n]. \end{cases} \end{align*}

输出:
\begin{align*} \begin{cases} v_i(x,t)=S_{d,i}(t,t_{1,ex}(x,t))S_{c,i}(t_{i,ex}(x,t),t_{1,en}(x,t))S_{d,i}(t_{i,en}(x,t),0)v_{i,0}(x) \forall\:i\in[1,p],\\ v_i(x,t)=S_{d,i}(t,0)v_{i,0}(x) \forall\:i\in[p+1,n].\end{cases} \end{align*}

状态: 已规范化 ✓
================================================================================

样本 615: TexTeller_0060737
------------------------------------------------------------
输入:
\begin{align*} \nabla c\cdot \nu=(\gamma-c)g\quad\mbox{on}\partial\Omega. \end{align*}

输出:
\begin{align*} \nabla c\cdot \nu=(\gamma-c)g\quad\text{CONTENTPROTECTED0}\partial\Omega.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 616: TexTeller_0062619
------------------------------------------------------------
输入:
\begin{align*}&\|I_1(x)\|_{L^{2,1} } = \frac{1} {\pi} \| \widehat{(z^{-1} \bar{r}_1)}(2x)\|_{L^{2,1}_x} = \frac{1}{\pi} \| z^{-1} \bar{r}_1(z) \|_{ H^1_z}. \end{align*}

输出:
\begin{align*}&\|I_1(x)\|_{L^{2,1}}=\frac{1}{\pi} \| \widehat{(z^{-1} \bar{r}_1)}(2x)\|_{L^{2,1}_x}=\frac{1}{\pi} \| z^{-1} \bar{r}_1(z) \|_{H^1_z}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 617: TexTeller_0064506
------------------------------------------------------------
输入:
\begin{align*} d(X( \bar u)) - L_X g (\nabla \bar u ,\cdot )&= \nabla^2 \bar u (X, \cdot ) - \nabla X (\nabla \bar u,\cdot).\end{align*}

输出:
\begin{align*} d(X(\bar{u))}-L_X g(\nabla \bar{u},\cdot)&=\nabla^2 \bar{u}(X,\cdot)-\nabla X(\nabla \bar{u,\cdot).\end{align*}}

状态: 已规范化 ✓
================================================================================

样本 618: TexTeller_0066347
------------------------------------------------------------
输入:
\begin{align*} g(0)g(\infty)=&\frac{(x_0+\sqrt{a}x_1)(\sqrt{b}x_2+\sqrt{ab}x_3)}{(\sqrt{b}x_2-\sqrt{ab}x_3)(x_0-\sqrt{a}x_1)}>0\\ \Leftrightarrow\ &(x_0^2-ax_1^2)(bx_2^2-abx_3^2)>0\\ \Leftrightarrow\ &(x_0^2-ax_1^2)(x_0^2-ax_1^2-1)>0\end{align*}

输出:
\begin{align*} g(0)g(\infty)=&\frac{(x_0+\sqrt{a}x_1)(\sqrt{b}x_2+\sqrt{ab}x_3)}{(\sqrt{b}x_2-\sqrt{ab}x_3)(x_0-\sqrt{a}x_1)}>0\\ \Leftrightarrow&(x_0^2-ax_1^2)(bx_2^2-abx_3^2)>0\\ \Leftrightarrow&(x_0^2-ax_1^2)(x_0^2-ax_1^2-1)>0\end{align*}

状态: 已规范化 ✓
================================================================================

样本 619: TexTeller_0068287
------------------------------------------------------------
输入:
\begin{align*} \frac{d\mu_S}{d\mu_T} =|h|^2 \end{align*}

输出:
\begin{align*} \frac{d\mu_S}{d\mu_T}=|h|^2 \end{align*}

状态: 已规范化 ✓
================================================================================

样本 620: TexTeller_0070147
------------------------------------------------------------
输入:
\begin{gather*}K=\sum _{i=-\mu}^\mu K_p,\end{gather*}

输出:
\begin{gather*}K=\sum_{i=-\mu}^\mu K_p,\end{gather*}

状态: 已规范化 ✓
================================================================================

样本 621: TexTeller_0072036
------------------------------------------------------------
输入:
\begin{align*}\frac{d}{dz}W_{\alpha,\beta}^{\gamma,\sigma}(z)=\frac{\gamma}{\sigma}W_{\alpha,\beta+\alpha}^{\gamma+1,\sigma+1}(z),\end{align*}

输出:
\begin{align*}\frac{d}{dz}W_{\alpha,\beta}^{\gamma,\sigma}(z)=\frac{\gamma}{\sigma}W_{\alpha,\beta+\alpha}^{\gamma+1,\sigma+1}(z),\end{align*}

状态: 无变化
================================================================================

样本 622: TexTeller_0073901
------------------------------------------------------------
输入:
\begin{align*} \left( A^{\alpha} g \right)(x) = \lim\limits_{\varepsilon \rightarrow 0+} \left( \left( J^{\alpha}_{\left( A + \varepsilon \right)^{-1}} \right)^{-1} g \right) (x) \end{align*}

输出:
\begin{align*} \left(A^{\alpha} g \right)(x)=\lim\limits_{\varepsilon \rightarrow 0+} \left(\left(J^{\alpha}_{\left(A+\varepsilon \right)^{-1}} \right)^{-1} g \right)(x) \end{align*}

状态: 已规范化 ✓
================================================================================

样本 623: TexTeller_0075788
------------------------------------------------------------
输入:
\begin{align*}A\left( \widetilde{u}\right) =f\left( x_{1}-x_{0}\right) \chi \left( \overline{x}-\overline{x}^{0}\right) ,\forall x_{0}\in \left[ 0,1\right] ,\end{align*}

输出:
\begin{align*}A\left(\widetilde{u}\right)=f\left(x_{1}-x_{0}\right) \chi \left(\overline{x}-\overline{x}^{0}\right),\forall x_{0}\in \left[0,1\right],\end{align*}

状态: 已规范化 ✓
================================================================================

样本 624: TexTeller_0077661
------------------------------------------------------------
输入:
\begin{align*} v_{3}(S_{k}(2x))= v_{3}(S_{k}(x))= \gamma+ 2d- 1\end{align*}

输出:
\begin{align*} v_{3}(S_{k}(2x))=v_{3}(S_{k}(x))=\gamma+2d-1\end{align*}

状态: 已规范化 ✓
================================================================================

样本 625: TexTeller_0079549
------------------------------------------------------------
输入:
\begin{align*} \frac{dx^i}{d\tau}=f(x^1,\ldots,x^n)\ X^i(x^1,\ldots,x^n),i=1,\ldots,n. \end{align*}

输出:
\begin{align*} \frac{dx^i}{d\tau}=f(x^1,. . .,x^n)\ X^i(x^1,. . .,x^n),i=1,. . .,n.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 626: TexTeller_0081426
------------------------------------------------------------
输入:
\begin{align*} -\nabla_{g}^2 f_{\sigma}(x) = \lambda(\sigma) f_{\sigma}(x) \;,\end{align*}

输出:
\begin{align*}-\nabla_{g}^2 f_{\sigma}(x)=\lambda(\sigma) f_{\sigma}(x) \;,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 627: TexTeller_0083245
------------------------------------------------------------
输入:
\begin{align*}\nu_{(a^-, i)}^{\hat{S}^k} = \nu_{(a_k^-, i_k)}^* - \frac{w_{(a^-, i)}^{\hat{S}^{k-1}}}{w_{(a^-, i)}^{\hat{S}^{k}}} \big(\nu_{(a_k^-, i_k)}^* - \nu_{(a^-, i)}^{\hat{S}^{k-1}}\big), (a^-, i) \in \hat{N}.\end{align*}

输出:
\begin{align*}\nu_{(a^-,i)}^{\hat{S}^k}=\nu_{(a_k^-,i_k)}^*-\frac{w_{(a^-,i)}^{\hat{S}^{k-1}}}{w_{(a^-,i)}^{\hat{S}^{k}}} \big(\nu_{(a_k^-,i_k)}^*-\nu_{(a^-,i)}^{\hat{S}^{k-1}}\big),(a^-,i) \in \hat{N}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 628: TexTeller_0085186
------------------------------------------------------------
输入:
\begin{align*} \sum_{j=0}^{n}\binom{n}{j}a_m(j+1)c_{m,r}(n-j)=b_{m-\ell-1}(n+1)+ b_{\ell-1}(n+1) - \frac{a_m(n+2)}{m}.\end{align*}

输出:
\begin{align*} \sum_{j=0}^{n}\binom{n}{j}a_m(j+1)c_{m,r}(n-j)=b_{m-\ell-1}(n+1)+b_{\ell-1}(n+1)-\frac{a_m(n+2)}{m}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 629: TexTeller_0087082
------------------------------------------------------------
输入:
\begin{align*} \kappa_{\beta}= \begin{cases} |\beta|-1 &\ \ \beta\neq [1,2,1,2,2]\\ |\beta|+1 &\ \ \beta=[1,2,1,2,2] \end{cases}.\end{align*}

输出:
\begin{align*} \kappa_{\beta}=\begin{cases} |\beta|-1 &\ \ \beta\neq[1,2,1,2,2]\|\beta|+1 &\ \ \beta=[1,2,1,2,2] \end{cases}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 630: TexTeller_0088929
------------------------------------------------------------
输入:
\begin{align*}G_t^p(x,y,N)&=\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=1\big)}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=0\big)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\\&\geq\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{p}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{1-f(T,p)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\end{align*}

输出:
\begin{align*}G_t^p(x,y,N)&=\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=1\big)}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=0\big)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\\&\geq\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{p}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{1-f(T,p)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\end{align*}

状态: 无变化
================================================================================

样本 631: TexTeller_0090818
------------------------------------------------------------
输入:
\begin{align*} f_a(z)=\cos(a(z-ir))\cos(a(z+ir))=\tfrac{1}{2}\big(\cos(2az)+\cosh(2ar)\big).\end{align*}

输出:
\begin{align*} f_a(z)=\cos(a(z-ir))\cos(a(z+ir))=\tfrac{1}{2}\big(\cos(2az)+\cosh(2ar)\big).\end{align*}

状态: 无变化
================================================================================

样本 632: TexTeller_0092705
------------------------------------------------------------
输入:
\begin{align*}{\cal E}_h(u,v)=\int_a^bu'(t)\overline{v(t)}dt-\int_a^bh(t)\{u(t)\overline{v(t)}\}'dt\ ,\end{align*}

输出:
\begin{align*}{\cal E}_h(u,v)=\int_a^bu'(t)\overline{v(t)}dt-\int_a^bh(t)\{u(t)\overline{v(t)}\}'dt,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 633: TexTeller_0094583
------------------------------------------------------------
输入:
\begin{gather*}\theta_{p}^0 = dp^0, \theta_{p}^i = d p^i, \quad\theta_x = dx, \theta_y^0 = dy^0 - p^0 dx, \theta_y^i = dy^i - p^i dx, \\ \theta_{z}^i = dz^i - p^0 dy^i - p^i dy^0 + p^0 p^i dx.\end{gather*}

输出:
\begin{gather*}\theta_{p}^0=dp^0,\theta_{p}^i=d p^i,\quad\theta_x=dx,\theta_y^0=dy^0-p^0 dx,\theta_y^i=dy^i-p^i dx,\\ \theta_{z}^i=dz^i-p^0 dy^i-p^i dy^0+p^0 p^i dx.\end{gather*}

状态: 已规范化 ✓
================================================================================

样本 634: TexTeller_0096486
------------------------------------------------------------
输入:
\begin{align*}W^\lambda= \{w\in W\,|\,w(\Pi_\lambda)\subset\Delta^+\}\end{align*}

输出:
\begin{align*}W^\lambda=\{w\in W\,|\,w(\Pi_\lambda)\subset\Delta^+\}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 635: TexTeller_0098343
------------------------------------------------------------
输入:
\begin{align*} \tilde{C}_{ij}(\alpha)= \sum_{k=1}^D\theta_{k 1} f_{\alpha - e_i - e_j - e_k} + (1-\delta_{|\alpha|,M})( \alpha_1 + 1) f_{\alpha-e_i-e_j+e_1}.\end{align*}

输出:
\begin{align*} \tilde{C}_{ij}(\alpha)=\sum_{k=1}^D\theta_{k 1} f_{\alpha-e_i-e_j-e_k}+(1-\delta_{|\alpha|,M})(\alpha_1+1) f_{\alpha-e_i-e_j+e_1}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 636: TexTeller_0100216
------------------------------------------------------------
输入:
\begin{align*} ||F_{6}(t,r)||_{L^{2}(r dr)}^{2} &\leq C \int_{\frac{t}{4}}^{\frac{t}{2}} \frac{\lambda(t)^{4} r (v_{4}(t,r)^{2}+v_{5}(t,r)^{2})}{r^{8}} dr + C \int_{\frac{t}{2}}^{\infty} \frac{\lambda(t)^{4} (v_{4}^{2}+v_{5}^{2}) r dr}{r^{8}}\\&\leq \frac{C}{t^{8} \log^{10b+4N-2}(t)} \end{align*}

输出:
\begin{align*} ||F_{6}(t,r)||_{L^{2}(r dr)}^{2} &\leq C \int_{\frac{t}{4}}^{\frac{t}{2}} \frac{\lambda(t)^{4} r(v_{4}(t,r)^{2}+v_{5}(t,r)^{2})}{r^{8}} dr+C \int_{\frac{t}{2}}^{\infty} \frac{\lambda(t)^{4}(v_{4}^{2}+v_{5}^{2}) r dr}{r^{8}}\\&\leq \frac{C}{t^{8} \log^{10b+4N-2}(t)} \end{align*}

状态: 已规范化 ✓
================================================================================

样本 637: TexTeller_0102111
------------------------------------------------------------
输入:
\begin{align*}\sum_{\mathsf{m=1}}^{+\infty }\sum_{\mathsf{n=1}}^{+\infty }r_{\mathsf{m,n}}^{2}=Z_{\left( \alpha -1\right) \beta }Z_{\left( \alpha +1\right) \beta}<+\infty \end{align*}

输出:
\begin{align*}\sum_{\mathsf{m=1}}^{+\infty}\sum_{\mathsf{n=1}}^{+\infty}r_{\mathsf{m,n}}^{2}=Z_{\left(\alpha-1\right) \beta}Z_{\left(\alpha+1\right) \beta}<+\infty \end{align*}

状态: 已规范化 ✓
================================================================================

样本 638: TexTeller_0103997
------------------------------------------------------------
输入:
\begin{align*} \mathcal{W}^p(M) := \int_M H^p \, dS, p\geq 1,\end{align*}

输出:
\begin{align*} \mathcal{W}^p(M):=\int_M H^p \,dS,p\geq 1,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 639: TexTeller_0105869
------------------------------------------------------------
输入:
\begin{align*}g_2(a,b)&=-g_1(b^{-1},b)+ g_1(b^{-1},b)g_1(a, a^{-1})\\&=-g_1(a,b)+ g_1(a,b)g_1(a, a^{-1})\\&=-g_1(a,b)+ g_1(a,a^{-1}b), \end{align*}

输出:
\begin{align*}g_2(a,b)&=-g_1(b^{-1},b)+g_1(b^{-1},b)g_1(a,a^{-1})\\&=-g_1(a,b)+g_1(a,b)g_1(a,a^{-1})\\&=-g_1(a,b)+g_1(a,a^{-1}b),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 640: TexTeller_0107761
------------------------------------------------------------
输入:
\begin{align*} \gamma_\textnormal{eq}\left( \textnormal{confluence}\left(J^{K\textnormal{th},\textnormal{eq}}\right)(z,Q) \right) = J^{\textnormal{coh},\textnormal{eq}}(z,Q) \end{align*}

输出:
\begin{align*} \gamma_\textnormal{eq}\left(\textnormal{confluence}\left(J^{K\textnormal{th},\textnormal{eq}}\right)(z,Q) \right)=J^{\textnormal{coh},\textnormal{eq}}(z,Q) \end{align*}

状态: 已规范化 ✓
================================================================================

样本 641: TexTeller_0109621
------------------------------------------------------------
输入:
\begin{align*} \frac{1}{n}\max_{x_2^n} C(\mathcal G_{x_2^n}) &\geq \log\max_{x_2^n} \mathsf{LP}(\mathcal G_{x_2^n})^{1/n}\\&= \log\max_{x_2^n} \prod_{i=1}^{n} \mathsf{LP}(\mathcal G_{x_{2i}})^{1/n}\\&= \log \max_{x_2}\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\log\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\bar{C}(\mathcal G_{x_{2}}).\end{align*}

输出:
\begin{align*} \frac{1}{n}\max_{x_2^n} C(\mathcal G_{x_2^n}) &\geq \log\max_{x_2^n} \mathsf{LP}(\mathcal G_{x_2^n})^{1/n}\\&=\log\max_{x_2^n} \prod_{i=1}^{n} \mathsf{LP}(\mathcal G_{x_{2i}})^{1/n}\\&=\mathrm{log} \max_{x_2}\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\log\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\bar{C}(\mathcal G_{x_{2}}).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 642: TexTeller_0111506
------------------------------------------------------------
输入:
\begin{align*}N(Q(u)\cdot x)=-u\frac{\partial}{\partial u}(Q(u))x+ Q(u) N(x)\end{align*}

输出:
\begin{align*}N(Q(u)\cdot x)=-u\frac{\partial}{\partial u}(Q(u))x+Q(u) N(x)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 643: TexTeller_0113387
------------------------------------------------------------
输入:
\begin{align*}(M\lambda)_i:=\bigcup_{j=1}^k(M_{ij}\cap\lambda_j),i=1,\ldots,k, \lambda\in {\mathcal{L}_{[n]:k}}.\end{align*}

输出:
\begin{align*}(M\lambda)_i:=\bigcup_{j=1}^k(M_{ij}\cap\lambda_j),i=1,. . .,k,\lambda\in{\mathcal{L}_{[n]:k}}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 644: TexTeller_0115258
------------------------------------------------------------
输入:
\begin{align*} L_{g_0}^1u=-\frac{n-4}{8(n-2)}R_{g_0} \Delta_{g_0}u- \frac{n(n-1)(n-4)}{8}u+\frac{n-4}{4(n-2)}\langle Ric_{g_0},\nabla_{g_0}^2u\rangle_{g_0}\end{align*}

输出:
\begin{align*} L_{g_0}^1u=-\frac{n-4}{8(n-2)}R_{g_0} \Delta_{g_0}u-\frac{n(n-1)(n-4)}{8}u+\frac{n-4}{4(n-2)}\langle Ric_{g_0},\nabla_{g_0}^2u\rangle_{g_0}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 645: TexTeller_0117162
------------------------------------------------------------
输入:
\begin{align*} \delta x^{-1} g(t,u,z) f =h \delta , \end{align*}

输出:
\begin{align*} \delta x^{-1} g(t,u,z) f=h \delta,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 646: TexTeller_0119046
------------------------------------------------------------
输入:
\begin{align*} \widetilde{\mu_j^n}(p)\ =\ \widetilde{\beta_i^n}(p)\ +\ \lambda_k, \ \ \mbox{ for } i,j=1,2,\ldots,\ \ k=0,1, \ldots\end{align*}

输出:
\begin{align*} \widetilde{\mu_j^n}(p)=\ \widetilde{\beta_i^n}(p)+\ \lambda_k,\\text{CONTENTPROTECTED0}i,j=1,2,. . .,\ \ k=0,1,. . .\end{align*}

状态: 已规范化 ✓
================================================================================

样本 647: TexTeller_0120891
------------------------------------------------------------
输入:
\begin{gather*}h^{pq}_{kl}=-h^{pk}h^{ql},\\h^{pq}_{kl,rs}=h^{pr}h^{ks}h^{ql}+h^{pk}h^{qr}h^{ls},\\D_jh^{pq}=h^{pq}_{kl}D_jh_{kl},\\D^2_{ij}h^{pq}=h^{pq}_{kl}D^2_{ij}h_{kl}+h^{pq}_{kl,rs}D_ih_{rs}D_jh_{kl}.\end{gather*}

输出:
\begin{gather*}h^{pq}_{kl}=-h^{pk}h^{ql},\\h^{pq}_{kl,rs}=h^{pr}h^{ks}h^{ql}+h^{pk}h^{qr}h^{ls},\\D_jh^{pq}=h^{pq}_{kl}D_jh_{kl},\\D^2_{ij}h^{pq}=h^{pq}_{kl}D^2_{ij}h_{kl}+h^{pq}_{kl,rs}D_ih_{rs}D_jh_{kl}.\end{gather*}

状态: 无变化
================================================================================

样本 648: TexTeller_0122807
------------------------------------------------------------
输入:
\begin{align*}c_{n}=\left( \int\psi_{+}(x,\mathrm{i}\kappa_{n})^{2}\mathrm{d}x\right)^{-1/2}\end{align*}

输出:
\begin{align*}c_{n}=\left(\int\psi_{+}(x,\mathrm{i}\kappa_{n})^{2}\mathrm{d} x\right)^{-1/2}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 649: TexTeller_0124676
------------------------------------------------------------
输入:
\begin{align*}d'(x,y) :=\begin{cases}\bar{d}(x,y), & \mbox{ if } \bar{d}(x,y) <1 \,,\\ 1, &\mbox{ otherwise }.\end{cases}\end{align*}

输出:
\begin{align*}d'(x,y):=\begin{cases}\bar{d}(x,y),&\text{CONTENTPROTECTED0}\bar{d}(x,y)<1 \,,\1,&\text{CONTENTPROTECTED1}.\end{cases}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 650: TexTeller_0126559
------------------------------------------------------------
输入:
\begin{align*} \mathbf{P}(A) = \mathbf{P}(\theta_{\tau} A).\end{align*}

输出:
\begin{align*} \mathbf{P}(A)=\mathbf{P}(\theta_{\tau} A).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 651: TexTeller_0128431
------------------------------------------------------------
输入:
\begin{align*}|y|^2=|\zeta_0+et|^2=1+2t(\zeta_0, e)+t^2\geqslant1-2t+t^2=(1-t)^2\geqslant 1/4\,.\end{align*}

输出:
\begin{align*}|y|^2=|\zeta_0+et|^2=1+2t(\zeta_0,e)+t^2\geqslant1-2t+t^2=(1-t)^2\geqslant 1/4\,.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 652: TexTeller_0130307
------------------------------------------------------------
输入:
\begin{align*}{}~~~~~~~~~~~~~~~~~~~~~~~~~~~\times W'(a_4|c_2dc_3|a_2b_3a_1|c_5)\stackrel{-}{W}(d|a_1a_3a_2|c_4c_5c_6|b_4)\end{align*}

输出:
\begin{align*}~~~~~~~~~~~~~~~~~~~~~~~~~~~\times W'(a_4|c_2dc_3|a_2b_3a_1|c_5)\stackrel{-}{W}(d|a_1a_3a_2|c_4c_5c_6|b_4)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 653: TexTeller_0132211
------------------------------------------------------------
输入:
\begin{align*}i\rho^{{\mbox{\scriptsize ren}}} = i\rho_0 - f_1^{{\mbox{\scriptsize ren}}}\end{align*}

输出:
\begin{align*}i\rho^{{\text{CONTENTPROTECTED0}}}=i\rho_0-f_1^{{\text{CONTENTPROTECTED1}}}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 654: TexTeller_0134085
------------------------------------------------------------
输入:
\begin{align*}\langle f|\exp[-i\hat{H}(t_{f}-t_{i})]|i\rangle\end{align*}

输出:
\begin{align*}\langle f|\exp[-i\hat{H}(t_{f}-t_{i})]|i\rangle\end{align*}

状态: 无变化
================================================================================

样本 655: TexTeller_0135974
------------------------------------------------------------
输入:
\begin{align*}{\mbox{e}}^{2\phi} = \sqrt{2} |P| \, {\mbox{e}}^{2\phi_0} \,\mbox{exp}(\frac{w}{\sqrt{2} P}) \ .\end{align*}

输出:
\begin{align*}{\text{CONTENTPROTECTED0}}^{2\phi}=\sqrt{2} |P| \,{\text{CONTENTPROTECTED1}}^{2\phi_0} \,\text{CONTENTPROTECTED2}(\frac{w}{\sqrt{2} P}).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 656: TexTeller_0137851
------------------------------------------------------------
输入:
\begin{align*}M\geq \sqrt {Q^2 + P^2}= |z_1|\ ,\end{align*}

输出:
\begin{align*}M\geq \sqrt{{Q^2}+P^2}=|z_1|,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 657: TexTeller_0139719
------------------------------------------------------------
输入:
\begin{align*}S_{\rm grav}=2M^{3}r_c\int d^{4}x\int dy\ln^{2/3}T^2\bar{R} .\end{align*}

输出:
\begin{align*}S_{\rm grav}=2M^{3}r_c\int d^{4}x\int dy\ln^{2/3}T^2\bar{R}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 658: TexTeller_0141610
------------------------------------------------------------
输入:
\begin{align*}{\cal L}=\partial_+\phi\partial_-\phi-m^2\phi^2/2\,.\end{align*}

输出:
\begin{align*}{\cal L}=\partial_+\phi\partial_-\phi-m^2\phi^2/2\,.\end{align*}

状态: 无变化
================================================================================

样本 659: TexTeller_0143490
------------------------------------------------------------
输入:
\begin{align*}{e_t}^0= \frac p r - A_t.\end{align*}

输出:
\begin{align*}{e_t}^0=\frac{p}{r}-A_t.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 660: TexTeller_0145362
------------------------------------------------------------
输入:
\begin{align*}E= {1 \over 2G g^{1/2}(L)}\int_{x_+}^{L}(G^0_0+T^0_0)dx+{1 \over 12\pi\beta_H g^{1/2}(L)}+E_{surf} ,\end{align*}

输出:
\begin{align*}E={1 \over 2G g^{1/2}(L)}\int_{x_+}^{L}(G^0_0+T^0_0)dx+{1 \over 12\pi\beta_H g^{1/2}(L)}+E_{surf},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 661: TexTeller_0147231
------------------------------------------------------------
输入:
\begin{align*}H^{(1)} = \int d^2x \left[ m \Phi^0 \partial_i A^i + \frac{m^2}{\sqrt{\kappa}} \epsilon_{ij} \Phi^i A^j + \sqrt{\kappa} \Phi^i \partial_i A^0 - \Phi^3 ( m A^0 - \frac{\kappa}{m} \epsilon_{ij} \partial^i A^j ) \right].\end{align*}

输出:
\begin{align*}H^{(1)}=\int d^2x \left[m \Phi^0 \partial_i A^i+\frac{m^2}{\sqrt{\kappa}} \epsilon_{ij} \Phi^i A^j+\sqrt{\kappa} \Phi^i \partial_i A^0-\Phi^3(m A^0-\frac{\kappa}{m} \epsilon_{ij} \partial^i A^j) \right].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 662: TexTeller_0149141
------------------------------------------------------------
输入:
\begin{align*}(\Psi_1,\Psi_2)=\int_{-\infty}^{+\infty}\Psi_1^*(x)\Psi_2(x)dx,\end{align*}

输出:
\begin{align*}(\Psi_1,\Psi_2)=\int_{-\infty}^{+\infty}\Psi_1^*(x)\Psi_2(x)dx,\end{align*}

状态: 无变化
================================================================================

样本 663: TexTeller_0151026
------------------------------------------------------------
输入:
\begin{align*}\mathbb P\left(\overline{X}_{e(q)}\in dz\right)=\sum_{k=1}^{M}C^q_{k}e^{-\beta_{k,q}z}dz,\end{align*}

输出:
\begin{align*}\mathbb P\left(\overline{X}_{e(q)}\in dz\right)=\sum_{k=1}^{M}C^q_{k}e^{-\beta_{k,q}z}dz,\end{align*}

状态: 无变化
================================================================================

样本 664: TexTeller_0152888
------------------------------------------------------------
输入:
\begin{align*} \lim_{q\to 1}(1-q)^{-n}P_n\bigl((1-q)\eta;q^{\alpha};q\bigr) =n!\,L^{(\alpha)}_n(\eta).\end{align*}

输出:
\begin{align*} \lim_{q\to 1}(1-q)^{-n}P_n\bigl((1-q)\eta;q^{\alpha};q\bigr)=n!\,L^{(\alpha)}_n(\eta).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 665: TexTeller_0154768
------------------------------------------------------------
输入:
\begin{align*}\nu = \sup\{\kappa_x \mid \exists \alpha \in A \exists p \in G [a^p(\alpha) = x]\}.\end{align*}

输出:
\begin{align*}\nu=\sup\{\kappa_x \mid \exists \alpha \in A \exists p \in G[a^p(\alpha)=x]\}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 666: TexTeller_0156661
------------------------------------------------------------
输入:
\begin{align*} X^{(1)}_{m,h}(n;\mu)&=\begin{cases}1 & m\,|\,n \ \ \ \ h=\frac{n}{m}, \\0 & .\end{cases}\end{align*}

输出:
\begin{align*} X^{(1)}_{m,h}(n;\mu)&=\begin{cases}1 & m\,|\,n \ \ \ \ h=\frac{n}{m},\\0 &.\end{cases}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 667: TexTeller_0158527
------------------------------------------------------------
输入:
\begin{align*}\theta^{k-l}(is)\theta^l(st)\theta^{k-l}(is)&=\theta^k(it)\\(jt)\theta^k(it)(jt)&=\theta^k(ij),\end{align*}

输出:
\begin{align*}\theta^{k-l}(is)\theta^l(st)\theta^{k-l}(is)&=\theta^k(it)\\(jt)\theta^k(it)(jt)&=\theta^k(ij),\end{align*}

状态: 无变化
================================================================================

样本 668: TexTeller_0160411
------------------------------------------------------------
输入:
\begin{align*}\beta_{s,a^1}^1=\frac{\left[r^1(s,a^1,a_s^2)-r^1(s,a_s^1,a_s^2)\right]}{\left[r^1(s,a^1,a_{s}^2)-\sum_{s'\in S}p(s'|s,a^1,a_s^2)r^1(s',a_{s'}^1,a_{s'}^2)\right]}.\end{align*}

输出:
\begin{align*}\beta_{s,a^1}^1=\frac{\left[r^1(s,a^1,a_s^2)-r^1(s,a_s^1,a_s^2)\right]}{\left[r^1(s,a^1,a_{s}^2)-\sum_{s'\in S}p(s'|s,a^1,a_s^2)r^1(s',a_{s'}^1,a_{s'}^2)\right]}.\end{align*}

状态: 无变化
================================================================================

样本 669: TexTeller_0162241
------------------------------------------------------------
输入:
\begin{align*}M^k = \bigcup_{\underline{k}: \sum ik_i=k} M^k_{\underline{k}},\end{align*}

输出:
\begin{align*}M^k=\bigcup_{\underline{k}:\sum ik_i=k} M^k_{\underline{k}},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 670: TexTeller_0164176
------------------------------------------------------------
输入:
\begin{align*}\Phi_{1}(x) = \frac{4}{pq}x+O(x^{2})\end{align*}

输出:
\begin{align*}\Phi_{1}(x)=\frac{4}{pq}x+O(x^{2})\end{align*}

状态: 已规范化 ✓
================================================================================

样本 671: TexTeller_0166048
------------------------------------------------------------
输入:
\begin{align*}&F_{\varepsilon,\delta}(t,x,w,p,X)=\min\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\\&F^{\varepsilon,\delta}(t,x,w,p,X)=\max\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\end{align*}

输出:
\begin{align*}&F_{\varepsilon,\delta}(t,x,w,p,X)=\min\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\\&F^{\varepsilon,\delta}(t,x,w,p,X)=\max\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\end{align*}

状态: 无变化
================================================================================

样本 672: TexTeller_0167946
------------------------------------------------------------
输入:
\begin{align*}T(\sigma \cdot \sigma')(\alpha) = T(\sigma')\circ T(\sigma)(\alpha).\end{align*}

输出:
\begin{align*}T(\sigma \cdot \sigma')(\alpha)=T(\sigma')\circ T(\sigma)(\alpha).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 673: TexTeller_0169817
------------------------------------------------------------
输入:
\begin{align*}\dim(\Sigma(s,\theta)\cap \Xi(e_1,e_2)) = 1.\end{align*}

输出:
\begin{align*}\dim(\Sigma(s,\theta)\cap \Xi(e_1,e_2))=1.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 674: TexTeller_0171699
------------------------------------------------------------
输入:
\begin{align*} [(M,K,\xi);D]=[(M,K,\xi);D_h] \end{align*}

输出:
\begin{align*}[(M,K,\xi);D]=[(M,K,\xi);D_h] \end{align*}

状态: 已规范化 ✓
================================================================================

样本 675: TexTeller_0173552
------------------------------------------------------------
输入:
\begin{align*}H_n^{(JW)}=\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n-1}\sum_{a,b=1}^2{\alpha}_{a,b,j}\sigma_{j}^{(a)}\sigma_{ j +1 }^{(b)}+\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n}{\alpha}_{3,0,j}\sigma_j^{(3)}\end{align*}

输出:
\begin{align*}H_n^{(JW)}=\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n-1}\sum_{a,b=1}^2{\alpha}_{a,b,j}\sigma_{j}^{(a)}\sigma_{j+1}^{(b)}+\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n}{\alpha}_{3,0,j}\sigma_j^{(3)}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 676: TexTeller_0175455
------------------------------------------------------------
输入:
\begin{align*}|\{\mu| \mu \in D, h_{1,1}(\mu)=r\}| = |\{\mu| \mu \in D, a_2(\mu)=r\}|\end{align*}

输出:
\begin{align*}|\{\mu| \mu \in D,h_{1,1}(\mu)=r\}|=|\{\mu| \mu \in D,a_2(\mu)=r\}|\end{align*}

状态: 已规范化 ✓
================================================================================

样本 677: TexTeller_0177343
------------------------------------------------------------
输入:
\begin{align*}x\star_{1}^{n} x-y\star_{1}^{n} y=\frac{1}{3}\sqrt{\frac{15}{2}}\frac{\mu_{0}\mu_{2}}{\mu_{1}^{2}}(x^{2}-y^{2}),\end{align*}

输出:
\begin{align*}x\star_{1}^{n} x-y\star_{1}^{n} y=\frac{1}{3}\sqrt{\frac{15}{2}}\frac{\mu_{0}\mu_{2}}{\mu_{1}^{2}}(x^{2}-y^{2}),\end{align*}

状态: 无变化
================================================================================

样本 678: TexTeller_0179204
------------------------------------------------------------
输入:
\begin{align*} \hat f(p_k, q_k) := \frac 1{(2\pi)^2} \int d^2 x_k d^2y_k e^{-i(x_k \cdot p_k +y_k \cdot q_k)} f(x_k, y_k) . \end{align*}

输出:
\begin{align*} \hat{f(p_k,} q_k):=\frac{1{(2\pi)^2}}{\int} d^2 x_k d^2y_k e^{-i(x_k \cdot p_k+y_k \cdot q_k)} f(x_k,y_k).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 679: TexTeller_0181116
------------------------------------------------------------
输入:
\begin{align*}\langle v_1,v_1\rangle=3\alpha^2,\quad\langle v_1,v_2\rangle=\frac{1}{3}\langle\alpha^\perp,\alpha^\perp\rangle=\alpha^2,\quad\\\langle v_2,v_2\rangle=\frac{1}{9}(\langle\alpha^\perp,\alpha^\perp\rangle+\langle\beta^\perp,\beta^\perp\rangle)=\frac{\alpha^2+\beta^2}{3}.\end{align*}

输出:
\begin{align*}\langle v_1,v_1\rangle=3\alpha^2,\quad\langle v_1,v_2\rangle=\frac{1}{3}\langle\alpha^\perp,\alpha^\perp\rangle=\alpha^2,\quad\\\langle v_2,v_2\rangle=\frac{1}{9}(\langle\alpha^\perp,\alpha^\perp\rangle+\langle\beta^\perp,\beta^\perp\rangle)=\frac{\alpha^2+\beta^2}{3}.\end{align*}

状态: 无变化
================================================================================

样本 680: TexTeller_0182987
------------------------------------------------------------
输入:
\begin{align*}\overline{\rho}^{(2)}(\widetilde{M}) \doteq \sum_{i =1}^r \overline{\rho}^{(2)}(\widetilde{M_i}).\end{align*}

输出:
\begin{align*}\overline{\rho}^{(2)}(\widetilde{M}) \doteq \sum_{i=1}^r \overline{\rho}^{(2)}(\widetilde{M_i}).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 681: TexTeller_0184849
------------------------------------------------------------
输入:
\begin{align*}\Upsilon_\eta(A) = \bigcup_{i=-1}^{k-1} \bigcup_{\sigma \in X(i) \setminus S_\eta^i(A)} \Gamma^{k+1}(I^\sigma( E(I_\sigma(S_\eta^{i+1}(A)),I_\sigma(S_\eta^{i+1}(A)))))\end{align*}

输出:
\begin{align*}\Upsilon_\eta(A)=\bigcup_{i=-1}^{k-1} \bigcup_{\sigma \in X(i) \setminus S_\eta^i(A)} \Gamma^{k+1}(I^\sigma(E(I_\sigma(S_\eta^{i+1}(A)),I_\sigma(S_\eta^{i+1}(A)))))\end{align*}

状态: 已规范化 ✓
================================================================================

样本 682: TexTeller_0186722
------------------------------------------------------------
输入:
\begin{align*}{\rm adj} \left( A + v u^{\sf T} \right) = {\rm adj}(A) +{\rm adj}(A) u^{\sf T} {\rm adj}(A) v -{\rm adj}(A) v u^{\sf T} {\rm adj}(A) \;.\end{align*}

输出:
\begin{align*}{\rm adj} \left(A+v u^{\sf T} \right)={\rm adj}(A)+{\rm adj}(A) u^{\sf T}{\rm adj}(A) v-{\rm adj}(A) v u^{\sf T}{\rm adj}(A) \;.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 683: TexTeller_0188605
------------------------------------------------------------
输入:
\begin{align*} &\sum^n_{\ell=0}t^{-\ell}\mathrm{STr\,}\alpha_\ell(x)dv_X(x) \\&=\frac{1}{2\pi}\left[\mathrm{Td_b\,}(\nabla^{T^{1,0}X},T^{1,0}X)\wedge\mathrm{ch_b\,}(\nabla^{E},E)\wedge e^{-m\frac{d\omega_0}{2\pi}}\wedge\omega_0\right]_{2n+1}(x).\end{align*}

输出:
\begin{align*} &\sum^n_{\ell=0}t^{-\ell}\mathrm{STr\,}\alpha_\ell(x)dv_X(x) \\&=\frac{1}{2\pi}\left[\mathrm{Td_b\,}(\nabla^{T^{1,0}X},T^{1,0}X)\wedge\mathrm{ch_b\,}(\nabla^{E},E)\wedge e^{-m\frac{d\omega_0}{2\pi}}\wedge\omega_0\right]_{2n+1}(x).\end{align*}

状态: 无变化
================================================================================

样本 684: TexTeller_0190496
------------------------------------------------------------
输入:
\begin{align*}E \hat{V}_{2}(Z\cap A)&= \gamma e^{-\gamma EV_3(K)}w^{(2)}Dc_1^\top EV_{2}(K)+O(a)\end{align*}

输出:
\begin{align*}E \hat{V}_{2}(Z\cap A)&=\gamma e^{-\gamma EV_3(K)}w^{(2)}Dc_1^\top EV_{2}(K)+O(a)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 685: TexTeller_0192400
------------------------------------------------------------
输入:
\begin{align*}\lim _{|z| \rightarrow \infty}z(\Psi^-_{11}(x;z)-e^{-ic_-(x)})=\widehat{\Psi}^-_{11}(x).\end{align*}

输出:
\begin{align*}\lim_{|z| \rightarrow \infty}z(\Psi^-_{11}(x;z)-e^{-ic_-(x)})=\widehat{\Psi}^-_{11}(x).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 686: TexTeller_0194269
------------------------------------------------------------
输入:
\begin{align*}L_\delta \omega_\delta=l_{1,\delta}+l_{2,\delta}+R_\delta(\omega_\delta),\end{align*}

输出:
\begin{align*}L_\delta \omega_\delta=l_{1,\delta}+l_{2,\delta}+R_\delta(\omega_\delta),\end{align*}

状态: 无变化
================================================================================

样本 687: TexTeller_0196154
------------------------------------------------------------
输入:
\begin{align*}\log\det (u_{,ij})=-v_jx^j+u_{,i}\xi^i+c,\end{align*}

输出:
\begin{align*}\log\det(u_{,ij})=-v_jx^j+u_{,i}\xi^i+c,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 688: TexTeller_0198032
------------------------------------------------------------
输入:
\begin{align*}\beta(a + 1) = \begin{cases} 1, & a + 1 \in \mathbb{P},\\ 0, & a + 1 \not \in \mathbb{P}. \end{cases}\end{align*}

输出:
\begin{align*}\beta(a+1)=\begin{cases} 1,& a+1 \in \mathbb{P},\0,& a+1 \not \in \mathbb{P}.\end{cases}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 689: TexTeller_0199912
------------------------------------------------------------
输入:
\begin{align*}\mathcal{T}(x, \textbf{1}) = \log \log x+C(\textbf{1})+o(1), \end{align*}

输出:
\begin{align*}\mathcal{T}(x,\textbf{1})=\mathrm{log} \mathrm{log} x+C(\textbf{1})+o(1),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 690: TexTeller_0201758
------------------------------------------------------------
输入:
\begin{align*} \ P_{n}^{(\alpha,\beta)}(x) =\frac{(\alpha+1)_n}{n!} \sum_{k=0}^{\infty}\frac{(-n)_k(n+\alpha+\beta+1)_k}{k!\, (\alpha+1)_k} \left(\frac{1-x}{2}\right)^k, n=1,2,\dots.\end{align*}

输出:
\begin{align*} \ P_{n}^{(\alpha,\beta)}(x)=\frac{(\alpha+1)_n}{n!} \sum_{k=0}^{\infty}\frac{(-n)_k(n+\alpha+\beta+1)_k}{k!\,(\alpha+1)_k} \left(\frac{1-x}{2}\right)^k,n=1,2,. . ..\end{align*}

状态: 已规范化 ✓
================================================================================

样本 691: TexTeller_0203668
------------------------------------------------------------
输入:
\begin{align*} B_m=\overset{1}{B}_m+\overset{2}{B}_m,\end{align*}

输出:
\begin{align*} B_m=\overset{1}{B}_m+\overset{2}{B}_m,\end{align*}

状态: 无变化
================================================================================

样本 692: TexTeller_0205552
------------------------------------------------------------
输入:
\begin{align*}\lim_{L\to\infty}\frac{1}{2L+1}|\{x\in[-L,L]:\sigma_{t_0}(x)=1\}|\end{align*}

输出:
\begin{align*}\lim_{L\to\infty}\frac{1}{2L+1}|\{x\in[-L,L]:\sigma_{t_0}(x)=1\}|\end{align*}

状态: 无变化
================================================================================

样本 693: TexTeller_0207442
------------------------------------------------------------
输入:
\begin{gather*}{\rm Hilb}\big(6T_n^{!},t\big)=\sum_{k=0}^{n-1} {n \brace n-k } t^{k}.\end{gather*}

输出:
\begin{gather*}{\rm Hilb}\big(6T_n^{!},t\big)=\sum_{k=0}^{n-1}{n \brace n-k} t^{k}.\end{gather*}

状态: 已规范化 ✓
================================================================================

样本 694: TexTeller_0209313
------------------------------------------------------------
输入:
\begin{align*}\begin{pmatrix}\mathbf{f}(t)\\\mathbf{f}(t+\varepsilon)\\\vdots\\\mathbf{f}(t+(N-1)\varepsilon)\end{pmatrix}=\mathbf{D}\begin{pmatrix}\mathbf{u}(t+(N-1)\varepsilon)\\\vdots\\\mathbf{u}(t-N\varepsilon)\end{pmatrix} \end{align*}

输出:
\begin{align*}\begin{pmatrix}\mathbf{f}(t)\\\mathbf{f}(t+\varepsilon)\\\vdots\\\mathbf{f}(t+(N-1)\varepsilon)\end{pmatrix}=\mathbf{D}\begin{pmatrix}\mathbf{u}(t+(N-1)\varepsilon)\\\vdots\\\mathbf{u}(t-N\varepsilon)\end{pmatrix} \end{align*}

状态: 无变化
================================================================================

样本 695: TexTeller_0211180
------------------------------------------------------------
输入:
\begin{align*} \Pi(0;x)h = \lim_{n\to \infty} \nabla_x Y(0;x,n)(I-B)^{1-\theta}Pg[h] = \nabla_x Y(0;x)(I-B)^{1-\theta}[h] \end{align*}

输出:
\begin{align*} \Pi(0;x)h=\lim_{n\to \infty} \nabla_x Y(0;x,n)(I-B)^{1-\theta}Pg[h]=\nabla_x Y(0;x)(I-B)^{1-\theta}[h] \end{align*}

状态: 已规范化 ✓
================================================================================

样本 696: TexTeller_0213073
------------------------------------------------------------
输入:
\begin{align*} d_1 \geq d_2 \geq \dots \geq d_n \geq 0 m_1 \geq m_2 \geq \dots \geq m_r \sum_{i=2}^n d_i \geq m_1 + m_2.\end{align*}

输出:
\begin{align*} d_1 \geq d_2 \geq . . . \geq d_n \geq 0 m_1 \geq m_2 \geq . . . \geq m_r \sum_{i=2}^n d_i \geq m_1+m_2.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 697: TexTeller_0214953
------------------------------------------------------------
输入:
\begin{align*}\tau\le C_2N^2+\sum_{i=0}^K C_3(i+1)^{-2}N^2\le T'.\end{align*}

输出:
\begin{align*}\tau\le C_2N^2+\sum_{i=0}^K C_3(i+1)^{-2}N^2\le T'.\end{align*}

状态: 无变化
================================================================================

样本 698: TexTeller_0216841
------------------------------------------------------------
输入:
\begin{align*}\sum_{i=1}^n \frac{\pi^2- \phi_i^2}{24 \pi \phi_i}=\frac{1}{6} \left(\frac{n-1}{n-2}\right).\end{align*}

输出:
\begin{align*}\sum_{i=1}^n \frac{\pi^2-\phi_i^2}{24 \pi \phi_i}=\frac{1}{6} \left(\frac{n-1}{n-2}\right).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 699: TexTeller_0218717
------------------------------------------------------------
输入:
\begin{align*}0\leq \phi(p_v)=\phi(s_\mu^* s_\mu)=e^{\beta|\mu|}\phi(s_\mu s_\mu^*)\leq e^{\beta|\mu|}\phi(p_{r(\mu)}).\end{align*}

输出:
\begin{align*}0\leq \phi(p_v)=\phi(s_\mu^*s_\mu)=e^{\beta|\mu|}\phi(s_\mu s_\mu^*)\leq e^{\beta|\mu|}\phi(p_{r(\mu)}).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 700: TexTeller_0220599
------------------------------------------------------------
输入:
\begin{align*} V_{r,t}^f=\{ \mathfrak e^f d x^{\kappa_d}\mid (d, \kappa_d) \in \mathcal {D}_{r,t}^f\times \mathbf N_f \}.\end{align*}

输出:
\begin{align*} V_{r,t}^f=\{\mathfrak{e^f} d x^{\kappa_d}\mid(d,\kappa_d) \in \mathcal{{D}_{r,t}^f\times} \mathbf{N_f} \}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 701: TexTeller_0222469
------------------------------------------------------------
输入:
\begin{align*}B = \epsilon_{ij} \partial_{i} A^{j} = -\frac{1}{2e} {\nabla}^2 \ln \rho.\end{align*}

输出:
\begin{align*}B=\epsilon_{ij} \partial_{i} A^{j}=-\frac{1}{2e}{\nabla}^2 \mathrm{ln} \rho.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 702: TexTeller_0224361
------------------------------------------------------------
输入:
\begin{align*} \chi(j) = \left\{ \begin{array}{cl} 1 & {\rm if} \ \ j=2 \\ 2 & {\rm if} \ \ j=1 \end{array} \right. \ .\end{align*}

输出:
\begin{align*} \chi(j)=\left\{\begin{array}{cl} 1 &{\rm if} \ \ j=2 \2 &{\rm if} \ \ j=1 \end{array} \right..\end{align*}

状态: 已规范化 ✓
================================================================================

样本 703: TexTeller_0226241
------------------------------------------------------------
输入:
\begin{align*}b_{\scriptscriptstyle{l-1}}\,a^l=-\pi^{-2}\,(-1)^m\,i^{l}\, e^{i \delta_{\scriptscriptstyle{l}}}\,\Delta_{\scriptscriptstyle{l}}\,( 1 + \mbox{O} (\Delta_{\scriptscriptstyle{l}}^2) )\,.\end{align*}

输出:
\begin{align*}b_{\scriptscriptstyle{l-1}}\,a^l=-\pi^{-2}\,(-1)^m\,i^{l}\,e^{i \delta_{\scriptscriptstyle{l}}}\,\Delta_{\scriptscriptstyle{l}}\,(1+\text{CONTENTPROTECTED0}(\Delta_{\scriptscriptstyle{l}}^2))\,.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 704: TexTeller_0228113
------------------------------------------------------------
输入:
\begin{align*}\Phi(x)\rightarrow \Phi'(x')=\lambda^{-\Delta}\Phi(x) \,,\end{align*}

输出:
\begin{align*}\Phi(x)\rightarrow \Phi'(x')=\lambda^{-\Delta}\Phi(x) \,,\end{align*}

状态: 无变化
================================================================================

样本 705: TexTeller_0229999
------------------------------------------------------------
输入:
\begin{align*}X^{(I)}(0,\tau_0) = X^{(I)}(\sigma_0, \tau_0) = X^{(I)}(2\pi,\tau_0)\end{align*}

输出:
\begin{align*}X^{(I)}(0,\tau_0)=X^{(I)}(\sigma_0,\tau_0)=X^{(I)}(2\pi,\tau_0)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 706: TexTeller_0231885
------------------------------------------------------------
输入:
\begin{align*}\left. \frac{d V(T)}{d T} \right|_{T=0} = 0.\end{align*}

输出:
\begin{align*}\left.\frac{d V(T)}{d T} \right|_{T=0}=0.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 707: TexTeller_0233775
------------------------------------------------------------
输入:
\begin{align*}K^{H^3}_t(\sigma)=\frac{e^{-ta^2}e^{-\sigma^2/4t}}{(4\pi t)^{3/2}}\:,\end{align*}

输出:
\begin{align*}K^{H^3}_t(\sigma)=\frac{e^{-ta^2}e^{-\sigma^2/4t}}{(4\pi t)^{3/2}}\:,\end{align*}

状态: 无变化
================================================================================

样本 708: TexTeller_0235635
------------------------------------------------------------
输入:
\begin{align*}\sum_s \phi_s = 0 \, , \qquad n^a_1 \nabla_a \phi_1 = n^a_2 \nabla_a \phi_2 =\cdots = n^a_n \nabla_a \phi_n \, . \end{align*}

输出:
\begin{align*}\sum_s \phi_s=0 \,,\qquad n^a_1 \nabla_a \phi_1=n^a_2 \nabla_a \phi_2=. . .=n^a_n \nabla_a \phi_n \,.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 709: TexTeller_0237522
------------------------------------------------------------
输入:
\begin{align*}[T_a,T_b]= \varepsilon_{abc}T^c\; ,\end{align*}

输出:
\begin{align*}[T_a,T_b]=\varepsilon_{abc}T^c\;,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 710: TexTeller_0239392
------------------------------------------------------------
输入:
\begin{align*}dl^2= \overline g_{\, mn} \, d\sigma^m\, d\sigma^n= -e^2(\, \tau\, )\,\, d\tau^2 +h_{ij}(\, \vec s\, )\, \, ds^i\, \, ds^j\end{align*}

输出:
\begin{align*}dl^2=\overline{g_{\,} mn} \,d\sigma^m\,d\sigma^n=-e^2(\,\tau\,)\,\,d\tau^2+h_{ij}(\,\vec{s\,})\,\,ds^i\,\,ds^j\end{align*}

状态: 已规范化 ✓
================================================================================

样本 711: TexTeller_0241267
------------------------------------------------------------
输入:
\begin{align*}E_{n,B}=\frac1{2\pi^2}\int_0^\Lambda\,k^{2n}C(k)_B \coth\frac{\beta W_k}2\,dk\ ,\qquad n\ge1\ .\end{align*}

输出:
\begin{align*}E_{n,B}=\frac1{2\pi^2}\int_0^\Lambda\,k^{2n}C(k)_B \coth\frac{\beta W_k}2\,dk,\qquad n\ge1.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 712: TexTeller_0243166
------------------------------------------------------------
输入:
\begin{align*}T_xT_x^*T_yT_y^*=\begin{cases}T_{x \vee y} T_{x \vee y}^*&\\0&\end{cases}\end{align*}

输出:
\begin{align*}T_xT_x^*T_yT_y^*=\begin{cases}T_{x \vee y} T_{x \vee y}^*&\\0&\end{cases}\end{align*}

状态: 无变化
================================================================================

样本 713: TexTeller_0245031
------------------------------------------------------------
输入:
\begin{align*}&|\partial^\zeta_x(\nabla \phi^2 \cdot Q(v))-\nabla \phi^2 \cdot \partial^\zeta_x Q(v)|_2\\\leq &C(|\nabla \phi|^2_\infty |\nabla v |_2+|\phi|_\infty|\nabla^2 \phi|_2 |\nabla v|_\infty\big)\leq Cc^3_3;\end{align*}

输出:
\begin{align*}&|\partial^\zeta_x(\nabla \phi^2 \cdot Q(v))-\nabla \phi^2 \cdot \partial^\zeta_x Q(v)|_2\\\leq &C(|\nabla \phi|^2_\infty |\nabla v |_2+|\phi|_\infty|\nabla^2 \phi|_2 |\nabla v|_\infty\big)\leq Cc^3_3;\end{align*}

状态: 无变化
================================================================================

样本 714: TexTeller_0246923
------------------------------------------------------------
输入:
\begin{align*}J = I \cap R_{N-1}.\end{align*}

输出:
\begin{align*}J=I \cap R_{N-1}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 715: TexTeller_0248793
------------------------------------------------------------
输入:
\begin{align*} \rho(p,lr,ld+\delta)=l^2 r(d-r)-l(gr+r-d)+\delta \geq -lr(lr+2),\end{align*}

输出:
\begin{align*} \rho(p,lr,ld+\delta)=l^2 r(d-r)-l(gr+r-d)+\delta \geq-lr(lr+2),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 716: TexTeller_0250670
------------------------------------------------------------
输入:
\begin{align*}h(X^j_t,\bar{X}^j_t) = I_1 + I_2' + I_2'' + I_3 + I_4 + I_5 + \int_0^t \left( \frac{\partial h}{\partial x}b_2(X^j_s) + \frac{\partial h}{\partial y}b_2(\bar{X}^j_s)\right)dW^j_s. \end{align*}

输出:
\begin{align*}h(X^j_t,\bar{X}^j_t)=I_1+I_2'+I_2''+I_3+I_4+I_5+\int_0^t \left(\frac{\partial h}{\partial x}b_2(X^j_s)+\frac{\partial h}{\partial y}b_2(\bar{X}^j_s)\right)dW^j_s.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 717: TexTeller_0252571
------------------------------------------------------------
输入:
\begin{align*}\delta(P) = \liminf_{h\to\infty} \delta(P; Z),\end{align*}

输出:
\begin{align*}\delta(P)=\liminf_{h\to\infty} \delta(P;Z),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 718: TexTeller_0254451
------------------------------------------------------------
输入:
\begin{align*}\hat{c}:=\left\{\begin{aligned}&\lambda^c,& e\subset\partial K^{in},\\&c,& e\subset\partial K^{out}.\\\end{aligned}\right.\end{align*}

输出:
\begin{align*}\hat{c}:=\left\{\begin{aligned}&\lambda^c,& e\subset\partial K^{in},\\&c,& e\subset\partial K^{out}.\\\end{aligned}\right.\end{align*}

状态: 无变化
================================================================================

样本 719: TexTeller_0256313
------------------------------------------------------------
输入:
\begin{align*}E^x[g(X_{T_{ab}})]=g(x)+ \int_a^b \frac{(s(x\wedge y)-s(a))(s(b)-s(x\vee y))}{s(b)-s(a)}g(y)\mu_A(dy).\end{align*}

输出:
\begin{align*}E^x[g(X_{T_{ab}})]=g(x)+\int_a^b \frac{(s(x\wedge y)-s(a))(s(b)-s(x\vee y))}{s(b)-s(a)}g(y)\mu_A(dy).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 720: TexTeller_0258190
------------------------------------------------------------
输入:
\begin{align*} A = \left(\prod_{i=1}^n[a_i,c_i]\right) \times [0,a], B= \left(\prod_{i=1}^n[b_i,d_i]\right) \times [0,b]. \end{align*}

输出:
\begin{align*} A=\left(\prod_{i=1}^n[a_i,c_i]\right) \times[0,a],B=\left(\prod_{i=1}^n[b_i,d_i]\right) \times[0,b].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 721: TexTeller_0260081
------------------------------------------------------------
输入:
\begin{align*} \big[ I-(z-b)u_{1,j}^*R_j^*(\bar z)H_{1,j}^{-1}R_j(b)v_j \big] Q_{3,j}^{*}(b)-Q_{3,j}^{*}(z)=0. \end{align*}

输出:
\begin{align*} \big[I-(z-b)u_{1,j}^*R_j^*(\bar z)H_{1,j}^{-1}R_j(b)v_j \big] Q_{3,j}^{*}(b)-Q_{3,j}^{*}(z)=0.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 722: TexTeller_0261969
------------------------------------------------------------
输入:
\begin{align*}Q_C^1 \, Q_C^2 \leq {k^2 \over 4} \,.\end{align*}

输出:
\begin{align*}Q_C^1 \,Q_C^2 \leq{k^2 \over 4} \,.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 723: TexTeller_0263846
------------------------------------------------------------
输入:
\begin{align*}\int _{0} ^{1} dv'(z-v)^{k_{0}.q_{0}}(1-v')^{p_{0}.q_{0}}(v')^{q_{0}.l_{0}-2}\end{align*}

输出:
\begin{align*}\int_{0}^{1} dv'(z-v)^{k_{0}.q_{0}}(1-v')^{p_{0}.q_{0}}(v')^{q_{0}.l_{0}-2}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 724: TexTeller_0265710
------------------------------------------------------------
输入:
\begin{align*}X_d=2ig_L\sum_{\Xi\in\Delta_L}x_d(\Xi\cdot q, \xi)E_d(\Xi),\quad Y_d=ig_L\sum_{\Xi\in\Delta_L}y_d(\Xi\cdot q, \xi)E_d(\Xi),\quad E_d(\Xi)_{\Upsilon \Omega}=\delta_{\Upsilon-\Omega,2\Xi}, \end{align*}

输出:
\begin{align*}X_d=2ig_L\sum_{\Xi\in\Delta_L}x_d(\Xi\cdot q,\xi)E_d(\Xi),\quad Y_d=ig_L\sum_{\Xi\in\Delta_L}y_d(\Xi\cdot q,\xi)E_d(\Xi),\quad E_d(\Xi)_{\Upsilon \Omega}=\delta_{\Upsilon-\Omega,2\Xi},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 725: TexTeller_0267564
------------------------------------------------------------
输入:
\begin{align*}\left<T^\mu{}_\mu(x)\right>={a^{-N}\over 2^{N-1}\pi^{N/2}\Gamma \left(\frac N2\right)} \sum_{n=0}^{\frac N2-1}c^N_{2n+1}\left[ \frac{(-1)^{n+1}}{2(n+1)}4^{-n-1}-2H_n(0)\right].\end{align*}

输出:
\begin{align*}\left<T^\mu_\mu(x)\right>={a^{-N}\over 2^{N-1}\pi^{N/2}\Gamma \left(\frac N2\right)} \sum_{n=0}^{\frac N2-1}c^N_{2n+1}\left[\frac{(-1)^{n+1}}{2(n+1)}4^{-n-1}-2H_n(0)\right].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 726: TexTeller_0269493
------------------------------------------------------------
输入:
\begin{align*}1=\alpha_{1}^{2}=\alpha_{2}^{2}=\alpha_{3}^{2}\,.\end{align*}

输出:
\begin{align*}1=\alpha_{1}^{2}=\alpha_{2}^{2}=\alpha_{3}^{2}\,.\end{align*}

状态: 无变化
================================================================================

样本 727: TexTeller_0271371
------------------------------------------------------------
输入:
\begin{align*}\omega ^2=k^2, \quad k^2 \equiv {{n^2} \over {a^2}} ,\end{align*}

输出:
\begin{align*}\omega^2=k^2,\quad k^2 \equiv{{n^2} \over{a^2}},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 728: TexTeller_0273239
------------------------------------------------------------
输入:
\begin{align*}ds^2=\frac{1}{H^2}[-(dq^0)^2+\sum_{i=1}^{3}(dq^{i})^2],\;\;\;\;\;\;\;\;\eta_{\mu\nu}q^\mu q^\nu=1.\end{align*}

输出:
\begin{align*}ds^2=\frac{1}{H^2}[-(dq^0)^2+\sum_{i=1}^{3}(dq^{i})^2],\;\;\;\;\;\;\;\;\eta_{\mu\nu}q^\mu q^\nu=1.\end{align*}

状态: 无变化
================================================================================

样本 729: TexTeller_0275143
------------------------------------------------------------
输入:
\begin{align*}\phi _{\mathbf{k}}\left( x\right) =A\phi _{\mathbf{k,}BD}\left( x\right)+B\phi _{\mathbf{k,}BD}\left( \overline{x}\right) ,\end{align*}

输出:
\begin{align*}\phi_{\mathbf{k}}\left(x\right)=A\phi_{\mathbf{k,}BD}\left(x\right)+B\phi_{\mathbf{k,}BD}\left(\overline{x}\right),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 730: TexTeller_0277004
------------------------------------------------------------
输入:
\begin{align*}\omega_0(x)^2-g_s\left(\frac{\omega_0(x)}{x}-\omega_0'(x)\right)+f(x)-2\omega_0(x)W'(x) =0, \end{align*}

输出:
\begin{align*}\omega_0(x)^2-g_s\left(\frac{\omega_0(x)}{x}-\omega_0'(x)\right)+f(x)-2\omega_0(x)W'(x)=0,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 731: TexTeller_0278891
------------------------------------------------------------
输入:
\begin{align*}{\cal L} = {\cal L}_0 + {\cal L}_1 + {\cal L}_2.\end{align*}

输出:
\begin{align*}{\cal L}={\cal L}_0+{\cal L}_1+{\cal L}_2.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 732: TexTeller_0280763
------------------------------------------------------------
输入:
\begin{align*}{SO(n\!-\!1,1)\over SO(n\!-\!1)}\ .\qquad (n\geq 3)\end{align*}

输出:
\begin{align*}{SO(n\!-\!1,1)\over SO(n\!-\!1)}.\qquad(n\geq 3)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 733: TexTeller_0282648
------------------------------------------------------------
输入:
\begin{align*} R_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}&=\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}})-\dim(\overline{H}_{\bar{f}|_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}}^{\perp_{\bar{f}}})\\ &=R_f-(l_0-1)-\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}}\bigcap (\overline{H}_{l_0-1}^{\perp_{\bar{f}}})^{\perp_{\bar{f}}})\\ &=R_f-2(l_0-1)\geq 2.\end{align*}

输出:
\begin{align*} R_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}&=\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}})-\dim(\overline{H}_{\bar{f}|_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}}^{\perp_{\bar{f}}})\&=R_f-(l_0-1)-\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}}\bigcap(\overline{H}_{l_0-1}^{\perp_{\bar{f}}})^{\perp_{\bar{f}}})\&=R_f-2(l_0-1)\geq 2.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 734: TexTeller_0284521
------------------------------------------------------------
输入:
\begin{align*}\mathcal{N}(T)&=||\mathbf{F}||^2_{s,\ast,T}+(||\dot{{\mathbf U}}||^2_{W^{2,\infty}_{\ast}(\Omega_T)}+||\varphi||^2_{W^{2,\infty}(\Gamma_T)}+||\mathbf{F}||^2_{W^{1,\infty}_{\ast}(\Omega_T)})||\hat{W}||^2_{s+4,\ast,T}.\end{align*}

输出:
\begin{align*}\mathcal{N}(T)&=||\mathbf{F}||^2_{s,\ast,T}+(||\dot{{\mathbf U}}||^2_{W^{2,\infty}_{\ast}(\Omega_T)}+||\varphi||^2_{W^{2,\infty}(\Gamma_T)}+||\mathbf{F}||^2_{W^{1,\infty}_{\ast}(\Omega_T)})||\hat{W}||^2_{s+4,\ast,T}.\end{align*}

状态: 无变化
================================================================================

样本 735: TexTeller_0286415
------------------------------------------------------------
输入:
\begin{align*}C_{p,q}^{(\nu)}(t) \coloneqq \begin{cases}C e^{-td^\nu} & (t\ge 1) \\ C t^{-\mu_\nu} & (0<t\le 1), \end{cases} \mu_\nu \coloneqq \max \Big\{ \frac{d}{\nu} \Big(\frac{1}{\min\{q,q'\}}-\frac{1}{\max\{p,p'\}} \Big) , 0\Big\},\end{align*}

输出:
\begin{align*}C_{p,q}^{(\nu)}(t) \coloneqq \begin{cases}C e^{-td^\nu} &(t\ge 1) \\ C t^{-\mu_\nu} &(0<t\le 1),\end{cases} \mu_\nu \coloneqq \mathrm{max} \Big\{\frac{d}{\nu} \Big(\frac{1}{\min\{q,q'\}}-\frac{1}{\max\{p,p'\}} \Big),0\Big\},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 736: TexTeller_0288304
------------------------------------------------------------
输入:
\begin{align*}2^{J_i}=2^{\mathfrak{k}_J}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}=2^{k}\prod_{j=1}^{J}(\mathfrak{s}_j)^{\mathfrak{d}_j}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}.\end{align*}

输出:
\begin{align*}2^{J_i}=2^{\mathfrak{k}_J}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}=2^{k}\prod_{j=1}^{J}(\mathfrak{s}_j)^{\mathfrak{d}_j}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}.\end{align*}

状态: 无变化
================================================================================

样本 737: TexTeller_0290162
------------------------------------------------------------
输入:
\begin{align*}\xi u + E_{\lambda}( u_{0}, v_{0} )\leq - \int_{0}^{u} H_{\lambda}(s) \, ds + E_{\lambda}( u_{0}, v_{0} )= \frac{1}{2} u_{t}^2,\end{align*}

输出:
\begin{align*}\xi u+E_{\lambda}(u_{0},v_{0})\leq-\int_{0}^{u} H_{\lambda}(s) \,ds+E_{\lambda}(u_{0},v_{0})=\frac{1}{2} u_{t}^2,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 738: TexTeller_0291849
------------------------------------------------------------
输入:
\begin{align*}\sigma_{g^{-1}}(y^*)_{((i',j'),1_H)} & = (y^*)_{g((i',j'),1_H)} \\& = (y^*)_{((i,j)+\varphi_h(i',j'),h)} \\& = (y^{h^{-1}})_{(i',j')+\varphi_{h^{-1}}(i,j)} \\& = (\sigma_{-\varphi_{h^{-1}}(i,j)}(y^{h^{-1}}))_{(i',j')}.\end{align*}

输出:
\begin{align*}\sigma_{g^{-1}}(y^*)_{((i',j'),1_H)} &=(y^*)_{g((i',j'),1_H)} \\&=(y^*)_{((i,j)+\varphi_h(i',j'),h)} \\&=(y^{h^{-1}})_{(i',j')+\varphi_{h^{-1}}(i,j)} \\&=(\sigma_{-\varphi_{h^{-1}}(i,j)}(y^{h^{-1}}))_{(i',j')}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 739: TexTeller_0293930
------------------------------------------------------------
输入:
\begin{align*}\tilde L_m B_n =\sum_{k=1}^m \left[B_k,B_{m+n-k}\right] + nB_{m+n} n\geq 0, m\geq -1.\end{align*}

输出:
\begin{align*}\tilde L_m B_n=\sum_{k=1}^m \left[B_k,B_{m+n-k}\right]+nB_{m+n} n\geq 0,m\geq-1.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 740: TexTeller_0295820
------------------------------------------------------------
输入:
\begin{align*}x^{\ell_1}_\gamma = x^{\ell_2}_\gamma,\forall \ell_1,\ell_2: \gamma \in \mathcal{I}_{\ell_1}^1\cap \mathcal{I}_{\ell_2}^1.\end{align*}

输出:
\begin{align*}x^{\ell_1}_\gamma=x^{\ell_2}_\gamma,\forall \ell_1,\ell_2:\gamma \in \mathcal{I}_{\ell_1}^1\cap \mathcal{I}_{\ell_2}^1.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 741: TexTeller_0297691
------------------------------------------------------------
输入:
\begin{align*}\Delta(\lambda)\;=\;\Delta_{He^0_\lambda}\;=\; A\otimes_B He^0_\lambda,\qquad\mbox{and}\overline{\Delta}(\lambda)\;=\;\Delta_{L^0(\lambda)}\;=\; A\otimes_B L^0(\lambda).\end{align*}

输出:
\begin{align*}\Delta(\lambda)\;=\;\Delta_{He^0_\lambda}\;=\;A\otimes_B He^0_\lambda,\qquad\text{CONTENTPROTECTED0}\overline{\Delta}(\lambda)\;=\;\Delta_{L^0(\lambda)}\;=\;A\otimes_B L^0(\lambda).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 742: TexTeller_0299572
------------------------------------------------------------
输入:
\begin{align*}\mathcal{A}_4:=\{ (j_1, j_2, j_3, j_4)\in \mathbb{Z}^4\setminus\{\textbf{0}\}\, :\, &j_1+j_2+j_3+j_4=0, j_1^3+j_2^3+j_3^3+j_4^3\neq 0,\\& \mbox{and at most one among}\,\,j_1, j_2, j_3, j_4\,\,\mbox{outside}\,\,S\}.\end{align*}

输出:
\begin{align*}\mathcal{A}_4:=\{(j_1,j_2,j_3,j_4)\in \mathbb{Z}^4\setminus\{\textbf{0}\}\,:\,&j_1+j_2+j_3+j_4=0,j_1^3+j_2^3+j_3^3+j_4^3\neq 0,\\&\text{CONTENTPROTECTED0}\,\,j_1,j_2,j_3,j_4\,\,\text{CONTENTPROTECTED1}\,\,S\}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 743: TexTeller_0301447
------------------------------------------------------------
输入:
\begin{align*} \gamma'(d)=\frac{D}{d}\sum_{(D,w)D\mid cd}\chi(w/(cd,w))(cd,w)\mu(c)c^{-2}.\end{align*}

输出:
\begin{align*} \gamma'(d)=\frac{D}{d}\sum_{(D,w)D\mid cd}\chi(w/(cd,w))(cd,w)\mu(c)c^{-2}.\end{align*}

状态: 无变化
================================================================================

样本 744: TexTeller_0303328
------------------------------------------------------------
输入:
\begin{align*} \frac{T}{4}\int_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon}\log \left(\frac{\sigma^2+T^2}{4}\right)\ d\sigma=\frac{T}{4}\left[\sigma\log \left(\frac{\sigma^2+T^2}{4}\right)-2\sigma+2T\arctan\left(\frac{\sigma}{T}\right)\right]\Biggr|_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon} \end{align*}

输出:
\begin{align*} \frac{T}{4}\int_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon}\log \left(\frac{\sigma^2+T^2}{4}\right)\ d\sigma=\frac{T}{4}\left[\sigma\log \left(\frac{\sigma^2+T^2}{4}\right)-2\sigma+2T\arctan\left(\frac{\sigma}{T}\right)\right]\Biggr|_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon} \end{align*}

状态: 无变化
================================================================================

样本 745: TexTeller_0305228
------------------------------------------------------------
输入:
\begin{align*}\tau_t(a_n\cdots a_1vb_1\cdots b_m)=t\cdot \tau(a_n\cdots a_1\tau'(v)b_1\cdots b_m)+o(t)\end{align*}

输出:
\begin{align*}\tau_t(a_n. . . a_1vb_1. . . b_m)=t\cdot \tau(a_n. . . a_1\tau'(v)b_1. . . b_m)+o(t)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 746: TexTeller_0307102
------------------------------------------------------------
输入:
\begin{align*}G_B(R) = (B \otimes_F R)^\times\end{align*}

输出:
\begin{align*}G_B(R)=(B \otimes_F R)^\times\end{align*}

状态: 已规范化 ✓
================================================================================

样本 747: TexTeller_0308982
------------------------------------------------------------
输入:
\begin{align*}\tilde{m}_n=\begin{cases}m_n-q_{n-1}, \ell_n=1\\m_n+q_{n-1}, \ell_n=-1\end{cases}.\end{align*}

输出:
\begin{align*}\tilde{m}_n=\begin{cases}m_n-q_{n-1},\ell_n=1\\m_n+q_{n-1},\ell_n=-1\end{cases}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 748: TexTeller_0310846
------------------------------------------------------------
输入:
\begin{align*}|\varphi(z)| & \leq C_{\varepsilon}\mathrm{e}^{\varepsilon|z|}\left(\sum_{n=0}^{\infty}(n!)^{2}2^{nq}|\varphi_{n}|^{2}\right)^{1/2}\left(\sum_{n=0}^{\infty}2^{-nq}\sigma_{\varepsilon}^{-2n}\right)^{1/2} \\ & =C_{\varepsilon}(1-2^{-q}\sigma_{\varepsilon}^{-2})^{-1/2}\|\varphi\|_{q,1,\pi_{\lambda,\beta}}\mathrm{e}^{\varepsilon|z|},\end{align*}

输出:
\begin{align*}|\varphi(z)| & \leq C_{\varepsilon}\mathrm{e}^{\varepsilon|z|}\left(\sum_{n=0}^{\infty}(n!)^{2}2^{nq}|\varphi_{n}|^{2}\right)^{1/2}\left(\sum_{n=0}^{\infty}2^{-nq}\sigma_{\varepsilon}^{-2n}\right)^{1/2} \&=C_{\varepsilon}(1-2^{-q}\sigma_{\varepsilon}^{-2})^{-1/2}\|\varphi\|_{q,1,\pi_{\lambda,\beta}}\mathrm{e}^{\varepsilon|z|},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 749: TexTeller_0312721
------------------------------------------------------------
输入:
\begin{align*}a_{n,p,10}= \sum_{s=1}^n \binom{n-1}{s} r_{s,p,10} = \sum_{s=1}^n \binom{n-1}{s-1} \binom{p+s-2}{s-1}=\sum_{s=0}^{n-1} \binom{n-1}{s} \binom{p+s-1}{s}.\end{align*}

输出:
\begin{align*}a_{n,p,10}=\sum_{s=1}^n \binom{n-1}{s} r_{s,p,10}=\sum_{s=1}^n \binom{n-1}{s-1} \binom{p+s-2}{s-1}=\sum_{s=0}^{n-1} \binom{n-1}{s} \binom{p+s-1}{s}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 750: TexTeller_0314622
------------------------------------------------------------
输入:
\begin{align*} \begin{aligned} \Delta A_0 \ = \ J_0, \Delta \partial_t A_0 \ = \ \nabla^i J_i. \end{aligned}\end{align*}

输出:
\begin{align*} \begin{aligned} \Delta A_0=\ J_0,\Delta \partial_t A_0=\ \nabla^i J_i.\end{aligned}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 751: TexTeller_0316503
------------------------------------------------------------
输入:
\begin{align*}\delta (s)=\left( s,\int \left( \int \cosh (bs)\kappa (s)\,ds\right) \,ds,\int \left(\int \sinh (bs)\kappa (s)\,ds\right) \,ds\right).\end{align*}

输出:
\begin{align*}\delta(s)=\left(s,\int \left(\int \mathrm{cosh}(bs)\kappa(s)\,ds\right) \,ds,\int \left(\int \mathrm{sinh}(bs)\kappa(s)\,ds\right) \,ds\right).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 752: TexTeller_0318400
------------------------------------------------------------
输入:
\begin{align*} \tilde X^{(\lambda)}(\infty) = \delta (\Pi_X(U^{(\lambda)}(\infty))-\gamma n).\end{align*}

输出:
\begin{align*} \tilde{X^{(\lambda)}(\infty)}=\delta(\Pi_X(U^{(\lambda)}(\infty))-\gamma n).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 753: TexTeller_0320257
------------------------------------------------------------
输入:
\begin{align*}\begin{multlined}[t][12.5cm]\Psi(x,t,g,\vartheta)=t\psi\left(\tilde{\mu}_{g^{-1}}^{G}\circ\tilde{\mu}_{-\vartheta}^{T}(x),x\right)-\nu_{T}\cdot\vartheta.\end{multlined}\end{align*}

输出:
\begin{align*}\begin{multlined}[t][12.5cm]\Psi(x,t,g,\vartheta)=t\psi\left(\tilde{\mu}_{g^{-1}}^{G}\circ\tilde{\mu}_{-\vartheta}^{T}(x),x\right)-\nu_{T}\cdot\vartheta.\end{multlined}\end{align*}

状态: 无变化
================================================================================

样本 754: TexTeller_0322148
------------------------------------------------------------
输入:
\begin{align*} \lim_{k\to\infty} C^{n_k} \max_{0 \leq j \leq n_k - 1}|\alpha(j) - \alpha(j \pm n_k)|=0\end{align*}

输出:
\begin{align*} \lim_{k\to\infty} C^{n_k} \max_{0 \leq j \leq n_k-1}|\alpha(j)-\alpha(j \pm n_k)|=0\end{align*}

状态: 已规范化 ✓
================================================================================

样本 755: TexTeller_0324014
------------------------------------------------------------
输入:
\begin{align*}\frac{\partial \phi_b}{\partial x}=W_b~\frac{\partial f}{\partial x}+V_b~\frac{\partial g}{\partial x};~~~\frac{\partial \phi_c}{\partial y}=W_c~\frac{\partial f}{\partial y}+V_c~\frac{\partial g}{\partial y}\end{align*}

输出:
\begin{align*}\frac{\partial \phi_b}{\partial x}=W_b~\frac{\partial f}{\partial x}+V_b~\frac{\partial g}{\partial x};~~~\frac{\partial \phi_c}{\partial y}=W_c~\frac{\partial f}{\partial y}+V_c~\frac{\partial g}{\partial y}\end{align*}

状态: 无变化
================================================================================

样本 756: TexTeller_0325895
------------------------------------------------------------
输入:
\begin{align*}(h^**k)(yx^{-1}) = \sum_z \bar h(z^{-1})k(z^{-1}yx^{-1}) = \sum_z \bar h(z^{-1}y^{-1})k(z^{-1}x^{-1}).\end{align*}

输出:
\begin{align*}(h^**k)(yx^{-1})=\sum_z \bar{h(z^{-1})k(z^{-1}yx^{-1})}=\sum_z \bar{h(z^{-1}y^{-1})k(z^{-1}x^{-1}).\end{align*}}

状态: 已规范化 ✓
================================================================================

样本 757: TexTeller_0327787
------------------------------------------------------------
输入:
\begin{align*}\int_{D \setminus D_{t} } \langle A \nabla u_0, \nabla u_0 \rangle - k^2 \int_{D \setminus D_{t} } \Sigma |u_0|^2 - \int_{\partial D_t \setminus \Gamma} A \nabla u_0 \cdot \nu \; \bar u_0 = 0. \end{align*}

输出:
\begin{align*}\int_{D \setminus D_{t}} \langle A \nabla u_0,\nabla u_0 \rangle-k^2 \int_{D \setminus D_{t}} \Sigma |u_0|^2-\int_{\partial D_t \setminus \Gamma} A \nabla u_0 \cdot \nu \;\bar{u_0}=0.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 758: TexTeller_0329663
------------------------------------------------------------
输入:
\begin{align*}V_{\psi}&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\gamma^i(\overline{\nabla}_i\psi)+\psi^2-\frac{\gamma^i(\overline{\nabla}_i\psi)+(\overline{\nabla}_i\psi)\gamma^i}{2}+L^iL_i\\&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\frac{1}{2}[\gamma^i,\overline{\nabla}_i\psi]+\psi^2+L^iL_i.\end{align*}

输出:
\begin{align*}V_{\psi}&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\gamma^i(\overline{\nabla}_i\psi)+\psi^2-\frac{\gamma^i(\overline{\nabla}_i\psi)+(\overline{\nabla}_i\psi)\gamma^i}{2}+L^iL_i\\&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\frac{1}{2}[\gamma^i,\overline{\nabla}_i\psi]+\psi^2+L^iL_i.\end{align*}

状态: 无变化
================================================================================

样本 759: TexTeller_0331543
------------------------------------------------------------
输入:
\begin{align*}\mathcal L(\lambda):&=\sum_{k\geq 0\atop i_1\geq \dots\geq i_k, i_j\in\mathbb Z}\mathbb Ax^-_{i_1}\cdots x^-_{i_k}v_\lambda\subset \mathcal N_q^-v_\lambda=\tilde M_q(\lambda)\end{align*}

输出:
\begin{align*}\mathcal L(\lambda):&=\sum_{k\geq 0\atop i_1\geq . . .\geq i_k,i_j\in\mathbb Z}\mathbb Ax^-_{i_1}. . . x^-_{i_k}v_\lambda\subset \mathcal{N_q^-v_\lambda=\tilde} M_q(\lambda)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 760: TexTeller_0333429
------------------------------------------------------------
输入:
\begin{align*}\frac{\mathbb E_G\gamma_G}{v(G)} =\frac12\int\frac{x^2}{1+x^2}\mathrm d\rho_G(x).\end{align*}

输出:
\begin{align*}\frac{\mathbb E_G\gamma_G}{v(G)}=\frac12\int\frac{x^2}{1+x^2}\mathrm d\rho_G(x).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 761: TexTeller_0335295
------------------------------------------------------------
输入:
\begin{align*}\partial_1 \, Q_k^n(x,y) & = (n-k) \left[P_k^{n-1}(x,y) + k\, P_{k-1}^{n-2}(x,y)\right], \\\partial_2 \, Q_k^n(x,y) & = k \left[P_{k-1}^{n-1}(x,y) + (n-k) \,P_{k-1}^{n-2}(x,y)\right].\end{align*}

输出:
\begin{align*}\partial_1 \,Q_k^n(x,y) &=(n-k) \left[P_k^{n-1}(x,y)+k\,P_{k-1}^{n-2}(x,y)\right],\\\partial_2 \,Q_k^n(x,y) &=k \left[P_{k-1}^{n-1}(x,y)+(n-k) \,P_{k-1}^{n-2}(x,y)\right].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 762: TexTeller_0337195
------------------------------------------------------------
输入:
\begin{align*} k_m=d_1+d_2,\qquad{k_m^S=d_2},\end{align*}

输出:
\begin{align*} k_m=d_1+d_2,\qquad{k_m^S=d_2},\end{align*}

状态: 无变化
================================================================================

样本 763: TexTeller_0339077
------------------------------------------------------------
输入:
\begin{align*}I(1-s,[\rho(d(h))\varphi]')=|\det h|^{-s}I(1-s,\varphi').\end{align*}

输出:
\begin{align*}I(1-s,[\rho(d(h))\varphi]')=|\det h|^{-s}I(1-s,\varphi').\end{align*}

状态: 无变化
================================================================================

样本 764: TexTeller_0340950
------------------------------------------------------------
输入:
\begin{align*}w_{r_k}(y):= \frac{w(r_k y)}{ r_k^{-\frac{n+1}{2}} r_k^{\frac{2s-1}{2}} \left\| y_{n+1}^{\frac{1-2s}{2}} w \right\|_{L^2(B_{r_k}^+)} }.\end{align*}

输出:
\begin{align*}w_{r_k}(y):=\frac{w(r_k y)}{r_k^{-\frac{n+1}{2}} r_k^{\frac{2s-1}{2}} \left\| y_{n+1}^{\frac{1-2s}{2}} w \right\|_{L^2(B_{r_k}^+)}}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 765: TexTeller_0342845
------------------------------------------------------------
输入:
\begin{align*}R^{i}\pi'_{*}(\bigwedge^{p}(\check{\mathcal{E}})\otimes p^{*}(Q))=0\end{align*}

输出:
\begin{align*}R^{i}\pi'_{*}(\bigwedge^{p}(\check{\mathcal{E}})\otimes p^{*}(Q))=0\end{align*}

状态: 无变化
================================================================================

样本 766: TexTeller_0344709
------------------------------------------------------------
输入:
\begin{align*}\hat{\psi}(p,t)=\mathcal{F}\left\{\psi(x,t);p\right\}=\frac{1}{2\pi{}\hslash{}}\int_{-\infty{}}^{\infty{}}e^{-ipx/\hslash{}}\\psi(x,t)\ dx\end{align*}

输出:
\begin{align*}\hat{\psi}(p,t)=\mathcal{F}\left\{\psi(x,t);p\right\}=\frac{1}{2\pi\hslash}\int_{-\infty}^{\infty}e^{-ipx/\hslash}\\psi(x,t)\ dx\end{align*}

状态: 已规范化 ✓
================================================================================

样本 767: TexTeller_0346589
------------------------------------------------------------
输入:
\begin{align*} \mathcal{I}_{2,+}(t,x) = \int_0^t \int_0^{\infty} \int_y^{\infty} \frac{\partial G_{t-s}}{\partial x}(x-z) \psi(s,z)\sigma_s(y) dz W(ds,dy)\end{align*}

输出:
\begin{align*} \mathcal{I}_{2,+}(t,x)=\int_0^t \int_0^{\infty} \int_y^{\infty} \frac{\partial G_{t-s}}{\partial x}(x-z) \psi(s,z)\sigma_s(y) dz W(ds,dy)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 768: TexTeller_0348483
------------------------------------------------------------
输入:
\begin{align*}h_{R_f}=h_K {f \over e_f}\prod_{p|f}\left(1-\left({\Delta\over p}\right){1\over p}\right),\end{align*}

输出:
\begin{align*}h_{R_f}=h_K{f \over e_f}\prod_{p|f}\left(1-\left({\Delta\over p}\right){1\over p}\right),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 769: TexTeller_0350353
------------------------------------------------------------
输入:
\begin{align*} I+C_\Sigma^+ A_-\big(v_A v_B^{-1} - I\big)B_-^{-1} - A_+B_+^{-1} = I+C_\Sigma^- A_-\big(v_A v_B^{-1} - I\big)B_-^{-1} - A_-B_-^{-1}. \end{align*}

输出:
\begin{align*} I+C_\Sigma^+A_-\big(v_A v_B^{-1}-I\big)B_-^{-1}-A_+B_+^{-1}=I+C_\Sigma^-A_-\big(v_A v_B^{-1}-I\big)B_-^{-1}-A_-B_-^{-1}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 770: TexTeller_0352242
------------------------------------------------------------
输入:
\begin{align*} g(\gamma) - C(\gamma) = p(\{n_{i, k}\}, j).\end{align*}

输出:
\begin{align*} g(\gamma)-C(\gamma)=p(\{n_{i,k}\},j).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 771: TexTeller_0354117
------------------------------------------------------------
输入:
\begin{align*}Y = \mu + \sigma A W + \sigma \alpha S + \sigma [B W ]^{\frac{1}{2}} U,\end{align*}

输出:
\begin{align*}Y=\mu+\sigma A W+\sigma \alpha S+\sigma[B W]^{\frac{1}{2}} U,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 772: TexTeller_0355983
------------------------------------------------------------
输入:
\begin{align*}r_2= 2-\beta-\beta_1, p_2=\frac{2}{r_2}=\frac{2}{2-\beta-\beta_1}, q_2= \frac{2}{1-r_2}=\frac{2}{\beta+\beta_1-1}. \end{align*}

输出:
\begin{align*}r_2=2-\beta-\beta_1,p_2=\frac{2}{r_2}=\frac{2}{2-\beta-\beta_1},q_2=\frac{2}{1-r_2}=\frac{2}{\beta+\beta_1-1}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 773: TexTeller_0357861
------------------------------------------------------------
输入:
\begin{align*}&( u_0,u_{t_1}^*u_{t_1},....,u_{t_N}^*u_{t_N},1_{mN}) \\ &=( 1_m,u_{t_1}^*,....,u_{t_N}^*,1_{mN})( u_0,u_{t_1},....,u_{t_N},1_{mN})\end{align*}

输出:
\begin{align*}&(u_0,u_{t_1}^*u_{t_1},....,u_{t_N}^*u_{t_N},1_{mN}) \&=(1_m,u_{t_1}^*,....,u_{t_N}^*,1_{mN})(u_0,u_{t_1},....,u_{t_N},1_{mN})\end{align*}

状态: 已规范化 ✓
================================================================================

样本 774: TexTeller_0359763
------------------------------------------------------------
输入:
\begin{align*} \hat{s}'(g) = 1\end{align*}

输出:
\begin{align*} \hat{s}'(g)=1\end{align*}

状态: 已规范化 ✓
================================================================================

样本 775: TexTeller_0361654
------------------------------------------------------------
输入:
\begin{align*}\hat \Delta \circ \hat \sigma_t = (\hat \tau_{t} \otimes \hat \sigma_{t})\hat \Delta,\end{align*}

输出:
\begin{align*}\hat \Delta \circ \hat{\sigma_t}=(\hat \tau_{t} \otimes \hat{\sigma_{t})\hat} \Delta,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 776: TexTeller_0363517
------------------------------------------------------------
输入:
\begin{align*}2(\Delta u)'(1)u'(1)+(1-\sigma)(1+\sigma)(u'(1))^2=-\dfrac{p+3}{p+1}\dfrac{1}{\pi}\int_B u^{p+1}\end{align*}

输出:
\begin{align*}2(\Delta u)'(1)u'(1)+(1-\sigma)(1+\sigma)(u'(1))^2=-\dfrac{p+3}{p+1}\dfrac{1}{\pi}\int_B u^{p+1}\end{align*}

状态: 无变化
================================================================================

样本 777: TexTeller_0365398
------------------------------------------------------------
输入:
\begin{align*} \tilde{y} &= y = y',\\ \mu_k(\tilde{z})&=\mu_k(z)=\mu_k(z'),\\ \sigma_k(\tilde{z}) &= (\max(s_0,s'_0),\dots,\max(s_r,s'_r)). \end{align*}

输出:
\begin{align*} \tilde{y} &=y=y',\\ \mu_k(\tilde{z})&=\mu_k(z)=\mu_k(z'),\\ \sigma_k(\tilde{z}) &=(\max(s_0,s'_0),. . .,\max(s_r,s'_r)).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 778: TexTeller_0367276
------------------------------------------------------------
输入:
\begin{align*}\| b_\ell \|_X \le (\tilde\lambda+C_Br)\|e_\ell\|_D+2C_B\|u_\ell^\star\|_D \|e_\ell\|_W,\ell=0,\dotsc,M-1 .\end{align*}

输出:
\begin{align*}\| b_\ell \|_X \le(\tilde\lambda+C_Br)\|e_\ell\|_D+2C_B\|u_\ell^\star\|_D \|e_\ell\|_W,\ell=0,. . .c,M-1.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 779: TexTeller_0369173
------------------------------------------------------------
输入:
\begin{align*}C\::\: x^py^q(ax+by+cz)^r-z^{p+q+r}=0,\end{align*}

输出:
\begin{align*}C\::\:x^py^q(ax+by+cz)^r-z^{p+q+r}=0,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 780: TexTeller_0371029
------------------------------------------------------------
输入:
\begin{align*}F\left( 0,x\right) =\frac{1}{x}.\end{align*}

输出:
\begin{align*}F\left(0,x\right)=\frac{1}{x}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 781: TexTeller_0372920
------------------------------------------------------------
输入:
\begin{align*}O_j^{h_1^{-1}}\subseteq(H\cap P_i^{g_j})^{h_1^{-1}}=H^{h_1^{-1}}\cap P_i^{p''g}=H\cap P_i^g\end{align*}

输出:
\begin{align*}O_j^{h_1^{-1}}\subseteq(H\cap P_i^{g_j})^{h_1^{-1}}=H^{h_1^{-1}}\cap P_i^{p''g}=H\cap P_i^g\end{align*}

状态: 无变化
================================================================================

样本 782: TexTeller_0374803
------------------------------------------------------------
输入:
\begin{align*}\Sigma_\omega=\{\sigma_I: \bar{I}\in \mathcal A_\omega\}.\end{align*}

输出:
\begin{align*}\Sigma_\omega=\{\sigma_I:\bar{I}\in \mathcal{A_\omega\}.\end{align*}}

状态: 已规范化 ✓
================================================================================

样本 783: TexTeller_0376681
------------------------------------------------------------
输入:
\begin{align*}K_{(10)}^{(\ell+1)} =K_{(10)}^{(1)}\prod_{\ell'=1}^\ell \chi_{||}^{(\ell')}.\end{align*}

输出:
\begin{align*}K_{(10)}^{(\ell+1)}=K_{(10)}^{(1)}\prod_{\ell'=1}^\ell \chi_{||}^{(\ell')}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 784: TexTeller_0378574
------------------------------------------------------------
输入:
\begin{align*}\sigma_{} (\mathcal{P}) = [0, + \infty).\end{align*}

输出:
\begin{align*}\sigma_(\mathcal{P})=[0,+\infty).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 785: TexTeller_0380401
------------------------------------------------------------
输入:
\begin{align*} \sum_{n \leq x / d^{2} s,\,(n, s) = 1} a(d^{2} n s + k) = \sum_{\delta \mid s}\mu (\delta) \ \ \sum_{ \delta n_{1} \leq x/ d^{2} s} a( d^{2}\, \delta\, n_{1}\, s + k).\end{align*}

输出:
\begin{align*} \sum_{n \leq x/d^{2} s,\,(n,s)=1} a(d^{2} n s+k)=\sum_{\delta \mid s}\mu(\delta) \ \ \sum_{\delta n_{1} \leq x/d^{2} s} a(d^{2}\,\delta\,n_{1}\,s+k).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 786: TexTeller_0382327
------------------------------------------------------------
输入:
\begin{align*}N_J \le 2^{-js(2-\alpha)} 2^{js(1+\alpha)} = 2^{js(2\alpha-1)}.\end{align*}

输出:
\begin{align*}N_J \le 2^{-js(2-\alpha)} 2^{js(1+\alpha)}=2^{js(2\alpha-1)}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 787: TexTeller_0384182
------------------------------------------------------------
输入:
\begin{align*} \sum_{n=1}^Me^{2\pi \textnormal{i}h(n)\alpha}\end{align*}

输出:
\begin{align*} \sum_{n=1}^Me^{2\pi \textnormal{i}h(n)\alpha}\end{align*}

状态: 无变化
================================================================================

样本 788: TexTeller_0386077
------------------------------------------------------------
输入:
\begin{align*}\left\{\begin{array}{ll}-\Delta_p u=\lambda |u|^{p-2}u-g(x,u) & \mbox{in }\Omega,\\\displaystyle{\frac{\partial u}{\partial n_p}}+\beta(x)|u|^{p-2}u=0 & \mbox{on } \partial\Omega,\\\end{array}\right.\end{align*}

输出:
\begin{align*}\left\{\begin{array}{ll}-\Delta_p u=\lambda |u|^{p-2}u-g(x,u) &\text{CONTENTPROTECTED0}\Omega,\\\displaystyle{\frac{\partial u}{\partial n_p}}+\beta(x)|u|^{p-2}u=0 &\text{CONTENTPROTECTED1}\partial\Omega,\\\end{array}\right.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 789: TexTeller_0387971
------------------------------------------------------------
输入:
\begin{align*}(f)(\partial_i,\partial_j) &= \nabla_i \nabla_j f\\&= \partial_i \partial_j f - {\omega^k}_{ij} \partial_k f.\end{align*}

输出:
\begin{align*}(f)(\partial_i,\partial_j) &=\nabla_i \nabla_j f\\&=\partial_i \partial_j f-{\omega^k}_{ij} \partial_k f.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 790: TexTeller_0389855
------------------------------------------------------------
输入:
\begin{align*}\ell_{\lambda}^{O(k+1)}:=\inf\{\tau>0:O(k+1)(PS)_{\tau}\mathcal{J}_{\lambda}\}.\end{align*}

输出:
\begin{align*}\ell_{\lambda}^{O(k+1)}:=\inf\{\tau>0:O(k+1)(PS)_{\tau}\mathcal{J}_{\lambda}\}.\end{align*}

状态: 无变化
================================================================================

样本 791: TexTeller_0391720
------------------------------------------------------------
输入:
\begin{align*}dX_t=-\frac{\sigma\left(e^{X_t}\right)}{2} dt +dW_t+ \frac{\sigma_2-\sigma_1}{\sigma_1+\sigma_2}dL_{t}^{\left(0\right)}(X)=\mu(X_t) dt +dW_t+(p-q)dL_{t}^{\left(0\right)}(X)\end{align*}

输出:
\begin{align*}dX_t=-\frac{\sigma\left(e^{X_t}\right)}{2} dt+dW_t+\frac{\sigma_2-\sigma_1}{\sigma_1+\sigma_2}dL_{t}^{\left(0\right)}(X)=\mu(X_t) dt+dW_t+(p-q)dL_{t}^{\left(0\right)}(X)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 792: TexTeller_0393607
------------------------------------------------------------
输入:
\begin{align*}\tilde{u}_s(a,t) := \hat{u}(a + s \nu(a),t),\forall (a,t) \in \mathcal{M} \times [0,T],\forall s \in [-\delta, \delta].\end{align*}

输出:
\begin{align*}\tilde{u}_s(a,t):=\hat{u}(a+s \nu(a),t),\forall(a,t) \in \mathcal{M} \times[0,T],\forall s \in[-\delta,\delta].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 793: TexTeller_0395473
------------------------------------------------------------
输入:
\begin{align*}\mathbf{x}_{\mathrm{d}}(i) = \sum_{z=1}^{Z}\beta_z\mathbf{a}(\mu^{\mathrm{r}}_{z},\mu^{\mathrm{t}}_{z},\gamma_z,\eta_z)\exp(-j(i-1)\eta_z)\end{align*}

输出:
\begin{align*}\mathbf{x}_{\mathrm{d}}(i)=\sum_{z=1}^{Z}\beta_z\mathbf{a}(\mu^{\mathrm{r}}_{z},\mu^{\mathrm{t}}_{z},\gamma_z,\eta_z)\exp(-j(i-1)\eta_z)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 794: TexTeller_0397370
------------------------------------------------------------
输入:
\begin{align*} \tilde{S}_{Y_p}\cap[N_{Y_p}/2, N_{Y_p}-K]=& \{ \xi r_-+\xi r_+,\; (\xi-1)r_-+(\xi+1)r_+,\dots,\;r_-+(2\xi-1)r_+, \\ &(2\xi+1)r_-,2\xi r_-+r_+,\dots,\; (\xi+3)r_-+(\xi-2)r_+, \; (\xi+2)r_-+(\xi-1)r_+ \}.\end{align*}

输出:
\begin{align*} \tilde{S}_{Y_p}\cap[N_{Y_p}/2,N_{Y_p}-K]=& \{\xi r_-+\xi r_+,\;(\xi-1)r_-+(\xi+1)r_+,. . .,\;r_-+(2\xi-1)r_+,\&(2\xi+1)r_-,2\xi r_-+r_+,. . .,\;(\xi+3)r_-+(\xi-2)r_+,\;(\xi+2)r_-+(\xi-1)r_+\}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 795: TexTeller_0399259
------------------------------------------------------------
输入:
\begin{align*}(Hf)(x)=\sum_{n=1}^{N}(H_{J_{n}^{x}}f)(x). \end{align*}

输出:
\begin{align*}(Hf)(x)=\sum_{n=1}^{N}(H_{J_{n}^{x}}f)(x).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 796: TexTeller_0401136
------------------------------------------------------------
输入:
\begin{align*}J_\mu^2=-|\mu|^2 I\end{align*}

输出:
\begin{align*}J_\mu^2=-|\mu|^2 I\end{align*}

状态: 无变化
================================================================================

样本 797: TexTeller_0402998
------------------------------------------------------------
输入:
\begin{align*}\begin{aligned}\Theta_\gamma (v) = 1 \textrm{ if } 0 \leq \gamma \leq 1 \,, \textrm{ and } \Theta_\gamma (v) = \nu^{-1} (v) \textrm{ if } - 3 < \gamma < 0 \,.\end{aligned}\end{align*}

输出:
\begin{align*}\begin{aligned}\Theta_\gamma(v)=1 \textrm{if} 0 \leq \gamma \leq 1 \,,\textrm{and} \Theta_\gamma(v)=\nu^{-1}(v) \textrm{if}-3<\gamma<0 \,.\end{aligned}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 798: TexTeller_0404887
------------------------------------------------------------
输入:
\begin{align*}\alpha=s+\sum_{j=1}^d \frac{a_j}{\tau_j}-\frac{\nu}{\tau_{}}.\end{align*}

输出:
\begin{align*}\alpha=s+\sum_{j=1}^d \frac{a_j}{\tau_j}-\frac{\nu}{\tau_}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 799: TexTeller_0406779
------------------------------------------------------------
输入:
\begin{align*}(x+6)((x+4)I+A[\mathcal C_i])^{-1} = \frac{1}{q_i(-x-4)} \sum_{k=1}^{\deg m_i} D^k m_i(-x-4) A[\mathcal C_i]^{k-1}\end{align*}

输出:
\begin{align*}(x+6)((x+4)I+A[\mathcal C_i])^{-1}=\frac{1}{q_i(-x-4)} \sum_{k=1}^{\deg m_i} D^k m_i(-x-4) A[\mathcal C_i]^{k-1}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 800: TexTeller_0408648
------------------------------------------------------------
输入:
\begin{align*} \dfrac{1}{\Phi_1[\alpha_0,\beta_0, L_1(0), N_1(0)]}+\dfrac{u_c }{\Phi_2[\beta_0,\infty,L_2(u_c),N_2(u_c)]}=\dfrac{2l_m\gamma_m(\beta_0)^{\nu+1}}{\theta_b-\theta_m}\end{align*}

输出:
\begin{align*} \dfrac{1}{\Phi_1[\alpha_0,\beta_0,L_1(0),N_1(0)]}+\dfrac{u_c}{\Phi_2[\beta_0,\infty,L_2(u_c),N_2(u_c)]}=\dfrac{2l_m\gamma_m(\beta_0)^{\nu+1}}{\theta_b-\theta_m}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 801: TexTeller_0410540
------------------------------------------------------------
输入:
\begin{align*}TM \oplus TM := \{(\xi , \eta) : \xi, \eta \in T_xM ,x \in M \}.\end{align*}

输出:
\begin{align*}TM \oplus TM:=\{(\xi,\eta):\xi,\eta \in T_xM,x \in M \}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 802: TexTeller_0412404
------------------------------------------------------------
输入:
\begin{align*}y_{n-1}^{(l)}|_{\mu=\tfrac{1}{n}}\stackrel{ \rho}{\approx} \tfrac{n!}{(n{-}l)!(l{-}1)!} \displaystyle\sum_{k=0}^{n{-}l} \binom{n{-}l}{k} \tfrac{(-1)^k}{k{+}l}\left(\tfrac{n-l-k}{n}\right)^{n-l}= 1.\end{align*}

输出:
\begin{align*}y_{n-1}^{(l)}|_{\mu=\tfrac{1}{n}}\stackrel{\rho}{\approx} \tfrac{n!}{(n{-}l)!(l{-}1)!} \displaystyle\sum_{k=0}^{n{-}l} \binom{n{-}l}{k} \tfrac{(-1)^k}{k{+}l}\left(\tfrac{n-l-k}{n}\right)^{n-l}=1.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 803: TexTeller_0414302
------------------------------------------------------------
输入:
\begin{align*}\begin{cases}\overline u_t\geq \overline u(t)( a_0(t)-a_1(t)\overline u(t) -a_2(t)\underline v(t))\\\underline v_t\leq \underline v(t)( b_0(t)-b_1(t)\overline u(t) -b_2(t)\underline v(t)).\end{cases}\end{align*}

输出:
\begin{align*}\begin{cases}\overline u_t\geq \overline{u(t)(} a_0(t)-a_1(t)\overline u(t)-a_2(t)\underline v(t))\\\underline v_t\leq \underline{v(t)(} b_0(t)-b_1(t)\overline u(t)-b_2(t)\underline v(t)).\end{cases}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 804: TexTeller_0416164
------------------------------------------------------------
输入:
\begin{align*}\chi (M_n (\pi/2))=&2 \sum_{s=1}^m (-1)^{m+s} \left(s- \big\lfloor s/2 \big\rfloor \right) {n \choose m-s} \\=& 2(-1)^m \sum_{s=1}^m (-1)^s \big\lfloor (s+1)/2 \big\rfloor {2m+1 \choose m-s} \\=& 2 (-1)^{m+1} \sum_{i=0}^{m+1} (-1)^i\big\lfloor i /2\big\rfloor {2m+1 \choose m+i} .\end{align*}

输出:
\begin{align*}\chi(M_n(\pi/2))=&2 \sum_{s=1}^m(-1)^{m+s} \left(s-\big\lfloor s/2 \big\rfloor \right){n \choose m-s} \\=& 2(-1)^m \sum_{s=1}^m(-1)^s \big\lfloor(s+1)/2 \big\rfloor{2m+1 \choose m-s} \\=& 2(-1)^{m+1} \sum_{i=0}^{m+1}(-1)^i\big\lfloor i/2\big\rfloor{2m+1 \choose m+i}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 805: TexTeller_0418036
------------------------------------------------------------
输入:
\begin{align*}\alpha(X(p_m),H_{t_m}^{-1}(X(p_m))) = 0\end{align*}

输出:
\begin{align*}\alpha(X(p_m),H_{t_m}^{-1}(X(p_m)))=0\end{align*}

状态: 已规范化 ✓
================================================================================

样本 806: TexTeller_0419937
------------------------------------------------------------
输入:
\begin{align*}\mathcal{A}_{\cap}^{(N)}=\left\{ A:A=A_{j_{1}}^{(1)}\cap A_{j_{2}}^{(2)}\cap...\cap A_{j_{N}}^{(N)},j_{k}\leqslant m_{k},k\leqslant N\right\}.\end{align*}

输出:
\begin{align*}\mathcal{A}_{\cap}^{(N)}=\left\{A:A=A_{j_{1}}^{(1)}\cap A_{j_{2}}^{(2)}\cap...\cap A_{j_{N}}^{(N)},j_{k}\leqslant m_{k},k\leqslant N\right\}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 807: TexTeller_0421824
------------------------------------------------------------
输入:
\begin{align*} X_i&=\partial_{x_i} +2y_i \partial_t,\ i=1, .., n\\X_{n+j}&= \partial_{y_j} - 2x_j \partial_t,\ j=1, .., n\end{align*}

输出:
\begin{align*} X_i&=\partial_{x_i}+2y_i \partial_t,\ i=1,..,n\\X_{n+j}&=\partial_{y_j}-2x_j \partial_t,\ j=1,..,n\end{align*}

状态: 已规范化 ✓
================================================================================

样本 808: TexTeller_0423717
------------------------------------------------------------
输入:
\begin{align*}T_j(-z):=F(j)\cdot\left(\right).\end{align*}

输出:
\begin{align*}T_j(-z):=F(j)\cdot\left(\right).\end{align*}

状态: 无变化
================================================================================

样本 809: TexTeller_0425571
------------------------------------------------------------
输入:
\begin{align*} B(u_k)&=\int_{\Omega}(|x|^{-\mu}*F(u_k))f^{'}(u_k) u_k^2 ~dx +\int_{\Omega} (|x|^{-\mu}*f(u_k)u_k)f(u_k)u_k ~dx\\ &\leq C(n, \mu) \left(\|f(u_k)u_k\|^2_{L^{2n/(2n-\mu)}(\Omega)} + \|F(u_k)\|_{L^{2n/(2n-\mu)}(\Omega)} \|f^{'}(u_k) (u_k)^2\|_{L^{2n/(2n-\mu)}(\Omega)}\right). \end{align*}

输出:
\begin{align*} B(u_k)&=\int_{\Omega}(|x|^{-\mu}*F(u_k))f^{'}(u_k) u_k^2 ~dx+\int_{\Omega}(|x|^{-\mu}*f(u_k)u_k)f(u_k)u_k ~dx\&\leq C(n,\mu) \left(\|f(u_k)u_k\|^2_{L^{2n/(2n-\mu)}(\Omega)}+\|F(u_k)\|_{L^{2n/(2n-\mu)}(\Omega)} \|f^{'}(u_k)(u_k)^2\|_{L^{2n/(2n-\mu)}(\Omega)}\right).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 810: TexTeller_0427446
------------------------------------------------------------
输入:
\begin{align*}B(t,x; \eta) \ &= \ \big\{(s,y) \in (0,T) \times H: \max \{|x-y|, |t-s|\}< \eta\big\}, \\\partial B(t,x; \eta) \ &= \ \big\{(s,y) \in (0,T) \times H: \max \{|x-y|, |t-s|\}= \eta\big\}.\end{align*}

输出:
\begin{align*}B(t,x;\eta) &=\ \big\{(s,y) \in(0,T) \times H:\mathrm{max} \{|x-y|,|t-s|\}<\eta\big\},\\\partial B(t,x;\eta) &=\ \big\{(s,y) \in(0,T) \times H:\mathrm{max} \{|x-y|,|t-s|\}=\eta\big\}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 811: TexTeller_0429333
------------------------------------------------------------
输入:
\begin{align*}\begin{aligned}\rho(x)\partial_t^2 w_t^n - \operatorname{div}(K(x)\nabla w_t^n) \rightarrow 0~ \hbox{ in }~H^{-1}_{loc}(\Omega \times (0,T)), \\\rho(x)\partial_t^2 z_t^n - \operatorname{div}(K(x)\nabla z_t^n) \rightarrow 0~ \hbox{ in }~H^{-1}_{loc}(\Omega \times (0,T)),\end{aligned}\end{align*}

输出:
\begin{align*}\begin{aligned}\rho(x)\partial_t^2 w_t^n-\operatorname{div}(K(x)\nabla w_t^n) \rightarrow 0~\text{CONTENTPROTECTED0}~H^{-1}_{loc}(\Omega \times(0,T)),\\\rho(x)\partial_t^2 z_t^n-\operatorname{div}(K(x)\nabla z_t^n) \rightarrow 0~\text{CONTENTPROTECTED1}~H^{-1}_{loc}(\Omega \times(0,T)),\end{aligned}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 812: TexTeller_0431228
------------------------------------------------------------
输入:
\begin{align*}\widehat{m}_{13}=-\widetilde{m}_{31}, \widehat{m}_{23}=-\widetilde{m}_{32}.\end{align*}

输出:
\begin{align*}\widehat{m}_{13}=-\widetilde{m}_{31},\widehat{m}_{23}=-\widetilde{m}_{32}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 813: TexTeller_0433079
------------------------------------------------------------
输入:
\begin{align*}H_{\alpha}g=\lambda g, \,\ f\in C^{+}[0,1]\end{align*}

输出:
\begin{align*}H_{\alpha}g=\lambda g,\,\ f\in C^{+}[0,1]\end{align*}

状态: 已规范化 ✓
================================================================================

样本 814: TexTeller_0434963
------------------------------------------------------------
输入:
\begin{align*}\ & \lim _{k\to \omega }\| \tilde{\alpha} _n (x_k) -\theta _t(x_k)\| _{\varphi \circ \theta _s}^\sharp \\ &=\mathrm{weak}\lim _{k \to \omega }\frac{1}{2} ( |\tilde{\alpha } _n (x_k) -\theta _t(x_k )|^2 +|(\tilde{\alpha }_n (x_k)-\theta _t(x_k))^*|^2 ) \\& =2\delta >0\end{align*}

输出:
\begin{align*}& \lim_{k\to \omega}\| \tilde{\alpha}_n(x_k)-\theta_t(x_k)\|_{\varphi \circ \theta_s}^\sharp \&=\mathrm{weak}\lim_{k \to \omega}\frac{1}{2}(|\tilde{\alpha}_n(x_k)-\theta_t(x_k)|^2+|(\tilde{\alpha}_n(x_k)-\theta_t(x_k))^*|^2) \\&=2\delta>0\end{align*}

状态: 已规范化 ✓
================================================================================

样本 815: TexTeller_0436840
------------------------------------------------------------
输入:
\begin{align*}X_{1}(u)+X_{5}(u)&\equiv \frac{2}{5}up \pmod{p^2}, X_3(u) \equiv \frac{1}{30}up \pmod{p^2} \textrm{and} \\X_2(u)+X_4(u)&\equiv -\frac{1}{10}up \pmod{p^2}.\end{align*}

输出:
\begin{align*}X_{1}(u)+X_{5}(u)&\equiv \frac{2}{5}up \pmod{p^2},X_3(u) \equiv \frac{1}{30}up \pmod{p^2} \textrm{and} \\X_2(u)+X_4(u)&\equiv-\frac{1}{10}up \pmod{p^2}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 816: TexTeller_0438745
------------------------------------------------------------
输入:
\begin{align*}y''=2y^3+\tilde zy+C.\end{align*}

输出:
\begin{align*}y''=2y^3+\tilde zy+C.\end{align*}

状态: 无变化
================================================================================

样本 817: TexTeller_0440604
------------------------------------------------------------
输入:
\begin{align*}a_k\,:=\,|A_{M^k}(u,B_1)\cap Q|\;\;\;\;\;\mbox{and}\;\;\;\;\;b_k\,:=\,\left|\left\lbrace x\in Q\,|\,m(f^p)(x)\,\geq\,(CM^k)^p \right\rbrace\right|.\end{align*}

输出:
\begin{align*}a_k\,:=\,|A_{M^k}(u,B_1)\cap Q|\;\;\;\;\;\text{CONTENTPROTECTED0}\;\;\;\;\;b_k\,:=\,\left|\left\lbrace x\in Q\,|\,m(f^p)(x)\,\geq\,(CM^k)^p \right\rbrace\right|.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 818: TexTeller_0442513
------------------------------------------------------------
输入:
\begin{align*}g_{1}=x_{0}^{n+1}-x_{3}^{n}&=\sum_{\mu=1}^{n-1}\alpha_{\mu}\left[x_{2}^{(n+1)-\mu}x_{3}^{\mu}-x_{0}^{(n+1)-\mu}x_{1}^{\mu+1}\right]\\&+\sum_{t=0}^{n}\beta_{t}\left[x_{1}^{(n+1)-t}x_{3}^{t}-x_{0}^{n-t}x_{2}^{t+1}\right]+\gamma_{2}(x_{1}x_{2}-x_{0}x_{3}),\end{align*}

输出:
\begin{align*}g_{1}=x_{0}^{n+1}-x_{3}^{n}&=\sum_{\mu=1}^{n-1}\alpha_{\mu}\left[x_{2}^{(n+1)-\mu}x_{3}^{\mu}-x_{0}^{(n+1)-\mu}x_{1}^{\mu+1}\right]\\&+\sum_{t=0}^{n}\beta_{t}\left[x_{1}^{(n+1)-t}x_{3}^{t}-x_{0}^{n-t}x_{2}^{t+1}\right]+\gamma_{2}(x_{1}x_{2}-x_{0}x_{3}),\end{align*}

状态: 无变化
================================================================================

样本 819: TexTeller_0444348
------------------------------------------------------------
输入:
\begin{align*} d M_\eta(t)& =e^{-\eta t}\frac{\sqrt{2\gamma}}{m}v(t)\, dW_0(t)+\frac{e^{-\eta t}}{m}\sum_{k=1}^N \sqrt{2\lambda_k}z_k(t)\, dW_k(t)\\&+e^{-\eta t}\sum_{k>N}\sqrt{2\lambda_k}k^{-2s}z_k(t)\, dW_k(t).\end{align*}

输出:
\begin{align*} d M_\eta(t)&=e^{-\eta t}\frac{\sqrt{2\gamma}}{m}v(t)\,dW_0(t)+\frac{e^{-\eta t}}{m}\sum_{k=1}^N \sqrt{2\lambda_k}z_k(t)\,dW_k(t)\\&+e^{-\eta t}\sum_{k>N}\sqrt{2\lambda_k}k^{-2s}z_k(t)\,dW_k(t).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 820: TexTeller_0446277
------------------------------------------------------------
输入:
\begin{align*}f=\sum_{j=j_0}^{\infty}\sum_{k=0}^{2^{j}-1}f_{jk}\psi_{jk},\end{align*}

输出:
\begin{align*}f=\sum_{j=j_0}^{\infty}\sum_{k=0}^{2^{j}-1}f_{jk}\psi_{jk},\end{align*}

状态: 无变化
================================================================================

样本 821: TexTeller_0448134
------------------------------------------------------------
输入:
\begin{align*}C_{\operatorname*{DtN},k}:=\left\Vert T_{k}\right\Vert _{\mathbf{H}_{\operatorname*{div}}^{-1/2}\left( \Gamma\right) \leftarrow\mathbf{H}_{\operatorname*{curl}}^{-1/2}\left( \Gamma\right) }<\infty.\end{align*}

输出:
\begin{align*}C_{\operatorname*{DtN},k}:=\left\Vert T_{k}\right\Vert_{\mathbf{H}_{\operatorname*{div}}^{-1/2}\left(\Gamma\right) \leftarrow\mathbf{H}_{\operatorname*{curl}}^{-1/2}\left(\Gamma\right)}<\infty.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 822: TexTeller_0450040
------------------------------------------------------------
输入:
\begin{align*}\rho^\textrm{T}_i = \sum\limits_{k=1}^K \pi_{i,k} \upsilon^\textrm{T}_{i,k}.\end{align*}

输出:
\begin{align*}\rho^\textrm{T}_i=\sum\limits_{k=1}^K \pi_{i,k} \upsilon^\textrm{T}_{i,k}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 823: TexTeller_0451911
------------------------------------------------------------
输入:
\begin{align*}A(R) = \frac{(N-3)!(N-2p)}{(N-p-1)!(p-1)!} \; \; ,\end{align*}

输出:
\begin{align*}A(R)=\frac{(N-3)!(N-2p)}{(N-p-1)!(p-1)!} \;\;,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 824: TexTeller_0453783
------------------------------------------------------------
输入:
\begin{align*}{\cal{A}} = c_1\frac{\Gamma(t) \Gamma(s)}{\Gamma(1+s+t)} (s a_1 -ta_2) \delta^{p+1}(p_1+p_2)\end{align*}

输出:
\begin{align*}{\cal{A}}=c_1\frac{\Gamma(t) \Gamma(s)}{\Gamma(1+s+t)}(s a_1-ta_2) \delta^{p+1}(p_1+p_2)\end{align*}

状态: 已规范化 ✓
================================================================================

样本 825: TexTeller_0455680
------------------------------------------------------------
输入:
\begin{align*}g(x) = \exp (\Gamma_{\mu} x^{\mu})\end{align*}

输出:
\begin{align*}g(x)=\mathrm{exp}(\Gamma_{\mu} x^{\mu})\end{align*}

状态: 已规范化 ✓
================================================================================

样本 826: TexTeller_0457546
------------------------------------------------------------
输入:
\begin{align*}e^{ c \phi} e^{f(q^{a})} - e^{-2 c \phi } e^{-2 f(q^{a})} = e^{\phi} [\,c+ c_{1} + Y_{0}X_{0} f(q^{a}) \,] - e^{-2 \phi} [\, c + c_{1}- Y_{1}X_{0} f(q^{a}) \,] .\end{align*}

输出:
\begin{align*}e^{c \phi} e^{f(q^{a})}-e^{-2 c \phi} e^{-2 f(q^{a})}=e^{\phi}[\,c+c_{1}+Y_{0}X_{0} f(q^{a}) \,]-e^{-2 \phi}[\,c+c_{1}-Y_{1}X_{0} f(q^{a}) \,].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 827: TexTeller_0459431
------------------------------------------------------------
输入:
\begin{align*}\frac{E_D (B)}{V} \, = \, \frac{1}{8 \pi^2} \, \zeta\left( \frac{3}{2} \right) \, (gB)^{3/2} \; .\end{align*}

输出:
\begin{align*}\frac{E_D(B)}{V} \,=\,\frac{1}{8 \pi^2} \,\zeta\left(\frac{3}{2} \right) \,(gB)^{3/2} \;.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 828: TexTeller_0461284
------------------------------------------------------------
输入:
\begin{align*}{\cal S}^{D0} = \frac{1}{2 g_{S}} \int dt~{\rm Tr} \left(G_{ij} \dot{X}^i \dot{X}^j+ \frac{1}{2}\frac{1}{(2\pi)^2} G_{ij} G_{kl} [X^i,X^k] [X^j,X^l]+ \right.\end{align*}

输出:
\begin{align*}{\cal S}^{D0}=\frac{1}{2 g_{S}} \int dt~{\rm Tr} \left(G_{ij} \dot{X}^i \dot{X}^j+\frac{1}{2}\frac{1}{(2\pi)^2} G_{ij} G_{kl}[X^i,X^k][X^j,X^l]+\right.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 829: TexTeller_0463171
------------------------------------------------------------
输入:
\begin{align*}\langle \psi (x) \, \bar{\psi}(\tilde{x})\rangle_w =\langle1\rangle_w\Omega^{-1}(x)\Omega^{-1}(\tilde{x})\, S_o(x,\,\tilde{x})\end{align*}

输出:
\begin{align*}\langle \psi(x) \,\bar{\psi}(\tilde{x})\rangle_w=\langle1\rangle_w\Omega^{-1}(x)\Omega^{-1}(\tilde{x})\,S_o(x,\,\tilde{x})\end{align*}

状态: 已规范化 ✓
================================================================================

样本 830: TexTeller_0465073
------------------------------------------------------------
输入:
\begin{align*}\ln \left[ \frac{ K-\sigma}{K } \right] = -\int_{0}^{\sigma} d s \frac{1}{ K -s},\end{align*}

输出:
\begin{align*}\ln \left[\frac{K-\sigma}{K} \right]=-\int_{0}^{\sigma} d s \frac{1}{K-s},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 831: TexTeller_0466926
------------------------------------------------------------
输入:
\begin{align*}\int _{\left|m_{eff}^{2}\right|^{1/2}\beta }^{\infty }\, dy\, \left(y^{2}+\left|m_{eff}^{2}\right|\beta ^{2}\right)^{1/2}e^{-2\pi rny\lambda /\beta }=\frac{\left|m_{eff}^{2}\right|^{1/2}\beta ^{2}}{4\pi rn\lambda }\times \left[H_{1}\left(\eta \lambda \right)-N_{1}\left(\eta \lambda \right)\right],\end{align*}

输出:
\begin{align*}\int_{\left|m_{eff}^{2}\right|^{1/2}\beta}^{\infty}\,dy\,\left(y^{2}+\left|m_{eff}^{2}\right|\beta^{2}\right)^{1/2}e^{-2\pi rny\lambda/\beta}=\frac{\left|m_{eff}^{2}\right|^{1/2}\beta^{2}}{4\pi rn\lambda}\times \left[H_{1}\left(\eta \lambda \right)-N_{1}\left(\eta \lambda \right)\right],\end{align*}

状态: 已规范化 ✓
================================================================================

样本 832: TexTeller_0468840
------------------------------------------------------------
输入:
\begin{align*} \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(1)} = 0 \qquad \hbox{and}\qquad \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(2)} = 0 \quad ,\end{align*}

输出:
\begin{align*} \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(1)}=0 \qquad\text{CONTENTPROTECTED0}\qquad \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(2)}=0 \quad,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 833: TexTeller_0470705
------------------------------------------------------------
输入:
\begin{align*}\frac{d^2R}{d\rho^2}+\frac{2}{\rho}\frac{dR}{d\rho}-\frac{l(l+1)}{\rho^2}R+ \frac{\lambda}{\rho}R - \frac{1}{4}R = 0,\end{align*}

输出:
\begin{align*}\frac{d^2R}{d\rho^2}+\frac{2}{\rho}\frac{dR}{d\rho}-\frac{l(l+1)}{\rho^2}R+\frac{\lambda}{\rho}R-\frac{1}{4}R=0,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 834: TexTeller_0472601
------------------------------------------------------------
输入:
\begin{align*}& (\Sigma_{m})_{1,j} = (\Sigma_{m})_{j,1} = \delta_{1,j}-\tfrac{1}{\sqrt{2}}\delta_{2,j}, & & 1 \leq j \leq m. \\& (\Sigma_{m})_{i,j} = \delta_{i,j}-\tfrac{1}{2}\delta_{i,j+1}- \tfrac{1}{2}\delta_{i,j-1}, & & 2 \leq i,j \leq m.\end{align*}

输出:
\begin{align*}&(\Sigma_{m})_{1,j}=(\Sigma_{m})_{j,1}=\delta_{1,j}-\tfrac{1}{\sqrt{2}}\delta_{2,j},& & 1 \leq j \leq m.\\&(\Sigma_{m})_{i,j}=\delta_{i,j}-\tfrac{1}{2}\delta_{i,j+1}-\tfrac{1}{2}\delta_{i,j-1},& & 2 \leq i,j \leq m.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 835: TexTeller_0474459
------------------------------------------------------------
输入:
\begin{align*}-z^T(T)Hz(T) = \int_0^T\left\{ [(A+G)z +g]^T Pz +z^TP[(A+G)z +g] +z^T \dot P z\right\}(t)dt.\end{align*}

输出:
\begin{align*}-z^T(T)Hz(T)=\int_0^T\left\{[(A+G)z+g]^T Pz+z^TP[(A+G)z+g]+z^T \dot{P} z\right\}(t)dt.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 836: TexTeller_0476357
------------------------------------------------------------
输入:
\begin{align*}\frac{1}{\left( 1+x\right) ^{s+1}}w_{n}^{\left( s+1\right) }\left(\frac{-x}{1+x};\alpha,\beta,r\right) =\frac{1}{2\pi i\Gamma\left(s+1\right) }{\displaystyle\int\limits_{a-i\infty}^{a+i\infty}}\left( r-\beta t\mid\alpha\right) _{n}x^{-t}\Gamma\left( t\right)\Gamma\left( s+1-t\right) dt, \end{align*}

输出:
\begin{align*}\frac{1}{\left(1+x\right)^{s+1}}w_{n}^{\left(s+1\right)}\left(\frac{-x}{1+x};\alpha,\beta,r\right)=\frac{1}{2\pi i\Gamma\left(s+1\right)}{\displaystyle\int\limits_{a-i\infty}^{a+i\infty}}\left(r-\beta t\mid\alpha\right)_{n}x^{-t}\Gamma\left(t\right)\Gamma\left(s+1-t\right) dt,\end{align*}

状态: 已规范化 ✓
================================================================================

样本 837: TexTeller_0478237
------------------------------------------------------------
输入:
\begin{align*}A(s):=\int_0^s e^{\frac{G(t)}{p-1}} \ dt,\end{align*}

输出:
\begin{align*}A(s):=\int_0^s e^{\frac{G(t)}{p-1}} \ dt,\end{align*}

状态: 无变化
================================================================================

样本 838: TexTeller_0480118
------------------------------------------------------------
输入:
\begin{align*}{\bf{Y}}\left[ i \right] = \mathsf{H}{\bf{X}}\left[ i \right] + {\bf{W}}\left[ i \right]\end{align*}

输出:
\begin{align*}{\bf{Y}}\left[i \right]=\mathsf{H}{\bf{X}}\left[i \right]+{\bf{W}}\left[i \right]\end{align*}

状态: 已规范化 ✓
================================================================================

样本 839: TexTeller_0481999
------------------------------------------------------------
输入:
\begin{align*}K(r) =& \int_0^{r} g(s) 2 \pi s ds,\\L(r) =& \sqrt{\frac{K(r)}{\pi}},\end{align*}

输出:
\begin{align*}K(r)=& \int_0^{r} g(s) 2 \pi s ds,\\L(r)=& \sqrt{\frac{K(r)}{\pi}},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 840: TexTeller_0483882
------------------------------------------------------------
输入:
\begin{align*}\deg^\vee = \frac{1}{2}(s_1 + \cdots +s_{2k})\end{align*}

输出:
\begin{align*}\deg^\vee=\frac{1}{2}(s_1+. . .+s_{2k})\end{align*}

状态: 已规范化 ✓
================================================================================

样本 841: TexTeller_0485754
------------------------------------------------------------
输入:
\begin{align*}\nu _{\mathcal{X}_{2}}(s)=\mu \int_{0}^{+\infty }\frac{e^{-s^{2}/2z}}{\sqrt{2\pi z^{3}}}e^{-\rho z}dz=\frac{\mu }{|s|}e^{-\sqrt{2\rho }|s|},\end{align*}

输出:
\begin{align*}\nu_{\mathcal{X}_{2}}(s)=\mu \int_{0}^{+\infty}\frac{e^{-s^{2}/2z}}{\sqrt{2\pi z^{3}}}e^{-\rho z}dz=\frac{\mu}{|s|}e^{-\sqrt{2\rho}|s|},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 842: TexTeller_0487645
------------------------------------------------------------
输入:
\begin{align*}D(L)=\{ u\in D(\widehat{L}) : \; [(I-K \widehat{L})u]|_{\partial\Omega}=0 \}, \end{align*}

输出:
\begin{align*}D(L)=\{u\in D(\widehat{L}):\;[(I-K \widehat{L})u]|_{\partial\Omega}=0 \},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 843: TexTeller_0489467
------------------------------------------------------------
输入:
\begin{align*}\int_0^1\zeta(a,x)\zeta(b,x)\,dx=\left\{B(a+b-1,1-a)+B(a+b-1,1-b)\right\}\zeta(a+b-1)\end{align*}%\end{align*}

输出:
\begin{align*}\int_0^1\zeta(a,x)\zeta(b,x)\,dx=\left\{B(a+b-1,1-a)+B(a+b-1,1-b)\right\}\zeta(a+b-1)\end{align*}%\end{align*}

状态: 无变化
================================================================================

样本 844: TexTeller_0491395
------------------------------------------------------------
输入:
\begin{align*}\sum_{\varnothing\neq J\subset\{1,\ldots,n\}} (-1)^{\#J-1} f_r(\Pi_J) = (-1)^d \left(\binom {n}{r+1} - f_r(\Pi)\right).\end{align*}

输出:
\begin{align*}\sum_{\varnothing\neq J\subset\{1,. . .,n\}}(-1)^{\#J-1} f_r(\Pi_J)=(-1)^d \left(\binom{n}{r+1}-f_r(\Pi)\right).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 845: TexTeller_0493288
------------------------------------------------------------
输入:
\begin{align*}f(m_2 + e) = &\ f(m_2) + a^{(m_2)} = f(m_1 + re) + a^{(m_2)} = f(m_1) +a^{(m_1)} r + a^{(m_2)}\\= &\ f(m_1 + (r+1)e) = f(m_1) + a^{(m_1)} (r+1).\end{align*}

输出:
\begin{align*}f(m_2+e)=&\ f(m_2)+a^{(m_2)}=f(m_1+re)+a^{(m_2)}=f(m_1)+a^{(m_1)} r+a^{(m_2)}\\=&\ f(m_1+(r+1)e)=f(m_1)+a^{(m_1)}(r+1).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 846: TexTeller_0495158
------------------------------------------------------------
输入:
\begin{align*}\begin{cases}\partial_t w -\partial_x^2w + x^2 w = 0 , t\in (0, \tau),\ x \in (-L, \, L) \\w(t, -L)=u_{-L}(t), w(t, L)=u_L(t), t\in (0, \tau) \\w(0, x)=f, x \in (-L, \, L).\end{cases}\end{align*}

输出:
\begin{align*}\begin{cases}\partial_t w-\partial_x^2w+x^2 w=0,t\in(0,\tau),\ x \in(-L,\,L) \\w(t,-L)=u_{-L}(t),w(t,L)=u_L(t),t\in(0,\tau) \\w(0,x)=f,x \in(-L,\,L).\end{cases}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 847: TexTeller_0497038
------------------------------------------------------------
输入:
\begin{align*}R\langle \Omega^1_R\{-1\}\rangle =R\langle \frac{\xi_1}{I}, \cdots, \frac{\xi_n}{I}\rangle,R^{PD}(1)= R\{\frac{\xi_1}{I}, \dots, \frac{\xi_n}{I}\}^{PD,\wedge}.\end{align*}

输出:
\begin{align*}R\langle \Omega^1_R\{-1\}\rangle=R\langle \frac{\xi_1}{I},. . .,\frac{\xi_n}{I}\rangle,R^{PD}(1)=R\{\frac{\xi_1}{I},. . .,\frac{\xi_n}{I}\}^{PD,\wedge}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 848: TexTeller_0498935
------------------------------------------------------------
输入:
\begin{align*}\widehat{\theta}_n - \theta_0 = -\frac{\dot{\mathbb{M}}_n(\theta_0)}{\int_0^1 \ddot{\mathbb{M}}_n(\theta_0 + t(\widehat{\theta}_n - \theta_0))dt}. \end{align*}

输出:
\begin{align*}\widehat{\theta}_n-\theta_0=-\frac{\dot{\mathbb{M}}_n(\theta_0)}{\int_0^1 \ddot{\mathbb{M}}_n(\theta_0+t(\widehat{\theta}_n-\theta_0))dt}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 849: TexTeller_0500790
------------------------------------------------------------
输入:
\begin{align*} \chi(|N|)= X^{-1}_{S_{i_0}}([a_{i_0-1},\mu_1,\mu_2,\dots,\mu_{t-1} \mid b_{i_0},\nu_1,\nu_2,\dots,\nu_{t-1}]+E) \end{align*}

输出:
\begin{align*} \chi(|N|)=X^{-1}_{S_{i_0}}([a_{i_0-1},\mu_1,\mu_2,. . .,\mu_{t-1} \mid b_{i_0},\nu_1,\nu_2,. . .,\nu_{t-1}]+E) \end{align*}

状态: 已规范化 ✓
================================================================================

样本 850: TexTeller_0502681
------------------------------------------------------------
输入:
\begin{align*}\gamma_{ij} = \gamma_{0i} \pm \gamma_{0j}.\end{align*}

输出:
\begin{align*}\gamma_{ij}=\gamma_{0i} \pm \gamma_{0j}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 851: TexTeller_0504541
------------------------------------------------------------
输入:
\begin{align*}\beta_i|_{\frak{t}^\sigma} =\alpha_{i+1}|_{\frak{t}^\sigma}\ (1\leq i\leq n-1),\beta_n|_{\frak{t}^\sigma}=\beta_{n+1}|_{\frak{t}^\sigma} =\alpha_{n+1}|_{\frak{t}^\sigma}\end{align*}

输出:
\begin{align*}\beta_i|_{\frak{t}^\sigma}=\alpha_{i+1}|_{\frak{t}^\sigma}(1\leq i\leq n-1),\beta_n|_{\frak{t}^\sigma}=\beta_{n+1}|_{\frak{t}^\sigma}=\alpha_{n+1}|_{\frak{t}^\sigma}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 852: TexTeller_0506424
------------------------------------------------------------
输入:
\begin{align*}\bar{\xi}_A(D^n,n^{-1}\delta_n x^n) = \zeta_A(D,x) + \frac{1}{\log(D)} \int_x^{xD} \frac{K_A(u)}{u} \mathrm{d}u.\end{align*}

输出:
\begin{align*}\bar{\xi}_A(D^n,n^{-1}\delta_n x^n)=\zeta_A(D,x)+\frac{1}{\log(D)} \int_x^{xD} \frac{K_A(u)}{u} \mathrm{d} u.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 853: TexTeller_0508326
------------------------------------------------------------
输入:
\begin{align*}\sum_{h=1}^H b(h)\xi(h) = {\bf 0},\end{align*}

输出:
\begin{align*}\sum_{h=1}^H b(h)\xi(h)={\bf 0},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 854: TexTeller_0510204
------------------------------------------------------------
输入:
\begin{align*} \begin{aligned}&-\dot{v}(t) + A^*(t)v(t) = g(t) &&\mbox{in $V^*$, $t \in (0,T)$}, \\&v(T) = \xi &&\mbox{in $H$},\end{aligned}\end{align*}

输出:
\begin{align*} \begin{aligned}&-\dot{v}(t)+A^*(t)v(t)=g(t) &&\text{CONTENTPROTECTED0},\\&v(T)=\xi &&\text{CONTENTPROTECTED1},\end{aligned}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 855: TexTeller_0512072
------------------------------------------------------------
输入:
\begin{align*}f^q(\beta, u)=\lim_{N\to \infty}\frac{1}{N}\log Z_N^{\beta, u, q}=\lim_{N\to \infty}\frac{1}{N}E^Q[\log Z_N^{\beta, u, q}]\end{align*}

输出:
\begin{align*}f^q(\beta,u)=\lim_{N\to \infty}\frac{1}{N}\log Z_N^{\beta,u,q}=\lim_{N\to \infty}\frac{1}{N}E^Q[\log Z_N^{\beta,u,q}]\end{align*}

状态: 已规范化 ✓
================================================================================

样本 856: TexTeller_0513962
------------------------------------------------------------
输入:
\begin{align*}p_{n+1}^a = \lim_{s\to\infty} \left(1-\frac{Q_n(as)}{\zeta(as)}\right)^{-1/s}\end{align*}

输出:
\begin{align*}p_{n+1}^a=\lim_{s\to\infty} \left(1-\frac{Q_n(as)}{\zeta(as)}\right)^{-1/s}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 857: TexTeller_0515853
------------------------------------------------------------
输入:
\begin{align*}\textbf{h}_{1,k'} = \phi_{1,k'} \hat{\textbf{h}}_{1,k} + \textbf{w}_{1,k'},\end{align*}

输出:
\begin{align*}\textbf{h}_{1,k'}=\phi_{1,k'} \hat{\textbf{h}}_{1,k}+\textbf{w}_{1,k'},\end{align*}

状态: 已规范化 ✓
================================================================================

样本 858: TexTeller_0517717
------------------------------------------------------------
输入:
\begin{align*}\limsup_{t \to -\infty} \frac{F(t)}{t^2}=0.\end{align*}

输出:
\begin{align*}\limsup_{t \to-\infty} \frac{F(t)}{t^2}=0.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 859: TexTeller_0519607
------------------------------------------------------------
输入:
\begin{align*}\Pi^{(p)}=\sum_{d=1}^{p}M(d) \sum_{k=1}^{\infty} \mathrm{S}(\cdot\mid d, \alpha=k)\bigg{/} \sum_{\tilde{d}=1}^{p}M(\tilde{d}).\end{align*}

输出:
\begin{align*}\Pi^{(p)}=\sum_{d=1}^{p}M(d) \sum_{k=1}^{\infty} \mathrm{S}(\cdot\mid d,\alpha=k)\bigg{/} \sum_{\tilde{d}=1}^{p}M(\tilde{d}).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 860: TexTeller_0521496
------------------------------------------------------------
输入:
\begin{align*}a_{i_1} + a_{i_4 } = a_{i_2 } + a_{i_3 } \end{align*}

输出:
\begin{align*}a_{i_1}+a_{i_4}=a_{i_2}+a_{i_3} \end{align*}

状态: 已规范化 ✓
================================================================================

样本 861: TexTeller_0523379
------------------------------------------------------------
输入:
\begin{align*}P_\lambda=\sum_{i_0,\ldots,i_{n+1}=0}^{d-2}c_{(i_0,\ldots,i_{n+1})}x_0^{i_0}\cdots x_{n+1}^{i_{n+1}}.\end{align*}

输出:
\begin{align*}P_\lambda=\sum_{i_0,. . .,i_{n+1}=0}^{d-2}c_{(i_0,. . .,i_{n+1})}x_0^{i_0}. . . x_{n+1}^{i_{n+1}}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 862: TexTeller_0525259
------------------------------------------------------------
输入:
\begin{align*}f\big[U\to V\big] = \big(\mathcal{W}f\big)\big[U\to V\big].\end{align*}

输出:
\begin{align*}f\big[U\to V\big]=\big(\mathcal{W}f\big)\big[U\to V\big].\end{align*}

状态: 已规范化 ✓
================================================================================

样本 863: TexTeller_0527127
------------------------------------------------------------
输入:
\begin{align*}[H]_{y,z}=\frac{H(y)-H(z)}{y-z} y \ne z\end{align*}

输出:
\begin{align*}[H]_{y,z}=\frac{H(y)-H(z)}{y-z} y \ne z\end{align*}

状态: 无变化
================================================================================

样本 864: TexTeller_0529007
------------------------------------------------------------
输入:
\begin{align*}dX_t = \widetilde\mu(X_t)\, dt + \widetilde\sigma(X_t)\, dW_t\end{align*}

输出:
\begin{align*}dX_t=\widetilde\mu(X_t)\,dt+\widetilde\sigma(X_t)\,dW_t\end{align*}

状态: 已规范化 ✓
================================================================================

样本 865: TexTeller_0530892
------------------------------------------------------------
输入:
\begin{align*}H(t;x,y,1,u,1,v)&=\frac{xvt^2(1-yr)}{(1-ytu)(1-tuv(y-yr+1))(tux+y^{-1}-tu)}\\&\quad+\frac{yu^2vt^2(1-v)(1-yr)}{(1-ytu)(1-tuv(y-yr+1))}F(t;x,y,1,u,1,v).\end{align*}

输出:
\begin{align*}H(t;x,y,1,u,1,v)&=\frac{xvt^2(1-yr)}{(1-ytu)(1-tuv(y-yr+1))(tux+y^{-1}-tu)}\\&\quad+\frac{yu^2vt^2(1-v)(1-yr)}{(1-ytu)(1-tuv(y-yr+1))}F(t;x,y,1,u,1,v).\end{align*}

状态: 无变化
================================================================================

样本 866: TexTeller_0532739
------------------------------------------------------------
输入:
\begin{align*} \omega_\lambda(t) &:= \frac{\phi_\lambda''(t)\,t - \phi_\lambda'(t)}{\phi_\lambda'(t)}.\end{align*}

输出:
\begin{align*} \omega_\lambda(t) &:=\frac{\phi_\lambda''(t)\,t-\phi_\lambda'(t)}{\phi_\lambda'(t)}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 867: TexTeller_0534657
------------------------------------------------------------
输入:
\begin{align*} \Lambda(f \otimes \mathrm{Ad}(g), k) = \frac{4 L(1,\pi,\mathrm{ad})L(1,\tau,\mathrm{ad})}{\langle \mathbf h, \mathbf h \rangle\langle \mathbf g, \mathbf g\rangle\langle \pmb{\phi}, \pmb{\phi} \rangle} \left( \prod_v \mathcal I_v^{\sharp}(\mathbf h, \mathbf g, \pmb{\phi})^{-1}\right) \mathcal Q(\mathbf h, \mathbf g, \pmb{\phi}).\end{align*}

输出:
\begin{align*} \Lambda(f \otimes \mathrm{Ad}(g),k)=\frac{4 L(1,\pi,\mathrm{ad})L(1,\tau,\mathrm{ad})}{\langle \mathbf{h,} \mathbf{h} \rangle\langle \mathbf{g,} \mathbf{g\rangle\langle} \pmb{\phi},\pmb{\phi} \rangle} \left(\prod_v \mathcal{I_v^{\sharp}(\mathbf} h,\mathbf{g,} \pmb{\phi})^{-1}\right) \mathcal{Q(\mathbf} h,\mathbf{g,} \pmb{\phi}).\end{align*}

状态: 已规范化 ✓
================================================================================

样本 868: TexTeller_0536535
------------------------------------------------------------
输入:
\begin{align*}c_{i, j} := \sum_{i(\ell) = i} \epsilon_\ell^j x_\ell.\end{align*}

输出:
\begin{align*}c_{i,j}:=\sum_{i(\ell)=i} \epsilon_\ell^j x_\ell.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 869: TexTeller_0538422
------------------------------------------------------------
输入:
\begin{align*}\int_{t_2}^{t_1} f(t) \, dt = 0.\end{align*}

输出:
\begin{align*}\int_{t_2}^{t_1} f(t) \,dt=0.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 870: TexTeller_0540297
------------------------------------------------------------
输入:
\begin{align*} {\mathbb P }_{0} \Big( \int^{t}_{0} \big \lvert {\mathbb E}_{0} \big[ \lvert b ( s, X_{s}, F_{s})\rvert \, \big \vert \, \mathcal F_{T}^{X} \big] \big \rvert ^{2} {\mathrm d} s < \infty \Big) \, =\, 1 \, ; 0 \le t \le T \, . \end{align*}

输出:
\begin{align*}{\mathbb P}_{0} \Big(\int^{t}_{0} \big\lvert{\mathbb E}_{0} \big[\lvert b(s,X_{s},F_{s})\rvert \,\big\vert \,\mathcal{F_{T}^{X}} \big] \big\rvert^{2}{\mathrm d} s<\infty \Big) \,=\,1 \,;0 \le t \le T \,.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 871: TexTeller_0542183
------------------------------------------------------------
输入:
\begin{align*}\begin{aligned} w_0^+\circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^+\circ X^{-1}_\gamma\right)(s,0)&=-a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma,\\w_0^- \circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^-\circ X^{-1}_\gamma\right)(s,0)&=a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma.\end{aligned}\end{align*}

输出:
\begin{align*}\begin{aligned} w_0^+\circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^+\circ X^{-1}_\gamma\right)(s,0)&=-a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma,\\w_0^-\circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^-\circ X^{-1}_\gamma\right)(s,0)&=a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma.\end{aligned}\end{align*}

状态: 已规范化 ✓
================================================================================

样本 872: TexTeller_0544060
------------------------------------------------------------
输入:
\begin{align*}DG(\lambda,b,\Omega,0,0)(h_{1},h_{2})=\left(\begin{array}{c}D_{f_{1}}G_{1}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{1}(\lambda,b,\Omega,0,0)h_{2}\\D_{f_{1}}G_{2}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{2}(\lambda,b,\Omega,0,0)h_{2}\end{array}\right).\end{align*}

输出:
\begin{align*}DG(\lambda,b,\Omega,0,0)(h_{1},h_{2})=\left(\begin{array}{c}D_{f_{1}}G_{1}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{1}(\lambda,b,\Omega,0,0)h_{2}\\D_{f_{1}}G_{2}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{2}(\lambda,b,\Omega,0,0)h_{2}\end{array}\right).\end{align*}

状态: 无变化
================================================================================

样本 873: TexTeller_0545925
------------------------------------------------------------
输入:
\begin{align*}A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2},0\right\}\\A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}+{d_1+d_2-a\choose 2},0\right\}\\A=max&\Big\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}-{d_3+1\choose 2}+{d_1+d_2-a\choose 2}+\\&+{d_2+d_3-a\choose 2}+{d_3+d_1-a\choose 2}-{d_1+d_2+d_3-2a-1\choose 2},0\Big\}\end{align*}

输出:
\begin{align*}A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2},0\right\}\\A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}+{d_1+d_2-a\choose 2},0\right\}\\A=max&\Big\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}-{d_3+1\choose 2}+{d_1+d_2-a\choose 2}+\\&+{d_2+d_3-a\choose 2}+{d_3+d_1-a\choose 2}-{d_1+d_2+d_3-2a-1\choose 2},0\Big\}\end{align*}

状态: 无变化
================================================================================

样本 874: TexTeller_0547814
------------------------------------------------------------
输入:
\begin{align*}m_\lambda=\sum_{(i_1,\dots,i_l)\in \mathbb N^l} x^{\lambda_1}_{i_1}\dots x^{\lambda_l}_{i_l}.\end{align*}

输出:
\begin{align*}m_\lambda=\sum_{(i_1,. . .,i_l)\in \mathbb{N^l}} x^{\lambda_1}_{i_1}. . . x^{\lambda_l}_{i_l}.\end{align*}

状态: 已规范化 ✓
================================================================================

样本 875: TexTeller_0549687
------------------------------------------------------------
输入:
\begin{align*} \mathcal{G}(v, x) &= x - \sum_{w \in c(v)} \dfrac{1}{\mathcal{G}(w, x)} (\forall v \in V(T)) , \end{align*}

输出:
\begin{align*} \mathcal{G}(v,x) &=x-\sum_{w \in c(v)} \dfrac{1}{\mathcal{G}(w,x)}(\forall v \in V(T)),\end{align*}

状态: 已规范化 ✓
================================================================================

样本 876: TexTeller_0551591
------------------------------------------------------------
输入:
\begin{align*} P_F(t)\cdot\hat{L}_{xy}=\frac{1}{n}\left(\frac{3}{4(4n+1)}-t\right),\ \ P_F(t)\cdot\hat{R}_0=P_F(t)\cdot\hat{R}_1=\frac{3}{4(4n+1)}-t.\end{align*}

输出:
\begin{align*} P_F(t)\cdot\hat{L}_{xy}=\frac{1}{n}\left(\frac{3}{4(4n+1)}-t\right),\ \ P_F(t)\cdot\hat{R}_0=P_F(t)\cdot\hat{R}_1=\frac{3}{4(4n+1)}-t.\end{align*}

状态: 无变化
================================================================================

样本 877: im2latexv3_cc05b8d6a6c729e
------------------------------------------------------------
输入:
d s ^ { 2 } = e ^ { 2 U } ( r ) d t ^ { 2 } + e ^ { - 2 U } ( r ) d r ^ { 2 } + R ^ { 2 } ( r ) d ^ { 2 } \Omega \ ,

输出:
d s^{2}=e^{2 U}(r) d t^{2}+e^{-2 U}(r) d r^{2}+R^{2}(r) d^{2} \Omega,

状态: 已规范化 ✓
================================================================================

样本 878: im2latexv3_c142ea369703e59
------------------------------------------------------------
输入:
\pi _ { 0 } ( { \cal A } ( K _ { 1 } ) \vee { \cal A } ( K _ { \infty } ) ) \subset \pi _ { 0 } ( { \cal A } ( { \cal C } ) ) ^ { \prime } .

输出:
\pi_{0}({\cal A}(K_{1}) \vee{\cal A}(K_{\infty})) \subset \pi_{0}({\cal A}({\cal C}))^{\prime}.

状态: 已规范化 ✓
================================================================================

样本 879: im2latexv3_b90cb07c8337207
------------------------------------------------------------
输入:
\langle { \bf x } \vert U _ { 1 } ( t - t ^ { \prime } ) \vert { \bf x } \, ^ { \prime } \rangle = 2 { \frac { \partial } { \partial t ^ { \prime } } } D ^ { - } ( x ^ { \prime } - x ) ,

输出:
\langle{\bf x} \vert U_{1}(t-t^{\prime}) \vert{\bf x} \,^{\prime} \rangle=2{\frac{\partial}{\partial t^{\prime}}} D^{-}(x^{\prime}-x),

状态: 已规范化 ✓
================================================================================

样本 880: im2latexv3_2a758318c0b64b2
------------------------------------------------------------
输入:
C _ { s p i n } = \left( \begin{matrix} { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 1 } \\ { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { - 1 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { - 1 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 1 } & { 0 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 0 } & { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { - 1 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { - 1 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } \\ { 1 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } \\ \end{matrix} \right) \ .

输出:
C_{s p i n}=\left(\begin{matrix}{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{1}} \{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{-1}} &{{0}} \{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{-1}} &{{0}} &{{0}} \{{0}} &{{0}} &{{0}} &{{1}} &{{0}} &{{0}} &{{0}} &{{0}} \{{0}} &{{0}} &{{0}} &{{0}} &{{1}} &{{0}} &{{0}} &{{0}} \{{0}} &{{0}} &{{-1}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} \{{0}} &{{-1}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} \{{1}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} \\ \end{matrix} \right).

状态: 已规范化 ✓
================================================================================

样本 881: im2latexv3_c3dcdca6f34c048
------------------------------------------------------------
输入:
R _ { 1 2 } ( - \zeta ) = - \sigma _ { 1 } ^ { z } R _ { 1 2 } ( \zeta ) \sigma _ { 1 } ^ { z } ;

输出:
R_{1 2}(-\zeta)=-\sigma_{1}^{z} R_{1 2}(\zeta) \sigma_{1}^{z};

状态: 已规范化 ✓
================================================================================

样本 882: im2latexv3_037a26b5e4d932d
------------------------------------------------------------
输入:
A ( \tau , N ) = \alpha _ { 0 } + \sum _ { k = 1 } ^ { \infty } \alpha _ { k } q ^ { k }

输出:
A(\tau,N)=\alpha_{0}+\sum_{k=1}^{\infty} \alpha_{k} q^{k}

状态: 已规范化 ✓
================================================================================

样本 883: im2latexv3_61c20ffcccb377a
------------------------------------------------------------
输入:
Z = e ^ { - { \frac { 1 } { g } } - i \chi _ { 0 } } .

输出:
Z=e^{-{\frac{1}{g}}-i \chi_{0}}.

状态: 已规范化 ✓
================================================================================

样本 884: im2latexv3_0b61decee421c58
------------------------------------------------------------
输入:
( P \cdot J ) _ { \alpha \beta } \tilde { F } ^ { \beta } = i \epsilon _ { \: \alpha \beta } ^ { \mu } p _ { \mu } \tilde { F } ^ { \beta } = - s m \tilde { F } _ { \alpha }

输出:
(P \cdot J)_{\alpha \beta} \tilde{F}^{\beta}=i \epsilon_{\:\alpha \beta}^{\mu} p_{\mu} \tilde{F}^{\beta}=-s m \tilde{F}_{\alpha}

状态: 已规范化 ✓
================================================================================

样本 885: im2latexv3_eef88584b762ec4
------------------------------------------------------------
输入:
{ \cal A } _ { p } = i \sqrt { \frac { 2 } { \pi } } { 2 ^ { ( 7 - p ) / 2 } } { \Gamma ( \frac { 9 - p } { 2 } ) } \mu ^ { ( p - 8 ) / 2 ( 7 - p ) } .

输出:
{\cal A}_{p}=i \sqrt{\frac{2}{\pi}}{2^{(7-p)/2}}{\Gamma(\frac{9-p}{2})} \mu^{(p-8)/2(7-p)}.

状态: 已规范化 ✓
================================================================================

样本 886: im2latexv3_bab336be88f0cfb
------------------------------------------------------------
输入:
w ^ { T } \, X \, w \; = \; ( w ^ { T } \, X \, w ) ^ { T } \; = \; w ^ { T } \, X ^ { T } \, w \, .

输出:
w^{T} \,X \,w \;=\;(w^{T} \,X \,w)^{T} \;=\;w^{T} \,X^{T} \,w \,.

状态: 已规范化 ✓
================================================================================

样本 887: im2latexv3_167cf9f4b2673d4
------------------------------------------------------------
输入:
\tilde { T } ^ { - 1 } b \tilde { T } = b a , \; \; \tilde { T } ^ { - 1 } a \tilde { T } = a , \; \; \tilde { T } ^ { - 1 } \sigma \tilde { T } = \sigma .

输出:
\tilde{T}^{-1} b \tilde{T}=b a,\;\;\tilde{T}^{-1} a \tilde{T}=a,\;\;\tilde{T}^{-1} \sigma \tilde{T}=\sigma.

状态: 已规范化 ✓
================================================================================

样本 888: im2latexv3_1a6878fbe957134
------------------------------------------------------------
输入:
\tilde { u } = v ^ { 2 N - 4 } \left\langle \mathrm { T r } ( \tilde { q } q \tilde { q } q ) - { \frac { 1 } { 2 } } \mathrm { T r } ( \tilde { q } q ) \mathrm { T r } ( \tilde { q } q ) \right\rangle \ .

输出:
\tilde{u}=v^{2 N-4} \left\langle \mathrm{Tr}(\tilde{q} q \tilde{q} q)-{\frac{1}{2}} \mathrm{Tr}(\tilde{q} q) \mathrm{Tr}(\tilde{q} q) \right\rangle.

状态: 已规范化 ✓
================================================================================

样本 889: im2latexv3_48ec021cd34ab91
------------------------------------------------------------
输入:
\tilde { L } _ { D S } = { \cal R } L _ { D S } = \frac { \alpha ^ { \prime } \hbar H } { c ^ { 2 } }

输出:
\tilde{L}_{D S}={\cal R} L_{D S}=\frac{\alpha^{\prime} \hbar H}{c^{2}}

状态: 已规范化 ✓
================================================================================

样本 890: im2latexv3_ec3481409253dca
------------------------------------------------------------
输入:
D _ { B } = { \frac { \alpha ^ { \prime } } { 4 \pi } } \cdot { \frac { 1 } { 2 } } \int _ { | z | \le 1 } d ^ { 2 } z { \frac { 1 } { | z | ^ { 2 } } } z ^ { L _ { 0 } } { \bar { z } } ^ { { \tilde { L } } _ { 0 } }

输出:
D_{B}={\frac{\alpha^{\prime}}{4 \pi}} \cdot{\frac{1}{2}} \int_{| z | \le 1} d^{2} z{\frac{1}{| z |^{2}}} z^{L_{0}}{\bar{z}}^{{\tilde{L}}_{0}}

状态: 已规范化 ✓
================================================================================

样本 891: im2latexv3_97ef553ef5d1676
------------------------------------------------------------
输入:
k _ { 1 } ^ { 2 } ( s ) \, = \, - \, \ddot { x } _ { \mu } \, \ddot { x } ^ { \mu } \, = \, - \ddot { x } ^ { 2 } \, { . }

输出:
k_{1}^{2}(s) \,=\,-\,\ddot{x}_{\mu} \,\ddot{x}^{\mu} \,=\,-\ddot{x}^{2} \,{.}

状态: 已规范化 ✓
================================================================================

样本 892: im2latexv3_6bc3d9ba37e1fef
------------------------------------------------------------
输入:
\partial _ { + } ( \partial _ { x } \phi ) = 0

输出:
\partial_{+}(\partial_{x} \phi)=0

状态: 已规范化 ✓
================================================================================

样本 893: im2latexv3_1e080e47ae2527a
------------------------------------------------------------
输入:
\Theta _ { 1 } ( x ) = T _ { 2 } ( x ) , ~ ~ ~ \Theta _ { 2 } ( x ) = T _ { 4 } ( x )

输出:
\Theta_{1}(x)=T_{2}(x),~ ~ ~ \Theta_{2}(x)=T_{4}(x)

状态: 已规范化 ✓
================================================================================

样本 894: im2latexv3_e8e4476459e4ec2
------------------------------------------------------------
输入:
L = \partial ^ { m } + \sum _ { j = 0 } ^ { m - 2 } U _ { j } \partial ^ { j } = \prod _ { j = 1 } ^ { m } ( \partial + P _ { j } )

输出:
L=\partial^{m}+\sum_{j=0}^{m-2} U_{j} \partial^{j}=\prod_{j=1}^{m}(\partial+P_{j})

状态: 已规范化 ✓
================================================================================

样本 895: im2latexv3_16bff059941d914
------------------------------------------------------------
输入:
\phi _ { a } \Big ( \prod _ { i \in I } \Gamma ^ { i } ( t ) \Big ) : = \prod _ { i \in I } \Gamma ^ { i } ( a ) ~ ,

输出:
\phi_{a} \Big(\prod_{i \in I} \Gamma^{i}(t) \Big):=\prod_{i \in I} \Gamma^{i}(a) ~,

状态: 已规范化 ✓
================================================================================

样本 896: im2latexv3_adc89c42c7ebbcf
------------------------------------------------------------
输入:
{ \mit \Phi } _ { \sigma _ { 2 } \sigma _ { 1 } } = { \mit \Phi } _ { \sigma _ { 2 } \sigma _ { 1 } } ( \vec { k } , \hat { n } ) .

输出:
{\mit \Phi}_{\sigma_{2} \sigma_{1}}={\mit \Phi}_{\sigma_{2} \sigma_{1}}(\vec{k},\hat{n}).

状态: 已规范化 ✓
================================================================================

样本 897: im2latexv3_c30f168d4af7dc3
------------------------------------------------------------
输入:
( \lambda + ( u - v ) P ) L ( u ) _ { s } \otimes L ( v ) _ { s } = L ( v ) _ { s } \otimes L ( u ) _ { s } ( \lambda + ( u - v ) P )

输出:
(\lambda+(u-v) P) L(u)_{s} \otimes L(v)_{s}=L(v)_{s} \otimes L(u)_{s}(\lambda+(u-v) P)

状态: 已规范化 ✓
================================================================================

样本 898: im2latexv3_1d3e93fee3be7bf
------------------------------------------------------------
输入:
Z ^ { ( \mathrm { c o v ) } } ( G , q ) = \sum _ { \{ h _ { i } \} } \left[ N ^ { | h | } \Lambda ( h ) \right] ^ { 2 G - 2 } q ^ { | h | } ~ ,

输出:
Z^{(\mathrm{cov)}}(G,q)=\sum_{\{h_{i} \}} \left[N^{| h |} \Lambda(h) \right]^{2 G-2} q^{| h |} ~,

状态: 已规范化 ✓
================================================================================

样本 899: im2latexv3_c8d92ec6bea7056
------------------------------------------------------------
输入:
\bar { \partial } J ^ { a } = - \eta \; \partial K ^ { a } \; + \; { \cal O } ( \epsilon ^ { 2 } ) ,

输出:
\bar{\partial} J^{a}=-\eta \;\partial K^{a} \;+\;{\cal O}(\epsilon^{2}),

状态: 已规范化 ✓
================================================================================

样本 900: im2latexv3_8c648b7467785e5
------------------------------------------------------------
输入:
\gamma ( e ) = d i a g ( 0 , e ^ { 2 \pi i / N } , . . . , e ^ { 2 \pi i ( N - 1 ) / N } )

输出:
\gamma(e)=d i a g(0,e^{2 \pi i/N},...,e^{2 \pi i(N-1)/N})

状态: 已规范化 ✓
================================================================================

样本 901: im2latexv3_62b8e4d6858abbe
------------------------------------------------------------
输入:
Q _ { s } = \int _ { \Sigma } ^ { } ( \nabla ^ { \ast } E ) _ { s } = \int _ { \Sigma } ^ { } \tilde { K } _ { s } \in \hbar \cdot { \bf Z } \; ,

输出:
Q_{s}=\int_{\Sigma}^(\nabla^{\ast} E)_{s}=\int_{\Sigma}^ \tilde{K}_{s} \in \hbar \cdot{\bf Z} \;,

状态: 已规范化 ✓
================================================================================

样本 902: im2latexv3_23279e44cd430ed
------------------------------------------------------------
输入:
M = T \pm 2 \quad \mathrm { a n d } \quad N = Y , \qquad \mathrm { f o r } \quad | T | \ge | Y |

输出:
M=T \pm 2 \quad \mathrm{and} \quad N=Y,\qquad \mathrm{for} \quad | T | \ge | Y |

状态: 已规范化 ✓
================================================================================

样本 903: im2latexv3_45c3ab8197ce96d
------------------------------------------------------------
输入:
\phi ^ { \alpha } ( x ^ { 0 } , x ^ { 1 } ) \longrightarrow \phi ^ { \alpha } ( x ^ { 0 } , x ^ { 1 } ) \, + \, f ^ { \alpha } ( x ^ { 0 } ) ,

输出:
\phi^{\alpha}(x^{0},x^{1}) \longrightarrow \phi^{\alpha}(x^{0},x^{1}) \,+\,f^{\alpha}(x^{0}),

状态: 已规范化 ✓
================================================================================

样本 904: im2latexv3_f2ed51debbc4a41
------------------------------------------------------------
输入:
X = i ( q - q ^ { - 1 } ) ^ { - 1 } \bigl ( q ^ { ( \hat { z } + 1 / 2 ) } - q ^ { - ( \hat { z } + 1 / 2 ) } \bigr ) \hat { p } ^ { - 1 } .

输出:
X=i(q-q^{-1})^{-1} \bigl(q^{(\hat{z}+1/2)}-q^{-(\hat{z}+1/2)} \bigr) \hat{p}^{-1}.

状态: 已规范化 ✓
================================================================================

样本 905: im2latexv3_b29ac76af448a21
------------------------------------------------------------
输入:
\langle \, , \, \rangle : ( a , u ) \rightarrow \langle a , u \rangle \quad \forall \, a \in { \cal A } \, , \quad \forall \, u \in { \cal U } \, ,

输出:
\langle \,,\,\rangle:(a,u) \rightarrow \langle a,u \rangle \quad \forall \,a \in{\cal A} \,,\quad \forall \,u \in{\cal U} \,,

状态: 已规范化 ✓
================================================================================

样本 906: im2latexv3_8eee61c7947cdb3
------------------------------------------------------------
输入:
F _ { \theta \phi } = \sqrt { B ^ { 2 } - \tau _ { 0 } ^ { 4 } } s i n h \theta .

输出:
F_{\theta \phi}=\sqrt{B^{2}-\tau_{0}^{4}} s i n h \theta.

状态: 已规范化 ✓
================================================================================

样本 907: im2latexv3_9c4e6ed8a5ff1a7
------------------------------------------------------------
输入:
\frac { d } { d x } \left( \frac { \delta F } { \delta \partial _ { x } T } \right) - \frac { d F } { d T } = 0

输出:
\frac{d}{d x} \left(\frac{\delta F}{\delta \partial_{x} T} \right)-\frac{d F}{d T}=0

状态: 已规范化 ✓
================================================================================

样本 908: im2latexv3_ec5fc9177c462a9
------------------------------------------------------------
输入:
\Pi _ { \mu \nu } \big | _ { \mathrm { o d d } } = g ^ { 2 } c _ { v } \frac { 7 } { 1 2 \pi } \epsilon _ { \mu \rho \nu } p ^ { \rho } .

输出:
\Pi_{\mu \nu} \big|_{\mathrm{odd}}=g^{2} c_{v} \frac{7}{1 2 \pi} \epsilon_{\mu \rho \nu} p^{\rho}.

状态: 已规范化 ✓
================================================================================

样本 909: im2latexv3_a429ed52b6dba42
------------------------------------------------------------
输入:
\frac { D ( a ) } { a } = { } _ { 2 } F _ { 1 } \left[ - \frac { 1 } { 3 w } , \frac { w - 1 } { 2 w } , 1 - \frac { 5 } { 6 w } , - a ^ { - 3 w } \frac { 1 - \Omega _ { N R } } { \Omega _ { N R } } \right]

输出:
\frac{D(a)}{a}=_{2} F_{1} \left[-\frac{1}{3 w},\frac{w-1}{2 w},1-\frac{5}{6 w},-a^{-3 w} \frac{1-\Omega_{N R}}{\Omega_{N R}} \right]

状态: 已规范化 ✓
================================================================================

样本 910: im2latexv3_8a6eec775eb1b74
------------------------------------------------------------
输入:
\delta C _ { k } ^ { ( 2 k + 2 ) } [ f ] = 0 \mathrm { ~ a s ~ } g _ { \mu \nu } \rightarrow e ^ { 2 \delta \omega } g _ { \mu \nu } , \: \: \: f \rightarrow e ^ { - 2 \delta \omega } f

输出:
\delta C_{k}^{(2 k+2)}[f]=0 \mathrm{~as~} g_{\mu \nu} \rightarrow e^{2 \delta \omega} g_{\mu \nu},\:\:\:f \rightarrow e^{-2 \delta \omega} f

状态: 已规范化 ✓
================================================================================

样本 911: im2latexv3_45d7135fe8a1f5d
------------------------------------------------------------
输入:
\Omega _ { \left( \mu \nu \xi \right) } = g _ { \alpha \beta } \Omega _ { \left( \mu \nu \xi \right) } ^ { \left\{ \alpha \beta \right\} } \, .

输出:
\Omega_{\left(\mu \nu \xi \right)}=g_{\alpha \beta} \Omega_{\left(\mu \nu \xi \right)}^{\left\{\alpha \beta \right\}} \,.

状态: 已规范化 ✓
================================================================================

样本 912: im2latexv3_f672ccc0caa97ed
------------------------------------------------------------
输入:
\partial ( ( 1 / c ) E \wedge v + e _ { 5 } B \cdot v ) = j / \varepsilon _ { 0 } c .

输出:
\partial((1/c) E \wedge v+e_{5} B \cdot v)=j/\varepsilon_{0} c.

状态: 已规范化 ✓
================================================================================

样本 913: im2latexv3_c4c924f0453df2b
------------------------------------------------------------
输入:
\left( \omega _ { \alpha \beta } \right) = \left( \begin{array} { c c c } { g _ { \mu \nu } } & { 0 } & { 0 } \\ { 0 } & { 0 } & { - 1 } \\ { 0 } & { - 1 } & { 0 } \\ \end{array} \right) ,

输出:
\left(\omega_{\alpha \beta} \right)=\left(\begin{array}{ccc}{{g_{\mu \nu}}} &{{0}} &{{0}} \{{0}} &{{0}} &{{-1}} \{{0}} &{{-1}} &{{0}} \end{array} \right),

状态: 已规范化 ✓
================================================================================

样本 914: im2latexv3_d172e6002b69f6e
------------------------------------------------------------
输入:
` ` \sigma _ { z } " \psi ( \bar { q } , \sigma ) = \sigma \psi ( \bar { q } , \sigma )

输出:
` ` \sigma_{z} " \psi(\bar{q},\sigma)=\sigma \psi(\bar{q},\sigma)

状态: 已规范化 ✓
================================================================================

样本 915: im2latexv3_2dec212b41438f5
------------------------------------------------------------
输入:
W ( \phi ) = - \frac { 2 ( d - 1 ) } { l } + { \frac { 1 } { 2 } } \sum _ { a } \lambda _ { a } \, \phi _ { a } ^ { 2 } + \frac { 1 } { 3 ! } \, \sum _ { a , b , c } \lambda _ { a b c } \, \phi _ { a } \phi _ { b } \phi _ { c } + \cdots ,

输出:
W(\phi)=-\frac{2(d-1)}{l}+{\frac{1}{2}} \sum_{a} \lambda_{a} \,\phi_{a}^{2}+\frac{1}{3 !} \,\sum_{a,b,c} \lambda_{a b c} \,\phi_{a} \phi_{b} \phi_{c}+. . .,

状态: 已规范化 ✓
================================================================================

样本 916: im2latexv3_c7960b64fe7277a
------------------------------------------------------------
输入:
t _ { a b } = \left( \begin{array} { c c } { 1 } & { \omega ^ { - 1 } } \\ { \omega } & { 1 } \\ \end{array} \right) .

输出:
t_{a b}=\left(\begin{array}{cc}{{1}} &{{\omega^{-1}}} \{{\omega}} &{{1}} \end{array} \right).

状态: 已规范化 ✓
================================================================================

样本 917: im2latexv3_0b8d56fb8a65f72
------------------------------------------------------------
输入:
S = \int d ^ { 2 } x \, G _ { a b } ( x ) \partial _ { \mu } \xi ^ { a } \partial ^ { \mu } \xi ^ { b } \, .

输出:
S=\int d^{2} x \,G_{a b}(x) \partial_{\mu} \xi^{a} \partial^{\mu} \xi^{b} \,.

状态: 已规范化 ✓
================================================================================

样本 918: im2latexv3_f883f04c2536bf1
------------------------------------------------------------
输入:
\{ \Gamma _ { \alpha } , \Gamma _ { \beta } \} = \{ \gamma _ { \alpha } , \gamma _ { \beta } \} = \eta _ { \alpha \beta }

输出:
\{\Gamma_{\alpha},\Gamma_{\beta} \}=\{\gamma_{\alpha},\gamma_{\beta} \}=\eta_{\alpha \beta}

状态: 已规范化 ✓
================================================================================

样本 919: im2latexv3_72a7edee8c73bcc
------------------------------------------------------------
输入:
\theta ( x , y , \tilde { x } , \tilde { y } , p , q ) = \Lambda ( x + \tilde { x } , y + \tilde { y } , p , q )

输出:
\theta(x,y,\tilde{x},\tilde{y},p,q)=\Lambda(x+\tilde{x},y+\tilde{y},p,q)

状态: 已规范化 ✓
================================================================================

样本 920: im2latexv3_6294d81058f05a9
------------------------------------------------------------
输入:
{ \tilde { R } } _ { m n } \, ^ { p q } \Gamma _ { p q } \eta _ { \pm } = 0 \, .

输出:
{\tilde{R}}_{m n} \,^{p q} \Gamma_{p q} \eta_{\pm}=0 \,.

状态: 已规范化 ✓
================================================================================

样本 921: im2latexv3_44c2d5dd5a0188c
------------------------------------------------------------
输入:
V _ { \alpha } ( \theta , \epsilon , \delta ) \Psi [ C ] \stackrel { \delta , \epsilon \rightarrow 0 } { \longrightarrow } v _ { \alpha } \left( C ( \theta ) \right) \Psi [ C ]

输出:
V_{\alpha}(\theta,\epsilon,\delta) \Psi[C] \stackrel{\delta{,} \epsilon \rightarrow 0}{\longrightarrow} v_{\alpha} \left(C(\theta) \right) \Psi[C]

状态: 已规范化 ✓
================================================================================

样本 922: im2latexv3_16513f1597397ff
------------------------------------------------------------
输入:
X _ { \alpha \beta } ^ { i j } = x _ { \alpha \beta } ^ { i j } ( p _ { a } \rightarrow \pi _ { a } ) + e y _ { \alpha \beta } ^ { i j }

输出:
X_{\alpha \beta}^{i j}=x_{\alpha \beta}^{i j}(p_{a} \rightarrow \pi_{a})+e y_{\alpha \beta}^{i j}

状态: 已规范化 ✓
================================================================================

样本 923: im2latexv3_9fb3f58f76f45f7
------------------------------------------------------------
输入:
\gamma _ { \Psi } a _ { 1 } = \gamma _ { \Psi } ( - b ^ { * } C ) = \overline { { C } } ^ { * } C .

输出:
\gamma_{\Psi} a_{1}=\gamma_{\Psi}(-b^{*} C)=\overline{{{C}}}^{*} C.

状态: 已规范化 ✓
================================================================================

样本 924: im2latexv3_2afe14b08e3dffa
------------------------------------------------------------
输入:
\mathrm { ~ \frac { 1 } { 2 } ~ } ( S _ { m ( 0 ) } , S _ { m ( 0 ) } ) ^ { a } + V _ { m } ^ { a } S _ { m ( 0 ) } = 0 , \qquad \mathrm { ~ \frac { 1 } { 2 } ~ } \{ S _ { m ( 0 ) } , S _ { m ( 0 ) } \} _ { \alpha } + V _ { \alpha } S _ { m ( 0 ) } = 0 .

输出:
\mathrm{~\frac{1}{2} ~}(S_{m(0)},S_{m(0)})^{a}+V_{m}^{a} S_{m(0)}=0,\qquad \mathrm{~\frac{1}{2} ~} \{S_{m(0)},S_{m(0)} \}_{\alpha}+V_{\alpha} S_{m(0)}=0.

状态: 已规范化 ✓
================================================================================

样本 925: im2latexv3_7a5ae451ab02bb0
------------------------------------------------------------
输入:
B _ { [ J ] i a } = { \frac { S _ { i a } } { \sqrt { S _ { i J } } } } \; \; .

输出:
B_{[J] i a}={\frac{S_{i a}}{\sqrt{S_{i J}}}} \;\;.

状态: 已规范化 ✓
================================================================================

样本 926: im2latexv3_74bd285dcb83b11
------------------------------------------------------------
输入:
( E _ { 2 s } - Q - Q ^ { - 1 } ) A ( k ) = A ( k - 1 ) + A ( k + 1 ) , \quad 2 \leq k \leq N - 2

输出:
(E_{2 s}-Q-Q^{-1}) A(k)=A(k-1)+A(k+1),\quad 2 \leq k \leq N-2

状态: 已规范化 ✓
================================================================================

样本 927: im2latexv3_274f20222c40c3a
------------------------------------------------------------
输入:
( \psi _ { f } ^ { + } ( { \bf 1 } ) _ { \alpha \beta } ) = \epsilon _ { \alpha \beta } \eta _ { f } ^ { + } ( { \bf 1 } ) ,

输出:
(\psi_{f}^{+}({\bf 1})_{\alpha \beta})=\epsilon_{\alpha \beta} \eta_{f}^{+}({\bf 1}),

状态: 已规范化 ✓
================================================================================

样本 928: im2latexv3_e1536b198193efa
------------------------------------------------------------
输入:
x _ { \mu } ^ { \prime 2 } = x ^ { \prime } \overline { { x } } ^ { \prime } = L x \overline { { L } } ^ { * } L ^ { * } \overline { { x } } \overline { { L } } = x \overline { { x } } = x _ { \mu } ^ { 2 } ,

输出:
x_{\mu}^{\prime 2}=x^{\prime} \overline{{{x}}}^{\prime}=L x \overline{{{L}}}^{*} L^{*} \overline{{{x}}} \overline{{{L}}}=x \overline{{{x}}}=x_{\mu}^{2},

状态: 已规范化 ✓
================================================================================

样本 929: im2latexv3_b087319683c82d5
------------------------------------------------------------
输入:
f _ { 0 } = - \frac { 3 } { 2 } z ( 1 - z ^ { 2 } ) , \; f _ { 1 } = \frac { 3 } { 2 } ( 1 - z ^ { 2 } ) , \; f _ { 2 } = \frac { 9 } { 4 } z ^ { 2 } ( 1 - \frac { 1 } { 3 } z ^ { 2 } ) ^ { 2 } - 1 .

输出:
f_{0}=-\frac{3}{2} z(1-z^{2}),\;f_{1}=\frac{3}{2}(1-z^{2}),\;f_{2}=\frac{9}{4} z^{2}(1-\frac{1}{3} z^{2})^{2}-1.

状态: 已规范化 ✓
================================================================================

样本 930: im2latexv3_28d1dc07ab4b0d8
------------------------------------------------------------
输入:
b _ { \alpha } = x ^ { \mu } \left( \gamma _ { \mu } \right) _ { \alpha } ^ { \beta } a _ { \beta } .

输出:
b_{\alpha}=x^{\mu} \left(\gamma_{\mu} \right)_{\alpha}^{\beta} a_{\beta}.

状态: 已规范化 ✓
================================================================================

样本 931: im2latexv3_ede4c988d752991
------------------------------------------------------------
输入:
{ \tilde { j } } _ { \mu } ( x ) = - { \tilde { g } } [ { \bar { \psi } } ( x ) \gamma _ { \mu } T ^ { i } \psi ( x ) ] \tau _ { i } ,

输出:
{\tilde{j}}_{\mu}(x)=-{\tilde{g}}[{\bar{\psi}}(x) \gamma_{\mu} T^{i} \psi(x)] \tau_{i},

状态: 已规范化 ✓
================================================================================

样本 932: im2latexv3_fb5d393835fcafa
------------------------------------------------------------
输入:
( \dot { x } _ { \mu } - v J _ { \mu } ) ^ { 2 } + e ^ { 2 } m ^ { 2 } = 0 , \quad \dot { x } J - v J ^ { 2 } - s m e = 0 .

输出:
(\dot{x}_{\mu}-v J_{\mu})^{2}+e^{2} m^{2}=0,\quad \dot{x} J-v J^{2}-s m e=0.

状态: 已规范化 ✓
================================================================================

样本 933: im2latexv3_131d03d473cd4a3
------------------------------------------------------------
输入:
\left( \vec { \sigma } + \vec { \tau } \right) \Xi = 0

输出:
\left(\vec{\sigma}+\vec{\tau} \right) \Xi=0

状态: 已规范化 ✓
================================================================================

样本 934: im2latexv3_d2f35944920f032
------------------------------------------------------------
输入:
s _ { n } \rightarrow { \frac { 1 } { 4 n } } e ^ { - ( 1 + \sqrt { \mu } ) \bar { A } _ { + } n } ~ ~ i f ~ ~ A _ { + } \rightarrow \infty

输出:
s_{n} \rightarrow{\frac{1}{4 n}} e^{-(1+\sqrt{\mu}) \bar{A}_{+} n} ~ ~ i f ~ ~ A_{+} \rightarrow \infty

状态: 已规范化 ✓
================================================================================

样本 935: im2latexv3_746cc95e3d773ae
------------------------------------------------------------
输入:
\begin{array} { l l } { ( 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ - \frac { 7 } { 2 } ) \longrightarrow } & { ~ ( 0 ~ 0 ~ 0 ) ( 0 ~ 0 ~ 0 ~ - \frac { 7 } { 2 } ) ( 2 ) \oplus ( 0 ~ 0 ~ 1 ) ( 0 ~ 0 ~ 0 ~ - \frac { 5 } { 2 } ) ( 1 ) } \\ { } & { \oplus ~ ( 0 ~ 1 ~ 0 ) ( 0 ~ 0 ~ 0 ~ - \frac { 3 } { 2 } ) ( 0 ) \oplus ( 1 ~ 0 ~ 0 ) ( 0 ~ 0 ~ 0 ~ - \frac { 1 } { 2 } ) ( - 1 ) } \\ { } & { \oplus ~ ( 0 ~ 0 ~ 0 ) ( 0 ~ 0 ~ 0 ~ \frac { 1 } { 2 } ) ( - 2 ) , } \\ \end{array}

输出:
\begin{array}{ll}{{(0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~-\frac{7}{2}) \longrightarrow}} &{{~(0 ~ 0 ~ 0)(0 ~ 0 ~ 0 ~-\frac{7}{2})(2) \oplus(0 ~ 0 ~ 1)(0 ~ 0 ~ 0 ~-\frac{5}{2})(1)}} \ &{{\oplus ~(0 ~ 1 ~ 0)(0 ~ 0 ~ 0 ~-\frac{3}{2})(0) \oplus(1 ~ 0 ~ 0)(0 ~ 0 ~ 0 ~-\frac{1}{2})(-1)}} \ &{{\oplus ~(0 ~ 0 ~ 0)(0 ~ 0 ~ 0 ~ \frac{1}{2})(-2),}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 936: im2latexv3_7413938855204cf
------------------------------------------------------------
输入:
E _ { n } \sim p _ { - } ^ { 2 } \tilde { \mu } ^ { 2 } L ^ { 2 } , \ \ \ \ \mathrm { f o r } \ \ p _ { - } ^ { 2 } | \mu ^ { 2 } | L ^ { 4 } \gg n ^ { 2 } ,

输出:
E_{n} \sim p_{-}^{2} \tilde{\mu}^{2} L^{2},\mathrm{for} p_{-}^{2} | \mu^{2} | L^{4} \gg n^{2},

状态: 已规范化 ✓
================================================================================

样本 937: im2latexv3_11f44ef8f3a10a2
------------------------------------------------------------
输入:
S _ { ( n ) } = \int d ^ { 4 } x \Bigl [ \frac { 1 } { 6 } \, H _ { ( n ) } ^ { \mu \nu \rho } H _ { ( n ) \mu \nu \rho } ^ { \ast } - \frac { 2 n ^ { 2 } \pi ^ { 2 } } { R ^ { 2 } } \Bigl ( i B _ { ( n ) } ^ { \mu \nu } - \frac { R } { 2 n \pi } \, F _ { ( n ) } ^ { \mu \nu } \Bigr ) \Bigl ( - \, i B _ { ( n ) \mu \nu } ^ { \ast } - \frac { R } { 2 n \pi } \, F _ { ( n ) \mu \nu } ^ { \ast } \Bigr ) \Bigr ]

输出:
S_{(n)}=\int d^{4} x \Bigl[\frac{1}{6} \,H_{(n)}^{\mu \nu \rho} H_{(n) \mu \nu \rho}^{\ast}-\frac{2 n^{2} \pi^{2}}{R^{2}} \Bigl(i B_{(n)}^{\mu \nu}-\frac{R}{2 n \pi} \,F_{(n)}^{\mu \nu} \Bigr) \Bigl(-\,i B_{(n) \mu \nu}^{\ast}-\frac{R}{2 n \pi} \,F_{(n) \mu \nu}^{\ast} \Bigr) \Bigr]

状态: 已规范化 ✓
================================================================================

样本 938: im2latexv3_0bf1383521ba746
------------------------------------------------------------
输入:
\begin{array} { c } { \{ A _ { i } , \underline { { g } } ( \xi ) \} = - \partial _ { i } \xi \ , } \\ { \{ E _ { i } , \underline { { g } } ( \xi ) \} = 0 \ , } \\ { \{ \psi , \underline { { g } } ( \xi ) \} = \xi \psi \ . } \\ \end{array}

输出:
\begin{array}{c}{{\{A_{i},\underline{{{g}}}(\xi) \}=-\partial_{i} \xi,}} \{{\{E_{i},\underline{{{g}}}(\xi) \}=0,}} \{{\{\psi,\underline{{{g}}}(\xi) \}=\xi \psi.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 939: im2latexv3_05edcd669ebe910
------------------------------------------------------------
输入:
\left\langle w ^ { \alpha } ( T ) \right\rangle ^ { 1 / \alpha } = \frac { \left\langle w ^ { \alpha } ( 1 ) \right\rangle ^ { 1 / \alpha ( 0 ) } } { L ^ { p } }

输出:
\left\langle w^{\alpha}(T) \right\rangle^{1/\alpha}=\frac{\left\langle w^{\alpha}(1) \right\rangle^{1/\alpha(0)}}{L^{p}}

状态: 已规范化 ✓
================================================================================

样本 940: im2latexv3_6eb95ce68aa0c18
------------------------------------------------------------
输入:
\sigma = { \frac { 1 } { 4 5 } } \left( - N _ { 0 } - \frac 7 4 N _ { 1 / 2 } + 1 3 N _ { 1 } + { \frac { 2 3 3 } { 4 } } N _ { 3 / 2 } - 2 1 2 N _ { 2 } \right) ~ ~ ~ .

输出:
\sigma={\frac{1}{4 5}} \left(-N_{0}-\frac{7}{4} N_{1/2}+1 3 N_{1}+{\frac{2 3 3}{4}} N_{3/2}-2 1 2 N_{2} \right) ~ ~ ~.

状态: 已规范化 ✓
================================================================================

样本 941: im2latexv3_78a201afe9b47e8
------------------------------------------------------------
输入:
y ^ { 2 } = x ^ { 3 } + \sum _ { k = - 4 } ^ { 4 } \sum _ { l = 0 } ^ { 1 2 - n k } f _ { k l } z ^ { \prime l } z ^ { 4 + k } \ldots a ^ { 4 - 6 k } x + \sum _ { k = - 6 } ^ { 6 } \sum _ { l = 0 } ^ { 1 2 - n k } g _ { k l } z ^ { \prime l } z ^ { 6 + k } \ldots a ^ { 6 - 6 k }

输出:
y^{2}=x^{3}+\sum_{k=-4}^{4} \sum_{l=0}^{1 2-n k} f_{k l} z^{\prime l} z^{4+k} . . . a^{4-6 k} x+\sum_{k=-6}^{6} \sum_{l=0}^{1 2-n k} g_{k l} z^{\prime l} z^{6+k} . . . a^{6-6 k}

状态: 已规范化 ✓
================================================================================

样本 942: im2latexv3_a52f0610f922da2
------------------------------------------------------------
输入:
B ( x ) = \sum _ { i } B _ { i } ^ { l o c } ( x )

输出:
B(x)=\sum_{i} B_{i}^{l o c}(x)

状态: 已规范化 ✓
================================================================================

样本 943: im2latexv3_a4693acb6dfde6a
------------------------------------------------------------
输入:
p _ { i } = \Omega _ { i \, i _ { 1 } i _ { 2 } i _ { 3 } i _ { 4 } } \epsilon _ { i _ { 1 } i _ { 2 } i _ { 3 } i _ { 4 } j _ { 1 } j _ { 2 } j _ { 3 } j _ { 4 } } c _ { j _ { 1 } } c _ { j _ { 2 } } c _ { j _ { 3 } } c _ { j _ { 4 } } \quad ,

输出:
p_{i}=\Omega_{i \,i_{1} i_{2} i_{3} i_{4}} \epsilon_{i_{1} i_{2} i_{3} i_{4} j_{1} j_{2} j_{3} j_{4}} c_{j_{1}} c_{j_{2}} c_{j_{3}} c_{j_{4}} \quad,

状态: 已规范化 ✓
================================================================================

样本 944: im2latexv3_971edda7271e549
------------------------------------------------------------
输入:
U ^ { I } ( i ) = P e x p ( \int _ { s ( i ) } ^ { t ( i ) } A ^ { I } ) .

输出:
U^{I}(i)=P e x p(\int_{s(i)}^{t(i)} A^{I}).

状态: 已规范化 ✓
================================================================================

样本 945: im2latexv3_fb49545fc287733
------------------------------------------------------------
输入:
x _ { 1 } ^ { 2 } + x _ { 2 } ^ { 2 } + x _ { 3 } ^ { 2 } = 1

输出:
x_{1}^{2}+x_{2}^{2}+x_{3}^{2}=1

状态: 已规范化 ✓
================================================================================

样本 946: im2latexv3_d5274b06fca7530
------------------------------------------------------------
输入:
2 M _ { * } ^ { 4 } ( R _ { A B } - g _ { A B } R ) = - \frac { 1 } { 2 } g _ { A B } \Lambda _ { B } + 8 \pi \delta ( r - a ) T _ { A B } ^ { a } + 8 \pi \delta ( r - b ) T _ { A B } ^ { b } \ .

输出:
2 M_{*}^{4}(R_{A B}-g_{A B} R)=-\frac{1}{2} g_{A B} \Lambda_{B}+8 \pi \delta(r-a) T_{A B}^{a}+8 \pi \delta(r-b) T_{A B}^{b}.

状态: 已规范化 ✓
================================================================================

样本 947: im2latexv3_026d8126abcb1e4
------------------------------------------------------------
输入:
I _ { e l } = I - \frac { 1 } { 4 \pi } \int _ { \Sigma } d ^ { 3 } x \sqrt { h } F ^ { \mu \nu } n _ { \mu } A _ { \nu } ,

输出:
I_{e l}=I-\frac{1}{4 \pi} \int_{\Sigma} d^{3} x \sqrt{h} F^{\mu \nu} n_{\mu} A_{\nu},

状态: 已规范化 ✓
================================================================================

样本 948: im2latexv3_f4de1e35bc8c4c7
------------------------------------------------------------
输入:
\gamma _ { \Omega h } ^ { T } = \pm \gamma _ { \Omega h } .

输出:
\gamma_{\Omega h}^{T}=\pm \gamma_{\Omega h}.

状态: 已规范化 ✓
================================================================================

样本 949: im2latexv3_4ab11ef34e306aa
------------------------------------------------------------
输入:
\left[ \frac { 1 } { \sqrt { - g } } \frac { \partial } { \partial x ^ { i } } \left( \sqrt { - g } g ^ { i j } \frac { \partial } { \partial x ^ { i } } \right) - m _ { 0 } ^ { 2 } \right] \Phi ( x ) = 0 ~ .

输出:
\left[\frac{1}{\sqrt{-g}} \frac{\partial}{\partial x^{i}} \left(\sqrt{-g} g^{i j} \frac{\partial}{\partial x^{i}} \right)-m_{0}^{2} \right] \Phi(x)=0 ~.

状态: 已规范化 ✓
================================================================================

样本 950: im2latexv3_7a93c8433e5b611
------------------------------------------------------------
输入:
f _ { k } ( \rho ) = \left\{ \begin{array} { l l } { C _ { k , n } . e ^ { - \frac { \rho ^ { 2 } } { 2 } } \rho ^ { k } L _ { n } ^ { k } ( \rho ^ { 2 } ) } & { \textrm { s i k \geq 0 } } \\ { C _ { k , n } . e ^ { - \frac { \rho ^ { 2 } } { 2 } } \rho ^ { - k } L _ { n + k } ^ { - k } ( \rho ^ { 2 } ) } & { \textrm { s i - n \leq k < 0 } \ } \\ \end{array} \right. .

输出:
f_{k}(\rho)=\left\{\begin{array}{ll}{{C_{k,n}.e^{-\frac{\rho^{2}}{2}} \rho^{k} L_{n}^{k}(\rho^{2})}} &{{\textrm{s i k \geq 0}}} \{{C_{k,n}.e^{-\frac{\rho^{2}}{2}} \rho^{-k} L_{n+k}^{-k}(\rho^{2})}} &{{\textrm{s i-n \leq k<0}}} \end{array} \right..

状态: 已规范化 ✓
================================================================================

样本 951: im2latexv3_e88ab427406176d
------------------------------------------------------------
输入:
\frac { 1 } { 2 \pi } \varepsilon ^ { \mu \nu \sigma } \partial _ { \mu } \mathrm { T r }

输出:
\frac{1}{2 \pi} \varepsilon^{\mu \nu \sigma} \partial_{\mu} \mathrm{Tr}

状态: 已规范化 ✓
================================================================================

样本 952: im2latexv3_8f5d574e546963c
------------------------------------------------------------
输入:
d s _ { ( b _ { 0 } , \bar { b } _ { 0 } ) } ^ { 2 } = l ^ { 2 } \left[ - d \tau ^ { 2 } + \frac { b _ { 0 } } { c / 6 } \frac { d z ^ { 2 } } { z ^ { 2 } } + \frac { \bar { b } _ { 0 } } { c / 6 } \frac { d \bar { z } ^ { 2 } } { \bar { z } ^ { 2 } } + \left( e ^ { - 2 \tau } + \frac { b _ { 0 } \bar { b } _ { 0 } } { ( c / 6 ) ^ { 2 } z ^ { 2 } \bar { z } ^ { 2 } } e ^ { 2 \tau } \right) d z d \bar { z } \right] ,

输出:
d s_{(b_{0},\bar{b}_{0})}^{2}=l^{2} \left[-d \tau^{2}+\frac{b_{0}}{c/6} \frac{d z^{2}}{z^{2}}+\frac{\bar{b}_{0}}{c/6} \frac{d \bar{z}^{2}}{\bar{z}^{2}}+\left(e^{-2 \tau}+\frac{b_{0} \bar{b}_{0}}{(c/6)^{2} z^{2} \bar{z}^{2}} e^{2 \tau} \right) d z d \bar{z} \right],

状态: 已规范化 ✓
================================================================================

样本 953: im2latexv3_3c906b26b96df7e
------------------------------------------------------------
输入:
\left[ \hat { M } _ { \hat { a } \hat { b } } , \hat { M } _ { \hat { c } \hat { d } } \right] = - \hat { \eta } _ { \hat { a } \hat { c } } \hat { M } _ { \hat { b } \hat { d } } - \hat { \eta } _ { \hat { b } \hat { d } } \hat { M } _ { \hat { a } \hat { c } } + \hat { \eta } _ { \hat { a } \hat { d } } \hat { M } _ { \hat { b } \hat { c } } + \hat { \eta } _ { \hat { b } \hat { c } } \hat { M } _ { \hat { a } \hat { d } } \, .

输出:
\left[\hat{M}_{\hat{a} \hat{b}},\hat{M}_{\hat{c} \hat{d}} \right]=-\hat{\eta}_{\hat{a} \hat{c}} \hat{M}_{\hat{b} \hat{d}}-\hat{\eta}_{\hat{b} \hat{d}} \hat{M}_{\hat{a} \hat{c}}+\hat{\eta}_{\hat{a} \hat{d}} \hat{M}_{\hat{b} \hat{c}}+\hat{\eta}_{\hat{b} \hat{c}} \hat{M}_{\hat{a} \hat{d}} \,.

状态: 已规范化 ✓
================================================================================

样本 954: im2latexv3_e583d05fff1b3bd
------------------------------------------------------------
输入:
\partial _ { 0 } \psi ^ { \alpha } ( x ) = \{ \psi ^ { \alpha } , H \} _ { D } = M _ { \alpha \beta } \partial _ { 1 } \psi ^ { \beta } ( x ) \, ,

输出:
\partial_{0} \psi^{\alpha}(x)=\{\psi^{\alpha},H \}_{D}=M_{\alpha \beta} \partial_{1} \psi^{\beta}(x) \,,

状态: 已规范化 ✓
================================================================================

样本 955: im2latexv3_458ad42abb4abb4
------------------------------------------------------------
输入:
\delta _ { j _ { 1 } 0 } \delta _ { j _ { 2 } 0 } = \int _ { 0 } ^ { \frac { 2 \pi } { l } } d k \int _ { 0 } ^ { l / A } d q e ^ { - i j _ { 1 } l k + i j _ { 2 } l \tau _ { 2 } q } | \tilde { c } ( k , q ) | ^ { 2 } \sum _ { n = 0 } ^ { A - 1 } | C _ { 0 } ( k , q + \frac { l n } { A } ) | ^ { 2 } .

输出:
\delta_{j_{1} 0} \delta_{j_{2} 0}=\int_{0}^{\frac{2 \pi}{l}} d k \int_{0}^{l/A} d q e^{-i j_{1} l k+i j_{2} l \tau_{2} q} | \tilde{c}(k,q) |^{2} \sum_{n=0}^{A-1} | C_{0}(k,q+\frac{l n}{A}) |^{2}.

状态: 已规范化 ✓
================================================================================

样本 956: im2latexv3_527675eea23c5ba
------------------------------------------------------------
输入:
\begin{array} { c c c c } { z : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , e ^ { 2 \pi i z } x ^ { 6 , 7 } , e ^ { - 2 \pi i z } x ^ { 8 , 9 } \right) } \\ { y : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( e ^ { 2 \pi i y } x ^ { 2 , 3 } , e ^ { - 2 \pi i y } x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } \\ { x : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( e ^ { - 2 \pi i x } x ^ { 2 , 3 } , x ^ { 4 , 5 } , e ^ { 2 \pi i x } x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } \\ { w : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , e ^ { - 2 \pi i w } x ^ { 6 , 7 } , e ^ { - 2 \pi i w } x ^ { 8 , 9 } \right) } \\ \end{array}

输出:
\begin{array}{cccc}{{z:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(x^{2,3},x^{4,5},e^{2 \pi i z} x^{6,7},e^{-2 \pi i z} x^{8,9} \right)}} \{{y:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(e^{2 \pi i y} x^{2,3},e^{-2 \pi i y} x^{4,5},x^{6,7},x^{8,9} \right)}} \{{x:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(e^{-2 \pi i x} x^{2,3},x^{4,5},e^{2 \pi i x} x^{6,7},x^{8,9} \right)}} \{{w:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(x^{2,3},x^{4,5},e^{-2 \pi i w} x^{6,7},e^{-2 \pi i w} x^{8,9} \right)}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 957: im2latexv3_06807ffb0e6b154
------------------------------------------------------------
输入:
H _ { 1 } = 1 + { \frac { g \alpha ^ { \prime } N _ { 1 } } { v r ^ { 2 } } } , \ \ \ \ H _ { 5 } = 1 + { \frac { g \alpha ^ { \prime } N _ { 5 } } { r ^ { 2 } } } ,

输出:
H_{1}=1+{\frac{g \alpha^{\prime} N_{1}}{v r^{2}}},H_{5}=1+{\frac{g \alpha^{\prime} N_{5}}{r^{2}}},

状态: 已规范化 ✓
================================================================================

样本 958: im2latexv3_29f463de94e3584
------------------------------------------------------------
输入:
\frac { \partial ^ { [ 2 ( n - l ) / l ] } } { \partial x _ { i _ { 1 } , 1 } \dots \partial x _ { i _ { [ 2 ( n - l ) / l ] } , 1 } } ,

输出:
\frac{\partial^{[2(n-l)/l]}}{\partial x_{i_{1},1} . . . \partial x_{i_{[2(n-l)/l]},1}},

状态: 已规范化 ✓
================================================================================

样本 959: im2latexv3_28e282bcda401fa
------------------------------------------------------------
输入:
{ \cal V } _ { R } = \partial X ( z ) \; \bar { \partial } \widetilde { X } ( \bar { z } ) ~ .

输出:
{\cal V}_{R}=\partial X(z) \;\bar{\partial} \widetilde{X}(\bar{z}) ~.

状态: 已规范化 ✓
================================================================================

样本 960: im2latexv3_b155421bd905143
------------------------------------------------------------
输入:
\left[ i \gamma ^ { \mu } \partial _ { \mu } - m \right] \Psi ( x ^ { \mu } ) = 0 \quad , \quad \hbar = c = 1 \quad ,

输出:
\left[i \gamma^{\mu} \partial_{\mu}-m \right] \Psi(x^{\mu})=0 \quad,\quad \hbar=c=1 \quad,

状态: 已规范化 ✓
================================================================================

样本 961: im2latexv3_98830bae044f1d7
------------------------------------------------------------
输入:
{ { M _ { V } } _ { \alpha } } ^ { \beta } = \delta _ { \alpha } ^ { \beta } ( - \Box ) - { R _ { \alpha } } ^ { \beta } \ .

输出:
{{M_{V}}_{\alpha}}^{\beta}=\delta_{\alpha}^{\beta}(-\Box)-{R_{\alpha}}^{\beta}.

状态: 已规范化 ✓
================================================================================

样本 962: im2latexv3_88de90a94d25015
------------------------------------------------------------
输入:
2 \kappa ^ { 2 } e ^ { - 1 } L = R + 4 m ^ { 2 } e ^ { ( 2 \lambda _ { 1 } + 2 \lambda _ { 2 } ) } - 5 \partial _ { \mu } ( \lambda _ { 1 } + \lambda _ { 2 } ) ^ { 2 } - \partial _ { \mu } ( \lambda _ { 1 } - \lambda _ { 2 } ) ^ { 2 } - e ^ { - 4 \lambda _ { 1 } } { F _ { \mu \nu } ^ { ( 1 ) } } ^ { 2 } - e ^ { - 4 \lambda _ { 2 } } { F _ { \mu \nu } ^ { ( 2 ) } } ^ { 2 } .

输出:
2 \kappa^{2} e^{-1} L=R+4 m^{2} e^{(2 \lambda_{1}+2 \lambda_{2})}-5 \partial_{\mu}(\lambda_{1}+\lambda_{2})^{2}-\partial_{\mu}(\lambda_{1}-\lambda_{2})^{2}-e^{-4 \lambda_{1}}{F_{\mu \nu}^{(1)}}^{2}-e^{-4 \lambda_{2}}{F_{\mu \nu}^{(2)}}^{2}.

状态: 已规范化 ✓
================================================================================

样本 963: im2latexv3_1ad600a81bf0f1b
------------------------------------------------------------
输入:
\hat { H } = \frac { 1 } { 2 } \hat { p } ^ { 2 } + \frac { m ^ { 2 } } { 2 } \hat { q } ^ { 2 } + \frac { \lambda } { 4 } \hat { q } ^ { 4 } .

输出:
\hat{H}=\frac{1}{2} \hat{p}^{2}+\frac{m^{2}}{2} \hat{q}^{2}+\frac{\lambda}{4} \hat{q}^{4}.

状态: 已规范化 ✓
================================================================================

样本 964: im2latexv3_eadb4fe4997f64d
------------------------------------------------------------
输入:
M _ { B P S } ^ { 2 } = { \frac { 1 } { 8 g _ { s } ^ { 2 } } } \vec { Q } _ { R } ^ { 2 } ,

输出:
M_{B P S}^{2}={\frac{1}{8 g_{s}^{2}}} \vec{Q}_{R}^{2},

状态: 已规范化 ✓
================================================================================

样本 965: im2latexv3_343bfd963f84112
------------------------------------------------------------
输入:
\left\{ \not \! \partial ( \phi + \psi / 2 ) + e ^ { 3 \psi / 8 + \phi / 2 } \gamma _ { 7 } \left( { \bf F } ^ { 1 } + \kappa { \bf F } ^ { 2 } \right) - i s _ { z } e ^ { \phi } \gamma _ { 7 } \not \! \partial \kappa \right\} \epsilon _ { 6 } = 0 ,

输出:
\left\{\not \! \partial(\phi+\psi/2)+e^{3 \psi/8+\phi/2} \gamma_{7} \left({\bf F}^{1}+\kappa{\bf F}^{2} \right)-i s_{z} e^{\phi} \gamma_{7} \not \! \partial \kappa \right\} \epsilon_{6}=0,

状态: 已规范化 ✓
================================================================================

样本 966: im2latexv3_13803a0d53f1b28
------------------------------------------------------------
输入:
\langle \mu \mu \mu \mu \rangle = | z _ { 1 2 } z _ { 3 4 } | ^ { \frac 1 2 } | x ( 1 - x ) | ^ { \frac 1 2 } \left( F ( x ) \overline { { F ( 1 - x ) } } + F ( 1 - x ) \overline { { F ( x ) } } \right) ,

输出:
\langle \mu \mu \mu \mu \rangle=| z_{1 2} z_{3 4} |^{\frac{1}{2}} | x(1-x) |^{\frac{1}{2}} \left(F(x) \overline{{{F(1-x)}}}+F(1-x) \overline{{{F(x)}}} \right),

状态: 已规范化 ✓
================================================================================

样本 967: im2latexv3_f1fc68baeabbb80
------------------------------------------------------------
输入:
V _ { Q } ^ { \prime } \, = \, \frac { { \hbar } ^ { 2 } } { 8 M } \left\{ R + \frac { ( \partial _ { a } f ) ( \partial ^ { a } f ) } { 4 M } \left[ 3 g _ { \phantom { 0 } , 0 } ^ { i j } \, g _ { i j , 0 } - { \left( g ^ { i j } \, g _ { i j , 0 } \right) } ^ { 2 } \right] \right\} .

输出:
V_{Q}^{\prime} \,=\,\frac{{\hbar}^{2}}{8 M} \left\{R+\frac{(\partial_{a} f)(\partial^{a} f)}{4 M} \left[3 g_{\phantom{0},0}^{i j} \,g_{i j,0}-{\left(g^{i j} \,g_{i j,0} \right)}^{2} \right] \right\}.

状态: 已规范化 ✓
================================================================================

样本 968: im2latexv3_2f204600dea9ecd
------------------------------------------------------------
输入:
M _ { S } = \widetilde { M } _ { S } + i { \cal A } \times I ,

输出:
M_{S}=\widetilde{M}_{S}+i{\cal A} \times I,

状态: 已规范化 ✓
================================================================================

样本 969: im2latexv3_5e4f0303433f01c
------------------------------------------------------------
输入:
\Box _ { q } \, l _ { q } = q ^ { - 4 } l _ { q } \, \Box _ { q } + q ^ { - 2 } s + ( q ^ { 2 } + 1 ) \; .

输出:
\Box_{q} \,l_{q}=q^{-4} l_{q} \,\Box_{q}+q^{-2} s+(q^{2}+1) \;.

状态: 已规范化 ✓
================================================================================

样本 970: im2latexv3_e728dd505623144
------------------------------------------------------------
输入:
T _ { \mu \nu } ^ { a } = D _ { \mu } e _ { v } ^ { a } - D _ { v } e _ { \mu } ^ { a } .

输出:
T_{\mu \nu}^{a}=D_{\mu} e_{v}^{a}-D_{v} e_{\mu}^{a}.

状态: 已规范化 ✓
================================================================================

样本 971: im2latexv3_b1ce5f3d0e1c798
------------------------------------------------------------
输入:
\gamma ^ { i } M = \xi ^ { i } M \gamma ^ { i } , \quad M ^ { T } = \eta M ,

输出:
\gamma^{i} M=\xi^{i} M \gamma^{i},\quad M^{T}=\eta M,

状态: 已规范化 ✓
================================================================================

样本 972: im2latexv3_62a2fa5b0a8ea8f
------------------------------------------------------------
输入:
P V \frac { 1 } { r ^ { + } } = \frac { 1 } { 2 } \left[ \frac { 1 } { r ^ { + } + i \epsilon } + \frac { 1 } { r ^ { + } - i \epsilon } \right] \, \, ,

输出:
P V \frac{1}{r^{+}}=\frac{1}{2} \left[\frac{1}{r^{+}+i \epsilon}+\frac{1}{r^{+}-i \epsilon} \right] \,\,,

状态: 已规范化 ✓
================================================================================

样本 973: im2latexv3_1b65cefa85c5ab5
------------------------------------------------------------
输入:
S [ A _ { i j } ] = - \int d x ^ { 0 } d ^ { 5 } x B ^ { i j } \partial _ { 0 } A _ { i j } - \int d x ^ { 0 } H

输出:
S[A_{i j}]=-\int d x^{0} d^{5} x B^{i j} \partial_{0} A_{i j}-\int d x^{0} H

状态: 已规范化 ✓
================================================================================

样本 974: im2latexv3_9c846daafcadfc1
------------------------------------------------------------
输入:
\psi _ { S E 2 } ( x , { \bf p } ; s _ { 1 } , s _ { 2 } ) = - \int { \frac { ( 1 - x ) d p ^ { - } } { 2 \pi } } { \frac { \bar { u } ( x P ^ { + } , { \bf p } ; s _ { 1 } ) \gamma ^ { + } \Phi ( p ) \Sigma _ { 1 } ( p - P ) v ( ( 1 - x ) P ^ { + } , - { \bf p } ; s _ { 2 } ) } { ( p - P ) ^ { 2 } - m ^ { 2 } + i \epsilon } } ,

输出:
\psi_{S E 2}(x,{\bf p};s_{1},s_{2})=-\int{\frac{(1-x) d p^{-}}{2 \pi}}{\frac{\bar{u}(x P^{+},{\bf p};s_{1}) \gamma^{+} \Phi(p) \Sigma_{1}(p-P) v((1-x) P^{+},-{\bf p};s_{2})}{(p-P)^{2}-m^{2}+i \epsilon}},

状态: 已规范化 ✓
================================================================================

样本 975: im2latexv3_aed77a857af21ce
------------------------------------------------------------
输入:
c h _ { \Gamma } ^ { \gamma } ( E ) = \sum _ { i = 1 } ^ { s } \lambda _ { i } c h ( E ^ { i } )

输出:
c h_{\Gamma}^{\gamma}(E)=\sum_{i=1}^{s} \lambda_{i} c h(E^{i})

状态: 已规范化 ✓
================================================================================

样本 976: im2latexv3_ec61396fc394711
------------------------------------------------------------
输入:
+ { \frac { \bar { \kappa } } { 2 \pi } } \left( \bar { S } + \bar { Q } _ { ( m ) } \right) + \int _ { C _ { \infty } } \sqrt { \gamma } d ^ { 2 } y \bar { B } \bar { N } ~ ~ ~ .

输出:
+{\frac{\bar{\kappa}}{2 \pi}} \left(\bar{S}+\bar{Q}_{(m)} \right)+\int_{C_{\infty}} \sqrt{\gamma} d^{2} y \bar{B} \bar{N} ~ ~ ~.

状态: 已规范化 ✓
================================================================================

样本 977: im2latexv3_2aa15125a073276
------------------------------------------------------------
输入:
\partial _ { R } \left[ \sqrt { \frac { C K ^ { d - 2 } } { A } } R ^ { d - 2 } \partial _ { R } H \right] = 0 \ .

输出:
\partial_{R} \left[\sqrt{\frac{C K^{d-2}}{A}} R^{d-2} \partial_{R} H \right]=0.

状态: 已规范化 ✓
================================================================================

样本 978: im2latexv3_0e5ec669ea4b967
------------------------------------------------------------
输入:
\kappa _ { m } = { \frac { g - 2 } { 2 } } { \frac { \delta \alpha _ { i } } { \beta _ { i } + 1 / ( 4 ( | \delta - m | - 1 ) ) } }

输出:
\kappa_{m}={\frac{g-2}{2}}{\frac{\delta \alpha_{i}}{\beta_{i}+1/(4(| \delta-m |-1))}}

状态: 已规范化 ✓
================================================================================

样本 979: im2latexv3_bf7ad0ce243b1ef
------------------------------------------------------------
输入:
K _ { M N } = h _ { M } ^ { P } h _ { N } ^ { Q } \bigtriangledown _ { _ P } n _ { Q } \quad ,

输出:
K_{M N}=h_{M}^{P} h_{N}^{Q} \bigtriangledown_{_P} n_{Q} \quad,

状态: 已规范化 ✓
================================================================================

样本 980: im2latexv3_efa55ec60208771
------------------------------------------------------------
输入:
I ( z ) = \mathrm { T r } \left( { \frac { z } { z + D _ { F } ^ { \dagger } D _ { F } } } - { \frac { z } { z + D _ { F } D _ { F } ^ { \dagger } } } \right) = \int _ { 0 } ^ { \infty } d \lambda { \frac { z } { z + \lambda } } \left( { \frac { d n _ { + } ( \lambda ) } { d \lambda } } - { \frac { d n _ { - } ( \lambda ) } { d \lambda } } \right) .

输出:
I(z)=\mathrm{Tr} \left({\frac{z}{z+D_{F}^{\dagger} D_{F}}}-{\frac{z}{z+D_{F} D_{F}^{\dagger}}} \right)=\int_{0}^{\infty} d \lambda{\frac{z}{z+\lambda}} \left({\frac{d n_{+}(\lambda)}{d \lambda}}-{\frac{d n_{-}(\lambda)}{d \lambda}} \right).

状态: 已规范化 ✓
================================================================================

样本 981: im2latexv3_fabec190534b209
------------------------------------------------------------
输入:
{ \cal O } [ A B C ] = { \cal O } [ A ] + { \cal O } [ B ] + { \cal O } [ C ] ,

输出:
{\cal O}[A B C]={\cal O}[A]+{\cal O}[B]+{\cal O}[C],

状态: 已规范化 ✓
================================================================================

样本 982: im2latexv3_412ae01a37d452b
------------------------------------------------------------
输入:
\delta _ { Q + S } \hat { s } = 0 \ \Rightarrow \ \left[ 4 i s ( \epsilon _ { 4 } ^ { - \alpha } + x _ { 4 } ^ { \alpha \dot { \beta } } \bar { \eta } _ { 4 \dot { \beta } } ^ { - } ) \left( { \frac { x _ { 1 4 } } { x _ { 1 4 } ^ { 2 } } } - { \frac { x _ { 3 4 } } { x _ { 3 4 } ^ { 2 } } } \right) _ { \alpha \dot { \alpha } } + \sum _ { a = 1 } ^ { 4 } ( \epsilon _ { a } ^ { + \alpha } + x _ { a } ^ { \alpha \dot { \beta } } \bar { \eta } _ { a \dot { \beta } } ^ { + } ) S _ { a 4 \; \alpha \dot { \alpha } } \right] \bar { \theta } _ { 4 } ^ { + \dot { \alpha } } = 0 \; .

输出:
\delta_{Q+S} \hat{s}=0 \Rightarrow \left[4 i s(\epsilon_{4}^{-\alpha}+x_{4}^{\alpha \dot{\beta}} \bar{\eta}_{4 \dot{\beta}}^{-}) \left({\frac{x_{1 4}}{x_{1 4}^{2}}}-{\frac{x_{3 4}}{x_{3 4}^{2}}} \right)_{\alpha \dot{\alpha}}+\sum_{a=1}^{4}(\epsilon_{a}^{+\alpha}+x_{a}^{\alpha \dot{\beta}} \bar{\eta}_{a \dot{\beta}}^{+}) S_{a 4 \;\alpha \dot{\alpha}} \right] \bar{\theta}_{4}^{+\dot{\alpha}}=0 \;.

状态: 已规范化 ✓
================================================================================

样本 983: im2latexv3_5872ca266742cf4
------------------------------------------------------------
输入:
L = \frac { 1 } { 2 } \phi ^ { * } ( P ^ { \mu } - e A ^ { \mu } ) \phi ( P _ { \mu } - e A _ { \mu } ) - \frac { 1 } { 2 } m _ { 0 } ^ { 2 } | \phi | ^ { 2 }

输出:
L=\frac{1}{2} \phi^{*}(P^{\mu}-e A^{\mu}) \phi(P_{\mu}-e A_{\mu})-\frac{1}{2} m_{0}^{2} | \phi |^{2}

状态: 已规范化 ✓
================================================================================

样本 984: im2latexv3_a359646e43b8a98
------------------------------------------------------------
输入:
\Gamma ^ { a } = \dot { \lambda } ^ { a } + \chi ^ { a } ( q ^ { i } , p _ { i } , \lambda ^ { a } ) ,

输出:
\Gamma^{a}=\dot{\lambda}^{a}+\chi^{a}(q^{i},p_{i},\lambda^{a}),

状态: 已规范化 ✓
================================================================================

样本 985: im2latexv3_a32fae55d69315d
------------------------------------------------------------
输入:
\partial _ { \mp } { ^ { ( \pm ) } \xi } ^ { i } = 0

输出:
\partial_{\mp}{^{(\pm)} \xi}^{i}=0

状态: 已规范化 ✓
================================================================================

样本 986: im2latexv3_9a23b79f5d9c304
------------------------------------------------------------
输入:
b _ { 1 } = 1 , \; \; \; \; \; b _ { 2 } = 0 , \; \; \; \; \; b _ { 3 } = 0 ,

输出:
b_{1}=1,\;\;\;\;\;b_{2}=0,\;\;\;\;\;b_{3}=0,

状态: 已规范化 ✓
================================================================================

样本 987: im2latexv3_4db48be0ba73210
------------------------------------------------------------
输入:
\Gamma ( a ) \lhd X = a \lhd \tilde { \Gamma } ( X ) \, .

输出:
\Gamma(a) \lhd X=a \lhd \tilde{\Gamma}(X) \,.

状态: 已规范化 ✓
================================================================================

样本 988: im2latexv3_a33f27214198cfe
------------------------------------------------------------
输入:
\left( - \nabla ^ { 2 } + U ^ { \prime \prime } ( \varphi ) \right) \psi _ { n } = \lambda _ { n } \psi _ { n } \; ,

输出:
\left(-\nabla^{2}+U^{\prime \prime}(\varphi) \right) \psi_{n}=\lambda_{n} \psi_{n} \;,

状态: 已规范化 ✓
================================================================================

样本 989: im2latexv3_40510326025cef1
------------------------------------------------------------
输入:
{ \frac { 1 } { 2 } } \left( a ^ { \dagger } - 2 g q ^ { 2 } \right) a \Phi _ { N + 1 } ^ { ( - ) } = E _ { N + 1 } ^ { ( - ) } \Phi _ { N + 1 } ^ { ( - ) } .

输出:
{\frac{1}{2}} \left(a^{\dagger}-2 g q^{2} \right) a \Phi_{N+1}^{(-)}=E_{N+1}^{(-)} \Phi_{N+1}^{(-)}.

状态: 已规范化 ✓
================================================================================

样本 990: im2latexv3_e69169b312ae8c5
------------------------------------------------------------
输入:
\partial _ { 0 } P _ { \mu } : = \partial _ { 0 } \int d ^ { 3 } x \, T _ { 0 \mu } = 0 .

输出:
\partial_{0} P_{\mu}:=\partial_{0} \int d^{3} x \,T_{0 \mu}=0.

状态: 已规范化 ✓
================================================================================

样本 991: im2latexv3_cfc4256397bb8cd
------------------------------------------------------------
输入:
a = \sum _ { i , j } ( 1 + \alpha _ { i j } ) p _ { i } d _ { f } p _ { j } .

输出:
a=\sum_{i,j}(1+\alpha_{i j}) p_{i} d_{f} p_{j}.

状态: 已规范化 ✓
================================================================================

样本 992: im2latexv3_1923760efe31351
------------------------------------------------------------
输入:
\langle \lambda \lambda \rangle \propto e ^ { 2 \pi i k / T _ { G } } \, , \qquad k = 0 , 1 , . . . , T _ { G } - 1 \, ,

输出:
\langle \lambda \lambda \rangle \propto e^{2 \pi i k/T_{G}} \,,\qquad k=0,1,...,T_{G}-1 \,,

状态: 已规范化 ✓
================================================================================

样本 993: im2latexv3_c15314d7aa2d19c
------------------------------------------------------------
输入:
\alpha ( t ) = \alpha _ { 0 } e ^ { - i \omega ( t - t _ { 0 } ) } \ \ \ , \ \ \ \alpha ^ { * } ( t ) = \alpha _ { 0 } ^ { * } e ^ { i \omega ( t - t _ { 0 } ) } \ ,

输出:
\alpha(t)=\alpha_{0} e^{-i \omega(t-t_{0})},\alpha^{*}(t)=\alpha_{0}^{*} e^{i \omega(t-t_{0})},

状态: 已规范化 ✓
================================================================================

样本 994: im2latexv3_1c4db771015f040
------------------------------------------------------------
输入:
J _ { 0 } = \frac { c } { 1 2 } ( A ^ { 3 } + 2 n + 1 ) , \qquad L _ { 0 } = ( 1 - \gamma ^ { 2 } ) \frac { c } { 2 4 } + \frac { c } { 2 4 } ( A ^ { 3 } + 2 n + 1 ) ^ { 2 } .

输出:
J_{0}=\frac{c}{1 2}(A^{3}+2 n+1),\qquad L_{0}=(1-\gamma^{2}) \frac{c}{2 4}+\frac{c}{2 4}(A^{3}+2 n+1)^{2}.

状态: 已规范化 ✓
================================================================================

样本 995: im2latexv3_2c6172ed1e70a86
------------------------------------------------------------
输入:
{ \delta _ { \rho } } { \hat { G } _ { D \varepsilon } } ( \xi , \xi ) = { \frac { \rho ( \xi ) } { 4 \pi } } , \quad \xi \not \in { B } .

输出:
{\delta_{\rho}}{\hat{G}_{D \varepsilon}}(\xi,\xi)={\frac{\rho(\xi)}{4 \pi}},\quad \xi \not \in{B}.

状态: 已规范化 ✓
================================================================================

样本 996: im2latexv3_fb849d0097cbfde
------------------------------------------------------------
输入:
A _ { i } ( x ) \rightarrow \lambda A _ { i } ( \lambda x ) ,

输出:
A_{i}(x) \rightarrow \lambda A_{i}(\lambda x),

状态: 已规范化 ✓
================================================================================

样本 997: im2latexv3_fa9890f273f239d
------------------------------------------------------------
输入:
\omega _ { \Lambda } = 2 - 2 n ( L - 1 ) - E _ { \phi } ( n + 1 ) . \qquad

输出:
\omega_{\Lambda}=2-2 n(L-1)-E_{\phi}(n+1).\qquad

状态: 已规范化 ✓
================================================================================

样本 998: im2latexv3_95ab1d8cdcfdbea
------------------------------------------------------------
输入:
\left\{ \begin{cases} { \delta ^ { ( 2 ) } x _ { \mu } ^ { i } } & { = 0 } \\ { \delta ^ { ( 2 ) } \xi ^ { i } } & { = \epsilon _ { 2 } } \\ \end{cases} \right. .

输出:
\left\{\left\{\begin{1.2}{{\delta^{(2)} x_{\mu}^{i}}} &{{=0}} \{{\delta^{(2)} \xi^{i}}} &{{=\epsilon_{2}}} \\ \end{1.2} \right.\right..

状态: 已规范化 ✓
================================================================================

样本 999: im2latexv3_698edee739be92c
------------------------------------------------------------
输入:
\begin{array} { c c } { V ( x ) = i \, Z } & { \ \ \ \ \mathrm { R e } \ x < 0 } \\ { V ( x ) = - i \, Z } & { \ \ \ \ \mathrm { R e } \ x > 0 . } \\ \end{array}

输出:
\begin{array}{cc}{{V(x)=i \,Z}} &{{\mathrm{Re} x<0}} \{{V(x)=-i \,Z}} &{{\mathrm{Re} x>0.}} \end{array}

状态: 已规范化 ✓
================================================================================

样本 1000: im2latexv3_3106921b55b25c1
------------------------------------------------------------
输入:
P = \frac { 2 ^ { 8 } } { 3 ^ { 4 } 5 ^ { 2 } 7 ^ { 2 } 1 1 } \frac { 2 \kappa ^ { 2 } N ^ { 2 } T _ { 0 } ^ { 2 } } { \Omega _ { 8 } } \left( \omega _ { 1 } ^ { 1 2 } R _ { 1 } ^ { 4 } + \omega _ { 2 } ^ { 1 2 } R _ { 2 } ^ { 4 } + \omega _ { 3 } ^ { 1 2 } R _ { 3 } ^ { 4 } \right)

输出:
P=\frac{2^{8}}{3^{4} 5^{2} 7^{2} 1 1} \frac{2 \kappa^{2} N^{2} T_{0}^{2}}{\Omega_{8}} \left(\omega_{1}^{1 2} R_{1}^{4}+\omega_{2}^{1 2} R_{2}^{4}+\omega_{3}^{1 2} R_{3}^{4} \right)

状态: 已规范化 ✓
================================================================================

