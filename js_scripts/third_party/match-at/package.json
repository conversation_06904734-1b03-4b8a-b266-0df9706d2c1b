{"name": "match-at", "version": "0.1.0", "description": "Relocatable regular expressions.", "repository": {"type": "git", "url": "https://github.com/spicyj/match-at"}, "main": "lib/matchAt.js", "files": ["lib/"], "devDependencies": {"babel": "^4.7.16", "jest-cli": "^0.4.0", "react-tools": "^0.13.1"}, "jest": {"scriptPreprocessor": "<rootDir>/jestSupport/preprocessor.js", "unmockedModulePathPatterns": [""]}, "scripts": {"prepublish": "babel -d lib/ src/", "test": "jest"}, "gitHead": "4197daff69720734c72ba3321ed68a41c0527fb2", "bugs": {"url": "https://github.com/spicyj/match-at/issues"}, "homepage": "https://github.com/spicyj/match-at", "_id": "match-at@0.1.0", "_shasum": "f561e7709ff9a105b85cc62c6b8ee7c15bf24f31", "_from": "match-at@", "_npmVersion": "2.2.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "spicyj", "email": "<EMAIL>"}, "maintainers": [{"name": "spicyj", "email": "<EMAIL>"}], "dist": {"shasum": "f561e7709ff9a105b85cc62c6b8ee7c15bf24f31", "tarball": "https://registry.npmjs.org/match-at/-/match-at-0.1.0.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/match-at/-/match-at-0.1.0.tgz"}