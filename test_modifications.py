#!/usr/bin/env python3
"""
测试脚本修改后的效果
验证新脚本相较于原脚本的改变
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from normalize_optimized import normalize_latex_string, tokenize_latex_string

def test_math_functions():
    """测试数学函数处理"""
    print("=== 测试数学函数处理 ===")
    
    test_cases = [
        r'\sin x + \cos y',
        r'\mathrm{sin} x + \mathrm{cos} y',
        r'\operatorname{sin} x + \operatorname{cos} y',
        r'\log_2 x + \exp(y)',
        r'\arcsin x + \arccos y',
        r'\sinh x + \cosh y',
        r'\min(a,b) + \max(c,d)',
    ]
    
    for test_case in test_cases:
        print(f"输入: {test_case}")
        result = normalize_latex_string(test_case)
        print(f"输出: {result}")
        print()

def test_text_handling():
    """测试\text命令处理"""
    print("=== 测试\\text命令处理 ===")
    
    test_cases = [
        r'\text{hello world}',
        r'x + \text{some text} + y',
        r'\text{中文文本}',
        r'\mbox{box text}',
        r'\hbox{horizontal box}',
    ]
    
    for test_case in test_cases:
        print(f"输入: {test_case}")
        result = normalize_latex_string(test_case)
        print(f"输出: {result}")
        print()

def test_complex_expressions():
    """测试复杂表达式"""
    print("=== 测试复杂表达式 ===")
    
    test_cases = [
        r'\sin(x) + \cos(y) = \text{trigonometric functions}',
        r'\frac{\sin x}{\cos x} = \tan x',
        r'\begin{align} \sin^2 x + \cos^2 x &= 1 \\ \tan x &= \frac{\sin x}{\cos x} \end{align}',
        r'\mathrm{d}x + \sin x \, \mathrm{d}y',
    ]
    
    for test_case in test_cases:
        print(f"输入: {test_case}")
        result = normalize_latex_string(test_case)
        print(f"输出: {result}")
        print()

if __name__ == "__main__":
    test_math_functions()
    test_text_handling()
    test_complex_expressions()
