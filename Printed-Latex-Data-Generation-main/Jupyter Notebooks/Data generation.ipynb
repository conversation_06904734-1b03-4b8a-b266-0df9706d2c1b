#%%
# Here we take care of paths.

from pathlib import Path
import os
print('Starting path:' + os.getcwd())
if os.getcwd()[-29:] == "Printed-Latex-Data-Generation":
    pass
else:
    PATH = Path().resolve().parents[0]
    os.chdir(PATH)

# make sure you are in Paragraph_to_Tex folder
print('Current path:' + os.getcwd())
#%%
from Printed_Tex import Generate_Printed_Tex
#%%
Generate_Printed_Tex(download_tex_dataset=False,
                    generate_tex_formulas =False,
                     number_tex_formulas_to_generate =1,
                     generate_svg_images_from_tex = False,
                     generate_png_from_svg = True,
                    )
#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%

#%%
