"""
辅助函数模块

提供LaTeX处理过程中需要的通用辅助函数。
这些函数被其他模块广泛使用，遵循低层模块原则。
"""

import re
from typing import List, Tuple, Optional, Set


def find_matching_brace(sequence: List[str], start_index: int, 
                       brace: Tuple[str, str] = ('{', '}')) -> int:
    """
    查找匹配的括号位置
    
    Args:
        sequence: token序列
        start_index: 起始位置
        brace: 括号对，默认为('{', '}')
    
    Returns:
        匹配括号的索引，如果未找到返回-1
    """
    left_brace, right_brace = brace
    depth = 0
    
    for i, char in enumerate(sequence[start_index:], start=start_index):
        if char == left_brace:
            depth += 1
        elif char == right_brace:
            depth -= 1
            if depth == 0:
                return i
    
    if depth > 0:
        print(f"警告: 在位置{start_index}找不到匹配的括号")
    
    return -1


def is_latex_command(token: str) -> bool:
    """
    判断token是否为LaTeX命令
    
    Args:
        token: 要检查的token
        
    Returns:
        是否为LaTeX命令
    """
    if not token or not token.startswith('\\'):
        return False
    
    # 检查是否为有效的LaTeX命令格式
    # \command 或 \command{...} 或 \command[...]
    pattern = r'^\\[a-zA-Z]+(\*)?(\{.*\}|\[.*\])?$|^\\[^a-zA-Z]$'
    return bool(re.match(pattern, token))


def is_spacing_command(token: str) -> bool:
    """
    判断token是否为LaTeX间距命令
    
    根据FR1需求，这些命令需要被保留：
    - 窄/中/大间距: \\, \\: \\; \\thinspace \\medspace \\thickspace
    - 负间距: \\! \\negthinspace \\negmedspace \\negthickspace
    - 强制空格: \\ (反斜杠后紧跟一个空格)
    - em间距: \\quad \\qquad \\enspace
    
    Args:
        token: 要检查的token
        
    Returns:
        是否为间距命令
    """
    spacing_commands = {
        # 基本间距
        '\\,', '\\:', '\\;', '\\!',
        # 命名间距
        '\\thinspace', '\\medspace', '\\thickspace',
        '\\negthinspace', '\\negmedspace', '\\negthickspace',
        # em间距
        '\\quad', '\\qquad', '\\enspace',
        # 强制空格
        '\\ '
    }
    
    return token in spacing_commands


def extract_latex_commands(text: str) -> List[str]:
    """
    从LaTeX文本中提取所有命令
    
    Args:
        text: LaTeX文本
        
    Returns:
        命令列表
    """
    # 匹配LaTeX命令的正则表达式
    # \command 或 \command{...} 或 \command[...] 或单字符命令如 \{
    pattern = r'\\[a-zA-Z]+\*?|\\[^a-zA-Z\s]'
    
    commands = re.findall(pattern, text)
    return commands


def validate_latex_syntax(text: str) -> Tuple[bool, List[str]]:
    """
    验证LaTeX语法的基本正确性
    
    检查：
    - 括号匹配
    - 环境匹配
    - 基本语法错误
    
    Args:
        text: LaTeX文本
        
    Returns:
        (是否有效, 错误信息列表)
    """
    errors = []
    
    # 检查花括号匹配
    brace_count = 0
    for char in text:
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count < 0:
                errors.append("花括号不匹配：多余的右括号")
                break
    
    if brace_count > 0:
        errors.append(f"花括号不匹配：缺少{brace_count}个右括号")
    
    # 检查方括号匹配
    bracket_count = 0
    for char in text:
        if char == '[':
            bracket_count += 1
        elif char == ']':
            bracket_count -= 1
            if bracket_count < 0:
                errors.append("方括号不匹配：多余的右括号")
                break
    
    if bracket_count > 0:
        errors.append(f"方括号不匹配：缺少{bracket_count}个右括号")
    
    # 检查环境匹配
    begin_pattern = r'\\begin\{([^}]+)\}'
    end_pattern = r'\\end\{([^}]+)\}'
    
    begins = re.findall(begin_pattern, text)
    ends = re.findall(end_pattern, text)
    
    # 简单检查：begin和end的数量应该相等
    begin_envs = {}
    for env in begins:
        begin_envs[env] = begin_envs.get(env, 0) + 1
    
    end_envs = {}
    for env in ends:
        end_envs[env] = end_envs.get(env, 0) + 1
    
    # 检查每个环境的匹配
    all_envs = set(begin_envs.keys()) | set(end_envs.keys())
    for env in all_envs:
        begin_count = begin_envs.get(env, 0)
        end_count = end_envs.get(env, 0)
        if begin_count != end_count:
            errors.append(f"环境{env}不匹配：{begin_count}个begin，{end_count}个end")
    
    return len(errors) == 0, errors


def get_math_function_commands() -> Set[str]:
    """
    获取数学函数命令集合
    
    用于FR3需求中的函数名称规范化
    
    Returns:
        数学函数命令集合
    """
    return {
        # 三角函数
        'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
        # 双曲函数
        'sinh', 'cosh', 'tanh', 'coth',
        # 反三角函数
        'arcsin', 'arccos', 'arctan',
        # 对数函数
        'ln', 'log', 'lg',
        # 其他函数
        'exp', 'min', 'max', 'det', 'lim', 'sup', 'inf',
        'gcd', 'lcm', 'mod', 'arg', 'deg', 'dim', 'hom', 'ker'
    }


def normalize_environment_name(env_name: str) -> str:
    """
    规范化环境名称
    
    根据FR2需求进行环境统一：
    - 对齐环境 → aligned
    - 小矩阵环境 → matrix
    
    Args:
        env_name: 原始环境名称
        
    Returns:
        规范化后的环境名称
    """
    # 移除星号后缀
    base_env = env_name.rstrip('*')
    
    # 对齐环境统一
    alignment_envs = {'split', 'align', 'alignedat', 'alignat', 'eqnarray'}
    if base_env in alignment_envs:
        return 'aligned'
    
    # 矩阵环境统一（仅smallmatrix）
    if base_env == 'smallmatrix':
        return 'matrix'
    
    # 其他环境保持不变
    return env_name


def count_token_complexity(token: str) -> int:
    """
    计算token的复杂度
    
    用于FR3需求中选择token数量最少的版本
    
    Args:
        token: LaTeX token
        
    Returns:
        复杂度分数（越小越简单）
    """
    if not token:
        return 0
    
    complexity = 0
    
    # 基础长度权重
    complexity += len(token)
    
    # 花括号增加复杂度
    complexity += token.count('{') * 2
    complexity += token.count('}') * 2
    
    # 方括号增加复杂度
    complexity += token.count('[') * 1
    complexity += token.count(']') * 1
    
    # 复杂命令增加权重
    if '\\operatorname' in token:
        complexity += 10
    elif '\\mathrm' in token:
        complexity += 5
    elif '\\mathbf' in token:
        complexity += 3
    
    return complexity


def clean_whitespace(text: str) -> str:
    """
    清理文本中的多余空白字符
    
    Args:
        text: 输入文本
        
    Returns:
        清理后的文本
    """
    if not text:
        return text
    
    # 将多个连续空格替换为单个空格
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空格
    text = text.strip()
    
    return text
