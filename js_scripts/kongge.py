def clean_latex_spaces(text):
    """
    LaTeX文本空格清理函数 - 智能空格处理
    保留 \text{} 内的空格，以及LaTeX命令后的必要空格
    """
    # 先找到所有 \text{...}, \mbox{...}, \hbox{...} 块，保护其中的空格
    text_blocks = []
    text_pattern = r'\\(?:text|mbox|hbox)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'

    def protect_text_spaces(match):
        """保护 \text{} 内的空格"""
        text_blocks.append(match.group(0))
        return f"__TEXT_BLOCK_{len(text_blocks)-1}__"

    # 替换 \text{} 块为占位符
    protected_text = re.sub(text_pattern, protect_text_spaces, text)

    # 保护LaTeX命令后的空格（避免\nabla f变成\nablaf）
    latex_command_blocks = []
    # 匹配反斜杠命令后跟空格再跟字母的模式
    latex_command_pattern = r'(\\[a-zA-Z]+)\s+([a-zA-Z])'

    def protect_latex_command_spaces(match):
        """保护LaTeX命令后的空格"""
        full_match = match.group(0)
        latex_command_blocks.append(full_match)
        return f"__LATEX_CMD_BLOCK_{len(latex_command_blocks)-1}__"

    # 保护LaTeX命令后的空格
    protected_text = re.sub(latex_command_pattern, protect_latex_command_spaces, protected_text)

    # 移除多余空格，但保持二元运算符前后的一致性
    # 首先移除非反斜杠字符之间的空格
    cleaned_text = re.sub(r'(?<=[^\\])\s+(?=[^\\])', '', protected_text)

    # 然后统一处理二元运算符前后的空格
    # 移除二元运算符前后的所有空格，让LaTeX自动处理间距
    binary_operators = ['+', '-', '=', '<', '>']
    latex_binary_operators = [r'\\leq', r'\\geq', r'\\neq', r'\\approx', r'\\equiv']

    # 处理简单运算符
    for op in binary_operators:
        # 移除运算符前的空格
        cleaned_text = re.sub(f'\\s+{re.escape(op)}', op, cleaned_text)
        # 移除运算符后的空格（如果后面跟的是反斜杠命令）
        cleaned_text = re.sub(f'{re.escape(op)}\\s+(?=\\\\)', op, cleaned_text)

    # 处理LaTeX运算符（保留运算符后面的空格，只移除前面的空格）
    for op in latex_binary_operators:
        # 移除运算符前的空格
        cleaned_text = re.sub(f'\\s+{op}', op, cleaned_text)

    # 恢复 \text{} 块
    for i, block in enumerate(text_blocks):
        cleaned_text = cleaned_text.replace(f"__TEXT_BLOCK_{i}__", block)

    # 恢复LaTeX命令块
    for i, block in enumerate(latex_command_blocks):
        cleaned_text = cleaned_text.replace(f"__LATEX_CMD_BLOCK_{i}__", block)

    return cleaned_text
