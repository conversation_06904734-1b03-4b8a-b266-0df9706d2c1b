#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速诊断脚本 - 检查 arxiv.py 的问题
"""

import os
import sys
import requests
from datetime import datetime

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_basic_functionality():
    """检查基本功能"""
    print("=== 快速诊断 ===")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查配置
    try:
        from arxiv import CONFIG, BULK_CONFIG
        print(f"✓ arxiv 模块导入成功")
        print(f"  - 模式: {CONFIG['mode']}")
        print(f"  - 输出目录: {CONFIG['output_dir']}")
        print(f"  - 目标论文数: {BULK_CONFIG['target_paper_count']}")
        print(f"  - 主分类: {BULK_CONFIG['primary_category']}")
    except Exception as e:
        print(f"✗ arxiv 模块导入失败: {e}")
        return False
    
    # 2. 检查输出目录
    output_dir = CONFIG['output_dir']
    if os.path.exists(output_dir):
        print(f"✓ 输出目录存在: {output_dir}")
        files = os.listdir(output_dir)
        if files:
            print(f"  - 目录中的文件: {files}")
            
            # 检查关键文件
            math_file = os.path.join(output_dir, 'math_arxiv.txt')
            visited_file = os.path.join(output_dir, 'visited_arxiv.txt')
            progress_file = os.path.join(output_dir, 'progress.json')
            
            if os.path.exists(math_file):
                size = os.path.getsize(math_file)
                print(f"  - math_arxiv.txt: {size} 字节")
                if size > 0:
                    with open(math_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"    包含 {len(lines)} 行公式")
                        if lines:
                            print(f"    示例: {lines[0].strip()}")
            
            if os.path.exists(visited_file):
                size = os.path.getsize(visited_file)
                print(f"  - visited_arxiv.txt: {size} 字节")
                if size > 0:
                    with open(visited_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"    已处理 {len(lines)} 篇论文")
            
            if os.path.exists(progress_file):
                size = os.path.getsize(progress_file)
                print(f"  - progress.json: {size} 字节")
                if size > 0:
                    import json
                    with open(progress_file, 'r', encoding='utf-8') as f:
                        progress = json.load(f)
                        print(f"    进度: {progress.get('total_papers_processed', 0)} 篇论文")
                        print(f"    公式: {progress.get('total_formulas_extracted', 0)} 个")
                        print(f"    最后更新: {progress.get('last_update', '未知')}")
        else:
            print("  - 目录为空")
    else:
        print(f"✗ 输出目录不存在: {output_dir}")
        try:
            os.makedirs(output_dir, exist_ok=True)
            print(f"✓ 已创建输出目录: {output_dir}")
        except Exception as e:
            print(f"✗ 无法创建输出目录: {e}")
    
    # 3. 检查网络连接（快速测试）
    try:
        response = requests.get("https://arxiv.org", timeout=5)
        if response.status_code == 200:
            print("✓ arXiv 网站连接正常")
        else:
            print(f"⚠️  arXiv 网站响应异常: {response.status_code}")
    except Exception as e:
        print(f"✗ 网络连接问题: {e}")
    
    # 4. 检查是否有 Python 进程在运行
    try:
        import subprocess
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, timeout=5)
        if 'python.exe' in result.stdout:
            print("⚠️  检测到 Python 进程正在运行")
            print("  - 可能有 arxiv.py 正在后台运行")
            print("  - 建议检查任务管理器或等待程序完成")
        else:
            print("✓ 没有检测到其他 Python 进程")
    except Exception as e:
        print(f"⚠️  无法检查进程状态: {e}")
    
    return True

def create_minimal_test():
    """创建最小化测试"""
    print("\n=== 创建最小化测试 ===")
    
    # 创建一个简单的测试公式
    test_formulas = [
        "E = mc^2",
        "\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}",
        "\\sum_{n=1}^{\\infty} \\frac{1}{n^2} = \\frac{\\pi^2}{6}"
    ]
    
    try:
        from arxiv import CONFIG
        output_dir = CONFIG['output_dir']
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存测试公式
        test_file = os.path.join(output_dir, 'test_formulas.txt')
        with open(test_file, 'w', encoding='utf-8') as f:
            for formula in test_formulas:
                f.write(formula + '\n')
        
        print(f"✓ 测试文件已创建: {test_file}")
        print(f"  - 包含 {len(test_formulas)} 个测试公式")
        
        # 验证文件
        if os.path.exists(test_file):
            size = os.path.getsize(test_file)
            print(f"  - 文件大小: {size} 字节")
            return True
        else:
            print("✗ 测试文件创建失败")
            return False
            
    except Exception as e:
        print(f"✗ 创建测试文件失败: {e}")
        return False

def main():
    """主函数"""
    print("ArXiv 快速诊断工具")
    print("=" * 40)
    
    success = check_basic_functionality()
    
    if success:
        create_minimal_test()
    
    print("\n" + "=" * 40)
    print("诊断完成")
    
    print("\n💡 建议:")
    print("1. 如果看到进度文件但没有公式，可能程序还在运行")
    print("2. 如果目录为空，可能程序遇到了网络或其他问题")
    print("3. 可以尝试减少目标论文数量重新运行")
    print("4. 检查是否有防火墙阻止网络访问")

if __name__ == "__main__":
    main()
