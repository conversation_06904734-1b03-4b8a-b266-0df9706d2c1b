[tool.poetry]
name = "unimernet"
version = "0.2.3"
description = 'UniMERNet: A Universal Network for Real-World Mathematical Expression Recognition'
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
license = "Apache License 2.0"
repository = "https://github.com/opendatalab/UniMERNet"
keywords = ["MER", "latex", "markdown", "pdf"]
include = [
    "train.py",
    "test.py",
    "demo.py",
    "unimernet_app.py",
    "run_unimernet_app.py",
]

[tool.poetry.dependencies]
python = ">=3.10"
torch = ">=2.2.2"
torchvision = ">=0.17.2"
omegaconf = "^2.3.0"
matplotlib = "^3.8.4"
iopath = "^0.1.9"
timm = "^0.9.16"
opencv-python = "^4.6.0"
transformers = "4.42.4"
fairscale = "^0.4.13"
# ftfy = "^6.2.0"
ftfy = {version = "^6.2.0", python = ">=3.10,<4.0"}
albumentations = "^1.4.4"
wand = "^0.6.13"
webdataset = "^0.2.86"
rapidfuzz = "^3.8.1"
termcolor = "^2.4.0"
pandas = "^2.2.2"
evaluate = "^0.4.1"
rich = "^13.7.1"
jupyterlab = "^4.1.6"
tabulate = "^0.9.0"
nltk = "^3.8.1"
streamlit = "^1.33.0"
pypdfium2 = "^4.29.0"
pdf2image = "^1.17.0"
streamlit_drawable_canvas = "^0.9.3"

[tool.poetry.extras]
full = [
    "termcolor",
    "pandas",
    "rich",
    "jupyterlab",
    "tabulate",
    "nltk",
    "streamlit",
    "pypdfium2",
    "pdf2image",
    "streamlit_drawable_canvas"
]

[tool.poetry.scripts]
unimernet = "demo:main"
unimernet_gui = "run_unimernet_app:run_app"
unimernet_eval = "test:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
