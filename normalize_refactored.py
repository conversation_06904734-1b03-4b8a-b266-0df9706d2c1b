# LaTeX 规范化脚本 - 重构版本
# 功能：将LaTeX公式规范化为标准格式，用于图片转LaTeX码任务

import os
import re
import subprocess
import tempfile
import csv
from pathlib import Path

# ==================== 配置区 ====================
CONFIG = {
    'mode': 'normalize',  # 可选: 'tokenize', 'normalize'
    'latex_string': r'F _ { { \mathbb T } _ { c } ^ { 2 } } \sim \frac { 1 } { \mathrm { v o l } ( { \mathbb T } _ { c } ^ { 2 } ) _ { h e t } } \, , ',
    'remove_trailing': False,
    'enable_synonym_replacement': False,
    'unify_environments': False,
}

# ==================== 常量定义 ====================
SKIP_PATTERNS = [r'\{', r'\}', r'[\[\]]', r'\\begin\{.*?\}', r'\\end\{.*?\}', r'\^', r'\_', r'\\.*rule.*', r'\\.*line.*', r'\[[\-.0-9]+[epm][xtm]\]']
SKIP_Tokens = ['\\', '\\\\', '\\index', '\\a', '&', '$', '\\multirow', '\\def', '\\edef', '\\raggedright', '\\url', '\\cr', '\\ensuremath', '\\left', '\\right',
               '\\mathchoice', '\\scriptstyle', '\\displaystyle', '\\qquad', '\\quad', '\\,', '\\!', '~', '\\boldmath', '\\gdef', '\\today', '\\the']
PHANTOM_Tokens = ['\\fontfamily', '\\vphantom', '\\phantom', '\\rowcolor', '\\ref', '\\thesubequation', '\\global', '\\theboldgroup']
TWO_Tail_Tokens = ['\\frac', '\\binom']
AB_Tail_Tokens = ['\\xrightarrow', '\\xleftarrow', '\\sqrt']
TWO_Tail_Invisb_Tokens = ['\\overset', '\\underset', '\\stackrel']
ONE_Tail_Tokens = ['\\widetilde', '\\overline', '\\hat', '\\widehat', '\\tilde', '\\Tilde', '\\dot', '\\bar', '\\vec', '\\underline', '\\underbrace', '\\check',
                   '\\breve', '\\Bar', '\\Vec', '\\mathring', '\\ddot', '\\Ddot', '\\dddot', '\\ddddot']
ONE_Tail_Invisb_Tokens = ['\\boldsymbol', '\\pmb', '\\textbf', '\\mathrm', '\\mathbf', '\\mathbb', '\\mathcal', '\\textmd', '\\texttt', '\\textnormal',
                          '\\textit', '\\textup', '\\mathop', '\\mathbin', '\\smash', '\\operatorname', '\\textrm', '\\mathfrak', '\\emph',
                          '\\textsf', '\\textsc']

SCRIPT_DIR = Path(__file__).parent
JS_DIR = SCRIPT_DIR / "js_scripts"

# ==================== 核心函数 ====================

def find_matching_brace(sequence, start_index, brace=['{', '}']):
    """查找匹配的括号位置"""
    left_brace, right_brace = brace
    depth = 0
    for i, char in enumerate(sequence[start_index:], start=start_index):
        if char == left_brace:
            depth += 1
        elif char == right_brace:
            depth -= 1
            if depth == 0:
                return i
    if depth > 0:
        print(f"Warning! found no matching brace in sequence starting at {start_index}")
    return -1

def clean_latex_spaces(text):
    """统一的空格清理函数"""
    result = text
    
    # 清理结构性空格
    result = re.sub(r'\s*\{\s*', '{', result)
    result = re.sub(r'\s*\}', '}', result)
    result = re.sub(r'\s*\[\s*', '[', result)
    result = re.sub(r'\s*\]', ']', result)
    result = re.sub(r'\s*\(\s*', '(', result)
    result = re.sub(r'\s*\)', ')', result)
    
    # 清理运算符空格
    operators = ['+', '-', '=', '<', '>', '*', '/', '^', '_']
    for op in operators:
        result = re.sub(f'\\s*{re.escape(op)}\\s*', op, result)
    
    # 清理标点符号空格
    result = re.sub(r'\s*,\s*', ',', result)
    result = re.sub(r'\s*;\s*', ';', result)
    result = re.sub(r'\s*:\s*', ':', result)
    result = re.sub(r'\s*\.\s*', '.', result)
    
    # 清理LaTeX命令空格
    result = re.sub(r'(\\[A-Za-z]+)\s*\{', r'\1{', result)
    result = re.sub(r'(\\[A-Za-z]+)\s*\[', r'\1[', result)
    
    # 清理数学字体命令内部空格
    math_fonts = ['mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt']
    for font in math_fonts:
        pattern = f'\\\\{font}\\s*\\{{\\s*([^}}]+)\\s*\\}}'
        result = re.sub(pattern, f'\\\\{font}{{{r"\1"}}}', result)
    
    return result

def add_required_spaces(text):
    """为需要的命令添加空格"""
    result = text
    
    # 需要尾随空格的命令
    space_required_commands = [
        '\\bf', '\\it', '\\rm', '\\sf', '\\tt', '\\sc', '\\em', '\\sl',
        '\\tiny', '\\scriptsize', '\\footnotesize', '\\small',
        '\\normalsize', '\\large', '\\Large', '\\LARGE', '\\huge', '\\Huge',
        '\\displaystyle', '\\textstyle', '\\scriptstyle', '\\scriptscriptstyle',
        '\\quad', '\\qquad', '\\enspace', '\\thinspace',
        '\\negthinspace', '\\medspace', '\\negmedspace',
        '\\thickspace', '\\negthickspace',
    ]
    
    # 按长度降序排序，处理需要添加空格的命令
    sorted_commands = sorted(space_required_commands, key=len, reverse=True)
    for cmd in sorted_commands:
        pattern = f'({re.escape(cmd)})(?=[a-zA-Z])'
        result = re.sub(pattern, r'\1 ', result)
    
    # 为LaTeX符号命令添加分隔空格
    latex_symbols = [
        r'\\alpha', r'\\beta', r'\\gamma', r'\\delta', r'\\epsilon', r'\\theta', r'\\lambda', r'\\mu', r'\\nu', r'\\pi', r'\\sigma', r'\\tau', r'\\phi', r'\\omega',
        r'\\Gamma', r'\\Delta', r'\\Theta', r'\\Lambda', r'\\Pi', r'\\Sigma', r'\\Phi', r'\\Omega',
        r'\\partial', r'\\nabla', r'\\infty', r'\\emptyset',
        r'\\leq', r'\\geq', r'\\neq', r'\\equiv', r'\\approx', r'\\sim',
        r'\\cdot', r'\\times', r'\\pm', r'\\mp',
    ]
    
    for symbol in latex_symbols:
        pattern = f'({re.escape(symbol)})([a-zA-Z])'
        result = re.sub(pattern, r'\1 \2', result)
    
    return result

def remove_empty_braces(text):
    """删除空花括号"""
    result = text
    prev_result = ""
    while prev_result != result:
        prev_result = result
        result = re.sub(r'\{\s*\}', '', result)
    return result

def remove_redundant_braces(text):
    """删除多余的嵌套花括号"""
    result = text
    
    # 处理LaTeX命令后的双重花括号
    latex_commands = [
        'sqrt', 'frac', 'mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt',
        'hat', 'tilde', 'bar', 'vec', 'dot', 'ddot', 'overline', 'underline',
        'sin', 'cos', 'tan', 'ln', 'log', 'exp', 'min', 'max'
    ]
    
    prev_result = ""
    iterations = 0
    while prev_result != result and iterations < 5:
        prev_result = result
        
        for cmd in latex_commands:
            pattern = f'\\\\{cmd}\\{{\\{{([^{{}}]+)\\}}\\}}'
            replacement = f'\\\\{cmd}{{{r"\1"}}}'
            result = re.sub(pattern, replacement, result)
        
        # 处理一般的双重花括号
        result = re.sub(r'\{\{([^{}]+)\}\}', r'{\1}', result)
        iterations += 1
    
    return result

def standardize_math_functions(text):
    """统一数学函数格式"""
    result = text
    
    # 将operatorname转换为mathrm格式
    operatorname_map = {
        r'\\operatorname\s*\{\s*sin\s*\}': r'\\mathrm{sin}',
        r'\\operatorname\s*\{\s*cos\s*\}': r'\\mathrm{cos}',
        r'\\operatorname\s*\{\s*tan\s*\}': r'\\mathrm{tan}',
        r'\\operatorname\s*\{\s*log\s*\}': r'\\mathrm{log}',
        r'\\operatorname\s*\{\s*ln\s*\}': r'\\mathrm{ln}',
        r'\\operatorname\s*\{\s*exp\s*\}': r'\\mathrm{exp}',
        r'\\operatorname\s*\{\s*min\s*\}': r'\\mathrm{min}',
        r'\\operatorname\s*\{\s*max\s*\}': r'\\mathrm{max}',
    }
    
    for pattern, replacement in operatorname_map.items():
        result = re.sub(pattern, replacement, result)
    
    # 将直接的数学函数名转换为mathrm格式
    result = " " + result + " "
    math_functions = ['sin', 'cos', 'tan', 'ln', 'log', 'exp', 'min', 'max']
    
    for func in math_functions:
        pattern = f'(?<=\\s)\\\\{func}(?=\\s)'
        replacement = f'\\\\mathrm{{{func}}}'
        result = re.sub(pattern, replacement, result)
    
    return result.strip()

def tokenize_latex(latex_code):
    """JavaScript AST处理"""
    if not latex_code:
        return False, latex_code

    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
        temp_filename = temp_file.name
        
        # 环境统一预处理
        prepre = latex_code
        
        # 将align*等环境转换为aligned环境
        prepre = re.sub(r'\\begin{(split|align|alignedat|alignat|eqnarray)\*?}((?:(?!\\begin{|\\end{).)*?)\\end{\1\*?}',
                       r'\\begin{aligned}\2\\end{aligned}', prepre, flags=re.S)
        
        if CONFIG.get('unify_environments', False):
            prepre = re.sub(r'\\begin{(smallmatrix)\*?}(.+?)\\end{\1\*?}',
                           r'\\begin{matrix}\2\\end{matrix}', prepre, flags=re.S)
        
        temp_file.write(prepre)
    
    try:
        js_script = JS_DIR / "preprocess_formula_refactored.js"
        if not js_script.exists():
            return True, prepre

        # 构建命令
        if os.name == 'nt':
            cmd = f'type "{temp_filename}" | node "{js_script}" normalize'
        else:
            cmd = f'cat "{temp_filename}" | node "{js_script}" normalize'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              timeout=30, encoding='utf-8', errors='replace')

        if result.returncode != 0:
            return True, prepre

        output = result.stdout
        if not output or not output.strip():
            return True, prepre

        output = output.strip()

        # 操作符名称规范化
        operators = '|'.join(['arccos', 'arcsin', 'arctan', 'arg', 'cos', 'cosh', 'cot', 'coth', 'csc', 'deg', 'det', 'dim', 'exp', 'gcd', 'hom', 'inf',
                             'injlim', 'ker', 'lg', 'lim', 'liminf', 'limsup', 'ln', 'log', 'max', 'min', 'Pr', 'projlim', 'sec', 'sin', 'sinh', 'sup', 'tan', 'tanh'])
        ops = re.compile(r'\\operatorname {(' + operators + ')}')

        names = ['\\' + x.replace(' ', '') for x in re.findall(ops, output)]
        if names:
            output = re.sub(ops, lambda match: names.pop(0), output)

        return True, output

    except Exception:
        return True, prepre
    finally:
        try:
            os.unlink(temp_filename)
        except:
            pass

def latex_aware_tokenizer(text):
    """LaTeX语法感知的tokenizer"""
    tokens = []
    i = 0

    while i < len(text):
        # 跳过普通空格
        if text[i] == ' ' and (i == 0 or text[i-1] != '\\'):
            i += 1
            continue

        # 处理LaTeX命令
        if text[i] == '\\':
            token, next_i = _parse_latex_command(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理花括号组
        if text[i] == '{':
            token, next_i = _parse_brace_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理方括号组
        if text[i] == '[':
            token, next_i = _parse_bracket_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理特殊字符
        if text[i] in '()[]':
            tokens.append(text[i])
            i += 1
            continue

        # 处理普通字符序列
        token, next_i = _parse_normal_sequence(text, i)
        if token:
            tokens.append(token)
        i = next_i

    return [t for t in tokens if t.strip()]

def _parse_latex_command(text, start):
    """解析LaTeX命令"""
    if start >= len(text) or text[start] != '\\':
        return None, start + 1

    i = start + 1

    # 处理转义字符和特殊命令
    if i < len(text) and text[i] in '{}[]$%&#_\\,:;!|=^.~"\'`()/-<>':
        return f'\\{text[i]}', i + 1

    # 处理强制空格命令
    if i < len(text) and text[i] == ' ':
        return '\\ ', i + 1

    # 处理普通命令
    command = '\\'
    while i < len(text) and (text[i].isalpha() or text[i] in '*'):
        command += text[i]
        i += 1

    return command, i

def _parse_brace_group(text, start):
    """解析花括号组"""
    if start >= len(text) or text[start] != '{':
        return None, start + 1

    brace_count = 1
    i = start + 1

    while i < len(text) and brace_count > 0:
        if text[i] == '{':
            brace_count += 1
        elif text[i] == '}':
            brace_count -= 1
        i += 1

    if brace_count == 0:
        return text[start:i], i
    else:
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_bracket_group(text, start):
    """解析方括号组"""
    if start >= len(text) or text[start] != '[':
        return None, start + 1

    bracket_count = 1
    i = start + 1

    while i < len(text) and bracket_count > 0:
        if text[i] == '[':
            bracket_count += 1
        elif text[i] == ']':
            bracket_count -= 1
        i += 1

    if bracket_count == 0:
        return text[start:i], i
    else:
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_normal_sequence(text, start):
    """解析普通字符序列"""
    i = start

    # 特殊处理数字
    if text[i].isdigit():
        if len(text) > start + 1 and text[start + 1].isdigit():
            i = start + 1
        else:
            while i < len(text) and text[i].isdigit():
                i += 1
    else:
        while i < len(text) and text[i] not in '\\{}[] ' and not text[i].isdigit():
            i += 1

    if i > start:
        return text[start:i], i
    else:
        return None, start + 1

def merge_tokens_with_pattern(text, pattern, process_func=None, add_trailing_space=False):
    """通用的token合并函数"""
    old_tokens = re.findall(pattern, text, re.DOTALL)
    if not old_tokens:
        return text
    
    if process_func is None:
        process_func = lambda x: x.replace(" ", "")
    
    for old_token in old_tokens:
        new_token = process_func(old_token)
        if add_trailing_space and not new_token.endswith(" "):
            new_token += " "
        text = text.replace(old_token, new_token)
    
    return text

def remove_trailing_latex(formula):
    """移除LaTeX公式尾部的间距和装饰命令"""
    pattern = r'(\\(hspace\*?\{[^{}]*?\}|vspace\*?\{[^{}]*?\}|smallskip|medskip|quad|qquad|bigskip|[;,])|\~|\.)*$'
    cleaned_formula = re.sub(pattern, '', formula, count=1)
    return cleaned_formula

def normalize_latex(latex_string, rm_trail=False):
    """LaTeX规范化主函数"""
    
    if rm_trail:
        latex_string = remove_trailing_latex(latex_string)

    # 基础矩阵命令替换
    latex_string = latex_string.strip().replace(r'\pmatrix', r'\mypmatrix').replace(r'\matrix', r'\mymatrix')

    # 移除不需要的命令
    for item in ['\\raggedright', '\\arraybackslash', '\\lowercase', '\\uppercase']:
        latex_string = latex_string.replace(item, "")

    # 空格处理
    latex_string = clean_latex_spaces(latex_string)
    latex_string = add_required_spaces(latex_string)

    # Token合并处理
    latex_string = merge_tokens_with_pattern(latex_string, r'\\[hv]space { [.0-9a-z ]+ }')
    
    def process_array_format(token):
        return token.replace("\\begin{array} ", "<s>").replace(" ", "").replace("<s>", "\\begin{array} ")
    latex_string = merge_tokens_with_pattern(latex_string, r'\\begin{array} { [lrc ]+ }', process_array_format)

    latex_string = merge_tokens_with_pattern(latex_string, r'\\string [^ ]+ ', add_trailing_space=True)
    latex_string = merge_tokens_with_pattern(latex_string, r'\\[Bb]ig[g]?[glrm]? [(){}|\[\]] ', add_trailing_space=True)
    
    def process_big_command(token):
        return token.replace(" ", "")
    latex_string = merge_tokens_with_pattern(latex_string, r'\\[Bb]ig[g]?[glrm]?\s*\\[a-zA-Z]+', process_big_command, add_trailing_space=True)
    
    def process_operatorname_star(token):
        return "\\operatorname"
    latex_string = merge_tokens_with_pattern(latex_string, r'\\operatorname \*', process_operatorname_star)

    # 移除有害命令
    latex_string = latex_string.replace("\\lefteqn", "")
    latex_string = latex_string.replace("\\footnote ", "^ ")

    # 重音符号合并
    latex_string = merge_tokens_with_pattern(latex_string, r'\\\' [^{] ', add_trailing_space=True)

    # 其他命令合并
    latex_string = merge_tokens_with_pattern(latex_string, r'\\parbox {[^{]+}')
    
    def process_raisebox(token):
        processed = token.replace(" ", "")
        return processed[0:-1] + " {"
    latex_string = merge_tokens_with_pattern(latex_string, r'\\raisebox {[^{]+} [\[\]0-9 exptcm]+{', process_raisebox)
    
    def process_char_command(token):
        processed = token.replace(" ", "")
        return "{ " + processed[1:-1] + " }"
    latex_string = merge_tokens_with_pattern(latex_string, r'{ \\char[0-9\' ]+}', process_char_command)
    
    latex_string = merge_tokens_with_pattern(latex_string, r'\\rule {[ .0-9a-z]+} {[ .0-9a-z]+}')
    latex_string = merge_tokens_with_pattern(latex_string, r'\\specialrule {[ .0-9a-z]+} {[ .0-9a-z]+} {[ .0-9a-z]+}')

    # 颜色命令移除
    pattern = r'\\colorbox[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\color[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\textcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\cellcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } '
    old_token = re.findall(pattern, latex_string, re.DOTALL)
    for bef in old_token:
        latex_string = latex_string.replace(bef, "")

    # 括号补全处理
    l_split = latex_aware_tokenizer(latex_string)
    idx = 0

    while idx < len(l_split):
        token = l_split[idx]
        if token in ONE_Tail_Tokens + ONE_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split):
                next_token = l_split[idx + 1]
                if next_token != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
        elif token in TWO_Tail_Tokens:
            if idx + 1 < len(l_split):
                current_pos = idx + 1
                if l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}"):
                    current_pos += 1
                elif l_split[current_pos] != "{":
                    l_split.insert(current_pos, "{")
                    l_split.insert(current_pos + 2, "}")
                    current_pos += 3
                else:
                    brace_count = 1
                    current_pos += 1
                    while current_pos < len(l_split) and brace_count > 0:
                        if l_split[current_pos] == "{":
                            brace_count += 1
                        elif l_split[current_pos] == "}":
                            brace_count -= 1
                        current_pos += 1

                if current_pos < len(l_split):
                    if l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}"):
                        current_pos += 1
                    elif l_split[current_pos] != "{":
                        l_split.insert(current_pos, "{")
                        l_split.insert(current_pos + 2, "}")
                        current_pos += 3
                    else:
                        brace_count = 1
                        current_pos += 1
                        while current_pos < len(l_split) and brace_count > 0:
                            if l_split[current_pos] == "{":
                                brace_count += 1
                            elif l_split[current_pos] == "}":
                                brace_count -= 1
                            current_pos += 1

                idx = current_pos - 1
        elif token in TWO_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split):
                if l_split[idx + 1] != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
                if idx + 3 < len(l_split) and l_split[idx + 3] != "{":
                    l_split.insert(idx + 3, "{")
                    l_split.insert(idx + 5, "}")
                    idx += 2
        elif token in AB_Tail_Tokens:
            if token == "\\sqrt":
                if idx + 1 < len(l_split):
                    if l_split[idx + 1] == "[":
                        bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                        if bracket_end != -1:
                            idx = bracket_end
                    if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                        l_split.insert(idx + 1, "{")
                        l_split.insert(idx + 3, "}")
                        idx += 2
            else:
                if idx + 1 < len(l_split):
                    if l_split[idx + 1] == "[":
                        bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                        if bracket_end != -1:
                            idx = bracket_end
                    if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                        l_split.insert(idx + 1, "{")
                        l_split.insert(idx + 3, "}")
                        idx += 2
        idx += 1

    latex_string = ' '.join(l_split)

    # 数学函数标准化
    latex_string = standardize_math_functions(latex_string)

    # 连字符展开和省略号统一化
    latex_string = re.sub(r'---', r'- - -', latex_string)
    latex_string = re.sub(r'--', r'- -', latex_string)

    # 省略号统一化
    latex_string = re.sub(r'\\dotsc', r'. . .', latex_string)
    latex_string = re.sub(r'\\dotsi', r'. . .', latex_string)
    latex_string = re.sub(r'\\dotsm', r'. . .', latex_string)
    latex_string = re.sub(r'\\dotso', r'. . .', latex_string)
    latex_string = re.sub(r'\\dotsb', r'. . .', latex_string)
    latex_string = re.sub(r'…', r'. . .', latex_string)
    latex_string = re.sub(r'\\ldots', r'. . .', latex_string)
    latex_string = re.sub(r'\\hdots', r'. . .', latex_string)
    latex_string = re.sub(r'\\cdots', r'. . .', latex_string)
    latex_string = re.sub(r'\\dddot', r'. . .', latex_string)
    latex_string = re.sub(r'\\dots', r'. . .', latex_string)
    latex_string = re.sub(r'\\mathellipsis', r'. . .', latex_string)

    # 删除空花括号和多余的嵌套花括号
    latex_string = remove_empty_braces(latex_string)
    latex_string = remove_redundant_braces(latex_string)

    return latex_string.strip()

def tokenize_latex_string(latex_string):
    """LaTeX tokenize流程"""
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            return latex_string
        return tokenized_latex
    except Exception as e:
        print(f"tokenize处理出错: {e}")
        return latex_string

def replace_synonym_tokens(tokenized_string):
    """同义词token替换函数"""
    if not tokenized_string or not tokenized_string.strip():
        return tokenized_string

    if not CONFIG.get('enable_synonym_replacement', True):
        return tokenized_string

    csv_file_path = SCRIPT_DIR / "token_unify.csv"
    if not csv_file_path.exists():
        return tokenized_string

    try:
        synonym_map = {}
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            for row_num, row in enumerate(csv_reader, 1):
                if not row or len(row) < 2:
                    continue
                synonym = row[0].strip()
                standard_token = row[1].strip()
                if not synonym or not standard_token:
                    continue
                synonym_map[synonym] = standard_token

        if not synonym_map:
            return tokenized_string

        result = tokenized_string
        sorted_synonyms = sorted(synonym_map.keys(), key=len, reverse=True)

        for synonym in sorted_synonyms:
            standard_token = synonym_map[synonym]
            if synonym in result:
                result = result.replace(synonym, standard_token)

        return result

    except Exception:
        return tokenized_string

def normalize_latex_string(latex_string):
    """完整的LaTeX规范化流程"""
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        # JavaScript AST处理
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            tokenized_latex = latex_string

        # Python规则处理
        normalized_latex = normalize_latex(tokenized_latex, CONFIG.get('remove_trailing', False))
        return normalized_latex

    except Exception as e:
        print(f"规范化处理出错: {e}")
        return latex_string

def validate_config():
    """验证配置参数的有效性"""
    errors = []

    if 'mode' not in CONFIG:
        errors.append("缺少必需参数: 'mode'")
    elif CONFIG['mode'] not in ['tokenize', 'normalize']:
        errors.append(f"无效的mode值: '{CONFIG['mode']}'，应为: 'tokenize', 'normalize'")

    if 'latex_string' not in CONFIG or not CONFIG['latex_string']:
        errors.append("需要设置 'latex_string' 参数")

    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False

    return True

def process_latex():
    """处理LaTeX字符串"""
    latex_string = CONFIG['latex_string']
    mode = CONFIG['mode']

    if mode == 'tokenize':
        tokenized_result = tokenize_latex_string(latex_string)
        result = replace_synonym_tokens(tokenized_result)
    elif mode == 'normalize':
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            tokenized_latex = latex_string
        synonym_replaced_latex = replace_synonym_tokens(tokenized_latex)
        result = normalize_latex(synonym_replaced_latex, CONFIG.get('remove_trailing', False))
    else:
        print(f"未知的模式: {mode}")
        return latex_string

    print("=" * 80)
    print("LaTeX 规范化处理结果")
    print("=" * 80)
    print(f"输入: {latex_string}")
    print(f"输出: {result}")
    print("=" * 80)

    return result

def main():
    """主函数"""
    if not validate_config():
        print("配置错误，请修正后重新运行")
        return
    process_latex()

if __name__ == "__main__":
    main() 