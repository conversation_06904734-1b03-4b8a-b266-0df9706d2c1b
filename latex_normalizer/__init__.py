"""
LaTeX规范化脚本包

基于抽象语法树(AST)的LaTeX公式规范化处理系统。
提供高度可扩展的模块化架构，支持：
- 空格规范化 (FR1)
- 结构规范化 (FR2) 
- Token规范化 (FR3)
- 外部CSV配置文件管理

作者: Augment Agent
版本: 1.0.0
"""

from .main import LaTeXNormalizer
from .core.ast_processor import ASTProcessor
from .normalizers.spacing_normalizer import SpacingNormalizer
from .normalizers.structure_normalizer import StructureNormalizer
from .normalizers.token_normalizer import TokenNormalizer
from .config.config_manager import ConfigManager

__version__ = "1.0.0"
__author__ = "Augment Agent"

__all__ = [
    "LaTeXNormalizer",
    "ASTProcessor", 
    "SpacingNormalizer",
    "StructureNormalizer",
    "TokenNormalizer",
    "ConfigManager"
]
