#!/usr/bin/env python3
"""
LaTeX规范化器测试脚本

测试新实现的模块化LaTeX规范化系统的各项功能。
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from latex_normalizer import LaTeXNormalizer


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试基本功能")
    print("=" * 60)
    
    normalizer = LaTeXNormalizer()
    
    # 测试用例
    test_cases = [
        # FR1: 空格规范化测试
        "\\frac {a} {b}",  # 应该变为 \frac{a}{b}
        "a + b",           # 应该变为 a+b
        "( x )",           # 应该变为 (x)
        "\\quad x",        # 应该保留 \quad
        
        # FR2: 结构规范化测试
        "\\begin{align} x = y \\end{align}",      # 应该变为 aligned
        "\\begin{split} a = b \\end{split}",      # 应该变为 aligned
        "\\begin{smallmatrix} 1 & 2 \\end{smallmatrix}",  # 应该变为 matrix
        
        # FR3: Token规范化测试
        "\\operatorname{sin} x",  # 应该变为 \sin x
        "\\mathrm{cos} y",        # 应该变为 \cos y
        "\\gets",                 # 应该变为 \leftarrow (如果配置了)
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case}")
        result = normalizer.normalize(test_case)
        print(f"结果: {result}")
        print(f"改变: {'是' if test_case != result else '否'}")


def test_system_validation():
    """测试系统验证"""
    print("\n" + "=" * 60)
    print("测试系统验证")
    print("=" * 60)
    
    normalizer = LaTeXNormalizer()
    
    # 验证系统
    is_valid, errors = normalizer.validate_system()
    print(f"系统有效性: {'有效' if is_valid else '无效'}")
    
    if errors:
        print("错误信息:")
        for error in errors:
            print(f"  - {error}")
    
    # 显示系统信息
    info = normalizer.get_system_info()
    print(f"\nAST处理器可用: {info['components']['ast_processor_available']}")
    print(f"配置有效: {info['components']['config_valid']}")
    print(f"同义词数量: {info['config_manager']['synonym_count']}")


def test_analysis_functionality():
    """测试分析功能"""
    print("\n" + "=" * 60)
    print("测试分析功能")
    print("=" * 60)
    
    normalizer = LaTeXNormalizer()
    
    test_latex = "\\operatorname{sin} \\frac {a} {b} + \\begin{align} x = y \\end{align}"
    
    analysis = normalizer.analyze(test_latex)
    
    print(f"输入长度: {analysis['input_length']}")
    print(f"输入有效: {analysis['input_valid']}")
    print(f"空格统计: {analysis['spacing_stats']}")
    print(f"结构统计: {analysis['structure_stats']}")
    print(f"Token统计: {analysis['token_stats']}")
    print(f"规范化改变: {analysis['normalization_preview']['changed']}")


def test_individual_normalizers():
    """测试各个规范化器"""
    print("\n" + "=" * 60)
    print("测试各个规范化器")
    print("=" * 60)
    
    normalizer = LaTeXNormalizer()
    
    test_latex = "\\operatorname{sin} \\frac {a} {b} + \\begin{align} x = y \\end{align}"
    
    print(f"原始: {test_latex}")
    
    # 测试空格规范化
    spacing_result = normalizer.spacing_normalizer.normalize(test_latex)
    print(f"空格规范化: {spacing_result}")
    
    # 测试结构规范化
    structure_result = normalizer.structure_normalizer.normalize(test_latex)
    print(f"结构规范化: {structure_result}")
    
    # 测试Token规范化
    token_result = normalizer.token_normalizer.normalize(test_latex)
    print(f"Token规范化: {token_result}")
    
    # 测试完整规范化
    full_result = normalizer.normalize(test_latex)
    print(f"完整规范化: {full_result}")


def test_configuration():
    """测试配置功能"""
    print("\n" + "=" * 60)
    print("测试配置功能")
    print("=" * 60)
    
    normalizer = LaTeXNormalizer()
    
    # 测试配置信息
    config_info = normalizer.config_manager.get_config_info()
    print(f"配置文件: {config_info['config_file']}")
    print(f"文件存在: {config_info['file_exists']}")
    print(f"同义词数量: {config_info['synonym_count']}")
    
    # 测试同义词替换
    test_text = "\\operatorname{sin} x + \\gets y"
    result, count = normalizer.config_manager.replace_tokens_in_text(test_text)
    print(f"\n同义词替换测试:")
    print(f"原始: {test_text}")
    print(f"结果: {result}")
    print(f"替换次数: {count}")


def main():
    """主测试函数"""
    print("LaTeX规范化器测试")
    print("基于AST的模块化LaTeX规范化系统")
    
    try:
        test_system_validation()
        test_basic_functionality()
        test_individual_normalizers()
        test_analysis_functionality()
        test_configuration()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
