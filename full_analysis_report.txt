============================================================
LaTeX公式Token长度和复杂度分析报告
============================================================

1. 基本统计信息
------------------------------
总公式数量: 9443
有效公式数量: 9443
空公式数量: 0

2. Token长度分析
------------------------------
字符数统计:
  平均值: 148.30
  中位数: 132.00
  标准差: 73.46
  最小值: 4
  最大值: 504

空格分割Token统计:
  平均值: 55.81
  中位数: 51.00
  标准差: 27.09
  最小值: 1
  最大值: 150

3. 复杂度分析
------------------------------
复杂度分数统计:
  平均值: 50.24
  中位数: 45.00
  标准差: 24.43
  最小值: 1.50
  最大值: 177.50

4. 复杂度等级分布
------------------------------
简单: 2368 (25.1%)
中等: 2427 (25.7%)
复杂: 2327 (24.6%)
极复杂: 2321 (24.6%)

5. 最简单的公式示例
------------------------------
1. 复杂度: 1.50
   公式: S = -

2. 复杂度: 2.00
   公式: \times

3. 复杂度: 2.00
   公式: \Phi

4. 复杂度: 2.00
   公式: \langle

5. 复杂度: 3.00
   公式: \{ F , G \} =

6. 最复杂的公式示例
------------------------------
1. 复杂度: 154.50
   公式: \gamma ( \beta \sqrt { - \triangle } ) = \frac { 2 \pi ^ { 2 } } { \beta \sqrt { - \triangle } } + \...

2. 复杂度: 155.50
   公式: S = - \frac { 1 } { 2 } \int d ^ { 2 } x d y \Bigl ( i \bar { \psi } \gamma ^ { \mu } \frac { \parti...

3. 复杂度: 155.50
   公式: \left\vert \frac { \mathrm { d } w } { \mathrm { d } \theta } \right\vert = \frac { \left\vert \kapp...

4. 复杂度: 169.50
   公式: \left( \begin{array} { c } { \Phi _ { 1 } } \\ { \Phi _ { 2 } } \\ \end{array} \right) = \frac { 1 }...

5. 复杂度: 177.50
   公式: \left( \begin{matrix} { Z _ { A B } } \\ { Z _ { I } } \\ \end{matrix} \right) = \int \left( \begin{...

============================================================
报告生成完成
============================================================
