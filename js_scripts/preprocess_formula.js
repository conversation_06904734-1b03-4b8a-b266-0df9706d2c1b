const path = require('path');
var katex = require(path.join(__dirname,"third_party/katex/katex.js"))
options = require(path.join(__dirname,"third_party/katex/src/Options.js"))
var readline = require('readline');
var rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: false
});


rl.on('line', function(line){
    // JavaScript AST处理器主函数
    // 数据流: Python输入 → 预处理 → KaTeX解析 → AST渲染 → 标准化输出

    a = line  // 保存原始输入用于调试

    // 步骤1: 注释处理 - 移除LaTeX注释，但保护转义的%
    if (line[0] == "%") {
        line = line.substr(1, line.length - 1);  // 移除行首%注释标记
    }

    // 保护转义的百分号 \%
    line = line.replace(/\\%/g, '__ESCAPED_PERCENT__');
    line = line.split('%')[0];  // 移除行内%注释及其后内容
    // 恢复转义的百分号
    line = line.replace(/__ESCAPED_PERCENT__/g, '\\%');

    // 步骤2: 特殊字符预处理
    line = line.split('\\~').join(' ');  // 将\~替换为空格

    // 步骤3: 清理不需要的LaTeX命令和符号

    line = line.replace(/\\label{.*?}/, ""); // 移除\label{...}标签


    // 步骤4: 换行符处理 - 仅对非结构化内容进行处理
    // 如果不包含matrix、cases、array、align等结构化环境
    if (line.indexOf("matrix") == -1 && line.indexOf("cases")==-1 &&
        line.indexOf("array")==-1 && line.indexOf("align")==-1 &&
        line.indexOf("begin")==-1)  {
        for (var i = 0; i < 300; i++) {
            line = line.replace(/\\\\/, "\\,");  // 将\\换行符替换为\,小间距
        }
    }

    // 步骤5: 添加结尾空格确保解析正确
    line = line + " "

    // global_str: tokenized版本 (在parser.js中构建)
    // norm_str: normalized版本 (在下面的renderer中构建)
    try {
        // 步骤6: 模式选择 - tokenize模式或normalize模式
        if (process.argv[2] == "tokenize") {
            // tokenize模式: 仅解析为tokens
            var tree = katex.__parse(line, {});
            console.log(global_str.replace(/\\label { .*? }/, ""));
        } else {
            // normalize模式: 完整的AST处理和规范化

            // 步骤6.1: \rm命令统一化
            // 将各种\rm格式统一为\mathrm格式
            for (var i = 0; i < 300; ++i) {
                line = line.replace(/{\\rm/, "\\mathrm{");   // {\\rm -> \\mathrm{
                line = line.replace(/{ \\rm/, "\\mathrm{");  // { \\rm -> \\mathrm{
                line = line.replace(/\\rm{/, "\\mathrm{");   // \\rm{ -> \\mathrm{
            }

            // 步骤6.2: KaTeX AST解析
            // 输入: 预处理后的LaTeX字符串
            // 输出: 抽象语法树(AST)
            var tree = katex.__parse(line, {});

            // 步骤6.3: AST渲染为标准化LaTeX
            // 核心处理: 遍历AST节点，应用规范化规则
            buildExpression(tree, new options({}));

            // 步骤6.4: 后处理 - 恢复$符号
            for (var i = 0; i < 300; ++i) {
                norm_str = norm_str.replace('SSSSSS', '$');        // 恢复单个$
                norm_str = norm_str.replace(' S S S S S S', '$');  // 恢复分隔的$
            }

            // 步骤6.5: 输出最终结果
            console.log(norm_str.replace(/\\label { .*? }/, ""));
        }
    } catch (e) {
        // 错误处理: 输出调试信息
        console.error(line);
        console.error(norm_str);
        console.error(e);
        console.log();
    }

    // 步骤7: 重置全局变量为下一行处理做准备
    global_str = ""
    norm_str = ""
})



// LaTeX AST到LaTeX渲染器 (修改版的KaTeX AST-> MathML)
// 核心功能: 将KaTeX解析的AST重新渲染为标准化的LaTeX字符串
norm_str = ""

var groupTypes = {};

// 数学普通字符处理 (变量、数字等)
groupTypes.mathord = function(group, options) {
    // 输入: AST节点包含字符值和字体选项
    // 输出: 规范化的字符序列
    if (options.font == "mathrm"){
        // mathrm字体: 每个字符单独处理，空格特殊处理
        for (i = 0; i < group.value.length; ++i ) {
            if (group.value[i] == " ") {
                norm_str = norm_str + group.value[i] + "\; ";  // 空格后添加\;
            } else {
                norm_str = norm_str + group.value[i] + " ";    // 普通字符后添加空格
            }
        }
    } else {
        // 其他字体: 直接输出值
        norm_str = norm_str + group.value + " ";
    }
};

// 文本字符处理
groupTypes.textord = function(group, options) {
    // 处理普通文本字符，直接输出
    norm_str = norm_str + group.value + " ";
};

// 二元运算符处理 (+, -, *, 等)
groupTypes.bin = function(group) {
    // 输入: 二元运算符AST节点
    // 输出: 运算符 + 空格
    norm_str = norm_str + group.value + " ";
};

// 关系运算符处理 (=, <, >, 等)
groupTypes.rel = function(group) {
    // 输入: 关系运算符AST节点
    // 输出: 关系符 + 空格
    norm_str = norm_str + group.value + " ";
};

// 开括号处理 (, [, {, 等
groupTypes.open = function(group) {
    // 输入: 开括号AST节点
    // 输出: 开括号 + 空格
    norm_str = norm_str + group.value + " ";
};

// 闭括号处理 ), ], }, 等
groupTypes.close = function(group) {
    // 输入: 闭括号AST节点
    // 输出: 闭括号 + 空格
    norm_str = norm_str + group.value + " ";
};

// 内部元素处理
groupTypes.inner = function(group) {
    // 输入: 内部元素AST节点
    // 输出: 元素值 + 空格
    norm_str = norm_str + group.value + " ";
};

// 标点符号处理
groupTypes.punct = function(group) {
    // 输入: 标点符号AST节点
    // 输出: 标点 + 空格
    norm_str = norm_str + group.value + " ";
};

// 有序组处理 (简化的合理版本)
groupTypes.ordgroup = function(group, options) {
    // 输入: 包含多个子表达式的有序组
    // 输出: { 子表达式... } 或直接输出（根据上下文决定）

    // 简化的判断逻辑：主要基于上下文和内容复杂度
    var needsBraces = false;

    // 1. 在特殊上下文中通常不需要额外花括号
    var inSpecialContext = options && (
        options.inSupSub || options.inFont || options.inAccent ||
        options.inFrac || options.inSqrt
    );

    if (!inSpecialContext) {
        // 2. 不在特殊上下文中时，根据内容决定
        if (group.value && group.value.length > 1) {
            // 多个元素通常需要花括号
            needsBraces = true;
        } else if (group.value && group.value.length === 1) {
            // 单个元素：检查是否是复杂结构
            var element = group.value[0];
            if (element.type === 'supsub' || element.type === 'genfrac' ||
                element.type === 'sqrt' || element.type === 'accent') {
                needsBraces = true;
            }
        }
    }

    if (needsBraces) {
        norm_str = norm_str + "{";
    }

    buildExpression(group.value, options);  // 递归处理组内表达式

    if (needsBraces) {
        norm_str = norm_str + "}";
    }

    // 只在需要时添加空格（不在特殊上下文中）
    if (!inSpecialContext) {
        norm_str = norm_str + " ";
    }
};

// 文本模式处理
groupTypes.text = function(group, options) {
    // 输入: 文本模式的AST节点
    // 输出: \mathrm { 文本内容 }
    norm_str = norm_str + "\\mathrm { ";

    buildExpression(group.value.body, options);  // 处理文本内容
    norm_str = norm_str + "} ";
};

// 颜色处理 (通常在规范化中忽略)
groupTypes.color = function(group, options) {
    // 输入: 颜色AST节点
    // 输出: MathML节点 (在LaTeX规范化中不常用)
    var inner = buildExpression(group.value.value, options);

    var node = new mathMLTree.MathNode("mstyle", inner);
    node.setAttribute("mathcolor", group.value.color);

    return node;
};

// 上下标处理 (重新设计的合理版本)
groupTypes.supsub = function(group, options) {
    // 输入: 包含base、sup(上标)、sub(下标)的AST节点
    // 输出: base_sub^sup 或 base^sup 或 base_sub

    // 辅助函数：判断是否需要花括号
    function needsBraces(node) {
        if (!node) return false;

        // 单个字符或数字不需要花括号
        if (node.type === 'mathord' || node.type === 'textord') {
            return false;
        }

        // ordgroup如果只包含一个简单元素，不需要花括号
        if (node.type === 'ordgroup' && node.value && node.value.length === 1) {
            var element = node.value[0];
            if (element.type === 'mathord' || element.type === 'textord') {
                return false;
            }
        }

        // 其他情况需要花括号
        return true;
    }

    // 辅助函数：渲染上下标内容
    function renderSupSubContent(node, options) {
        if (node.type === 'ordgroup') {
            // 对于ordgroup，直接处理其内容
            buildExpression(node.value, options);
        } else {
            buildGroup(node, options);
        }
    }

    // 步骤1: 处理基础表达式
    buildGroup(group.value.base, options);

    // 步骤2: 处理下标 (注意：下标在前，上标在后，这是LaTeX的标准顺序)
    if (group.value.sub) {
        norm_str = norm_str + "_";

        var subOptions = Object.assign({}, options, {inSupSub: true});

        if (needsBraces(group.value.sub)) {
            norm_str = norm_str + "{";
            renderSupSubContent(group.value.sub, subOptions);
            norm_str = norm_str + "}";
        } else {
            renderSupSubContent(group.value.sub, subOptions);
        }
    }

    // 步骤3: 处理上标
    if (group.value.sup) {
        norm_str = norm_str + "^";

        var supOptions = Object.assign({}, options, {inSupSub: true});

        if (needsBraces(group.value.sup)) {
            norm_str = norm_str + "{";
            renderSupSubContent(group.value.sup, supOptions);
            norm_str = norm_str + "}";
        } else {
            renderSupSubContent(group.value.sup, supOptions);
        }
    }

    // 在上下标后添加空格分隔
    norm_str = norm_str + " ";
};

// 广义分数处理 (分数和二项式系数) - 优化版本
groupTypes.genfrac = function(group, options) {
    // 输入: 包含分子、分母和是否有分数线的AST节点
    // 输出: \frac{分子}{分母} 或 \binom{上}{下}

    if (!group.value.hasBarLine) {
        // 无分数线: 二项式系数
        norm_str = norm_str + "\\binom ";
    } else {
        // 有分数线: 普通分数
        norm_str = norm_str + "\\frac ";
    }

    // 处理分子/上部 - 优化：避免双重花括号
    norm_str = norm_str + "{";

    // 创建分数内部的上下文选项
    var fracOptions = Object.assign({}, options, {inFrac: true});

    if (group.value.numer.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容，避免双重花括号
        buildExpression(group.value.numer.value, fracOptions);
    } else {
        buildGroup(group.value.numer, fracOptions);
    }

    norm_str = norm_str + "} ";

    // 处理分母/下部 - 优化：避免双重花括号
    norm_str = norm_str + "{";

    if (group.value.denom.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容，避免双重花括号
        buildExpression(group.value.denom.value, fracOptions);
    } else {
        buildGroup(group.value.denom, fracOptions);
    }

    norm_str = norm_str + "} ";
};

// 数组/矩阵处理 (核心结构化处理)
groupTypes.array = function(group, options) {
    // 输入: 包含行列数据和对齐信息的数组AST节点
    // 输出: \begin{环境名}{对齐} 行数据... \end{环境名}

    // 步骤1: 智能确定环境名称 - 支持嵌套环境
    var envName = "aligned";  // 默认统一为aligned环境

    // 检查AST节点是否有环境信息
    if (group.value && group.value.envName) {
        envName = group.value.envName;
    } else {
        // 从输入字符串中推断环境类型
        var currentInput = a || "";  // a是全局变量，保存原始输入

        // 智能环境识别：优先识别特定用途的环境
        if (currentInput.indexOf("\\begin{cases}") !== -1) {
            envName = "cases";
        } else if (currentInput.indexOf("\\begin{array}") !== -1) {
            // array环境通常用于复杂的表格或分段函数，保持原样
            envName = "array";
        } else if (currentInput.indexOf("\\begin{pmatrix}") !== -1) {
            envName = "pmatrix";
        } else if (currentInput.indexOf("\\begin{bmatrix}") !== -1) {
            envName = "bmatrix";
        } else if (currentInput.indexOf("\\begin{vmatrix}") !== -1) {
            envName = "vmatrix";
        } else if (currentInput.indexOf("\\begin{Vmatrix}") !== -1) {
            envName = "Vmatrix";
        } else if (currentInput.indexOf("\\begin{matrix}") !== -1) {
            envName = "matrix";
        } else if (currentInput.indexOf("\\begin{aligned}") !== -1) {
            envName = "aligned";
        } else if (currentInput.indexOf("\\begin{align") !== -1) {
            // 所有align类型环境统一为aligned（这些在Python中已经被转换为aligned）
            envName = "aligned";
        } else {
            // 默认使用aligned环境
            envName = "aligned";
        }
    }

    // 步骤2: 开始环境
    norm_str = norm_str + "\\begin{" + envName + "}";

    // 步骤3: 处理列对齐信息（仅对array环境）
    if (envName === "array") {
        norm_str = norm_str + " { ";
        if (group.value.cols) {
            // 使用指定的列对齐
            group.value.cols.map(function(start) {
                if (start && start.align) {
                    norm_str = norm_str + start.align + " ";  // l, c, r等对齐方式
                }
            });
        } else {
            // 默认左对齐
            group.value.body[0].map(function(start) {
                norm_str = norm_str + "l ";  // 默认左对齐
            });
        }
        norm_str = norm_str + "} ";
    }
    // 修复：对于非array环境，不添加额外空格，避免\begin{aligned} 中间出现空格

    // 步骤4: 处理行数据
    var totalRows = group.value.body.length;
    var processedRows = 0;
    var isAlignEnvironment = (envName === "aligned");

    group.value.body.map(function(row, rowIndex) {
        // 检查行是否有内容 (修复原始代码的bug)
        if (row.some(cell => cell.value.length > 0)) {
            processedRows++;

            // 处理行中的每个单元格
            out = row.map(function(cell) {
                // 在align环境中，避免为简单单元格添加不必要的花括号
                if (isAlignEnvironment) {
                    // 检查单元格是否简单（只包含一个元素且不是复杂结构）
                    var isSimpleCell = cell.value && cell.value.length === 1;
                    var isComplexCell = false;
                    
                    if (isSimpleCell) {
                        var element = cell.value[0];
                        isComplexCell = (element.type === 'genfrac' || element.type === 'sqrt' || 
                                       element.type === 'supsub' || element.type === 'accent' ||
                                       element.type === 'ordgroup');
                    }
                    
                    if (isSimpleCell && !isComplexCell) {
                        // 简单单元格，直接处理内容
                        buildExpression(cell.value, options);
                    } else {
                        // 复杂单元格，正常处理
                        buildGroup(cell, options);
                    }
                } else {
                    // 非align环境，正常处理
                    buildGroup(cell, options);
                }

                // 清理空的{}组
                if (norm_str.length > 4
                    && norm_str.substring(norm_str.length-4, norm_str.length) == "{ } ") {
                    norm_str = norm_str.substring(0, norm_str.length-4);
                }
                norm_str = norm_str + "& ";  // 添加列分隔符
            });

            // 移除最后的&
            norm_str = norm_str.substring(0, norm_str.length-2);

            // 只有在以下情况才添加行分隔符：
            // 1. 不是最后一行，或者
            // 2. 是array环境且有多行，或者
            // 3. 是多行的aligned/matrix环境
            var shouldAddRowSeparator = false;

            if (envName === "array") {
                // array环境：如果不是最后一行就添加行分隔符
                shouldAddRowSeparator = (rowIndex < totalRows - 1);
            } else {
                // aligned/matrix等环境：只有多行时才添加行分隔符，且不是最后一行
                shouldAddRowSeparator = (totalRows > 1 && rowIndex < totalRows - 1);
            }

            if (shouldAddRowSeparator) {
                norm_str = norm_str + "\\\\ ";
            } else {
                norm_str = norm_str + " ";  // 添加空格分隔
            }
        }
    });

    // 步骤5: 结束环境
    norm_str = norm_str + "\\end{" + envName + "} ";
};

// 根号处理 (平方根和n次根) - 优化版本
groupTypes.sqrt = function(group, options) {
    // 输入: 根号AST节点，可能包含指数和被开方数
    // 输出: \sqrt[n]{expr} 或 \sqrt{expr}
    var node;

    if (group.value.index) {
        // n次根: \sqrt[n]{expr}
        norm_str = norm_str + "\\sqrt [ ";
        buildExpression(group.value.index.value, options);  // 处理根指数
        norm_str = norm_str + "] ";
    } else {
        // 平方根: \sqrt{expr}
        norm_str = norm_str + "\\sqrt ";
    }

    // 处理被开方数 - 优化：确保花括号正确，避免双重嵌套
    norm_str = norm_str + "{";

    // 创建根号内部的上下文选项
    var sqrtOptions = Object.assign({}, options, {inSqrt: true});

    if (group.value.body.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容，避免双重花括号
        buildExpression(group.value.body.value, sqrtOptions);
    } else {
        buildGroup(group.value.body, sqrtOptions);
    }

    norm_str = norm_str + "} ";
};

// 左右定界符处理 (括号、方括号等的自动调整大小)
groupTypes.leftright = function(group, options) {
    // 输入: 包含左定界符、内容、右定界符的AST节点
    // 输出: \left( 内容 \right) 或类似结构
    // 数据流: \left( → 内容处理 → \right)

    // 步骤1: 添加左定界符
    norm_str = norm_str + "\\left" + group.value.left + " ";

    // 步骤2: 处理定界符内的表达式
    buildExpression(group.value.body, options);

    // 步骤3: 添加右定界符
    norm_str = norm_str + "\\right" + group.value.right + " ";
};

// 重音符号处理 (帽子、波浪线等) - 修复版本
groupTypes.accent = function(group, options) {
    // 输入: 重音符号AST节点，包含重音类型和基础表达式
    // 输出: \hat{x} 或 \tilde{expr} 等

    // 修复：重音符号的参数总是需要用花括号包围，确保语法正确
    // 这样可以避免 \bar U _ B 变成 \bar U_ {B} 的问题
    norm_str = norm_str + group.value.accent + " { ";

    // 创建重音符号内部的上下文选项
    var accentOptions = Object.assign({}, options, {inAccent: true});

    if (group.value.base.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容，避免双重花括号
        buildExpression(group.value.base.value, accentOptions);
    } else {
        // 对于其他类型，正常处理
        buildGroup(group.value.base, accentOptions);
    }

    norm_str = norm_str + "} ";
};

// 间距处理 (空格和各种间距命令)
groupTypes.spacing = function(group, options) {
    // 输入: 间距AST节点，包含间距类型和值
    // 输出: 标准化的间距命令
    var node;

    if (group.value == " ") {
        // 修复：在text模式下保持原始空格，其他模式转换为不换行空格
        if (options && options.font == "text") {
            norm_str = norm_str + " ";  // text模式下保持原始空格
        } else {
            norm_str = norm_str + "~ ";  // 其他模式转换为不换行空格
        }
    } else {
        // 其他间距命令直接输出 (\,, \;, \quad等)
        norm_str = norm_str + group.value + " ";
    }
    return node;
};

// 操作符处理 (求和、积分、极限等大型操作符)
groupTypes.op = function(group) {
    // 输入: 操作符AST节点，可能是符号或操作符名称
    // 输出: 符号或\operatorname{name}，可能包含\limits修饰符
    var node;

    // TODO(emily): 使用`largeop`属性处理大型操作符

    if (group.value.symbol) {
        // 符号类操作符: 输出符号 (如∑, ∫等)
        norm_str = norm_str + group.value.body;

        // 只有当显式设置了limits修饰符时才输出
        // 检查是否有alwaysHandleSupSub属性，这表示用户显式使用了\limits或\nolimits
        if (group.value.alwaysHandleSupSub === true) {
            if (group.value.limits === true) {
                norm_str = norm_str + "\\limits ";
            } else if (group.value.limits === false) {
                norm_str = norm_str + "\\nolimits ";
            } else {
                norm_str = norm_str + " ";
            }
        } else {
            // 没有显式设置limits，保持默认行为
            norm_str = norm_str + " ";
        }
    } else {
        // 命名操作符: 使用\operatorname包装
        // 修复：对于数学函数，直接输出原始命令，不添加空格分隔
        var opName = group.value.body;
        
        // 移除开头的反斜杠，获取纯函数名
        var cleanOpName = opName.replace(/^\\/, '');
        
        // 检查是否是常见的数学函数
        var mathFunctions = ['sin', 'cos', 'tan', 'cot', 'sec', 'csc', 'sinh', 'cosh', 'tanh', 'coth',
                           'arcsin', 'arccos', 'arctan', 'ln', 'lg', 'log', 'exp', 'min', 'max', 'mod',
                           'deg', 'arg', 'dim', 'ker', 'hom', 'lim', 'sup', 'inf', 'det', 'gcd', 'Pr'];
        
        // 如果操作符名称在数学函数列表中，直接输出原始命令
        if (mathFunctions.indexOf(cleanOpName) !== -1) {
            norm_str = norm_str + "\\" + cleanOpName + " ";
        } else {
            // 其他操作符使用\operatorname包装
            if (group.value.limits == false) {
                // 无上下限的操作符
                norm_str = norm_str + "\\operatorname { ";
            } else {
                // 有上下限的操作符 (使用*版本)
                norm_str = norm_str + "\\operatorname* { ";
            }

            // 输出操作符名称 (跳过第一个字符，通常是\)
            for (i = 1; i < group.value.body.length; ++i ) {
                norm_str = norm_str + group.value.body[i] + " ";
            }
            norm_str = norm_str + "} ";
        }
    }
};

// KaTeX标识处理 (通常不在规范化中使用)
groupTypes.katex = function(group) {
    // 输入: KaTeX标识AST节点
    // 输出: MathML文本节点 (在LaTeX规范化中很少用到)
    var node = new mathMLTree.MathNode(
        "mtext", [new mathMLTree.TextNode("KaTeX")]);

    return node;
};



// 字体处理 (数学字体命令) - 改进版本
groupTypes.font = function(group, options) {
    // 输入: 字体AST节点，包含字体类型和内容
    // 输出: \mathbf{...}, \mathrm{...}, \mathcal{...}等
    var font = group.value.font;

    // 字体名称标准化: mbox和hbox统一为mathrm
    if (font == "mbox" || font == "hbox") {
        font = "text";
    }

    // 输出字体命令和开始花括号
    norm_str = norm_str + "\\" + font + "{";

    // 处理字体内容，避免双重花括号
    // 创建字体上下文选项
    var fontOptions = options.withFont(font);
    fontOptions.inFont = true;

    if (group.value.body.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容，跳过ordgroup的花括号
        buildExpression(group.value.body.value, fontOptions);
    } else {
        // 对于其他类型，正常处理
        buildGroup(group.value.body, fontOptions);
    }

    // 输出结束花括号
    norm_str = norm_str + "}";
};

// 定界符大小调整处理 (\big, \Big, \bigg等)
groupTypes.delimsizing = function(group) {
    // 输入: 定界符大小调整AST节点
    // 输出: \big(, \Big[, \bigg\{等
    var children = [];

    // 输出函数名和定界符值
    // 例: \big + ( → \big(
    norm_str = norm_str + group.value.funcName + " " + group.value.value + " ";
};

// 样式处理 (数学样式命令)
groupTypes.styling = function(group, options) {
    // 输入: 样式AST节点，包含样式类型和内容
    // 输出: \displaystyle, \textstyle, \scriptstyle等

    // 输出原始样式命令
    norm_str = norm_str + " " + group.value.original + " ";

    // 处理样式内的表达式
    buildExpression(group.value.value, options);
};

// 大小调整处理 (字体大小命令)
groupTypes.sizing = function(group, options) {
    // 输入: 大小调整AST节点，包含大小类型和内容
    // 输出: 标准化的大小命令

    if (group.value.original == "\\rm") {
        // 特殊处理: \rm转换为\mathrm
        norm_str = norm_str + "\\mathrm { ";
        buildExpression(group.value.value, options.withFont("mathrm"));
        norm_str = norm_str + "} ";
    } else {
        // 其他大小命令: \tiny, \small, \large等
        norm_str = norm_str + " " + group.value.original + " ";
        buildExpression(group.value.value, options);
    }
};

// 上划线处理
groupTypes.overline = function(group, options) {
    // 输入: 上划线AST节点，包含需要加上划线的表达式
    // 输出: \overline{表达式}
    norm_str = norm_str + "\\overline { ";

    // 处理上划线内的表达式
    buildGroup(group.value.body, options);
    norm_str = norm_str + "} ";
};

// 下划线处理
groupTypes.underline = function(group, options) {
    // 输入: 下划线AST节点，包含需要加下划线的表达式
    // 输出: \underline{表达式}
    norm_str = norm_str + "\\underline { ";

    // 处理下划线内的表达式
    buildGroup(group.value.body, options);
    norm_str = norm_str + "} ";
};

// 规则线处理 (绘制指定尺寸的线条)
groupTypes.rule = function(group) {
    // 输入: 规则线AST节点，包含宽度和高度信息
    // 输出: \rule{宽度}{高度}

    // 格式化输出: \rule { 宽度数值 单位 } { 高度数值 单位 }
    norm_str = norm_str + "\\rule { " + group.value.width.number + " " + group.value.width.unit + " } { " + group.value.height.number + " " + group.value.height.unit + " } ";
};

// 左重叠处理 (左对齐重叠)
groupTypes.llap = function(group, options) {
    // 输入: 左重叠AST节点
    // 输出: \llap{内容} - 内容向左重叠，不占用水平空间
    norm_str = norm_str + "\\llap ";
    buildGroup(group.value.body, options);
};

// 右重叠处理 (右对齐重叠)
groupTypes.rlap = function(group, options) {
    // 输入: 右重叠AST节点
    // 输出: \rlap{内容} - 内容向右重叠，不占用水平空间
    norm_str = norm_str + "\\rlap ";
    buildGroup(group.value.body, options);
};

// 幻影处理 (占位但不显示)
groupTypes.phantom = function(group, options, prev) {
    // 输入: 幻影AST节点，包含需要隐藏但保留空间的表达式
    // 输出: \phantom{表达式} - 表达式不可见但占用空间
    norm_str = norm_str + "\\phantom { ";

    // 处理幻影内的表达式
    buildExpression(group.value.value, options);
    norm_str = norm_str + "} ";
};

/**
 * 表达式构建函数 - AST渲染的核心递归函数
 *
 * 功能: 遍历AST节点列表，逐个调用相应的处理函数
 * 数据流: AST节点数组 → 逐个处理 → 累积到norm_str
 *
 * @param {Array} expression - AST节点数组
 * @param {Object} options - 渲染选项(字体、样式等)
 */
var buildExpression = function(expression, options) {
    var groups = [];

    // 遍历表达式中的每个AST节点
    for (var i = 0; i < expression.length; i++) {
        var group = expression[i];
        // 递归处理每个节点
        buildGroup(group, options);
    }
    // console.log(norm_str);  // 调试用
    // return groups;          // 在LaTeX渲染中不需要返回值
};

/**
 * 单个AST节点处理函数 - 分发器
 *
 * 功能: 根据AST节点类型调用相应的groupTypes处理函数
 * 数据流: AST节点 → 类型识别 → 调用对应处理函数 → 更新norm_str
 *
 * @param {Object} group - 单个AST节点
 * @param {Object} options - 渲染选项
 */
var buildGroup = function(group, options) {
    if (groupTypes[group.type]) {
        // 调用对应类型的处理函数
        groupTypes[group.type](group, options);
    } else {
        // 未知节点类型错误处理
        throw new ParseError(
            "Got group of unknown type: '" + group.type + "'");
    }
};


