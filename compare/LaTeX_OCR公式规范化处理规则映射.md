# LaTeX-OCR LaTeX规范化处理规则

## 概述

LaTeX-OCR项目通过四个核心阶段实现LaTeX公式的规范化处理：
1. **宏展开阶段** - 移除注释、转换命令定义、展开嵌套命令
2. **公式提取阶段** - 识别数学环境、移除标签、清理空白字符
3. **预处理阶段** - 环境标准化、操作符标准化、格式修复
4. **语法标准化阶段** - KaTeX解析、AST节点处理、Token标准化

## 环境层面统一化规则

### 宏展开规则

#### 注释处理规则
```
% 注释内容 → (移除)
\n\n\n → \n
```

#### 命令转换规则
```
\def\command{内容} → \newcommand*{\command}{内容}
\let\command=\other → \newcommand*{\command}{\other}
```

#### 括号替换规则
```
{外层{内层}} → {外层Ḋ内层Ḍ}
Ḋ → {
Ḍ → }
```

#### 命令展开规则
```
\newcommand{\cmd}[1]{定义#1} → 定义参数值
\newcommand{\cmd}[2][默认]{定义#1#2} → 定义默认值参数值
```

### 数学环境匹配规则
```
美元符号 → $x^2$, $$\int f(x)dx$$
行内公式 → \(x^2\), \[\int f(x)dx\]
方程环境 → \begin{equation}x^2\end{equation}
对齐环境 → \begin{align}x&=y\\y&=z\end{align}
显示数学 → \displaystyle\int f(x)dx
```

### 环境标准化规则
```
\begin{split}...\end{split} → \begin{aligned}...\end{aligned}
\begin{align}...\end{align} → \begin{aligned}...\end{aligned}
\begin{alignedat}...\end{alignedat} → \begin{aligned}...\end{aligned}
\begin{alignat}...\end{alignat} → \begin{aligned}...\end{aligned}
\begin{eqnarray}...\end{eqnarray} → \begin{aligned}...\end{aligned}
\begin{smallmatrix}...\end{smallmatrix} → \begin{matrix}...\end{matrix}
```

## 字体命令统一化规则

### 字体标准化规则
```
{\rm → \mathrm{
{ \rm → \mathrm{
\rm{ → \mathrm{
```

## 结构层面统一化规则

### 标签移除规则
```
\ref{标签} → (移除)
\cite{引用} → (移除)
\label{标签} → (移除)
\eqref{标签} → (移除)
```

### 空白字符清理规则
```
^\ → (移除)
\ $ → (移除)
^~ → (移除)
~$ → (移除)
^\\ → (移除)
\\ $ → (移除)
^\\thinspace → (移除)
\\thinspace$ → (移除)
^\\! → (移除)
\\!$ → (移除)
^\\: → (移除)
\\:$ → (移除)
^\\; → (移除)
\\;$ → (移除)
^\\enspace → (移除)
\\enspace$ → (移除)
^\\quad → (移除)
\\quad$ → (移除)
^\\qquad → (移除)
\\qquad$ → (移除)
^\\hspace{[a-zA-Z0-9]+} → (移除)
\\hspace{[a-zA-Z0-9]+}$ → (移除)
^\\hfill → (移除)
\\hfill$ → (移除)
```

### 公式清理规则
```
\n → (移除)
\notag → (移除)
\nonumber → (移除)
包含tikz的公式 → (过滤)
长度<1字符的公式 → (过滤)
以\结尾的公式 → (过滤)
包含newcommand的公式 → (过滤)
```

### 格式修复规则
```
\\ \end{array} → \end{array}
```

## 操作符名称统一化规则

### 函数名标准化规则
```
\operatorname{sin} → \sin
\operatorname{cos} → \cos
\operatorname{tan} → \tan
\operatorname{log} → \log
\operatorname{ln} → \ln
\operatorname{lim} → \lim
\operatorname{max} → \max
\operatorname{min} → \min
\operatorname{sup} → \sup
\operatorname{inf} → \inf
\operatorname{det} → \det
\operatorname{dim} → \dim
\operatorname{ker} → \ker
\operatorname{hom} → \hom
\operatorname{deg} → \deg
\operatorname{arg} → \arg
\operatorname{Pr} → \Pr
\operatorname{sec} → \sec
\operatorname{csc} → \csc
\operatorname{cot} → \cot
\operatorname{sinh} → \sinh
\operatorname{cosh} → \cosh
\operatorname{tanh} → \tanh
\operatorname{coth} → \coth
\operatorname{arccos} → \arccos
\operatorname{arcsin} → \arcsin
\operatorname{arctan} → \arctan
\operatorname{lg} → \lg
\operatorname{exp} → \exp
\operatorname{gcd} → \gcd
\operatorname{injlim} → \injlim
\operatorname{projlim} → \projlim
\operatorname{liminf} → \liminf
\operatorname{limsup} → \limsup
```

## 语法层面统一化规则

### 预处理规则
```
%注释 → (移除注释部分)
\~ → (空格)
\> → (空格)
$ → (空格)
\label{...} → (移除)
\\ (非矩阵环境) → \,
```

### AST节点处理规则

#### 基础节点类型
```
mathord → 数学符号处理
textord → 文本符号处理
bin → 二元操作符处理
rel → 关系操作符处理
open → 开括号处理
close → 闭括号处理
inner → 内部间距处理
punct → 标点符号处理
```

#### 复杂结构处理
```
ordgroup → { 内容 }
text → \mathrm { 内容 }
supsub → base _ { sub } ^ { sup }
genfrac → \frac 分子 分母 或 \binom 分子 分母
array → \begin{array} { 对齐 } 内容 \end{array}
sqrt → \sqrt [ 指数 ] 内容 或 \sqrt 内容
```

#### 高级功能处理
```
leftright → \left左括号 内容 \right右括号
accent → 重音符号 { 内容 } 或 重音符号 内容
spacing → ~ 或 间距值
op → \operatorname { 内容 } 或 \operatorname* { 内容 }
font → \字体类型 内容
delimsizing → 函数名 值
styling → 原始样式 内容
sizing → \mathrm { 内容 } 或 原始大小 内容
```

#### 装饰功能处理
```
overline → \overline { 内容 }
underline → \underline { 内容 }
rule → \rule { 宽度 } { 高度 }
llap → \llap 内容
rlap → \rlap 内容
phantom → \phantom { 内容 }
```

### 特殊替换规则
```
SSSSSS → $
 S S S S S S → $
```

## 符号层面统一化规则

### Token分隔规则
```
每个LaTeX命令、符号、数字之间用单个空格分隔
花括号前后添加空格：{ 内容 }
上下标前后添加空格：_ { 下标 } 和 ^ { 上标 }
分数前后添加空格：\frac 分子 分母
```

### 过滤规则
```
长度少于5个token的公式 → (移除)
包含\label的公式 → (移除)
解析失败的公式 → (移除)
```

## 输出格式规则

### 最终输出格式
```
\ begin { aligned } x ^ 2 + y ^ 2 = z ^ 2 \ end { aligned }
\ frac { 1 } { 2 } \ pi r ^ 2
\ sqrt { a ^ 2 + b ^ 2 }
```

## 技术实现特点

1. **四阶段处理架构**：从宏展开到语法标准化的递进式优化
2. **AST驱动的核心处理**：利用KaTeX成熟的解析器确保语法正确性
3. **智能算法集成**：括号匹配、深度计数、递归处理等复杂算法
4. **全面的容错机制**：异常情况下返回原始输入，确保系统稳定性
5. **模块化设计理念**：Python和JavaScript各自发挥优势，形成高效协作
6. **Token级标准化**：通过空格分隔实现精确的字符级匹配和评估
7. **机器学习优化**：为模型训练、推理和评估提供标准化数据格式

这套完整的规范化系统确保了不同来源、不同写法的LaTeX代码能够转换为高度标准化、一致性的token序列，为机器学习模型的训练、推理和评估提供了可靠的数据基础，是公式识别和处理领域的重要技术贡献。 