# ==================== 配置区 ====================
# 在这里修改参数，然后直接运行脚本即可

CONFIG = {
    # 运行模式选择
    'mode': 'normalize',  # 可选: 'tokenize', 'normalize'

    # LaTeX字符串处理 - 测试上下标顺序
    'latex_string': 'A^{\\alpha}_{\\beta} + B_{i}^{j} + C^{2}_{n} + D_{\\mu}^{\\nu}',


    # 处理选项
    'remove_trailing': True,         # 是否移除尾部LaTeX命令
    'enable_synonym_replacement': False,  # 是否启用同义词token替换功能
    'unify_environments': True,       # 是否统一对齐环境为aligned/matrix



}

# ==================== 配置区结束 ====================

import os
import re
import subprocess
import tempfile
import csv
from pathlib import Path

# ==================== 常量定义 ====================

# Token类型定义 (从原脚本复制)
SKIP_PATTERNS = [r'\{', r'\}', r'[\[\]]', r'\\begin\{.*?\}', r'\\end\{.*?\}', r'\^', r'\_', r'\\.*rule.*', r'\\.*line.*', r'\[[\-.0-9]+[epm][xtm]\]']
SKIP_Tokens = ['\\', '\\\\', '\\index', '\\a', '&', '$', '\\multirow', '\\def', '\\edef', '\\raggedright', '\\url', '\\cr', '\\ensuremath', '\\left', '\\right',
               '\\mathchoice', '\\scriptstyle', '\\displaystyle', '\\qquad', '\\quad', '\\,', '\\!', '~', '\\boldmath', '\\gdef', '\\today', '\\the']
PHANTOM_Tokens = ['\\fontfamily', '\\vphantom', '\\phantom', '\\rowcolor', '\\ref', '\\thesubequation', '\\global', '\\theboldgroup']
TWO_Tail_Tokens = ['\\frac', '\\binom']
AB_Tail_Tokens = ['\\xrightarrow', '\\xleftarrow', '\\sqrt']        # special token \xxx [] {}
TWO_Tail_Invisb_Tokens = ['\\overset', '\\underset', '\\stackrel']
ONE_Tail_Tokens = ['\\widetilde', '\\overline', '\\hat', '\\widehat', '\\tilde', '\\Tilde', '\\dot', '\\bar', '\\vec', '\\underline', '\\underbrace', '\\check',
                   '\\breve', '\\Bar', '\\Vec', '\\mathring', '\\ddot', '\\Ddot', '\\dddot', '\\ddddot']
ONE_Tail_Invisb_Tokens = ['\\boldsymbol', '\\pmb', '\\textbf', '\\mathrm', '\\mathbf', '\\mathbb', '\\mathcal', '\\textmd', '\\texttt', '\\textnormal',
                          '\\textit', '\\textup', '\\mathop', '\\mathbin', '\\smash', '\\operatorname', '\\textrm', '\\mathfrak', '\\emph',
                          '\\textsf', '\\textsc']

# 特殊保护的命令 - 这些命令的内容不应该被修改
# PROTECTED_COMMANDS = ['\\text']  # 已删除text特殊保护

# 脚本目录
SCRIPT_DIR = Path(__file__).parent
JS_DIR = SCRIPT_DIR / "js_scripts"

# ==================== 辅助函数 ====================

def find_matching_brace(sequence, start_index, brace=['{', '}']):
    """
    查找匹配的括号位置 (从原脚本复制)
    
    Args:
        sequence: token序列
        start_index: 起始位置
        brace: 括号对，默认为['{', '}']
    
    Returns:
        匹配括号的索引，如果未找到返回-1
    """
    left_brace, right_brace = brace
    depth = 0
    for i, char in enumerate(sequence[start_index:], start=start_index):
        if char == left_brace:
            depth += 1
        elif char == right_brace:
            depth -= 1
            if depth == 0:
                return i
    if depth > 0:
        print(f"Warning! found no matching brace in sequence starting at {start_index}")
    return -1

def merge_tokens_with_pattern(text, pattern, process_func=None, add_trailing_space=False):
    """
    通用的token合并函数 - 抽象重复逻辑
    
    Args:
        text: 输入文本
        pattern: 正则表达式模式
        process_func: 处理函数，默认为移除空格
        add_trailing_space: 是否在处理后添加尾随空格
    
    Returns:
        处理后的文本
    """
    old_tokens = re.findall(pattern, text, re.DOTALL)
    if not old_tokens:
        return text
    
    if process_func is None:
        process_func = lambda x: x.replace(" ", "")
    
    for old_token in old_tokens:
        new_token = process_func(old_token)
        if add_trailing_space and not new_token.endswith(" "):
            new_token += " "
        text = text.replace(old_token, new_token)
    
    return text

def remove_empty_braces(text):
    """
    删除空花括号 {} (从原脚本复制)
    这个函数会删除所有的空花括号，但保留有意义的空格
    修复：在align环境中更加谨慎，避免删除必要的花括号结构
    """
    # 检查是否包含align环境，如果是，使用更保守的处理
    if '\\begin{align' in text:
        # 在align环境中，我们暂时跳过空花括号的删除
        # 让fix_align_environment_braces函数来处理align环境中的花括号问题
        return text
    else:
        # 对于非align环境，使用原来的处理方式
        result = re.sub(r'\{\s*\}', '', text)

        # 多次执行以处理嵌套的空花括号
        prev_result = ""
        while prev_result != result:
            prev_result = result
            result = re.sub(r'\{\s*\}', '', result)

        return result

def remove_redundant_braces(text):
    """
    删除多余的嵌套花括号
    例如: \\sqrt{{3}} → \\sqrt{3}, \\mathrm{{sin}} → \\mathrm{sin}
    """
    result = text

    # 处理LaTeX命令后的双重花括号
    # 匹配模式：\command{{content}} → \command{content}
    latex_commands = [
        'sqrt', 'frac', 'mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt',
        'text', 'textbf', 'textit', 'textrm', 'textsf', 'texttt', 'textsc',
        'hat', 'tilde', 'bar', 'vec', 'dot', 'ddot', 'overline', 'underline',
        'sin', 'cos', 'tan', 'sec', 'csc', 'cot', 'sinh', 'cosh', 'tanh',
        'arcsin', 'arccos', 'arctan', 'ln', 'log', 'exp', 'min', 'max'
    ]

    # 使用迭代方法处理多层嵌套
    prev_result = ""
    iterations = 0
    while prev_result != result and iterations < 10:  # 增加迭代次数
        prev_result = result

        # 处理LaTeX命令的双重花括号
        for cmd in latex_commands:
            # 处理 \command{{content}} → \command{content}
            # 使用更宽松的匹配，支持嵌套内容
            pattern = f'\\\\{cmd}\\{{\\{{([^{{}}]*(?:\\{{[^{{}}]*\\}}[^{{}}]*)*)\\}}\\}}'
            replacement = f'\\\\{cmd}{{{r"\1"}}}'
            result = re.sub(pattern, replacement, result)

            # 处理简单情况
            pattern = f'\\\\{cmd}\\{{\\{{([^{{}}]+)\\}}\\}}'
            replacement = f'\\\\{cmd}{{{r"\1"}}}'
            result = re.sub(pattern, replacement, result)

        # 处理一般的双重花括号
        # 匹配 {{content}} 但不破坏必要的嵌套
        result = re.sub(r'\{\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}\}', r'{\1}', result)

        # 处理简单的双重花括号
        result = re.sub(r'\{\{([^{}]+)\}\}', r'{\1}', result)

        iterations += 1

    return result

def fix_align_environment_braces(text):
    """
    修复align环境中的花括号问题 - 简化版本
    
    由于JavaScript已经优化了align环境的处理，此函数主要用于：
    1. 清理剩余的空花括号
    2. 修复括号平衡问题
    3. 处理特殊情况
    """
    # 检查是否包含align环境
    if '\\begin{align' not in text:
        return text

    result = text

    # 第一步：清理剩余的空花括号
    # 删除 \\begin{align*} 后的空花括号
    result = re.sub(r'(\\begin{align\*?})\s*\{\s*\}', r'\1', result)
    
    # 删除 \\end{align*} 前的空花括号
    result = re.sub(r'\{\s*\}\s*(\\end{align\*?})', r'\1', result)
    
    # 删除换行符周围的空花括号
    result = re.sub(r'\}\s*\\\\\s*\{\s*\}', r'\\\\', result)
    result = re.sub(r'\\\\\s*\{\s*\}', r'\\\\', result)
    result = re.sub(r'\{\s*\}\s*\\\\', r'\\\\', result)
    
    # 删除对齐符号 & 周围的空花括号
    result = re.sub(r'&\s*\{\s*\}', r'&', result)
    result = re.sub(r'\{\s*\}\s*&', r'&', result)
    
    # 第二步：修复空上标问题
    # 只处理真正的空上标，不处理有内容的上标
    result = re.sub(r'\^\s*\{\s*\}\s*', r'^{}', result)  # 处理 ^ {} 的情况
    
    # 第三步：修复换行符和对齐符号的连接问题
    result = re.sub(r'\\\\\s*&=', r'\\\\ &=', result)
    
    # 第四步：清理多余的空格
    result = re.sub(r'\s+', ' ', result)
    result = re.sub(r'^\s+|\s+$', '', result)
    
    # 第五步：括号平衡检查
    open_braces = result.count('{')
    close_braces = result.count('}')
    
    if open_braces > close_braces:
        missing_braces = open_braces - close_braces
        align_end_pos = result.rfind('\\end{align')
        if align_end_pos != -1:
            result = result[:align_end_pos] + '}' * missing_braces + result[align_end_pos:]
        else:
            result = result + '}' * missing_braces
    elif close_braces > open_braces:
        missing_braces = close_braces - open_braces
        align_start_pos = result.find('\\begin{align')
        if align_start_pos != -1:
            result = result[:align_start_pos] + '{' * missing_braces + result[align_start_pos:]
        else:
            result = '{' * missing_braces + result

    return result

def remove_trailing_latex(formula):
    """移除LaTeX公式尾部的间距和装饰命令 (从原脚本复制)"""
    pattern = r'(\\(hspace\*?\{[^{}]*?\}|vspace\*?\{[^{}]*?\}|smallskip|medskip|quad|qquad|bigskip|[;,])|\~|\.)*$'
    cleaned_formula = re.sub(pattern, '', formula, count=1)
    return cleaned_formula

# ==================== 核心函数 ====================

def latex_aware_tokenizer(text):
    """
    LaTeX语法感知的tokenizer - 全面解决token分割问题

    使用状态机方法，正确理解LaTeX结构：
    - 命令识别 (\\command)
    - 参数组识别 ({...}, [...])
    - 强制空格命令 (\\ )
    - 数学函数处理
    - 嵌套结构处理
    """
    tokens = []
    i = 0

    while i < len(text):
        # 跳过普通空格
        if text[i] == ' ' and (i == 0 or text[i-1] != '\\'):
            i += 1
            continue

        # 处理LaTeX命令
        if text[i] == '\\':
            token, next_i = _parse_latex_command(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理花括号组
        if text[i] == '{':
            token, next_i = _parse_brace_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理方括号组
        if text[i] == '[':
            token, next_i = _parse_bracket_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        # 处理单独的特殊字符
        if text[i] in '()[]':
            tokens.append(text[i])
            i += 1
            continue

        # 处理普通字符序列
        token, next_i = _parse_normal_sequence(text, i)
        if token:
            tokens.append(token)
        i = next_i

    return [t for t in tokens if t.strip()]

def _parse_latex_command(text, start):
    """解析LaTeX命令"""
    if start >= len(text) or text[start] != '\\':
        return None, start + 1

    i = start + 1

    # 处理转义字符 \{, \}, \[, \], \$, \%, \&, \#, \_
    if i < len(text) and text[i] in '{}[]$%&#_':
        return f'\\{text[i]}', i + 1

    # 处理LaTeX换行符 \\
    if i < len(text) and text[i] == '\\':
        return '\\\\', i + 1

    # 处理LaTeX特殊命令：间距命令、重音符、数学模式、特殊符号
    # 间距命令: \, \: \; \!
    # 双竖线: \|
    # 重音符: \= \^ \. \~ \" \' \`
    # 数学模式: \( \)
    # 特殊符号: \/ \-
    # 添加 \> 到LaTeX特殊命令列表
    if i < len(text) and text[i] in ',:;!|=^.~"\'`()/-<>':
        return f'\\{text[i]}', i + 1

    # 处理强制空格命令 \ (反斜杠+空格)
    if i < len(text) and text[i] == ' ':
        # 检查后面是否跟着运算符或标点符号（扩展列表包含更多符号）
        # 注意：移除了所有LaTeX特殊命令字符，避免错误解析
        # 移除的字符: , ; : ! | = ^ . ~ " ' ` ( ) / -
        if i + 1 < len(text) and text[i + 1] in '+\\<>*_[]{}':
            return f'\\ {text[i + 1]}', i + 2
        else:
            return '\\ ', i + 1

    # 处理普通命令
    command = '\\'
    while i < len(text) and (text[i].isalpha() or text[i] in '*'):
        command += text[i]
        i += 1

    # 特殊处理环境命令 \begin 和 \end
    if command in ['\\begin', '\\end']:
        # 检查后面是否直接跟着花括号
        if i < len(text) and text[i] == '{':
            # 解析整个环境命令，包括花括号
            brace_count = 1
            j = i + 1
            while j < len(text) and brace_count > 0:
                if text[j] == '{':
                    brace_count += 1
                elif text[j] == '}':
                    brace_count -= 1
                j += 1

            if brace_count == 0:
                # 返回完整的环境命令，如 \begin{aligned}
                return text[start:j], j

    # 特殊处理数学函数命令
    math_commands = ['\\mathrm', '\\mathbf', '\\mathit', '\\mathcal', '\\mathbb', '\\mathfrak', '\\mathsf', '\\mathtt']
    if command in math_commands:
        # 检查后面是否直接跟着字符（不是花括号）
        if i < len(text) and text[i] not in '{[ ':
            # 只取第一个字符作为参数
            param_char = text[i]
            return command, i  # 返回命令，参数字符将在下次迭代中单独处理

    return command, i

def _parse_brace_group(text, start):
    """解析花括号组"""
    if start >= len(text) or text[start] != '{':
        return None, start + 1

    brace_count = 1
    i = start + 1

    while i < len(text) and brace_count > 0:
        if text[i] == '{':
            brace_count += 1
        elif text[i] == '}':
            brace_count -= 1
        i += 1

    if brace_count == 0:
        return text[start:i], i
    else:
        # 不匹配的花括号，返回到下一个空格或结尾
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_bracket_group(text, start):
    """解析方括号组"""
    if start >= len(text) or text[start] != '[':
        return None, start + 1

    bracket_count = 1
    i = start + 1

    while i < len(text) and bracket_count > 0:
        if text[i] == '[':
            bracket_count += 1
        elif text[i] == ']':
            bracket_count -= 1
        i += 1

    if bracket_count == 0:
        return text[start:i], i
    else:
        # 不匹配的方括号，返回到下一个空格或结尾
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_normal_sequence(text, start):
    """解析普通字符序列"""
    i = start

    # 特殊处理：如果是多位数字，需要检查是否应该分割为单个数字
    if text[i].isdigit():
        # 检查前面是否是需要单个数字参数的命令（如\frac）
        # 简化处理：对于多位数字，只取第一位
        if len(text) > start + 1 and text[start + 1].isdigit():
            # 多位数字，只取第一位
            i = start + 1
        else:
            # 单位数字，正常处理
            while i < len(text) and text[i].isdigit():
                i += 1
    else:
        while i < len(text) and text[i] not in '\\{}[] ' and not text[i].isdigit():
            i += 1

    if i > start:
        return text[start:i], i
    else:
        return None, start + 1

def tokenize_latex(latex_code):
    """
    LaTeX tokenization函数 - JavaScript AST处理 (简化版本，删除text特殊保护)

    Args:
        latex_code: 原始LaTeX字符串

    Returns:
        (success: bool, processed_latex: str)
    """
    if not latex_code:
        return False, latex_code

    # 删除了text命令的特殊保护逻辑，让JavaScript正常处理所有命令
    protected_latex = latex_code

    # 创建临时文件，指定UTF-8编码
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
        temp_filename = temp_file.name

        # 环境统一预处理 - 修复：KaTeX兼容性处理
        prepre = protected_latex

        # KaTeX兼容性：将不支持的环境转换为支持的环境
        # 将align*等环境转换为aligned（KaTeX支持的环境）
        # 修复：使用更简单的正则表达式
        prepre = re.sub(r'\\begin{align\*?}', r'\\begin{aligned}', prepre)
        prepre = re.sub(r'\\end{align\*?}', r'\\end{aligned}', prepre)
        prepre = re.sub(r'\\begin{(split|alignedat|alignat|eqnarray)\*?}', r'\\begin{aligned}', prepre)
        prepre = re.sub(r'\\end{(split|alignedat|alignat|eqnarray)\*?}', r'\\end{aligned}', prepre)

        # 根据配置决定是否进行额外的环境统一
        if CONFIG.get('unify_environments', False):
            # 将smallmatrix统一为matrix
            prepre = re.sub(r'\\begin{(smallmatrix)\*?}(.+?)\\end{\1\*?}',
                           r'\\begin{matrix}\2\\end{matrix}', prepre, flags=re.S)

        temp_file.write(prepre)
    
    try:
        # 调用JavaScript处理器
        js_script = JS_DIR / "preprocess_formula.js"
        if not js_script.exists():
            return True, prepre  # 返回预处理后的结果

        # 构建命令
        if os.name == 'nt':  # Windows
            cmd = f'type "{temp_filename}" | node "{js_script}" normalize'
        else:  # Unix/Linux/Mac
            cmd = f'cat "{temp_filename}" | node "{js_script}" normalize'

        # 执行命令，指定UTF-8编码
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              timeout=30, encoding='utf-8', errors='replace')

        if result.returncode != 0:
            return True, prepre  # 返回预处理后的结果

        # 处理输出
        output = result.stdout
        if output is None:
            return True, prepre  # 返回预处理后的结果

        output = output.strip()
        if not output:
            return True, prepre  # 返回预处理后的结果

        # 操作符名称规范化
        operators = '|'.join(['arccos', 'arcsin', 'arctan', 'arg', 'cos', 'cosh', 'cot', 'coth', 'csc', 'deg', 'det', 'dim', 'exp', 'gcd', 'hom', 'inf',
                             'injlim', 'ker', 'lg', 'lim', 'liminf', 'limsup', 'ln', 'log', 'max', 'min', 'Pr', 'projlim', 'sec', 'sin', 'sinh', 'sup', 'tan', 'tanh'])
        ops = re.compile(r'\\operatorname {(' + operators + ')}')

        # 替换operatorname为简化形式
        names = ['\\' + x.replace(' ', '') for x in re.findall(ops, output)]
        if names:
            output = re.sub(ops, lambda match: names.pop(0), output)
        output = output.replace(r'\\ \end{array}', r'\end{array}')

        # 删除了text命令的特殊保护恢复逻辑

        # 环境统一后处理：保持aligned环境，不再恢复原始环境类型
        # 注释掉环境恢复逻辑，保持统一后的aligned环境
        # if 'align*' in latex_code:
        #     output = re.sub(r'\\begin{aligned}(.+?)\\end{aligned}', r'\\begin{align*}\1\\end{align*}', output, flags=re.S)
        # elif 'align}' in latex_code:
        #     output = re.sub(r'\\begin{aligned}(.+?)\\end{aligned}', r'\\begin{align}\1\\end{align}', output, flags=re.S)

        # 应用统一的数学函数处理
        output = unified_math_function_processing(output)

        # 后处理修复
        output = post_process_fixes(output)

        return True, output

    except subprocess.TimeoutExpired:
        # 删除了text命令的特殊保护恢复逻辑
        return True, prepre  # 返回预处理后的结果
    except Exception as e:
        # 删除了text命令的特殊保护恢复逻辑
        return True, prepre  # 返回预处理后的结果
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_filename)
        except:
            pass

def post_process_fixes(text):
    """
    后处理修复函数 - 修复各种已知问题

    修复内容：
    1. aligned环境中的多余花括号
    2. 错误的符号组合（如反斜杠和运算符的错误组合）
    3. 其他已知的格式问题

    注意：重音符号与上下标的冲突问题已在JavaScript AST层面解决
    """
    result = text

    # 修复1: 清理aligned环境中的多余花括号
    # \begin{aligned} {content} \end{aligned} → \begin{aligned} content \end{aligned}
    result = re.sub(r'\\begin{aligned}\s*\{\s*(.+?)\s*\}\s*\\end{aligned}',
                    r'\\begin{aligned} \1 \\end{aligned}', result, flags=re.DOTALL)

    # 修复2: 处理 \=\ 等错误的符号组合
    # 这些可能是由于LaTeX换行符和等号处理冲突造成的
    error_patterns = {
        r'\\=\\\\': '=',      # \=\ → =
        r'\\\+\\\\': '+',     # \+\ → +
        r'\\\-\\\\': '-',     # \-\ → -
        r'\\\*\\\\': '*',     # \*\ → *
        r'\\\/\\\\': '/',     # \/\ → /
        r'\\<\\\\': '<',      # \<\ → <
        r'\\>\\\\': '>',      # \>\ → >
    }

    for pattern, replacement in error_patterns.items():
        result = re.sub(pattern, replacement, result)

    # 修复3: 修复 \left 和 \right 命令的分隔符问题
    # 这些在JavaScript处理阶段可能被错误处理
    left_right_fixes = {
        r'\\left\s*\{': r'\\left\\{',     # \left { → \left\{
        r'\\right\s*\}': r'\\right\\}',   # \right } → \right\}
        r'\\left\s*\[': r'\\left\\[',     # \left [ → \left\[
        r'\\right\s*\]': r'\\right\\]',   # \right ] → \right\]
        r'\\left\s*\(': r'\\left\\(',     # \left ( → \left\(
        r'\\right\s*\)': r'\\right\\)',   # \right ) → \right\)
        r'\\left\s*\|': r'\\left\\|',     # \left | → \left\|
        r'\\right\s*\|': r'\\right\\|',   # \right | → \right\|
    }

    for pattern, replacement in left_right_fixes.items():
        result = re.sub(pattern, replacement, result)

    # 修复4: 清理其他可能的错误组合
    # 修复连续的反斜杠问题（但保护真正的LaTeX换行符\\）
    result = re.sub(r'(?<!\\)\\(?!\\)(?![a-zA-Z])', '', result)  # 移除孤立的反斜杠

    # 修复5: 清理多余的空格
    result = re.sub(r'\s{2,}', ' ', result)  # 多个空格 → 单个空格
    result = re.sub(r'^\s+|\s+$', '', result)  # 移除首尾空格

    return result

def unified_space_processing(text):
    """
    重新设计的空格处理函数 - 简化逻辑，避免冲突

    设计原则：
    1. 先保护重要的LaTeX结构
    2. 清理不必要的空格
    3. 添加必要的空格
    4. 恢复被保护的结构
    """
    result = text

    # === 第一阶段：保护重要的LaTeX结构 ===

    # 保护LaTeX换行符
    result = result.replace('\\\\', '__LATEX_NEWLINE__')

    # 保护LaTeX间距命令（这些命令本身就是用来控制空格的）
    spacing_commands = [
        r'\\quad', r'\\qquad', r'\\enspace', r'\\thinspace',
        r'\\medspace', r'\\thickspace', r'\\negthinspace',
        r'\\negmedspace', r'\\negthickspace',
        r'\\,', r'\\:', r'\\;', r'\\!', r'\\ ', r'\\>'
    ]

    protected_spacing = []
    def protect_spacing(match):
        protected_spacing.append(match.group(0))
        return f'__SPACING_{len(protected_spacing)-1}__'

    # 按长度降序排序，确保长命令优先匹配
    for cmd in sorted(spacing_commands, key=len, reverse=True):
        result = re.sub(re.escape(cmd), protect_spacing, result)

    # 保护 \left 和 \right 命令的分隔符
    left_right_commands = []
    def protect_left_right(match):
        left_right_commands.append(match.group(0))
        return f'__LEFT_RIGHT_{len(left_right_commands)-1}__'

    # 保护 \left\{ \right\} \left\[ \right\] \left\( \right\) 等
    left_right_patterns = [
        r'\\left\\\{',   # \left\{
        r'\\right\\\}',  # \right\}
        r'\\left\\\[',   # \left\[
        r'\\right\\\]',  # \right\]
        r'\\left\\\(',   # \left\(
        r'\\right\\\)',  # \right\)
        r'\\left\\\|',   # \left\|
        r'\\right\\\|',  # \right\|
        r'\\left\.',     # \left.
        r'\\right\.'     # \right.
    ]

    for pattern in left_right_patterns:
        result = re.sub(pattern, protect_left_right, result)

    # === 第二阶段：清理不必要的空格 ===

    # 1. 清理结构性空格
    result = re.sub(r'\s*\{\s*', '{', result)
    result = re.sub(r'\s*\}', '}', result)
    result = re.sub(r'\s*\[\s*', '[', result)
    result = re.sub(r'\s*\]', ']', result)
    result = re.sub(r'\s*\(\s*', '(', result)
    result = re.sub(r'\s*\)', ')', result)

    # 2. 清理运算符周围的空格
    operators = ['+', '-', '=', '<', '>', '*', '/', '^', '_', ',', ';', ':']
    for op in operators:
        result = re.sub(f'\\s*{re.escape(op)}\\s*', op, result)

    # 3. 清理LaTeX命令和参数之间的空格
    result = re.sub(r'(\\[A-Za-z]+)\s*\{', r'\1{', result)
    result = re.sub(r'(\\[A-Za-z]+)\s*\[', r'\1[', result)
    result = re.sub(r'(\\[A-Za-z]+)\s*\(', r'\1(', result)

    # 4. 清理数学字体命令内部的空格
    math_fonts = ['mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt']
    for font in math_fonts:
        pattern = f'\\\\{font}\\s*\\{{\\s*([^}}]+)\\s*\\}}'
        def clean_font_spaces(match):
            content = re.sub(r'\s+', '', match.group(1))
            return f'\\{font}{{{content}}}'
        result = re.sub(pattern, clean_font_spaces, result)

    # === 第三阶段：添加必要的空格 ===

    # 1. 为LaTeX命令后面跟字母的情况添加空格
    # 需要后面跟空格的命令
    commands_needing_space = [
        # 样式命令（按长度降序排列，避免短命令误匹配长命令）
        '\\scriptscriptstyle', '\\scriptstyle', '\\displaystyle', '\\textstyle',
        # 大小命令
        '\\footnotesize', '\\scriptsize', '\\normalsize', '\\tiny', '\\small',
        '\\Large', '\\LARGE', '\\large', '\\huge', '\\Huge',
        # 字体命令（注意：\\sc 可能与 \\scriptscriptstyle 冲突，需要特殊处理）
        '\\bf', '\\it', '\\rm', '\\sf', '\\tt', '\\em', '\\sl',
        # 其他命令
        '\\LaTeX', '\\TeX', '\\BibTeX'
    ]

    # 特殊处理：先保护长命令，避免被短命令误匹配
    protected_long_commands = {}
    long_commands = ['\\scriptscriptstyle', '\\scriptstyle', '\\displaystyle', '\\textstyle',
                    '\\footnotesize', '\\scriptsize', '\\normalsize']

    for i, cmd in enumerate(long_commands):
        if cmd in result:
            placeholder = f'__LONG_CMD_{i}__'
            protected_long_commands[placeholder] = cmd
            result = result.replace(cmd, placeholder)

    # 现在安全地处理短命令（包括 \\sc）
    short_commands = ['\\bf', '\\it', '\\rm', '\\sf', '\\tt', '\\sc', '\\em', '\\sl',
                     '\\tiny', '\\small', '\\large', '\\Large', '\\LARGE', '\\huge', '\\Huge',
                     '\\LaTeX', '\\TeX', '\\BibTeX']

    for cmd in sorted(short_commands, key=len, reverse=True):
        # 只在命令后面直接跟字母时添加空格，且确保不是单词的一部分
        pattern = f'(?<!\\\\[a-zA-Z])({re.escape(cmd)})(?![a-zA-Z])([a-zA-Z])'
        result = re.sub(pattern, r'\1 \2', result)

    # 恢复被保护的长命令
    for placeholder, original_cmd in protected_long_commands.items():
        result = result.replace(placeholder, original_cmd)

    # 现在处理长命令的空格添加
    for cmd in long_commands:
        pattern = f'({re.escape(cmd)})([a-zA-Z])'
        result = re.sub(pattern, r'\1 \2', result)

    # 2. 为希腊字母和其他数学符号后面跟字母的情况添加空格
    greek_letters = [
        '\\alpha', '\\beta', '\\gamma', '\\delta', '\\epsilon', '\\zeta',
        '\\eta', '\\theta', '\\iota', '\\kappa', '\\lambda', '\\mu',
        '\\nu', '\\xi', '\\pi', '\\rho', '\\sigma', '\\tau', '\\upsilon',
        '\\phi', '\\chi', '\\psi', '\\omega',
        '\\Gamma', '\\Delta', '\\Theta', '\\Lambda', '\\Xi', '\\Pi',
        '\\Sigma', '\\Upsilon', '\\Phi', '\\Psi', '\\Omega',
        '\\varepsilon', '\\vartheta', '\\varpi', '\\varrho', '\\varsigma',
        '\\varphi', '\\varkappa'
    ]

    math_symbols = [
        '\\partial', '\\nabla', '\\infty', '\\emptyset', '\\varnothing',
        '\\prime', '\\backprime', '\\hbar', '\\ell', '\\wp', '\\Re', '\\Im'
    ]

    all_symbols = greek_letters + math_symbols

    # 按长度降序排序
    for symbol in sorted(all_symbols, key=len, reverse=True):
        # 只在符号后面直接跟字母时添加空格
        pattern = f'({re.escape(symbol)})([a-zA-Z])'
        result = re.sub(pattern, r'\1 \2', result)

    # === 第四阶段：清理和恢复 ===

    # 清理多余的连续空格
    result = re.sub(r'\s{2,}', ' ', result)
    result = re.sub(r'^\s+|\s+$', '', result)

    # 恢复被保护的间距命令
    for i, spacing in enumerate(protected_spacing):
        result = result.replace(f'__SPACING_{i}__', spacing)

    # 恢复被保护的left/right命令
    for i, left_right in enumerate(left_right_commands):
        result = result.replace(f'__LEFT_RIGHT_{i}__', left_right)

    # 恢复LaTeX换行符
    result = result.replace('__LATEX_NEWLINE__', '\\\\')

    return result

def unified_math_function_processing(text):
    """
    统一的数学函数处理 - 将所有数学函数统一为标准的\\sin格式
    处理operatorname、mathrm等格式，统一转换为\\sin、\\cos等标准命令
    """

    result = text

    # 第一阶段：处理JavaScript输出的嵌套花括号问题
    # 修复 \operatorname { {sin} } → \operatorname{sin}
    result = re.sub(r'\\operatorname\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\operatorname{\1}', result)
    # 修复 \mathrm { {sin} } → \mathrm{sin}
    result = re.sub(r'\\mathrm\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\mathrm{\1}', result)
    # 修复 \mathbb { {T} } → \mathbb{T}
    result = re.sub(r'\\mathbb\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\mathbb{\1}', result)
    # 修复 \mathcal { {A} } → \mathcal{A}
    result = re.sub(r'\\mathcal\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\mathcal{\1}', result)
    # 修复 \mathfrak { {B} } → \mathfrak{B}
    result = re.sub(r'\\mathfrak\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\mathfrak{\1}', result)
    # 修复 \mathscr { {C} } → \mathscr{C}
    result = re.sub(r'\\mathscr\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\mathscr{\1}', result)

    # 通用的嵌套花括号修复：\command { {content} } → \command{content}
    result = re.sub(r'\\([a-zA-Z]+)\s*\{\s*\{\s*([^}]+)\s*\}\s*\}', r'\\\1{\2}', result)

    # 第二阶段：使用具体的替换规则，避免复杂的正则表达式

    # 处理 \operatorname 格式的函数
    operatorname_replacements = {
        # 处理空格分隔的格式
        r'\\operatorname\s*\{\s*s\s+i\s+n\s*\}': r'\\sin',
        r'\\operatorname\s*\{\s*c\s+o\s+s\s*\}': r'\\cos',
        r'\\operatorname\s*\{\s*t\s+a\s+n\s*\}': r'\\tan',
        r'\\operatorname\s*\{\s*c\s+o\s+t\s*\}': r'\\cot',
        r'\\operatorname\s*\{\s*s\s+e\s+c\s*\}': r'\\sec',
        r'\\operatorname\s*\{\s*c\s+s\s+c\s*\}': r'\\csc',
        r'\\operatorname\s*\{\s*s\s+i\s+n\s+h\s*\}': r'\\sinh',
        r'\\operatorname\s*\{\s*c\s+o\s+s\s+h\s*\}': r'\\cosh',
        r'\\operatorname\s*\{\s*t\s+a\s+n\s+h\s*\}': r'\\tanh',
        r'\\operatorname\s*\{\s*c\s+o\s+t\s+h\s*\}': r'\\coth',
        r'\\operatorname\s*\{\s*a\s+r\s+c\s+s\s+i\s+n\s*\}': r'\\arcsin',
        r'\\operatorname\s*\{\s*a\s+r\s+c\s+c\s+o\s+s\s*\}': r'\\arccos',
        r'\\operatorname\s*\{\s*a\s+r\s+c\s+t\s+a\s+n\s*\}': r'\\arctan',
        r'\\operatorname\s*\{\s*e\s+x\s+p\s*\}': r'\\exp',
        r'\\operatorname\s*\{\s*l\s+n\s*\}': r'\\ln',
        r'\\operatorname\s*\{\s*l\s+o\s+g\s*\}': r'\\log',
        r'\\operatorname\s*\{\s*l\s+g\s*\}': r'\\lg',
        r'\\operatorname\s*\{\s*m\s+i\s+n\s*\}': r'\\min',
        r'\\operatorname\s*\{\s*m\s+a\s+x\s*\}': r'\\max',
        r'\\operatorname\s*\{\s*m\s+o\s+d\s*\}': r'\\bmod',
        # 处理普通格式
        r'\\operatorname\s*\{\s*sin\s*\}': r'\\sin',
        r'\\operatorname\s*\{\s*cos\s*\}': r'\\cos',
        r'\\operatorname\s*\{\s*tan\s*\}': r'\\tan',
        r'\\operatorname\s*\{\s*cot\s*\}': r'\\cot',
        r'\\operatorname\s*\{\s*sec\s*\}': r'\\sec',
        r'\\operatorname\s*\{\s*csc\s*\}': r'\\csc',
        r'\\operatorname\s*\{\s*sinh\s*\}': r'\\sinh',
        r'\\operatorname\s*\{\s*cosh\s*\}': r'\\cosh',
        r'\\operatorname\s*\{\s*tanh\s*\}': r'\\tanh',
        r'\\operatorname\s*\{\s*coth\s*\}': r'\\coth',
        r'\\operatorname\s*\{\s*arcsin\s*\}': r'\\arcsin',
        r'\\operatorname\s*\{\s*arccos\s*\}': r'\\arccos',
        r'\\operatorname\s*\{\s*arctan\s*\}': r'\\arctan',
        r'\\operatorname\s*\{\s*exp\s*\}': r'\\exp',
        r'\\operatorname\s*\{\s*ln\s*\}': r'\\ln',
        r'\\operatorname\s*\{\s*log\s*\}': r'\\log',
        r'\\operatorname\s*\{\s*lg\s*\}': r'\\lg',
        r'\\operatorname\s*\{\s*min\s*\}': r'\\min',
        r'\\operatorname\s*\{\s*max\s*\}': r'\\max',
        r'\\operatorname\s*\{\s*mod\s*\}': r'\\bmod',
    }

    for pattern, replacement in operatorname_replacements.items():
        result = re.sub(pattern, replacement, result)

    # 处理 \mathrm 格式的函数
    mathrm_replacements = {
        r'\\mathrm\s*\{\s*sin\s*\}': r'\\sin',
        r'\\mathrm\s*\{\s*cos\s*\}': r'\\cos',
        r'\\mathrm\s*\{\s*tan\s*\}': r'\\tan',
        r'\\mathrm\s*\{\s*cot\s*\}': r'\\cot',
        r'\\mathrm\s*\{\s*sec\s*\}': r'\\sec',
        r'\\mathrm\s*\{\s*csc\s*\}': r'\\csc',
        r'\\mathrm\s*\{\s*sinh\s*\}': r'\\sinh',
        r'\\mathrm\s*\{\s*cosh\s*\}': r'\\cosh',
        r'\\mathrm\s*\{\s*tanh\s*\}': r'\\tanh',
        r'\\mathrm\s*\{\s*coth\s*\}': r'\\coth',
        r'\\mathrm\s*\{\s*arcsin\s*\}': r'\\arcsin',
        r'\\mathrm\s*\{\s*arccos\s*\}': r'\\arccos',
        r'\\mathrm\s*\{\s*arctan\s*\}': r'\\arctan',
        r'\\mathrm\s*\{\s*exp\s*\}': r'\\exp',
        r'\\mathrm\s*\{\s*ln\s*\}': r'\\ln',
        r'\\mathrm\s*\{\s*log\s*\}': r'\\log',
        r'\\mathrm\s*\{\s*lg\s*\}': r'\\lg',
        r'\\mathrm\s*\{\s*min\s*\}': r'\\min',
        r'\\mathrm\s*\{\s*max\s*\}': r'\\max',
        r'\\mathrm\s*\{\s*mod\s*\}': r'\\bmod',
    }

    for pattern, replacement in mathrm_replacements.items():
        result = re.sub(pattern, replacement, result)

    return result

def normalize_latex(l, rm_trail=False):
    """
    LaTeX规范化主函数 - 重新设计流程版本
    新的处理流程：空格处理 → Token合并 → 其他处理
    """
    # 步骤0: 删除了text命令的特殊保护逻辑

    # 步骤1: 尾部清理 (可选)
    if rm_trail:
        l = remove_trailing_latex(l)

    # 步骤2: 基础矩阵命令替换
    l = l.strip().replace(r'\pmatrix', r'\mypmatrix').replace(r'\matrix', r'\mymatrix')

    # 步骤3: 移除对齐相关命令
    for item in ['\\raggedright', '\\arraybackslash']:
        l = l.replace(item, "")

    # 步骤4: 移除大小写转换命令
    for item in ['\\lowercase', '\\uppercase']:
        l = l.replace(item, "")

    # 步骤5: 统一空格处理 (提前到Token合并之前)
    l = unified_space_processing(l)
    


    # 步骤6: 使用通用函数处理Token合并 (在空格处理之后)

    l = merge_tokens_with_pattern(l, r'\\[hv]space { [.0-9a-z ]+ }')

    # 处理 \begin{array} 格式
    def process_array_format(token):
        return token.replace("\\begin{array} ", "<s>").replace(" ", "").replace("<s>", "\\begin{array} ")
    l = merge_tokens_with_pattern(l, r'\\begin{array} { [lrc ]+ }', process_array_format)

    # Token合并处理
    # l = merge_tokens_with_pattern(l, r'\\string [^ ]+ ', add_trailing_space=True)
    l = merge_tokens_with_pattern(l, r'\\[Bb]ig[g]?[glrm]? [(){}|\[\]] ', add_trailing_space=True)
    
    # 处理Big命令与后面LaTeX命令的合并（现在在空格处理之后，不会被覆盖）
    def process_big_command(token):
        # 移除Big命令和后面LaTeX命令之间的空格
        return token.replace(" ", "")
    l = merge_tokens_with_pattern(l, r'\\[Bb]ig[g]?[glrm]?\s*\\[a-zA-Z]+', process_big_command, add_trailing_space=True)
    
    # def process_operatorname_star(token):
    #     return "\\operatorname"
    # l = merge_tokens_with_pattern(l, r'\\operatorname \*', process_operatorname_star)

    # 移除有害命令
    l = l.replace("\\lefteqn", "")
    l = l.replace("\\footnote ", "^ ")

    # 重音符号合并
    l = merge_tokens_with_pattern(l, r'\\\' [^{] ', add_trailing_space=True)

    # 其他命令合并
    l = merge_tokens_with_pattern(l, r'\\parbox {[^{]+}')
    
    # def process_raisebox(token):
    #     processed = token.replace(" ", "")
    #     return processed[0:-1] + " {"
    # l = merge_tokens_with_pattern(l, r'\\raisebox {[^{]+} [\[\]0-9 exptcm]+{', process_raisebox)
    #
    # def process_char_command(token):
    #     processed = token.replace(" ", "")
    #     return "{ " + processed[1:-1] + " }"
    # l = merge_tokens_with_pattern(l, r'{ \\char[0-9\' ]+}', process_char_command)
    
    # l = merge_tokens_with_pattern(l, r'\\rule {[ .0-9a-z]+} {[ .0-9a-z]+}')
    # l = merge_tokens_with_pattern(l, r'\\specialrule {[ .0-9a-z]+} {[ .0-9a-z]+} {[ .0-9a-z]+}')

    # 步骤7: 颜色命令移除
    pattern = r'\\colorbox[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\color[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\textcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\cellcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } '
    old_token = re.findall(pattern, l, re.DOTALL)
    for bef in old_token:
        l = l.replace(bef, "")

    # 步骤8: 括号补全处理
    l_split = latex_aware_tokenizer(l)
    idx = 0

    while idx < len(l_split):
        token = l_split[idx]
        if token in ONE_Tail_Tokens + ONE_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split):
                next_token = l_split[idx + 1]
                if next_token != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
        elif token in TWO_Tail_Tokens:
            if idx + 1 < len(l_split):
                current_pos = idx + 1
                if l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}"):
                    current_pos += 1
                elif l_split[current_pos] != "{":
                    l_split.insert(current_pos, "{")
                    l_split.insert(current_pos + 2, "}")
                    current_pos += 3
                else:
                    brace_count = 1
                    current_pos += 1
                    while current_pos < len(l_split) and brace_count > 0:
                        if l_split[current_pos] == "{":
                            brace_count += 1
                        elif l_split[current_pos] == "}":
                            brace_count -= 1
                        current_pos += 1

                if current_pos < len(l_split):
                    if l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}"):
                        current_pos += 1
                    elif l_split[current_pos] != "{":
                        l_split.insert(current_pos, "{")
                        l_split.insert(current_pos + 2, "}")
                        current_pos += 3
                    else:
                        brace_count = 1
                        current_pos += 1
                        while current_pos < len(l_split) and brace_count > 0:
                            if l_split[current_pos] == "{":
                                brace_count += 1
                            elif l_split[current_pos] == "}":
                                brace_count -= 1
                            current_pos += 1

                idx = current_pos - 1
        elif token in TWO_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split):
                if l_split[idx + 1] != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
                if idx + 3 < len(l_split) and l_split[idx + 3] != "{":
                    l_split.insert(idx + 3, "{")
                    l_split.insert(idx + 5, "}")
                    idx += 2
        elif token in AB_Tail_Tokens:
            if token == "\\sqrt":
                if idx + 1 < len(l_split):
                    if l_split[idx + 1] == "[":
                        bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                        if bracket_end != -1:
                            idx = bracket_end
                    if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                        l_split.insert(idx + 1, "{")
                        l_split.insert(idx + 3, "}")
                        idx += 2
            else:
                if idx + 1 < len(l_split):
                    if l_split[idx + 1] == "[":
                        bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                        if bracket_end != -1:
                            idx = bracket_end
                    if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                        l_split.insert(idx + 1, "{")
                        l_split.insert(idx + 3, "}")
                        idx += 2
        idx += 1

    l = ' '.join(l_split)

    # 步骤9: 统一数学函数处理
    l = unified_math_function_processing(l)
    


    # 步骤10: 连字符展开和省略号统一化
    l = re.sub(r'---', r'- - -', l)
    l = re.sub(r'--', r'- -', l)

    # 省略号统一化
    # 修复：先处理所有dots变体，避免被错误拆分
    l = re.sub(r'\\dotsc', r'. . .', l)  # 先处理dotsc，避免被错误拆分
    l = re.sub(r'\\dotsi', r'. . .', l)  # 先处理dotsi，避免被错误拆分
    l = re.sub(r'\\dotsm', r'. . .', l)  # 先处理dotsm，避免被错误拆分
    l = re.sub(r'\\dotso', r'. . .', l)  # 先处理dotso，避免被错误拆分
    l = re.sub(r'\\dotsb', r'. . .', l)  # 先处理dotsb，避免被错误拆分
    
    l = re.sub(r'…', r'. . .', l)
    l = re.sub(r'\\ldots', r'. . .', l)
    l = re.sub(r'\\hdots', r'. . .', l)
    l = re.sub(r'\\cdots', r'. . .', l)
    l = re.sub(r'\\dddot', r'. . .', l)
    l = re.sub(r'\\dots', r'. . .', l)
    l = re.sub(r'\\mathellipsis', r'. . .', l)

    # 步骤11: 删除空花括号和多余的嵌套花括号
    l = remove_empty_braces(l)
    l = remove_redundant_braces(l)

    # 步骤12: 修复align环境中的花括号问题
    l = fix_align_environment_braces(l)



    return l.strip()

def tokenize_latex_string(latex_string):
    """
    LaTeX tokenize流程 (从原脚本复制)

    Args:
        latex_string: 原始LaTeX字符串

    Returns:
        tokenize后的LaTeX字符串
    """
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        # JavaScript AST处理
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            # JavaScript处理失败时，返回原始字符串
            return latex_string

        return tokenized_latex

    except Exception as e:
        print(f"tokenize处理出错: {e}")
        return latex_string

def replace_synonym_tokens(tokenized_string):
    """
    同义词token替换函数 (从原脚本复制)

    Args:
        tokenized_string: tokenize后的LaTeX字符串

    Returns:
        替换同义词后的LaTeX字符串
    """
    if not tokenized_string or not tokenized_string.strip():
        return tokenized_string

    # 检查是否启用同义词替换功能
    if not CONFIG.get('enable_synonym_replacement', True):
        return tokenized_string

    # 配置文件路径
    csv_file_path = SCRIPT_DIR / "token_unify.csv"

    # 如果配置文件不存在，跳过替换步骤
    if not csv_file_path.exists():
        return tokenized_string

    try:
        # 读取CSV配置文件
        synonym_map = {}
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            for row_num, row in enumerate(csv_reader, 1):
                # 跳过空行
                if not row or len(row) < 2:
                    continue

                # 第一列是同义词，第二列是标准token
                synonym = row[0].strip()
                standard_token = row[1].strip()

                # 跳过空的条目
                if not synonym or not standard_token:
                    continue

                synonym_map[synonym] = standard_token

        if not synonym_map:
            return tokenized_string

        # 执行token替换
        result = tokenized_string

        # 按同义词长度降序排序，避免短token被长token的一部分误匹配
        sorted_synonyms = sorted(synonym_map.keys(), key=len, reverse=True)

        replacement_count = 0
        for synonym in sorted_synonyms:
            standard_token = synonym_map[synonym]

            # 简单的字符串替换，确保匹配完整的token
            if synonym in result:
                new_result = result.replace(synonym, standard_token)

                if new_result != result:
                    replacement_count += 1
                    result = new_result



        return result

    except Exception as e:
        return tokenized_string

def normalize_latex_string(latex_string):
    """
    完整的LaTeX规范化流程 (优化版本)

    Args:
        latex_string: 原始LaTeX字符串

    Returns:
        规范化后的LaTeX字符串
    """
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        # 第一阶段: JavaScript AST处理 (保持不变)
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            # JavaScript处理失败时，直接使用Python规则处理
            tokenized_latex = latex_string

        # 第二阶段: Python规则处理 (优化版本)
        normalized_latex = normalize_latex(tokenized_latex, CONFIG.get('remove_trailing', False))

        return normalized_latex

    except Exception as e:
        print(f"规范化处理出错: {e}")
        return latex_string

# ==================== 配置验证函数 ====================

def validate_config():
    """
    验证配置参数的有效性 (从原脚本复制)
    """
    errors = []

    # 检查必需的参数
    if 'mode' not in CONFIG:
        errors.append("缺少必需参数: 'mode'")
    elif CONFIG['mode'] not in ['tokenize', 'normalize']:
        errors.append(f"无效的mode值: '{CONFIG['mode']}'，应为: 'tokenize', 'normalize'")

    # 检查LaTeX字符串参数
    if 'latex_string' not in CONFIG or not CONFIG['latex_string']:
        errors.append("需要设置 'latex_string' 参数")

    # 输出验证结果
    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False

    return True



def process_latex():
    """处理LaTeX字符串"""
    latex_string = CONFIG['latex_string']
    mode = CONFIG['mode']

    if mode == 'tokenize':
        # tokenize模式: tokenize → 同义词替换 → 输出
        tokenized_result = tokenize_latex_string(latex_string)
        result = replace_synonym_tokens(tokenized_result)
    elif mode == 'normalize':
        # normalize模式: tokenize → 同义词替换 → normalize → 输出
        # 第一阶段: JavaScript AST处理
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            tokenized_latex = latex_string

        # 第二阶段: 同义词替换
        synonym_replaced_latex = replace_synonym_tokens(tokenized_latex)

        # 第三阶段: Python规则处理
        result = normalize_latex(synonym_replaced_latex, CONFIG.get('remove_trailing', False))
    else:
        print(f"未知的模式: {mode}")
        return latex_string

    # 输出结果
    print("=" * 80)
    print("LaTeX 规范化处理结果")
    print("=" * 80)
    print(f"输入: {latex_string}")
    print(f"输出: {result}")
    print("=" * 80)

    return result

# ==================== 主函数 ====================



def main():
    """
    主函数 - 简化输出版本
    """
    # 验证配置
    if not validate_config():
        print("配置错误，请修正后重新运行")
        return

    # 处理LaTeX字符串
    process_latex()

if __name__ == "__main__":
    main()
