============================================================
LaTeX公式Token长度和复杂度分析报告
============================================================

1. 基本统计信息
------------------------------
总公式数量: 8475
有效公式数量: 8475
空公式数量: 0

2. Token长度分析
------------------------------
字符数统计:
  平均值: 147.85
  中位数: 131.00
  标准差: 74.17
  最小值: 8
  最大值: 485

空格分割Token统计:
  平均值: 55.73
  中位数: 50.00
  标准差: 27.33
  最小值: 2
  最大值: 150

3. 复杂度分析
------------------------------
复杂度分数统计:
  平均值: 50.03
  中位数: 44.50
  标准差: 24.66
  最小值: 2.50
  最大值: 175.00

4. 复杂度等级分布
------------------------------
简单: 2211 (26.1%)
中等: 2091 (24.7%)
复杂: 2099 (24.8%)
极复杂: 2074 (24.5%)

5. 最简单的公式示例
------------------------------
1. 复杂度: 2.50
   公式: \Gamma =

2. 复杂度: 4.00
   公式: n = 1 : \quad

3. 复杂度: 6.00
   公式: \dot { C }

4. 复杂度: 6.50
   公式: B _ { q } ( u ) =

5. 复杂度: 7.00
   公式: H = - B ^ { - 1 } V

6. 最复杂的公式示例
------------------------------
1. 复杂度: 157.00
   公式: \lambda ( p ^ { \mu } ) \equiv \left( \begin{matrix} { \left( \zeta _ { \lambda } \Theta _ { [ j ] }...

2. 复杂度: 163.00
   公式: \left( \begin{matrix} { \alpha } & { \beta } \\ { \gamma } & { \delta } \\ \end{matrix} \right) = \l...

3. 复杂度: 164.00
   公式: \begin{array} { c } { K = h ^ { \mu \nu } \nabla _ { \mu } u _ { \nu } = - h ^ { \mu \nu } \Gamma _ ...

4. 复杂度: 165.00
   公式: { \cal T } _ { \mu \nu } ^ { \alpha \beta } = 2 { \it g _ { 1 } } ^ { 2 } \epsilon _ { \mu } ^ { \al...

5. 复杂度: 175.00
   公式: \begin{array} { c } { C ^ { - 1 } \left( \begin{array} { c } { \gamma _ { 4 } } \\ { \gamma _ { 5 } ...

============================================================
报告生成完成
============================================================
