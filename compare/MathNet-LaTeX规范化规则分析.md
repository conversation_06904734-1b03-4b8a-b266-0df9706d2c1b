# MathNet项目LaTeX规范化规则详细分析

本文档基于对MathNet项目中三个核心文件的逐行分析，提取出所有LaTeX代码规范化规则。

## 文件分析概览

- **preprocess_formulas.py**: 预处理入口文件，调用其他处理器
- **preprocess_latex.js**: 核心LaTeX规范化引擎，基于KaTeX AST
- **improve_tokens.py**: Token级别的规范化和优化

## 1. preprocess_formulas.py 规范化规则

### 1.1 间距命令规范化 (第68行)
```
hskip<数值>(cm|in|pt|mm|em) → hspace{<数值><单位>}
```

### 1.2 字符编码清理 (第76行)
```
\r → 空格
```

## 2. preprocess_latex.js 规范化规则

### 2.1 注释处理规则 (第12-15行)

```
%<任意内容> → (删除整行)
<内容>%<注释> → <内容>
```

### 2.2 特殊字符替换规则 (第17-24行)

```
\\~ → 空格
\\> → 空格
$ → 空格
\\label{<任意内容>} → (删除)
```

### 2.3 换行符规范化 (第26-30行)
```
\\\\ → \\,  (仅在非matrix/cases/array/begin环境中)
```

### 2.4 字体命令规范化 (第40-44行)

```
{\\rm → \\mathrm{
{ \\rm → \\mathrm{
\\rm{ → \\mathrm{
```

### 2.5 特殊符号处理 (第46-49行)

```
SSSSSS → $
 S S S S S S → $
```

### 2.6 AST渲染规则

#### 2.6.1 数学序列处理 (第64-70行)
```
在mathrm字体中：
  空格 → <空格>\\;
  其他字符 → <字符><空格>
在其他字体中：
  <值> → <值><空格>
```

#### 2.6.2 文本处理 (第111-116行)
```
text环境 → \\mathrm { <内容> }
```

#### 2.6.3 分组处理 (第104-108行)
```
ordgroup → { <内容> }
```

#### 2.6.4 上下标处理 (第127-152行)
```
如果上标/下标不是ordgroup类型：
  _ → _ { <内容> }
  ^ → ^ { <内容> }
如果是ordgroup类型：
  _ → _ <内容>
  ^ → ^ <内容>
```

#### 2.6.5 分数处理 (第154-161行)
```
无分数线的分数 → \\binom <分子> <分母>
有分数线的分数 → \\frac <分子> <分母>
```

#### 2.6.6 数组/矩阵处理 (第163-190行)
```
数组开始 → \\begin{<样式>}
array/tabular样式添加列定义 → { <列定义> }
数组行 → <单元格> & <单元格> \\\\
数组结束 → \\end{<样式>}
\\hline行 → \\hline
```

#### 2.6.7 根号处理 (第192-200行)
```
带指数的根号 → \\sqrt [ <指数> ] <内容>
普通根号 → \\sqrt <内容>
```

#### 2.6.8 左右分隔符处理 (第202-208行)
```
左右分隔符 → \\left<左分隔符> <内容> \\right<右分隔符>
```

#### 2.6.9 重音符号处理 (第210-219行)
```
如果基础不是ordgroup：
  重音 → <重音符号> { <基础> }
否则：
  重音 → <重音符号> <基础>
```

#### 2.6.10 间距处理 (第221-228行)
```
空格字符 → ~
其他间距 → <间距值>
```

#### 2.6.11 算子处理 (第230-247行)
```
符号算子 → <符号体>
非限制算子 → \\operatorname { <算子名> }
限制算子 → \\operatorname* { <算子名> }
```

#### 2.6.12 字体处理 (第254-261行)
```
mbox → mathrm
hbox → mathrm
其他字体 → \\<字体名>
```

#### 2.6.13 大小调整处理 (第268-276行)
```
\\rm → \\mathrm { <内容> }
其他大小命令 → <原始命令> <内容>
```

#### 2.6.14 上划线/下划线处理 (第278-291行)
```
上划线 → \\overline { <内容> }
下划线 → \\underline { <内容> }
```

#### 2.6.15 规则线处理 (第293-296行)
```
规则线 → \\rule { <宽度数值> <宽度单位> } { <高度数值> <高度单位> }
```

#### 2.6.16 重叠处理 (第298-306行)
```
左重叠 → \\llap <内容>
右重叠 → \\rlap <内容>
```

#### 2.6.17 幻影处理 (第308-312行)
```
幻影 → \\phantom { <内容> }
```

## 3. improve_tokens.py 规范化规则

### 3.1 Token分割规则 (第285-334行)

#### 3.1.1 左分隔符分割
```
\\left( → [\\left, (]
\\left. → [\\left, .]
\\left< → [\\left, <]
\\left[ → [\\left, []
\\left\\langle → [\\left, \\langle]
\\left\\lbrace → [\\left, \\lbrace]
\\left\\lbrack → [\\left, \\lbrack]
\\left\\lfloor → [\\left, \\lfloor]
\\left\\vert → [\\left, \\vert]
\\left\\{ → [\\left, \\{]
\\left\\| → [\\left, \\|]
\\left| → [\\left, |]
\\left/ → [\\left, /]
\\left\\lceil → [\\left, \\lceil]
\\left] → [\\left, ]]
```

#### 3.1.2 右分隔符分割
```
\\right) → [\\right, )]
\\right. → [\\right, .]
\\right> → [\\right, >]
\\right\\rangle → [\\right, \\rangle]
\\right\\rbrace → [\\right, \\rbrace]
\\right\\rbrack → [\\right, \\rbrack]
\\right\\rfloor → [\\right, \\rfloor]
\\right\\vert → [\\right, \\vert]
\\right\\| → [\\right, \\|]
\\right\\} → [\\right, \\}]
\\right] → [\\right, ]]
\\right| → [\\right, |]
\\right/ → [\\right, /]
\\right[ → [\\right, []
\\right\\rceil → [\\right, \\rceil]
```

#### 3.1.3 间距命令分割
```
,\\; → [,, \\;]
,\\qquad → [,, \\qquad]
```

#### 3.1.4 字体命令分割
```
\\rm\\bf → [\\rm, \\bf]
```

#### 3.1.5 样式和符号分割
```
\\scriptstyle\\lambda → [\\scriptstyle, \\lambda]
\\mu\\nu → [\\mu, \\nu]
\\mu\\alpha → [\\mu, \\alpha]
```

#### 3.1.6 比较符号分割
```
\\gtM → [\\gt, M]
\\gtp → [\\gt, p]
\\ltN → [\\lt, N]
\\ltl → [\\lt, l]
\\ltq → [\\lt, q]
```

#### 3.1.7 特殊对象分割
```
Object] → [O, b, j, e, c, t, ]]
[object → [[, o, b, j, e, c, t]
```

### 3.2 空括号移除规则 (第168-182行)
```
{ } → (删除)
```

### 3.3 非LaTeX命令分割规则 (第184-194行)
```
不以\\开头且包含\\的token → 按字符分割
以\\开头的token → 保持不变
其他token → 按字符分割
```

### 3.4 数组处理规则 (第196-270行)

#### 3.4.1 数组对齐规范化
```
当use_only_c=True时：
l → c
r → c
c → c (保持不变)
```

#### 3.4.2 单行数组简化
```
只有一行的数组 → 直接内容(移除数组结构)
```

#### 3.4.3 数组格式规范化
```
多行数组 → { <列定义> } <行1> \\\\ <行2> \\\\ ...
```

### 3.5 MathType检测和移除 (第384-391行)
```
包含"M a t h T y p e !"的公式 → 完全删除
```

### 3.6 首Token检查 (第393-401行)
```
以_开头的公式 → 删除
以^开头的公式 → 删除
```

### 3.7 括号自动添加规则 (第403-424行)

#### 3.7.1 分数括号添加
```
\\frac后没有{的情况 → \\frac { <分子> } { <分母> }
```

#### 3.7.2 上下标括号添加
```
_后没有{的情况 → _ { <内容> }
^后没有{的情况 → ^ { <内容> }
```

### 3.8 上下标顺序规范化 (第455-490行)
```
任意顺序的上下标 → 先下标后上标
例如：x^2_1 → x _ { 1 } ^ { 2 }
例如：x_1^2 → x _ { 1 } ^ { 2 }
```

### 3.9 样式增强规则 (第527-574行，可选)

#### 3.9.1 粗体样式
```
字母/希腊字母 → \\boldsymbol { <字符> } (概率性应用)
带重音的粗体 → \\boldsymbol { <重音> <字符> }
```

#### 3.9.2 花体样式
```
大写字母 → \\mathcal{<字母>} (概率性应用)
```

#### 3.9.3 黑板粗体样式
```
大写字母和数字1 → \\mathbb{<字符>} (概率性应用)
```

## 4. Token类型配置规则

根据`data/vocabs/token_types2.csv`文件的配置：

### 4.1 Token分类
```
类型0: 普通符号
类型1: 需要后置括号的token
类型2: 需要前置括号的token  
类型3: 需要移除的token
类型5: 需要两个括号的token
类型6: 导致整个公式被移除的token
```

### 4.2 样式标记
```
标记为'y'的token: 被视为样式元素(可被移除)
```

## 5. 处理流程

### 5.1 preprocess_formulas.py处理流程
1. 检查输入文件格式(CSV转TXT)
2. 使用Perl处理hskip命令
3. Unicode编码处理和\r字符清理
4. 调用preprocess_latex.js进行主要处理
5. 最终token清理和输出

### 5.2 improve_tokens.py处理流程
1. 加载token类型配置
2. 分割复合tokens
3. 移除指定tokens
4. 分割非LaTeX命令
5. Token替换
6. 自动添加括号
7. 移除空括号
8. 处理数组结构
9. 移除MathType公式
10. 首token检查
11. 移除样式元素
12. 移除不必要括号
13. 上下标顺序规范化

## 6. 规范化效果总结

这些规则的目标是：
- **标准化表示**: 将相同含义的不同LaTeX表达统一为标准形式
- **结构优化**: 确保括号、上下标等结构的一致性
- **冗余消除**: 移除不必要的样式和格式元素
- **错误处理**: 检测和移除有问题的公式
- **兼容性增强**: 确保生成的LaTeX代码具有良好的兼容性

通过这套规范化规则，MathNet能够有效地处理来自不同源的LaTeX公式，提高数学表达式识别的准确性和一致性。 