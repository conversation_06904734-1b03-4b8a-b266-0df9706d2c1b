#!/usr/bin/env python3
"""
测试align环境花括号处理的最终效果
"""

from normalize_optimized import normalize_latex_string

def test_final_result():
    """测试最终的空花括号删除效果"""
    
    print("=== 测试最终的空花括号删除效果 ===")
    
    # 使用您提供的例子
    test_input = r'\begin{align*}&\ \frac{1}{\pi} (1+r)^{-p} \int_0^1 \left(1 - \frac{4r}{(1+r)^2}x\right)^{-p/2} x^{-1/2} (1-x)^{-1/2} dx \\ &= (1+r)^{-p} {}_2F_1(p/2, 1/2;1; 4r/(1+r)^2)\end{align*}'
    
    print(f"原始输入: {test_input}")
    print(f"开括号数: {test_input.count('{')}, 闭括号数: {test_input.count('}')}")
    
    result = normalize_latex_string(test_input)
    print(f"处理后: {result}")
    print(f"开括号数: {result.count('{')}, 闭括号数: {result.count('}')}")
    print(f"括号平衡: {'✓' if result.count('{') == result.count('}') else '✗'}")
    
    # 检查是否还有空花括号
    if '{{}}' in result:
        print("✗ 仍然存在双重空花括号 {{}}")
    elif '{}' in result:
        print("✗ 仍然存在单个空花括号 {}")
    else:
        print("✓ 成功删除了所有空花括号")
    
    # 检查是否保留了有内容的花括号
    if '\\frac' in result and '\\int' in result:
        print("✓ 成功保留了有内容的花括号")
    else:
        print("✗ 错误删除了有内容的花括号")
    
    print()

if __name__ == "__main__":
    test_final_result() 