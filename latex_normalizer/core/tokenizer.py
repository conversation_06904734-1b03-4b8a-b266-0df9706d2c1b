"""
LaTeX Tokenizer

基于现有代码的LaTeX语法感知tokenizer，提供精确的token分割功能。
复用并优化现有的latex_aware_tokenizer实现。
"""

from typing import List, Tuple, Optional
from ..utils.helpers import is_latex_command, clean_whitespace


class LaTeXTokenizer:
    """
    LaTeX语法感知的tokenizer
    
    使用状态机方法，正确理解LaTeX结构：
    - 命令识别 (\\command)
    - 参数组识别 ({...}, [...])
    - 强制空格命令 (\\ )
    - 数学函数处理
    - 嵌套结构处理
    """
    
    def __init__(self):
        """初始化tokenizer"""
        # 数学字体命令列表
        self.math_commands = [
            '\\mathrm', '\\mathbf', '\\mathit', '\\mathcal', 
            '\\mathbb', '\\mathfrak', '\\mathsf', '\\mathtt'
        ]
    
    def tokenize(self, text: str) -> List[str]:
        """
        对LaTeX文本进行tokenization
        
        Args:
            text: 输入的LaTeX文本
            
        Returns:
            token列表
        """
        if not text:
            return []
        
        tokens = []
        i = 0
        
        while i < len(text):
            # 跳过普通空格
            if text[i] == ' ' and (i == 0 or text[i-1] != '\\'):
                i += 1
                continue
            
            # 处理LaTeX命令
            if text[i] == '\\':
                token, next_i = self._parse_latex_command(text, i)
                if token:
                    tokens.append(token)
                i = next_i
                continue
            
            # 处理花括号组
            if text[i] == '{':
                token, next_i = self._parse_brace_group(text, i)
                if token:
                    tokens.append(token)
                i = next_i
                continue
            
            # 处理方括号组
            if text[i] == '[':
                token, next_i = self._parse_bracket_group(text, i)
                if token:
                    tokens.append(token)
                i = next_i
                continue
            
            # 处理单独的特殊字符
            if text[i] in '()[]':
                tokens.append(text[i])
                i += 1
                continue
            
            # 处理普通字符序列
            token, next_i = self._parse_normal_sequence(text, i)
            if token:
                tokens.append(token)
            i = next_i
        
        # 过滤空token
        return [t for t in tokens if t.strip()]
    
    def _parse_latex_command(self, text: str, start: int) -> Tuple[Optional[str], int]:
        """
        解析LaTeX命令
        
        Args:
            text: 输入文本
            start: 起始位置
            
        Returns:
            (解析的命令, 下一个位置)
        """
        if start >= len(text) or text[start] != '\\':
            return None, start + 1
        
        i = start + 1
        
        # 处理转义字符 \{, \}, \[, \], \$, \%, \&, \#, \_
        if i < len(text) and text[i] in '{}[]$%&#_':
            return f'\\{text[i]}', i + 1
        
        # 处理LaTeX换行符 \\
        if i < len(text) and text[i] == '\\':
            return '\\\\', i + 1
        
        # 处理LaTeX特殊命令：间距命令、重音符、数学模式、特殊符号
        if i < len(text) and text[i] in ',:;!|=^.~"\'`()/-<>':
            return f'\\{text[i]}', i + 1
        
        # 处理强制空格命令 \ (反斜杠+空格)
        if i < len(text) and text[i] == ' ':
            # 检查后面是否跟着运算符或标点符号
            if i + 1 < len(text) and text[i + 1] in '+\\<>*_[]{}':
                return f'\\ {text[i + 1]}', i + 2
            else:
                return '\\ ', i + 1
        
        # 处理普通命令
        command = '\\'
        while i < len(text) and (text[i].isalpha() or text[i] in '*'):
            command += text[i]
            i += 1
        
        # 特殊处理环境命令 \begin 和 \end
        if command in ['\\begin', '\\end']:
            if i < len(text) and text[i] == '{':
                # 解析整个环境命令，包括花括号
                brace_count = 1
                j = i + 1
                while j < len(text) and brace_count > 0:
                    if text[j] == '{':
                        brace_count += 1
                    elif text[j] == '}':
                        brace_count -= 1
                    j += 1
                
                if brace_count == 0:
                    return text[start:j], j
        
        # 特殊处理数学函数命令
        if command in self.math_commands:
            # 检查后面是否直接跟着字符（不是花括号）
            if i < len(text) and text[i] not in '{[ ':
                # 只取第一个字符作为参数
                return command, i
        
        return command, i
    
    def _parse_brace_group(self, text: str, start: int) -> Tuple[Optional[str], int]:
        """
        解析花括号组
        
        Args:
            text: 输入文本
            start: 起始位置
            
        Returns:
            (解析的组, 下一个位置)
        """
        if start >= len(text) or text[start] != '{':
            return None, start + 1
        
        brace_count = 1
        i = start + 1
        
        while i < len(text) and brace_count > 0:
            if text[i] == '{':
                brace_count += 1
            elif text[i] == '}':
                brace_count -= 1
            i += 1
        
        if brace_count == 0:
            return text[start:i], i
        else:
            # 不匹配的花括号，返回到下一个空格或结尾
            while i < len(text) and text[i] != ' ':
                i += 1
            return text[start:i], i
    
    def _parse_bracket_group(self, text: str, start: int) -> Tuple[Optional[str], int]:
        """
        解析方括号组
        
        Args:
            text: 输入文本
            start: 起始位置
            
        Returns:
            (解析的组, 下一个位置)
        """
        if start >= len(text) or text[start] != '[':
            return None, start + 1
        
        bracket_count = 1
        i = start + 1
        
        while i < len(text) and bracket_count > 0:
            if text[i] == '[':
                bracket_count += 1
            elif text[i] == ']':
                bracket_count -= 1
            i += 1
        
        if bracket_count == 0:
            return text[start:i], i
        else:
            # 不匹配的方括号，返回到下一个空格或结尾
            while i < len(text) and text[i] != ' ':
                i += 1
            return text[start:i], i
    
    def _parse_normal_sequence(self, text: str, start: int) -> Tuple[Optional[str], int]:
        """
        解析普通字符序列
        
        Args:
            text: 输入文本
            start: 起始位置
            
        Returns:
            (解析的序列, 下一个位置)
        """
        i = start
        
        # 特殊处理：如果是多位数字，需要检查是否应该分割为单个数字
        if text[i].isdigit():
            # 检查前面是否是需要单个数字参数的命令（如\frac）
            # 简化处理：对于多位数字，只取第一位
            if len(text) > start + 1 and text[start + 1].isdigit():
                # 多位数字，只取第一位
                i = start + 1
            else:
                # 单位数字，正常处理
                while i < len(text) and text[i].isdigit():
                    i += 1
        else:
            while i < len(text) and text[i] not in '\\{}[] ' and not text[i].isdigit():
                i += 1
        
        if i > start:
            return text[start:i], i
        else:
            return None, start + 1
