import os
import re
import json
import shutil
import logging
import numpy as np
from PIL import Image


SKIP_PATTERNS = [r'\{', r'\}', r'[\[\]]', r'\\begin\{.*?\}', r'\\end\{.*?\}', r'\^', r'\_', r'\\.*rule.*', r'\\.*line.*', r'\[[\-.0-9]+[epm][xtm]\]']
SKIP_Tokens = ['\\', '\\\\', '\\index', '\\a', '&', '$', '\\multirow', '\\def', '\\edef', '\\raggedright', '\\url', '\\cr', '\\ensuremath', '\\left', '\\right', 
               '\\mathchoice', '\\scriptstyle', '\\displaystyle', '\\qquad', '\\quad', '\\,', '\\!', '~', '\\boldmath', '\\gdef', '\\today', '\\the']
PHANTOM_Tokens = ['\\fontfamily', '\\vphantom', '\\phantom', '\\rowcolor', '\\ref', '\\thesubequation', '\\global', '\\theboldgroup']
TWO_Tail_Tokens = ['\\frac', '\\binom']
AB_Tail_Tokens = ['\\xrightarrow', '\\xleftarrow', '\\sqrt']        # special token \xxx [] {} 
TWO_Tail_Invisb_Tokens = ['\\overset', '\\underset', '\\stackrel']
ONE_Tail_Tokens = ['\\widetilde', '\\overline', '\\hat', '\\widehat', '\\tilde', '\\Tilde', '\\dot', '\\bar', '\\vec', '\\underline', '\\underbrace', '\\check',
                   '\\breve', '\\Bar', '\\Vec', '\\mathring', '\\ddot', '\\Ddot', '\\dddot', '\\ddddot']
ONE_Tail_Invisb_Tokens = ['\\boldsymbol', '\\pmb', '\\textbf', '\\mathrm', '\\mathbf', '\\mathbb', '\\mathcal', '\\textmd', '\\texttt', '\\textnormal', 
                          '\\text', '\\textit', '\\textup', '\\mathop', '\\mathbin', '\\smash', '\\operatorname', '\\textrm', '\\mathfrak', '\\emph',
                          '\\textsf', '\\textsc']


def flatten_multiline(latex):
    brace_map = {
        "\\left(": "\\right)",
        "\\left[": "\\right]",
        "\\left{": "\\right}",
    }
    l_split = latex.split(' ')
    if l_split[0] == "\\begin{array}":
        if l_split[-1] == "\\end{array}":
            l_split = l_split[2:-1]
        else:
            l_split = l_split[2:]
    
    idx = 0
    while idx < len(l_split):
        token = l_split[idx]
        if token.startswith("\\left") and token in brace_map.keys():
            end_idx = find_matching_brace(l_split, idx, brace=[token, brace_map[token]])
            if end_idx != -1:
                idx = end_idx
        elif token in ["\\\\", "~", "\\qquad"]:
            l_split = l_split[0:idx] + l_split[idx+1:]
            idx -= 1
        idx += 1
    latex = ' '.join(l_split)
    return "$ "+latex+" $"
    
    
def clean_latex(text):
    """
    LaTeX文本清理函数 - 智能空格处理

    功能: 移除不必要的空格，但保留LaTeX命令后必需的空格
    数据流: 原始LaTeX → 空格清理 → 必要空格恢复 → 清理后LaTeX

    Args:
        text: 需要清理的LaTeX字符串

    Returns:
        清理后的LaTeX字符串
    """

    # 步骤1: 智能空格移除
    # 移除非反斜杠字符后的多余空格，但保留LaTeX命令结构
    # 正则说明: (?<=[^\\]) - 前面不是反斜杠, \s+ - 一个或多个空格, (?=[^\\]) - 后面不是反斜杠
    cleaned_text = re.sub(r'(?<=[^\\])\s+(?=[^\\])', '', text)

    # 步骤2: 恢复必要空格
    # 某些LaTeX命令后必须有空格才能正确解析
    essential_commands = ["\\hline", "\\midrule", "\\times", "\\bf", "\\footnotesize", "\\cr", '\\log']
    for item in essential_commands:
        cleaned_text = cleaned_text.replace(item, item+" ")  # 确保命令后有空格

    # 步骤3: 特殊情况处理
    # 移除mathcolor命令前的多余空格
    cleaned_text = cleaned_text.replace(" \\mathcolor{black}", "\\mathcolor{black}")

    return cleaned_text

def remove_trailing_latex(formula):
    """
    移除LaTeX公式尾部的间距和装饰命令

    功能: 清理公式末尾的空格、间距命令和标点符号
    数据流: 带尾部命令的公式 → 正则匹配 → 清理后的公式

    Args:
        formula: 需要清理的LaTeX公式字符串

    Returns:
        移除尾部命令后的公式字符串
    """

    # 匹配公式尾部的各种间距和装饰命令
    # 包括: \hspace, \vspace, \smallskip, \medskip, \quad, \qquad, \bigskip, 标点符号等
    pattern = r'(\\(hspace\*?\{[^{}]*?\}|vspace\*?\{[^{}]*?\}|smallskip|medskip|quad|qquad|bigskip|[;,])|\~|\.)*$'

    # 移除匹配的尾部内容 (只处理一次)
    cleaned_formula = re.sub(pattern, '', formula, count=1)
    return cleaned_formula

def find_matching_brace(sequence, start_index, brace=['{', '}']):
    # Finds the index of the matching brace for the one at start_index
    left_brace, right_brace = brace
    depth = 0
    for i, char in enumerate(sequence[start_index:], start=start_index):
        if char == left_brace:
            depth += 1
        elif char == right_brace:
            depth -= 1
            if depth == 0:
                return i
    if depth > 0:
        error_info = "Warning! found no matching brace in sequence !"
        raise ValueError(error_info)
    return -1

def normalize_latex(l, rm_trail=False):
    """
    LaTeX规范化主函数 - Python端的核心处理逻辑
    数据流: 原始LaTeX → 类型检测 → 基础清理 → 命令统一 → 函数展开 → 规范化LaTeX

    Args:
        l: 输入的LaTeX字符串
        rm_trail: 是否移除尾部LaTeX命令

    Returns:
        规范化后的LaTeX字符串
    """

    # 步骤1: 类型检测 - 判断是表格还是公式
    if "tabular" in l:
        latex_type = "tabular"
    else:
        latex_type = "formula"

    # 步骤2: 尾部清理 (可选)
    if rm_trail:
        l = remove_trailing_latex(l)

    # 步骤3: 基础矩阵命令替换
    # 数据流: \pmatrix → \mypmatrix, \matrix → \mymatrix
    l = l.strip().replace(r'\pmatrix', r'\mypmatrix').replace(r'\matrix', r'\mymatrix')

    # 步骤4: 移除对齐相关命令 (表格布局命令，难以处理)
    for item in ['\\raggedright', '\\arraybackslash']:
        l = l.replace(item, "")

    # 步骤5: 移除大小写转换命令
    for item in ['\\lowercase', '\\uppercase']:
        l = l.replace(item, "")
        
    # TODO \hspace {1 . 5 cm}, for formula, change to \hspace{1.5cm}, for table, remove it.
    pattern = r'\\[hv]space { [.0-9a-z ]+ }'
    old_token = re.findall(pattern, l, re.DOTALL)
    if latex_type == "tabular":
        new_token = ["" for item in old_token]
    else:
        new_token = [item.replace(" ", "") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft)
        
    # TODO take \begin {tabular} {} as one token
    # TODO there are \begin{array} in table too，so the process should run in both formula and table.
    if latex_type == "tabular":
        l = l.replace("\\begin {tabular}", "\\begin{tabular}")
        l = l.replace("\\end {tabular}", "\\end{tabular}")
        l = l.replace("\\begin {array}", "\\begin{array}")
        l = l.replace("\\end {array}", "\\end{array}")
        l_split = l.split(' ')
        idx = 0
        while idx < len(l_split):
            token = l_split[idx]
            if token == "\\begin{tabular}":
                sub_idx = idx + 1
                end_idx = find_matching_brace(l_split, sub_idx)
                new_token = "".join(l_split[idx: end_idx+1])
                l_split = l_split[0:idx] + [new_token] + l_split[end_idx+1:]
                break
            idx += 1
        l = ' '.join(l_split)
        
        # TODO some complex format, hart to deal with re.match, so using brace match, such as：\cmidrule ( l { 3 p t } r { 3 p t } ) { 1 - 1 }
        l_split = l.split(' ')
        idx = 0
        while idx < len(l_split):
            token = l_split[idx]
            if token in ["\\cmidrule", "\\cline"]:
                sub_idx = idx + 1
                if l_split[sub_idx] == "(":
                    mid_end = find_matching_brace(l_split, sub_idx, brace=['(', ')'])
                    end_idx = find_matching_brace(l_split, mid_end+1)
                else:
                    end_idx = find_matching_brace(l_split, sub_idx)
                new_token = "".join(l_split[idx: end_idx+1])
                l_split = l_split[0:idx] + [new_token] + l_split[end_idx+1:]
            idx += 1
        l = ' '.join(l_split)
    
    pattern = r'\\begin{array} { [lrc ]+ }'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace("\\begin{array} ", "<s>").replace(" ", "").replace("<s>", "\\begin{array} ") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft)
    
    # # TODO token such \not= should be one token
    # pattern = r'\\not [<>+=\-]'
    # old_token = re.findall(pattern, l, re.DOTALL)
    # new_token = [item.replace(" ", "") for item in old_token]
    # for bef, aft in zip(old_token, new_token):
    #     l = l.replace(bef, aft)
    
    # # TODO \not xx shoudle be combined as one token
    # pattern = r'\\not [\\=\<\>][^ ]+ '
    # old_token = re.findall(pattern, l, re.DOTALL)
    # new_token = [item.replace(" ", "") for item in old_token]
    # for bef, aft in zip(old_token, new_token):
    #     l = l.replace(bef, aft+" ")
        
    # 步骤6: 函数名和符号展开 (为了便于字符级匹配)
    # 核心思想: 将复合命令分解为单个字符，便于边界框匹配

    l = " "+l+" "  # 添加前后空格确保正则匹配正确

    # 步骤6.1: 连字符展开
    l = re.sub(r'(?<=\s)--(?=\s)', r'- -', l)        # 双连字符 → 两个单独的-
    l = re.sub(r'(?<=\s)---(?=\s)', r'- - -', l)     # 三连字符 → 三个单独的-

    # 步骤6.2: 省略号统一化 (多种省略号命令统一为三个点)
    # 数据流: \ldots, \cdots, \dots等 → . . .
    l = re.sub(r'(?<=\s)…(?=\s)', r'. . .', l)           # Unicode省略号
    l = re.sub(r'(?<=\s)\\ldots(?=\s)', r'. . .', l)     # 低位省略号
    l = re.sub(r'(?<=\s)\\hdots(?=\s)', r'. . .', l)     # 水平省略号
    l = re.sub(r'(?<=\s)\\cdots(?=\s)', r'. . .', l)     # 居中省略号
    l = re.sub(r'(?<=\s)\\dddot(?=\s)', r'. . .', l)     # 三点
    l = re.sub(r'(?<=\s)\\dots(?=\s)', r'. . .', l)      # 通用省略号
    l = re.sub(r'(?<=\s)\\dotsc(?=\s)', r'. . .', l)     # 逗号省略号
    l = re.sub(r'(?<=\s)\\dotsi(?=\s)', r'. . .', l)     # 积分省略号
    l = re.sub(r'(?<=\s)\\dotsm(?=\s)', r'. . .', l)     # 乘法省略号
    l = re.sub(r'(?<=\s)\\dotso(?=\s)', r'. . .', l)     # 其他省略号
    l = re.sub(r'(?<=\s)\\dotsb(?=\s)', r'. . .', l)     # 二元省略号
    l = re.sub(r'(?<=\s)\\mathellipsis(?=\s)', r'. . .', l)  # 数学省略号
    # 步骤6.3: 数学函数名展开 (核心规范化逻辑)
    # 目的: 将函数名分解为单个字符，便于字符级别的边界框匹配
    # 数据流: \sin → \mathrm { s i n }

    # 指数和对数函数
    l = re.sub(r'(?<=\s)\\ex(?=\s)', r'\\mathrm { e x }', l)      # 自然常数e的x次方
    l = re.sub(r'(?<=\s)\\ln(?=\s)', r'\\mathrm { l n }', l)      # 自然对数
    l = re.sub(r'(?<=\s)\\lg(?=\s)', r'\\mathrm { l g }', l)      # 常用对数
    l = re.sub(r'(?<=\s)\\log(?=\s)', r'\\mathrm { l o g }', l)   # 对数
    l = re.sub(r'(?<=\s)\\exp(?=\s)', r'\\mathrm { e x p }', l)   # 指数函数

    # 三角函数
    l = re.sub(r'(?<=\s)\\sin(?=\s)', r'\\mathrm { s i n }', l)   # 正弦
    l = re.sub(r'(?<=\s)\\cos(?=\s)', r'\\mathrm { c o s }', l)   # 余弦
    l = re.sub(r'(?<=\s)\\tan(?=\s)', r'\\mathrm { t a n }', l)   # 正切
    l = re.sub(r'(?<=\s)\\cot(?=\s)', r'\\mathrm { c o t }', l)   # 余切
    l = re.sub(r'(?<=\s)\\sec(?=\s)', r'\\mathrm { s e c }', l)   # 正割
    l = re.sub(r'(?<=\s)\\csc(?=\s)', r'\\mathrm { c s c }', l)   # 余割
    l = re.sub(r'(?<=\s)\\scs(?=\s)', r'\\mathrm { s c s }', l)   # 特殊余割

    # 双曲函数
    l = re.sub(r'(?<=\s)\\sinh(?=\s)', r'\\mathrm { s i n h }', l)  # 双曲正弦
    l = re.sub(r'(?<=\s)\\cosh(?=\s)', r'\\mathrm { c o s h }', l)  # 双曲余弦
    l = re.sub(r'(?<=\s)\\tanh(?=\s)', r'\\mathrm { t a n h }', l)  # 双曲正切
    l = re.sub(r'(?<=\s)\\coth(?=\s)', r'\\mathrm { c o t h }', l)  # 双曲余切

    # 反三角函数
    l = re.sub(r'(?<=\s)\\arcsin(?=\s)', r'\\mathrm { a r c s i n }', l)  # 反正弦
    l = re.sub(r'(?<=\s)\\arccos(?=\s)', r'\\mathrm { a r c c o s }', l)  # 反余弦
    l = re.sub(r'(?<=\s)\\arctan(?=\s)', r'\\mathrm { a r c t a n }', l)  # 反正切

    # 其他数学函数
    l = re.sub(r'(?<=\s)\\min(?=\s)', r'\\mathrm { m i n }', l)   # 最小值
    l = re.sub(r'(?<=\s)\\max(?=\s)', r'\\mathrm { m a x }', l)   # 最大值
    l = re.sub(r'(?<=\s)\\deg(?=\s)', r'\\mathrm { d e g }', l)   # 度数
    l = re.sub(r'(?<=\s)\\arg(?=\s)', r'\\mathrm { a r g }', l)   # 幅角
    l = re.sub(r'(?<=\s)\\dim(?=\s)', r'\\mathrm { d i m }', l)   # 维数
    l = re.sub(r'(?<=\s)\\ker(?=\s)', r'\\mathrm { k e r }', l)   # 核
    l = re.sub(r'(?<=\s)\\hom(?=\s)', r'\\mathrm { h o m }', l)   # 同态

    # 模运算 (注意: \pmod实际上和\mod不同，但为简化处理统一为\mod)
    l = re.sub(r'(?<=\s)\\mod(?=\s)', r'\\mathrm { m o d }', l)   # 模
    l = re.sub(r'(?<=\s)\\bmod(?=\s)', r'\\mathrm { m o d }', l)  # 二元模
    l = re.sub(r'(?<=\s)\\pmod(?=\s)', r'\\mathrm { m o d }', l)  # 括号模
    
    # 步骤7: Token合并处理 (将相关的tokens合并为单个token)

    # 步骤7.1: \string命令合并
    # 数据流: \string abc → \stringabc
    pattern = r'\\string [^ ]+ '
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]  # 移除空格
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft+" ")  # 替换并保留尾部空格

    # 步骤7.2: 大型定界符合并 (\big, \Big, \bigg等与括号的组合)
    # 数据流: \big ( → \big(, \Big [ → \Big[
    pattern = r'\\[Bb]ig[g]?[glrm]? [(){}|\[\]] '
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]  # 移除中间空格
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft+" ")  # 合并为单个token

    # 步骤7.3: 大型定界符与LaTeX命令的合并
    # 数据流: \big \langle → \big\langle
    pattern = r'\\[Bb]ig[g]?[glrm]? \\.*? '
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft+" ")

    # 步骤7.4: \operatorname*处理 (移除*号避免与mathcolor冲突)
    # 问题: \operatorname*与mathcolor结合时会出错，*号实际无用，直接移除
    pattern = r'\\operatorname \*'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = ["\\operatorname" for item in old_token]  # 移除*号
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft)

    # 步骤7.5: 移除有害命令
    # \lefteqn会导致字母重叠，影响渲染效果，直接移除
    l = l.replace("\\lefteqn", "")

    # 步骤7.6: \footnote简化处理
    # \footnote无法作为ONE_Tail_Invisb_Tokens处理，简化为上标^
    l = l.replace("\\footnote ", "^ ")
    
    # 步骤7.7: 重音符号合并 (\' 与字符的组合)
    # 问题: \'无法单独渲染，会影响视觉效果，需要与后续字符合并
    # 数据流: \' e → \'e (但如果后面是{则分别渲染)
    pattern = r'\\\' [^{] '
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]  # 移除空格合并
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft+" ")

    # 步骤7.8: 表格布局调整参数合并 (仅对表格类型)
    # 数据流: [ -1.5ex ] → [-1.5ex] (布局调整参数，无需渲染)
    if latex_type == "tabular":
        pattern = r'\[ [\-.0-9 ]+[exptcm ]+ \]'
        old_token = re.findall(pattern, l, re.DOTALL)
        new_token = [item.replace(" ", "") for item in old_token]  # 移除内部空格
        for bef, aft in zip(old_token, new_token):
            l = l.replace(bef, aft)

    # 步骤7.9: \parbox命令合并
    # 数据流: \parbox { 3cm } → \parbox{3cm}
    pattern = r'\\parbox {[^{]+}'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]  # 移除空格
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft)

    # 步骤7.10: \raisebox命令合并 (垂直位置调整)
    # 数据流: \raisebox { -1.5ex } [ 0pt ] { → \raisebox{-1.5ex}[0pt]{
    pattern = r'\\raisebox {[^{]+} [\[\]0-9 exptcm]+{'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft[0:-1]+" {")  # 保留最后的{前的空格

    # 步骤7.11: \char字符命令合并
    # 数据流: { \char123 } → {\char123}
    pattern = r'{ \\char[0-9\' ]+}'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, "{ "+aft[1:-1]+" }")  # 保持外部格式

    # 步骤7.12: \rule线条命令合并 (不渲染的装饰线)
    # 数据流: \rule { 1pt } { 2pt } → \rule{1pt}{2pt}
    pattern = r'\\rule {[ .0-9a-z]+} {[ .0-9a-z]+}'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft)

    # 步骤7.13: \specialrule特殊线条命令合并
    # 数据流: \specialrule { 1pt } { 2pt } { 2pt } → \specialrule{1pt}{2pt}{2pt}
    pattern = r'\\specialrule {[ .0-9a-z]+} {[ .0-9a-z]+} {[ .0-9a-z]+}'
    old_token = re.findall(pattern, l, re.DOTALL)
    new_token = [item.replace(" ", "") for item in old_token]
    for bef, aft in zip(old_token, new_token):
        l = l.replace(bef, aft)
        
    # 步骤8: 颜色命令移除 (为后续颜色添加做准备)
    # 移除原有的颜色命令，便于后续统一添加颜色标注
    # 支持的颜色命令: \color[rgb]{0,1,0}, \color{red}, \textcolor, \colorbox, \cellcolor
    pattern = r'\\colorbox[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\color[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\textcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\cellcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } '
    old_token = re.findall(pattern, l, re.DOTALL)
    for bef in old_token:
        l = l.replace(bef, "")  # 移除所有颜色命令

    # 步骤9: 括号补全处理 (根据token类型自动添加缺失的括号)
    # 核心功能: 确保LaTeX命令具有正确的参数结构
    l_split = l.split(' ')  # 按空格分割为token列表
    idx = 0

    while idx < len(l_split):
        token = l_split[idx]

        # 步骤9.1: 单参数token处理 (\hat, \tilde, \mathbf等)
        if token in ONE_Tail_Tokens + ONE_Tail_Invisb_Tokens:
            # 规范化: \hat \lambda → \hat {\lambda}
            sub_idx = idx + 1
            while sub_idx < len(l_split) and l_split[sub_idx] in ONE_Tail_Tokens+ONE_Tail_Invisb_Tokens:
                sub_idx += 1
            new_split = l_split[0:idx]
            for ii in range(idx, sub_idx):
                new_split = new_split + [l_split[ii], "{"]
            if l_split[sub_idx] != "{":
                new_split = new_split + [l_split[sub_idx]] + ["}"]*(sub_idx-idx)
                l_split = new_split + l_split[sub_idx+1:]
            else:
                end_idx = find_matching_brace(l_split, sub_idx)
                new_split = new_split + l_split[sub_idx+1:end_idx] + ["}"]*(sub_idx-idx)
                l_split = new_split + l_split[end_idx+1:]
        elif token in AB_Tail_Tokens:
        # ** normalize special tokens such as \sqrt, fill the missing [] {} in \sqrt [] {}, yet the [] is optional, for example: \sqrt A B -> \sqrt {A} B and \sqrt [A] B -> \sqrt [A] {B}
            if l_split[idx + 1] != "[" and l_split[idx + 1] != "{":
                l_split = l_split[0:idx+1] + ["{"] + [l_split[idx+1]] + ["}"] + l_split[idx+2:]
            else:
                if l_split[idx + 1] == "[":
                    end1 = find_matching_brace(l_split, idx+1, brace=['[', ']'])
                else:
                    end1 = idx
                if l_split[end1 + 1] != "{":
                    l_split = l_split[0:end1+1] + ["{"] + [l_split[end1+1]] + ["}"] + l_split[end1+2:]
        elif token in TWO_Tail_Tokens + TWO_Tail_Invisb_Tokens:
        # ** normalize special tokens such as \frac, add missing brace in \frac {A} {B} for example: \frac {\lambda} 2 -> \frac {\lambda} {2}
            if l_split[idx + 1] != "{":
                l_split = l_split[0:idx+1] + ["{"] + [l_split[idx+1]] + ["}"] + l_split[idx+2:]
            end1 = find_matching_brace(l_split, idx+1)
            if l_split[end1 + 1] != "{":
                l_split = l_split[0:end1+1] + ["{"] + [l_split[end1+1]] + ["}"] + l_split[end1+2:]
            
        idx += 1
    l = ' '.join(l_split)
    
    return l

def token_add_color(l_split, idx, render_dict):
    token = l_split[idx]
    if token in PHANTOM_Tokens:
        # ** special tokens that do not need render, skip it 
        if l_split[idx + 1] == '{':
            brace_end = find_matching_brace(l_split, idx + 1)
        else:
            brace_end = idx + 1
        next_idx = brace_end + 1
    elif token in TWO_Tail_Tokens:
        # ** tokens such as \frac A B, and the token needs render too.
        num_start = idx + 1
        num_end = find_matching_brace(l_split, num_start)
        den_start = num_end + 1
        den_end = find_matching_brace(l_split, den_start)
        l_split_copy = l_split[:idx] + [r'\mathcolor{black}{'+token+'{'] + \
                        [r'\mathcolor{gray}{'] + l_split[num_start + 1:num_end] + \
                        ['}'] + [r'}{'] + [r'\mathcolor{gray}{'] + l_split[den_start + 1:den_end] + \
                        ['}'] + ['}'] + ['}'] + l_split[den_end + 1:]
                        
        l_new = ' '.join(l_split_copy)
        l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
        render_dict[str(idx)] = l_new, token
        next_idx = idx + 1
    elif token in ONE_Tail_Tokens:
        # ** tokens such as \hat A, and the token needs render too.
        num_start = idx + 1
        num_end = find_matching_brace(l_split, num_start)
        l_split_copy = l_split[:idx] + [r'\mathcolor{black}{'] + l_split[idx: num_start+1] + \
                        [r'\mathcolor{gray}{'] + l_split[num_start+1: num_end] + \
                        ['}'] + l_split[num_end: num_end+1] + ['}'] + l_split[num_end+1:]
        l_new = ' '.join(l_split_copy)
        l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
        render_dict[str(idx)] = l_new, token
        next_idx = idx + 1
    elif token in ONE_Tail_Invisb_Tokens:
        # ** tokens such as \text A B, and the token does not need render.
        num_start = idx + 1
        num_end = find_matching_brace(l_split, num_start)
        sub_idx = num_start+1
        if num_end-num_start == 2:
            l_split_copy = l_split.copy()
            l_split_copy[sub_idx] = r'{\mathcolor{black}{' + l_split_copy[sub_idx] + '}}'
            l_new = ' '.join(l_split_copy)
            l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
            render_dict[str(idx)] = l_new, l_split[sub_idx]
            next_idx = num_end
        else:
            while sub_idx < num_end:
                l_split, sub_idx, render_dict = token_add_color(l_split, sub_idx, render_dict)
        next_idx = num_end + 1
    elif token in AB_Tail_Tokens:
        # ** special token \xrightarrow, could be \xrightarrow [] {} or \xrightarrow {}, process method are different.
        if l_split[idx+1] == '{':
            num_start = idx + 1
            num_end = find_matching_brace(l_split, num_start)
            l_split_copy = l_split[:idx] + [r'\mathcolor{black}{'] + l_split[idx: idx+2] \
                        + [r'\mathcolor{gray}{'] + l_split[num_start+1: num_end] + ['}}'] + l_split[num_end:]
            l_new = ' '.join(l_split_copy)
            l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
            render_dict[str(idx)] = l_new, token
            sub_idx = num_start+1
            while sub_idx < num_end:
                l_split, sub_idx, render_dict = token_add_color(l_split, sub_idx, render_dict)
            next_idx = num_end + 1
        elif l_split[idx+1] == '[':
            num_start = idx + 1
            num_end = find_matching_brace(l_split, num_start, brace=['[', ']'])
            den_start = num_end + 1
            den_end = find_matching_brace(l_split, den_start)
            l_split_copy = l_split[:idx] + [r'{\mathcolor{black}{'] + l_split[idx: idx+2] \
                        + [r'\mathcolor{gray}{'] + l_split[idx+2: num_end] + ['}'] + l_split[num_end:den_start+1] \
                        + [r'\mathcolor{gray}{'] + l_split[den_start+1: den_end] + ['}'] + l_split[den_end: den_end+1] \
                        + ['}}'] + l_split[den_end+1:]
            l_new = ' '.join(l_split_copy)
            l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
            render_dict[str(idx)] = l_new, token
            sub_idx = num_start + 1
            while sub_idx < num_end:
                l_split, sub_idx, render_dict = token_add_color(l_split, sub_idx, render_dict)
            sub_idx = den_start + 1
            while sub_idx < den_end:
                l_split, sub_idx, render_dict = token_add_color(l_split, sub_idx, render_dict)
            next_idx = den_end + 1
    elif token in ["\\multicolumn", "\\multirow"]:
        # ** tokens with three {}, such as \multicolumn {} {} {}, the text in third {} need be rendered.
        first_start = idx + 1
        first_end = find_matching_brace(l_split, first_start)
        second_start = first_end + 1
        second_end = find_matching_brace(l_split, second_start)
        third_start = second_end + 1
        third_end = find_matching_brace(l_split, third_start)
        
        sub_idx = third_start+1
        while sub_idx < third_end:
            l_split, sub_idx, render_dict = token_add_color(l_split, sub_idx, render_dict)
        next_idx = third_end + 1
    elif token in SKIP_Tokens+TWO_Tail_Invisb_Tokens or any(re.match(pattern, token) for pattern in SKIP_PATTERNS):
        # ** tokens no need render, just skip
        # print('skip', idx, token)
        # TODO special case :[], could be single, or in \sqrt[]{}.
        if (token == "[" and l_split[idx-1]!="\\sqrt") or (token == "]" and idx>=3 and l_split[idx-3]!="\\sqrt"):
            l_split_copy = l_split.copy()
            l_split_copy[idx] = r'\mathcolor{black}{ ' + l_split_copy[idx] + ' }'
            l_new = ' '.join(l_split_copy)
            l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
            render_dict[str(idx)] = l_new, token
            next_idx = idx + 1
        else:
            next_idx = idx + 1
    else:
        # ** nomal token
        l_split_copy = l_split.copy()
        # TODO sometimes there is translation after add color, the exp prove that \mathcolor{black}{ A } is better than \mathcolor{black}{A}
        l_split_copy[idx] = r'\mathcolor{black}{ ' + l_split_copy[idx] + ' }'

        l_new = ' '.join(l_split_copy)
        l_new = r'\mathcolor{gray}{ ' + l_new + ' }'
        render_dict[str(idx)] = l_new, token
        next_idx = idx + 1
        
    return l_split, next_idx, render_dict


def token_add_color_RGB(l_split, idx, token_list, brace_color=False):
    """using \mathcolor[RGB]{r,g,b} to render latex. 
    """
    token = l_split[idx]
    if not token:
        next_idx = idx + 1
    elif token in PHANTOM_Tokens:
        # ** special tokens that do not need render, skip it 
        if l_split[idx + 1] == '{':
            brace_end = find_matching_brace(l_split, idx + 1)
        else:
            brace_end = idx + 1
        next_idx = brace_end + 1
    elif token in TWO_Tail_Tokens:
        # ** tokens such as \frac A B, and the token needs render too.
        num_start = idx + 1
        num_end = find_matching_brace(l_split, num_start)
        den_start = num_end + 1
        den_end = find_matching_brace(l_split, den_start)
        color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
        l_split = l_split[:idx] + [color_token+token] + l_split[idx+1: den_end+1] + ["}"] + l_split[den_end+1:]
        token_list.append(token)
        next_idx = idx + 1
    elif token in ONE_Tail_Tokens:
        # ** tokens such as \hat A, and the token needs render too.
        num_start = idx + 1
        num_end = find_matching_brace(l_split, num_start)
        color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
        if token != "\\underbrace" and num_end+1 < len(l_split) and l_split[num_end+1] == "_":
            l_split = l_split[:idx] + ["{"+color_token+token] + l_split[idx+1: num_end+1] + ["}}"] + l_split[num_end+1:]
        else:
            l_split = l_split[:idx] + [color_token+token] + l_split[idx+1: num_end+1] + ["}"] + l_split[num_end+1:]
        token_list.append(token)
        next_idx = idx + 1
    elif token in ONE_Tail_Invisb_Tokens:
        # ** tokens such as \text A B, and the token does not need render.
        num_start = idx + 1
        num_end = find_matching_brace(l_split, num_start)
        sub_idx = num_start+1
        if num_end-num_start == 2:
            color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
            token_list.append(l_split[num_start+1])
            l_split = l_split[:num_start+1] + [color_token+l_split[num_start+1]+"}"] + l_split[num_end:]
        else:
            while sub_idx < num_end:
                l_split, sub_idx, token_list = token_add_color_RGB(l_split, sub_idx, token_list)
        next_idx = num_end + 1
    elif token in AB_Tail_Tokens:
        # ** special token \xrightarrow, could be \xrightarrow [] {} or \xrightarrow {}, process method are different.
        if l_split[idx+1] == '{':
            num_start = idx + 1
            num_end = find_matching_brace(l_split, num_start)
            color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
            l_split = l_split[:idx] + [color_token+token] + l_split[idx+1: num_end+1] + ["}"] + l_split[num_end+1:]
            token_list.append(token)
            sub_idx = num_start+1
            while sub_idx < num_end:
                l_split, sub_idx, token_list = token_add_color_RGB(l_split, sub_idx, token_list)
            next_idx = num_end + 1
        elif l_split[idx+1] == '[':
            num_start = idx + 1
            num_end = find_matching_brace(l_split, num_start, brace=['[', ']'])
            den_start = num_end + 1
            den_end = find_matching_brace(l_split, den_start)
            color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
            l_split = l_split[:idx] + [color_token+token] + l_split[idx+1: den_end+1] + ["}"] + l_split[den_end+1:]
            token_list.append(token)
            sub_idx = num_start + 1
            while sub_idx < num_end:
                l_split, sub_idx, token_list = token_add_color_RGB(l_split, sub_idx, token_list, brace_color=True)
            sub_idx = den_start + 1
            while sub_idx < den_end:
                l_split, sub_idx, token_list = token_add_color_RGB(l_split, sub_idx, token_list)
            next_idx = den_end + 1
    elif token in ["\\multicolumn", "\\multirow"]:
        # ** tokens with three {}, such as \multicolumn {} {} {}, the text in third {} need be rendered.
        first_start = idx + 1
        first_end = find_matching_brace(l_split, first_start)
        second_start = first_end + 1
        second_end = find_matching_brace(l_split, second_start)
        third_start = second_end + 1
        third_end = find_matching_brace(l_split, third_start)
        
        sub_idx = third_start+1
        while sub_idx < third_end:
            l_split, sub_idx, token_list = token_add_color_RGB(l_split, sub_idx, token_list)
        next_idx = third_end + 1
    elif token in SKIP_Tokens+TWO_Tail_Invisb_Tokens or any(re.match(pattern, token) for pattern in SKIP_PATTERNS):
        # ** tokens no need render, just skip
        # print('skip', idx, token)
        # TODO special case :[], could be single, or in \sqrt[]{}.
        if (token == "[" and l_split[idx-1]!="\\sqrt") or (token == "]" and idx>=3 and l_split[idx-3]!="\\sqrt"):
            color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
            l_split = l_split[:idx] + [color_token + l_split[idx] + "}"] + l_split[idx+1:]
            token_list.append(token)
            next_idx = idx + 1
        else:
            next_idx = idx + 1
    else:
        # ** nomal token
        if brace_color or (idx > 1 and l_split[idx-1] == "_"):
            color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
            l_split = l_split[:idx] + ["{" + color_token + l_split[idx] + "}}"] + l_split[idx+1:]
            token_list.append(token)
            next_idx = idx + 1
        else:
            color_token = "\\mathcolor[RGB]{<color_<idx>>}{".replace("<idx>", str(len(token_list)))
            l_split = l_split[:idx] + [color_token + l_split[idx] + "}"] + l_split[idx+1:]
            token_list.append(token)
            next_idx = idx + 1
    return l_split, next_idx, token_list