#!/usr/bin/env python3
"""
简化的LaTeX规范化测试脚本
只输出规范前后的对比，窗口简化
"""

import json
import sys
from pathlib import Path

# 导入新的规范化系统
try:
    from latex_normalizer import LaTeXNormalizer
    print("✓ 使用新的模块化规范化系统")
    USE_NEW_SYSTEM = True
except ImportError:
    # 降级到旧系统
    try:
        from normalize_optimized import normalize_latex_string
        print("✓ 使用 normalize_optimized.py")
        USE_NEW_SYSTEM = False
    except ImportError:
        try:
            from normalize import normalize_latex_string
            print("✓ 使用 normalize.py")
            USE_NEW_SYSTEM = False
        except ImportError:
            print("✗ 错误：无法导入任何规范化函数")
            sys.exit(1)


def load_samples(file_path, sample_count=50):
    """加载样本数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 均匀抽样
        keys = list(data.keys())
        if len(keys) <= sample_count:
            return data
        
        interval = len(keys) / sample_count
        sampled_keys = [keys[int(i * interval)] for i in range(sample_count)]
        
        return {key: data[key] for key in sampled_keys}
        
    except Exception as e:
        print(f"✗ 加载文件失败: {e}")
        return None


def normalize_latex(latex_input):
    """规范化LaTeX字符串"""
    if USE_NEW_SYSTEM:
        normalizer = LaTeXNormalizer()
        return normalizer.normalize(latex_input)
    else:
        return normalize_latex_string(latex_input)


def process_samples(samples):
    """处理样本并输出对比"""
    print("\n" + "=" * 80)
    print("LaTeX规范化对比结果")
    print("=" * 80)
    
    total_samples = len(samples)
    changed_count = 0
    error_count = 0
    
    for i, (filename, latex_input) in enumerate(samples.items(), 1):
        try:
            # 规范化处理
            latex_output = normalize_latex(latex_input)
            
            # 检查是否有变化
            has_changed = latex_input != latex_output
            if has_changed:
                changed_count += 1
            
            # 输出对比
            print(f"\n[{i}/{total_samples}] {filename}")
            print("─" * 60)
            print(f"规范前: {latex_input}")
            print(f"规范后: {latex_output}")
            
            if has_changed:
                print("状态: ✓ 已规范化")
                
                # 简单的改进分析
                length_change = len(latex_output) - len(latex_input)
                if length_change < 0:
                    print(f"      长度减少 {-length_change} 字符")
                elif length_change > 0:
                    print(f"      长度增加 {length_change} 字符")
                else:
                    print("      长度不变")
            else:
                print("状态: ─ 无变化")
            
        except Exception as e:
            error_count += 1
            print(f"\n[{i}/{total_samples}] {filename}")
            print("─" * 60)
            print(f"规范前: {latex_input}")
            print(f"错误: {e}")
            print("状态: ✗ 处理失败")
    
    # 输出统计信息
    print("\n" + "=" * 80)
    print("统计信息")
    print("=" * 80)
    print(f"总样本数: {total_samples}")
    print(f"成功处理: {total_samples - error_count} ({(total_samples-error_count)/total_samples*100:.1f}%)")
    print(f"发生变化: {changed_count} ({changed_count/total_samples*100:.1f}%)")
    print(f"处理失败: {error_count} ({error_count/total_samples*100:.1f}%)")


def test_specific_cases():
    """测试特定的问题案例"""
    print("\n" + "=" * 80)
    print("JavaScript节点处理改进测试")
    print("=" * 80)
    
    test_cases = [
        # 上下标问题测试
        ("上下标基础", "x_{a}^{b}"),
        ("上下标复杂", "x_{a+b}^{c+d}"),
        ("多层上下标", "x_{i_j}^{k^l}"),
        ("嵌套上下标", "y^{a^{b^c}}_{d_{e_f}}"),
        
        # 分数问题测试
        ("简单分数", "\\frac{a}{b}"),
        ("分数空格", "\\frac { a } { b }"),
        ("嵌套分数", "\\frac{\\frac{a}{b}}{\\frac{c}{d}}"),
        ("复杂分数", "\\frac{a+\\sqrt{b}}{c-\\sqrt{d}}"),
        
        # 函数问题测试
        ("operatorname", "\\operatorname{sin} x"),
        ("mathrm函数", "\\mathrm{cos} y"),
        ("复杂函数", "\\operatorname{sin}(\\operatorname{cos}(x))"),
        ("函数组合", "\\operatorname{log}(\\operatorname{exp}(y))"),
        
        # 环境问题测试
        ("pmatrix", "\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}"),
        ("align环境", "\\begin{align} x &= y \\\\ a &= b \\end{align}"),
        ("cases环境", "f(x) = \\begin{cases} x^2 & x > 0 \\\\ -x & x \\leq 0 \\end{cases}"),
        
        # 空格和间距测试
        ("多余空格", "\\frac { a } { b } + ( x )"),
        ("间距命令", "a \\quad b \\, c \\; d"),
        ("运算符空格", "a + b = c"),
        
        # 根号和重音测试
        ("根号", "\\sqrt{x}"),
        ("复杂根号", "\\sqrt{\\frac{a}{b}}"),
        ("重音符号", "\\hat{x} + \\tilde{y}"),
        ("重音组合", "\\hat{\\bar{x}}"),
        
        # 字体测试
        ("mathrm", "\\mathrm{text}"),
        ("mathbf", "\\mathbf{bold}"),
        ("text模式", "\\text{some text}"),
        
        # 复杂组合测试
        ("综合测试1", "\\frac{\\operatorname{sin}(x)}{\\operatorname{cos}(y)} + \\sqrt{z}"),
        ("综合测试2", "x_{\\frac{a}{b}}^{\\sqrt{c}} + \\hat{y}"),
        ("综合测试3", "\\begin{pmatrix} \\frac{a}{b} & \\operatorname{sin}(x) \\\\ \\sqrt{y} & z^2 \\end{pmatrix}"),
    ]
    
    changed_count = 0
    error_count = 0
    
    for desc, latex_input in test_cases:
        try:
            latex_output = normalize_latex(latex_input)
            has_changed = latex_input != latex_output
            
            if has_changed:
                changed_count += 1
            
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  规范后: {latex_output}")
            print(f"  状态: {'✓ 已规范化' if has_changed else '─ 无变化'}")
            
        except Exception as e:
            error_count += 1
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  错误: {e}")
            print(f"  状态: ✗ 处理失败")
    
    print(f"\n特定案例统计:")
    print(f"  总测试: {len(test_cases)}")
    print(f"  规范化: {changed_count}")
    print(f"  失败: {error_count}")


def main():
    """主函数"""
    print("LaTeX规范化测试工具 (简化版)")
    print("专注于JavaScript AST节点处理改进验证")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        labels_file = sys.argv[1]
        sample_count = int(sys.argv[2]) if len(sys.argv) > 2 else 20
        
        # 检查文件是否存在
        if Path(labels_file).exists():
            print(f"\n从 {labels_file} 加载 {sample_count} 个样本...")
            samples = load_samples(labels_file, sample_count)
            
            if samples:
                process_samples(samples)
            else:
                print("样本加载失败，运行特定案例测试...")
                test_specific_cases()
        else:
            print(f"文件 {labels_file} 不存在，运行特定案例测试...")
            test_specific_cases()
    else:
        print("\n运行特定案例测试...")
        test_specific_cases()


if __name__ == "__main__":
    main()
