#!/usr/bin/env python3
"""
调试测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from normalize_optimized import tokenize_latex

def debug_tokenize():
    """调试tokenize过程"""
    test_string = r'\mathrm{sin} x'

    print(f"输入: {test_string}")

    # 步骤1: JavaScript处理
    success, js_result = tokenize_latex(test_string)
    print(f"JavaScript输出: '{js_result}'")

    # 步骤2: Python normalize处理
    from normalize_optimized import normalize_latex
    python_result = normalize_latex(js_result, False)
    print(f"Python normalize结果: '{python_result}'")

    # 检查实际的字符
    print("实际字符分析:")
    for i, char in enumerate(python_result):
        print(f"  {i}: '{char}' (ASCII: {ord(char)})")

    # 步骤3: 测试我的函数
    from normalize_optimized import unified_math_function_processing
    test_result = unified_math_function_processing(python_result)
    print(f"我的函数处理后: '{test_result}'")

if __name__ == "__main__":
    debug_tokenize()
