#!/usr/bin/env python3
"""
测试字体命令和aligned环境的修复效果
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from latex_normalizer import LaTeXNormalizer


def test_font_command_fixes():
    """测试字体命令修复"""
    print("=" * 80)
    print("字体命令修复测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        # \bf命令测试
        ("\\bf基础", "{\\bf K}"),
        ("\\bf范围", "{\\bf K}_{\\chi,\\sigma}"),
        ("\\bf复杂", "{\\bf K}_{\\chi,\\sigma}(W)"),
        
        # 其他老式字体命令
        ("\\rm命令", "{\\rm text}"),
        ("\\it命令", "{\\it italic}"),
        ("\\sf命令", "{\\sf sans}"),
        
        # 混合测试
        ("混合字体", "{\\bf K}_{\\rm text}"),
        ("复杂表达式", "{\\bf K}_{\\chi,\\sigma}(W):=\\bigcap"),
        
        # 原问题样本
        ("原问题样本", "\\begin{align*}{\\bf K}_{\\chi,\\sigma}(W):=\\bigcap\\limits_{w\\in W} w\\tilde {\\bf D}_{\\chi,\\sigma}(W)w^{-1}\\ .\\end{align*}"),
    ]
    
    for desc, latex_input in test_cases:
        try:
            latex_output = normalizer.normalize(latex_input)
            has_changed = latex_input != latex_output
            
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  规范后: {latex_output}")
            print(f"  状态: {'✓ 已规范化' if has_changed else '─ 无变化'}")
            
            # 检查特定问题
            if "\\bf" in latex_input:
                if "\\mathbf{" in latex_output:
                    print("  ✓ \\bf正确转换为\\mathbf{}")
                else:
                    print("  ⚠️  问题: \\bf未正确转换")
                
                # 检查花括号匹配
                open_braces = latex_output.count('{')
                close_braces = latex_output.count('}')
                if open_braces == close_braces:
                    print("  ✓ 花括号匹配正确")
                else:
                    print(f"  ⚠️  问题: 花括号不匹配 ({open_braces}个开括号, {close_braces}个闭括号)")
            
        except Exception as e:
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  错误: {e}")
            print(f"  状态: ✗ 处理失败")


def test_aligned_environment_fixes():
    """测试aligned环境修复"""
    print("\n" + "=" * 80)
    print("Aligned环境修复测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        # aligned环境测试
        ("简单aligned", "\\begin{align} x &= y \\\\ a &= b \\end{align}"),
        ("align*环境", "\\begin{align*} x &= y \\\\ a &= b \\end{align*}"),
        ("复杂aligned", "\\begin{align*} {\\bf K}_{\\chi,\\sigma}(W) &:= \\bigcap_{w\\in W} w \\end{align*}"),
        
        # 原问题样本
        ("原问题完整", "\\begin{align*}{\\bf K}_{\\chi,\\sigma}(W):=\\bigcap\\limits_{w\\in W} w\\tilde {\\bf D}_{\\chi,\\sigma}(W)w^{-1}\\ .\\end{align*}"),
    ]
    
    for desc, latex_input in test_cases:
        try:
            latex_output = normalizer.normalize(latex_input)
            has_changed = latex_input != latex_output
            
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  规范后: {latex_output}")
            print(f"  状态: {'✓ 已规范化' if has_changed else '─ 无变化'}")
            
            # 检查特定问题
            if "align" in latex_input:
                if "\\begin{aligned}" in latex_output and "\\end{aligned}" in latex_output:
                    print("  ✓ align正确转换为aligned")
                else:
                    print("  ⚠️  问题: align未正确转换为aligned")
                
                # 检查是否有不必要的花括号包装
                if "\\begin{aligned}{" in latex_output and "}\\end{aligned}" in latex_output:
                    print("  ⚠️  问题: aligned环境被错误包装在花括号中")
                else:
                    print("  ✓ aligned环境没有被错误包装")
                
                # 检查换行符
                if "\\\\" in latex_output:
                    print("  ✓ 保留了换行符")
                else:
                    print("  ⚠️  问题: 换行符丢失")
            
        except Exception as e:
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  错误: {e}")
            print(f"  状态: ✗ 处理失败")


def test_specific_problem():
    """测试原始问题样本"""
    print("\n" + "=" * 80)
    print("原始问题样本专项测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    # 原始问题样本
    problem_sample = "\\begin{align*}{\\bf K}_{\\chi,\\sigma}(W):=\\bigcap\\limits_{w\\in W} w\\tilde {\\bf D}_{\\chi,\\sigma}(W)w^{-1}\\ .\\end{align*}"
    
    print("原始问题样本:")
    print(f"输入: {problem_sample}")
    
    try:
        result = normalizer.normalize(problem_sample)
        print(f"输出: {result}")
        
        # 详细检查
        print("\n详细检查:")
        
        # 1. 检查\\bf转换
        if "\\mathbf{K}" in result:
            print("✓ 第一个\\bf K正确转换")
        else:
            print("✗ 第一个\\bf K转换有问题")
        
        if "\\mathbf{D}" in result:
            print("✓ 第二个\\bf D正确转换")
        else:
            print("✗ 第二个\\bf D转换有问题")
        
        # 2. 检查花括号匹配
        open_braces = result.count('{')
        close_braces = result.count('}')
        print(f"花括号统计: {open_braces}个开括号, {close_braces}个闭括号")
        if open_braces == close_braces:
            print("✓ 花括号匹配")
        else:
            print("✗ 花括号不匹配")
        
        # 3. 检查aligned环境
        if "\\begin{aligned}" in result and "\\end{aligned}" in result:
            print("✓ 正确转换为aligned环境")
        else:
            print("✗ aligned环境转换有问题")
        
        # 4. 检查是否有错误的花括号包装
        if "\\begin{aligned}{" in result:
            print("✗ aligned环境被错误包装")
        else:
            print("✓ aligned环境没有被错误包装")
        
        # 5. 检查换行符
        if "\\\\" in result:
            print("✓ 保留了换行符")
        else:
            print("✗ 换行符丢失")
        
    except Exception as e:
        print(f"处理失败: {e}")


if __name__ == "__main__":
    test_font_command_fixes()
    test_aligned_environment_fixes()
    test_specific_problem()
