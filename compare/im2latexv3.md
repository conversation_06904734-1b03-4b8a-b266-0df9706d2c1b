# LaTeX预处理规范化规则文档

本文档详细记录了 `preprocess_formulas.py` 和 `preprocess_latex.js` 两个文件中的LaTeX规范化规则，按照代码逐行分析。

## 1. preprocess_formulas.py 分析

### 1.1 文件头部和导入 (第1-4行)
```python
#!/usr/bin/env python
# tokenize latex formulas
import sys, os, argparse, logging, subprocess, shutil
```
- 第1行：Python解释器路径声明
- 第2行：说明文档 - 用于tokenize latex formulas
- 第4行：导入必要模块用于文件操作、参数解析、日志记录、子进程调用和文件操作

### 1.2 ASCII检查函数 (第6-11行)
```python
def is_ascii(str):
    try:
        str.decode('ascii')
        return True
    except UnicodeError:
        return False
```
**规则1：ASCII字符检查**
- 检查字符串是否为ASCII编码
- 用于后续的字符过滤（虽然在当前版本中被注释掉）

### 1.3 参数处理 (第14-36行)
支持两种处理模式：
- `tokenize`：将LaTeX分割成以空格分隔的标记
- `normalize`：进一步转换为等价的标准形式

### 1.4 主要预处理规则 (第38-93行)

#### 规则2：hskip到hspace转换 (第53行)
```bash
perl -pe 's|hskip(.*?)(cm\|in\|pt\|mm\|em)|hspace{\1\2}|g'
```
- 将 `\hskip<length><unit>` 转换为 `\hspace{<length><unit>}`
- 支持的单位：cm, in, pt, mm, em

#### 规则3：回车符清理 (第57-60行)
```python
fout.write(line.replace('\r', ' ').strip() + '\n')
```
- 将回车符 `\r` 替换为空格
- 去除行首行尾空白字符

#### 规则4：调用JavaScript处理器 (第64行)
```bash
cat file | node preprocess_latex.js mode > output
```
- 通过管道将文本传递给JavaScript处理器
- 根据模式（tokenize/normalize）进行不同处理

#### 规则5：最终token过滤 (第69-78行)
- 当前版本：所有token → 保留
- 历史版本（已注释）：非ASCII token → 过滤掉

#### 规则6：全局变量重置 (第55-56行)
- 每行处理完成 → `global_str = ""` 和 `norm_str = ""`

## 2. preprocess_latex.js 分析

### 2.1 模块导入和读取设置 (第1-8行)
```javascript
var katex = require("./third_party/katex/katex.js")
options = require("./third_party/katex/src/Options.js")
var readline = require('readline');
```
- 导入KaTeX解析器和选项模块
- 设置标准输入读取接口

### 2.2 逐行处理入口 (第10-56行)

#### 规则7：注释处理 (第12-14行)
- 行首 `%` → 移除首个 `%` 字符
- 行内 `%` → 截取 `%` 之前的内容

#### 规则8：特殊符号替换 (第16-22行)
- `\~` → 空格
- `\>` → 空格（重复300次确保完全替换）
- `$` → 空格
- `\label{...}` → 删除

#### 规则9：条件性双反斜杠处理 (第24-29行)
- 条件：不包含 `matrix`、`cases`、`array`、`begin`
- 符合条件时：`\\` → `\,`（换行符转换为小间距）

#### 规则10：行尾空格添加 (第31行)
- 每行末尾 → 添加一个空格字符

#### 规则11：rm命令标准化 (第39-43行)
- `{\rm` → `\mathrm{`
- `{ \rm` → `\mathrm{`
- `\rm{` → `\mathrm{`

#### 规则12：美元符号恢复 (第46-49行)
- 特殊标记 `SSSSSS` → `$`
- 特殊标记 ` S S S S S S` → `$`

#### 规则13：模式差异处理
**Tokenize模式**：
- 解析后输出 `global_str`（由KaTeX内部构建的token序列）
- 最终去除 `\label { ... }` 模式

**Normalize模式**：
- 解析后通过AST重建输出 `norm_str`
- 最终去除 `\label { ... }` 模式

### 2.3 AST节点类型处理规则 (第65-392行)

#### 规则14：数学序号处理 (mathord, 第67-77行)
- `mathrm` 字体空格 → 字符 + `\;`
- `mathrm` 字体其他字符 → 字符 + 空格
- 其他字体 → 值 + 空格

#### 规则15：基本节点类型 (第79-103行)
- `textord` → 值 + 空格
- `bin`（二元运算符） → 值 + 空格  
- `rel`（关系运算符） → 值 + 空格
- `open`（开括号） → 值 + 空格
- `close`（闭括号） → 值 + 空格
- `inner`（内部元素） → 值 + 空格
- `punct`（标点符号） → 值 + 空格

#### 规则16：组合处理 (ordgroup, 第105-111行)
- 组合内容 → `{ 组合内容 }`

#### 规则17：文本处理 (text, 第113-118行)  
- 文本节点 → `\mathrm { 文本内容 }`

#### 规则18：颜色处理 (color, 第120-127行)
- 颜色节点 → 返回MathML节点（不修改输出字符串）

#### 规则19：上下标处理 (supsub, 第129-153行)
- 下标（非ordgroup类型） → `基础 _ { 下标内容 }`
- 下标（ordgroup类型） → `基础 _ 下标内容`
- 上标（非ordgroup类型） → `基础 ^ { 上标内容 }`
- 上标（ordgroup类型） → `基础 ^ 上标内容`

#### 规则20：分数处理 (genfrac, 第155-163行)
- 无分数线的分数 → `\binom 分子 分母`
- 有分数线的分数 → `\frac 分子 分母`

#### 规则21：数组/矩阵处理 (array, 第165-194行)
- 数组环境 → `\begin{环境名} {列定义} 内容 \end{环境名}`
- 未指定列定义 → 自动添加 `c`（居中对齐）
- 行首 `\hline` → 保留并移至正确位置
- 单元格分隔 → 添加 `& `
- 行结束 → 添加 `\\ `

#### 规则22：开方处理 (sqrt, 第196-204行)
- 带指数的根号 → `\sqrt [ 指数 ] 被开方数`
- 普通根号 → `\sqrt 被开方数`

#### 规则23：左右分隔符处理 (leftright, 第206-212行)
- 可变大小分隔符 → `\left左分隔符 内容 \right右分隔符`

#### 规则24：重音符号处理 (accent, 第214-223行)
- 重音（非ordgroup） → `重音符号 { 基础内容 }`
- 重音（ordgroup） → `重音符号 基础内容`

#### 规则25：间距处理 (spacing, 第225-233行)
- 空格字符 → `~`
- 其他间距命令 → 保持原值

#### 规则26：操作符处理 (op, 第235-253行)
- 符号操作符 → 直接输出符号
- 函数操作符（无限制） → `\operatorname { 函数名 }`
- 函数操作符（有限制） → `\operatorname* { 函数名 }`

#### 规则27：KaTeX标记处理 (katex, 第255-259行)
- KaTeX标记 → 返回MathML节点（不修改输出字符串）

#### 规则28：字体处理 (font, 第262-270行)
- `\mbox` 或 `\hbox` → `\mathrm`
- 其他字体命令 → `\字体名 内容`

#### 规则29：分隔符大小处理 (delimsizing, 第272-275行)
- 分隔符大小命令 → `函数名 分隔符值`

#### 规则30：样式处理 (styling, 第277-281行)
- 样式命令 → `原始命令 内容`

#### 规则31：尺寸处理 (sizing, 第280-291行)
- `\rm` 命令 → `\mathrm { 内容 }`
- 其他尺寸命令 → `原始命令 内容`

#### 规则32：上划线和下划线处理 (第293-309行)
- 上划线 → `\overline { 内容 }`
- 下划线 → `\underline { 内容 }`

#### 规则33：规则线处理 (rule, 第311-313行)
- 规则线 → `\rule { 宽度数值 宽度单位 } { 高度数值 高度单位 }`

#### 规则34：左右重叠处理 (llap/rlap, 第315-324行)
- 左重叠 → `\llap 内容`
- 右重叠 → `\rlap 内容`

#### 规则35：幻影处理 (phantom, 第326-331行)
- 幻影元素 → `\phantom { 内容 }`

#### 规则36：错误处理机制 (第50-54行)
- 解析异常 → 输出原始行、norm_str、错误信息到stderr
- 错误标记 → 输出 `"########--------"`
- 继续处理 → 不中断整体流程

## 3. 处理流程总结

### 3.1 整体处理流程
1. **Perl预处理**：`hskip` → `hspace` 转换
2. **字符清理**：删除 `\r` 字符
3. **JavaScript核心处理**：
   - 注释和特殊符号处理
   - 条件性双反斜杠转换
   - rm命令标准化
   - AST解析和重建
4. **最终过滤**：token级别的清理

### 3.2 主要规范化原则
1. **统一字体命令**：`\rm`, `\mbox`, `\hbox` → `\mathrm`
2. **标准化间距**：`\hskip` → `\hspace`, `\\` → `\,` (特定条件下)
3. **规范化结构**：所有复杂结构都用标准LaTeX命令重写
4. **添加空格**：在大多数元素后添加空格以便tokenization
5. **括号规范化**：确保上下标、重音符号等正确使用大括号

### 3.3 特殊处理规则
1. **条件处理**：矩阵相关环境中保留 `\\`
2. **字体上下文**：在特定字体环境中添加额外间距
3. **操作符区分**：区分符号操作符和命名操作符
4. **错误处理**：捕获解析错误并记录到stderr

这套规则系统确保了LaTeX公式的标准化和一致性，便于后续的处理和分析。 