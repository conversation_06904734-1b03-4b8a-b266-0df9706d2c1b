"""
Token规范化器

实现FR3需求：Token规范化
- 函数名称统一：统一为Token数量最少的版本
- 同义符号替换：基于外部配置文件的同义词替换
"""

import re
from typing import Dict, List, Tuple, Set

from ..config.config_manager import ConfigManager
from ..utils.helpers import get_math_function_commands, count_token_complexity


class TokenNormalizer:
    """
    Token规范化器
    
    将渲染效果一致但源码写法不同的Token统一为唯一的标准形式。
    """
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化Token规范化器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.math_functions = get_math_function_commands()
        
        # 函数名称的不同表示形式，按复杂度排序（简单到复杂）
        self.function_representations = {
            'sin': ['\\sin', '\\mathrm{sin}', '\\operatorname{sin}'],
            'cos': ['\\cos', '\\mathrm{cos}', '\\operatorname{cos}'],
            'tan': ['\\tan', '\\mathrm{tan}', '\\operatorname{tan}'],
            'cot': ['\\cot', '\\mathrm{cot}', '\\operatorname{cot}'],
            'sec': ['\\sec', '\\mathrm{sec}', '\\operatorname{sec}'],
            'csc': ['\\csc', '\\mathrm{csc}', '\\operatorname{csc}'],
            'sinh': ['\\sinh', '\\mathrm{sinh}', '\\operatorname{sinh}'],
            'cosh': ['\\cosh', '\\mathrm{cosh}', '\\operatorname{cosh}'],
            'tanh': ['\\tanh', '\\mathrm{tanh}', '\\operatorname{tanh}'],
            'coth': ['\\coth', '\\mathrm{coth}', '\\operatorname{coth}'],
            'arcsin': ['\\arcsin', '\\mathrm{arcsin}', '\\operatorname{arcsin}'],
            'arccos': ['\\arccos', '\\mathrm{arccos}', '\\operatorname{arccos}'],
            'arctan': ['\\arctan', '\\mathrm{arctan}', '\\operatorname{arctan}'],
            'exp': ['\\exp', '\\mathrm{exp}', '\\operatorname{exp}'],
            'ln': ['\\ln', '\\mathrm{ln}', '\\operatorname{ln}'],
            'log': ['\\log', '\\mathrm{log}', '\\operatorname{log}'],
            'lg': ['\\lg', '\\mathrm{lg}', '\\operatorname{lg}'],
            'min': ['\\min', '\\mathrm{min}', '\\operatorname{min}'],
            'max': ['\\max', '\\mathrm{max}', '\\operatorname{max}'],
            'det': ['\\det', '\\mathrm{det}', '\\operatorname{det}'],
            'lim': ['\\lim', '\\mathrm{lim}', '\\operatorname{lim}']
        }
    
    def normalize(self, latex_code: str) -> str:
        """
        执行Token规范化
        
        Args:
            latex_code: 输入的LaTeX代码
            
        Returns:
            规范化后的LaTeX代码
        """
        if not latex_code:
            return latex_code
        
        result = latex_code
        
        # 第一阶段：函数名称统一
        result = self._normalize_function_names(result)
        
        # 第二阶段：同义符号替换
        result, _ = self._replace_synonym_tokens(result)
        
        return result
    
    def _normalize_function_names(self, text: str) -> str:
        """
        统一函数名称为Token数量最少的版本
        
        Args:
            text: 输入文本
            
        Returns:
            处理后的文本
        """
        result = text
        
        # 对每个函数，将复杂形式替换为简单形式
        for func_name, representations in self.function_representations.items():
            # 按复杂度降序排序，先替换复杂的形式
            sorted_representations = sorted(representations, 
                                          key=count_token_complexity, reverse=True)
            
            target_form = sorted_representations[-1]  # 最简单的形式
            
            # 替换所有复杂形式
            for complex_form in sorted_representations[:-1]:
                if complex_form in result:
                    result = result.replace(complex_form, target_form)
        
        return result
    
    def _replace_synonym_tokens(self, text: str) -> Tuple[str, int]:
        """
        基于配置文件替换同义词Token
        
        Args:
            text: 输入文本
            
        Returns:
            (替换后的文本, 替换次数)
        """
        return self.config_manager.replace_tokens_in_text(text)
    
    def extract_function_tokens(self, text: str) -> List[str]:
        """
        提取文本中的函数Token
        
        Args:
            text: 输入文本
            
        Returns:
            函数Token列表
        """
        function_tokens = []
        
        # 提取所有可能的函数表示
        for func_name, representations in self.function_representations.items():
            for repr_form in representations:
                if repr_form in text:
                    function_tokens.append(repr_form)
        
        return function_tokens
    
    def analyze_token_complexity(self, text: str) -> Dict[str, any]:
        """
        分析文本中Token的复杂度
        
        Args:
            text: 输入文本
            
        Returns:
            复杂度分析结果
        """
        function_tokens = self.extract_function_tokens(text)
        
        analysis = {
            'total_function_tokens': len(function_tokens),
            'complexity_distribution': {},
            'simplification_potential': 0,
            'token_details': []
        }
        
        for token in function_tokens:
            complexity = count_token_complexity(token)
            
            if complexity in analysis['complexity_distribution']:
                analysis['complexity_distribution'][complexity] += 1
            else:
                analysis['complexity_distribution'][complexity] = 1
            
            # 检查是否可以简化
            for func_name, representations in self.function_representations.items():
                if token in representations:
                    simplest_form = min(representations, key=count_token_complexity)
                    if token != simplest_form:
                        analysis['simplification_potential'] += 1
                    
                    analysis['token_details'].append({
                        'token': token,
                        'function': func_name,
                        'complexity': complexity,
                        'simplest_form': simplest_form,
                        'can_simplify': token != simplest_form
                    })
                    break
        
        return analysis
    
    def preview_normalization(self, text: str) -> Dict[str, any]:
        """
        预览Token规范化效果
        
        Args:
            text: 输入文本
            
        Returns:
            预览信息字典
        """
        original_tokens = self.extract_function_tokens(text)
        normalized_text = self.normalize(text)
        normalized_tokens = self.extract_function_tokens(normalized_text)
        
        # 分析同义词替换
        synonym_result, synonym_count = self.config_manager.replace_tokens_in_text(text)
        
        changes = []
        
        # 检测函数名称变化
        for func_name, representations in self.function_representations.items():
            for complex_form in representations[1:]:  # 跳过最简单的形式
                if complex_form in text and complex_form not in normalized_text:
                    simple_form = representations[0]
                    changes.append({
                        'type': 'function_simplification',
                        'original': complex_form,
                        'normalized': simple_form,
                        'function': func_name
                    })
        
        return {
            'original_function_tokens': len(original_tokens),
            'normalized_function_tokens': len(normalized_tokens),
            'synonym_replacements': synonym_count,
            'changes': changes,
            'original_text': text,
            'normalized_text': normalized_text,
            'complexity_reduction': self._calculate_complexity_reduction(text, normalized_text)
        }
    
    def _calculate_complexity_reduction(self, original: str, normalized: str) -> Dict[str, int]:
        """
        计算复杂度减少量
        
        Args:
            original: 原始文本
            normalized: 规范化后的文本
            
        Returns:
            复杂度减少统计
        """
        original_tokens = self.extract_function_tokens(original)
        normalized_tokens = self.extract_function_tokens(normalized)
        
        original_complexity = sum(count_token_complexity(token) for token in original_tokens)
        normalized_complexity = sum(count_token_complexity(token) for token in normalized_tokens)
        
        return {
            'original_complexity': original_complexity,
            'normalized_complexity': normalized_complexity,
            'complexity_reduction': original_complexity - normalized_complexity,
            'reduction_percentage': round((original_complexity - normalized_complexity) / max(original_complexity, 1) * 100, 2)
        }
    
    def get_synonym_statistics(self) -> Dict[str, any]:
        """
        获取同义词配置统计信息
        
        Returns:
            同义词统计信息
        """
        return self.config_manager.get_config_info()
    
    def validate_configuration(self) -> Tuple[bool, List[str]]:
        """
        验证Token规范化配置
        
        Returns:
            (是否有效, 错误信息列表)
        """
        return self.config_manager.validate_config()
