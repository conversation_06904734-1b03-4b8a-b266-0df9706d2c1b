"""
AST处理器

基于KaTeX的抽象语法树处理器，复用现有的JavaScript AST处理逻辑。
提供高质量的LaTeX解析和规范化功能。
"""

import os
import re
import subprocess
import tempfile
from pathlib import Path
from typing import Tuple, Optional

from ..utils.helpers import clean_whitespace


class ASTProcessor:
    """
    基于AST的LaTeX处理器
    
    使用KaTeX JavaScript库进行AST解析和处理，确保高质量的LaTeX规范化。
    """
    
    def __init__(self, js_script_dir: Optional[Path] = None):
        """
        初始化AST处理器
        
        Args:
            js_script_dir: JavaScript脚本目录，默认为项目根目录下的js_scripts
        """
        if js_script_dir is None:
            # 从当前文件位置推导项目根目录
            current_dir = Path(__file__).parent.parent.parent
            js_script_dir = current_dir / "js_scripts"
        
        self.js_script_dir = Path(js_script_dir)
        self.js_script_path = self.js_script_dir / "preprocess_formula.js"
        
        # 验证JavaScript脚本是否存在
        if not self.js_script_path.exists():
            print(f"警告: JavaScript脚本不存在: {self.js_script_path}")
    
    def process_latex(self, latex_code: str, mode: str = "normalize") -> Tuple[bool, str]:
        """
        使用AST处理LaTeX代码
        
        Args:
            latex_code: 原始LaTeX字符串
            mode: 处理模式，"tokenize" 或 "normalize"
            
        Returns:
            (是否成功, 处理后的LaTeX字符串)
        """
        if not latex_code or not latex_code.strip():
            return True, latex_code
        
        # 预处理：环境统一
        preprocessed_latex = self._preprocess_environments(latex_code)
        
        # 如果JavaScript脚本不存在，返回预处理结果
        if not self.js_script_path.exists():
            return True, preprocessed_latex
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', 
                                           delete=False, encoding='utf-8') as temp_file:
                temp_filename = temp_file.name
                temp_file.write(preprocessed_latex)
            
            # 调用JavaScript处理器
            success, result = self._call_javascript_processor(temp_filename, mode)
            
            if success:
                # 后处理：操作符名称规范化
                result = self._postprocess_operators(result)
                return True, result
            else:
                return True, preprocessed_latex
                
        except Exception as e:
            print(f"AST处理出错: {e}")
            return True, preprocessed_latex
        finally:
            # 清理临时文件
            try:
                if 'temp_filename' in locals():
                    os.unlink(temp_filename)
            except:
                pass
    
    def _preprocess_environments(self, latex_code: str) -> str:
        """
        预处理：环境统一
        
        将不支持的环境转换为KaTeX支持的环境
        
        Args:
            latex_code: 原始LaTeX代码
            
        Returns:
            预处理后的LaTeX代码
        """
        result = latex_code
        
        # KaTeX兼容性：将align*等环境转换为aligned
        result = re.sub(r'\\begin{align\*?}', r'\\begin{aligned}', result)
        result = re.sub(r'\\end{align\*?}', r'\\end{aligned}', result)
        result = re.sub(r'\\begin{(split|alignedat|alignat|eqnarray)\*?}', 
                       r'\\begin{aligned}', result)
        result = re.sub(r'\\end{(split|alignedat|alignat|eqnarray)\*?}', 
                       r'\\end{aligned}', result)
        
        # 将smallmatrix统一为matrix
        result = re.sub(r'\\begin{(smallmatrix)\*?}(.+?)\\end{\1\*?}',
                       r'\\begin{matrix}\2\\end{matrix}', result, flags=re.S)
        
        return result
    
    def _call_javascript_processor(self, temp_filename: str, mode: str) -> Tuple[bool, str]:
        """
        调用JavaScript处理器
        
        Args:
            temp_filename: 临时文件名
            mode: 处理模式
            
        Returns:
            (是否成功, 处理结果)
        """
        try:
            # 构建命令
            if os.name == 'nt':  # Windows
                cmd = f'type "{temp_filename}" | node "{self.js_script_path}" {mode}'
            else:  # Unix/Linux/Mac
                cmd = f'cat "{temp_filename}" | node "{self.js_script_path}" {mode}'
            
            # 执行命令
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                                  timeout=30, encoding='utf-8', errors='replace')
            
            if result.returncode != 0:
                print(f"JavaScript处理器执行失败: {result.stderr}")
                return False, ""
            
            # 处理输出
            output = result.stdout
            if output is None:
                return False, ""
            
            output = output.strip()
            if not output:
                return False, ""
            
            return True, output
            
        except subprocess.TimeoutExpired:
            print("JavaScript处理器执行超时")
            return False, ""
        except Exception as e:
            print(f"调用JavaScript处理器出错: {e}")
            return False, ""
    
    def _postprocess_operators(self, latex_code: str) -> str:
        """
        后处理：操作符名称规范化
        
        将\\operatorname转换为简化形式
        
        Args:
            latex_code: 处理后的LaTeX代码
            
        Returns:
            规范化后的LaTeX代码
        """
        result = latex_code
        
        # 定义操作符映射
        operators = [
            'arccos', 'arcsin', 'arctan', 'arg', 'cos', 'cosh', 'cot', 'coth', 
            'csc', 'deg', 'det', 'dim', 'exp', 'gcd', 'hom', 'inf', 'injlim', 
            'ker', 'lg', 'lim', 'liminf', 'limsup', 'ln', 'log', 'max', 'min', 
            'Pr', 'projlim', 'sec', 'sin', 'sinh', 'sup', 'tan', 'tanh'
        ]
        
        # 构建正则表达式
        operators_pattern = '|'.join(operators)
        ops_regex = re.compile(r'\\operatorname {(' + operators_pattern + ')}')
        
        # 替换operatorname为简化形式
        matches = re.findall(ops_regex, result)
        if matches:
            names = ['\\' + match.replace(' ', '') for match in matches]
            for i, match in enumerate(re.finditer(ops_regex, result)):
                if i < len(names):
                    result = result.replace(match.group(0), names[i], 1)
        
        # 清理特殊情况
        result = result.replace(r'\\ \end{array}', r'\end{array}')
        
        return result
    
    def is_available(self) -> bool:
        """
        检查AST处理器是否可用
        
        Returns:
            是否可用
        """
        return self.js_script_path.exists()
    
    def get_processor_info(self) -> dict:
        """
        获取处理器信息
        
        Returns:
            处理器信息字典
        """
        return {
            "js_script_dir": str(self.js_script_dir),
            "js_script_path": str(self.js_script_path),
            "script_exists": self.js_script_path.exists(),
            "is_available": self.is_available()
        }
