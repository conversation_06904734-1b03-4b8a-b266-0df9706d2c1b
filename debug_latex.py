#!/usr/bin/env python3
"""
调试LaTeX处理过程中的"misplaced &"问题
"""

import re

def test_latex_processing():
    """测试LaTeX处理过程"""
    
    # 测试关键部分
    test_string = r'\\ &= \frac{1}{{K + {N_{{\rm{CP}}}}}}\sum\limits_{k = 0}^{K - 1} {{{\log }_2}\left( {1 + {\gamma _k}} \right)}.'
    
    print("=== 测试字符串 ===")
    print(f"原始: {test_string}")
    print()
    
    # 模拟空格处理过程
    result = test_string
    
    # 保护LaTeX换行符
    result = result.replace('\\\\', '__LATEX_NEWLINE__')
    print(f"保护换行符后: {result}")
    print()
    
    # 清理运算符空格
    operators = ['+', '-', '=', '<', '>', '*', '/', '^', '_']
    for op in operators:
        result = re.sub(f'\\s*{re.escape(op)}\\s*', op, result)
    
    print(f"清理运算符空格后: {result}")
    print()
    
    # 恢复换行符
    result = result.replace('__LATEX_NEWLINE__', '\\\\')
    
    print(f"最终结果: {result}")
    print()
    
    # 检查问题
    print("=== 问题分析 ===")
    if '\\ &=' in result:
        print("❌ 发现问题: '\\ &=' 模式存在")
        print("   这会导致 'misplaced &' 错误")
    else:
        print("✅ 未发现 '\\ &=' 模式")
    
    if '\\\\&=' in result:
        print("✅ 发现正确的 '\\\\&=' 模式")
    else:
        print("❌ 未发现正确的 '\\\\&=' 模式")

if __name__ == "__main__":
    test_latex_processing() 