"""
LaTeX规范化器主入口

集成所有规范化模块，提供统一的LaTeX规范化接口。
实现完整的规范化流程：AST处理 → 空格规范化 → 结构规范化 → Token规范化
"""

from pathlib import Path
from typing import Dict, Optional, Tuple

from .core.ast_processor import ASTProcessor
from .core.tokenizer import LaTeXTokenizer
from .normalizers.spacing_normalizer import SpacingNormalizer
from .normalizers.structure_normalizer import StructureNormalizer
from .normalizers.token_normalizer import TokenNormalizer
from .config.config_manager import ConfigManager
from .utils.helpers import validate_latex_syntax, clean_whitespace


class LaTeXNormalizer:
    """
    LaTeX规范化器主类
    
    提供完整的LaTeX规范化功能，集成所有子模块。
    """
    
    def __init__(self, config_dir: Optional[Path] = None, js_script_dir: Optional[Path] = None):
        """
        初始化LaTeX规范化器
        
        Args:
            config_dir: 配置文件目录
            js_script_dir: JavaScript脚本目录
        """
        # 初始化配置管理器
        self.config_manager = ConfigManager(config_dir)
        
        # 初始化核心处理器
        self.ast_processor = ASTProcessor(js_script_dir)
        self.tokenizer = LaTeXTokenizer()
        
        # 初始化规范化器
        self.spacing_normalizer = SpacingNormalizer()
        self.structure_normalizer = StructureNormalizer()
        self.token_normalizer = TokenNormalizer(self.config_manager)
        
        # 处理选项
        self.options = {
            'enable_ast_processing': True,
            'enable_spacing_normalization': True,
            'enable_structure_normalization': True,
            'enable_token_normalization': True,
            'validate_input': True,
            'validate_output': True
        }
    
    def normalize(self, latex_code: str, **options) -> str:
        """
        执行完整的LaTeX规范化
        
        Args:
            latex_code: 输入的LaTeX代码
            **options: 规范化选项
            
        Returns:
            规范化后的LaTeX代码
        """
        if not latex_code or not latex_code.strip():
            return latex_code
        
        # 更新选项
        current_options = self.options.copy()
        current_options.update(options)
        
        result = latex_code
        
        try:
            # 第0阶段：输入验证
            if current_options.get('validate_input', True):
                is_valid, errors = validate_latex_syntax(result)
                if not is_valid:
                    print(f"警告: 输入LaTeX语法可能有问题: {errors}")
            
            # 第1阶段：AST处理
            if current_options.get('enable_ast_processing', True):
                success, ast_result = self.ast_processor.process_latex(result, "normalize")
                if success:
                    result = ast_result
            
            # 第2阶段：结构规范化
            if current_options.get('enable_structure_normalization', True):
                result = self.structure_normalizer.normalize(result)
            
            # 第3阶段：Token规范化
            if current_options.get('enable_token_normalization', True):
                result = self.token_normalizer.normalize(result)
            
            # 第4阶段：空格规范化
            if current_options.get('enable_spacing_normalization', True):
                result = self.spacing_normalizer.normalize(result)
            
            # 第5阶段：最终清理
            result = clean_whitespace(result)
            
            # 第6阶段：输出验证
            if current_options.get('validate_output', True):
                is_valid, errors = validate_latex_syntax(result)
                if not is_valid:
                    print(f"警告: 输出LaTeX语法可能有问题: {errors}")
            
            return result
            
        except Exception as e:
            print(f"规范化过程出错: {e}")
            return latex_code  # 返回原始代码
    
    def tokenize(self, latex_code: str) -> list:
        """
        对LaTeX代码进行tokenization
        
        Args:
            latex_code: 输入的LaTeX代码
            
        Returns:
            token列表
        """
        return self.tokenizer.tokenize(latex_code)
    
    def analyze(self, latex_code: str) -> Dict[str, any]:
        """
        分析LaTeX代码
        
        Args:
            latex_code: 输入的LaTeX代码
            
        Returns:
            分析结果字典
        """
        analysis = {
            'input_length': len(latex_code),
            'input_valid': True,
            'input_errors': [],
            'spacing_stats': {},
            'structure_stats': {},
            'token_stats': {},
            'normalization_preview': {}
        }
        
        try:
            # 输入验证
            is_valid, errors = validate_latex_syntax(latex_code)
            analysis['input_valid'] = is_valid
            analysis['input_errors'] = errors
            
            # 空格分析
            analysis['spacing_stats'] = self.spacing_normalizer.get_spacing_statistics(latex_code)
            
            # 结构分析
            analysis['structure_stats'] = self.structure_normalizer.get_environment_statistics(latex_code)
            
            # Token分析
            analysis['token_stats'] = self.token_normalizer.analyze_token_complexity(latex_code)
            
            # 规范化预览
            normalized = self.normalize(latex_code)
            analysis['normalization_preview'] = {
                'original': latex_code,
                'normalized': normalized,
                'length_change': len(normalized) - len(latex_code),
                'changed': latex_code != normalized
            }
            
        except Exception as e:
            analysis['error'] = str(e)
        
        return analysis
    
    def get_system_info(self) -> Dict[str, any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        return {
            'ast_processor': self.ast_processor.get_processor_info(),
            'config_manager': self.config_manager.get_config_info(),
            'options': self.options.copy(),
            'components': {
                'ast_processor_available': self.ast_processor.is_available(),
                'config_valid': self.config_manager.validate_config()[0],
                'spacing_normalizer': True,
                'structure_normalizer': True,
                'token_normalizer': True
            }
        }
    
    def set_options(self, **options) -> None:
        """
        设置规范化选项
        
        Args:
            **options: 选项字典
        """
        self.options.update(options)
    
    def get_options(self) -> Dict[str, any]:
        """
        获取当前选项
        
        Returns:
            选项字典
        """
        return self.options.copy()
    
    def validate_system(self) -> Tuple[bool, list]:
        """
        验证系统完整性
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查AST处理器
        if not self.ast_processor.is_available():
            errors.append("AST处理器不可用：JavaScript脚本未找到")
        
        # 检查配置
        config_valid, config_errors = self.config_manager.validate_config()
        if not config_valid:
            errors.extend([f"配置错误: {err}" for err in config_errors])
        
        # 检查Token规范化器配置
        token_valid, token_errors = self.token_normalizer.validate_configuration()
        if not token_valid:
            errors.extend([f"Token配置错误: {err}" for err in token_errors])
        
        return len(errors) == 0, errors


def main():
    """
    命令行入口函数
    """
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python -m latex_normalizer.main <latex_string>")
        print("示例: python -m latex_normalizer.main '\\\\frac {a} {b}'")
        return
    
    latex_input = sys.argv[1]
    
    # 创建规范化器
    normalizer = LaTeXNormalizer()
    
    # 验证系统
    is_valid, errors = normalizer.validate_system()
    if not is_valid:
        print("系统验证失败:")
        for error in errors:
            print(f"  - {error}")
        print()
    
    # 执行规范化
    print("=" * 60)
    print("LaTeX规范化结果")
    print("=" * 60)
    print(f"输入: {latex_input}")
    
    result = normalizer.normalize(latex_input)
    print(f"输出: {result}")
    
    # 显示分析信息
    analysis = normalizer.analyze(latex_input)
    print(f"长度变化: {analysis['normalization_preview']['length_change']}")
    print(f"是否改变: {analysis['normalization_preview']['changed']}")
    print("=" * 60)


if __name__ == "__main__":
    main()
