#!/usr/bin/env python3
"""
测试环境处理修复效果
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from latex_normalizer import LaTeXNormalizer


def test_environment_fixes():
    """测试环境处理修复"""
    print("=" * 80)
    print("环境处理修复测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        # 矩阵环境测试
        ("pmatrix基础", "\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}"),
        ("bmatrix基础", "\\begin{bmatrix} 1 & 2 \\\\ 3 & 4 \\end{bmatrix}"),
        ("matrix基础", "\\begin{matrix} x & y \\\\ z & w \\end{matrix}"),
        ("vmatrix基础", "\\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix}"),
        
        # 对齐环境测试
        ("align环境", "\\begin{align} x &= y \\\\ a &= b \\end{align}"),
        ("align*环境", "\\begin{align*} x &= y \\\\ a &= b \\end{align*}"),
        ("split环境", "\\begin{split} x &= y \\\\ a &= b \\end{split}"),
        ("aligned环境", "\\begin{aligned} x &= y \\\\ a &= b \\end{aligned}"),
        
        # cases环境测试
        ("cases环境", "f(x) = \\begin{cases} x^2 & x > 0 \\\\ -x & x \\leq 0 \\end{cases}"),
        
        # smallmatrix测试
        ("smallmatrix", "\\begin{smallmatrix} a & b \\\\ c & d \\end{smallmatrix}"),
        
        # 复杂嵌套测试
        ("嵌套矩阵", "\\begin{pmatrix} \\begin{matrix} 1 & 2 \\end{matrix} & 3 \\\\ 4 & 5 \\end{pmatrix}"),
    ]
    
    for desc, latex_input in test_cases:
        try:
            latex_output = normalizer.normalize(latex_input)
            has_changed = latex_input != latex_output
            
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  规范后: {latex_output}")
            print(f"  状态: {'✓ 已规范化' if has_changed else '─ 无变化'}")
            
            # 检查特定问题
            if "pmatrix" in latex_input and "\\left(" in latex_output:
                print("  ⚠️  问题: pmatrix不应该有\\left(")
            
            if "align" in latex_input and "matrix" in latex_output and "aligned" not in latex_output:
                print("  ⚠️  问题: align应该转为aligned，不是matrix")
            
            if "\\\\" in latex_input and "\\\\" not in latex_output:
                print("  ⚠️  问题: 换行符丢失")
            
        except Exception as e:
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  错误: {e}")
            print(f"  状态: ✗ 处理失败")


def test_specific_issues():
    """测试特定问题"""
    print("\n" + "=" * 80)
    print("特定问题验证")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    # 测试问题1: pmatrix不应该有left/right
    print("\n问题1: pmatrix环境处理")
    test1 = "\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}"
    result1 = normalizer.normalize(test1)
    print(f"输入: {test1}")
    print(f"输出: {result1}")
    
    expected_patterns = ["\\begin{pmatrix}", "\\end{pmatrix}", "\\\\"]
    unwanted_patterns = ["\\left(", "\\right)"]
    
    for pattern in expected_patterns:
        if pattern in result1:
            print(f"✓ 包含期望的: {pattern}")
        else:
            print(f"✗ 缺少期望的: {pattern}")
    
    for pattern in unwanted_patterns:
        if pattern not in result1:
            print(f"✓ 正确排除: {pattern}")
        else:
            print(f"✗ 错误包含: {pattern}")
    
    # 测试问题2: align应该转为aligned
    print("\n问题2: align环境转换")
    test2 = "\\begin{align} x &= y \\\\ a &= b \\end{align}"
    result2 = normalizer.normalize(test2)
    print(f"输入: {test2}")
    print(f"输出: {result2}")
    
    if "aligned" in result2:
        print("✓ 正确转换为aligned")
    elif "matrix" in result2:
        print("✗ 错误转换为matrix")
    else:
        print("? 转换结果不明确")
    
    if "\\\\" in result2:
        print("✓ 保留了换行符")
    else:
        print("✗ 丢失了换行符")


if __name__ == "__main__":
    test_environment_fixes()
    test_specific_issues()
