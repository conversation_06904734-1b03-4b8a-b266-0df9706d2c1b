{"UniMER-1M_0000308": "1/k_{L}w_{0} \\gg \\omega_{p}^{2}/ \\omega_{L}^{2}", "UniMER-1M_0000493": "\\beta= \\textrm{cos}( \\theta) \\textrm{sin}( \\theta)(c_{23}+c_{44})", "UniMER-1M_0000132": "T", "UniMER-1M_0000395": "o", "UniMER-1M_0000306": "\\begin{array}{rl}&{{N \\rho_{A_{ \\mathrm{O}}}p_{i_{ \\mathrm{D}}A_{ \\mathrm{O}}}^{ \\prime} \\simN_{1}+N_{2},}} \\\\ &{{N_{1} \\sim{ \\calB}(N \\rho_{A_{ \\mathrm{O}}}p_{i_{ \\mathrm{R}}A_{ \\mathrm{O}}},a_{A_{ \\mathrm{O}}}^{ \\mathrm{GC}}),}} \\\\ &{{N_{2} \\sim{ \\calB}(N \\rho_{A_{ \\mathrm{O}}}(1-p_{i_{ \\mathrm{R}}A_{ \\mathrm{O}}}),a_{A_{ \\mathrm{O}}}^{ \\mathrm{BC}}).}} \\end{array}", "UniMER-1M_0000040": "C_{ \\mathrm{G}} \\approx16 \\: \\mathrm{ \\ muF/cm^{2}}", "UniMER-1M_0000492": "\\begin{array}{rl}{{y_{i,1}}}&{{=1 \\mathrm{~if~} c_{i+2}c_{i+1}c_{i} \\in \\{001,011 \\}, \\mathrm{~and~} y_{i,1}=0 \\mathrm{~otherwise},}} \\\\ {{y_{i,2}}}&{{=1 \\mathrm{~if~} c_{i+1}c_{i}=01 \\textup{s.t.}y_{i,1}=0, \\mathrm{~and~} y_{i,2}=0 \\mathrm{~otherwise}.}} \\end{array}", "UniMER-1M_0000131": "B= \\left[{ \\begin{array}{rrrrrrrr}{{-26}}&{{-3}}&{{-6}}&{{2}}&{{2}}&{{-1}}&{{0}}&{{0}} \\\\ {{0}}&{{-2}}&{{-4}}&{{1}}&{{1}}&{{0}}&{{0}}&{{0}} \\\\ {{-3}}&{{1}}&{{5}}&{{-1}}&{{-1}}&{{0}}&{{0}}&{{0}} \\\\ {{-3}}&{{1}}&{{2}}&{{-1}}&{{0}}&{{0}}&{{0}}&{{0}} \\\\ {{1}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{0}} \\end{array}} \\right].", "UniMER-1M_0000394": "\\rho^{q_{f}^{0}{ \\bar{q}}}= \\rho^{q_{f}^{0}} \\otimes \\rho^{ \\bar{q}}= \\left( \\begin{array}{cc}{{{c_{1f} \\rho^{ \\bar{q}}}}}&{{{0}}} \\\\ {{{0}}}&{{{c_{2f} \\rho^{ \\bar{q}}}}} \\end{array} \\right).", "UniMER-1M_0000304": "260^{ \\circ} \\leq \\Phi \\leq360^{ \\circ}", "UniMER-1M_0000039": "\\begin{array}{r}{{ \\xi \\mathrm{cos} \\theta_{ \\mathrm{L}}+ \\Delta \\mathrm{sin} \\theta_{ \\mathrm{L}}= \\sqrt{ \\xi^{2}+ \\Delta^{2}}.}} \\end{array}", "UniMER-1M_0000130": "\\int_{- \\infty}^{ \\infty} \\hat{G}_{w}^{(P)}(x, \\omega+P \\omega_{m}) \\mathrm{e}^{- \\mathrm{i}( \\kappa+P \\kappa_{m})x} \\, \\mathrm{d} x= \\mathbf{ \\tilde{G}}( \\kappa+P \\kappa_{m}, \\omega+P \\omega_{m}).", "UniMER-1M_0000491": "^{4}", "UniMER-1M_0000393": "N_{ \\mathrm{wave}}({ \\bar{ \\theta}}_{ \\mathrm{BV}},r)", "UniMER-1M_0000303": "h", "UniMER-1M_0000038": "k^{2}=k_{x}^{2}+k_{y}^{2}", "UniMER-1M_0000129": "f_{i}", "UniMER-1M_0000490": "\\mu \\approx{ \\frac{N_{A}h}{V}} \\mathrm{exp} \\left(3.8{ \\frac{T_{b}}{T}} \\right),", "UniMER-1M_0000392": "I=-igf^{abc}V_{ \\mu \\sigma \\alpha}(k_{1},-k_{2},k_{2}-k_{1}) \\frac{ \\epsilon^{ \\mu}(k_{1})}{2k_{1} \\cdotk_{2}}B^{b \\alpha \\rho}(q,k_{1}-k_{2}),", "UniMER-1M_0000302": "\\mathrm{2p^{2}(^{3}P)3p~^{4}S_{3/2}^{o}}", "UniMER-1M_0000037": "\\frac{d \\rho}{dt}=-[ \\overline{{{F}}}, \\rho] \\neq0.", "UniMER-1M_0000010": "N \\to \\infty", "UniMER-1M_0000128": "\\left( \\widetilde{ \\frac{ \\deltaS_{0}}{ \\deltaQ^{ \\dagger}}} \\right)+K_{n}( \\omega) \\, \\tilde{Q}( \\omega)=0,", "UniMER-1M_0000489": "\\begin{array}{rl}{{ \\mathrm{ln}{ \\gamma_{ \\mathrm{{H}}}}}}&{{=- \\epsilon_{ \\mathrm{{H}}}^{ \\mathrm{{Si}}}{x_{ \\mathrm{{Si}}}^{ \\prime}} \\left({1+ \\frac{{ \\mathrm{ln}(1-{x_{ \\mathrm{{Si}}}^{ \\prime}})}}{{{x_{ \\mathrm{{Si}}}^{ \\prime}}}}- \\frac{1}{{1-{x_{ \\mathrm{{H}}}}}}} \\right)}} \\end{array}", "UniMER-1M_0000011": "a_{1}", "UniMER-1M_0000301": "h_{ \\sigma \\sigma^{ \\prime}}", "UniMER-1M_0000036": "\\phi(t)", "UniMER-1M_0000391": "\\mum", "UniMER-1M_0000000": "n \\to \\infty", "UniMER-1M_0000127": "10^{-9}", "UniMER-1M_0000488": "\\begin{array}{rlr}{{ \\frac{d}{d \\tau} \\nabla_{b}X^{c}=X^{a} \\nabla_{a} \\nabla_{b}X^{c}}}&{{=}}&{{X^{a}( \\nabla_{a} \\nabla_{b}- \\nabla_{b} \\nabla_{a})X^{c}+X^{a} \\nabla_{b} \\nabla_{a}X^{c}}} \\\\ &{{=}}&{{X^{a}( \\nabla_{a} \\nabla_{b}- \\nabla_{b} \\nabla_{a})X^{c}+ \\nabla_{b}(X^{a} \\nabla_{a}X^{c})-( \\nabla_{b}X^{a})( \\nabla_{a}X^{c})}} \\\\ &{{=}}&{{ \\mathcal{R}_{ \\ dab}^{c}X^{a}X^{d}-( \\nabla_{b}X^{a})( \\nabla_{a}X^{c}) \\ .}} \\end{array}", "UniMER-1M_0000300": "\\leftarrow, \\rightarrow", "UniMER-1M_0000035": "{ \\frac{ \\par tiala_{k}^{s}}{ \\par tialt}}= \\frac{i \\epsilon}{8k_{ \\perp}} \\int_{ \\mathbb{R}^{6}} \\sum_{s_{p}s_{q}} \\left[s_{p}s_{q}k_{ \\perp}^{2}+ \\frac{k_{ \\par allel}}{pq} \\left(p_{ \\par allel}q^{2}+q_{ \\par allel}p^{2} \\right) \\right.", "UniMER-1M_0000390": "n_{ \\pm}=(n_{B} \\pmn_{A})/2", "UniMER-1M_0000126": "\\begin{array}{r}{{| \\psi_{k} \\rangle= \\sum_{x}( \\psi_{k}(x,A)a_{x}^{ \\dagger}+ \\psi_{k}(x,B)b_{x}^{ \\dagger})| \\varphi,g \\rangle+ \\psi_{e}| \\varphi,e \\rangle,}} \\end{array}", "UniMER-1M_0000487": "\\mathring{ \\mathbb{V}}_{1} \\times \\mathbb{V}_{h}^{2}", "UniMER-1M_0000034": "p_{L}", "UniMER-1M_0000388": "R(q)={ \\cfrac{q^{1/5}}{1+{ \\cfrac{q}{1+{ \\cfrac{q^{2}}{1+{ \\cfrac{q^{3}}{1+ \\ddots}}}}}}}}.", "UniMER-1M_0000299": "^{+}", "UniMER-1M_0000125": "\\begin{array}{rl}{{ \\left\\langlew, \\frac{ \\deltaH}{ \\deltau}-Du \\right\\rangle}}&{{=0, \\, \\forallw \\in \\mathbb{V}_{h}^{1},}} \\\\ {{ \\left\\langle \\phi, \\frac{ \\deltaH}{ \\deltaD}- \\frac{1}{2}|u|^{2}-g(D+b) \\right\\rangle}}&{{=0, \\quad \\forall \\phi \\in \\mathbb{V}_{h}^{2},}} \\end{array}", "UniMER-1M_0000486": "\\mathcal{J}_{2}( \\alpha)= \\frac{(k_{0}d)^{2}L_{y}}{2d^{2}} \\mathrm{exp}{ \\left(-| \\alpha| \\frac{ \\pid}{L_{y}} \\right)},", "UniMER-1M_0000033": "\\overrightarrow{ \\xi}", "UniMER-1M_0000387": "Q=+{ \\frac{ \\pi}{2}}- \\rho \\int_{0}^{n^{2}-1}{ \\frac{du^{ \\prime \\prime}}{u^{ \\prime \\prime}}}{ \\frac{ \\sqrt{n^{2}-1-u^{ \\prime \\prime}} \\sqrt{u^{ \\prime \\prime}}}{(n^{2}-1-u^{ \\prime \\prime})+ \\rho^{2}u^{ \\prime \\prime}}}.", "UniMER-1M_0000298": "l_{0} \\equiv \\sqrt{ \\kappa} \\simU_{0} \\tau_{R}", "UniMER-1M_0000484": "x_{1}", "UniMER-1M_0000124": "x=1", "UniMER-1M_0000386": "{ \\dot{ \\phi}}^{2}={ \\frac{M}{r^{3}}}{ \\dot{t}}^{2}", "UniMER-1M_0000032": "\\rho", "UniMER-1M_0000297": "230", "UniMER-1M_0000483": "q_{i}=2 \\alpha_{i}^{II}r_{u}w_{u} \\,, \\quad q_{f}=2 \\alpha_{f}^{II}r_{d}w_{d} \\,.", "UniMER-1M_0000123": "t_{l}=l \\, \\trianglet", "UniMER-1M_0000031": "({ \\hat{T}}( \\mathbf{x}))^{-1} \\mathbf{ \\hat{r}}{ \\hat{T}}( \\mathbf{x})= \\mathbf{ \\hat{r}}+ \\mathbf{x}{ \\hat{ \\mathbb{I}}}", "UniMER-1M_0000009": "p", "UniMER-1M_0000385": "p=1", "UniMER-1M_0000296": "u^{i}", "UniMER-1M_0000482": "\\tilde{ \\mathbf{u}}( \\tilde{ \\mathbf{x}}, \\tilde{z}, \\tilde{t})= \\tilde{ \\mathbf{u}}_{b}( \\tilde{ \\mathbf{x}}, \\tilde{t})+O( \\sigma^{2}) \\ ,", "UniMER-1M_0000121": "dX(t)= \\up silon_{f} \\left(X(t), \\ t \\right)dt+ \\sigmadW_{f}(t),", "UniMER-1M_0000384": "\\up mu \\mathrm{W}", "UniMER-1M_0000295": "\\rho_{i}", "UniMER-1M_0000030": "\\begin{array}{rlr}{{E_{12}(a,c,W \\rightarrow0)}}&{{=}}&{{ \\left\\{ \\begin{array}{lcl}{{- \\frac{1}{2} \\mathrm{cos}2 \\theta}}&{{,}}&{{d=0}} \\\\ {{ \\frac{ \\pi}{4} \\mathrm{sin}2 \\theta \\mathrm{cos}2 \\theta- \\mathrm{cos}2 \\theta+ \\mathrm{ln}[| \\mathrm{tan} \\theta|^{ \\mathrm{sin}^{2}2 \\theta/2}]}}&{{,}}&{{d=2}} \\\\ {{- \\mathrm{cos}2 \\theta}}&{{,}}&{{d=4}} \\\\ {{- \\frac{1}{2} \\mathrm{cos}2 \\theta \\left[1+24(19+5 \\mathrm{cos}4 \\theta)^{-1} \\right]}}&{{,}}&{{d=6}} \\\\ {{-(53 \\mathrm{cos}2 \\theta+7 \\mathrm{cos}6 \\theta)(39+21 \\mathrm{cos}4 \\theta)^{-1}}}&{{,}}&{{d=8}} \\end{array} \\right.,}} \\end{array}", "UniMER-1M_0000481": "E_{fr}=- \\frac{ \\hbar^{2} \\kappa^{2}}{2m_{r}}=- \\frac{ \\hbar^{2}}{2m_{r}r_{0}^{2}} \\left(1- \\sqrt{1- \\frac{2r_{0}}{a}} \\right)^{2},", "UniMER-1M_0000120": "v_{g}", "UniMER-1M_0000383": "\\omega_{ \\mathrm{~N~L~}}(k,I_{1})=-k-gI_{1}/2", "UniMER-1M_0000294": "\\rho_{m}", "UniMER-1M_0000480": "k^{2}=2m(E-U_{0})/ \\hbar^{2}", "UniMER-1M_0000119": "u(t)", "UniMER-1M_0000382": "y", "UniMER-1M_0000293": "\\sim40 \\", "UniMER-1M_0000478": "0.506", "UniMER-1M_0000008": "\\begin{array}{rlr}&&{{ \\left. \\frac{ \\par tial}{ \\par tialb} \\left\\{I(u,v) \\right\\} \\right|_{b=0}}} \\\\ &{{=}}&{{-j \\left[ \\int_{0}^{2 \\pi} \\int_{0}^{1}e^{j[aZ_{5}(r, \\theta)-2 \\pi \\rhorcos( \\phi- \\theta)}rdrd \\theta \\right.}} \\\\ &&{{ \\times \\int_{0}^{2 \\pi} \\int_{0}^{1}Z_{4}(r, \\theta)e^{-j[aZ_{5}(r, \\theta)-2 \\pi \\rhorcos( \\phi- \\theta)}rdrd \\theta}} \\\\ &&{{- \\: \\int_{0}^{2 \\pi} \\int_{0}^{1}e^{-j[aZ_{5}(r, \\theta)-2 \\pi \\rhorcos( \\phi- \\theta)}rdrd \\theta}} \\\\ &&{{ \\times \\left. \\int_{0}^{2 \\pi} \\int_{0}^{1}Z_{4}(r, \\theta)e^{j[aZ_{5}(r, \\theta)-2 \\pi \\rhorcos( \\phi- \\theta)}rdrd \\theta \\right]}} \\end{array}", "UniMER-1M_0000118": "m=1", "UniMER-1M_0000381": "\\begin{array}{r}{{- \\nabla \\cdot \\boldsymbol{ \\mathcal{S}}= \\frac{1}{16 \\pi} \\left( \\boldsymbol{ \\mathcal{E}} \\cdot \\frac{ \\par tial \\boldsymbol{ \\mathcal{D}^{*}}}{ \\par tialt}+ \\boldsymbol{ \\mathcal{E}^{*}} \\cdot \\frac{ \\par tial \\boldsymbol{ \\mathcal{D}}}{ \\par tialt} \\right),}} \\end{array}", "UniMER-1M_0000292": "H=5/8", "UniMER-1M_0000477": "\\theta", "UniMER-1M_0000117": "\\left| \\frac{ \\epsilon_{1} \\epsilon_{2}}{ \\epsilon_{+}^{2}} \\frac{tr(fh^{l}f^{ \\dag} \\tilde{h}^{l})}{tr \\{(ff^{ \\dag})^{2} \\}}- \\frac{h_{D1}^{2}}{2f_{1}^{2}} \\right|<10^{-8}.", "UniMER-1M_0000380": "\\begin{array}{rl}&{{G_{[ \\sigma]}( \\widehat{h}_{0}, \\widehat{h}_{0})= \\left\\langle4 \\frac{ \\Gamma(1/4-S) \\Gamma(1/4+S)}{ \\Gamma(3/4-S) \\Gamma(3/4+S)}(- \\Delta_{ \\sigma}+2)^{-1}4 \\frac{|q|^{2}}{ \\sigma^{3}},(- \\Delta_{ \\sigma}+2)^{-1}4 \\frac{|q|^{2}}{ \\sigma^{3}} \\right\\rangle_{L_{ \\sigma}^{2}(S)}+ \\frac{1}{ \\pi^{2}| \\chi(S)|^{2}} \\|q \\|_{ \\sigma}^{4},}} \\end{array}", "UniMER-1M_0000291": "N \\to \\infty", "UniMER-1M_0000476": "\\beta", "UniMER-1M_0000379": "B_{2}", "UniMER-1M_0000116": ". . .", "UniMER-1M_0000029": "\\sigma(r)= \\frac{2E}{1+ \\nu} \\frac{h \\kappa}{4 \\pir^{3}}", "UniMER-1M_0000290": "t=0", "UniMER-1M_0000007": "c=(m^{2}+n^{2})^{2},", "UniMER-1M_0000475": "\\mathcal{D}= \\frac{ \\int_{ \\mathcal{L}}~d \\mathcal{L} \\int_{ \\widetilde{ \\xi}_{n}}~[u( \\widetilde{ \\xi}_{n})]~[u( \\widetilde{ \\xi}_{n}- \\mathcal{L})]~d \\widetilde{ \\xi}_{n}}{ \\int_{ \\widetilde{ \\xi}_{n}}~[u( \\widetilde{ \\xi}_{n})]~[u( \\widetilde{ \\xi}_{n})]~d \\widetilde{ \\xi}_{n}}= \\frac{ \\int_{ \\mathcal{L}}~d \\mathcal{L} \\int_{ \\widetilde{ \\xi}_{n}}~[u^{ \\lambda}( \\widetilde{ \\xi}_{n})]~[u^{ \\lambda}( \\widetilde{ \\xi}_{n}- \\mathcal{L})]~d \\widetilde{ \\xi}_{n}}{ \\int_{ \\widetilde{ \\xi}_{n}}~[u^{ \\lambda}( \\widetilde{ \\xi}_{n})]~[u^{ \\lambda}( \\widetilde{ \\xi}_{n})]~d \\widetilde{ \\xi}_{n}}", "UniMER-1M_0000377": "V_{ \\alpha \\beta, \\delta \\gamma}", "UniMER-1M_0000289": "\\begin{array}{rl}{{P_{1}= \\,}}&{{ \\mathrm{span} \\left( \\left\\{ \\vphantom{ \\frac{1}{x^{2}+1}} \\mathrm{d} u^{1}, \\mathrm{d} x^{2}- \\mathrm{d} x^{4},(u^{1}+1) \\mathrm{d} x^{2}+x^{2} \\mathrm{d} u^{1} \\right. \\right.,}} \\\\ &{{ \\left. \\left. \\frac{1}{x^{2}+1} \\left(-(u^{1}+1) \\mathrm{d} x^{1}+ \\frac{(u^{1}+1)x^{1}}{x^{2}+1} \\mathrm{d} x^{2} \\right. \\right. \\right.}} \\\\ &{{ \\left. \\left. \\left. \\vphantom{ \\frac{1}{x^{2}+1}} \\quad+(x^{2}+1) \\mathrm{d} x^{5}-x^{1} \\mathrm{d} u^{1} \\right) \\right\\} \\right).}} \\end{array}", "UniMER-1M_0000115": "1 \\", "UniMER-1M_0000474": "x", "UniMER-1M_0000376": "\\begin{array}{rl}{{ \\par tial_{ \\rho} \\bigg( \\nu( \\theta_{0}) \\big( \\par tial_{ \\rho} \\hat{ \\mathbf{v}}_{0}- \\mathbf{u}_{0}d_{ \\Gamma} \\eta^{ \\prime} \\big) \\bigg)}}&{{= \\big(2 \\theta_{0}^{ \\prime} \\theta_{0}^{ \\prime \\prime}+ \\par tial_{ \\rho} \\hat{p}_{-1} \\big) \\nablad_{ \\Gamma},}} \\\\ {{ \\big( \\par tial_{ \\rho} \\hat{ \\mathbf{v}}_{0}- \\mathbf{u}_{0}d_{ \\Gamma} \\eta^{ \\prime} \\big) \\cdot \\nablad_{ \\Gamma}}}&{{=0,}} \\end{array}", "UniMER-1M_0000288": "\\begin{array}{rl}{{f(x)}}&{{= \\frac{1}{2}x^{ \\top}A^{ \\top}Ax-x^{ \\top}A^{ \\top}b+ \\frac{1}{2} \\|b \\|_{2}^{2}}} \\\\ &{{= \\frac{1}{2}x^{ \\top}A^{ \\top}Ax-x^{ \\top}A^{ \\top}AA^{ \\dagger}b+ \\frac{1}{2} \\|b \\|_{2}^{2}}} \\\\ &{{= \\frac{1}{2} \\|Ax-AA^{ \\dagger}b \\|_{2}^{2}- \\frac{1}{2} \\|AA^{ \\dagger}b \\|_{2}^{2}+ \\frac{1}{2} \\|b \\|_{2}^{2},}} \\end{array}", "UniMER-1M_0000114": "^{ \\circ}", "UniMER-1M_0000473": "e", "UniMER-1M_0000375": "\\phi", "UniMER-1M_0000287": "\\phi \\in[0,2 \\pi]", "UniMER-1M_0000113": "R^{2}", "UniMER-1M_0000472": "\\tau_{1}=-2.20 \\, \\mu \\mathrm{s}", "UniMER-1M_0000374": "\\mathcal{S}=4.52", "UniMER-1M_0000286": "\\Gamma>1", "UniMER-1M_0000112": "\\begin{array}{r}{{ \\Phi_{2}(-1+a)- \\Phi_{0}(1-a)=0}} \\end{array}", "UniMER-1M_0000028": "V(t)", "UniMER-1M_0000006": "_{DD}", "UniMER-1M_0000471": "\\tilde{ \\Omega}=e^{-f_{ \\calS}(z)}{ \\calS} \\Omega \\ ,", "UniMER-1M_0000373": "\\in{ \\calD}", "UniMER-1M_0000285": "R_{N} \\,[ \\mathrm{m} \\Omega]", "UniMER-1M_0000111": "\\begin{array}{rl}{{ \\mathrm{PL^{CI}}}}&{{(f,d)[ \\mathrm{dB}]= \\mathrm{FSPL}(f, \\mathrm{1 \\,m})[ \\mathrm{dB}]+10 \\eta \\mathrm{log}_{10}(d)}} \\\\ &{{+ \\mathrm{AT}[ \\mathrm{dB}]+ \\mathrm{O2I}[ \\mathrm{dB}]+ \\mathrm{FL}[ \\mathrm{dB}]+ \\chi_{ \\sigma}^{CI}[ \\mathrm{dB}]}} \\end{array}", "UniMER-1M_0000470": "</span>Sincethenumberofelementsinaninclusiverangelikethis,i.e.<spanclass=\"math-container\"> \\left[n_{ \\mathrm{min}},~n_{ \\mathrm{max}} \\right],</span>is<spanclass=\"math-container\">1+n_{ \\mathrm{max}}-n_{ \\mathrm{min}},</span>we^{ \\prime}rethereforeinterestedin<spanclass=\"math-container\">", "UniMER-1M_0000371": "\\begin{array}{r}{{p(a_{ \\kappa \\kappa^{ \\prime}}=1| \\Delta \\theta)= \\frac{1}{1+ \\left( \\frac{R \\Delta \\theta}{( \\mu \\kappa^{ \\prime} \\kappa)^{1/D}} \\right)^{ \\beta}}.}} \\end{array}", "UniMER-1M_0000284": "\\operatorname{det} \\left( \\rho \\omega^{2}g^{il}-C^{ijkl} \\zeta^{2}n_{j}n_{k} \\right)=0 \\,.", "UniMER-1M_0000110": "\\mathcal{W}", "UniMER-1M_0000468": "\\lambda={ \\frac{1}{32 \\pi^{2}}} \\left[ \\left({ \\frac{2}{d-4}} \\right)+ \\gamma_{E}-1- \\mathrm{ln}(4 \\pi) \\right].", "UniMER-1M_0000370": "{S_{22}^{th}= \\frac{8e^{2}}{h} \\intdEf_{0}(1-f_{0}),}", "UniMER-1M_0000283": "\\xi_{i}", "UniMER-1M_0000109": "T^{0}(x):=1200 \\mathrm{exp} \\left(- \\frac{(x-l/2)^{2}}{200m^{2}} \\right) \\quad \\mathrm{~a~n~d~} \\quad S^{0} \\equiv1.", "UniMER-1M_0000467": "\\!(1+ \\beta_{0})", "UniMER-1M_0000369": "\\hat{H}_{ \\mathrm{e} l}= \\sum_{ \\mathrm{pq}}h_{pq}a_{p}^{ \\dagger}a_{q}+ \\frac{1}{2} \\sum_{ \\mathrm{pqrs}}h_{pqrs}a_{p}^{ \\dagger}a_{q}^{ \\dagger}a_{r}a_{s},", "UniMER-1M_0000005": "R", "UniMER-1M_0000282": "\\mathrm{d} X_{t}=a(X_{t}) \\, \\mathrm{d} t+b(X_{t}) \\, \\mathrm{d} W_{t},", "UniMER-1M_0000108": "f=1,2", "UniMER-1M_0000027": "\\begin{array}{rl}{{ \\phi_{i}( \\lambda_{i})}}&{{= \\mathrm{vec} \\left({{ \\mathbf{P}}}_{i} \\right)^{H} \\mathrm{vec} \\left({{ \\mathbf{P}}}_{i} \\right)}} \\\\ &{{={{ \\mathbf{a}}}_{i}^{H}{{ \\mathbf{D}}}_{i} \\left( \\lambda_{i}{{ \\mathbf{I}}}_{MM_{e}}+ \\mathrm{{diag}} \\left({{ \\mathbf{f}}}_{i} \\right) \\right)^{-1} \\left( \\lambda_{i}{{ \\mathbf{I}}}_{MM_{e}}+{ \\mathrm{diag}} \\left({{ \\mathbf{f}}}_{i} \\right) \\right)^{-1}{{ \\mathbf{D}}}_{i}^{H}{{ \\mathbf{a}}}_{i}= \\sum_{m=1}^{MM_{e}} \\frac{b_{i,m}^{2}}{ \\left( \\lambda_{i}+f_{i,m} \\right)^{2}},}} \\end{array}", "UniMER-1M_0000466": "\\begin{array}{rl}{{S_{2}=}}&{{[F_{0}( \\vec{1})+H_{0}( \\vec{1})]-[F_{0}( \\vec{ \\zeta})+H_{0}( \\vec{ \\xi})]}} \\end{array}", "UniMER-1M_0000368": "c_{ \\mathrm{air}}=331.3~{ \\sqrt{1+{ \\frac{ \\vartheta}{273.15}}}}~~~~ \\mathrm{m/s}.", "UniMER-1M_0000281": "x=1/ \\zeta", "UniMER-1M_0000107": "| \\mathscr{W}|=-2", "UniMER-1M_0000465": "if", "UniMER-1M_0000367": "\\begin{array}{r}{{z_{j}^{j}(t)=w_{j}^{P}(t) \\frac{{b}^{SB,j}( \\gamma_{j}^{j})^{2} \\frac{ \\eta_{P} \\eta_{i}}{ \\eta_{P}+ \\eta_{i}}+ \\frac{1}{q_{j}}}{{b}^{SB,j}( \\gamma_{j}^{j})^{2}( \\eta_{j}+ \\frac{ \\eta_{P} \\eta_{i}}{ \\eta_{P}+ \\eta_{i}})+ \\frac{1}{q_{j}}} \\;, \\quad z_{i}^{j}(t)=w_{i}^{P}(t) \\frac{ \\eta_{P} \\eta_{i}}{ \\eta_{P}+ \\eta_{j}} \\frac{{b}^{SB,i}( \\gamma_{i}^{i})^{2}}{{b}^{SB,i}( \\gamma_{i}^{i})^{2}( \\eta_{i}+ \\frac{ \\eta_{P} \\eta_{j}}{ \\eta_{P}+ \\eta_{j}})+ \\frac{1}{q_{i}}} \\;.}} \\end{array}", "UniMER-1M_0000280": "\\mathbf{E}_{m}^{ \\kappa}(t)= \\sum_{k \\in2 \\ensuremath{ \\mathbb{Z}}+1} \\xi_{m,k}(t)^{2} \\kappa \\Bigl( \\ensuremath{ \\mathrm{I}_{2}}+ \\, \\bigl\\langle( \\nabla{ \\boldsymbol{ \\chi}}_{m,k}^{ \\kappa})^{ \\intercal} \\nabla{ \\boldsymbol{ \\chi}}_{m,k}^{ \\kappa} \\bigr\\rangle \\Bigr) \\,.", "UniMER-1M_0000106": "U", "UniMER-1M_0000464": "I_{13}^{(1)}", "UniMER-1M_0000366": "\\zeta^{(1)}( \\mathbf{x},t)= \\mathcal{R} \\left[ \\frac{1}{4 \\pi^{2}} \\int| \\hat{ \\zeta}( \\mathbf{k})| \\mathrm{e}^{ \\mathrm{i} \\psi( \\mathbf{k}, \\mathbf{x},t)} \\mathrm{d} \\mathbf{k} \\right],", "UniMER-1M_0000279": "\\{O_{ \\alpha} \\}_{ \\alpha \\inA \\setminusA^{ \\prime}} \\cup \\{ \\tilde{O}_{ \\alpha^{ \\prime}} \\}_{ \\alpha^{ \\prime} \\inA^{ \\prime}}", "UniMER-1M_0000103": "\\hat{D}^{2}=D^{2}+i \\gamma^{5}( \\par tial_{5}A \\! \\! \\!/)+i \\{ \\varphi, \\par tial_{5} \\}- \\par tial_{5}^{2}.", "UniMER-1M_0000004": "s_{ \\mathrm{im}}^{2} \\left(a_{1110}+a_{2001}-s_{ \\mathrm{im}}a_{2100} \\right)", "UniMER-1M_0000463": "\\theta_{0}= \\pi", "UniMER-1M_0000278": "E \\subset \\Omega", "UniMER-1M_0000102": "n_{g}=v_{g}/c", "UniMER-1M_0000365": "ms", "UniMER-1M_0000026": "x_{0}=x^{u}", "UniMER-1M_0000462": "E>1", "UniMER-1M_0000277": "g_{n}=n=[z^{n}]z/(1-z)^{2}", "UniMER-1M_0000100": "\\mathcal{H}=-J \\sum_{ \\langleij \\rangle} \\pmb{ \\sigma}_{i} \\pmb{ \\sigma}_{j},", "UniMER-1M_0000364": "u_{ \\mathrm{~m~a~x~}}>u_{ \\mathrm{~m~i~n~}}>u_{ \\mathrm{~B~F~}}", "UniMER-1M_0000461": "\\mathcal{I}", "UniMER-1M_0000276": "\\  \\rho{ \\frac{Dh}{Dt}}={ \\frac{Dp}{Dt}}+ \\nabla \\cdot \\left(k \\nablaT \\right)+ \\Phi", "UniMER-1M_0000099": "{ \\mathcal{M}}={ \\frac{1}{ \\sqrt{Z}}}{ \\Big(} \\operatorname{lim}_{x_{1}^{0} \\rightarrow- \\infty}- \\operatorname{lim}_{x_{1}^{0} \\rightarrow+ \\infty}{ \\Big)} \\int \\! \\mathrm{d}^{3}x_{1} \\; \\mathrm{e}^{ip_{1} \\cdotx_{1}} \\langle \\beta \\  \\mathrm{out}|{ \\bar{ \\Psi}}(x_{1}) \\gamma^{0}u_{{ \\textbf{p}}_{1}}^{s_{1}}| \\alpha^{ \\prime} \\  \\mathrm{in} \\rangle,", "UniMER-1M_0000363": "P^{ \\sigma}", "UniMER-1M_0000460": "\\bar{ \\phi}_{ \\mathrm{~S~t~a~n~d~a~r~d~}}", "UniMER-1M_0000275": "\\deltaV(t)=j_{ \\mu}(x,t)A_{cl}^{ \\mu}(x,t),", "UniMER-1M_0000098": "\\lambda=L/H", "UniMER-1M_0000003": "S= \\textsf{Stabs}( \\Pi)= \\{P_{i} \\}", "UniMER-1M_0000362": "R", "UniMER-1M_0000001": "\\begin{array}{rl}{{ \\langleu|u|^{2},b_{j,1} \\rangle}}&{{= \\sum_{k,l,m} \\frac{A_{k}A_{l}A_{m}}{L_{k}L_{l}L_{m}} \\left\\langlee^{i \\Gamma_{k}+i \\Gamma_{l}-i \\Gamma_{m}}e^{- \\frac{|y_{k}|^{2}+|y_{l}|^{2}+|y_{m}|^{2}}{2}},e^{i \\Gamma_{j}}e^{- \\frac{1}{2}|y_{j}|^{2}} \\right\\rangle}} \\end{array}", "UniMER-1M_0000459": "\\mathbf{G}_{(l)} \\omega_{n}^{m}+ \\sum_{k=1}^{N_{0}} \\omega_{n}^{k} \\lambda_{lk}^{m}=0.", "UniMER-1M_0000097": "\\neg(A \\landB)", "UniMER-1M_0000361": "\\Lambda_{ \\nu}", "UniMER-1M_0000274": ">20", "UniMER-1M_0000458": "n_{d}", "UniMER-1M_0000096": "(l+1)(l+2) . . .(l+d-1)", "UniMER-1M_0000025": "96 \\", "UniMER-1M_0000359": "L \\rightarrow \\infty", "UniMER-1M_0000272": "m_{ \\tilde{ \\chi}_{1}^{0}}^{2} \\simeq0.16m_{1/2}^{2}", "UniMER-1M_0000457": "\\mathrm{exp} \\{-{ \\it i} \\frac{ \\eta}{|{ \\bf q}|} \\;2 \\alpha_{ \\perp}t \\} \\rightarrow1,~~(|{ \\bf q}| \\rightarrow \\infty).", "UniMER-1M_0000095": "\\phi=0", "UniMER-1M_0000358": "\\begin{array}{r}{{ \\gamma_{ \\mathrm{Low}}(R, \\beta):= \\operatorname{sup}_{K \\in{ \\mathscrC}^{ \\beta}({ \\mathbb{R}): \\,R \\geq \\|K \\|_{{ \\mathscrC}^{ \\beta}({ \\mathbb{R})}} \\frac{(2 \\beta)^{2}}{2^{1/ \\beta}(2 \\beta+1)^{2+1/ \\beta}} \\frac{K(0)^{2+1/ \\beta}}{ \\|K \\|_{2}^{2}}.}} \\end{array}", "UniMER-1M_0000271": "t_{ii} \\ggt_{ \\mathrm{eq}} \\simeqt_{ \\mathrm{orb}}>t_{ \\mathrm{IC}} \\gtrsimt_{ee}", "UniMER-1M_0000002": "\\begin{array}{rlr}{{V(x,t)}}&{{=}}&{{ \\frac{1}{| \\sqrt{2 \\lambda} \\alpha_{0}|^{2q}} \\sum_{m=0}^{q}(-1)^{m+q} \\frac{(2^{m}q!)^{2}}{(2m)!(q-m)!} \\lambda^{q-m}x^{2m}}} \\\\ &&{{- \\frac{(-i)^{q}}{| \\sqrt{2 \\lambda} \\alpha_{0}|^{2q}} \\Big[ \\Big(e^{-i \\tau} \\alpha_{0} \\sqrt{2 \\lambda} \\Big)^{q}+ \\Big(e^{i \\tau} \\alpha_{0}^{*} \\sqrt{2 \\lambda} \\Big)^{q} \\Big]}} \\\\ &&{{ \\times \\Big([1+(-1)^{q}] \\frac{ \\Gamma(1+q) \\Gamma( \\frac{1}{2})}{ \\Gamma( \\frac{1}{2}+ \\frac{q}{2})} \\frac{(-1)^{ \\frac{q}{2}}}{2}+i[1-(-1)^{q}] \\frac{ \\Gamma(q+1) \\Gamma( \\frac{3}{2})}{ \\Gamma( \\frac{q}{2}+1)}(-1)^{ \\frac{q-1}{2}} \\Big)x^{q}}} \\\\ &&{{+1.}} \\end{array}", "UniMER-1M_0000456": "\\langle{t_{13}} \\rangle=T_{13}", "UniMER-1M_0000094": "\\begin{array}{rl}{{h_{00}^{ \\mathrm{PD}} \\big|_{ \\vec{x}=(x,0,0)}}}&{{= \\frac{1- \\mathrm{cos}(x \\omega_{g}c_{ \\vartheta})}{ \\omega_{g}^{2}c_{ \\vartheta}^{2}} \\,( \\par tial_{0})^{2}h_{11}^{ \\mathrm{TT}} \\big|_{ \\vec{x}=(0,0,0)} \\,,}} \\\\ {{h_{01}^{ \\mathrm{PD}} \\big|_{ \\vec{x}=(x,0,0)}}}&{{=h_{11}^{ \\mathrm{PD}} \\big|_{ \\vec{x}=(x,0,0)}=0 \\,.}} \\end{array}", "UniMER-1M_0000357": "C_{ \\textrm{N}}^{ \\prime}", "UniMER-1M_0000270": "*", "UniMER-1M_0000455": "H^{ \\prime}=0.6473", "UniMER-1M_0000093": "\\frac{dn_{i}}{dt}= \\frac{ \\par tialn_{i}}{ \\par tialt}+ \\triangledown \\cdot(D \\triangledownn_{i})= \\frac{ \\deltan_{i}}{ \\deltat},", "UniMER-1M_0000356": "\\delta_{1}^{C}= \\delta_{2}^{C}= \\delta_{3}^{C}=0.4", "UniMER-1M_0000269": "\\begin{array}{r}{{ \\mathbf{a}_{ \\mathrm{air}}= \\mathbf{T}_{ \\mathrm{top}} \\mathbf{P}(h_{ \\mathrm{t}}) \\mathbf{a}_{ \\mathrm{tot}}^{ \\mathrm{J}}.}} \\end{array}", "UniMER-1M_0000454": "^{ \\#}", "UniMER-1M_0000092": "E_{c}", "UniMER-1M_0000024": "G_{F}", "UniMER-1M_0000355": "ds^{2}=-e^{2A}fdt^{2}+e^{-2A}({ \\frac{dr^{2}}{f}}+r^{2}d \\Omega^{2}),", "UniMER-1M_0000268": "2", "UniMER-1M_0000453": "\\mathbf{s}_{1:T}", "UniMER-1M_0000090": "\\tilde{ \\Omega}", "UniMER-1M_0000353": "\\sum_{i=1}^{m} \\frac{(n_{i}-n \\timesp_{i})^{2}}{n \\timesp_{i}}", "UniMER-1M_0000267": "\\mu", "UniMER-1M_0000089": "BW=15", "UniMER-1M_0000451": "6.47", "UniMER-1M_0000352": "\\kappa", "UniMER-1M_0000266": "c_{i}= \\frac{1}{n-1} \\sum_{j \\neqi} \\frac{1}{d_{ij}}", "UniMER-1M_0000088": "v", "UniMER-1M_0000450": "\\sim500", "UniMER-1M_0000351": "- \\, \\left[ \\frac{d^{2}}{dr^{2}}+ \\frac{1}{r} \\frac{d}{dr} \\right] \\; \\chi(r) \\;+ \\; \\frac{ \\par tialV( \\chi)}{ \\par tial \\chi} \\;+ \\;(P^{+})^{2} \\; \\frac{b}{2} \\;I_{g}(R_{q \\bar{q}}) \\; \\intdr \\,r \\, \\kappa( \\chi) \\;= \\;0 \\;,", "UniMER-1M_0000265": "\\begin{array}{r}{{D_{L}=- \\omega_{0}^{2}(k)+ \\omega_{ \\mathrm{pd}}^{2}e^{-kR} \\left[(1+kR) \\left( \\frac{1}{3}- \\frac{2 \\mathrm{cos}{kR}}{k^{2}R^{2}} \\right. \\right.}} \\\\ {{ \\left. \\left.+ \\frac{2 \\mathrm{sin}{kR}}{k^{3}R^{3}} \\right)- \\frac{ \\kappa^{2}}{ \\kappa^{2}+k^{2}} \\left( \\mathrm{cos}{kR}+ \\frac{ \\kappa}{k} \\mathrm{sin}{kR} \\right) \\right]}} \\end{array}", "UniMER-1M_0000087": "s", "UniMER-1M_0000447": "Re_{L}= \\overline{{{w_{ \\mathrm{rms}}}}}L_{33}^{F}/ \\nu", "UniMER-1M_0000350": "\\begin{array}{rlr}{{ \\frac{ \\par tial{ \\bf u}}{ \\par tialt}+({ \\bf u} \\cdot \\nabla){ \\bf u}}}&{{=}}&{{- \\nabla({p}/{ \\rho})+ \\nu \\nabla^{2}{ \\bf u}+{ \\bf F}_{u}({ \\bf u,B})+{ \\bf F}_{ \\mathrm{ext}},}} \\\\ {{ \\frac{ \\par tial{ \\bf B}}{ \\par tialt}+({ \\bf u} \\cdot \\nabla){{ \\bf B}}}}&{{=}}&{{ \\eta \\nabla^{2}{{ \\bf B}}+{ \\bf F}_{B}({ \\bf u,B}),}} \\\\ {{ \\nabla \\cdot{ \\bf u}}}&{{=}}&{{0,}} \\end{array}", "UniMER-1M_0000264": "\\begin{array}{rlr}{{ \\rho(j_{x})}}&{{=}}&{{ \\frac{1}{ \\epsilon_{x}} \\mathrm{exp}[- \\frac{j_{x}}{ \\epsilon_{x}}]= \\frac{1}{ \\epsilon_{x}} \\mathrm{exp}[-2 \\alpha_{x}], \\; \\; \\; \\alpha_{x}= \\frac{a_{x}^{2}}{4}= \\frac{j_{x}}{2 \\epsilon_{x}}}} \\\\ {{ \\rho( \\alpha_{x})}}&{{=}}&{{ \\rho(a_{x})[ \\frac{ \\par tial \\alpha_{x}}{ \\par tiala_{x}}]^{-1}=a_{x} \\mathrm{exp}[- \\frac{1}{2}a_{x}^{2}][a_{x}/2]^{-1}=2 \\mathrm{exp}[-2 \\alpha_{x}]}} \\end{array}", "UniMER-1M_0000023": "r<R_{ \\mathrm{phot}}", "UniMER-1M_0000086": "\\forall^{p}L:= \\left\\{x \\in \\{0,1 \\}^{*} \\  \\left| \\  \\left( \\forallw \\in \\{0,1 \\}^{ \\leqp(|x|)} \\right) \\langlex,w \\rangle \\inL \\right. \\right\\}", "UniMER-1M_0000445": "13.5", "UniMER-1M_0000263": "10^{4}", "UniMER-1M_0000349": "\\pi", "UniMER-1M_0000085": "\\Gamma(p) \\rightarrow \\Gamma(p^{ \\prime})= \\Lambda \\Gamma(p) \\Lambda^{-1}", "UniMER-1M_0000444": "\\par tial(X \\timesY)=( \\par tialX \\timesY) \\cup(X \\times \\par tialY).", "UniMER-1M_0000262": "F^{0}F^{8} \\mathrm{sin}{( \\theta_{8}- \\theta_{0})}=f_{8}b_{0}+f_{0}b_{8}.", "UniMER-1M_0000348": "f_{0}", "UniMER-1M_0000083": "y_{0}=|H( \\mathrm{j} \\omega)|", "UniMER-1M_0000443": "6.3 \\times10^{6}cm/s", "UniMER-1M_0000261": "x_{c} \\approx \\pm100 \\, \\sqrt{m_{e}} \\,a_{0}", "UniMER-1M_0000347": "f_{m}", "UniMER-1M_0000081": "\\mathrm{Nu}^{*,(drop)}<-2", "UniMER-1M_0000442": "x=1/2", "UniMER-1M_0000260": "B_{n_{2}^{ \\prime}}^{ \\mathrm{~I~I~}}", "UniMER-1M_0000346": "M", "UniMER-1M_0000021": "F_{3}", "UniMER-1M_0000080": "^1", "UniMER-1M_0000441": "\\begin{array}{rl}{{ \\frac{d}{dt} \\langle \\hat{n}_{1} \\rangle=}}&{{ \\,2 \\left( \\beta_{1}- \\gamma_{1} \\right) \\langle \\hat{n}_{1} \\rangle+ \\mu \\langle \\hat{ \\eta} \\rangle+2 \\beta_{1}}} \\\\ {{ \\frac{d}{dt} \\langle \\hat{n}_{2} \\rangle=}}&{{ \\,2 \\left( \\beta_{2}- \\gamma_{2} \\right) \\langle \\hat{n}_{2} \\rangle- \\mu \\langle \\hat{ \\eta} \\rangle+2 \\beta_{2}}} \\\\ {{ \\frac{d}{dt} \\langle \\hat{ \\eta} \\rangle=}}&{{ \\,2 \\mu \\langle \\hat{n}_{2} \\rangle-2 \\mu \\langle \\hat{n}_{1} \\rangle}} \\\\ &{{+ \\left( \\beta_{1}+ \\beta_{2}- \\gamma_{1}- \\gamma_{2} \\right) \\langle \\hat{ \\eta} \\rangle}} \\end{array}", "UniMER-1M_0000259": "\\mathsfX= \\left[ \\begin{array}{ll}{{ \\mathrm{cos} \\zeta_{-} \\mathrm{cos} \\zeta_{+}}}&{{- \\Delta \\mathrm{sin} \\zeta_{-} \\mathrm{sin} \\zeta_{+}}} \\\\ {{- \\displaystyle \\frac{ \\mathrm{sin} \\zeta_{-}}{ \\Delta} \\mathrm{sin} \\zeta_{+}}}&{{ \\mathrm{cos} \\zeta_{-} \\mathrm{cos} \\zeta_{+}}} \\end{array} \\right].", "UniMER-1M_0000345": "\\tau(q)=(q-1)D_{ \\mathrm{~f~}}", "UniMER-1M_0000079": "{ \\mathcal{K}}( \\hat{M}_{f}, \\hat{M}_{b})= \\mathrm{const} \\; \\int{ \\mathcal{D}}{ \\mathcal{F}}{ \\mathcal{D}}{ \\mathcal{B}} \\mathrm{exp} \\left\\{-{ \\mathcal{W_{F}}}-{ \\mathcal{W_{B}}}-{ \\mathcal{W_{FB}}} \\right\\}", "UniMER-1M_0000440": "{ \\calQ}_{gh}^{ \\prime}= \\ointJ_{z}- \\{{ \\calQ}_{s},S \\}.", "UniMER-1M_0000258": "\\alpha>0", "UniMER-1M_0000344": "U= \\sum_{ \\alpha}V \\int \\mathrm{d} \\boldsymbol{v} \\, \\frac{1}{2}m_{ \\alpha} \\boldsymbol{v}^{2}f_{ \\alpha}( \\boldsymbol{v})+ \\frac{1}{2} \\sum_{ \\alpha} \\sum_{ \\beta}Vn_{ \\alpha}n_{ \\beta} \\int \\mathrm{d} \\boldsymbol{r} \\, \\varphi_{ \\alpha \\beta}( \\boldsymbol{r}) \\,G_{ \\alpha \\beta}( \\boldsymbol{r}) \\,,", "UniMER-1M_0000078": ">", "UniMER-1M_0000439": "t_{0}=t_{so}=1.0,m_{z}=0.3, \\gamma_{ \\downarrow}=0.6,K=2 \\pi/3", "UniMER-1M_0000257": "\\begin{array}{r}{{ \\frac{ \\left[ \\left( \\omega-u_{j}k \\right)^{2}/a_{j}^{2}-k^{2} \\right]^{1/2} \\rho_{0} \\omega^{2}}{ \\left(k^{2}- \\omega^{2}/a_{0}^{2} \\right)^{1/2} \\rho_{j} \\left( \\omega-u_{j}k \\right)^{2}}}} \\\\ {{+ \\mathrm{tan} \\left\\{ \\left[ \\frac{ \\left( \\omega-u_{j}k \\right)^{2}}{a_{j}^{2}}-k^{2} \\right]^{1/2}h/2 \\right\\}=0}} \\end{array}", "UniMER-1M_0000342": "\\rho^{({ \\calS})}= \\frac{-i}{2V({ \\calS})} \\int_{{ \\calS}}{Tr \\left\\{ \\hat{ \\Phi}d \\hat{ \\Phi} \\wedged \\hat{ \\Phi} \\right\\}} \\ ,", "UniMER-1M_0000396": "\\ell( \\boldsymbol{ \\theta}_{C})= \\sum_{ \\ell=1}^{n} \\mathrm{log} \\left[c \\left(F_{11} \\left(X_{11}^{( \\ell)}; \\widehat{ \\boldsymbol{ \\theta}}_{11,n}^{ \\mathrm{IFM}} \\right), . . .,F_{kd_{k}} \\left(X_{kd_{k}}^{( \\ell)}; \\widehat{ \\boldsymbol{ \\theta}}_{kd_{k},n}^{ \\mathrm{IFM}} \\right); \\boldsymbol{ \\theta}_{C} \\right) \\right].", "UniMER-1M_0000077": "2n_{ \\mathrm{ink}}h \\mathrm{cos}{ \\theta_{i}}=m \\lambda,", "UniMER-1M_0000438": "\\ensuremath{ \\mathrm{~ \\boldmath~v~}} \\cdot \\left[ \\nabla( \\ensuremath{ \\mathrm{~ \\boldmath~v~}} \\cdot \\nablas)+ \\frac{1}{ \\gamma} \\left( \\frac{ \\par tial \\mathcal{L}}{ \\par tialT} \\right)_{p} \\nablas+ \\frac{ \\gamma-1}{ \\gamma} \\left( \\frac{ \\par tial \\mathcal{L}}{ \\par tialT} \\right)_{s} \\nabla \\mathrm{ln} p \\right]=0.", "UniMER-1M_0000256": "B", "UniMER-1M_0000020": "\\begin{array}{r}{{4 \\pi+ \\frac{ \\gamma \\varepsilon^{3}}{2}(1+ \\varepsilon+ \\varepsilon^{2})- \\left( \\frac{2 \\pi{h^{ \\ast}}^{4}}{ \\varepsilon^{2}}- \\frac{ \\gamma{h^{ \\ast}}^{2} \\varepsilon^{2}}{2} \\right) \\mathrm{log}{(h^{ \\ast})}+ \\ensuremath{ \\operatorname{O} \\left( \\gamma \\varepsilon^{6},( \\gamma+1){h^{ \\ast}}^{2} \\varepsilon^{2}, \\frac{{h^{ \\ast}}^{4}}{ \\varepsilon^{2}} \\right)}}} \\\\ {{ \\leq4 \\pi+ \\frac{ \\gamma \\varepsilon^{3}}{2}(1+ \\varepsilon+ \\varepsilon^{2})+ \\frac{ \\gamma^{2} \\varepsilon^{6}}{32 \\pi} \\mathrm{log}{ \\left( \\sqrt[]{ \\frac{ \\gamma \\varepsilon^{4}}{8 \\pi}} \\right)}+ \\ensuremath{ \\operatorname{O} \\left( \\gamma( \\gamma+1) \\varepsilon^{6} \\right)}.}} \\end{array}", "UniMER-1M_0000341": "\\mathbb{E}_{ \\tau_{i}} \\mathbb{E}_{t_{i}} \\mathbb{E}_{ \\ell} \\frac{e_{t_{i}, \\ell}^{2}}{ \\|A_{t_{i}} \\|_{2}^{2}}= \\mathbb{E}_{ \\tau_{i}} \\sum_{{t_{i}} \\in \\tau_{i}} \\sum_{g=g_{0}({t_{i}})}^{n_{t_{i}}} \\sum_{ \\ell=0}^{k} \\frac{ \\binom{N_{t_{i}}p_{t_{i}, \\ell}}{g}a_{g, \\ell}^{t_{i}}}{ \\binom{N_{t_{i}}}{n_{t_{i}}}} \\prod_{s \\in \\tau_{i} \\setminus \\{{t_{i}} \\}} \\frac{b_{g}^{s}}{ \\binom{N_{s}}{n_{s}}} \\frac{e_{{t_{i}}, \\ell}^{2}}{ \\|A_{{t_{i}}} \\|_{2}^{2}}", "UniMER-1M_0000076": "H^{2}( \\mathrm{~ \\boldmath~q~}^{(1)}, \\mathrm{~ \\boldmath~q~}^{(2)})= \\frac{1}{2} \\sum_{j} \\left( \\sqrt{q_{j}^{(1)}}- \\sqrt{q_{j}^{(2)}} \\right)^{2},", "UniMER-1M_0000436": "9.5 \\times10^{-6}", "UniMER-1M_0000255": "\\theta \\le \\pi/4", "UniMER-1M_0000340": "K( \\beta=20^{ \\circ})=1.33", "UniMER-1M_0000075": "C_{1}=a(I-P^{T})^{ \\dag}", "UniMER-1M_0000435": "D_{m}", "UniMER-1M_0000254": "n_{k}=2^{-k}", "UniMER-1M_0000339": "\\mathcal{M}=0.4", "UniMER-1M_0000074": "+{ \\frac{1}{2m^{2}}} \\Pi_{ \\psi}^{2}+A_{0} \\Pi_{ \\psi}+{ \\frac{m^{2}}{2}}(( \\par tial_{i} \\psi)^{2}+2A_{i} \\par tial_{i} \\psi).", "UniMER-1M_0000433": "u_{i}", "UniMER-1M_0000253": "\\Phi(Y)", "UniMER-1M_0000338": "2 \\times2", "UniMER-1M_0000073": "T^{2}", "UniMER-1M_0000432": "\\left\\{ \\begin{array}{ll}{{{ \\alpha_{2}=F_{1}( \\alpha_{0}, \\alpha_{1}, \\phi;B,K)}}} \\\\ {{{ \\beta_{2}=F_{2}( \\alpha_{0}, \\alpha_{1}, \\phi;B,K)}}} \\end{array} \\right.,", "UniMER-1M_0000252": "\\existsx(x=y)", "UniMER-1M_0000337": "w={ \\frac{v \\cdotp^{ \\prime}}{m_{K^{*}}}} \\ ,", "UniMER-1M_0000072": "\\sigma^{ \\perp}=61.2 \\mathrm{~m~}", "UniMER-1M_0000019": "15.5H \\times4.5H \\times4H=6.2 \\times1.8 \\times1.6m^{3}", "UniMER-1M_0000431": "E", "UniMER-1M_0000251": "2g_{ \\mu \\nu}(X^{ \\alpha}) \\par tial_{a} \\par tial^{a}X^{ \\mu}+2 \\par tial_{a}X^{ \\mu} \\par tial^{a}X^{ \\rho} \\par tial_{ \\rho}g_{ \\mu \\nu}(X^{ \\alpha})+ \\par tial_{a}X^{ \\mu} \\par tial^{a}X^{ \\eta} \\par tial_{ \\nu}g_{ \\mu \\eta}(X^{ \\alpha})=0", "UniMER-1M_0000336": "2[ \\dot{x}^{2} \\acute{x}^{AA^{ \\prime}}-( \\dot{x} \\acute{x}) \\dot{x}^{AA^{ \\prime}}]=v \\bar{ \\pi}^{A} \\bar{ \\pi}_{B} \\dot{x}^{BA^{ \\prime}}+ \\bar{v} \\pi^{A^{ \\prime}} \\pi_{B^{ \\prime}} \\dot{x}^{AB^{ \\prime}}.", "UniMER-1M_0000071": "\\frac{ \\par tial \\langle \\hat{ \\mathcal{L}} \\rangle}{ \\par tial \\mu}=( \\langle \\hat{A} \\rangle-a) \\equiv0,", "UniMER-1M_0000429": "\\mathscr{R}_{P}( \\omega)= \\frac{ \\mathscr{R}_{T}}{G} \\frac{1}{ \\sqrt{1+ \\omega^{2} \\tau_{th}^{2}}} \\,,", "UniMER-1M_0000250": "\\mathrm{K}_{ \\alpha}", "UniMER-1M_0000335": "a \\approx319", "UniMER-1M_0000070": "g({ \\mathsf{d}})=0", "UniMER-1M_0000428": "(Z_{ \\bf m}^{N})_{ \\bf kl} \\equiv \\intd^{2} \\sigmaf_{ \\bf k}^{*}( \\sigma) \\,Z_{ \\bf m}^{N}( \\sigma) \\,f_{ \\bf l}( \\sigma)=-2i \\mathrm{sin} \\left( \\frac{2 \\pi}{N}{ \\bf m \\timesl} \\right) \\delta_{{ \\bf m} \\,({ \\bf k-l})} \\,.", "UniMER-1M_0000248": "M= \\left( \\begin{array}{ll}{{{m-{ \\frac{g^{2}}{4 \\pir}}}}}&{{{{ \\frac{g^{2}}{4 \\pir}}}}} \\\\ {{{{ \\frac{g^{2}}{4 \\pir}}}}}&{{{m-{ \\frac{g^{2}}{4 \\pir}}}}} \\end{array} \\right) \\,.", "UniMER-1M_0000334": "^{3}", "UniMER-1M_0000069": "\\epsilon_{ \\mu \\nu}p^{ \\mu} \\Gamma^{ \\nu \\lambda( \\beta)}(p^{0},p^{1})=0= \\epsilon_{ \\mu \\lambda}p^{ \\mu} \\Gamma^{ \\nu \\lambda( \\beta)}(p^{0},p^{1})", "UniMER-1M_0000427": "E_{ \\mathrm{a}, \\mathrm{V}_{2}}^{ \\mathrm{~d~i~s~}}", "UniMER-1M_0000247": "-424", "UniMER-1M_0000333": "m_{0} \\left(B,V \\right)= \\frac{1}{c^{2}} \\int_{V} \\frac{1}{2 \\mu_{0}} \\left(B^{2} \\right)dV", "UniMER-1M_0000018": "d_{n}", "UniMER-1M_0000068": "\\mathrm{sin}( \\theta)=-X_{3}", "UniMER-1M_0000426": "\\begin{array}{rl}{{ \\mathrm{V}_{ \\mathrm{MCS}}^{ \\Omega, \\, \\mathrm{QPD}_{2}}}}&{{= \\mathrm{E}_{0}^{2} \\frac{ \\lambda \\deltaz_{0}}{2 \\piw_{0}^{2}}m \\Big(-A_{n} \\beta_{(n,m),(n+2,m)}+A_{m} \\beta_{(n,m),(n,m+2)}}} \\end{array}", "UniMER-1M_0000332": "(p_{1}+p_{2}+p_{3}+ . . .+p_{k})^{n}", "UniMER-1M_0000246": "\\theta", "UniMER-1M_0000067": "s \\gg \\lambda", "UniMER-1M_0000425": "\\frac{ \\par tialV_{n}(t)}{ \\par tialt}= \\Sigma_{i=1}^{Q} \\frac{ \\par tialV_{n}(t)}{ \\par tialf_{i}} \\Omega_{i}( \\vec{f}(t)),", "UniMER-1M_0000331": "M_{ \\nu_{LR}}= \\left( \\begin{array}{ccc}{{{0}}}&{{{0}}}&{{{a}}} \\\\ {{{0}}}&{{{b}}}&{{{c}}} \\\\ {{{a}}}&{{{c}}}&{{{1}}} \\end{array} \\right)d", "UniMER-1M_0000245": "|z|=1", "UniMER-1M_0000066": "\\Phi={ \\left\\{ \\begin{array}{ll}{{ \\mathrm{tan}{ \\frac{ \\pi \\alpha}{2}}}}&{{{ \\mathrm{if~}} \\alpha \\neq1}} \\\\ {{-{ \\frac{2}{ \\pi}} \\mathrm{log}|c \\,t|}}&{{{ \\mathrm{if~}} \\alpha=1}} \\end{array} \\right.}", "UniMER-1M_0000424": "\\frac{f}{M_{P}}> \\frac{g_{1}}{4 \\pi} \\sqrt{ \\left| \\frac{5c_{3}}{c_{2}} \\right|} \\;.", "UniMER-1M_0000244": "\\Gamma(Z \\rightarrowh_{1}h_{2})={ \\frac{M_{Z}}{16 \\pi}}g_{Zh_{1}h_{2}}^{2} \\lambda^{ \\frac{3}{2}}(1,x_{1},x_{2}),", "UniMER-1M_0000330": "f(S)", "UniMER-1M_0000065": "R_{1}", "UniMER-1M_0000423": "x \\ll1", "UniMER-1M_0000017": "{ \\calZ}_{B}=Tr \\;e^{- \\beta \\varepsilon_{ \\kappa}( \\overline{{{{ \\Phi}}}}_{1, \\kappa} \\Phi_{1, \\kappa}+ \\overline{{{{ \\Phi}}}}_{2, \\kappa} \\Phi_{2, \\kappa})}e^{- \\beta \\mu(N_{1, \\kappa}+N_{2, \\kappa})},", "UniMER-1M_0000243": "\\hat{ \\rho}", "UniMER-1M_0000329": "m^{3} \\ kg(M)^{-1} \\ d^{-1}", "UniMER-1M_0000064": "\\pm2 \\", "UniMER-1M_0000422": "^-1", "UniMER-1M_0000242": "v_{i}+(1+r)c_{i}", "UniMER-1M_0000063": "\\varphi_{1}", "UniMER-1M_0000328": "xoy", "UniMER-1M_0000421": "2 \\times1", "UniMER-1M_0000241": "\\tau_{M}^{ \\mathrm{ex}} \\omega_{E}=1", "UniMER-1M_0000062": "F= \\frac{v_{x}^{2}+v_{y}^{2}}{2g}.", "UniMER-1M_0000327": "F_{1}", "UniMER-1M_0000420": "\\par tialT/ \\par tialp_{ \\mu}= \\delta^{ \\alpha \\beta}( \\delta_{ \\alpha}^{ \\mu}p_{ \\beta}+p_{ \\alpha} \\delta_{ \\beta}^{ \\mu})/2=p^{ \\mu}", "UniMER-1M_0000240": "n_{ \\alpha}+n_{x_{p}}+n_{u_{p}}=3", "UniMER-1M_0000061": "\\leftarrowc_{n-1} \\cdot", "UniMER-1M_0000326": "\\begin{array}{rl}&{{ \\frac{ \\par tial \\left( \\epsilon_{s} \\rho_{s} \\right)}{ \\par tialt}+ \\nabla_{x} \\cdot \\left( \\epsilon_{s} \\rho_{s} \\textbf{U}_{s} \\right)=0,}} \\\\ &{{ \\frac{ \\par tial \\left( \\epsilon_{s} \\rho_{s} \\textbf{U}_{s} \\right)}{ \\par tialt}+ \\nabla_{x} \\cdot \\left( \\epsilon_{s} \\rho_{s} \\textbf{U}_{s} \\textbf{U}_{s}+p_{s} \\mathbb{I} \\right)= \\frac{ \\epsilon_{s} \\rho_{s} \\left( \\textbf{U}_{g}- \\textbf{U}_{s} \\right)}{ \\tau_{st}}- \\epsilon_{s} \\nabla_{x}p_{g}+ \\epsilon_{s} \\rho_{s} \\textbf{G},}} \\\\ &{{ \\frac{ \\par tial \\left( \\epsilon_{s} \\rho_{s}E_{s} \\right)}{ \\par tialt}+ \\nabla_{x} \\cdot \\left( \\left( \\epsilon_{s} \\rho_{s}E_{s}+p_{s} \\right) \\textbf{U}_{s} \\right)= \\frac{ \\epsilon_{s} \\rho_{s} \\textbf{U}_{s} \\cdot \\left( \\textbf{U}_{g}- \\textbf{U}_{s} \\right)}{ \\tau_{st}}- \\frac{3p_{s}}{ \\tau_{st}}- \\epsilon_{s} \\textbf{U}_{s} \\cdot \\nabla_{x}p_{g}+ \\epsilon_{s} \\rho_{s} \\textbf{U}_{s} \\cdot \\textbf{G}.}} \\end{array}", "UniMER-1M_0000016": "x", "UniMER-1M_0000419": "a_{ \\mathrm{c}}^{2}=B^{2}/ \\mu \\rho_{ \\mathrm{c}}=(1+ \\chi)a^{2}", "UniMER-1M_0000239": "\\begin{array}{rl}{{ \\sum_{ \\theta \\inE}}}&{{C \\delta^{-2 \\epsilon} \\sum_{B \\in \\mathcal{K}} \\mu \\left(B \\capH_{ \\theta} \\left(K, \\left( \\frac{ \\delta}{ \\bar{ \\delta}} \\right)^{- \\sigma},[4 \\delta,4 \\bar{ \\delta}] \\right) \\right)}} \\\\ &{{ \\stackrel{=}(4 \\bar{ \\delta})^{t} \\sum_{B \\in \\mathcal{K}}C \\delta^{-2 \\epsilon} \\sum_{ \\theta \\inE} \\mu_{B} \\left(B(1) \\capH_{ \\theta} \\left(T_{B}(K), \\Delta^{- \\sigma},[ \\Delta,1] \\right) \\right)}} \\\\ &{{ \\stackrel{ \\leq}(4 \\bar{ \\delta})^{t} \\sum_{B \\in \\mathcal{K}}C \\delta^{-2 \\epsilon} \\delta^{10 \\epsilon}|E| \\stackrel{ \\lesssim} \\delta^{7 \\epsilon}|E|.}} \\end{array}", "UniMER-1M_0000059": "x={ \\frac{-2t^{2}-1}{-t^{2}-1}} \\qquad \\ dx={ \\frac{2t}{(-t^{2}-1)^{2}}} \\, \\ dt,", "UniMER-1M_0000325": "\\Delta", "UniMER-1M_0000418": "m=N+1", "UniMER-1M_0000238": "\\alpha_{ \\mathrm{inf}}", "UniMER-1M_0000058": "\\operatorname{archavercos}(y)=2 \\mathrm{arccos} \\left({ \\sqrt{y}} \\right)= \\mathrm{arccos} \\left(2y-1 \\right)", "UniMER-1M_0000324": "x", "UniMER-1M_0000417": "F_{0}=0, \\quad F_{1}=1,", "UniMER-1M_0000237": "\\begin{array}{r}{{r_{ \\theta}(X, \\tilde{X}):= \\frac{ \\mu_{ \\mathrm{aug}}( \\tilde{X})p_{ \\theta}(X \\mid \\tilde{X}^{p})}{ \\mu_{ \\mathrm{aug}}(X)p_{ \\theta}( \\tilde{X} \\midX^{p})}.}} \\end{array}", "UniMER-1M_0000057": "\\delta", "UniMER-1M_0000323": "N_{2}", "UniMER-1M_0000416": "{D_{eff}^{N,top}}/{D_{eff}^{N,bottom}}<1", "UniMER-1M_0000236": "\\GammaZ", "UniMER-1M_0000015": "\\begin{array}{rl}{{( \\pi_{C|D}- \\pi_{D|D})q_{D|D}=}}&{{ \\left[-c+w_{I}b+ \\frac{1-w_{I}}{k}(k-1)q_{C|C}b- \\frac{1-w_{I}}{k}(k-1)q_{C|D}b \\right] \\left(1- \\frac{k-2}{k-1}p_{C} \\right)}} \\\\ {{=}}&{{ \\left(-c+w_{I}b+ \\frac{1-w_{I}}{k}b \\right) \\left(1- \\frac{k-2}{k-1}p_{C} \\right),}} \\end{array}", "UniMER-1M_0000056": "a_{ \\mu}^{ \\mathrm{SUSY}}={ \\frac{ \\mathrm{tan} \\beta}{48 \\pi}}{ \\frac{m_{ \\mu}^{2}}{M_{ \\mathrm{SUSY}}^{2}}}(5 \\alpha_{2}+ \\alpha_{1})", "UniMER-1M_0000322": "N_{p}", "UniMER-1M_0000234": "f \\left({ \\frac{1}{n}},{ \\frac{1}{n}} \\right)", "UniMER-1M_0000415": "t>0", "UniMER-1M_0000055": "\\mu \\mathrm{m}", "UniMER-1M_0000321": "^{45}", "UniMER-1M_0000233": "R=0.9", "UniMER-1M_0000412": "\\beta_{N}(u,v,w)={ \\frac{Q_{N}(u)(v-w)+Q_{N}(v)(w-u)+Q_{N}(w)(u-v)}{(u-v)(v-w)(w-u)}}.", "UniMER-1M_0000053": "\\mathrm{mwnv}= \\int_{-1}^{1}u_{2}(0,x_{2})dx_{2}", "UniMER-1M_0000320": "\\varepsilon_{ \\theta}", "UniMER-1M_0000231": "{7.69 \\times10^{-3}}", "UniMER-1M_0000411": "{ \\bf f}({ \\bf n})={ \\boldsymbol{ \\eta}^{ \\mathrm{min}}[{ \\bf n}]-{ \\bf n}", "UniMER-1M_0000052": "\\overline{{{ \\vx}}}_{k}^{f}= \\frac{1}{N_{e}} \\sum_{n=1}^{N_{e}} \\vx_{k}^{f,(n)}", "UniMER-1M_0000319": "G_{n}", "UniMER-1M_0000230": "G \\rightarrow0.0", "UniMER-1M_0000014": "(x,y) \\in \\left[-10,10 \\right]", "UniMER-1M_0000409": "P_{ \\mathrm{{s}}}^{ \\mathrm{{sub}}}", "UniMER-1M_0000051": "a_{P}a_{Q}^{ \\dagger}=S_{PQ}-a_{Q}^{ \\dagger}a_{P},", "UniMER-1M_0000318": "4.03 \\", "UniMER-1M_0000229": "\\zeta \\rightarrow-1/2", "UniMER-1M_0000408": "C_{n}", "UniMER-1M_0000317": "I", "UniMER-1M_0000050": "I-V", "UniMER-1M_0000228": "\\it y^{(l)}=W_{ \\mathrm{~t~}}^{(l)}{h^{(l)}}", "UniMER-1M_0000407": "x^{2}-dy^{2}= \\pm1", "UniMER-1M_0000316": "l", "UniMER-1M_0000049": "\\begin{array}{rlr}{{ \\sum_{i}P_{i, \\nu}dQ_{i, \\nu}}}&{{=}}&{{ \\frac{1}{2} \\left[(P_{k}-P_{-k})d(Q_{k}-Q_{-k})-(Q_{k}+Q_{-k})d(P_{k}+P_{-k}) \\right]=}} \\end{array}", "UniMER-1M_0000227": "\\alpha(1), . . ., \\alpha(n)", "UniMER-1M_0000406": "\\DeltaE= \\sqrt{2} \\cdot \\DeltaE_{ \\mathrm{beam}}=0.848", "UniMER-1M_0000013": "j", "UniMER-1M_0000315": "\\alpha", "UniMER-1M_0000226": "\\begin{array}{rlr}&&{{1,3,4,4,6,4,6,8,10,10,12,10,15,11, \\quad \\quad \\underline{{{19}}}, \\mathbf{16}, \\underline{{{19}}}, \\mathbf{21}, \\underline{{{17}}}, \\mathbf{21}, \\underline{{{19}}}, \\mathbf{24},}} \\\\ &&{{ \\underline{{{19}}}, \\mathbf{29}, \\underline{{{17}}}, \\mathbf{29}, \\underline{{{19}}}, \\mathbf{32}, \\underline{{{19}}}, \\mathbf{37}, \\underline{{{17}}}, \\mathbf{37}, \\underline{{{19}}}, \\mathbf{40}, \\underline{{{19}}}, \\mathbf{45}, \\underline{{{17}}}, \\mathbf{45}, \\underline{{{19}}}, \\mathbf{48}, \\underline{{{19}}}, \\mathbf{53}, \\underline{{{17}}}.}} \\end{array}", "UniMER-1M_0000048": "^*", "UniMER-1M_0000405": "1.11 \\; \\times10^{6} \\; \\mathrm{~m~m~}^{2}/ \\mathrm{~s~}^{2}", "UniMER-1M_0000314": "\\tilde{C}= \\frac{1}{2 \\pi} \\int_{0}^{1}dx \\mathrm{ln} \\left| \\frac{ \\vartheta_{1}(x \\mid \\tau)}{ \\vartheta_{1}^{ \\prime}(0 \\mid \\tau)} \\right|+ \\frac{| \\tau|}{12}.", "UniMER-1M_0000225": "{ \\vec{a}}_{ \\theta}(t)=R{ \\frac{d \\omega}{dt}}{ \\hat{u}}_{ \\theta}(t)={ \\frac{dR \\omega}{dt}}{ \\hat{u}}_{ \\theta}(t)={ \\frac{d \\left|{ \\vec{v}}(t) \\right|}{dt}}{ \\hat{u}}_{ \\theta}(t) \\ .", "UniMER-1M_0000047": "( \\mathfrak{n}^{+} \\otimes \\mathbb{C}[t])v_{ \\pmb{ \\xi}}=0, \\quad \\,(h_{ \\alpha_{i}} \\otimest^{s})v_{ \\pmb{ \\xi}}= \\lambda(h_{ \\alpha_{i}}) \\delta_{s,0}v_{ \\pmb{ \\xi}}, \\quad \\,(x_{ \\alpha_{i}}^{-} \\otimes1)^{ \\lambda(h_{ \\alpha_{i}})+1}v_{ \\pmb{ \\xi}}=0, \\quad \\, \\mathrm{for} \\,i=1,2,", "UniMER-1M_0000404": "M", "UniMER-1M_0000313": "\\Psi_{i}^{ \\prime} \\leftarrow \\tau \\Psi_{i}+(1- \\tau) \\Psi_{i}^{ \\prime}", "UniMER-1M_0000046": "G_{xx}(0,0)=g( \\rhoR^{2}, \\infty)/N", "UniMER-1M_0000403": "\\begin{array}{rl}{{m \\Dot{u}_{x}}}&{{=L \\mathrm{cos}( \\mu) \\mathrm{sin}( \\gamma) \\mathrm{cos}( \\psi)-L \\mathrm{sin}( \\mu) \\mathrm{sin}( \\psi)-D \\mathrm{cos}( \\gamma) \\mathrm{cos}( \\psi),}} \\\\ {{m \\Dot{u}_{y}}}&{{=L \\mathrm{cos}( \\mu) \\mathrm{sin}( \\gamma) \\mathrm{sin}( \\psi)+L \\mathrm{sin}( \\mu) \\mathrm{cos}( \\psi)-D \\mathrm{cos}( \\gamma) \\mathrm{sin}( \\psi),}} \\\\ {{m \\Dot{u}_{z}}}&{{=L \\mathrm{cos}( \\mu) \\mathrm{cos}( \\gamma)+D \\mathrm{sin}( \\gamma)-mg,}} \\\\ {{ \\Dot{ \\vec{r}}}}&{{= \\mathbf{u},}} \\end{array}", "UniMER-1M_0000224": "992", "UniMER-1M_0000312": "f_{A}=|k_{ \\par allel}V_{A}|/(2 \\pi)", "UniMER-1M_0000045": "i", "UniMER-1M_0000402": "n", "UniMER-1M_0000223": "\\hat{G}^{0}(t,t^{ \\prime}; \\{ \\sigma \\})= \\frac{1}{2}[G^{0}(t,t^{ \\prime}; \\{ \\sigma_{p} \\})+G^{0}(t,t^{ \\prime}; \\{- \\sigma_{p} \\})]", "UniMER-1M_0000311": "2 \\kappa", "UniMER-1M_0000044": "F_{r{ \\tilde{ \\theta}}}^{1}=- \\, \\frac{1}{2 \\lambda} \\,{ \\dot{a}}~~,~~F_{r{ \\tilde{ \\varphi}}}^{2}= \\frac{1}{2 \\lambda} \\,{ \\mathrm{sin}{ \\widetilde{ \\theta}} \\,{ \\dot{a}}}~~,~~F_{{ \\tilde{ \\theta}}{ \\tilde{ \\varphi}}}^{3}= \\frac{1}{2 \\lambda} \\,{ \\mathrm{sin}{ \\widetilde{ \\theta}}} \\left(1-a^{2} \\right)~~.", "UniMER-1M_0000401": "N_{c}=2", "UniMER-1M_0000222": "\\gamma= \\omega^{2}/a^{2}", "UniMER-1M_0000012": "\\ddot{ \\mathrm{~o~}}", "UniMER-1M_0000310": "n", "UniMER-1M_0000399": "f_{XY|Z}(x,y|z)=f_{X|Z}(x|z) \\cdotf_{Y|Z}(y|z)", "UniMER-1M_0000221": "q", "UniMER-1M_0000043": "\\leq", "UniMER-1M_0000397": "{ \\begin{array}{rl}{{ \\|f \\|^{2}}}&{{= \\int_{0}^{1} \\vartheta^{2} \\,d \\vartheta={ \\frac{1}{3}}}} \\\\ {{ \\langlef,e_{k} \\rangle}}&{{= \\int_{0}^{1} \\varthetae^{-2 \\pi \\imathk \\vartheta} \\,d \\vartheta={ \\Biggl\\{ \\begin{array}{ll}{{{ \\frac{1}{2}},}}&{{k=0}} \\\\ {{-{ \\frac{1}{2 \\pi \\imathk}}}}&{{k \\neq0,}} \\end{array}}}} \\end{array}}", "UniMER-1M_0000309": "38.0", "UniMER-1M_0000220": "\\begin{array}{r}{{d_{2}-d_{1}= \\frac{d_{1} \\,d_{2}}{c_{1}} \\,B_{0}^{2} \\;.}} \\end{array}", "UniMER-1M_0000042": "{ \\begin{array}{rl}{{{F^{ \\alpha \\beta}}_{; \\beta}}}&{{=0}} \\\\ {{F_{[ \\alpha \\beta; \\gamma]}}}&{{={ \\frac{1}{3}} \\left(F_{ \\alpha \\beta; \\gamma}+F_{ \\beta \\gamma; \\alpha}+F_{ \\gamma \\alpha; \\beta} \\right)={ \\frac{1}{3}} \\left(F_{ \\alpha \\beta, \\gamma}+F_{ \\beta \\gamma, \\alpha}+F_{ \\gamma \\alpha, \\beta} \\right)=0.}} \\end{array}}", "UniMER-1M_0000671": "\\Lambda= \\mathcal{S}(s) \\mathcal{R}^{-1}(z).", "UniMER-1M_0000219": "i", "UniMER-1M_0000583": "H", "UniMER-1M_0000670": "{R}_{{mp}}", "UniMER-1M_0000218": "40", "UniMER-1M_0000582": "y^{+} \\leqd_{v}^{+}", "UniMER-1M_0000669": "\\left\\langle \\frac{1}{2} \\sigma_{kj} \\par tial_{k}( \\sigma_{ij} \\par tial_{i}Q)+f_{i} \\par tial_{i}Q+ \\phi-L \\right\\rangle \\geq0 \\ .", "UniMER-1M_0000217": "- \\alpha", "UniMER-1M_0000581": "\\begin{array}{rl}{{ \\vertc({ \\ensuremath{ \\mathcal{A}}}, \\boldsymbol{ \\omega}^{ \\star}) \\vert}}&{{= \\left\\vert \\vertf \\verte^{ \\jmath \\theta \\left(f \\right)}+ \\sum_{i \\in{ \\ensuremath{ \\mathcal{A}}}} \\etae^{- \\jmath \\left( \\theta \\left(g_{i} \\right)- \\theta \\left(f \\right) \\right)} \\vertg_{i} \\verte^{ \\jmath \\theta \\left(g_{i} \\right)} \\right.}} \\\\ &{{+ \\left. \\sum_{i \\in{ \\ensuremath{ \\mathcal{A}}}^{c}}e^{- \\jmath \\left( \\theta \\left(g_{i} \\right)- \\theta \\left(f \\right) \\right)} \\vertg_{i} \\verte^{ \\jmath \\theta \\left(g_{i} \\right)} \\right\\vert}} \\\\ &{{= \\left\\vert \\vertf \\vert+ \\left( \\sum_{i \\in{ \\ensuremath{ \\mathcal{A}}}} \\eta \\vertg_{i} \\vert+ \\sum_{i \\in{ \\ensuremath{ \\mathcal{A}}}^{c}} \\vertg_{i} \\vert \\right) \\right\\vert.}} \\end{array}", "UniMER-1M_0000667": "x", "UniMER-1M_0000216": "i \\getsi+1", "UniMER-1M_0000580": "D", "UniMER-1M_0000666": "J_{b}", "UniMER-1M_0000215": "J_{s} \\proptoe^{ \\frac{(1-1/( \\alpha+1)V}{V_{0}}}", "UniMER-1M_0000579": "\\alpha_{l}^{l/2}k_{x}^{l} \\to \\varepsilon_{F}", "UniMER-1M_0000665": "\\theta", "UniMER-1M_0000214": "a", "UniMER-1M_0000577": "E_{C}^{(2) \\;reg}=- \\frac{M_{0}}{4 \\sqrt{ \\alpha}}- \\frac{M_{0}^{2}R}{8 \\pi \\alpha} \\Gamma(-1)- \\frac{M_{0}}{2 \\pi \\sqrt{ \\alpha}} \\sum_{n=1}^{ \\infty}n^{-1}K_{1} \\left( \\frac{2nM_{0}R}{ \\sqrt{ \\alpha}} \\right).", "UniMER-1M_0000664": "N", "UniMER-1M_0000213": "\\omega_{y}", "UniMER-1M_0000576": "m \\ge2", "UniMER-1M_0000663": "\\begin{array}{r}{{ \\hat{ \\mathbf{x}}_{t_{i}}^{ \\mathrm{noise}}= \\mathbf{x}_{t_{i}}+ \\sqrt{ \\Deltat} \\,g(t_{i}) \\,z_{t_{i}}+( \\mathbf{x}_{t_{i+1}}- \\Deltat \\, \\mathcal{P}( \\mathbf{x}_{t_{i+1}})- \\mathbf{x}_{t_{i}}),}} \\end{array}", "UniMER-1M_0000212": "c", "UniMER-1M_0000662": "90 \\, \\", "UniMER-1M_0000575": "A<0", "UniMER-1M_0000211": "0.49", "UniMER-1M_0000661": "j", "UniMER-1M_0000574": "\\Delta \\mathbf{z}^{ \\pm}= \\mathbf{z}^{ \\pm}( \\mathbf{x}+ \\Delta \\mathbf{x})- \\mathbf{z}^{ \\pm}( \\mathbf{x})", "UniMER-1M_0000210": "( \\leftarrow)", "UniMER-1M_0000573": "\\psi(x)", "UniMER-1M_0000660": "250", "UniMER-1M_0000209": "1.43", "UniMER-1M_0000572": "\\mathbf{D}= \\varepsilon_{0} \\mathbf{E}+ \\mathbf{P}", "UniMER-1M_0000659": "f(z)", "UniMER-1M_0000208": "\\mathbf{d}_{i}^{ \\mathrm{~d~y~n~}}", "UniMER-1M_0000571": "ds^{2}=-e^{2 \\Phi(r)}dt^{2}+ \\frac{dr^{2}}{1- \\frac{b(r)}{r}}+r^{2} \\left(d \\theta^{2}+ \\mathrm{sin}^{2} \\thetad \\phi^{2} \\right)", "UniMER-1M_0000658": "f(p_{1},p_{2},p_{3};l)= \\mathrm{exp} \\left(-l \\ D^{2}(p_{1},p_{2},p_{3}) \\right)", "UniMER-1M_0000207": "E_{F}=10 \\mueV", "UniMER-1M_0000570": "[0,T]", "UniMER-1M_0000657": "\\theta", "UniMER-1M_0000205": "\\rho_{y}(y)", "UniMER-1M_0000569": "\\begin{array}{rl}{{J_{2}^{i}}}&{{= \\int_{0}^{T} \\int_{D} \\mathbb{P}^{ \\eta \\rightarrow \\xi} \\left[1_{ \\{t< \\zeta( \\psi \\circ \\tau_{T}) \\}}Q_{j}^{i}( \\psi,T;T-t)F^{j}( \\psi(T-t),T-t) \\right]p_{b}(0, \\eta,T, \\xi) \\mathrm{d} \\eta \\mathrm{d} t}} \\end{array}", "UniMER-1M_0000656": "\\eta_{ \\mathrm{{th}}}=1-{ \\bigg(}{ \\frac{p_{2}}{p_{1}}}{ \\bigg)}^{ \\frac{1- \\gamma}{ \\gamma}}", "UniMER-1M_0000204": "16:1", "UniMER-1M_0000568": "N=1", "UniMER-1M_0000655": "u,v", "UniMER-1M_0000203": "du/d(k^{ \\perp}/m)", "UniMER-1M_0000567": "H_{ \\mathrm{~T~O~F~}}( \\cdot)", "UniMER-1M_0000654": "F \\operatorname{lim}_{x \\rightarrowx_{o}} \\frac{g(f_{x})-g(f_{x_{o}})}{f(x)-f(x_{o})}=F \\operatorname{lim}_{x \\rightarrowx_{o}} \\frac{S_{F^{ \\prime},a_{o}}^{ \\alpha}(x)-S_{F^{ \\prime},a_{o}}^{ \\alpha}(x_{o})}{f(x)-f(x_{o})} \\,.", "UniMER-1M_0000202": "\\begin{array}{rl}{{ \\Deltat \\d_{t}S_{ \\mathrm{B}}(t)}}&{{ \\approxS_{ \\mathrm{B}}(t+ \\Deltat)-S_{ \\mathrm{B}}(t)={k_{ \\mathrm{B}}}D_{KL} \\left(P_{N}(t+ \\Deltat) \\left| \\prod_{n}P_{1}(t+ \\Deltat) \\right. \\right)-{k_{ \\mathrm{B}}}D_{KL} \\left(P_{N}(t) \\left| \\prod_{n}P_{1}(t) \\right. \\right) \\,,}} \\end{array}", "UniMER-1M_0000566": "0.4121^{k}", "UniMER-1M_0000653": "( \\deltaB)", "UniMER-1M_0000201": "c_{1}F_{ab}F^{ab}+c_{2}R^{2}+c_{3}R_{ab}R^{ab}+c_{4}R_{abcd}R^{abcd}", "UniMER-1M_0000565": "\\Omega", "UniMER-1M_0000652": "0< \\beta<1", "UniMER-1M_0000200": "p \\doteq1/2", "UniMER-1M_0000564": "_{14}", "UniMER-1M_0000651": "h_{v}=[1+P(v,iD)]h_{v}^{(p)} \\leftrightarrowh_{v}^{(p)}=[1+Q(v,iD)]h_{v} \\,,", "UniMER-1M_0000199": "f_{k}=| \\psi_{k}^{ \\mathrm{vb}}|| \\psi_{k}^{ \\mathrm{cb}}|", "UniMER-1M_0000563": "We_{j}= \\left( \\frac{U_{j}}{U_{c}} \\right)^{2} \\sim \\left( \\frac{Z_{c}}{R} \\right)^{2} \\left( \\frac{R}{r_{b}} \\right)^{4},", "UniMER-1M_0000650": "X_{0}=X^{*}- \\frac{Z^{*}}{(dz/dx)_{-}}=X^{*} \\left[1+ \\frac{2( \\tau_{z}/ \\tau_{x})^{2}}{(1+8 \\tau_{z}/ \\tau_{x})^{1/2}+1} \\right].", "UniMER-1M_0000197": "\\psi_{j}^{( \\nu)}=a_{ \\nu} \\beta^{j}+b_{ \\nu} \\beta^{-j}", "UniMER-1M_0000562": "\\cal{L}", "UniMER-1M_0000649": "\\left\\{c^{i},c^{j} \\right\\} \\ = \\  \\left\\{b_{i},b_{j} \\right\\} \\ = \\ 0, \\quad \\left\\{c^{i},b_{j} \\right\\} \\ = \\  \\delta_{j}^{i} \\ .", "UniMER-1M_0000196": "10ns", "UniMER-1M_0000561": "e^{-2M}", "UniMER-1M_0000648": "\\sigma_{ \\alpha}", "UniMER-1M_0000195": "A_{2}^{(0)}(s)=-2 \\lambda \\frac{s}{s-M^{2}+iM \\Gamma \\, \\theta(s)}", "UniMER-1M_0000560": "V=", "UniMER-1M_0000647": "6d_{3/2}^{4}d_{5/2}^{5}(^{2}D_{ \\frac{5}{2}})", "UniMER-1M_0000194": "l", "UniMER-1M_0000559": "N_{f}", "UniMER-1M_0000646": "\\left({ \\frac{x^{-}}{x^{+}}} \\right)^{2}F(x^{-},x^{+})={ \\frac{N}{k}} \\sum_{n}{ \\frac{M_{n}^{4}}{32 \\pi^{2}}}{ \\frac{(k-2n)^{2}}{k^{2}}}K_{4}(M_{n}r)", "UniMER-1M_0000193": "\\delta{ \\calL}= \\frac{-1}{16{ \\pi}^{2}} \\sum_{ \\alpha} \\intd^{2} \\theta \\frac{W^{ \\alpha}W_{ \\alpha}}{4} \\left([c(G_{ \\alpha})- \\sum_{r_{ \\omega}}T(r_{ \\omega})]g( \\phi)+2 \\sum_{r_{ \\omega}}T(r_{ \\omega}) \\mathrm{log} deth_{ \\alpha \\beta}( \\phi,{ \\bar{ \\phi}}) \\right)+h.c", "UniMER-1M_0000556": "{ \\calA}^{ \\prime}(y_{ \\pm})~{ \\mathrm{are~finite,~and}}~{ \\calA}^{ \\prime}(y_{+})-{ \\calA}^{ \\prime}(y_{-})=0~.", "UniMER-1M_0000645": "2^{18}", "UniMER-1M_0000192": "\\frac{ \\par tial \\mathbf{u}^{ \\prime}}{ \\par tialt}", "UniMER-1M_0000555": "-0.25", "UniMER-1M_0000644": "\\begin{array}{r}{{ \\mathrm{max}_{(i,l,k) \\in \\mathbb{B}} \\operatorname{sup}_{t \\in \\mathcal{T}}| \\beta_{k}^{i,l}(t)- \\gamma_{0}^{i,l}(t)|=O( \\chi^{h}+h/n), \\quad \\mathrm{max}_{(i,l,k) \\in \\mathbb{B}} \\operatorname{sup}_{t \\in \\mathcal{T}}| \\beta_{h}^{i,l}(t)-2 \\gamma_{0}^{i,l}(t)|=O( \\chi^{h}+h/n).}} \\end{array}", "UniMER-1M_0000191": "K", "UniMER-1M_0000554": "B", "UniMER-1M_0000642": "\\omega", "UniMER-1M_0000190": "\\zeta<0", "UniMER-1M_0000553": "R_{d}", "UniMER-1M_0000641": "N_{A}", "UniMER-1M_0000189": "k", "UniMER-1M_0000552": "\\vec{q}^{ \\, \\, \\gamma}= \\left( \\! \\! \\begin{array}{c}{{{2 \\sum_{q}q^{ \\gamma}}}} \\\\ {{{G^{ \\, \\gamma}}}} \\end{array} \\! \\! \\right)= \\vec{q}_{PL}^{ \\, \\, \\gamma}+ \\vec{q}_{had}^{ \\, \\, \\gamma} \\: \\:,", "UniMER-1M_0000640": "\\frac{1}{4}Z_{talbot}= \\frac{d_{lat}^{2}}{2 \\lambda}", "UniMER-1M_0000188": "P(X=0)={ \\frac{ \\binom{100-5}{45}}{ \\binom{100}{45}}}={ \\frac{ \\frac{95!}{50!}}{ \\frac{100!}{55!}}}={ \\frac{95 \\times94 \\times . . . \\times51}{100 \\times99 \\times . . . \\times56}}={ \\frac{55 \\times54 \\times53 \\times52 \\times51}{100 \\times99 \\times98 \\times97 \\times96}}=4.6 \\", "UniMER-1M_0000551": "L \\in \\mathrm{DTIME}(f^{ \\prime}(n))", "UniMER-1M_0000639": "\\begin{array}{rlr}{{S( \\mathbf{Q}^{ \\prime}, \\mathbf{Q})}}&{{=}}&{{K( \\mathbf{Q}^{ \\prime}, \\mathbf{Q})+U( \\mathbf{Q}^{ \\prime}, \\mathbf{Q}),}} \\\\ {{K( \\mathbf{Q}^{ \\prime}, \\mathbf{Q})}}&{{=}}&{{N \\hbar \\mathrm{ln}(4 \\pi \\lambda \\tau/ \\hbar)+ \\frac{ \\hbar^{2}s^{2}( \\mathbf{Q}^{ \\prime}, \\mathbf{Q})}{4 \\lambda \\tau},}} \\end{array}", "UniMER-1M_0000187": "|v_{eh}|= \\left| \\frac{ieE_{h}}{m_{e}( \\frac{ \\omega_{ce}^{2}}{ \\omega_{rf}}- \\omega_{rf})} \\right|", "UniMER-1M_0000550": "T_{ \\mathrm{~m~i~n~}}=34~ \\mu \\mathrm{~K~}", "UniMER-1M_0000638": "\\vec{x}^{ \\circ}=[ \\delta_{s}^{ \\circ},0,E_{s}^{ \\circ}]", "UniMER-1M_0000186": "\\DeltaT_{acc}", "UniMER-1M_0000549": "\\left( \\Delta, \\Sigma \\right)= \\left( \\Delta^{*}, \\Sigma^{*} \\right) \\equiv \\left( \\frac{ \\left(1+ \\varepsilon \\right) \\left(2q-1 \\right)}{2 \\varepsilon}, \\frac{1+ \\varepsilon}{2} \\right)", "UniMER-1M_0000637": "F_{ \\mathrm{bind},z}^{2 \\rightarrow1} \\left(z_{1},z_{2} \\right) \\neq-F_{ \\mathrm{bind},z}^{1 \\rightarrow2} \\left(z_{2},z_{1} \\right)", "UniMER-1M_0000185": "\\mathbf{ \\hat{m}}= \\left[ \\begin{array}{ccccccc}{{ \\hat{m}_{0}}}&{{ . . .}}&{{ \\hat{m}_{-Q+1}}}&{{ \\hat{m}_{-Q}}}&{{ \\hat{m}_{-Q-1}}}&{{ . . .}}&{{ \\hat{m}_{-2Q}}} \\\\ {{ \\vdots}}&{{ \\ddots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}}&&{{ \\vdots}} \\\\ {{ \\hat{m}_{+Q-1}}}&{{ . . .}}&{{ \\hat{m}_{0}}}&{{ \\hat{m}_{-1}}}&{{ \\hat{m}_{-2}}}&{{ . . .}}&{{ \\hat{m}_{-Q-1}}} \\\\ {{ \\hat{m}_{+Q}}}&{{ . . .}}&{{ \\hat{m}_{+1}}}&{{ \\hat{m}_{0}}}&{{ \\hat{m}_{-1}}}&{{ . . .}}&{{ \\hat{m}_{-Q}}} \\\\ {{ \\hat{m}_{+Q+1}}}&{{ . . .}}&{{ \\hat{m}_{+2}}}&{{ \\hat{m}_{+1}}}&{{ \\hat{m}_{0}}}&{{ . . .}}&{{ \\hat{m}_{-Q+1}}} \\\\ {{ \\vdots}}&&{{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\ddots}}&{{ \\vdots}} \\\\ {{ \\hat{m}_{+2Q}}}&{{ . . .}}&{{ \\hat{m}_{+Q+1}}}&{{ \\hat{m}_{+Q}}}&{{ \\hat{m}_{+Q-1}}}&{{ . . .}}&{{ \\hat{m}_{0}}} \\end{array} \\right].", "UniMER-1M_0000547": "L", "UniMER-1M_0000723": "Pr_{t}=0.9", "UniMER-1M_0000636": "\\begin{array}{rl}&{{ \\{ \\mathcal{F},H \\}_{D}(v, \\Sigma)=- \\{H, \\mathcal{F} \\}_{D}(v, \\Sigma)}} \\\\ {{=}}&{{- \\int_{ \\Omega} \\frac{ \\delta \\mathcal{F}}{ \\deltav} \\wedge \\Big(i_{( \\ast \\frac{ \\deltaH}{ \\deltav})^{ \\sharp}}dv+d \\big( \\mathrm{li}(E( \\frac{ \\deltaH}{ \\delta \\Sigma})) \\big) \\Big)+(-1)^{n-1} \\int_{ \\Sigma} \\frac{ \\delta \\mathcal{F}}{ \\delta \\Sigma} \\wedge \\mathrm{tr}( \\frac{ \\deltaH}{ \\deltav})}} \\\\ &{{+(-1)^{n-1} \\int_{ \\Gamma}E( \\frac{ \\delta \\mathcal{F}}{ \\delta \\Sigma}) \\wedge \\mathrm{tr}( \\frac{ \\deltaH}{ \\deltav}).}} \\end{array}", "UniMER-1M_0000184": "\\mu=0", "UniMER-1M_0000546": "\\mathbf{A}={ \\frac{ \\mu_{0}}{4 \\pi}}{ \\frac{q \\mathbf{v}_{q}(t_{ \\mathrm{{ret}}})}{ \\left| \\mathbf{r}- \\mathbf{r}_{q}(t_{ \\mathrm{{ret}}}) \\right|-{ \\frac{ \\mathbf{v}_{q}(t_{ \\mathrm{{ret}}})}{c}} \\cdot( \\mathbf{r}- \\mathbf{r}_{q}(t_{ \\mathrm{{ret}}}))}}.", "UniMER-1M_0000635": "\\Delta_{ \\mathrm{H}}= \\omega_{k}- \\omega_{u}", "UniMER-1M_0000183": "\\begin{array}{rl}&{{ \\dot{ \\rho}_{11}=-2( \\gamma_{1}+ \\gamma_{2}+ \\Lambda) \\rho_{11}+2 \\Lambda \\rho_{33}+iG_{l} \\rho_{21}-iG_{l}^{*} \\rho_{12},}} \\\\ &{{ \\dot{ \\rho}_{22}=2 \\gamma_{2} \\rho_{11}-iG_{l} \\rho_{21}+iG_{l}^{*} \\rho_{12},}} \\\\ &{{ \\dot{ \\rho}_{21}=-( \\Gamma_{21}-i \\Delta_{l}) \\rho_{21}-iG_{l}^{*} \\rho_{22}+iG_{l}^{*} \\rho_{11},}} \\\\ &{{ \\dot{ \\rho}_{31}=- \\Gamma_{31} \\rho_{31}-iG_{l}^{*} \\rho_{32},}} \\\\ &{{ \\dot{ \\rho}_{32}=-( \\Gamma_{32}+i \\Delta_{l}) \\rho_{32}-iG_{l} \\rho_{31}.}} \\end{array}", "UniMER-1M_0000545": "\\varphi_{X}(t)= \\operatorname{E} \\left[ \\mathrm{exp}(i \\operatorname{Re}(t^{*} \\!X)) \\right],", "UniMER-1M_0000634": "{ \\calF}^{ \\mit \\Gamma}( \\omega, \\zeta)= \\intdx \\sum_{i=Q,G}{^iT^{ \\mit \\Gamma}}( \\omega,x, \\zeta,Q^{2}| \\alpha_{s}){^i{ \\calO}^{ \\mit \\Gamma}}(x, \\zeta).", "UniMER-1M_0000182": "\\begin{array}{r}{{P_{ \\mathrm{ram}}= \\rhov_{ \\mathrm{r}}^{2},}} \\end{array}", "UniMER-1M_0000544": "g_{i}^{2}(t)={ \\frac{g_{i}^{2}(0)}{1-(g_{i}^{2}(0)/8 \\pi^{2})b_{i}t}}.", "UniMER-1M_0000633": "\\left( \\mathbf{S}+ \\rho_{t} \\mathbf{D} \\right) \\mathbf{y}= \\left( \\mathbf{S} \\mathbf{x}_{0}+ \\rho_{t} \\mathbf{A} \\mathbf{y} \\right).", "UniMER-1M_0000181": "\\varepsilon^{ \\mu \\nu}(k) \\rightarrow \\varepsilon^{ \\mu \\nu}(k)= \\varepsilon^{ \\mu \\nu}(k)+i(k^{ \\mu}f^{ \\nu}(k)-k^{ \\nu}f^{ \\mu}(k))", "UniMER-1M_0000543": "E= \\frac{h^{2}J(J+1)}{8 \\pi^{2}I}", "UniMER-1M_0000632": "n+1", "UniMER-1M_0000180": "\\frac{1}{q} \\varepsilon_{SSl} \\mathrm{log} \\left(1+ \\frac{1}{ \\varepsilon_{SSl}} \\cdot \\frac{C^{*}}{q} \\right) \\sim \\frac{1}{q} \\left[ \\varepsilon_{SSl} \\mathrm{log} \\frac{1}{ \\varepsilon_{SSl}}+ \\varepsilon_{SSl} \\mathrm{log} \\frac{C^{*}}{q}+ . . . \\right].", "UniMER-1M_0000542": "\\mathbf{u}^{n}= \\frac{1}{2} \\bigl( \\mathbf{u}^{n+1/2}+ \\mathbf{u}^{n-1/2} \\bigr)", "UniMER-1M_0000631": "-0.29", "UniMER-1M_0000179": "\\pi/298", "UniMER-1M_0000541": "\\hat{ \\boldsymbol{ \\xi}", "UniMER-1M_0000630": "\\left( \\frac{ \\beta_{1}}{ \\beta_{2}} \\right)^{L+1}=1.", "UniMER-1M_0000178": "P_{2}", "UniMER-1M_0000540": "\\gamma_{ \\mathrm{~G~}}<1", "UniMER-1M_0000629": "\\mathbf{G}", "UniMER-1M_0000177": "\\mathrm{SSP}( \\mathbf{y}_{i}, \\mathbf{ \\hat{y}_{i}})= \\mathrm{SSP}_{i}= \\frac{ \\sqrt{ \\int|F_{ \\mathbf{y}_{i}}(k)-F_{ \\mathbf{ \\hat{y}}_{i}}(k)|^{2}dk}}{ \\sqrt{ \\int|F_{ \\mathbf{y}_{i}}(k)|^{2}dk}+ \\sqrt{ \\int|F_{ \\mathbf{ \\hat{y}}_{i}}(k)|^{2}dk}} \\in[0,1],", "UniMER-1M_0000539": "\\times", "UniMER-1M_0000715": "\\infty", "UniMER-1M_0000628": "\\mathrm{li}( \\tilde{e}_{ \\Sigma})- \\mathrm{li}^{ \\prime}( \\tilde{e}_{ \\Sigma})=0 \\quad \\mathrm{~i~n~} \\Omega,", "UniMER-1M_0000176": "E_{ \\mathrm{~c~o~r~}}( \\mathrm{~N~E~C~})", "UniMER-1M_0000538": "\\Gamma_{ \\mathrm{{X^{*}}}}=6.0~ \\mathrm{{meV}}", "UniMER-1M_0000713": "m={ \\sqrt{ \\left({ \\frac{M}{2}} \\right)^{2}+j^{2}}}", "UniMER-1M_0000627": "\\frac{d \\hat{F}( \\lambda)}{d \\lambda}= \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2}e^{ \\lambda \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2}} \\hat{b}_{2}^{ \\dagger}- \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2}e^{ \\lambda \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2}}= \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2} \\hat{F}( \\lambda)+ \\hat{b}_{2}^{ \\dagger}e^{ \\lambda \\hat{b}_{2}^{ \\dagger} \\hat{b}_{2}}.", "UniMER-1M_0000175": "a<v_{2}-v_{1}", "UniMER-1M_0000712": "2", "UniMER-1M_0000536": "1/L", "UniMER-1M_0000626": "A", "UniMER-1M_0000174": "u_{m}(x,y)= \\bigl[u_{1}(x,y)+u_{2}(x,y) \\bigr] \\bigg/2", "UniMER-1M_0000535": "p^{0}(b_{r}^{0,1}+ \\cdot \\cdot \\cdot+b_{r}^{0,6}) \\mid \\Phi \\rangle+ \\mathrm{(terms~containing~both~ \\ alpha~and~b~oscillators)} \\mid \\Phi \\rangle=0", "UniMER-1M_0000625": "m_{I}=m_{I_{ \\mathrm{Na}}}+m_{I_{ \\mathrm{Cs}}}", "UniMER-1M_0000173": "G_{z}", "UniMER-1M_0000534": "\\langle{u}_{x}^{ \\prime} \\mathrm{curl}_{y} \\vec{B^{ \\prime}} \\rangle", "UniMER-1M_0000624": "0.006<G_{B}/Q_{m}<0.16", "UniMER-1M_0000172": "3 \\sigma", "UniMER-1M_0000708": "\\gamma \\gtrsim1.5", "UniMER-1M_0000533": "\\begin{array}{rl}&{{ \\quad_{17}+_{8}}} \\\\ &{{= \\delta_{i,j} \\alpha_{1} \\delta(q \\leqm-n)e_{p,q}^{(1)}[-3]+2 \\delta_{i,j} \\alpha_{2} \\delta(q \\leqm-n)e_{p,q}^{(1)}[-3],}} \\\\ &{{= \\frac{1}{2} \\delta_{i,j}( \\alpha_{1}+2 \\alpha_{2}) \\delta(q \\leqm-n) \\par tial^{2}e_{p,q}^{(1)},}} \\\\ &{{ \\quad_{6}+_{4}+_{2}+_{2}+_{3}}} \\\\ &{{=-2 \\delta_{i,j} \\alpha_{2} \\delta(q>m-n)e_{p,q}^{(2)}[-3]- \\delta_{i,j} \\delta(q>m-n) \\alpha_{1}e_{p,q}^{(2)}[-3]}} \\\\ &{{ \\quad- \\delta_{i,j} \\alpha_{2}e_{p,q}^{(1)}[-3]-2 \\delta_{i,j} \\alpha_{2}e_{p,q}^{(1)}[-3]+ \\delta_{i,j}(m-n)e_{p,q}^{(1)}[-3]}} \\\\ &{{=- \\frac{1}{2} \\delta_{i,j}( \\alpha_{1}+2 \\alpha_{2}) \\par tial^{2}e_{p,q}^{(1)}}} \\end{array}", "UniMER-1M_0000623": "\\Deltat_{max}", "UniMER-1M_0000171": "\\eta_{0}", "UniMER-1M_0000707": "\\mathrm{exp}(- \\epsilont+i \\sqrt{k^{2}- \\epsilon^{2}}t) \\ (k> \\epsilon)", "UniMER-1M_0000532": "\\begin{array}{rl}{{ \\mathbb{E}_{ \\mathcal{N}( \\mathbf{0}, \\mathbf{R}_{0})} \\left[ \\mathbf{X}^{ \\mathrm{T}} \\mathbf{D} \\mathbf{X} \\alpha( \\mathbf{X}) \\right]}}&{{= \\mathbb{E}_{ \\mathcal{N}( \\mathbf{0}, \\mathbf{R}_{0})} \\left[ \\mathrm{Tr} \\left( \\mathbf{X}^{ \\mathrm{T}} \\mathbf{D} \\mathbf{X} \\alpha( \\mathbf{X}) \\right) \\right]}} \\\\ &{{= \\mathbb{E}_{ \\mathcal{N}( \\mathbf{0}, \\mathbf{R}_{0})} \\left[ \\mathrm{Tr} \\left( \\alpha( \\mathbf{X}) \\mathbf{X} \\mathbf{X}^{ \\mathrm{T}} \\mathbf{D} \\right) \\right]}} \\\\ &{{= \\mathrm{Tr} \\left( \\mathbb{E}_{ \\mathcal{N}( \\mathbf{0}, \\mathbf{R}_{0})} \\left[ \\alpha( \\mathbf{X}) \\mathbf{X} \\mathbf{X}^{ \\mathrm{T}} \\right] \\mathbf{D} \\right)}} \\\\ &{{=- \\mathrm{Tr} \\left( \\mathbf{F}_{1} \\mathbf{H} \\right),}} \\end{array}", "UniMER-1M_0000621": "f_{i}^{*}=f_{i}+ \\gamma \\left(f_{i}^{ \\mathrm{eq}}-f_{i} \\right).", "UniMER-1M_0000170": "_2", "UniMER-1M_0000706": "y_{0}", "UniMER-1M_0000531": "\\begin{array}{rl}{{ \\left\\vert \\frac{ \\par tialG_{N_{s}}}{ \\par tial \\vec{ \\Deltaq}} \\right\\vert^{2}}}&{{= \\sum_{k=1}^{N_{e}}{ \\left( \\frac{ \\par tialG_{N_{s}}}{ \\par tial \\Deltaq_{k}} \\right)^{2}}}} \\end{array}", "UniMER-1M_0000620": "\\mathbf{U}", "UniMER-1M_0000169": "p_{y}", "UniMER-1M_0000705": "A", "UniMER-1M_0000530": "r", "UniMER-1M_0000619": "1/(I_{e}-I_{u})", "UniMER-1M_0000168": "6", "UniMER-1M_0000704": "\\hat{R}", "UniMER-1M_0000529": "\\Delta \\omega", "UniMER-1M_0000618": "\\gamma_{S}(i):= \\gamma_{S}(e_{S,i})", "UniMER-1M_0000167": "S_{ \\mathrm{min}}=- \\rhoW_{0} \\left[- \\frac{1}{ \\rho} \\mathrm{exp} \\left(- \\frac{H_{ \\mathrm{SIR}}}{ \\rho} \\right) \\right] \\,, \\quad S_{ \\mathrm{max}}=- \\rhoW_{-1} \\left[- \\frac{1}{ \\rho} \\mathrm{exp} \\left(- \\frac{H_{ \\mathrm{SIR}}}{ \\rho} \\right) \\right] \\,.", "UniMER-1M_0000703": "\\alpha^{4}", "UniMER-1M_0000528": "\\begin{array}{rl}{{ \\mathbf{m}_{ \\mathbf{s}}^{p, \\mathrm{DD}}}}&{{= \\left( \\mathbf{F}_{N}^{ \\mathrm{H}} \\otimes \\mathbf{I}_{M} \\right) \\mathbf{m}_{ \\mathbf{x}}^{p, \\mathrm{DD}},}} \\\\ {{ \\mathbf{C}_{ \\mathbf{s}}^{p, \\mathrm{DD}}}}&{{=( \\mathbf{F}_{N}^{ \\mathrm{H}} \\otimes \\mathbf{I}_{M}) \\mathbf{C}_{ \\mathbf{x}}^{p, \\mathrm{DD}}( \\mathbf{F}_{N} \\otimes \\mathbf{I}_{M}).}} \\end{array}", "UniMER-1M_0000617": "^3", "UniMER-1M_0000166": "t", "UniMER-1M_0000702": "\\spadesuit", "UniMER-1M_0000527": "10", "UniMER-1M_0000165": "\\begin{array}{r}{{ \\langlex_{s} \\rangle= \\frac{ \\alpha_{g} \\alpha_{l}[2+( \\alpha_{g}+ \\alpha_{l})s+e^{( \\alpha_{g}+ \\alpha_{l})s}(( \\alpha_{g}+ \\alpha_{l})s-2)]}{s( \\alpha_{g}+ \\alpha_{l})^{2}[ \\alpha_{g}+ \\alpha_{l}e^{( \\alpha_{g}+ \\alpha_{l})s}]}.}} \\end{array}", "UniMER-1M_0000616": "P(z)", "UniMER-1M_0000701": "S_{13}^{q}= \\frac{-4e^{2}}{h}0.16RTk_{B} \\mathcal{T}.", "UniMER-1M_0000526": "\\theta(x)= \\frac{2 \\pi}{ \\mu( \\psi_{*}(x))} \\int_{ \\Gamma_{x_{0}( \\psi_{*}),x}} \\frac{ \\mathrm{d} \\ell}{| \\nabla \\psi_{*}|}", "UniMER-1M_0000164": "{ \\frac{dS}{dt}}= \\sum_{k=1}^{K}{ \\dot{M}}_{k}{ \\hat{S}}_{k}+{ \\frac{ \\dot{Q}}{T}}+{ \\dot{S}}_{ \\mathrm{gen}}", "UniMER-1M_0000615": "\\bar{ \\bar{ \\chi}}= \\bar{ \\bar{ \\chi}}(k=0)", "UniMER-1M_0000041": "0", "UniMER-1M_0000523": "I_{0}>4 \\times10^{22} \\mathrm{~Wcm^{-2}}", "UniMER-1M_0000163": "\\left\\langle \\left[ \\par tial_{ \\muy}A_{ \\nu}^{m}(y) \\right] \\left[g \\epsilon^{3mn}a^{ \\mu}(x)A^{n \\nu}(x) \\right] \\right\\rangle \\Bigr|_{y=x}=-8iga^{ \\mu}(x) \\par tial_{ \\muy} \\mathcal{P}(y,x) \\Bigr|_{y=x}.", "UniMER-1M_0000614": "D", "UniMER-1M_0000699": "H/{ \\left\\langle \\operatorname{im}f \\right\\rangle}^{H}", "UniMER-1M_0000522": "u_{i}", "UniMER-1M_0000161": "d{ \\tilde{ \\rho}}_{K} \\left(X \\right)= \\sum_{k=1}^{2K}{ \\overbrace{{ \\tilde{ \\rho}}_{1/2} \\left(1 \\right) \\otimes . . . \\otimes{ \\tilde{ \\rho}}_{1/2} \\left(1 \\right)}^{k-1} \\otimesd{ \\tilde{ \\rho}}_{1/2} \\left(X \\right) \\otimes \\overbrace{{ \\tilde{ \\rho}}_{1/2} \\left(1 \\right) \\otimes . . . \\otimes{ \\tilde{ \\rho}}_{1/2} \\left(1 \\right)}^{2K-k}} \\ ,", "UniMER-1M_0000612": "p_{ \\alpha}=P \\left( \\operatorname{sup}_{t<t_{e}}( \\mathbb{1}_{ \\alpha}( \\ensuremath{ \\boldsymbol{{x}}}(t))>0 \\  \\middle| \\; \\ensuremath{ \\boldsymbol{{x}}}(t_{0}) \\inW_{- \\alpha} \\right).", "UniMER-1M_0000698": "\\langlef( \\underline{{{t}}}_{1}, \\underline{{{t}}}_{2}) \\rangle= \\sum_{ \\underline{{{t}}}_{1}, \\underline{{{t}}}_{2}}f( \\underline{{{t}}}_{1}, \\underline{{{t}}}_{2})P_{I}( \\underline{{{t}}}_{1}| \\mathcal{O})P_{I}( \\underline{{{t}}}_{2}| \\mathcal{O})", "UniMER-1M_0000521": "\\begin{array}{rl}{{ \\tilde{ \\rho}_{AB}^{ \\mathrm{P}}}}&{{ \\equiv \\mathcal{G}_{ \\Pi_{A}} \\otimes \\mathcal{G}_{ \\Pi_{B}}[ \\rho_{AB}]}} \\end{array}", "UniMER-1M_0000160": "\\begin{array}{rl}{{{L(m_{ \\pi^{0} \\eta}^{2})}}}&{{{= \\  \\frac{1}{2(a-b)}- \\frac{2}{(a-b)^{2}} \\left[f \\left( \\frac{1}{b} \\right)-f \\left( \\frac{1}{a} \\right) \\right]}}} \\\\ &{{{+ \\  \\frac{a}{(a-b)^{2}} \\left[g \\left( \\frac{1}{b} \\right)-g \\left( \\frac{1}{a} \\right) \\right] \\ ,}}} \\end{array}", "UniMER-1M_0000611": "v_{B \\rho0}= \\frac{3}{4 \\pi} \\int_{0}^{ \\pi} \\Gamma_{+} \\frac{ \\rho_{c} \\, \\delta_{0 \\rho}- \\rho_{+} \\delta_{0 \\rho}(1-2 \\mathrm{cos}^{2} \\theta)+(z_{c}-z_{+}) \\delta_{0z}}{{R_{2A}^{ \\delta}}^{5}(1-{k_{A}^{ \\delta}}^{2} \\mathrm{sin}^{2} \\theta)^{5/2}}(z_{c}-z_{+})(2-4 \\mathrm{cos}^{2} \\theta) \\,d \\theta", "UniMER-1M_0000520": "\\begin{array}{rlr}{{ \\frac{1}{2 \\pi} \\int_{- \\pi}^{ \\pi} \\mathcal{F}( \\varphi) \\mathcal{G}( \\varphi)e^{-ip \\varphi} \\ d \\varphi}}&{{=}}&{{ \\frac{1}{2 \\pi} \\int_{- \\pi}^{ \\pi} \\left( \\sum_{m} \\mathcal{F}_{m}e^{im \\varphi} \\right) \\left( \\sum_{n} \\mathcal{G}_{n}e^{in \\varphi} \\right)e^{-ip \\varphi} \\ d \\varphi}} \\\\ &{{=}}&{{ \\sum_{m} \\sum_{n} \\mathcal{F}_{m} \\mathcal{G}_{n} \\frac{1}{2 \\pi} \\int_{- \\pi}^{ \\pi}d \\varphie^{i(m+n-p) \\varphi}}} \\\\ &{{=}}&{{ \\sum_{m} \\sum_{n} \\mathcal{F}_{m} \\mathcal{G}_{n} \\delta_{m+n,p}}} \\\\ &{{=}}&{{ \\sum_{m} \\mathcal{F}_{m} \\mathcal{G}_{p-m},}} \\end{array}", "UniMER-1M_0000610": "p_{ni}=p_{ni, \\mathrm{avg.}}+ \\vec{s} \\cdot \\vec{V}.", "UniMER-1M_0000159": "A=1", "UniMER-1M_0000696": "R_{6}^{(l_{1},l_{2})} \\simR_{6}^{(-l_{1}+l_{2},-l_{1})} \\simR_{6}^{(-l_{2},l_{1}-l_{2})},", "UniMER-1M_0000519": "\\begin{array}{rl}{{I_{ \\mathrm{total}=}}}&{{ \\sum_{i \\in \\{1,2 \\}}I^{(i,0)} \\mathrm{L}(f_{r},f_{i}^{ \\textrm{QD}},0,0, \\Gamma)}} \\\\ &{{+ \\sum_{i \\in \\{1,2 \\}}I^{(0,i)} \\mathrm{L}(0,0,f_{l},f_{i}^{ \\textrm{QD}}, \\Gamma)}} \\\\ &{{- \\sum_{i,j \\in \\{1,2 \\}}(I^{(i,0)}+I^{(0,j)}) \\mathrm{L}(f_{r},f_{i}^{ \\textrm{QD}},f_{l},f_{j}^{ \\textrm{QD}}, \\Gamma)}} \\\\ &{{+ \\sum_{i \\in \\{1,2 \\}}I^{(i,i)} \\mathrm{L}(f_{r},f_{i}^{ \\textrm{QD}},f_{l},f_{i}^{ \\textrm{QD}}, \\Gamma)}} \\\\ &{{+ \\sum_{i,j \\in \\{1,2 \\},i \\neqj}I^{(i,j)} \\mathrm{L}(f_{r},f_{i}^{ \\textrm{QD}},f_{l},f_{j}^{ \\textrm{QD}}, \\Gamma).}} \\end{array}", "UniMER-1M_0000158": "\\mathbf{x}", "UniMER-1M_0000695": "\\left( \\begin{array}{c}{{c \\left(e, \\mathbf{p}_{+}^{ \\left(1,0 \\right)}+ \\hbar \\mathbf{k},T_{1,0}+ \\tau \\right)}} \\\\ {{c \\left(g, \\mathbf{p}_{+}^{ \\left(1,0 \\right)},T_{1,0}+ \\tau \\right)}} \\end{array} \\right)= \\left( \\begin{array}{c}{{S_{eg}^{ \\left(1,0 \\right)} \\left( \\mathbf{p}_{+,T_{1,0}+ \\tau}^{ \\left(1,0 \\right)}+ \\hbar \\mathbf{k}/2 \\right)}} \\\\ {{S_{gg}^{ \\left(1,0 \\right)} \\left( \\mathbf{p}_{+,T_{1,0}+ \\tau}^{ \\left(1,0 \\right)}+ \\hbar \\mathbf{k}/2 \\right)}} \\end{array} \\right),", "UniMER-1M_0000609": "\\rho", "UniMER-1M_0000518": "P/200", "UniMER-1M_0000157": "\\begin{array}{rl}{{ \\delta \\mathcal{B}_{12, \\widehat{ \\boldsymbol{ \\sigma}}} \\left(c_{1}^{2}+c_{2}^{2} \\right)=}}&{{2 \\overline{{{ \\alpha}}}( \\overline{{{ \\alpha}}}-1)( \\mathbf{c}_{12} \\cdot \\widehat{ \\boldsymbol{ \\sigma}})^{2}+2 \\overline{{{ \\beta}}}( \\overline{{{ \\beta}}}-1)}} \\\\ {{ \\delta \\mathcal{B}_{12, \\widehat{ \\boldsymbol{ \\sigma}}} \\left(w_{1}^{2}+w_{2}^{2} \\right)=}}&{{ \\frac{2 \\overline{{{ \\beta}}}^{2}}{ \\kappa \\theta} \\left( \\widehat{ \\boldsymbol{ \\sigma}} \\times \\mathbf{c}_{12} \\right)^{2}+8 \\frac{ \\overline{{{ \\beta}}}}{ \\kappa} \\left( \\frac{ \\overline{{{ \\beta}}}}{ \\kappa}-1 \\right)}} \\end{array}", "UniMER-1M_0000693": "[ \\mathbf{ \\nabla} \\mathbf{F}_{t_{0}}^{t}( \\mathbf{r})]_{ij}", "UniMER-1M_0000608": "| \\varphi \\rangle= \\sum_{x,j} \\beta_{j}^{x}|x \\rangle \\otimes| \\phi_{j} \\rangle_{c},", "UniMER-1M_0000517": "\\begin{array}{r}{{ \\left( \\begin{array}{ll}{{ \\Delta \\widetilde{U}^{ss^{ \\prime}}}}&{{ \\vec{1} \\,}} \\\\ {{ \\vec{1} \\,^{ \\intercal}}}&{{0 \\,}} \\end{array} \\right) \\left( \\begin{array}{l}{{ \\vec{ \\beta}_{ \\star} \\,}} \\\\ {{ \\lambda_{ \\star} \\,}} \\end{array} \\right)= \\left( \\begin{array}{l}{{ \\vec{L}_{E}^{ss^{ \\prime}} \\,}} \\\\ {{1}} \\end{array} \\right),}} \\end{array}", "UniMER-1M_0000156": "| \\frac{r- \\widetilde{r}}{r}|=|1- \\rho| \\leq \\sigma", "UniMER-1M_0000692": "u_{0}( \\hat{ \\xi},X)=U_{0}( \\hat{ \\xi})f(X),", "UniMER-1M_0000607": "\\begin{array}{rl}{{ \\operatorname{i^{n}erfc}(z)}}&{{= \\int_{z}^{ \\infty} \\operatorname{i^{n-1}erfc}( \\zeta) \\,d \\zeta}} \\\\ {{ \\operatorname{i^{0}erfc}(z)}}&{{= \\operatorname{erfc}(z)}} \\\\ {{ \\operatorname{i^{1}erfc}(z)}}&{{= \\operatorname{ierfc}(z)={ \\frac{1}{ \\sqrt{ \\pi}}}e^{-z^{2}}-z \\operatorname{erfc}(z)}} \\\\ {{ \\operatorname{i^{2}erfc}(z)}}&{{={ \\frac{1}{4}} \\left[ \\operatorname{erfc}(z)-2z \\operatorname{ierfc}(z) \\right]}} \\end{array}", "UniMER-1M_0000516": "\\begin{array}{r}{{|e^{ \\prime}(t_{n})|^{2} \\leC \\mathrm{max}_{1 \\leqi \\leqn} \\left\\{ \\displaystyle \\frac{k_{i}^{2 \\mathrm{min} \\{r_{i}-2,s \\}+2}}{r_{i}^{2(s+1)}} \\right\\} \\|e \\|_{H^{2}(0,t_{n})}^{2}+C \\|e \\|_{H^{2}(0,t_{n})} \\|e \\|_{H^{1}(0,t_{n})}^{3}.}} \\end{array}", "UniMER-1M_0000155": "\\begin{array}{rlrl}{{f_{2n-1}^{(n)}}}&{{=f_{2n-1}^{(n-1)}+c_{2n-1},}}&{{f_{2n+1}^{(n)}}}&{{= \\left\\{ \\begin{array}{ll}{{c_{5}+f_{3}^{(1)} \\{c_{3} \\}- \\frac{1}{2}c_{3} \\{c_{3} \\},}}&{{n=2;}} \\\\ {{c_{2n+1}+f_{3} \\{c_{2n-1} \\},}}&{{n>2;}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0000691": "\\begin{array}{rl}{{ \\Delta_{n}( \\theta_{1}, \\theta_{2})}}&{{ \\triangleq \\beta_{n}^{2}( \\widetilde{ \\gamma}, \\theta_{1}, \\theta_{2})-4 \\gamma_{n}( \\widetilde{ \\gamma}, \\theta_{1}, \\theta_{2})}} \\end{array}", "UniMER-1M_0000606": "U_{p}= \\sqrt{ \\delta_{1}P^{ \\prime}}", "UniMER-1M_0000515": "\\mathcal{D}= \\mathcal{D}_{x} \\times \\mathcal{D}_{y} \\times \\mathcal{D}_{z}.", "UniMER-1M_0000154": "\\|Rx_{n}- \\lambdax_{n} \\|={ \\sqrt{ \\frac{2}{n}}} \\to0.", "UniMER-1M_0000605": "F_{ \\Theta}/A= \\frac{1}{2}K_{m}m^{2}+ \\frac{1}{4}b_{4}m^{4}+ \\frac{1}{6}b_{6}m^{6}+...,", "UniMER-1M_0000690": "^ \\star", "UniMER-1M_0000514": "\\varphi \\left( \\mathcal{Q}^{i,l}, \\mathcal{K}^{i,l}, \\mathcal{R}^{i,l} \\right)= \\operatorname{softmax} \\left( \\frac{ \\mathcal{Q}^{i,l}( \\mathcal{K}^{i,l})^{T}}{ \\sqrt{d_{k}}} \\right) \\odot \\hat{ \\mathcal{R}}^{i}( \\hat{ \\mathcal{R}}^{i})^{T},", "UniMER-1M_0000604": "\\eta_{c_{2}}", "UniMER-1M_0000153": "\\omega", "UniMER-1M_0000689": "F_{y}", "UniMER-1M_0000513": "\\begin{array}{rlr}&&{{ \\Re \\left[{ \\bf E}^{*} \\times{ \\bf B} \\right]= \\frac{1}{2} \\left({ \\bf E} \\times{ \\bf B}^{*}+{ \\bf E}^{*} \\times{ \\bf B} \\right)}} \\\\ &&{{= \\frac{ \\omega}{2i} \\left(A^{*} \\par tial_{x}A-A \\par tial_{x}A^{*},A^{*} \\par tial_{y}A-A \\par tial_{y}A^{*},A^{*} \\par tial_{z}A-A \\par tial_{z}A^{*} \\right)}} \\\\ &&{{= \\frac{ \\omega}{2i} \\left(A^{*} \\overrightarrow{ \\nabla}A-A \\overrightarrow{ \\nabla}A^{*} \\right),}} \\end{array}", "UniMER-1M_0000603": "A_{p}", "UniMER-1M_0000152": "2(M_{ \\Xi}+M_{N})=3M_{ \\Lambda}+M_{ \\Sigma}", "UniMER-1M_0000688": "2 \\mathrm{arctan}{ \\frac{1}{5}}", "UniMER-1M_0000512": "p_{0}", "UniMER-1M_0000602": "\\frac{ \\par tial^{2} \\tilde{ \\mathcal{M}}_{0}}{ \\par tial \\tilde{r}^{2}}( \\eta_{I}(t),t)=- \\left. \\left( \\tilde{u}_{0}- \\frac{1}{ \\tilde{h}_{0}} \\frac{ \\par tial \\tilde{h}_{0}}{ \\par tial \\tilde{r}} \\right) \\frac{ \\par tial \\tilde{ \\mathcal{M}}_{0}}{ \\par tial \\tilde{r}} \\right|_{( \\eta_{I}(t),t)}=0.", "UniMER-1M_0000150": "\\left( \\nabla^{4}-k_{0}^{4} \\right) \\phi( \\boldsymbol{r})=0,", "UniMER-1M_0000687": "iN_{h}^{c}+1 \\lej+1<[(i+1)N_{h}^{c}+1],i=0,1,...,N_{B}^{c}", "UniMER-1M_0000511": "33 \\times33", "UniMER-1M_0000601": "\\omega_{m}", "UniMER-1M_0000149": "\\rho_{i}={ \\frac{1}{Z}} \\mathrm{exp} \\left({ \\frac{ \\lambda_{2}}{k_{ \\mathrm{B}}}}E_{i} \\right).", "UniMER-1M_0000686": "(F_{bc}+F_{bc,K}J_{a}^{2}+F_{bc,J}J^{2}+F_{2bc}(J_{b}^{2}-J_{c}^{2})+...) \\times(J_{b}J_{c}+J_{c}J_{b})/2", "UniMER-1M_0000510": "\\mu_{a}", "UniMER-1M_0000599": "\\mu_{02}^{ \\mathrm{H}} \\phi_{ \\mathbf{w}}^{ \\mathrm{H}}( \\mathbf{w})+ \\frac{ \\mu_{02}^{ \\mathrm{H}}}{{d_{r}}}w \\frac{ \\par tial}{ \\par tialw} \\phi_{ \\mathbf{w}}^{ \\mathrm{H}}( \\mathbf{w}) \\approx-B_{1} \\overline{{{c_{12}}}}^{ \\mathrm{H}}( \\mathbf{w}) \\phi_{ \\mathbf{w}}^{ \\mathrm{H}}( \\mathbf{w}),", "UniMER-1M_0000148": "\\Lambda \\subseteq \\{<PERSON>,<PERSON>, . . . \\}", "UniMER-1M_0000685": "e^{-2kt}=e^{- \\lambda_{1}t}", "UniMER-1M_0000509": "\\frac{1}{2}D_{t}{ \\psi}_{q}^{ \\prime}=-2 \\nu \\! \\! \\int_{ \\Sigma}S_{ij}^{ \\psi}S_{ij}^{ \\psi}- \\int_{ \\Sigma}{ \\psi}_{i}S_{ij}{ \\psi}_{j}.", "UniMER-1M_0000598": "A_{c}", "UniMER-1M_0000147": "\\tilde{I}^{(dif)}(z)", "UniMER-1M_0000508": "D_{x}= \\frac{1}{p} \\mathrm{Var}(x,E) \\,,", "UniMER-1M_0000684": "\\delta", "UniMER-1M_0000597": "F^{ \\alpha \\beta}={ \\frac{ \\par tialA^{ \\beta}}{ \\par tialx_{ \\alpha}}}-{ \\frac{ \\par tialA^{ \\alpha}}{ \\par tialx_{ \\beta}}} \\,,", "UniMER-1M_0000146": "X_{n},n=1,2,3, . . .", "UniMER-1M_0000507": "\\left\\{ab12345678abc123456abcdef \\alpha \\beta \\gamma \\delta1234556 \\alpha \\beta \\frac{1 \\sum_{b}^{a}}{A^{2}} \\right\\}.", "UniMER-1M_0000683": "\\bullet", "UniMER-1M_0000596": "\\hat{ \\mathcal{P}}_{ \\mathrm{max}}, \\hat{ \\mathcal{D}}_{ \\mathrm{min}}", "UniMER-1M_0000506": "\\nu_{4}", "UniMER-1M_0000145": "0.03", "UniMER-1M_0000682": "\\begin{array}{rl}{{A_{00}=}}&{{-2 \\frac{GM}{a^{2}} \\mathrm{sin} \\left( \\frac{ \\gamma}{2} \\right)}} \\\\ &{{- \\frac{3 \\sqrt{5}}{2} \\frac{GM}{R^{2}} \\left( \\frac{R}{a} \\right)^{4} \\left(3( \\mathrm{sin} i)^{2}-2 \\right) \\mathrm{sin} \\left( \\frac{ \\gamma}{2} \\right) \\bar{C}_{20}.}} \\end{array}", "UniMER-1M_0000595": "Q_{i}= \\omega_{0}/ \\kappa_{i}", "UniMER-1M_0000505": "\\begin{array}{rl}{{R_{z}( \\lbracef,g \\rbrace)}}&{{=R_{z}(X_{g}f)}} \\\\ &{{=R_{z}(df(X_{g}))}} \\\\ &{{=R_{z}(d \\theta(X_{f},X_{g})+(R_{t}f) \\eta(X_{g}))}} \\\\ &{{=(L_{R_{z}}d \\theta)(X_{f},X_{g})+d \\theta(L_{R_{z}}X_{f},X_{g})+d \\theta(X_{f},L_{R_{z}}X_{g})}} \\\\ &{{=-dg(L_{R_{z}}X_{f})+(R_{t}g) \\eta(L_{R_{z}}X_{f})+df(L_{R_{z}}X_{g})-(R_{t}f) \\eta(L_{R_{z}}X_{g})}} \\\\ &{{=dg(L_{X_{f}}R_{z})-df(L_{X_{g}}R_{z})}} \\\\ &{{=L_{X_{f}}(dg(R_{z}))-(L_{X_{f}}dg)(R_{z})-L_{X_{g}}(df(R_{z}))+(L_{X_{g}}df)(R_{z})}} \\\\ &{{=-d(X_{f}g)(R_{z})+d(X_{g}f)(R_{z})}} \\\\ &{{=-R_{z}( \\lbraceg,f \\rbrace)+R_{z}( \\lbracef,g \\rbrace)}} \\\\ &{{=2R_{z}( \\lbracef,g \\rbrace),}} \\end{array}", "UniMER-1M_0000144": "q_{ \\xi}= \\textrm{RH}q_{ \\xi}^{*},", "UniMER-1M_0000681": "\\pm37", "UniMER-1M_0000594": "\\widetilde{ \\psi} \\left( \\boldsymbol{x} \\right)", "UniMER-1M_0000504": "\\begin{array}{rl}{{E_{n \\,j}}}&{{= \\muc^{2} \\left(1+ \\left[{ \\frac{Z \\alpha}{n-|k|+{ \\sqrt{k^{2}-Z^{2} \\alpha^{2}}}}} \\right]^{2} \\right)^{-1/2}}} \\end{array}", "UniMER-1M_0000143": "\\theta_{ \\chi} \\left({ \\frac{az+b}{cz+d}} \\right)= \\chi(d) \\left({ \\frac{-1}{d}} \\right)^{ \\nu} \\left({ \\frac{ \\theta_{1} \\left({ \\frac{az+b}{cz+d}} \\right)}{ \\theta_{1}(z)}} \\right)^{1+2 \\nu} \\theta_{ \\chi}(z)", "UniMER-1M_0000593": "\\begin{array}{r}{{ \\begin{array}{r}{{D \\left( \\mathbf{a} \\right)= \\mathbf{A}_{1} \\left( \\mathbf{a},t \\right)+ \\underline{{{ \\mathbf{B}}}} \\left( \\mathbf{a},t \\right) \\cdot \\mathbf{w}(t)}} \\end{array}}} \\end{array}", "UniMER-1M_0000680": "\\", "UniMER-1M_0000503": "| \\up arrow \\downarrow \\Uparrow \\rangle", "UniMER-1M_0000142": "^3", "UniMER-1M_0000592": "u^{*}=-{ \\frac{ \\sqrt{4 \\, \\omega^{2} \\,m_{ \\mathrm{p}}^{2}-( \\omega^{2}-p^{2}-3m_{ \\mathrm{p}}^{2})^{2}}}{2 \\,p \\,m_{ \\mathrm{p}}}} \\,.", "UniMER-1M_0000679": "b", "UniMER-1M_0000502": "\\begin{array}{rl}{{z^{2N+1} \\left( \\widetilde{G}(z)- \\sum_{k=0}^{2N-1} \\frac{m_{k}}{z^{k+1}} \\right)}}&{{=z^{2N+1} \\int_{ \\par tialD_{ \\eta}} \\left( \\frac{1}{z-w}- \\sum_{k=0}^{2N-1} \\frac{w^{k}}{z^{k+1}} \\right) \\frac{1}{ \\sqrt{2 \\pi}}e^{- \\frac{w^{2}}{2}} \\, \\mathrm{d} w}} \\\\ &{{= \\int_{ \\par tialD_{ \\eta}} \\frac{w^{2N}z}{z-w} \\cdot \\frac{1}{ \\sqrt{2 \\pi}}e^{- \\frac{w^{2}}{2}} \\, \\mathrm{d} w, \\qquad z \\inD_{ \\varepsilon}.}} \\end{array}", "UniMER-1M_0000140": "t=0", "UniMER-1M_0000591": "\\phi( \\mathbf{x}) \\sim \\frac{1}{r^{N-2}}", "UniMER-1M_0000678": "\\begin{array}{rl}{{ \\mathbb{U}}}&{{:= \\Bigl\\{ \\vec{ \\chi} \\in[H_{1}^{1}( \\mathscr{R})]^{2} \\;: \\;( \\vec{ \\chi} \\cdot \\vec{e}_{1}) \\inL_{-1}^{2}( \\mathscr{R}), \\; \\vec{ \\chi}= \\vec{0} \\; \\mathrm{~o~n~} \\; \\par tial_{1} \\mathscr{R}, \\; \\vec{ \\chi} \\cdot \\vec{n}=0 \\; \\mathrm{~o~n~} \\; \\par tial_{2} \\mathscr{R} \\Bigr\\},}} \\\\ {{ \\mathbb{V}}}&{{:=H^{1}(0,T; \\,[L_{1}^{2}( \\mathscr{R})]^{2}) \\capL^{2}(0,T; \\mathbb{U}), \\qquad{ \\mathbb{P}}:= \\bigl\\{ \\chi \\inL_{1}^{2}( \\mathscr{R}):(r, \\, \\chi)=0 \\bigr\\}.}} \\end{array}", "UniMER-1M_0000501": "\\frac{2 \\lambda}{E+ \\lambda+R}", "UniMER-1M_0000139": "\\left\\langlen \\right\\rangle={ \\frac{1}{48}}{ \\left( \\frac{mk_{B}}{{ \\hbar^{2} \\pi}} \\right)^{3/2}}{ \\frac{T_{F}^{3}}{T^{3/2}}}", "UniMER-1M_0000590": "\\begin{array}{r}{{ \\tilde{p}(z,s|z_{0})= \\left\\{ \\begin{array}{ll}{{ \\frac{g(z_{0},s)}{g(H,s)} \\frac{ \\mathrm{sinh}( \\alpha(H-z))}{D \\alpha},}}&{{z>z_{0},}} \\\\ {{ \\frac{ \\mathrm{sinh} \\left( \\alpha \\left(H-z_{0} \\right) \\right)}{D \\alpha} \\frac{g(z,s)}{g(H,s)},}}&{{z<z_{0},}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0000677": "5 \\times5", "UniMER-1M_0000500": "c_{-}=x_{-}^{p}(0)<u_{-}", "UniMER-1M_0000589": "\\begin{array}{rlr}{{ \\frac{p_{0y}}{p_{ \\mathrm{C}}}}}&{{=}}&{{ \\frac{A_{ \\mathrm{t}}}{A_{y}^{*}}= \\bigg[ \\frac{2 \\gammaM_{x}^{2}-( \\gamma-1)}{ \\gamma+1} \\bigg]^{ \\frac{-1}{ \\gamma-1}} \\bigg[ \\frac{( \\gamma+1)M_{x}^{2}}{2+( \\gamma-1)M_{x}^{2}} \\bigg]^{ \\frac{ \\gamma}{ \\gamma-1}}}} \\\\ {{ \\frac{A_{ \\mathrm{e}}}{A_{y}^{*}}}}&{{=}}&{{ \\frac{1}{M_{ \\mathrm{e}}} \\bigg[ \\frac{2}{ \\gamma+1} \\bigg(1+ \\frac{ \\gamma-1}{2}M_{ \\mathrm{e}}^{2} \\bigg) \\bigg]^{ \\frac{ \\gamma+1}{2( \\gamma-1)}}}} \\\\ {{ \\frac{p_{0y}}{p_{ \\mathrm{e}}}}}&{{=}}&{{ \\bigg[1+ \\frac{ \\gamma-1}{2}M_{ \\mathrm{e}}^{2} \\bigg]^{ \\frac{ \\gamma}{ \\gamma-1}}}} \\\\ {{p_{ \\mathrm{e}}}}&{{=}}&{{p_{ \\infty}.}} \\end{array}", "UniMER-1M_0000138": "30d", "UniMER-1M_0000676": "\\omega_{G} \\simeql \\omega_{b}", "UniMER-1M_0000499": "S_{14}^{q}={S_{14}^{th}}=- \\frac{4e^{2}}{h}k_{B} \\mathcal{T}(1+R).", "UniMER-1M_0000588": "\\hat{D}_{ \\pm1}, \\hat{d}_{ \\pm1} \\rightarrow \\hat{D}_{ \\pm1} \\mathrm{exp}( \\pmi \\delta \\phi), \\hat{d}_{ \\pm1} \\mathrm{exp}( \\pmi \\delta \\phi)", "UniMER-1M_0000137": "U_{2} \\psi_{+}U_{2}^{ \\dagger}= \\mathrm{exp}(-ie \\eta) \\psi_{+}.", "UniMER-1M_0000675": "S_{mnt}= \\sum_{i,j=1}^{r_{1},r_{2}}G_{ijt}U_{im}V_{jn}, \\quad m=1, . . .,k, \\,n=1, . . .,k, \\,t=1, . . .,N_{T}.", "UniMER-1M_0000498": "\\hat{g}_{ \\mathrm{eff}}", "UniMER-1M_0000587": "\\mathbf{e}_{ij} \\in \\mathbb{R}^{d_{e}}", "UniMER-1M_0000674": "\\begin{array}{rl}{{ \\frac{ \\par tialc_{2}}{ \\par tialx}=0 \\qquad}}&{{x=-L}} \\\\ {{ \\sigma_{-,l}^{ \\mathrm{eff}} \\frac{ \\par tial \\phi_{-,l}}{ \\par tialx}=0 \\qquad}}&{{x=-L}} \\\\ {{ \\frac{ \\par tialc_{4}}{ \\par tialx}=0 \\qquad}}&{{x=L}} \\\\ {{ \\sigma_{+,l}^{ \\mathrm{eff}} \\frac{ \\par tial \\phi_{+,l}}{ \\par tialx}=0 \\qquad}}&{{x=L}} \\\\ {{ \\frac{ \\par tial \\phi_{ \\pm,l}}{ \\par tialy}= \\frac{ \\par tial \\phi_{ \\pm,s}}{ \\par tialy}=0 \\qquad}}&{{y=0}} \\\\ {{ \\frac{ \\par tial \\phi_{ \\pm,l}}{ \\par tialy}= \\frac{ \\par tial \\phi_{ \\pm,s}}{ \\par tialy}=0 \\qquad}}&{{y=H}} \\\\ {{ \\frac{ \\par tialc_{2}}{ \\par tialy}= \\frac{ \\par tialc_{4}}{ \\par tialy}=0 \\qquad}}&{{y=H}} \\end{array}", "UniMER-1M_0000497": "\\boldsymbol{V_{k}}", "UniMER-1M_0000136": "^3", "UniMER-1M_0000586": "x \\to- \\infty", "UniMER-1M_0000496": "| \\phi \\rangle={ \\left[ \\begin{array}{l}{{ \\langles| \\phi \\rangle}} \\\\ {{ \\langles-1| \\phi \\rangle}} \\\\ {{ \\vdots}} \\\\ {{ \\langle-(s-1)| \\phi \\rangle}} \\\\ {{ \\langle-s| \\phi \\rangle}} \\end{array} \\right]}={ \\left[ \\begin{array}{l}{{ \\varepsilon_{s}}} \\\\ {{ \\varepsilon_{s-1}}} \\\\ {{ \\vdots}} \\\\ {{ \\varepsilon_{-s+1}}} \\\\ {{ \\varepsilon_{-s}}} \\end{array} \\right]}", "UniMER-1M_0000135": "D(x,x^{ \\prime})= \\sum_{neven} \\int{ \\frac{d^{4}k}{(2 \\pi)^{4}}} \\int_{0}^{ \\infty}{ \\frac{qdq}{ \\pi}}{ \\frac{e^{ik_{ \\mu}(x^{ \\mu}-x^{ \\mu})}e^{in( \\theta- \\theta^{ \\prime})}}{k_{4}^{2}+q^{2}}}J_{n}(qr)J_{n}(qr^{ \\prime}).", "UniMER-1M_0000673": "0.48", "UniMER-1M_0000585": "\\delta(f(x))= \\sum_{i=1}^{n} \\frac{1}{|f^{ \\prime}(x_{i})|} \\delta(x-x_{i})", "UniMER-1M_0000495": "T_{ \\mu \\nu}= \\frac{1}{r^{4}} \\,f( \\theta, \\alpha) \\, \\mathrm{diag}(1,-3,1,1)=(T_{rr},T_{ \\theta \\theta},T_{zz},- \\rho)", "UniMER-1M_0000134": "f_{ \\rho}(t) \\simeqf_{ \\rho}+ \\frac{(f_{ \\omega}-f_{ \\rho})}{ \\deltam^{2}}", "UniMER-1M_0000672": "\\DeltaB_{ \\chi}= \\sum_{f} \\frac{B_{f} \\{ \\Gamma( \\chi \\tof)- \\Gamma( \\bar{ \\chi} \\to \\bar{f}) \\}}{ \\Gamma_{ \\chi}^{ \\mathrm{total}}}~,", "UniMER-1M_0000584": "n=10", "UniMER-1M_0000494": "\\begin{array}{r}{{ \\frac{b^{2}-2 \\sqrt{b^{2}+1}+2}{b^{3}}= \\frac{2v_{s}}{B \\kappan}.}} \\end{array}", "UniMER-1M_0000133": "S_{j}", "UniMER-1M_0000722": "\\pi^{ \\prime} \\left(u_{0}, \\sigma_{i_{1}}^{ \\epsilon_{1}} \\cdot \\sigma_{i_{2}}^{ \\epsilon_{2}} . . . \\sigma_{i_{m}}^{ \\epsilon_{m}} \\right)= \\sigma \\cdotv_{0}", "UniMER-1M_0000721": "k", "UniMER-1M_0000738": "\\langle:e^{i \\varphi(x)}:_{m}e^{i \\varphi(y)}:_{m} \\rangle_{C_{m}} \\;= \\; \\mathrm{exp} \\left(-C_{m}(x-y) \\right) \\;.", "UniMER-1M_0000742": "[R_{g}( \\tilde{N}, \\tilde{ \\epsilon}_{ \\mathrm{~n~b~}})-R_{g}(N, \\epsilon_{ \\mathrm{~n~b~}})]/R_{g}(N, \\epsilon_{ \\mathrm{~n~b~}})", "UniMER-1M_0000740": "\\mathbf{M}^{e}= \\frac{ \\mu_{s}L_{e}}{420} \\left[ \\begin{array}{cccc}{{156}}&{{22L_{e}}}&{{54}}&{{-13L_{e}}} \\\\ {{22L_{e}}}&{{4L_{e}^{2}}}&{{13L_{e}}}&{{-3L_{e}^{2}}} \\\\ {{54}}&{{13L_{e}}}&{{156}}&{{-22L_{e}}} \\\\ {{-13L_{e}}}&{{-3L_{e}^{2}}}&{{-22L_{e}}}&{{4L_{e}^{2}}} \\end{array} \\right],", "UniMER-1M_0000734": "\\delta \\rho/ \\rho", "UniMER-1M_0000728": "\\boldsymbol{u}", "UniMER-1M_0000739": "\\frac{ \\par tialu_{i}}{ \\par tialx_{i}}=0,", "UniMER-1M_0000731": "z=0", "UniMER-1M_0000733": "\\begin{array}{rl}&{{ \\sum_{l=1}^{m}( \\sigma_{l}^{0}(x,y)+ \\varphi_{l}^{ \\varepsilon,0}(x,y))^{2} \\geq \\delta_{1},}} \\\\ &{{|b^{0}(x,y)+ \\varphi^{ \\varepsilon,0}(x,y)| \\leq \\delta_{2} \\quad \\mathrm{~on~}(x,y) \\in[- \\widehat{A} \\varepsilon, \\widehat{A} \\varepsilon] \\times \\mathbb{R}^{n}.}} \\end{array}", "UniMER-1M_0000732": "r_{ \\pm}^{2}=m-{ \\frac{1}{2}}l_{1}^{2}-{ \\frac{1}{2}}l_{2}^{2} \\pm{ \\frac{1}{2}} \\sqrt{(l_{1}^{2}-l_{2}^{2})^{2}+4m(m-l_{1}^{2}-l_{2}^{2})},", "UniMER-1M_0000697": "x_{mTGD}^{ \\infty} \\equivU_{-}^{ \\infty}", "UniMER-1M_0000725": "z", "UniMER-1M_0000729": "\\mu= \\langlex(t) \\rangle= \\int_{0}^{ \\infty}vsh(s,t)ds= \\frac{v}{ \\Gamma(1+ \\alpha)}t^{ \\alpha}.", "UniMER-1M_0000724": "{ \\bf{v}}_{p}^{n}=0.5({ \\bf{v}}_{p}^{n+1/2}+{ \\bf{v}}_{p}^{n-1/2})", "UniMER-1M_0000727": "-1", "UniMER-1M_0000710": "\\Xi_{max}=Q~| \\phi_{0}|^{2}", "UniMER-1M_0000720": "P_{n}(0,t)= \\frac{ \\Bigl( \\int_{0( \\Gamma_{0})}^{t}R_{pe}( \\tau)d \\tau \\Bigr)^{n}}{n!} \\mathrm{exp}{ \\Bigl(- \\int_{0( \\Gamma_{0})}^{t}R_{pe}( \\tau)d \\tau \\Bigr)}.", "UniMER-1M_0000719": "ED-MQ", "UniMER-1M_0000718": "\\mathrm{a)} \\quad \\xi< \\displaystyle{ \\frac{ \\eta}{4}+ \\frac{4m^{2}}{k_{ \\mathrm{max}}^{4}}}, \\qquad \\mathrm{b)} \\quad \\xi< \\displaystyle{ \\eta+ \\sqrt{- \\frac{12m^{2} \\eta}{k_{ \\mathrm{max}}^{4}}}}.", "UniMER-1M_0000717": "\\begin{array}{rl}{{f_{0}( \\boldsymbol{r})}}&{{=f_{0}+ \\boldsymbol{r} \\cdot \\frac{ \\par tialf}{ \\par tial \\vec{r}},}} \\\\ {{g( \\boldsymbol{r},t)}}&{{=g_{0}+ \\boldsymbol{r} \\cdot \\frac{ \\par tialg}{ \\par tial \\vec{r}}+ \\frac{ \\par tialg}{ \\par tialt}t,}} \\end{array}", "UniMER-1M_0000716": "N_{*}", "UniMER-1M_0000700": "^{13}", "UniMER-1M_0000741": "\\hat{ \\kappa}", "UniMER-1M_0000743": "\\begin{array}{rl}{{L_{x}}}&{{= \\mathrm{~M~S~E~} \\left(x,f_{ \\mathrm{~I~A~F~}}^{-1}(f_{ \\mathrm{~M~A~F~}}(x)) \\right)}} \\\\ {{L_{z}}}&{{= \\mathrm{~M~S~E~} \\left(z,f_{ \\mathrm{~M~A~F~}}(f_{ \\mathrm{~I~A~F~}}^{-1}(z)) \\right).}} \\end{array}", "UniMER-1M_0000709": "p_{i}^{n+1}", "UniMER-1M_0000735": "\\gimel", "UniMER-1M_0000726": "\\mum", "UniMER-1M_0000759": "( \\sigma_{1})^{1}( \\sigma_{2})^{2}( \\overline{{{ \\sigma_{3}}}})^{1}", "UniMER-1M_0000748": "1", "UniMER-1M_0000766": "N=128", "UniMER-1M_0000745": "^2", "UniMER-1M_0000767": "\\mathrm{min}(3,d_{1}+d_{0})", "UniMER-1M_0000757": "\\Psi", "UniMER-1M_0000760": "(t,s)", "UniMER-1M_0000770": "\\beta", "UniMER-1M_0000754": "\\eta=r^{*} \\tilde{ \\eta} \\in \\Omega^{1}(U^{ \\prime})", "UniMER-1M_0000750": "\\Gamma=L/H", "UniMER-1M_0000756": "\\mathcal{C}", "UniMER-1M_0000746": "^{6}", "UniMER-1M_0000762": "\\psi", "UniMER-1M_0000780": "{ \\bf B}", "UniMER-1M_0000765": "a_{i}( \\tau)=a", "UniMER-1M_0000768": "\\begin{array}{rl}&{{ \\mathrel{ \\phantom{=}}U \\Bigl(- \\nu- \\mathrm{ \\small~ \\displaystyle \\frac{1}{2}~}, \\mathrm{ \\small~ \\displaystyle \\frac{1}{2}~}, \\mathrm{ \\small~ \\displaystyle \\frac{1}{2}~} \\omegar_{12}^{2} \\Bigr)}} \\\\ &{{= \\frac{ \\pi^{1/2}}{ \\Gamma(- \\nu)}- \\frac{(2 \\, \\pi)^{1/2}}{ \\Gamma(- \\nu- \\frac{1}{2})} \\omega^{1/2}r_{12}+ . . .}} \\end{array}", "UniMER-1M_0000747": "| \\Omega|", "UniMER-1M_0000783": "M", "UniMER-1M_0000785": "aY^{2}+bY+c=0,", "UniMER-1M_0000786": "S(-f)=S(f)^{*},", "UniMER-1M_0000778": "{ \\calH}=E_{p}+E_{k}", "UniMER-1M_0000771": "\\par tial_{ \\mu}h_{ \\nu}^{ \\mu}=- \\frac{1}{N-2} \\par tial_{ \\nu}h_{ \\lambda}^{ \\lambda}", "UniMER-1M_0000793": "\\deltag \\proptoNg^{3}m_{q} \\mathrm{log} m_{q}/ \\mu", "UniMER-1M_0000796": "\\mathrm{Pm}=1/3,1,", "UniMER-1M_0000753": "n( \\mu^{+} \\mu^{-})_{max} \\approxn^{+} \\times10^{-5}", "UniMER-1M_0000777": "\\hat{ \\rho}_{0}^{ \\mathrm{weak}}", "UniMER-1M_0000758": "\\int_{t,{ \\bf x}} \\equiv \\intdtd^{d}{ \\bf x}", "UniMER-1M_0000789": "N_{B}", "UniMER-1M_0000761": "\\Gamma \\propto \\omega_{pe}(Zm_{e}/m_{i})^{1/3}", "UniMER-1M_0000764": "| \\mathrm{V} \\rangle_{ \\mathrm{s}}| \\mathrm{L} \\rangle_{ \\mathrm{o}}", "UniMER-1M_0000790": "\\begin{array}{rl}{{p(x_{i}^{t+1}=I|x_{i}^{t},x_{ \\par tiali}^{t})}}&{{=1- \\delta_{x_{i}^{t},S} \\prod_{j \\in \\par tiali}(1- \\lambda_{ji} \\delta_{x_{j}^{t},I})}} \\end{array}", "UniMER-1M_0000805": "\\varepsilon", "UniMER-1M_0000806": "\\lambda_{u}", "UniMER-1M_0000744": "\\DeltaW_{n}=W_{ \\tau_{n+1}}-W_{ \\tau_{n}}.", "UniMER-1M_0000772": "\\lambdaf(u)= \\frac{1}{8 \\lambda}e^{ \\frac{-a}{ \\lambda}(u+u_{0})}", "UniMER-1M_0000804": "n_{1}+ . . .+n_{d}=N", "UniMER-1M_0000799": "\\Omega", "UniMER-1M_0000775": "\\Lambda_{0}= \\frac{6 \\sigmaD_{0}}{ \\beta}= \\frac{2}{3} \\chih_{0}^{3}D_{0},~~~~D_{0} \\equiv \\frac{1}{ \\sigma} \\sqrt{ \\frac{a^{2}}{4}- \\frac{2 \\betaC_{1}}{3}},", "UniMER-1M_0000812": "1/3", "UniMER-1M_0000794": "2^{n}", "UniMER-1M_0000803": "10 \\up arrow \\up arrow \\up arrow2", "UniMER-1M_0000801": "c_{1}= \\sqrt{ \\eta} \\, \\mathrm{e}^{i \\operatorname{arg}(c_{1})}", "UniMER-1M_0000809": "\\mathbb{E} \\left[ \\operatorname{sup}_{0 \\leqs \\leqt} \\|U(s, \\cdot) \\|_{p}^{p}+ \\sum_{j=1}^{4} \\int_{0}^{t} \\int_{ \\mathbb{T}^{3}} \\left| \\nabla \\left( \\left|U_{j}(s,x) \\right|^{p/2} \\right) \\right|^{2}dxds \\right] \\leqC \\mathbb{E} \\left[ \\left\\|U_{0} \\right\\|_{p}^{p} \\right]+C_{t}", "UniMER-1M_0000808": "\\bar{f}_{ \\alpha}^{( \\dagger)} \\bar{f}_{ \\beta}^{( \\dagger)}", "UniMER-1M_0000817": "\\epsilon_{C}", "UniMER-1M_0000795": "x", "UniMER-1M_0000791": "\\textit{G-V}", "UniMER-1M_0000800": "K( \\Phi, \\bar{ \\Phi}, \\phi, \\bar{ \\phi})= \\Phi \\bar{ \\Phi}+ \\phi \\bar{ \\phi}", "UniMER-1M_0000755": "\\mathrm{Im} z_{0} \\geq \\sum_{i=1}^{n} \\mathrm{Im} Z_{ \\alpha_{i}}+ \\mathrm{Im} z_{n}", "UniMER-1M_0000752": "j=0)", "UniMER-1M_0000825": "q=0", "UniMER-1M_0000826": "a^{*}", "UniMER-1M_0000816": "\\begin{array}{rl}{{ \\mathrm{~N~L~L~}_{F_{i}}( \\theta)}}&{{= \\sum_{d=1}^{D}- \\mathrm{log} p(F_{i,d}^{ \\mathrm{~o~b~s~}}|x, \\theta)}} \\end{array}", "UniMER-1M_0000828": "82.6", "UniMER-1M_0000820": "[0,1.2] \\times \\Omega", "UniMER-1M_0000830": "-0.08", "UniMER-1M_0000798": "\\{i_{ \\tau},i_{ \\tau}+1 \\} \\subset \\mathcal{L}_{ \\tau} \\quad \\mathrm{~w~h~e~r~e~} \\quad i_{ \\tau}= \\lfloori_{ \\tau-1}/3 \\rfloor.", "UniMER-1M_0000829": "^ \\mathrm{V}", "UniMER-1M_0000822": "T_{flip}^{rainbow} \\,stackrel{ \\lambda \\rightarrow \\infty}{ \\longrightarrow} \\frac{m_{V}}{1- \\frac{g^{2}}{ \\lambda^{2}} \\mathrm{ln} \\frac{ \\lambda^{2}}{M^{2}}},", "UniMER-1M_0000834": "I(t)", "UniMER-1M_0000819": "\\begin{array}{rl}{{ \\mathrm{d}_{t}f}}&{{= \\underbrace{ \\par tial_{t}f+ \\{ \\phi,f \\}}_{ \\epsilon}+ \\underbrace{ \\nabla_{ \\perp} \\xi \\cdot \\nabla_{ \\perp}f}_{ \\epsilon^{2}},}} \\\\ {{ \\boldsymbol{b} \\cdot \\nablaf}}&{{= \\underbrace{v_{ \\mathrm{A}} \\par tial_{z}f+ \\{ \\psi,f \\}}_{ \\epsilon},}} \\end{array}", "UniMER-1M_0000749": "{M}_{{S}_{f}}={S}_{i} \\pm \\frac{1}{2}", "UniMER-1M_0000824": "\\begin{array}{rl}{{ \\int_{0}^{ \\infty} \\frac{r^{ \\frac{2}{ \\gamma} \\alpha}}{[(1+r)r]^{ \\frac{4}{ \\gamma^{2}}+1}}dr=B \\left( \\frac{2}{ \\gamma}( \\alpha-Q)+1, \\frac{8}{ \\gamma^{2}}- \\frac{2 \\alpha}{ \\gamma}+1 \\right)}}&{{= \\frac{ \\Gamma( \\frac{2}{ \\gamma}( \\alpha-Q)+1) \\Gamma( \\frac{8}{ \\gamma^{2}}- \\frac{2 \\alpha}{ \\gamma}+1)}{ \\Gamma( \\frac{4}{ \\gamma^{2}}+1)}}} \\\\ &{{= \\frac{ \\Gamma( \\frac{2 \\alpha}{ \\gamma}- \\frac{4}{ \\kappa}) \\Gamma( \\frac{8}{ \\kappa}- \\frac{2 \\alpha}{ \\gamma}+1)}{ \\Gamma( \\frac{4}{ \\kappa}+1)},}} \\end{array}", "UniMER-1M_0000810": "g=G{ \\frac{m}{r^{2}}}", "UniMER-1M_0000787": "\\Delta_{ij,kl}^{ \\alpha \\beta} \\left( \\mathcal{M}^{-1/2}h \\right)W_{ \\alpha \\beta}( \\boldsymbol{ \\xi}, \\boldsymbol{ \\xi}_{ \\ast},I_{i}^{ \\alpha},I_{j}^{ \\beta} \\left\\vert \\boldsymbol{ \\xi}^{ \\prime}, \\boldsymbol{ \\xi}_{ \\ast}^{ \\prime},I_{k}^{ \\alpha},I_{l}^{ \\beta} \\right.)=0 \\mathrm{~a.e.,}", "UniMER-1M_0000840": "\\sim0.02 \\", "UniMER-1M_0000841": "\\sigma_{*}", "UniMER-1M_0000827": "(<PERSON>,M)", "UniMER-1M_0000838": "\\boldsymbol{Y}_{ \\ellm}^{E}( \\theta, \\phi)= \\frac{1}{ \\sqrt{ \\ell( \\ell+1)}}r \\boldsymbol{ \\nabla}Y_{ \\ellm}^{ \\calR}( \\theta, \\phi)", "UniMER-1M_0000811": "\\sum_{j=1}^{N} \\chi_{ij}q_{ij}= \\sigma \\sum_{j=1}^{N}F_{ij} \\left(T_{i}^{4}-T_{j}^{4} \\right){,}", "UniMER-1M_0000833": "\\gamma_{S,0}", "UniMER-1M_0000846": "\\bigcirc", "UniMER-1M_0000847": "\\Delta \\theta_{j}", "UniMER-1M_0000774": "\\operatorname{lcm}(8,9,21)=2^{3} \\cdot3^{2} \\cdot7^{1}=8 \\cdot9 \\cdot7=504.", "UniMER-1M_0000821": "\\begin{array}{r}{{ \\Omega_{Fm,F^{ \\prime}m^{ \\prime}}^{q}= \\frac{ \\mu_{B}g_{S}}{ \\hbar} \\frac{ \\mu_{Fma,F^{ \\prime}mb}^{q}}{ \\hbar} \\  \\beta_{ \\mathrm{mw}}^{q},}} \\end{array}", "UniMER-1M_0000784": "\\begin{array}{rl}{{e^{- \\Deltas}}}&{{=1-e^{- \\Deltat},}} \\end{array}", "UniMER-1M_0000813": "m \\timesn", "UniMER-1M_0000823": "\\rhogR", "UniMER-1M_0000848": "\\ell^{ \\mu}= \\int \\frac{d^{3}k}{(2 \\pi)^{3}} \\frac{| \\tilde{ \\j}(k)|^{2}}{2k^{0}}k^{ \\mu},", "UniMER-1M_0000835": "{ \\left\\|{ \\boldsymbol{ \\varphi}}_{k} \\right\\|}_{2} \\leq \\frac{2{ \\delta}^{2}}{{q}_{k}} \\left(1+ \\frac{{ \\delta}^{2}}{6{q}_{k}} \\right) \\left(2{ \\left\\|{ \\boldsymbol{u}}_{k}^{1} \\right\\|}_{2}+{ \\tau}^{2} \\mathrm{max}_{0 \\leqj \\leq2}{{ \\left\\|{ \\boldsymbol{f}}_{k}^{j} \\right\\|}_{2}} \\right) \\,.", "UniMER-1M_0000855": "y_{1}", "UniMER-1M_0000851": "Q_{w}", "UniMER-1M_0000769": "N \\equivD_{ \\mathrm{out}}/ \\Deltay \\in \\mathbb{Z}", "UniMER-1M_0000836": "\\left(V,1,1, \\right) \\left(V^{ \\prime},1,1 \\right)= \\left(V+V^{ \\prime},1,1 \\right) \\quad.", "UniMER-1M_0000781": "{ \\begin{array}{rl}{{{ \\hat{ \\mathbf{x}}}}}&{{={ \\frac{x \\left({ \\sqrt{x^{2}+y^{2}}}{ \\hat{ \\mathbf{r}}}+z{ \\hat{ \\boldsymbol{ \\theta}}} \\right)-y{ \\sqrt{x^{2}+y^{2}+z^{2}}}{ \\hat{ \\boldsymbol{ \\varphi}}}}{{ \\sqrt{x^{2}+y^{2}}}{ \\sqrt{x^{2}+y^{2}+z^{2}}}}}}} \\\\ {{{ \\hat{ \\mathbf{y}}}}}&{{={ \\frac{y \\left({ \\sqrt{x^{2}+y^{2}}}{ \\hat{ \\mathbf{r}}}+z{ \\hat{ \\boldsymbol{ \\theta}}} \\right)+x{ \\sqrt{x^{2}+y^{2}+z^{2}}}{ \\hat{ \\boldsymbol{ \\varphi}}}}{{ \\sqrt{x^{2}+y^{2}}}{ \\sqrt{x^{2}+y^{2}+z^{2}}}}}}} \\\\ {{{ \\hat{ \\mathbf{z}}}}}&{{={ \\frac{z{ \\hat{ \\mathbf{r}}}-{ \\sqrt{x^{2}+y^{2}}}{ \\hat{ \\boldsymbol{ \\theta}}}}{ \\sqrt{x^{2}+y^{2}+z^{2}}}}}} \\end{array}}", "UniMER-1M_0000831": "\\Gamma_{S}^{0} \\ll \\Gamma_{I}^{0} \\approx \\Gamma_{H}^{0} \\ll \\Gamma_{U}^{0}.", "UniMER-1M_0000853": "\\gtrless", "UniMER-1M_0000776": "\\begin{array}{r}{{C_{ \\mu \\nu}(t)= \\frac{1}{ \\pi} \\left( \\frac{e \\hbar}{mL} \\right)^{2} \\left( \\frac{ \\hbar^{2}}{2me} \\right)^{ \\mu+ \\nu-2}e^{-t/ \\tau_{c}} \\int \\displaylimits_{-L/2}^{L/2} \\textrm{d}z \\int \\displaylimits_{k^{-}}^{k^{+}}k^{2( \\mu+ \\nu-1)}f_{0}(1-f_{0}) \\textrm{d}k, \\quad k^{ \\pm}= \\frac{m}{ \\hbart} \\left( \\pm \\frac{L}{2}-z \\right),}} \\end{array}", "UniMER-1M_0000807": "3n+3", "UniMER-1M_0000849": "n", "UniMER-1M_0000852": "{ \\calD}{ \\varphi^{ \\dagger}}^{ \\prime}{ \\calD} \\varphi^{ \\prime}= \\mathrm{exp}[ \\pm \\intd^{4}x \\alpha(x) \\frac{e_{0}^{2}}{96 \\pi^{2}}F^{ \\mu \\nu}F_{ \\mu \\nu}]{ \\calD}{ \\varphi^{ \\dagger}}{ \\calD} \\varphi", "UniMER-1M_0000857": "\\beta_{ \\mathrm{2D}}= \\beta_{ \\mathrm{3D}}/(a_{z} \\sqrt{ \\pi})", "UniMER-1M_0000797": "\\begin{array}{rl}{{ \\gamma^{2}=}}&{{ \\frac{1}{2} \\big[( \\alpha_{xx}- \\alpha_{yy})^{2}+( \\alpha_{xx}- \\alpha_{zz})^{2}+( \\alpha_{yy}- \\alpha_{zz})^{2} \\big]}} \\\\ &{{+3 \\big[ \\alpha_{xy}^{2}+ \\alpha_{xz}^{2}+ \\alpha_{yz}^{2} \\big] \\;,}} \\end{array}", "UniMER-1M_0000869": "o=l,r", "UniMER-1M_0000859": "F=F_{0} \\frac{1- \\displaystyle \\frac{v}{v_{0}}}{1+ \\displaystyle \\kappa \\displaystyle \\frac{v}{v_{0}}},", "UniMER-1M_0000815": "\\begin{array}{rlr}&&{{ \\vec{B} \\approx \\left\\{ \\begin{array}{cc}{{ \\frac{q_{e}n_{e}R_{L}}{2 \\epsilon_{0}c} \\, \\frac{r}{R_{L}} \\, \\vec{e}_{ \\phi} \\,,}}&{{r \\leR_{L}}} \\\\ {{0 \\,,}}&{{r>R_{L}}} \\end{array} \\right. \\,,}} \\\\ &&{{ \\vec{E} \\approx \\left\\{ \\begin{array}{cc}{{ \\frac{q_{e}n_{e}R_{L}}{2 \\epsilon_{0}c} \\, \\frac{cr}{R_{L}} \\, \\vec{e}_{r} \\,,}}&{{r \\leR_{L}}} \\\\ {{0 \\,,}}&{{r>R_{L}}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0000870": "\\begin{array}{rl}{{ \\|A(x)-}}&{{A(y) \\|_{X} \\leqc \\mathrm{max} \\{ \\|A(a) \\|_{X}, \\|A(b) \\|_{X} \\}}} \\\\ &{{ \\leqc \\mathrm{max} \\{ \\|a \\|_{X} \\|A(a/ \\|a \\|_{X}) \\|_{X}, \\|b \\|_{X} \\|A(b/ \\|b \\|_{X}) \\|_{X} \\} \\leqcCM \\|x-y \\|_{X}.}} \\end{array}", "UniMER-1M_0000839": "r_{n-1}~U[ \\mathbb{E}_{ \\mathrm{{R}}}]", "UniMER-1M_0000873": "h \\times10~ \\mathrm{~k~H~z~}/ \\mathrm{~G~}", "UniMER-1M_0000845": "\\omega_{bi} \\equiv(r/R_{0})^{1/2}(T_{i}/m_{i})^{1/2}/(qR_{0}) \\approx \\epsilon^{1/2} \\omega_{ti}", "UniMER-1M_0000850": "10^{-27} \\, \\mathrm{e \\cdotcm}", "UniMER-1M_0000860": "\\nabla \\mathsf{ \\tilde{U}}_{ \\mu} \\gets \\nablaU_{ \\mu}({ \\boldsymbol{x}}+ \\mathrm{d}{ \\boldsymbol{x}},n)", "UniMER-1M_0000864": "t_{0}=a,t_{1},t_{2}, . . .,t_{n}=b", "UniMER-1M_0000788": "A_{R}=0.00253", "UniMER-1M_0000880": "n", "UniMER-1M_0000881": "\\phi_{ \\varepsilon}", "UniMER-1M_0000832": "\\begin{array}{rlr}{{u^{ \\alpha} \\mathcal{D}_{ \\alpha} \\varepsilon+(P_{l}-P_{ \\perp})l^{ \\mu}l^{ \\nu} \\sigma_{ \\mu \\nu}}}&{{=}}&{{-u^{ \\alpha} \\mathcal{D}_{ \\alpha} \\mathcal{E}^{(1)}-( \\mathcal{P}_{l}^{(1)}- \\mathcal{P}_{ \\perp}^{(1)})l^{ \\mu}l^{ \\nu} \\sigma_{ \\mu \\nu}-M \\mathcal{D}_{ \\alpha}l^{ \\alpha}-l^{ \\alpha} \\mathcal{D}_{ \\alpha}M- \\mathcal{D}_{ \\nu}W_{ \\perpu}^{ \\nu}}} \\\\ &&{{- \\,2W_{ \\perpl}^{ \\alpha}l^{ \\nu} \\sigma_{ \\mu \\nu}- \\pi_{ \\perp}^{ \\mu \\nu} \\sigma_{ \\perp \\mu \\nu}.}} \\end{array}", "UniMER-1M_0000875": "T_{o}^{2}={ \\frac{1}{4D}}(m_{H}^{2}-8Bv_{o}^{2}) \\ , \\  \\ B={ \\frac{3}{64 \\pi^{2}}}(2m_{W}^{4}+m_{Z}^{4}-4m_{t}^{4}) \\ ,", "UniMER-1M_0000842": "2.5", "UniMER-1M_0000867": "E_{ \\mathrm{s}}=-180 \\mathrm{~m~V~}", "UniMER-1M_0000865": "\\kappa=1.0", "UniMER-1M_0000792": "\\mathcal{B}_{abcd}= \\frac{1}{ \\Sigma} \\iint \\mathrm{d} \\Sigma \\left( \\nabla \\chi_{a} \\cdot \\nabla \\chi_{b} \\right) \\left( \\nabla \\chi_{c} \\cdot \\nabla \\chi_{d} \\right)~.", "UniMER-1M_0000861": "v_{ \\perp}", "UniMER-1M_0000879": "\\mathbb{E}_{X^{n}} \\left\\{{ \\mathrm{Tr}} \\left\\{ \\Pi_{ \\rho, \\delta} \\  \\rho_{X^{n}} \\right\\} \\right\\}={ \\mathrm{Tr}} \\left\\{ \\Pi_{ \\rho, \\delta} \\  \\mathbb{E}_{X^{n}} \\left\\{ \\rho_{X^{n}} \\right\\} \\right\\}", "UniMER-1M_0000888": "\\lll", "UniMER-1M_0000862": "S", "UniMER-1M_0000893": "n \\rightarrown \\pm1", "UniMER-1M_0000773": "\\sigma_{ \\mathrm{ln} \\hat{ \\calL}}^{2}( \\Lambda)= \\sum_{i}^{N} \\sigma_{ \\mathrm{ln} \\hat{ \\calL}_{i}}^{2}( \\Lambda)+N^{2} \\sigma_{ \\mathrm{sel}}^{2}( \\Lambda).", "UniMER-1M_0000889": "A^{-1}=4{ \\sqrt{H(H-h_{a}^{-1})(H-h_{b}^{-1})(H-h_{c}^{-1})}}.", "UniMER-1M_0000863": "I(t)=I_{0}e^{-t/ \\tau_{RC}},", "UniMER-1M_0000751": "S_{ \\DeltaP}^{ \\mathrm{~s~h~o~t~}}= \\frac{2hc}{ \\lambda_{0, \\mathrm{~L~}}} \\left<P \\right>.", "UniMER-1M_0000856": "\\par tial_{t}(X_{1}(x,t)+X_{2}(x,t))=D \\par tial_{xx}X_{2}(x,t)- \\mu_{D}X_{2}(x,t)- \\mu_{M}X_{1}(x,t)+ \\betaf( \\{Y_{2} \\}_{Y \\neqX})~,", "UniMER-1M_0000844": "T_{2}^{ \\mu \\nu, \\,AC}(i,j,l,m)=V_{2}^{ \\mu \\nu} \\:T_{2}^{AC}(i,j,l,m),", "UniMER-1M_0000892": "\\begin{array}{r}{{p(a_{1},a_{2},b)=p(b)p(a_{1}|b)p(a_{2}|b),}} \\\\ {{p(a_{1},a_{2},b)=p(b)p(a_{2})p(a_{1}|a_{2},b).}} \\end{array}", "UniMER-1M_0000782": "\\begin{array}{rlr}{{x^{ \\prime}}}&{{=}}&{{2 \\Big((x+y)^{k}-x^{k} \\Big)+ \\left(1+ \\frac{y}{1-x} \\right) \\Big(x^{k}-(x-2r)^{k} \\Big)}} \\\\ {{y^{ \\prime}}}&{{=}}&{{2 \\Big(1-(x+y)^{k} \\Big)-2 \\Big((x+y)^{k}-x^{k} \\Big)-2 \\Big(x^{k}-(x-2r)^{k} \\Big) \\frac{y}{1-x}}} \\\\ {{r^{ \\prime}}}&{{=}}&{{- \\frac{2r}{1-x} \\Big((x+y)^{k}-x^{k} \\Big)- \\left( \\frac{(1-x+y)r}{(1-x)^{2}}+1 \\right) \\Big(x^{k}-(x-2r)^{k} \\Big)}} \\\\ &&{{+ \\Big((x-2r)^{k}-(3r)^{k} \\Big),}} \\end{array}", "UniMER-1M_0000900": "HWW", "UniMER-1M_0000897": "\\overline{{{ \\Omega}}}_{A,B}", "UniMER-1M_0000904": "\\delta \\phi", "UniMER-1M_0000887": "{ \\mathrm{Min}}={ \\left\\{ \\begin{array}{ll}{{f \\left(0,1.25313 \\right)}}&{{=0.292579}} \\\\ {{f \\left(0,-1.25313 \\right)}}&{{=0.292579}} \\end{array} \\right.}", "UniMER-1M_0000901": "\\hat{S}", "UniMER-1M_0000814": "3~MeV", "UniMER-1M_0000899": "\\alpha", "UniMER-1M_0000896": "2 \\Longrightarrow1", "UniMER-1M_0000843": "Re", "UniMER-1M_0000868": "\\begin{array}{rl}{{ \\int_{U}|u|^{2}dV}}&{{= \\int_{U}|c_{mn}|^{2}(1/2-1/2 \\mathrm{cos}(2mx))(1/2-1/2 \\mathrm{cos}(2ny))dV}} \\\\ &{{= \\pi^{-2} \\int_{U}(1- \\mathrm{cos}(2mx)- \\mathrm{cos}(2nx)+ \\mathrm{cos}(2mx) \\mathrm{cos}(2ny))dV}} \\\\ &{{= \\frac{ \\mathrm{Area}(U)}{ \\mathrm{Area}( \\Omega)}+{ \\mathcal{O}(m^{-1}+n^{-1}).}} \\end{array}", "UniMER-1M_0000882": "u^{ \\prime}", "UniMER-1M_0000908": "_2", "UniMER-1M_0000916": "2z", "UniMER-1M_0000917": "x=0", "UniMER-1M_0000905": "y", "UniMER-1M_0000837": "\\alpha_{s}(m_{ \\mathrm{Z}})=0.119 \\pm0.001,", "UniMER-1M_0000883": "Un^{2}/2", "UniMER-1M_0000922": "x", "UniMER-1M_0000872": "X", "UniMER-1M_0000871": "\\begin{array}{rlr}&&{{| \\tilde{W}_{s}| \\leq|W_{s}|+|(sL \\beta_{n}^{-1 \\sl ash2},sL \\beta_{n}^{-1 \\sl ash2}+L_{0} \\beta_{n}^{-1 \\sl ash2} \\sl ash2] \\cap \\mathbb{N}^{*}|}} \\\\ &{{ \\leq}}&{{|W_{s}|+ \\frac{1}{2}L_{0} \\beta_{n}^{-1 \\sl ash2}+1 \\leq|W_{s}|+5C_{1} \\beta_{n}^{-1 \\sl ash2}.}} \\end{array}", "UniMER-1M_0000884": "f(x; \\lambda,k)= \\frac{k}{ \\lambda} \\left( \\frac{x}{ \\lambda} \\right)^{k-1}e^{- \\left( \\frac{x}{ \\lambda} \\right)^{k}}~,~x \\geq0.", "UniMER-1M_0000927": "\\mu", "UniMER-1M_0000866": "{ \\dot{u}}- \\sum_{i}p_{i}{ \\dot{x}}_{i}=0", "UniMER-1M_0000929": "l=2", "UniMER-1M_0000911": "[ \\epsilon,1]", "UniMER-1M_0000931": "\\delta", "UniMER-1M_0000932": "p( \\mathfrak{r}; \\vartheta)", "UniMER-1M_0000895": "g_{ \\mu \\nu}=diagonal(-V(r),V^{-1}(r),r^{2} \\hat{1}_{D-2}) \\quad,", "UniMER-1M_0000933": "k", "UniMER-1M_0000935": "0.5", "UniMER-1M_0000913": "{ \\par tial}_{t} \\bar{ \\cal{A}}_{M}=0 \\ .", "UniMER-1M_0000903": "\\frac{ \\par tial \\phi}{ \\par tialn}=0", "UniMER-1M_0000890": "\\begin{array}{r}{{G_{n,i}^{*}( \\rho^{*})=G_{n,0}^{*} \\binom{n}{i} \\prod_{j=0}^{i-1}[ \\bar{ \\lambda}_{n,i}( \\rho^{*})j+ \\rho^{*}] \\quad \\foralli \\in \\lbrace1, . . .,n \\rbrace \\;,}} \\end{array}", "UniMER-1M_0000928": "r=0", "UniMER-1M_0000914": "\\hat{S}^{2}= \\hat{S}", "UniMER-1M_0000941": "\\textbf{p} \\par allel \\mathbf{e}_{z}", "UniMER-1M_0000894": "\\Phi:(-a,a) \\rightarrow \\mathbb{R}", "UniMER-1M_0000902": "( \\sigma_{x}, \\sigma_{z})=(200~ \\mathrm{ \\ mum},16~ \\mathrm{ \\ mum})", "UniMER-1M_0000920": "r_{T}", "UniMER-1M_0000891": "( \\gamma,v_{2}, \\theta_{*}, \\hat{d}, \\alpha)", "UniMER-1M_0000874": "f(x,y)=1", "UniMER-1M_0000947": "\\widehat{ \\tau}", "UniMER-1M_0000936": "M^{*}= \\left(M^{n}+M^{*} \\times \\left(n-1 \\right) \\right)/n.", "UniMER-1M_0000937": "E(+/0)=E_{X}^{0}-E_{X}^{1}- \\epsilon_{ \\mathrm{~V~B~M~}}", "UniMER-1M_0000939": "\\epsilon_{123}=+1", "UniMER-1M_0000907": "\\gamma/ \\Omega_{p} \\geq-0.05", "UniMER-1M_0000946": "20 \\", "UniMER-1M_0000912": "q(x|y)=q(y|x)", "UniMER-1M_0000954": "\\Omega_{n}", "UniMER-1M_0000924": "r= \\frac{ \\mu}{N_{ph}} \\qquad \\left[ \\frac{ \\mathrm{~m~V~}}{ \\mathrm{~p~h~o~t~o~n~}} \\right]", "UniMER-1M_0000940": "\\alpha_{eq}>50^{ \\circ}", "UniMER-1M_0000949": "\\mathbf{k}_{SVD}= \\frac{1}{4} \\sum_{i=1,2,3,4} \\hat{ \\mathbf{k}}_{SVD,Ci}", "UniMER-1M_0000958": "\\approx110", "UniMER-1M_0000925": "mX^{2}+nY^{2}", "UniMER-1M_0000915": "\\begin{array}{rl}{{ \\DeltaE_{0}(R,x_{c})}}&{{=E_{0}^{(ec)}(R,x_{c})-V_{0}(R,x_{c})}} \\end{array}", "UniMER-1M_0000951": "40 \\", "UniMER-1M_0000877": "\\bar{P}_{ecdsw}= \\bar{P}_{ecdsw,0}+ \\epsilon \\bar{P}_{ecdsw,1}+ \\epsilon^{2} \\bar{P}_{ecdsw,2}+ \\epsilon^{3} \\bar{P}_{ecdsw,3}+ \\epsilon^{4} \\bar{P}_{ecdsw,4},", "UniMER-1M_0000965": "p_{1}, \\alpha_{1}, \\mu_{1}", "UniMER-1M_0000943": "{ \\bf y}=(y_{1},y_{2},y_{3})", "UniMER-1M_0000886": "\\mathcal{C} \\ell_{3}( \\mathbb{C})", "UniMER-1M_0000968": "m_{ \\mathrm{+}}/m_{ \\mathrm{-}}=1836", "UniMER-1M_0000934": "\\delta( \\boldsymbol{r}- \\boldsymbol{r}_{n}- \\delta \\boldsymbol{r}_{n})= \\delta( \\boldsymbol{r}- \\boldsymbol{r}_{n})-( \\delta \\boldsymbol{r}_{n} \\cdot \\nabla) \\delta( \\boldsymbol{r}- \\boldsymbol{r}_{n})+ . . .,", "UniMER-1M_0000971": "k_{12} \\cdot \\delta_{f}=0.196", "UniMER-1M_0000953": "P(x,y)_{train} \\neqP(x,y)_{test}", "UniMER-1M_0000918": "^{133}", "UniMER-1M_0000876": "C", "UniMER-1M_0000921": "\\mathrm{~K~E~}= \\boldsymbol{q}^{f} \\cdot \\boldsymbol{q}^{f} \\approx \\sum_{j=1}^{N}a_{j}^{2}/2", "UniMER-1M_0000948": "\\prod_{i=1}^{3} \\frac{ \\Gamma( \\alpha_{i})}{ \\Gamma( \\beta_{i})}=(-1)^{ \\sum_{i=1}^{3}( \\beta_{i}- \\alpha_{i})} \\prod_{i=1}^{3} \\frac{ \\Gamma(1- \\beta_{i})}{ \\Gamma(1- \\alpha_{i})},", "UniMER-1M_0000980": "\\mu", "UniMER-1M_0000981": "0^{ \\circ}", "UniMER-1M_0000962": "\\lambda", "UniMER-1M_0000956": "\\varepsilon", "UniMER-1M_0000944": "dt^{ \\prime}= \\frac{ \\par tialt^{ \\prime}}{ \\par tialt}dt=(1+ \\frac{d}{dt} \\deltat)dt,", "UniMER-1M_0000952": "g(x) \\mathcal{C}[f(x)/g(x)]", "UniMER-1M_0000938": "{ \\frac{ \\pi}{4}}= \\sum_{n=0}^{ \\infty}{ \\frac{(-1)^{n}}{2n+1}}=1-{ \\frac{1}{3}}+{ \\frac{1}{5}}-{ \\frac{1}{7}}+ . . .,", "UniMER-1M_0000957": "0= \\frac{ \\par tial \\left\\langleV,V \\right\\rangle}{ \\par tial \\lambda_{m}}", "UniMER-1M_0000963": "\\left( \\sigma_{2}^{3}, \\sigma_{1}, \\sigma_{2}, \\sigma_{1}^{-1} \\sigma_{2} \\sigma_{1} \\right) \\to \\left( \\sigma_{2}^{3}, \\left( \\sigma_{1} \\sigma_{2} \\right) \\sigma_{1}^{-1} \\sigma_{2} \\sigma_{1} \\left( \\sigma_{1} \\sigma_{2} \\right)^{-1}, \\sigma_{1}, \\sigma_{2} \\right)", "UniMER-1M_0000909": "\\frac{ \\epsilon(z)}{2}i^{m+n} \\frac{ \\Gamma( \\lambda+m+1) \\Gamma(-2 \\lambda-m-n-1)}{ \\Gamma(- \\lambda-n)}z^{2 \\lambda+m+n+1} \\; \\times", "UniMER-1M_0000979": "0.67", "UniMER-1M_0000972": "V( \\mathbf{r})", "UniMER-1M_0000970": "w(x_{0})= \\frac{ \\mathrm{~H~e~a~v~i~s~i~d~e~}(3-x_{0}) \\cdot \\mathrm{~H~e~a~v~i~s~i~d~e~}(3+x_{0}) \\cdot(9-x_{0}^{2})}{9}.", "UniMER-1M_0000942": "\\boldsymbol{s}_{t_{0}}^{( \\mathrm{~s~i~m~})},~ \\boldsymbol{a}_{t}^{( \\mathrm{~s~i~m~})},~ \\boldsymbol{ \\omega}_{t}^{( \\mathrm{~s~i~m~})}", "UniMER-1M_0000989": "\\begin{array}{rl}{{ \\nabla \\cdot{ \\vec{A}}}}&{{= \\nabla \\cdot( \\varphi \\, \\nabla \\psi \\;- \\; \\psi \\, \\nabla \\varphi)}} \\end{array}", "UniMER-1M_0000976": "\\lambda/4", "UniMER-1M_0000961": "\\boldsymbol{ \\alpha}_{n-1}^{(p)} \\mathbf{U}_{n-1}^{(p)}= \\boldsymbol{ \\zeta}_{n}^{(p)} \\mathbf{U}_{n}^{(p)}+ \\boldsymbol{ \\gamma}_{n}^{(p)},", "UniMER-1M_0000997": "a_{P}", "UniMER-1M_0000945": "D_{t2}=D_{t3}=D_{32}/2=150~mm", "UniMER-1M_0000950": "\\beta \\in \\lbrack0,1]", "UniMER-1M_0000984": "\\begin{array}{rl}{{ \\Sigma_{rad}( \\tau_{1}, \\tau_{2})}}&{{= \\sum_{ \\alpha}V_{C \\alpha} \\,A_{ \\alpha}( \\tau_{1}, \\tau_{2}) \\,V_{ \\alphaC}}} \\\\ {{ \\left[ \\Sigma_{ph} \\right]_{ii}( \\tau_{1}, \\tau_{2})}}&{{= \\sum_{ \\beta_{i}}V_{i \\beta_{i}} \\,B_{ \\beta_{i}}( \\tau_{1}, \\tau_{2}) \\,V_{ \\beta_{i}i}}} \\end{array}", "UniMER-1M_0000986": "\\begin{array}{rl}{{ \\operatorname{curl} \\mathbf{A}}}&{{=( \\operatorname{curl} \\mathbf{A})_{ \\rho}{ \\hat{ \\boldsymbol{ \\rho}}}+( \\operatorname{curl} \\mathbf{A})_{ \\phi}{ \\hat{ \\boldsymbol{ \\phi}}}+( \\operatorname{curl} \\mathbf{A})_{z}{ \\hat{ \\boldsymbol{z}}}}} \\end{array}", "UniMER-1M_0000994": "I", "UniMER-1M_0000923": "\\begin{array}{r}{{C_{LR}=0.085 \\;MeV \\qquad \\quad C_{LL}=1.13 \\;MeV \\qquad \\quad C_{RR}=0.49MeV}} \\end{array}", "UniMER-1M_0000955": "\\tilde{M}", "UniMER-1M_0000998": "\\frac{d \\mathbf{Q}}{dt}= \\mathbf{z},", "UniMER-1M_0000898": "\\begin{array}{r}{{ \\frac{a_{1}}{a_{2}} \\approx{ \\frac{g_{I}{(1)}}{g_{I}{(2)}}{[1+ \\epsilon_{BW}(1)- \\epsilon_{BW}(2)][1+ \\epsilon_{BR}(1)- \\epsilon_{BR}(2)]}}}} \\\\ {{={ \\frac{g_{I}{(1)}}{g_{I}{(2)}}{[1+^{1} \\Delta_{BW}^{2}][1+^{1} \\Delta_{BR}^{2}]}}}} \\end{array}", "UniMER-1M_0000973": "\\lambda^{ \\prime}", "UniMER-1M_0000982": "\\sigma:=p \\eta, \\enspace \\alpha:=p \\eta^{2}, \\enspace \\mathrm{~a~n~d~} \\enspace \\chi:=p \\eta^{3}.", "UniMER-1M_0000996": "Y(t)", "UniMER-1M_0000878": "^{ \\star}", "UniMER-1M_0000993": "\\omega=8", "UniMER-1M_0000960": "\\ell", "UniMER-1M_0000985": "\\sigma_{b}/j_{0}=0.17", "UniMER-1M_0000978": "N=2", "UniMER-1M_0000990": "0.32 \\pm0.01^{ \\ast}", "UniMER-1M_0000967": "\\rho^{*}", "UniMER-1M_0000930": "\\xi", "UniMER-1M_0000969": "\\gamma_{81,88}^{(2)}", "UniMER-1M_0000999": "\\beta_{5}", "UniMER-1M_0000992": "327", "UniMER-1M_0001006": "\\hbar", "UniMER-1M_0001008": "R", "UniMER-1M_0001007": "\\frac{ \\sqrt{V}}{h}", "UniMER-1M_0001021": "M_{2}", "UniMER-1M_0001005": "^*", "UniMER-1M_0001022": "\\boldsymbol{ \\phi}", "UniMER-1M_0001034": "Q^{ \\prime}", "UniMER-1M_0000995": "(SF)_{ \\theta}=3.37 \\", "UniMER-1M_0001004": "(i)", "UniMER-1M_0000854": "\\begin{array}{rl}{{k_{2}=}}&{{ \\frac{1}{2q \\gamma}m_{2},}} \\\\ {{k_{4}=}}&{{ \\frac{1}{2q \\gamma(2 \\gamma+2)(2q \\gamma+2)} \\Big[m_{4}-(1+ \\frac{ \\gamma+1}{q \\gamma})m_{2}^{2} \\Big],}} \\\\ {{k_{6}=}}&{{ \\frac{1}{2q \\gamma(2 \\gamma+2)(2q \\gamma+2)(2 \\gamma+4)(2q \\gamma+4)}}} \\\\ {{ \\cdot}}&{{ \\Bigg(m_{6}- \\Big[(3 \\times2q \\gamma+2 \\gamma+2+2q \\gamma+2+2 \\gamma+4+2q \\gamma+4) \\cdot \\frac{1}{2q \\gamma} \\Big] \\Big[m_{4}-(1+ \\frac{ \\gamma+1}{q \\gamma})m_{2}^{2} \\Big]m_{2}}} \\\\ &{{- \\Big[1+ \\frac{ \\gamma+1}{q \\gamma}+ \\frac{( \\gamma+1)(q \\gamma+1)}{(q \\gamma)^{2}} \\Big]m_{2}^{3} \\Bigg).}} \\end{array}", "UniMER-1M_0001037": "Ax^{2}+<PERSON><PERSON>+<PERSON>^{2}+Dxz+Eyz+<PERSON><PERSON>^{2}=0.", "UniMER-1M_0001041": "\\rho=4", "UniMER-1M_0001016": "\\approx600", "UniMER-1M_0001019": "q", "UniMER-1M_0001025": "K_{f}", "UniMER-1M_0001046": "Q_{b}", "UniMER-1M_0001043": "N_{z}", "UniMER-1M_0001002": "\\mathrm{Arg}[F_{ \\pi}(t+i \\epsilon)]= \\delta_{1}^{1}(t) \\,, \\quad t_{ \\pi} \\let \\let_{in} \\,,", "UniMER-1M_0001029": "\\Sigma \\left( \\frac{p+z}{z} \\left[ \\mathrm{tan}^{-1} \\left( \\frac{ \\frac{w}{2}+u}{p+z} \\right)- \\mathrm{tan}^{-1} \\left( \\frac{u- \\frac{w}{2}}{p+z} \\right) \\right]+ \\frac{z}{p} \\left[ \\mathrm{tan}^{-1} \\left( \\frac{u- \\frac{w}{2}}{z} \\right)- \\mathrm{tan}^{-1} \\left( \\frac{ \\frac{w}{2}+u}{z} \\right) \\right] \\right)", "UniMER-1M_0001035": "\\Deltaf= \\frac{|b|D_{1}}{(2 \\pi)^{2}}.", "UniMER-1M_0001040": "P_{k_{x}}(k_{x1},k_{x2})", "UniMER-1M_0001028": "S_{ \\mathrm{min}}= \\int_{t_{i}}^{t_{f}}dt \\int_{0}^{1}d \\lambda \\Big[-({ \\frac{ \\par tialu^{ \\mu}}{ \\par tialt}}{ \\frac{ \\par tialu_{ \\mu}}{ \\par tialt}})({ \\frac{ \\par tialu^{ \\nu}}{ \\par tial \\lambda}}{ \\frac{ \\par tialu_{ \\nu}}{ \\par tial \\lambda}})+({ \\frac{ \\par tialu^{ \\mu}}{ \\par tialt}}{ \\frac{ \\par tialu_{ \\mu}}{ \\par tial \\lambda}})^{2} \\Big]^{ \\frac{1}{2}} \\,,", "UniMER-1M_0001013": "\\hat{s}=(x_{A}P_{A}+x_{B}P_{B})^{2} \\,, \\quad \\hat{t}=(x_{A}P_{A}-k_{1})^{2} \\,, \\, \\, \\mathrm{and} \\quad \\hat{u}=(x_{A}P_{A}-k_{2})^{2} \\,,", "UniMER-1M_0001042": "\\frac{f}{(d)}", "UniMER-1M_0001055": "D_{x}", "UniMER-1M_0001003": "{ \\mathrm{Proportion}} \\leqx={ \\frac{1}{2}} \\left[1+ \\operatorname{erf} \\left({ \\frac{x- \\mu}{ \\sigma{ \\sqrt{2}}}} \\right) \\right]={ \\frac{1}{2}} \\left[1+ \\operatorname{erf} \\left({ \\frac{z}{ \\sqrt{2}}} \\right) \\right]", "UniMER-1M_0001010": "A_{ \\mu}^{ \\mu^{ \\prime}}A_{ \\nu}^{ \\nu^{ \\prime}}( \\delta_{ \\mu^{ \\prime}}^{ \\lambda^{ \\prime}} \\delta_{ \\nu^{ \\prime}}^{ \\rho^{ \\prime}}-{ \\bar{B^{ \\prime}}}_{ \\mu^{ \\prime} \\nu^{ \\prime}}^{ \\lambda^{ \\prime} \\rho^{ \\prime}}) \\frac{ \\par tial{ \\bar{g}^{ \\prime}}_{ \\lambda^{ \\prime} \\rho^{ \\prime}}}{ \\par tiall_{i}}", "UniMER-1M_0001026": "\\Sigma(z)=G_{0}(z)-{ \\frac{8gG_{0}(z)}{1-G_{0}^{2}(z)}}-{ \\frac{4gG_{0}^{3}(z)}{1-G_{0}^{2}(z)}}+O(g^{2})", "UniMER-1M_0001024": "N(t)=N_{eq}(1- \\mathrm{exp}(-t/ \\tau)).", "UniMER-1M_0001060": "m_{i}", "UniMER-1M_0001048": "N_{ \\mathrm{~m~}}=N_{ \\mathrm{~e~x~t~}}", "UniMER-1M_0001015": "\\psi_{4}^{lm}= \\intd \\OmegaY_{lm}^{-2 \\star} \\psi_{4}.", "UniMER-1M_0001031": "\\sim0.3", "UniMER-1M_0001032": "G^{ \\pm}(k,0)= \\operatorname{lim}_{ \\xi \\to0} \\left[ \\pm(E_{k}^{ \\pm}( \\xi)-E_{0}^{N})- \\mu \\right]/ \\xi^{2}.", "UniMER-1M_0001054": "We=49", "UniMER-1M_0001038": "\\DeltaE=(1- \\xi) \\Deltam^{2}/2E=0 \\; \\; \\mathrm{for} \\; \\; \\xi=1 \\; \\;,", "UniMER-1M_0001057": "0.117", "UniMER-1M_0001053": "22", "UniMER-1M_0001036": "3 \\", "UniMER-1M_0001000": "\\varepsilon_{0} \\left( \\mathbf{x}, \\par tialS/ \\par tial \\mathbf{x}, \\omega \\right)", "UniMER-1M_0001020": "W(C)=e^{i \\pi \\int_{S}d^{2}xP(x)} \\,.", "UniMER-1M_0001045": "\\int_{ \\mathcal{S}}p( \\phi)d \\phi", "UniMER-1M_0001073": "m", "UniMER-1M_0001050": "1.01", "UniMER-1M_0001049": ". . .- . . .+k^{v}(z)", "UniMER-1M_0001076": "{ \\boldsymbol{ \\pi}}_{t}^{i} \\in \\mathbb{R}^{3}", "UniMER-1M_0001064": "\\breve{j}", "UniMER-1M_0001059": "\\mathbf{V}_{p}=V_{p} \\hat{ \\mathbf{e}}", "UniMER-1M_0001009": "{ \\calL}_{mass}^{(5)}=m \\left[ \\overline{{{{ \\psi}}}} \\psi+h^{i}c^{ij} \\left(F^{j} \\right)^{+}+ \\left(h^{i} \\right)^{+}c^{ij}F^{j} \\right]", "UniMER-1M_0001056": "\\operatorname{Re}[j_{y}(k,t)]", "UniMER-1M_0001065": "M", "UniMER-1M_0001011": "(1+ \\gamma_{5})_{AB} \\ W_{B} \\ = \\ 0 \\quad, \\qquad{ \\gamma^{n}}_{AB} \\  \\par tial_{n}W_{B} \\ = \\ 0 \\quad,", "UniMER-1M_0001066": "\\begin{array}{rlr}{{E_{x}}}&{{=}}&{{ \\mathrm{Re} \\left\\{ \\psie^{-i \\omega_{0}t^{ \\prime}} \\right\\}, \\, \\, \\, \\,E_{z}= \\mathrm{Re} \\left\\{ \\frac{i}{k_{0}} \\par tial_{x} \\psie^{-i \\omega_{0}t^{ \\prime}} \\right\\},}} \\\\ {{B_{y}}}&{{=}}&{{ \\mathrm{Re} \\left\\{ \\frac{1}{c} \\psie^{-i \\omega_{0}t^{ \\prime}} \\right\\},B_{z}= \\mathrm{Re} \\left\\{ \\frac{i}{k_{0}c} \\par tial_{y} \\psie^{-i \\omega_{0}t^{ \\prime}} \\right\\},}} \\end{array}", "UniMER-1M_0001014": "B_{x}(t)={ \\alpha} \\bar{P}_{y}(t)/{ \\gamma}", "UniMER-1M_0001052": "V( \\vert \\vec{x} \\vert)= \\left\\{ \\begin{array}{ll}{{{- \\frac{2s^{2}}{c}+s^{2}r}}}&{{{ \\mathrm{for} \\, \\,r \\to0~,}}} \\\\ {{{- \\frac{2s^{2}}{c^{2}} \\, \\frac{1}{r}}}}&{{{ \\mathrm{for} \\, \\,r \\to \\infty~,}}} \\end{array} \\right.", "UniMER-1M_0001067": "FGR", "UniMER-1M_0001001": "A_{N} \\frac{d \\sigma}{dt}= \\frac{ \\alpha \\sigma_{tot}e^{bt/2}}{2m \\sqrt{-t}} \\{( \\mu-1)-2 \\mathrm{Re}( \\tau)-2 \\rho \\mathrm{Im}( \\tau) \\}+2 \\mathrm{Im}( \\tau) \\frac{ \\sqrt{-t}}{m} \\left( \\frac{d \\sigma}{dt} \\right)_{ \\mathrm{hadronic}}", "UniMER-1M_0001069": "u_{0}=- \\infty", "UniMER-1M_0001070": "\\begin{array}{rl}&{{ \\alpha_{ \\ell}({ \\bf r})= \\mathrm{e}^{- \\mathrm{i} \\, \\ell^{2} \\phi} \\sum_{i}a_{i} \\,J_{ \\ell}(2| \\beta_{i}|) \\, \\mathrm{e}^{ \\mathrm{i} \\ell \\mathrm{arg} \\{- \\beta_{i} \\}},}} \\\\ &{{a_{i}= \\int_{ \\theta_{i-1}}^{ \\theta_{i}} \\! \\! \\! \\thetad \\theta \\,J_{0}(q_{0}R \\, \\theta) \\, \\mathrm{e}^{- \\mathrm{i} q_{0}z \\, \\theta^{2}/2}.}} \\end{array}", "UniMER-1M_0001018": "15/30=50 \\", "UniMER-1M_0001092": "\\tau_{s}", "UniMER-1M_0001068": "\\theta_{K}= \\frac{1}{2}Arg \\left[ \\frac{R_{xx}+iR_{xy}}{R_{xx}-iR_{xy}} \\right]= \\frac{1}{2}Arg \\left[ \\frac{ \\sigma_{xx}-i \\sigma_{xy}}{ \\sigma_{xx}+i \\sigma_{xy}} \\frac{1+a( \\sigma_{xx}+i \\sigma_{xy})}{1+a( \\sigma_{xx}-i \\sigma_{xy})} \\right] \\,.", "UniMER-1M_0001072": "t_{g}= \\frac{2( \\beta- \\alpha)r_{0}}{c_{g,r}}= \\frac{2( \\beta- \\alpha)r_{0}k_{r}^{2}}{k_{ \\perp}N}.", "UniMER-1M_0001062": "\\Upsilon=5r_{e}^{2} \\gammaN/6 \\alpha \\sigma_{z}( \\sigma_{x}+ \\sigma_{y})", "UniMER-1M_0001061": "{ \\mathrm{SO}}(3;1)^{+}", "UniMER-1M_0001074": "r", "UniMER-1M_0001075": "H", "UniMER-1M_0001078": "\\dot{ \\vec{ \\eta}}( \\tau)=- \\vec{ \\alpha} \\Omega^{2} \\tau+ \\vec{ \\beta}( \\Omega-", "UniMER-1M_0001101": "\\eta_{3}", "UniMER-1M_0001058": "\\gamma", "UniMER-1M_0001017": "\\sigma_{ \\mathrm{{R}}} \\ll \\sigma_{ \\mathrm{{R}}}^{*}", "UniMER-1M_0001081": "\\omega \\ll \\Omega_{ \\mathrm{i}} \\ll| \\Omega_{ \\mathrm{e}}|", "UniMER-1M_0001105": "l(X)= \\aleph_{0}", "UniMER-1M_0001012": "Y_{i}=R_{i}U_{i}^{T}", "UniMER-1M_0001079": "{ \\frac{ \\par tialC}{ \\par tialt}} \\Rightarrow{ \\frac{C_{i}^{j+1}-C_{i}^{j}}{ \\Deltat}}", "UniMER-1M_0001082": "r", "UniMER-1M_0001091": "T", "UniMER-1M_0001051": "\\begin{array}{rl}{{S( \\tau_{2})=}}&{{ \\; \\frac{1}{4}S_{ \\infty}e^{(-1- \\sqrt{2}) \\tau_{2}} \\left((2+ \\sqrt{2})e^{2 \\sqrt{2} \\tau_{2}}+2- \\sqrt{2} \\right)- \\frac{(P_{ \\infty}+T_{ \\infty})e^{(-1- \\sqrt{2}) \\tau_{2}}(e^{2 \\sqrt{2} \\tau_{2}}-1)}{2 \\sqrt{2}}+1,}} \\\\ {{P( \\tau_{2})=}}&{{ \\;- \\frac{S_{ \\infty}e^{(-1- \\sqrt{2}) \\tau_{2}}(e^{2 \\sqrt{2} \\tau_{2}}-1)}{2 \\sqrt{2}}- \\frac{1}{4}(P_{ \\infty}+T_{ \\infty})e^{(-1- \\sqrt{2}) \\tau_{2}} \\left((-2+ \\sqrt{2})e^{2 \\sqrt{2} \\tau_{2}}-2- \\sqrt{2} \\right).}} \\end{array}", "UniMER-1M_0001095": "- \\cdot-", "UniMER-1M_0001047": "\\begin{array}{rl}{{H_{2D}}}&{{= \\sum_{ \\vec{j}} \\Big\\{(m_{z}+i \\gamma_{ \\downarrow}/2) \\bigr(| \\vec{j} \\up arrow \\rangle \\langle \\vec{j} \\up arrow|-| \\vec{j} \\downarrow \\rangle \\langle \\vec{j} \\downarrow| \\bigr)}} \\\\ &{{- \\sum_{k=x,y} \\Big[t_{0} \\bigr(| \\vec{j} \\up arrow \\rangle \\langle \\vec{j}+ \\vec{e}_{k} \\up arrow|-e^{-i \\vec{K} \\cdot \\vec{e}_{k}}| \\vec{j} \\downarrow \\rangle \\langle \\vec{j}+ \\vec{e}_{k} \\downarrow| \\bigr)}} \\\\ &{{+t_{ \\mathrm{so}}^{k} \\bigr(| \\vec{j} \\downarrow \\rangle \\langle \\vec{j}+ \\vec{e}_{k} \\up arrow|-e^{i \\vec{K} \\cdot \\vec{e}_{k}}| \\vec{j}+ \\vec{e}_{k} \\downarrow \\rangle \\langle \\vec{j} \\up arrow| \\bigr)+h.c. \\Big] \\Big\\},}} \\end{array}", "UniMER-1M_0001093": "10^{-4.5}", "UniMER-1M_0001096": "\\mu^{0}", "UniMER-1M_0001087": "D \\beta", "UniMER-1M_0001098": "\\begin{array}{rlr}{{ \\sum_{n} \\frac{e^{ikd_{n}}}{ikd_{n}} \\left(1- \\frac{z_{n}^{2}}{d_{n}^{2}} \\right)}}&{{ \\to}}&{{ \\frac{1}{ia^{2}} \\int \\int \\frac{e^{ikd}}{kd} \\left(1- \\frac{z^{2}}{d^{2}} \\right)dydz}} \\end{array}", "UniMER-1M_0001097": "(x,y)=(0,32,0.16)", "UniMER-1M_0001099": "Y", "UniMER-1M_0001063": "\\approxE_{m}+b^{*}(0) \\sum_{ \\boldsymbol{R_{n}}}e^{-i{ \\boldsymbol{k \\cdotR_{n}}}} \\  \\intd^{3}r \\  \\varphi^{*}({ \\boldsymbol{r-R_{n}}}) \\DeltaU({ \\boldsymbol{r}}) \\psi({ \\boldsymbol{r}}) \\ .", "UniMER-1M_0001077": "\\begin{array}{rl}{{t_{ \\mathrm{eq}}}}&{{= \\frac{1}{2} \\frac{m_{i}}{m_{e}} \\frac{3 \\sqrt{m_{e}}(k_{ \\mathrm{B}}T_{e})^{3/2}}{4 \\sqrt{2 \\pi}n_{e}e^{4} \\mathrm{ln}{ \\Lambda_{e}}}}} \\end{array}", "UniMER-1M_0001103": "\\frac{ \\par tialq_{c}}{ \\par tialz}|_{fall}=-q_{c}/L", "UniMER-1M_0001104": "\\lbrackK]_{q \\bar{q}}= \\left( \\begin{array}{ll}{{{q \\bar{q}+q^{-1} \\bar{q}^{-1}}}}&{{{-1}}} \\\\ {{{-(1+ \\bar{q}^{2}+ \\bar{q}^{-2})}}}&{{{q \\bar{q}^{3}+q^{-1} \\bar{q}^{-3}}}} \\end{array} \\right)", "UniMER-1M_0001089": "I \\equivPL", "UniMER-1M_0001088": "V(Q_{1}, . . .,Q_{M_{ \\mathrm{int}}})= \\sum_{ \\alpha=1}^{M_{ \\mathrm{int}}}V_{ \\alpha}(Q_{ \\alpha}(q_{ \\mu_{ \\alpha,1}}, . . .,q_{ \\mu_{ \\alpha,M_{ \\alpha}}})).", "UniMER-1M_0001107": "\\theta(x)=- \\int \\! \\!d^{4}y_{_{E}} \\Delta \\phi_{ \\mu}(x-y)A_{ \\mu}(y) \\ .", "UniMER-1M_0001083": "60 \\", "UniMER-1M_0001086": "(7 \\sim20) \\times10^{-4}", "UniMER-1M_0001125": "\\sigma_{ \\gamma}", "UniMER-1M_0001110": "q_{2}", "UniMER-1M_0001131": "\\mathbf{z}", "UniMER-1M_0001085": "\\langle \\delta \\theta^{2} \\rangle= \\frac{1}{2 \\pi} \\int_{- \\infty}^{ \\infty}S_{ \\delta \\theta} \\,d \\omega= \\frac{k_{B}T}{I} \\frac{ \\Gamma}{ \\Gamma^{ \\prime}} \\frac{1}{ \\omega_{m}^{ \\prime \\,2}}+ \\frac{2 \\beta^{2}P_{ \\mathrm{~o~p~t~}}^{2} \\tau_{0}^{2}G_{D}^{2}}{I^{2} \\Gamma^{ \\prime}}S_{ \\theta_{n}}", "UniMER-1M_0001121": "\\overline{{{x_{1}}}}", "UniMER-1M_0001119": "0^{ \\circ}", "UniMER-1M_0001116": "\\eta=35", "UniMER-1M_0001109": "L_{ \\infty} \\, \\, \\mathrm{~n~o~r~m~}= \\mathrm{max}_{j=1,2,3, . . .,N}|u_{j}-u^{exact}(x_{j},T_{f})|", "UniMER-1M_0001122": "r_{0}", "UniMER-1M_0001090": "\\lambda_{ij} \\in[0,1]", "UniMER-1M_0001124": "v", "UniMER-1M_0001115": "{{J}_{Ohm}} \\left( \\omega \\right)= \\sum_{l=1}^{{{n}_{lor}}}{ \\frac{{{p}_{l}} \\omega}{ \\Upsilon \\left({{ \\Omega}_{l}},{{ \\Gamma}_{l}} \\right)}}", "UniMER-1M_0001117": "(h)", "UniMER-1M_0001100": "\\begin{array}{rl}{{F_{1,Y}(t,r_{0})-F_{2}(t)}}&{{ \\simeq2s_{ \\mathrm{eq}}t \\biggl[( \\mathcal{E}_{H}(v_{1}(r_{0})) \\!- \\! \\mathcal{E}_{H}(0))(1-r_{0})}} \\\\ &{{ \\quad+ \\mathcal{E}_{H}(v_{1}(r_{0})) \\! \\! \\left(1-r- \\!r_{0} \\! \\right) \\biggr] \\!.}} \\end{array}", "UniMER-1M_0001106": "\\lesssim200", "UniMER-1M_0001123": "\\begin{array}{rl}{{c_{i_{1}, . . .,i_{M}}= \\sum_{ \\alpha_{0}, . . ., \\alpha_{M}=0}^{ \\chi-1}}}&{{ \\Gamma_{ \\alpha_{0} \\alpha_{1}}^{[1]} \\lambda_{ \\alpha_{1}}^{[1]} \\Gamma_{ \\alpha_{1} \\alpha_{2}}^{[2]} . . . \\lambda_{ \\alpha_{M-1}}^{[M-1]} \\Gamma_{ \\alpha_{M-1} \\alpha_{M}}^{[M]}}} \\\\ {{ \\prod_{k=1}^{M}}}&{{ \\delta \\left(c_{ \\alpha_{k-1}}^{[k-1]}-c_{ \\alpha_{k}}^{[k]}-i_{k} \\right).}} \\end{array}", "UniMER-1M_0001134": "\\sigma_{X}( \\tau) \\equiv \\sigma_{( \\DeltaX/X)}( \\tau)= \\sigma_{r}( \\tau)/K_{X}^{r}", "UniMER-1M_0001145": "{ \\hat{H}}={ \\hat{T}}+{ \\hat{V}},", "UniMER-1M_0001080": "M=10", "UniMER-1M_0001149": "\\langle \\deltax(0) \\deltav( \\tau) \\rangle", "UniMER-1M_0001120": "- \\hbar \\omega", "UniMER-1M_0001127": "n", "UniMER-1M_0001133": "g_{T}^{D/L} \\equiv \\frac{ \\lbrackP^{D/L}( \\omega, \\widehat{ \\mathbf{k}}, \\mathbf{B}_{0})-P^{D/L}( \\omega, \\widehat{ \\mathbf{k}},- \\mathbf{B}_{0})]}{[P^{D/L}( \\omega, \\widehat{ \\mathbf{k}}, \\mathbf{B}_{0})+P^{D/L}( \\omega, \\widehat{ \\mathbf{k}},- \\mathbf{B}_{0})]}= \\gamma^{D/L} \\hat{ \\mathbf{k}} \\cdot \\mathbf{B}_{0}.", "UniMER-1M_0001146": "1.5", "UniMER-1M_0001132": "\\phi_{0}:=m^{2}+ \\frac{k_{ \\|}^{2}}{2} \\frac{ \\mathrm{cosh} z^{ \\prime}- \\mathrm{cosh} \\nuz^{ \\prime}}{z^{ \\prime} \\mathrm{sinh} z^{ \\prime}}+ \\frac{k_{ \\bot}^{2}}{2} \\frac{ \\mathrm{cos} \\nuz- \\mathrm{cos} z}{z \\mathrm{sin} z}.", "UniMER-1M_0001130": "| \\psi \\rangle=|n_{{ \\mathbf{k}}_{1}},n_{{ \\mathbf{k}}_{2}},n_{{ \\mathbf{k}}_{3}}...n_{{ \\mathbf{k}}_{l}},... \\rangle", "UniMER-1M_0001102": "\\begin{array}{rl}{{ \\gamma_{ \\mathrm{rp}}(F)}}&{{=( \\gamma_{ \\mathrm{r}}/2) \\mathrm{exp} \\left[qFb/(2k_{ \\mathrm{B}}T) \\right]}} \\\\ {{ \\gamma_{ \\mathrm{rm}}(F)}}&{{=( \\gamma_{ \\mathrm{r}}/2) \\mathrm{exp} \\left[-qFb/(2k_{ \\mathrm{B}}T) \\right],}} \\end{array}", "UniMER-1M_0001118": "\\mathcal{E}= \\intE \\, \\mathrm{d} x", "UniMER-1M_0001108": "\\hat{W}_{i,l}=i \\hat{U}_{i}^{-1}L_{i,l}", "UniMER-1M_0001143": "C_{e,i}=( \\deltan_{e,i}/n_{0})^{2}/(| \\delta \\mathbf{B}|/B_{0})^{2}", "UniMER-1M_0001140": "{ \\frac{ \\mathrm{sin} \\theta_{1}}{ \\mathrm{sin} \\theta_{2}}}={ \\frac{v_{1}}{v_{2}}}={ \\frac{n_{2}}{n_{1}}}", "UniMER-1M_0001136": "\\rho<0", "UniMER-1M_0001112": "\\begin{array}{rl}{{P_{4} \\,= \\,}}&{{- \\frac{15}{1024} \\,(Z{-}Z^{ \\prime})^{4}+ \\frac{21}{512} \\,(R{-}R^{ \\prime})^{2}(Z{-}Z^{ \\prime})^{2}+ \\frac{3}{16} \\,RR^{ \\prime} \\,(Z{-}Z^{ \\prime})^{2}}} \\\\ {{Q_{4} \\,= \\,}}&{{ \\frac{31}{2048} \\,(Z{-}Z^{ \\prime})^{4}- \\frac{89}{1024} \\,(R{+}R^{ \\prime})^{2}(Z{-}Z^{ \\prime})^{2}+ \\frac{1}{256} \\,RR^{ \\prime} \\,(Z{-}Z^{ \\prime})^{2}}} \\end{array}", "UniMER-1M_0001113": "T_{ij}^{MERW}= \\frac{1}{ \\psi_{1j}^{2}} \\sum_{k=2}^{N} \\frac{ \\lambda_{1}}{ \\lambda_{1}- \\lambda_{k}} \\left( \\psi_{kj}^{2}- \\psi_{ki} \\psi_{kj} \\frac{ \\psi_{1j}}{ \\psi_{1i}} \\right).", "UniMER-1M_0001114": "\\frac{1}{s_{v}} \\, \\iota_{v} \\, \\omega_{ \\xi}= \\pounds_{v} \\tau- \\mathrm{d} \\mathrm{log} s_{v}+( \\pounds_{v} \\mathrm{log} s_{v}) \\, \\tau \\ .", "UniMER-1M_0001150": "A_{ \\mu}= \\psi^{ \\dagger} \\par tial_{ \\mu} \\psi \\,,", "UniMER-1M_0001144": "\\alpha_{0}^{ \\prime}=0", "UniMER-1M_0001071": "F_{i_{1} . . .i_{n}}( \\beta_{1}, . . . \\beta_{n})= \\langlevac \\vertO(0) \\vert \\beta_{1},i_{1}, . . ., \\beta_{n},i_{n} \\rangle.", "UniMER-1M_0001126": "i \\dot{c}= \\frac{f^{2} \\left(t \\right)}{2} \\left( \\begin{array}{cc}{{0}}&{{ \\Omega \\mathrm{exp} \\left\\{-i \\left[ \\deltat+ \\phi \\left(t \\right)- \\mathbf{k \\cdot} \\frac{ \\mathbf{P}}{M}t- \\frac{1}{2} \\mathbf{k \\cdotg}t^{2} \\right] \\right\\}}} \\\\ {{ \\Omega^{ \\ast} \\mathrm{exp} \\left\\{i \\left[ \\deltat+ \\phi \\left(t \\right)- \\mathbf{k \\cdot} \\frac{ \\mathbf{P}}{M}t- \\frac{1}{2} \\mathbf{k \\cdotg}t^{2} \\right] \\right\\}}}&{{0}} \\end{array} \\right)c.", "UniMER-1M_0001157": "\\mathrm{d}^{3}", "UniMER-1M_0001142": "r(n)={ \\frac{n^{2} \\hbar^{2}}{Zm_{ \\mathrm{{e}}}e^{2}}}", "UniMER-1M_0001139": "\\begin{array}{rl}{{ \\beta \\mu_{ \\mathrm{p}}}}&{{= \\mathrm{ln} \\phi_{ \\mathrm{p}}+(1-N_{ \\mathrm{p}})+(N_{ \\mathrm{p}}-1) \\mathrm{ln}(1- \\phi_{ \\mathrm{s}})-N_{ \\mathrm{p}} \\mathrm{ln}(1- \\phi_{ \\mathrm{s}}- \\phi_{ \\mathrm{p}})+N_{ \\mathrm{p}} \\mathrm{ln} X_{ \\mathrm{p}},}} \\\\ {{ \\beta \\mu_{ \\mathrm{s}}}}&{{= \\mathrm{ln} \\phi_{ \\mathrm{s}}+ \\betau_{ \\mathrm{s}}- \\mathrm{ln}(1- \\phi_{ \\mathrm{s}})+N_{ \\mathrm{s}} \\mathrm{ln} \\left( \\frac{1- \\phi_{ \\mathrm{s}}}{1- \\phi_{ \\mathrm{s}}- \\phi_{ \\mathrm{p}}} \\right)-N_{ \\mathrm{s}} \\left(1- \\frac{1}{N_{ \\mathrm{p}}} \\right) \\frac{ \\phi_{ \\mathrm{p}}}{1- \\phi_{ \\mathrm{s}}}+n \\mathrm{ln} X_{ \\mathrm{s}}.}} \\end{array}", "UniMER-1M_0001147": "\\quad \\: \\left<E_{B/S} \\right>= \\beta \\cdotN_{B} \\approx \\beta \\cdot \\tilde{N}_{B}= \\frac{ \\beta}{ \\delta} \\cdot \\left( \\varepsilon \\cdotN-E_{S} \\right).", "UniMER-1M_0001137": "F", "UniMER-1M_0001156": "c_{L}", "UniMER-1M_0001166": "\\gamma_{ \\mathrm{{max}}} \\simy_{0}^{4}", "UniMER-1M_0001163": "n_{x}", "UniMER-1M_0001171": "\\kappa=1.6", "UniMER-1M_0001174": "b=", "UniMER-1M_0001175": "2", "UniMER-1M_0001176": "n+2", "UniMER-1M_0001138": "\\mathbf{n}=(n_{1},n_{2})= \\frac{ \\nabla \\phi}{ \\vert \\nabla \\phi \\vert}= \\frac{ \\phi_{x} \\vec{i}+ \\phi_{y} \\vec{j}}{ \\sqrt{ \\phi_{x}^{2}+ \\phi_{y}^{2}}}.", "UniMER-1M_0001153": "\\epsilon", "UniMER-1M_0001184": "\\phi", "UniMER-1M_0001152": "\\psi_{n}(x)= \\left\\{ \\begin{array}{ll}{{ \\sqrt{ \\frac{2}{a}} \\mathrm{sin} \\left( \\frac{n \\pix}{a} \\right)}}&{{(0 \\lex \\lea)}} \\\\ {{0}}&{{ \\mathrm{~o~t~h~e~r~w~i~s~e~.~}}} \\end{array} \\right.", "UniMER-1M_0001128": "\\begin{array}{rl}&{{{<0| . . .T( \\pi_{1} \\pi_{2} \\pi_{5} \\stare^{ \\pmip \\cdotx_{5}} \\star \\pi_{5}) . . .|0>}}} \\\\ {{{=}}}&{{{ \\displaystyle \\sum_{ \\lambda_{1} \\lambda_{2}} \\tau_{15}^{ \\lambda_{1}} \\tau_{25}^{ \\lambda_{2}}(D_{15}^{ \\lambda_{1}} \\stare^{ \\pmip_{+} \\cdotx_{5}} \\starD_{25}^{ \\lambda_{2}}+(1 \\leftrightarrow2))<0| . . .|0>.}}} \\end{array}", "UniMER-1M_0001154": "\\sigma_{q(j)} \\tau_{j^{ \\prime}}=(-1)^{ \\delta{_{j,j^{ \\prime}}}} \\tau_{j^{ \\prime}} \\sigma_{q(j)}.", "UniMER-1M_0001135": "t=4", "UniMER-1M_0001191": "\\vec{k}", "UniMER-1M_0001111": "\\begin{array}{rl}{{f(j, \\Delta)}}&{{=3j^{2}-( \\mathsf{k}+3) \\Delta-(2 \\mathsf{k}+3)j}} \\\\ {{ \\mathrm{and} \\quad g_{n}(j, \\Delta)}}&{{= \\frac{1}{n} \\sum_{m=0}^{n-1}f(j-m, \\Delta)=3j^{2}-( \\mathsf{k}+3) \\Delta-(2 \\mathsf{k}+3n)j+(n-1)( \\mathsf{k}+n+1),}} \\end{array}", "UniMER-1M_0001194": "1.11", "UniMER-1M_0001161": "\\rho", "UniMER-1M_0001160": "C_{a}L_{x,a}=C_{ \\bar{a}}L_{x, \\bar{a}}", "UniMER-1M_0001141": "(N, \\phi)", "UniMER-1M_0001162": "\\begin{array}{rl}{{A}}&{{ \\equivR_{ \\ell}+R_{0}(1+ \\beta),}} \\\\ {{B}}&{{ \\equiv \\frac{R_{0} \\mathscr{L}(2+ \\beta)}{1- \\mathscr{L}},}} \\\\ {{ \\tau_{1}}}&{{ \\equiv \\frac{ \\tau_{0}}{1- \\mathscr{L}},}} \\\\ {{ \\tau_{2}}}&{{ \\equiv \\frac{L}{R_{ \\ell}+R_{0}(1+ \\beta)}.}} \\end{array}", "UniMER-1M_0001199": "\\epsilon", "UniMER-1M_0001167": "2", "UniMER-1M_0001164": "\\begin{array}{rl}{{{ \\mathcal{R}}=}}&{{ \\Big[-i(1+ \\phi^{ \\prime \\prime}) \\bar{u}_{+}+ \\left(( \\phi^{ \\prime})^{2}-2| \\bar{u}_{+}|^{2}- \\phi^{ \\prime \\prime} \\right)v_{s}-(v_{s})^{2} \\bar{u}_{+}^{*}-( \\bar{u}_{+})^{2}v_{s}^{*} \\Big] \\bar{u}_{-}.}} \\end{array}", "UniMER-1M_0001190": "0.0", "UniMER-1M_0001177": "0.1", "UniMER-1M_0001203": "\\begin{array}{rl}{{a_{ij}^{(k+1)}}}&{{= \\sigma \\bigg(W^{(k,l)}a_{ij}^{(k)}+b^{(k,l)} \\bigg);}} \\\\ {{m_{ij}^{(l+1)}}}&{{= \\sigma \\bigg(W^{(L)}a_{ij}^{(L)}+b^{(L)} \\bigg);}} \\end{array}", "UniMER-1M_0001204": "A", "UniMER-1M_0001195": "- \\frac{d^{2} \\phi(x_{2})}{dx_{2}^{2}} \\,+ \\,V^{ \\prime}( \\phi(x_{2})) \\,+ \\,g{ \\bar{ \\psi}}(x_{2}) \\psi(x_{2}) \\;= \\;0 \\;,", "UniMER-1M_0001192": "V_{1}", "UniMER-1M_0001165": "C_{l}=C_{l-1}+ \\frac{N \\cdotc_{m} \\cdot \\sigma_{m,l}(| \\vec{v}_{k}|) \\cdot| \\vec{v}_{k}|}{ \\nu^{ \\prime}}", "UniMER-1M_0001159": "X \\timesY= \\{(x,y):x \\inX \\landy \\inY \\}.", "UniMER-1M_0001212": "\\sigma_{i}", "UniMER-1M_0001168": "-", "UniMER-1M_0001198": "M", "UniMER-1M_0001201": "t", "UniMER-1M_0001189": "\\gamma_{ \\sigma \\sigma^{ \\prime}}= \\nablan_{ \\sigma} \\cdot \\nablan_{ \\sigma^{ \\prime}}", "UniMER-1M_0001151": "\\mathbf{L}= \\mathbf{R} \\timesm \\mathbf{V}=I_{R}{ \\boldsymbol{ \\omega}}_{R}.", "UniMER-1M_0001158": "m+n", "UniMER-1M_0001205": "\\lambda_{s}", "UniMER-1M_0001218": "\\pm \\ensuremath{1.25 \\sigma}", "UniMER-1M_0001179": "C_{1}", "UniMER-1M_0001185": "q>2q_{F}", "UniMER-1M_0001197": "(S,T) \\mapsto(x,y)", "UniMER-1M_0001155": "\\mathbf{ \\Psi} \\in \\mathbb{R}^{(N_{x}N_{ \\mathrm{t}}) \\timesr}", "UniMER-1M_0001206": "{ \\bf P}_{ \\perp}({ \\bf q})={ \\bf q} \\times{ \\bf P}( \\bf q)/q", "UniMER-1M_0001173": "\\lambda=1/n \\sigma(E_{e})", "UniMER-1M_0001181": "s=s_{ \\mathrm{~m~a~x~}} \\mathrm{cos}(2 \\pi \\nut)", "UniMER-1M_0001231": "G_{p}", "UniMER-1M_0001178": "\\hat{ \\mathbf{k}}_{ \\perp,in(k_{SVD},b_{0})plane}= \\hat{ \\mathbf{b}}_{0} \\times \\hat{ \\mathbf{k}}_{ \\perp,outof(k_{SVD},b_{0})plane}", "UniMER-1M_0001169": "\\begin{array}{r}{{ \\mathcal{L}= \\frac{1}{2} \\nabla_{ \\mu} \\Phi \\cdot \\nabla^{ \\mu} \\Phi+ \\eta \\sqrt{ \\vert \\par tial_{ \\mu} \\psi \\par tial^{ \\mu} \\psi \\vert}+ \\frac{1}{2} \\par tial_{ \\mu} \\psi \\par tial^{ \\mu} \\psi- \\frac{1}{4}F_{ \\mu \\nu}F^{ \\mu \\nu}- \\mathcal{V}( \\phi_{3}, \\psi).}} \\end{array}", "UniMER-1M_0001208": "\\begin{array}{rl}{{Z_{ij}}}&{{= \\textit{up} \\times \\textnormal{e}^{- \\beta(a_{j_{ \\up arrow}}+ \\omega_{jj_{ \\up arrow}})} \\times \\mathrm{Calc \\_Z_{ij}}(i,j_{ \\up arrow},a, \\omega)}} \\end{array}", "UniMER-1M_0001235": "3d", "UniMER-1M_0001182": "\\widetilde{E}_{ \\mathrm{torsions}}^{ \\mathrm{QM}}=E_{ \\mathrm{total}}^{ \\mathrm{QM}}-E_{ \\mathrm{nonbonded}}^{ \\mathrm{FF}}-E_{ \\mathrm{bond}}^{ \\mathrm{FF}}-E_{ \\mathrm{angle}}^{ \\mathrm{FF}},", "UniMER-1M_0001180": "u^{i}", "UniMER-1M_0001188": "\\sum_{m=0}^{p-1} \\operatorname{Li}_{s}(ze^{2 \\piim/p})=p^{1-s} \\operatorname{Li}_{s}(z^{p}),", "UniMER-1M_0001200": "\\begin{array}{rl}{{ \\mathrm{i} \\par tial_{ \\tau} \\varphi_{ \\mathrm{~n~e~w~}}( \\tau,q)}}&{{=(- \\par tial_{q}^{2}+q^{2}+ \\lambda \\, \\mathrm{~c~o~s~}(a \\,q)) \\, \\varphi_{ \\mathrm{~n~e~w~}}( \\tau,q),}} \\end{array}", "UniMER-1M_0001196": "e_{a}^{i}= \\left( \\begin{array}{ll}{{1- \\frac{ \\beta}{4}y}}&{{- \\frac{ \\beta}{4}x}} \\\\ {{- \\frac{ \\beta}{4}x}}&{{1+ \\frac{ \\beta}{4}y}} \\end{array} \\right), \\quad \\mathbf{A}^{s}= \\frac{ \\beta}{2} \\left( \\begin{array}{l}{{-y}} \\\\ {{x}} \\end{array} \\right).", "UniMER-1M_0001216": "e=1", "UniMER-1M_0001219": "(i)0, \\qquad \\qquad \\qquad(ii) \\frac{1}{3}M_{GUT}(1,1,1,1,-4), \\qquad \\qquad \\qquad(iii)M_{GUT}(2,2,2,-3,-3)", "UniMER-1M_0001217": "\\lambda", "UniMER-1M_0001244": "1-p", "UniMER-1M_0001223": "N=20", "UniMER-1M_0001202": "\\phi_{1}", "UniMER-1M_0001221": "y^{2} \\pm \\sqrt{u-p}(y- \\frac{q}{2(u-p)})+ \\frac{u}{2}=0", "UniMER-1M_0001210": "1.46e \\mathrm{~+~}00 \\pm2.6e \\mathrm{~+~}00", "UniMER-1M_0001249": "\\mathcal{A}_{1}, . . ., \\mathcal{A}_{d}", "UniMER-1M_0001172": "10^{-8}~ \\mathrm{{cm^{3}/s}}", "UniMER-1M_0001228": "f_{o}", "UniMER-1M_0001170": "\\beta(h)", "UniMER-1M_0001225": "\\begin{array}{rl}{{ \\delta \\hat{L}_{ijkl}(t)}}&{{ \\approx \\delta \\hat{L}_{ijkl}^{(0)}(t) \\,,}} \\end{array}", "UniMER-1M_0001230": "V_{i}", "UniMER-1M_0001226": "\\begin{array}{r}{{ \\frac{ \\par tial}{ \\par tial \\rho} \\mathrm{ln}(2)f( \\rho, \\alpha)= \\frac{q-p}{q} \\frac{1}{(1- \\rho) \\rho}+ \\frac{p}{q} \\frac{1 \\!+ \\! \\alpha}{(1 \\!- \\! \\rho)^{2}} \\left( \\frac{(q-p)}{1+(q \\!- \\!p) \\frac{ \\rho+ \\alpha}{1- \\rho}}- \\frac{(q-p)}{1-(q \\!- \\!p) \\frac{ \\rho+ \\alpha}{1- \\rho}} \\right) \\,.}} \\end{array}", "UniMER-1M_0001213": "f(x,p)= \\int \\!dadb~ \\tilde{f}(a,b)~e^{iax}e^{ibp}.", "UniMER-1M_0001252": "\\forallx \\, \\phi(x)", "UniMER-1M_0001211": "\\begin{array}{rlr}{{[(ba)z]_{ \\beta}}}&{{=}}&{{[ \\theta- \\frac{1}{2} \\beta( \\alpha- \\beta)( \\alpha-2 \\beta)+ \\beta \\delta^{f}- \\beta^{2}( \\alpha- \\beta)]w}} \\\\ &{{-}}&{{[ \\theta- \\frac{1}{2} \\beta( \\alpha- \\beta)( \\alpha-2 \\beta)]w}} \\\\ &{{=}}&{{( \\beta \\delta^{f}- \\beta^{2}( \\alpha- \\beta))w.}} \\end{array}", "UniMER-1M_0001227": "\\mathcal{C}_{c}^{(k)}( \\vec{x})= \\left( \\begin{array}{llllllllll}{{ \\mathcal{A}_{1}^{1}( \\vec{x})}}&{{ \\mathcal{A}_{2}^{1}( \\vec{x})}}&{{ \\mathcal{A}_{3}^{1}( \\vec{x})}}&{{0}}&{{0}}&{{ . . .}}&{{0}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{ \\mathcal{A}_{2}^{2}( \\vec{x})}}&{{ \\mathcal{A}_{3}^{2}( \\vec{x})}}&{{ \\mathcal{A}_{4}^{2}( \\vec{x})}}&{{0}}&{{ . . .}}&{{0}}&{{0}}&{{0}}&{{0}} \\\\ {{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\ddots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}}&{{ \\vdots}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{ . . .}}&{{0}}&{{ \\mathcal{A}_{k-2}^{k-2}( \\vec{x})}}&{{ \\mathcal{A}_{k-1}^{k-2}( \\vec{x})}}&{{ \\mathcal{A}_{k}^{k-2}( \\vec{x})}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{ . . .}}&{{0}}&{{0}}&{{ \\mathcal{A}_{k-1}^{k-1}( \\vec{x})}}&{{ \\mathcal{A}_{k}^{k-1}( \\vec{x})}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{ . . .}}&{{0}}&{{0}}&{{0}}&{{ \\mathcal{A}_{k}^{k}( \\vec{x})}} \\end{array} \\right)", "UniMER-1M_0001250": "r \\neq0", "UniMER-1M_0001248": "\\begin{array}{rl}{{ \\bar{n}_{ \\mathrm{~e~x~}}^{ \\mathrm{~T~L~O~}}}}&{{ \\gtrsim \\bar{n}_{ \\mathrm{~e~x~,~b~c~}}^{ \\mathrm{~T~L~O~}}:= \\frac{ \\Theta_{ \\mathrm{~e~l~}}}{ \\tau^{ \\prime \\prime}},}} \\\\ {{ \\bar{n}_{ \\mathrm{~e~x~}}^{ \\mathrm{~L~L~O~}}}}&{{ \\gtrsim \\bar{n}_{ \\mathrm{~e~x~,~b~c~}}^{ \\mathrm{~L~L~O~}}:= \\Theta_{ \\mathrm{~e~l~}}+ \\Theta_{ \\mathrm{~p~h~}} \\tau^{ \\prime}.}} \\end{array}", "UniMER-1M_0001247": "_5", "UniMER-1M_0001222": "St= \\frac{0.332}{ \\sqrt{Re_{x}}Pr^{2/3}} \\,,", "UniMER-1M_0001214": "\\oint_{v} \\nabla \\cdotfdv= \\oint_{S}f \\,dS", "UniMER-1M_0001236": "\\theta", "UniMER-1M_0001243": "D_{ \\mu}", "UniMER-1M_0001246": "i+v \\lek", "UniMER-1M_0001242": "r^{*}", "UniMER-1M_0001238": "0.3 \\ensuremath{ \\, \\mathrm{~G~H~z~}}", "UniMER-1M_0001237": "\\deltaq_{+}= \\deltaq_{-}= \\alpha.", "UniMER-1M_0001234": "\\boldsymbol{ \\Phi}", "UniMER-1M_0001245": "L(G)", "UniMER-1M_0001233": "C_{ \\alpha}(k)= \\int_{- \\infty}^{ \\infty}Cos(k \\cdot \\eta)f_{ \\alpha}( \\eta)d \\eta.", "UniMER-1M_0001240": "l", "UniMER-1M_0001254": "b_{w}(i-1)+ \\theta(j-1) \\leqt \\leqb_{w}(i-1)+ \\thetaj", "UniMER-1M_0001239": "D^{ \\mathrm{S}}(Q^{2})=Q^{2} \\int_{0}^{ \\infty} \\frac{R^{ \\mathrm{S}}(s)}{(s+Q^{2})^{2}}ds=3 \\left[m(Q^{2}) \\right]^{2} \\bigg[1+ \\sum_{n \\geq1}d_{n}^{ \\mathrm{S}} \\bigg( \\frac{ \\alpha_{s}}{ \\pi} \\bigg)^{n} \\bigg]", "UniMER-1M_0001258": "\\mathrm{A}_{ \\mathrm{transmitted}}= \\mathrm{sgn} \\left( \\mathrm{cos}{ \\beta} \\right) \\sqrt{ \\mathrm{I}_{ \\mathrm{transmitted}}}", "UniMER-1M_0001257": "(q_{12}* \\beta^{4})(n)= \\delta^{K}(n),", "UniMER-1M_0001232": "a_{0,BSI,<PERSON>^{5+} \\rightarrowN^{6+}}=2.21", "UniMER-1M_0001229": "W_{ \\tau+ \\Delta \\tau,w}e^{i \\theta_{ \\tau+ \\Delta \\tau,w}}= \\frac{ \\langle \\Phi_{T}| \\Phi_{ \\tau+ \\Delta \\tau,w} \\rangle}{ \\langle \\Phi_{T}| \\Phi_{ \\tau,w} \\rangle}W_{ \\tau,w}e^{i \\theta_{ \\tau,w}}.", "UniMER-1M_0001251": "\\begin{array}{rl}{{ \\lambda^{3}- \\frac{1}{4} \\sum_{m<n}( \\gamma_{m} \\gamma_{n}+ \\kappa_{mn}^{2}) \\lambda- \\frac{1}{4} \\prod_{m<n} \\kappa_{mn}}}&{{=0,}} \\\\ {{ \\sum_{m=1}^{3} \\gamma_{m} \\lambda^{2}- \\frac{1}{4} \\prod_{m=1}^{3} \\gamma_{m} \\left(1+ \\sum_{m<n} \\frac{ \\kappa_{mn}^{2}}{ \\gamma_{m} \\gamma_{n}} \\right)}}&{{=0,}} \\end{array}", "UniMER-1M_0001278": "L=aM", "UniMER-1M_0001259": "v_{n}=- \\frac{ \\varphi_{f}}{ \\varphi_{n}}v_{f}", "UniMER-1M_0001283": "\\psi^{k}", "UniMER-1M_0001268": "{ \\cal{L}}_{-}= \\frac{m}{2}{ \\dot{y_{i}}}^{2}- \\frac{B}{2} \\epsilon_{ij}y_{i} \\dot{y_{j}}", "UniMER-1M_0001275": "V_{eff}(z)= \\frac{3(5 \\alpha+4)k^{2}}{64 \\left({ \\frac{kz}{2}}+1 \\right)^{2}}+{ \\frac{l^{2}}{2R^{2}}} \\left({ \\frac{kz}{2}}+1 \\right)^{{ \\frac{5}{2}}( \\alpha-2)}-{ \\frac{3k}{4}} \\delta(z)", "UniMER-1M_0001280": "\\hat{H}_{X}^{ \\alpha}", "UniMER-1M_0001277": "\\begin{array}{r}{{u=(x^{2}-1) \\sum_{i=1}^{4} \\left[ \\omega_{2i-1} \\mathrm{sin}(i \\pix)+ \\omega_{2i} \\mathrm{cos}(i \\pix) \\right],}} \\end{array}", "UniMER-1M_0001284": "\\begin{array}{rl}{{ \\Phi[ \\chi]}}&{{= \\int_{ \\Omega} \\vartheta \\big( \\mathbf{x}; \\chi \\big) \\, \\mathrm{d} \\Omega}} \\end{array}", "UniMER-1M_0001262": "e^{ \\tauL_{0}}= \\left( \\begin{array}{lll}{{{ \\strut1}}}&{{{ \\tau}}}&{{{ \\tau}}} \\\\ {{{ \\strut \\tau}}}&{{{1+{ \\frac{ \\tau^{2}}{2}}}}}&{{{{ \\frac{ \\tau^{2}}{2}}}}} \\\\ {{{ \\strut- \\tau}}}&{{{-{ \\frac{ \\tau^{2}}{2}}}}}&{{{1-{ \\frac{ \\tau^{2}}{2}}}}} \\end{array} \\right) \\ .", "UniMER-1M_0001290": "c_{0}", "UniMER-1M_0001270": "\\begin{array}{rl}{{p_{t}(i)}}&{{= \\sum_{j,l_{1},...,l_{k-2}=1}^{n}T_{i,j,l_{1},...,l_{k-2}} \\left( \\frac{1}{n} \\right)^{k-1}}} \\\\ &{{= \\left( \\frac{1}{n} \\right)^{k-1} \\sum_{l_{1},...,l_{k-2}=1}^{n} \\sum_{j=1}^{n}T_{i,j,l_{1},...,l_{k-2}}}} \\\\ &{{= \\left( \\frac{1}{n} \\right)^{k-1}n^{k-2}}} \\\\ &{{= \\frac{1}{n}.}} \\end{array}", "UniMER-1M_0001293": "\\epsilon_{i}", "UniMER-1M_0001295": "m=-1", "UniMER-1M_0001255": "E_{ \\{0 \\}}^{ \\sc riptscriptstyle0}= \\frac{m}{ \\sqrt{2hm}} \\sum_{j} \\Omega_{j}^{ \\sc riptscriptstyle0}.", "UniMER-1M_0001298": "u", "UniMER-1M_0001300": "C_{s}", "UniMER-1M_0001272": "{ \\bf C}_{00}= \\mathbb{E}[{ \\bf q}(0){ \\bf q}^{*}(0)] \\mathrm{~,~}", "UniMER-1M_0001285": "f_{I}(s)=t_{I}(s)+D_{I}^{-1}(s) \\{(c_{I}+d_{I}s)- \\frac{s^{2}}{ \\pi} \\int_{4m_{ \\pi}^{2}}^{ \\infty} \\frac{dx}{x^{2}} \\frac{t_{I}(x)ImD_{I}(x)}{x-s-i \\epsilon} \\}", "UniMER-1M_0001273": "b", "UniMER-1M_0001256": "\\begin{array}{rl}{{ \\mathbb{P}_{n}=}}&{{{ \\bf C}i^{n/2-1} \\int_{0}^{+ \\infty} \\frac{d( \\omega \\tau)}{( \\omega \\tau)^{D/2}}J_{n/2}[ \\frac{U_{ \\mathrm{p}}}{ \\hbar \\omega} \\omega \\tau \\gamma( \\omega \\tau) \\alpha( \\omega \\tau)]}} \\end{array}", "UniMER-1M_0001281": "\\begin{array}{rlr}{{P(x,y|Z)}}&{{=}}&{{ \\frac{1+x \\,E_{1}(Z)+y \\,E_{2}(Z)+x \\,y \\,E_{12}(Z)}{4}= \\frac{1+x \\,E_{1}(Z)}{2} \\frac{1+y \\,E_{2}(Z)}{2}+x \\,y \\, \\frac{E_{12}(Z)-E_{1}(Z) \\,E_{2}(Z)}{4}}} \\\\ &{{=}}&{{P(x|Z)P(y|Z)+x \\,y \\, \\frac{E_{12}(Z)-E_{1}(Z) \\,E_{2}(Z)}{4} \\;,}} \\end{array}", "UniMER-1M_0001279": "i=1,2", "UniMER-1M_0001271": "x^{x} \\geq \\left({ \\frac{1}{e}} \\right)^{ \\frac{1}{e}}.", "UniMER-1M_0001260": "\\begin{array}{rlrl}{{{2}d}}&{{ \\in \\mathbb{N}, \\qquad}}&&{{ \\theta_{lex}(h) \\leq2V_{d}(c)d! \\beta^{ \\alpha} \\gamma_{abs} \\sum_{k=0}^{d} \\frac{ \\left( \\frac{ \\psi(h)}{c} \\right)^{k}}{k! \\left( \\frac{ \\psi(h)}{c}+ \\beta \\right)^{ \\alpha-d-1+k}} \\frac{ \\Gamma( \\alpha-d-1+k)}{ \\Gamma( \\alpha)},}} \\end{array}", "UniMER-1M_0001263": "k=2", "UniMER-1M_0001287": "CD", "UniMER-1M_0001286": "\\begin{array}{rlr}{{ \\par tial_{t} \\rho_{ \\sigma}(x,t)=}}&&{{ \\par tial_{x}[ \\rho_{ \\sigma}(x,t)(-v_{0} \\sigma+W^{ \\prime}(x)+N \\intdy \\tilde{ \\rho}(y,t; \\sigma) \\tilde{V}(x-y))]- \\gamma \\rho_{ \\sigma}(x,t)+ \\gamma \\rho_{- \\sigma}(x,t)}} \\\\ &&{{+ \\frac{T}{N} \\par tial_{x}^{2} \\rho_{ \\sigma}(x,t)+ \\frac{1}{N} \\par tial_{x}[ \\sqrt{2T \\rho_{ \\sigma}(x,t)} \\eta_{ \\sigma}(x,t)]+ \\frac{ \\sigma}{ \\sqrt{N}} \\zeta(x,t)}} \\end{array}", "UniMER-1M_0001288": "\\Omega(0,0)= \\frac{1}{C_{x}C_{y} \\Sigma_{x} \\Sigma_{y}}; \\quad \\mathcal{L}(0,0)=fN_{1}N_{2} \\Omega(0,0),", "UniMER-1M_0001265": "y_{jR}", "UniMER-1M_0001305": "x", "UniMER-1M_0001289": "P=2.6", "UniMER-1M_0001269": "{ \\calT}_{ \\pm \\, \\pm}^{ \\,0}= \\pm \\, \\sqrt{2} \\,K^{ \\prime} \\, \\frac{1-x}{ \\sqrt{x(1-x)}} \\, \\mp \\, \\frac{1}{16 \\sqrt{2}} \\,K^{ \\prime} \\, \\frac{1-4(1-x) \\mathrm{cos}^{2} \\theta}{x^{2}(1-x) \\sqrt{x(1-x)}} \\, \\beta^{2} \\;.", "UniMER-1M_0001311": "\\mathcal{P}", "UniMER-1M_0001314": "z", "UniMER-1M_0001317": "\\theta_{3}", "UniMER-1M_0001264": "\\left| \\boldsymbol{E}_{*} \\right|=78 \\cdot10^{-9} \\mathrm{~V~m~}^{-1}", "UniMER-1M_0001291": "h_{ \\mathfrak{n} \\mathfrak{n}}= \\big\\langle \\bar{ \\phi}_{ \\mathfrak{n}}( \\vec{r}) \\big|- \\frac{1}{2} \\, \\hat{ \\nabla}^{2}-(| \\vec{r}|^{-1}+ \\frac{1}{2} \\,R^{-1}) \\, \\hat{1} \\big| \\bar{ \\phi}_{ \\mathfrak{n}}( \\vec{r}) \\big \\rangle", "UniMER-1M_0001261": "\\theta= \\left( \\begin{array}{c}{{{ \\theta_{1}}}} \\\\ {{{ \\theta_{2}}}} \\\\ {{{ \\theta_{3}}}} \\\\ {{{ \\theta_{4}}}} \\end{array} \\right)", "UniMER-1M_0001304": "\\Omega_{s}", "UniMER-1M_0001329": "L", "UniMER-1M_0001267": "\\phi_{APB}= \\phi_{AP} \\phi_{PB}", "UniMER-1M_0001294": "\\sigma_{z}", "UniMER-1M_0001292": "\\begin{array}{rl}{{D_{ \\alpha}D^{ \\alpha} \\phi}}&{{= \\par tial_{ \\alpha} \\par tial^{ \\alpha} \\phi+2i \\par tial_{ \\alpha} \\big(A^{ \\alpha} \\phi)-i \\big( \\par tial_{ \\alpha}A^{ \\alpha} \\big) \\phi-A_{ \\alpha}A^{ \\alpha} \\phi}} \\\\ &{{= \\par tial_{ \\alpha} \\par tial^{ \\alpha} \\phi+2i \\par tial_{ \\alpha} \\big(A^{ \\alpha} \\phi)-A_{ \\alpha}A^{ \\alpha} \\phi.}} \\end{array}", "UniMER-1M_0001302": "\\begin{array}{rl}{{ \\gamma \\int_{v_{0}}^{v} \\frac{U^{ \\prime}(r)}{vU^{ \\prime}(v)} \\mathrm{d} r-1}}&{{= \\gamma \\int_{v_{0}/v}^{1} \\frac{U^{ \\prime}(zv)}{U^{ \\prime}(v)} \\mathrm{d} z-1}} \\\\ &{{= \\int_{v_{0}/v}^{1} \\gammaz^{ \\gamma-1} \\left[ \\mathrm{exp} \\left\\lbrace- \\int_{z}^{1} \\frac{A(vu)}{u} \\mathrm{d} u \\right\\rbrace-1 \\right] \\mathrm{d} z- \\left( \\frac{v_{0}}{v} \\right)^{ \\gamma}.}} \\end{array}", "UniMER-1M_0001334": "^b", "UniMER-1M_0001310": "O(o)", "UniMER-1M_0001241": "\\begin{array}{rlrl}&{{ \\Delta_{33,+}( \\zeta,k)= \\Delta_{33,-}( \\zeta,k),}}&&{{k \\in \\Gamma_{2}^{(2)},}} \\\\ &{{ \\Delta_{33,+}( \\zeta,k)= \\Delta_{33,-}( \\zeta,k) \\frac{1+r_{1}(k)r_{2}(k)}{f(k)},}}&&{{k \\in \\Gamma_{5}^{(2)},}} \\\\ &{{ \\Delta_{33,+}( \\zeta,k)= \\Delta_{33,-}( \\zeta,k) \\frac{1}{f( \\omega^{2}k)},}}&&{{k \\in \\Gamma_{10}^{(2)}.}} \\end{array}", "UniMER-1M_0001299": "z=r( \\mathrm{cos} \\theta+i \\mathrm{sin} \\theta)", "UniMER-1M_0001315": "\\displaystyle \\sum_{n}|A_{n}|^{2}=1.", "UniMER-1M_0001282": "\\begin{array}{rl}&{{ \\mathrm{Occ}_{G, \\mathcal{P}}(z):= \\sum_{H \\in \\mathcal{P}} \\frac{ \\mathrm{Occ}_{G}(H)z^{N(H)-N(G)}}{N(H)!}; \\qquad \\mathrm{Occ}_{G, \\mathcal{P}^{ \\bullet}}(z):= \\sum_{H \\in \\mathcal{P}^{ \\bullet}} \\frac{ \\mathrm{Occ}_{G}(H)z^{N(H)-N(G)}}{N(H)!}}} \\\\ &{{ \\mathrm{Occ}_{G,a, \\mathcal{P}^{ \\bullet}}(z):= \\sum_{H \\in \\mathcal{P}^{ \\bullet}} \\frac{ \\mathrm{Occ}_{G,a}(H)z^{N(H)-N(G)+1}}{N(H)!}}} \\end{array}", "UniMER-1M_0001308": "u(x,y)= \\left\\{ \\begin{array}{cl}{{c_{1}^{-1}e^{10(r^{2}-R^{2})} \\  \\ }}&{{ \\mathrm{if} \\ r<R \\  \\  \\  \\ ( \\mathrm{or~in} \\  \\Omega_{1}),}} \\\\ {{c_{2}^{-1}e^{10(R^{2}-r^{2})}+ \\big(c_{1}^{-1}-c_{2}^{-1} \\big)e^{r^{2}-R^{2}} \\  \\ }}&{{ \\mathrm{otherwise} \\ ( \\mathrm{or~in} \\  \\Omega_{2}),}} \\end{array} \\right.", "UniMER-1M_0001319": "^4", "UniMER-1M_0001321": "\\hat{n}= \\hat{n}_{ \\mathrm{L}}+ \\hat{n}_{ \\mathrm{R}}", "UniMER-1M_0001335": "U_{ \\mathrm{pl}}(y)={ \\calU}_{ \\mathrm{pl}}(y)+ \\sum_{n=1}^{N_{ \\mathrm{max}}} \\left[{ \\calU}_{ \\mathrm{pl}}(y+nd)+{ \\calU}_{ \\mathrm{pl}}(y-nd) \\right]+C \\,,", "UniMER-1M_0001322": "X,Y,H", "UniMER-1M_0001301": "N_{f}^{(543k)}=N_{f}^{(345k)},", "UniMER-1M_0001337": "X^{I}= \\frac{q^{I}}{Z} \\,, \\qquad X_{I}= \\frac{V_{I}}{X^{J}V_{J}} \\,.", "UniMER-1M_0001332": "St \\in[1-2]", "UniMER-1M_0001316": "\\bar{Z}=[]", "UniMER-1M_0001307": "\\begin{array}{rl}{{ \\mathbb{E}[S^{2}|{ \\bf n}, \\beta]}}&{{= \\intd \\boldsymbol{ \\rho} \\;S( \\boldsymbol{ \\rho}| \\beta)^{2} \\;p( \\boldsymbol{ \\rho}|{ \\bf n})= \\sum_{i \\neqj}^{K} \\frac{(n_{i}+ \\beta) \\,(n_{j}+ \\beta)}{(N+K \\beta+1) \\,(N+K \\beta)} \\,I_{i,j}+ \\sum_{i=1}^{K} \\frac{(n_{i}+ \\beta+1) \\,(n_{i}+ \\beta)}{(N+K \\beta+1) \\,(N+K \\beta)} \\,J_{i} \\;,}} \\end{array}", "UniMER-1M_0001324": "\\left( \\begin{array}{ll}{{{U^{IJ}_{ij}}}}&{{{ \\bar{V}^{IJij}}}} \\\\ {{{V_{IJij}}}}&{{{ \\bar{U}_{IJ}^{ij}}}} \\end{array} \\right) \\ .", "UniMER-1M_0001326": "P_{ \\varphi}(x_{ \\varphi})= \\langle{x_{ \\varphi}}|{ \\hat{ \\rho}}|{x_{ \\varphi}} \\rangle", "UniMER-1M_0001320": "\\phi(z)", "UniMER-1M_0001297": "\\begin{array}{rl}{{ \\lambda(P_{1},P_{2})}}&{{= \\left\\{ \\begin{array}{ll}{{0}}&{{P_{1} \\mathrm{~c~o~m~m~u~t~e~s~w~i~t~h~} P_{2}}} \\\\ {{1}}&{{ \\mathrm{~o~t~h~e~r~w~i~s~e~}}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0001340": "1.92 \\", "UniMER-1M_0001345": "20.00", "UniMER-1M_0001354": "f", "UniMER-1M_0001356": "2^{ \\circ}", "UniMER-1M_0001357": "i=1,2,...,N_{s}", "UniMER-1M_0001348": "\\mathbf{P}_{1}, \\mathbf{P}_{2}", "UniMER-1M_0001331": "t_{E} \\gg \\bar{L}", "UniMER-1M_0001362": "<200", "UniMER-1M_0001349": "v={ \\frac{ \\Gamma}{4 \\pir}} \\left[ \\mathrm{cos} A- \\mathrm{cos} B \\right]", "UniMER-1M_0001352": "\\tau_{k}=- \\frac{k}{ \\mathrm{log} K_{k}}= \\frac{1}{- \\mathrm{log} \\left(1-p_{+}-p_{-} \\right)+ \\frac{1}{k} \\mathrm{log} \\gamma}.", "UniMER-1M_0001342": "\\forallx \\in{U}: \\mu_{A \\setminus{B}}(x)= \\mathrm{min}( \\mu_{A}(x),1- \\mu_{B}(x)).", "UniMER-1M_0001325": "130s", "UniMER-1M_0001330": "\\hat{P}_{0}(5 \\Deltat;{ \\mathbf{z})", "UniMER-1M_0001368": "\\zeta=x+iy", "UniMER-1M_0001369": "\\rho_{1}", "UniMER-1M_0001350": "\\ddot{X}^{i}=-[[X^{i},X^{j}],X^{j}].", "UniMER-1M_0001360": "\\begin{array}{r}{{N_{ \\mathrm{ad}}P+N_{ \\mathrm{ad}} \\frac{B}{E_{ \\mathrm{2}}} \\left( \\frac{dP}{dt} \\right)= \\left( \\frac{BB_{ \\mathrm{a}}}{E_{ \\mathrm{2}}} \\right) \\left( \\frac{dQ}{dt} \\right)+ \\left(B+B_{ \\mathrm{a}} \\right)Q+N_{ \\mathrm{ad}} \\frac{B}{E_{ \\mathrm{2}}} \\left( \\frac{dP_{ \\mathrm{E1}}}{dt} \\right)+N_{ \\mathrm{ad}}P_{ \\mathrm{E1}},}} \\end{array}", "UniMER-1M_0001363": "S_{1}^{2}+S_{2}^{2}+S_{3}^{2}<1", "UniMER-1M_0001323": "\\begin{array}{rl}{{ \\par tial_{t} \\overline{{{ \\rho}}}_{f}(p)=}}&{{- \\sum_{j=1,2} \\Gamma_{t}^{(j)}(p-p_{j}) \\overline{{{ \\rho}}}_{f}(p)}} \\\\ &{{+ \\frac{t(1+ \\delta_{ab})}{ \\hbar^{2}} \\int_{- \\infty}^{ \\infty} \\mathrm{d} qG_{0}(q) \\Big[ \\overline{{{ \\rho}}}_{f}(p-q)- \\overline{{{ \\rho}}}_{f}(p) \\Big]}} \\end{array}", "UniMER-1M_0001374": "s_{m}", "UniMER-1M_0001377": "N=14", "UniMER-1M_0001366": "0.97", "UniMER-1M_0001274": "f(p)=f( \\mathrm{Re} \\:a,b, \\mathrm{Im} \\:a, \\mathrm{arg} \\:A)=", "UniMER-1M_0001359": "\\forallt \\inB^{1}:Q(t)-P(t)=Rx+( \\gamma-1)(R \\circP)x- \\gamma(R \\circv \\circJ)x.", "UniMER-1M_0001313": "T_{D}({ \\bf r^{ \\prime \\prime}},{ \\bf r^{ \\prime}};E)= \\sum_{n=1}^{ \\infty}T_{D}^{(n)}({ \\bf r^{ \\prime \\prime}},{ \\bf r^{ \\prime}};E) \\;,", "UniMER-1M_0001346": "\\begin{array}{r}{{ \\operatorname{sup}_{y>0} \\int_{- \\infty}^{ \\infty}|F(x+iy)|^{2} \\,dx< \\infty.}} \\end{array}", "UniMER-1M_0001383": "\\tilde{ \\phi}_{ \\mathrm{fiber}}(t)", "UniMER-1M_0001384": "Z", "UniMER-1M_0001370": "Y=y* \\left(L_{y} \\right)^{-1}", "UniMER-1M_0001381": "\\displaystyle Z_{ \\sc riptscriptstyleCPI}^{ \\sc riptscriptstyleBFV}= \\int{ \\calD} \\Lambda_{ \\sc riptscriptstyleA}{ \\calD} \\xi^{ \\sc riptscriptstyleA}{ \\calD} \\bar{ \\Gamma}_{ \\sc riptscriptstyleA}{ \\calD} \\Gamma^{ \\sc riptscriptstyleA}e^{i \\intd^{4}x \\widetilde{ \\calL}^{ \\sc riptscriptstyleBFV}}", "UniMER-1M_0001351": "1/120=0.0 \\ 0 \\ 0 \\ 0 \\ 1_{!}", "UniMER-1M_0001388": "\\gamma=0.4", "UniMER-1M_0001353": "\\Phi_{ \\mathrm{ac}}(0)=0.35", "UniMER-1M_0001390": "x", "UniMER-1M_0001338": "\\phi^{(0)} \\ = \\  \\left[ \\begin{array}{cl}{{{ \\displaystyle 0 \\ ,}}}&{{{r \\leR_{g}}}} \\\\ {{{ \\displaystyle v \\left(1- \\frac{K_{0}(m_{H}r)}{ \\mathrm{ln}(2/m_{H}R_{g})} \\right),}}}&{{{r \\ggR_{g}.}}} \\end{array} \\right.", "UniMER-1M_0001355": "\\begin{array}{rl}{{U_{ns}^{ \\varkappa}(p( \\bar{ \\alpha}))}}&{{= \\left( \\frac{n_{<}! \\, \\Gamma(2 \\varkappa+n_{>})}{n_{>}! \\, \\Gamma(2 \\varkappa+n_{<})} \\right)^{1/2} \\left(1- \\vert \\alpha \\vert^{2} \\right)^{ \\varkappa} \\,( \\mathrm{sgn}(n-s))^{n-s}}} \\\\ &{{ \\qquad \\timesP_{n_{<}}^{(n_{>}-n_{<} \\,, \\,2 \\varkappa-1)} \\left(1-2 \\vert \\alpha \\vert^{2} \\right) \\,}} \\\\ &{{ \\qquad \\qquad \\times \\left\\lbrace \\begin{array}{cc}{{ \\alpha^{n-s}}}&{{ \\mathrm{if} \\ n_{>}=n}} \\\\ {{ \\bar{ \\alpha}^{s-n}}}&{{ \\mathrm{if} \\ n_{>}=s}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0001341": "\\stackrel{ \\bullet}{M} \\left(0 \\right)= \\frac{d}{d \\lambda} \\mid_{ \\lambda=0}M \\left( \\lambda \\right)=M \\left(0 \\right) \\int_{0}^{2 \\pi}r_{t} \\left(y \\right)dy,", "UniMER-1M_0001343": "l_{ \\mathrm{~d~i~f~f~}}", "UniMER-1M_0001364": "\\mathrm{Pe} \\to \\infty", "UniMER-1M_0001395": "\\mathbf{l}", "UniMER-1M_0001396": "907", "UniMER-1M_0001365": "256,000 \\times5^{3}", "UniMER-1M_0001380": "\\pm", "UniMER-1M_0001372": "g_{2} \\left(0 \\right) \\to2", "UniMER-1M_0001328": "\\overline{{{u_{1}^{ \\prime}u_{3}^{ \\prime}}}}= \\overline{{{u_{2}^{ \\prime}u_{3}^{ \\prime}}}}=0", "UniMER-1M_0001373": "f \\lambda/D", "UniMER-1M_0001327": "x", "UniMER-1M_0001382": "a", "UniMER-1M_0001358": "k( \\phi)=0.107 \\phi-0.037", "UniMER-1M_0001371": "kT_{H}^{ \\mathrm{RN}}={ \\frac{ \\hbar}{4 \\pir_{H}}} \\left(1-{ \\frac{GQ^{2}}{r_{H}^{2}}} \\right).", "UniMER-1M_0001347": "\\mathrm{PDF}(u_{y})", "UniMER-1M_0001333": "\\operatorname{lim}_{ \\varepsilon \\to0} \\frac{ \\nu}{ \\varepsilon^{2}} \\int_{D}p^{D}(s, \\xi,t,y) \\theta_{+}( \\xi_{1},s) \\phi^{ \\prime \\prime}( \\xi_{2}/ \\varepsilon) \\textrm{d} \\xi= \\nu \\int_{0}^{+ \\infty} \\frac{ \\par tial}{ \\par tial \\xi_{2}}p^{D}(s,( \\xi_{1},0+),t,y) \\theta_{+}( \\xi_{1},s) \\textrm{d} \\xi_{1},", "UniMER-1M_0001361": "\\begin{array}{rl}{{K_{i}^{a}}}&{{= \\frac{2U_{0i}}{w_{0,ai}^{2}},K_{i}^{b}= \\frac{2U_{0i}}{w_{0,bi}^{2}},}} \\\\ {{K_{i}^{c}}}&{{= \\frac{U_{0i} \\lambda^{2}}{2 \\pi^{2}} \\left( \\frac{1}{w_{0,ai}^{4}}+ \\frac{1}{w_{0,bi}^{4}} \\right),}} \\end{array}", "UniMER-1M_0001391": "x_{1}= \\betax~, \\  \\  \\  \\  \\  \\ x_{2}= \\beta(1-x)~,", "UniMER-1M_0001309": "\\begin{array}{rl}{{ \\Delta( \\mathcal{S}q^{2m+1})}}&{{=u^{-m} \\Delta(Sq_{C_{2}}^{2m+1})}} \\\\ {{ \\&=u^{-m} \\sum_{0 \\leqi \\leq2m+1}Sq_{C_{2}}^{i} \\otimesSq_{C_{2}}^{2m+1-i}}} \\\\ {{ \\&= \\sum_{0 \\leqi \\leq2m+1}u^{- \\lfloori/2 \\rfloor}Sq_{C_{2}}^{i} \\otimesu^{- \\lfloor(2m+1-i)/2 \\rfloor}Sq_{C_{2}}^{2m+1-i}}} \\\\ {{ \\&= \\sum_{0 \\leqi \\leq2m+1} \\mathcal{S}q_{C_{2}}^{i} \\otimes \\mathcal{S}q_{C_{2}}^{2m+1-i}}} \\end{array}", "UniMER-1M_0001401": "1.0J", "UniMER-1M_0001398": "G(w,z):= \\sum_{m,n \\geq0}g_{m,n}w^{m}z^{n}", "UniMER-1M_0001400": "\\varepsilon_{ \\mathrm{~M~g~O~}}=0.5", "UniMER-1M_0001394": "\\varepsilon_{0}", "UniMER-1M_0001392": "L_{b}", "UniMER-1M_0001403": "\\begin{array}{rl}{{I_{ \\mathrm{D}}(x,y)}}&{{= \\frac{c \\epsilon_{0}}{2} \\left|E_{ \\mathrm{S}}(x,y)+E_{ \\mathrm{R}}(x,y) \\right|^{2}}} \\end{array}", "UniMER-1M_0001367": "\\frac{ \\mathrm{tan} \\alpha- \\mathrm{tan} \\beta}{1+ \\mathrm{tan} \\alpha \\mathrm{tan} \\beta}", "UniMER-1M_0001379": "\\mathbf{F}_{-1}( \\zeta_{1}, \\zeta_{2})= \\left[ \\begin{array}{l}{{2}} \\\\ {{hphantom{0}}} \\end{array} \\right], \\quad \\mathbf{F}_{0}( \\zeta_{1}, \\zeta_{2})=2 \\left[ \\begin{array}{l}{{ \\mathrm{cos} \\zeta_{+} \\mathrm{cos} \\zeta_{-}}} \\\\ {{- \\mathrm{sin} \\zeta_{+} \\displaystyle \\frac{ \\mathrm{sin} \\zeta_{-}}{ \\Delta}}} \\end{array} \\right].", "UniMER-1M_0001420": "2 \\pi", "UniMER-1M_0001422": "p_{ij}^{ \\leftarrow}", "UniMER-1M_0001409": "\\mathbf{x}_{1}^{ \\prime} \\neq \\mathbf{x}_{2}^{ \\prime}", "UniMER-1M_0001389": "\\int_{0}^{+ \\infty} \\frac{ \\mathrm{sin}(x)}{x}dx= \\frac{ \\pi}{2}", "UniMER-1M_0001411": "\\DeltaN", "UniMER-1M_0001412": "(Y,{ \\mathcal{B}}, \\nu,S)", "UniMER-1M_0001413": "\\omega_{t}", "UniMER-1M_0001399": "\\approx2196/2", "UniMER-1M_0001431": "\\sigma_{1}", "UniMER-1M_0001417": "\\nu", "UniMER-1M_0001419": "c_{P}", "UniMER-1M_0001339": "\\aleph_{ \\alpha}^{ \\aleph_{ \\beta}}", "UniMER-1M_0001393": "\\tau_{0}^{p}- \\tau^{p}= \\frac{1}{7990 \\mathrm{ \\ mus}^{-1}}- \\frac{1}{7990 \\mathrm{ \\ mus}^{-1}+513 \\mathrm{ \\ mus}^{-1}} \\approx7", "UniMER-1M_0001344": "\\mathrm{D}_{L}^{ \\prime}(s)=- \\frac{(s-s_{0})^{n}}{ \\pi} \\int_{s_{th}}^{ \\infty}ds^{ \\prime} \\frac{ \\nu(s^{ \\prime})^{L} \\rho(s^{ \\prime}) \\mathrm{N}_{L}^{ \\prime}(s^{ \\prime})}{(s^{ \\prime}-s)(s^{ \\prime}-s_{0})^{n}}+ \\sum_{m=0}^{n-1} \\overline{{{a}}}_{m}s^{m}", "UniMER-1M_0001416": "\\tilde{ \\chi}^{ \\mu_{1} . . . \\mu_{p-1}}= \\par tial_{ \\mu}A^{ \\mu \\mu_{1} . . . \\mu_{p-1}}+ \\frac{1}{p-1} \\par tial^{ \\left[ \\mu_{1} \\right.}B^{(1) \\left. \\mu_{2} . . . \\mu_{p-1} \\right]},", "UniMER-1M_0001386": "\\mathbf{F}^{pp} \\equiv0", "UniMER-1M_0001421": "20.55 \\pm0.47", "UniMER-1M_0001418": "\\begin{array}{r}{{T_{ \\varphi}^{ \\mu \\nu}=2 \\, \\varphi^{ \\dag} \\, \\frac{ \\deltaA_{ \\varphi}}{ \\deltag_{ \\mu \\nu}} \\, \\varphi}} \\end{array}", "UniMER-1M_0001426": "y", "UniMER-1M_0001442": "\\langle \\hat{e}_{n} \\rangle= \\langle \\hat{e}_{0} \\rangle", "UniMER-1M_0001405": "\\bar{ \\epsilon}_{ij}= \\epsilon \\, \\delta_{ij}+{ \\frac{ \\mathrm{i}}{{2{ \\omega}^{2}}}} \\left(a_{i} \\,c_{n}+a_{n} \\,c_{i} \\right){ \\epsilon}_{nbj} \\,k_{b} \\;.", "UniMER-1M_0001407": "\\begin{array}{r}{{ \\hat{H}= \\frac{ \\hat{p}^{2}}{2 \\mu}+ \\frac{1}{2} \\mu \\omega^{2} \\hat{x}^{2}+U_{m}( \\hat{x}) \\otimes \\hbar \\big(2J \\hat{ \\Sigma}_{X}- \\Delta_{m} \\big) \\ ,}} \\end{array}", "UniMER-1M_0001406": "\\begin{array}{rl}{{J}}&{{= \\sum_{i<j}^{N_{ \\mathrm{e}}}u(r_{ij})+ \\sum_{i}^{N_{ \\mathrm{e}}} \\sum_{I}^{N_{ \\mathrm{n}}} \\chi(r_{iI})}} \\end{array}", "UniMER-1M_0001424": "q", "UniMER-1M_0001402": "{ \\mathcal{B}}_{2}= \\{(R_{1},P_{1},y_{1}),(R_{2},P_{2},y_{2}), . . . \\}", "UniMER-1M_0001427": "\\ulcorner", "UniMER-1M_0001449": "F", "UniMER-1M_0001453": "\\chi_{p}", "UniMER-1M_0001425": "{ \\calV}_{4}= \\big[{ \\calV}_{e},{ \\calV}_{ef};{ \\calV}_{ef}^{ \\mathrm{T}},{ \\calV}_{f} \\big]", "UniMER-1M_0001410": "S_{(-1)/3}=- \\, \\sum_{ \\phi} \\phi(x_{0}) \\,J_{ \\phi}({ \\calM})", "UniMER-1M_0001457": "\\theta_{ \\mathrm{max}}", "UniMER-1M_0001378": "| \\mathcal{V}_{0}|>| \\mathcal{V}_{1}|>| \\mathcal{V}_{2}|> . . .>| \\mathcal{V}_{k}|", "UniMER-1M_0001460": "U(z,t)", "UniMER-1M_0001385": "\\begin{array}{rl}{{ \\|u-u_{k} \\|_{L^{ \\infty}(B_{2^{-k}})} \\leC2^{-2k} \\bigg(}}&{{2^{- \\alphak} \\|f \\|_{C^{0, \\alpha}(B_{2^{-k}})}}} \\\\ &{{+2^{- \\alphak} \\|D^{2}u \\|_{L^{ \\infty}(B_{2^{-k}})} \\sum_{i,j=1}^{n} \\|a_{ij} \\|_{C^{0, \\alpha}(B_{2^{-k}})} \\bigg).}} \\end{array}", "UniMER-1M_0001404": "\\begin{array}{rl}{{ \\bigcap_{ \\lambda \\in \\Lambda} \\mathcal{C}_{ \\lambda} \\cap \\left(- \\bigcap_{ \\lambda \\in \\Lambda} \\mathcal{C}_{ \\lambda} \\right)=}}&{{ \\bigcap_{ \\lambda \\in \\Lambda} \\left( \\mathcal{C}_{ \\lambda} \\cap(- \\mathcal{C}_{ \\lambda}) \\right)= \\bigcap_{ \\lambda \\in \\Lambda} \\{0 \\}= \\{0 \\}.}} \\end{array}", "UniMER-1M_0001415": "c_{A}=c_{A}(N, \\ensuremath{ \\varepsilon}), \\mathbf{v}_{A}= \\mathbf{v}_{A}(N, \\ensuremath{ \\varepsilon}) \\inH^{1}(0,T_{0};L^{2}( \\ensuremath{ \\Omega})) \\capL^{2}(0,T_{0};H^{2}( \\ensuremath{ \\Omega}))", "UniMER-1M_0001430": "\\Nu=0.0073 \\, \\mathrm{~{~R~e~}~}_{b}^{0.802}", "UniMER-1M_0001433": "X_{j}=2 \\,a_{j} \\mathrm{exp} \\left( \\xi_{j}({t,x}) \\right)", "UniMER-1M_0001437": "\\frac{ \\delta \\mathcal{S}[ \\chi( \\Omega)]}{ \\delta \\chi( \\Omega)} \\circ \\hat{ \\chi}( \\Omega)=0.", "UniMER-1M_0001447": "\\eta=0.1", "UniMER-1M_0001440": "M=3", "UniMER-1M_0001438": "\\begin{array}{rlr}{{x_{1}}}&{{=}}&{{(x-x_{0}) \\mathrm{cos} \\Theta}} \\\\ {{y_{1}}}&{{=}}&{{-(x-x_{0}) \\mathrm{sin} \\Theta}} \\end{array}", "UniMER-1M_0001459": "\\mathrm{exp} \\left(xTrU \\right)= \\sum_{r} \\alpha_{r}(x) \\chi_{r}(U),", "UniMER-1M_0001414": "\\mathbf{q}( \\tau, \\eta)= \\hat{ \\mathbf{{q}}}({ \\eta}) \\mathrm{exp}(2 \\pii \\tau)", "UniMER-1M_0001443": "j,", "UniMER-1M_0001428": "\\left( \\begin{array}{l}{{{{Q_{E}}^{U}}}} \\\\ {{{{Q_{M}}^{U}}}} \\end{array} \\right)={ \\frac{e}{4 \\pi}} \\intdS_{k} \\, \\sqrt{-g} \\,U(x) \\left( \\begin{array}{l}{{{F^{k0}}}} \\\\ {{{ \\tilde{F}^{k0}}}} \\end{array} \\right)U^{-1}(x)", "UniMER-1M_0001475": "\\epsilon \\lesssim1", "UniMER-1M_0001441": "\\vec{A}", "UniMER-1M_0001462": "\\sim", "UniMER-1M_0001455": "n_{ph}>5 \\times{10}^{6}", "UniMER-1M_0001436": "\\sum_{t=1}^{T} \\frac{dR(t)}{dt}= \\sum_{t=0}^{T} \\gammaI(t)= \\sum_{t=1}^{T} \\gamma \\beta^{t-1}k \\leq \\gammak \\sum_{t=1}^{T} \\frac{ \\beta_{0}^{t-1}}{r^{t-1}}= \\frac{ \\gammak \\big(( \\beta_{0}/r)^{T}-1 \\big)}{( \\beta_{0}/r) \\big(( \\beta_{0}/r)-1 \\big)}.", "UniMER-1M_0001463": "\\begin{array}{rlr}{{ \\left( \\hat{W}_{S}(u_{0}) \\right)_{ij}}}&{{=e^{- \\beta \\sigma}u_{0} \\quad}}&{{ \\textrm{ifi=j+1}}} \\\\ {{ \\left( \\hat{W}_{S}(u_{0}) \\right)_{ij}}}&{{=j \\quad}}&{{ \\textrm{ifi=j-1}}} \\\\ {{ \\left( \\hat{W}_{S}(u_{0}) \\right)_{ij}}}&{{=0 \\quad}}&{{ \\textrm{otherwise}}} \\end{array}", "UniMER-1M_0001439": "\\mathbf{k}=(k_{1}, . . .,k_{N}) \\in \\mathbb{N}_{0}^{N}", "UniMER-1M_0001482": "2", "UniMER-1M_0001434": "\\pi \\int_{c}^{d} \\{g(y) \\}^{2}dy", "UniMER-1M_0001432": "\\Psi_{+} \\equiv \\left( \\begin{array}{c}{{{ \\chi_{1}}}} \\\\ {{{ \\chi_{2}}}} \\end{array} \\right),~~ \\Psi_{-} \\equiv \\left( \\begin{array}{c}{{{ \\eta_{1}}}} \\\\ {{{ \\eta_{2}}}} \\end{array} \\right),~~ \\Psi= \\frac{1}{ \\sqrt{2}} \\left( \\begin{array}{c}{{{ \\Psi_{+}+ \\Psi_{-}}}} \\\\ {{{ \\Psi_{+}- \\Psi_{-}}}} \\end{array} \\right)", "UniMER-1M_0001435": "\\begin{array}{r}{{ \\nabla=( \\frac{ \\par tial}{ \\par tialx}-( \\frac{z}{ \\phi- \\phi_{2}}( \\phi- \\phi_{2})_{x}+ \\frac{ \\phi_{2,x}}{ \\phi- \\phi_{2}}) \\frac{ \\par tial}{ \\par tialz}, \\frac{1}{ \\phi- \\phi_{2}} \\frac{ \\par tial}{ \\par tialz})=D \\cdot \\nabla^{*},}} \\end{array}", "UniMER-1M_0001487": "X{ \\stackrel{+}{ \\Rightarrow}}X", "UniMER-1M_0001450": "\\nu", "UniMER-1M_0001452": "C_{a,Cl \\left(-1 \\right)}:= \\frac{a_{a,Cl^{-}}}{ \\gamma_{a,Cl^{-}} \\left( \\mu \\right)}.", "UniMER-1M_0001464": "A(x,t),B(x,t),a(x,t),b(x,t)", "UniMER-1M_0001493": "\\theta", "UniMER-1M_0001465": "\\gamma^{+}= \\left( \\begin{array}{cc}{{{1}}}&{{{ \\sigma_{3}}}} \\\\ {{{- \\sigma_{3}}}}&{{{-1}}} \\end{array} \\right)~,~~~~ \\gamma^{-}= \\left( \\begin{array}{cc}{{{1}}}&{{{- \\sigma_{3}}}} \\\\ {{{ \\sigma_{3}}}}&{{{-1}}} \\end{array} \\right) \\;.", "UniMER-1M_0001469": "s=-1", "UniMER-1M_0001446": "f/b", "UniMER-1M_0001387": "\\begin{array}{rl}{{ \\mu=}}&{{ \\zeta^{ \\!-1} \\Bigg( \\frac{1}{n(n \\!-1)} \\sum_{i,j=1 \\atopi \\neqj}^{n} \\zeta \\Big( \\tau^{ \\!-1} \\left(p \\cdot \\tau \\left( \\mu_{ \\alpha_{i}} \\right) \\!+q \\cdot \\tau \\left( \\mu_{ \\alpha_{j}} \\right) \\right) \\Big) \\Bigg)}} \\\\ {{=}}&{{ \\zeta^{ \\!-1} \\Bigg( \\frac{1}{n(n \\!-1)} \\sum_{i,j=1 \\atopi \\neqj,i \\neqi_{0}}^{n} \\zeta \\Big( \\tau^{ \\!-1} \\left(p \\cdot \\tau \\left( \\mu_{ \\alpha_{i}} \\right) \\!+q \\cdot \\tau \\left( \\mu_{ \\alpha_{j}} \\right) \\right) \\Big) \\Bigg.}} \\\\ &{{ \\Bigg. \\!+ \\frac{1}{n(n \\!-1)} \\sum_{j=1 \\atopj \\neqi_{0}}^{n} \\zeta \\Big( \\tau^{ \\!-1} \\left(p \\cdot \\tau \\left( \\mu_{ \\alpha_{i_{0}}} \\right) \\!+q \\cdot \\tau \\left( \\mu_{ \\alpha_{j}} \\right) \\right) \\Big) \\Bigg)}} \\\\ {{<}}&{{ \\zeta^{ \\!-1} \\Bigg( \\frac{1}{n(n \\!-1)} \\sum_{i,j=1 \\atopi \\neqj,i \\neqi_{0}}^{n} \\zeta \\Big( \\tau^{ \\!-1} \\left(p \\cdot \\tau \\left( \\mu_{ \\beta_{i}} \\right)+q \\cdot \\tau \\left( \\mu_{ \\beta_{j}} \\right) \\right) \\Big) \\Bigg.}} \\\\ &{{ \\Bigg.+ \\frac{1}{n(n \\!-1)} \\sum_{j=1 \\atopj \\neqi_{0}}^{n} \\zeta \\Big( \\tau^{ \\!-1} \\left(p \\cdot \\tau \\left( \\mu_{ \\beta_{i_{0}}} \\right)+q \\cdot \\tau \\left( \\mu_{ \\beta_{j}} \\right) \\right) \\Big) \\Bigg)}} \\\\ {{=}}&{{ \\zeta^{ \\!-1} \\Bigg( \\frac{1}{n(n \\!-1)} \\sum_{i,j=1 \\atopi \\neqj}^{n} \\zeta \\Big( \\tau^{ \\!-1} \\left(p \\cdot \\tau \\left( \\mu_{ \\beta_{i}} \\right) \\!+q \\cdot \\tau \\left( \\mu_{ \\beta_{j}} \\right) \\right) \\Big) \\Bigg)}} \\\\ {{=}}&{{ \\mu^{ \\prime},}} \\end{array}", "UniMER-1M_0001429": "C>0", "UniMER-1M_0001477": "(0,-1,1)+(0,2,0)", "UniMER-1M_0001500": "\\Delta", "UniMER-1M_0001478": "k", "UniMER-1M_0001479": "\\omega", "UniMER-1M_0001461": "v \\propto \\! \\,r \\omega \\,,", "UniMER-1M_0001481": "r=1.3", "UniMER-1M_0001480": "\\mu_{D}", "UniMER-1M_0001506": "\\simeq2", "UniMER-1M_0001507": "\\mu \\neq{ \\overline{{{X}}}}", "UniMER-1M_0001444": "\\rho \\mathbf{u}_{i} \\cdot \\mathbf{u}_{i} \\frac{ \\par tial|C_{i}(t)|}{ \\par tialt}+ \\mathbf{u}_{i} \\cdot|C_{i}(t)| \\frac{ \\par tial \\rho \\mathbf{u}_{i}}{ \\par tialt}- \\frac{1}{2} \\rho \\mathbf{u}_{i}^{2} \\frac{ \\par tial|C_{i}(t)|}{ \\par tialt}+ \\sum_{C_{j} \\in \\mathcal{K}_{i}}| \\Gamma_{ij}| \\left(u_{ij}-V_{ij} \\right) \\frac{1}{2} \\rho \\mathbf{u}_{i} \\cdot \\mathbf{u}_{j}=0,", "UniMER-1M_0001489": "J_{y}", "UniMER-1M_0001468": "\\begin{array}{rl}{{M_{ \\mathrm{{total}}}(E,s/d)=}}&{{ \\frac{ \\mu_{1s,3p} \\mu_{3p,E}}{4 \\omega_{ \\mathrm{IR}} \\omega_{ \\mathrm{H15}}} \\mathrm{e}^{- \\mathrm{i} \\omega_{ \\mathrm{IR}} \\tau} \\int \\mathrm{d} t \\int^{t} \\mathrm{d} t^{ \\prime} \\sqrt{I_{ \\mathrm{IR}}(t)I_{ \\mathrm{H15}}(t^{ \\prime})} \\times}} \\\\ &{{ \\mathrm{exp} \\left\\{- \\mathrm{i}[ \\Phi_{3p}(t)+ \\omega_{ \\mathrm{IR}}t-Et- \\Phi_{3p}(t^{ \\prime})+ \\Phi_{1s}(t^{ \\prime})- \\omega_{ \\mathrm{IR}}t^{ \\prime}] \\right\\}+}} \\\\ &{{ \\pi \\frac{ \\mu_{1s,E^{ \\prime}} \\mu_{E^{ \\prime},E}}{4 \\omega_{ \\mathrm{IR}} \\omega_{ \\mathrm{H17}}} \\mathrm{e}^{ \\mathrm{i} \\omega_{ \\mathrm{IR}} \\tau} \\int \\sqrt{I_{ \\mathrm{IR}}(t)I_{ \\mathrm{H17}}(t)} \\mathrm{exp} \\left\\{- \\mathrm{i}[- \\omega_{ \\mathrm{IR}}t-Et+ \\omega_{2}t+ \\Phi_{1s}(t)] \\right\\} \\, \\mathrm{d} t.}} \\end{array}", "UniMER-1M_0001510": "\\delta_{b} \\approx13 \\mathrm{mm}", "UniMER-1M_0001511": "\\begin{array}{rl}{{ \\frac{dS}{dt}}}&{{=- \\betaSI- \\lambdaSI}} \\\\ {{ \\frac{dI}{dt}}}&{{= \\betaSI- \\gammaI}} \\\\ {{ \\frac{dR}{dt}}}&{{= \\gammaI}} \\\\ {{ \\frac{dV}{dt}}}&{{= \\lambdaSI}} \\end{array}", "UniMER-1M_0001501": "\\begin{array}{rl}{{ \\int_{0}^{ \\frac{ \\pi}{4}}}}&{{ \\frac{ \\mathrm{cos}^{2}( \\theta)- \\frac{ \\lambda_{n}}{ \\kappa_{0}^{2}}}{ \\big(1- \\frac{4 \\kappa_{0}^{2}}{( \\kappa_{0}^{2}- \\lambda_{n})^{2}} \\mathrm{sin}^{2}( \\theta) \\big) \\sqrt{1-p_{n}^{2} \\mathrm{sin}^{2}( \\theta)}} \\,d \\theta}} \\\\ &{{ \\leq \\frac{5 \\pi \\varepsilon+o(1)-C(M)}{ \\lambda_{n}} \\to- \\infty \\quad \\mathrm{as~n \\to \\infty~,}}} \\end{array}", "UniMER-1M_0001514": "j \\to \\mathbf{j}", "UniMER-1M_0001485": "{A^{ \\mu}}(x)= \\int{ \\frac{d^{4}k}{(2 \\pi)^{4}}}e^{-ik \\cdotx}{A^{ \\mu}}(k).", "UniMER-1M_0001490": "N_{1} \\setminusV \\congN_{M_{1}}V \\setminusV \\toN_{M_{2}}V \\setminusV \\congN_{2} \\setminusV,", "UniMER-1M_0001470": "{ \\begin{array}{rl}{{ \\mathrm{cosh}(ix)}}&{{={ \\frac{1}{2}} \\left(e^{ix}+e^{-ix} \\right)= \\mathrm{cos} x}} \\\\ {{ \\mathrm{sinh}(ix)}}&{{={ \\frac{1}{2}} \\left(e^{ix}-e^{-ix} \\right)=i \\mathrm{sin} x}} \\\\ {{ \\mathrm{cosh}(x+iy)}}&{{= \\mathrm{cosh}(x) \\mathrm{cos}(y)+i \\mathrm{sinh}(x) \\mathrm{sin}(y)}} \\\\ {{ \\mathrm{sinh}(x+iy)}}&{{= \\mathrm{sinh}(x) \\mathrm{cos}(y)+i \\mathrm{cosh}(x) \\mathrm{sin}(y)}} \\\\ {{ \\mathrm{tanh}(ix)}}&{{=i \\mathrm{tan} x}} \\\\ {{ \\mathrm{cosh} x}}&{{= \\mathrm{cos}(ix)}} \\\\ {{ \\mathrm{sinh} x}}&{{=-i \\mathrm{sin}(ix)}} \\\\ {{ \\mathrm{tanh} x}}&{{=-i \\mathrm{tan}(ix)}} \\end{array}}", "UniMER-1M_0001509": "\\deltak", "UniMER-1M_0001476": "\\mathrm{I}_{i} \\approx \\frac{ \\rho_{i}}{6 \\sqrt{ \\sigma_{1}^{2}+ \\rho_{i}^{2}}}e^{- \\frac{1}{2} \\frac{ \\mu_{1}^{2}}{ \\sigma_{1}^{2}+ \\rho_{i}^{2}}}+ \\frac{ \\rho_{i}}{2 \\sqrt{ \\frac{4}{3} \\sigma_{1}^{2}+ \\rho_{i}^{2}}}e^{- \\frac{2}{3} \\frac{ \\mu_{1}^{2}}{ \\frac{4}{3} \\sigma_{1}^{2}+ \\rho_{i}^{2}}}, \\;i=1,2.", "UniMER-1M_0001496": "\\delta{{I}_{{ \\lambda_{i}}}^{ \\prime}} \\mathrm{~=~} \\sigma \\mathrm{~+~} \\varphi", "UniMER-1M_0001521": "\\mathcal{B}", "UniMER-1M_0001499": "V_{z}/v_{A} \\sim \\delta \\rho/ \\rho_{0} \\sim \\deltap/p_{0} \\sim \\epsilon", "UniMER-1M_0001471": "\\beta_{1,3}=k_{0} \\mp \\sqrt{B^{2}-A^{2}},", "UniMER-1M_0001466": "\\tau_{R} \\propto \\frac{ \\sigma^{3}N_{ \\mathrm{tot}}^{4}}{Dl_{p}}", "UniMER-1M_0001525": "47", "UniMER-1M_0001467": "\\begin{array}{rl}&{{ \\mathbb{E} \\mathrm{ln} \\Bigl( \\mathbb{E}_{u,x, \\delta} \\Bigl[ \\prod_{k:| \\xi_{n+1,k}| \\geq \\varepsilon} \\bigl(1+ \\mathrm{tanh}( \\beta \\xi_{n+1,k})s_{n+1,k} \\delta_{n+1} \\bigr) \\frac{ \\prod_{i \\len} \\prod_{k \\ge1}(1+ \\mathrm{tanh}( \\beta \\xi_{i,k})s_{i,k} \\delta_{i})}{ \\mathbbE_{u,x, \\delta} \\prod_{i \\len} \\prod_{k \\ge1}(1+ \\mathrm{tanh}( \\beta \\xi_{i,k})s_{i,k} \\delta_{i})} \\Bigr] \\Bigr)}} \\\\ &{{= \\mathbb{E} \\mathrm{ln} \\Bigl( \\mathbb{E}_{u,x, \\delta} \\Bigl[ \\prod_{k:| \\xi_{n+1,k}| \\geq \\varepsilon} \\bigl(1+ \\mathrm{tanh}( \\beta \\xi_{n+1,k})s_{n+1,k} \\delta_{n+1} \\bigr) \\Bigr] \\Bigr).}} \\end{array}", "UniMER-1M_0001473": "\\forallx \\, \\forally \\,[x=y \\rightarrow \\forallF(Fx \\leftrightarrowFy)]", "UniMER-1M_0001508": "1000 \\times70", "UniMER-1M_0001491": "y \\to+ \\infty", "UniMER-1M_0001474": "\\begin{array}{r}{{e^{d(t-s)} \\nabla_{v}f(s, \\mathrm{Z}^{s;t}(x,v))= \\nabla_{v}f(t,x,v)+ \\int_{s}^{t}e^{d(t- \\tau)} \\left( \\nabla_{x}f( \\tau, \\mathrm{Z}^{ \\tau;t}(x,v))- \\nabla_{v}f( \\tau, \\mathrm{Z}^{ \\tau;t}(x,v)) \\right) \\, \\mathrm{d} \\tau.}} \\end{array}", "UniMER-1M_0001456": "\\kappa= \\operatorname{lim}_{T \\to \\infty} \\frac{N_{T}}{T},", "UniMER-1M_0001497": "0.99423 \\pm0.00004", "UniMER-1M_0001504": "\\int_{0}^{T} \\frac{1-e^{- \\gammat}}{ \\mathrm{max}(1,t^{ \\frac{3}{2}})} \\,dt \\leq \\gammaT \\int_{0}^{T} \\frac{1}{ \\mathrm{max}(1,t^{ \\frac{3}{2}})} \\,dt \\leq \\gammaT \\int_{0}^{ \\infty} \\frac{1}{ \\mathrm{max}(1,t^{ \\frac{3}{2}})} \\,dt=3 \\gammaT=3 \\gamma^{ \\frac{1}{3}}.", "UniMER-1M_0001513": "D_{w}(L)=D_{0} \\,L^{2-d_{w}}.", "UniMER-1M_0001495": "x_{off}=1680 \\, \\mu", "UniMER-1M_0001518": "\\mathcal{L}_{{ \\tilde{B}}}{ \\tilde{ \\mu}}= \\mathcal{L}_{B} \\mu=0", "UniMER-1M_0001515": "\\left. \\par tial_{0} \\phi(x_{0},{ \\bf x}) \\right|_{x_{0}= \\epsilon} \\to \\epsilon^{- \\lambda} \\par tial_{ \\epsilon} \\phi_{h}( \\epsilon,{ \\bf x}).", "UniMER-1M_0001529": "f(x, \\mu_{0}^{2})=Nx^{ \\alpha}(1-x)^{ \\beta}(1+ \\gamma_{1} \\sqrt{x}+ \\gamma_{2}x) \\; \\;", "UniMER-1M_0001526": "\\begin{array}{rl}&{{2 \\nu \\left[g_{r_{x}}+ig_{i_{x}} \\right]+2 \\nuU_{y}^{2}t^{2} \\left[-f_{i_{x}}+f_{r_{x}} \\right]-2 \\nuU_{y}t \\left[-g_{i_{x}}+ig_{r_{x}}+f_{r_{x}}+if_{i_{x}} \\right]-e^{- \\alphaUt}}} \\\\ &{{-R_{2} \\left[ \\nuU_{y}y \\! \\!- \\! \\!U \\! \\!+ \\! \\!(1 \\! \\!- \\! \\! \\nu)A_{0_{y}}^{21} \\! \\!+ \\! \\!i \\alpha \\left[2 \\nuU_{y}+(1 \\! \\!- \\! \\! \\nu)(A_{0}^{12} \\! \\!+ \\! \\!A_{0}^{21}) \\right] \\! \\!+ \\! \\!B \\alpha^{2} \\! \\!- \\! \\!2t(1 \\! \\!- \\! \\! \\nu)i \\alphaU_{y}A_{0}^{22} \\right]}} \\\\ &{{+(1- \\nu)R_{3}+t(1- \\nu)U_{y}(R_{4}+R_{5})+t^{2}(U_{y})^{2}(1- \\nu)R_{6}=0,}} \\end{array}", "UniMER-1M_0001483": "F[n]=T[n]+U[n]", "UniMER-1M_0001523": "_6", "UniMER-1M_0001530": "1^{o}", "UniMER-1M_0001543": "l", "UniMER-1M_0001537": "\\ensuremath{N}", "UniMER-1M_0001535": "\\beta_{ \\mathrm{i}}=100", "UniMER-1M_0001503": "f(a)={ \\frac{1}{2 \\pii}} \\oint_{C}{ \\frac{f(z)}{z-a}}dz,", "UniMER-1M_0001532": "A_{-1}^{ \\mathrm{tr}}(s)={ \\frac{1}{2 \\sqrt{ \\pi}}}{ \\frac{ \\Gamma(s-{ \\frac{1}{2}})}{ \\Gamma(s+1)}} \\Bigr[r_{+}^{2s}-r_{-}^{2s} \\Bigr] \\Bigr[ \\zeta_{R}(2s-3)- \\zeta_{R}(2s-1) \\Bigr],", "UniMER-1M_0001519": "U_{0},p_{0}", "UniMER-1M_0001538": ",keys", "UniMER-1M_0001540": "C_{k}", "UniMER-1M_0001505": "\\tilde{ \\textbf{A}}= \\tilde{ \\textbf{Y}} \\tilde{ \\textbf{X}}^{ \\dagger}.", "UniMER-1M_0001502": "{{ \\left|{{{ \\vec{u}}}^{ \\mathrm{~t~h~}}}_{sk} \\right|}^{2}}={{ \\left|{{{ \\vec{u}}}^{ \\mathrm{~t~h~}}}_{ck} \\right|}^{2}}", "UniMER-1M_0001534": "\\tau=1", "UniMER-1M_0001531": "R", "UniMER-1M_0001494": "\\mathrm{g}^{u}=-3.05432 \\times10^{-3}", "UniMER-1M_0001536": "f^{ \\prime \\prime}(x)>0", "UniMER-1M_0001541": "\\frac{d}{dt} \\|u \\|_{ \\Omega}^{2}=2 \\big(u,u_{t} \\big)_{ \\Omega}+ \\big(u, \\big(n^{T} \\dot{x} \\big)u \\big)_{ \\par tial \\Omega},", "UniMER-1M_0001520": "s_{ \\textup{cr}} \\sim \\delta^{-1/2}", "UniMER-1M_0001498": "d_{ \\mathrm{~s~t~o~p~}}", "UniMER-1M_0001560": "\\vec{r}", "UniMER-1M_0001561": "u_{x}= \\frac{ \\par tial \\phi}{ \\par tialx}+ \\frac{ \\par tialH}{ \\par tialy}", "UniMER-1M_0001562": "l_{c}=V \\tau_{c}", "UniMER-1M_0001512": "T^{ \\varphi}= \\varphi_{ww}-{ \\frac{1}{2}} \\varphi_{w}^{2}, \\qquad R= \\varphi_{w \\bar{w}}.", "UniMER-1M_0001516": "\\left\\{ \\begin{array}{ll}{{- \\varepsilon^{2}N_{ \\varepsilon}^{ \\prime \\prime}(z)-R(z)N_{ \\varepsilon}(z)=- \\lambda_{ \\varepsilon}( \\mathbb{R})N_{ \\varepsilon}(z), \\quad z \\in \\Omega}} \\\\ {{N_{ \\varepsilon}(z)>0, \\quad z \\in \\Omega, \\quad \\|N_{ \\varepsilon} \\|_{L^{2}( \\mathbb{R})}=1.}} \\end{array} \\right.", "UniMER-1M_0001484": "P(t,T)=e^{- \\int_{t}^{T}f(t,s)ds}", "UniMER-1M_0001517": "k", "UniMER-1M_0001563": "\\mathrm{He/CH_{4}}", "UniMER-1M_0001527": "X_{ \\mathrm{L}}= \\omegaL", "UniMER-1M_0001558": "5000", "UniMER-1M_0001555": "*", "UniMER-1M_0001542": "(g,z) \\sim(gt, \\lambda(t^{-1})z) \\forallt \\inT.", "UniMER-1M_0001546": "0.2", "UniMER-1M_0001551": "\\sigma_{ \\eta}", "UniMER-1M_0001557": "\\sim10 \\", "UniMER-1M_0001549": "g_{ \\mu \\nu}^{ind}(x)=e^{- \\frac{ \\phi(x)}{ \\langle \\phi \\rangle}}~ \\eta_{ \\mu \\nu}", "UniMER-1M_0001545": "\\pi_{i}^{2}=w^{2}( \\mu_{i}), \\quad i=1, . . .,n+1,", "UniMER-1M_0001522": "R^{2}-R_{0}R+R_{0}l=0.", "UniMER-1M_0001524": "f(y,x)= \\pi(y,x)f(x),", "UniMER-1M_0001566": "8 \\times8", "UniMER-1M_0001533": "\\texttt{generation \\_threshold}=0.6", "UniMER-1M_0001552": "\\beta_{k}= \\frac{ \\epsilon_{k} \\rho_{k}}{ \\tau_{st,k}}", "UniMER-1M_0001550": "\\omega", "UniMER-1M_0001528": "\\begin{array}{rl}{{( \\par tial_{t}+ \\mathbf{U} \\cdot \\nabla)^{2} \\par tial_{z}w^{(2)}- \\mathbf{U}^{ \\prime} \\cdot( \\par tial_{t}+ \\mathbf{U} \\cdot \\nabla) \\nablaw^{(2)}- \\nabla^{2}w^{(2)}=~}}&{{ \\mathcal{F}^{(2)}( \\mathbf{x},z,t)~ \\mathrm{for}~z=0,}} \\\\ {{w^{(2)}=~}}&{{0~ \\mathrm{for}~z \\to- \\infty,}} \\end{array}", "UniMER-1M_0001544": "\\hat{x}_{ij}= \\lambdax_{i}+(1- \\lambda)x_{j}", "UniMER-1M_0001564": "\\operatorname{E} \\left[g(X_{1}, . . .,X_{d}) \\right]", "UniMER-1M_0001553": "\\frac{G\"}{G^{ \\prime}}( \\omega)= \\mathrm{tan}{( \\delta)}= \\frac{ \\mathrm{sin} \\left(m \\pi/2 \\right)( \\omega \\tau)^{m} \\mathrm{tan} \\left(m \\pi/2 \\right)}{ \\mathrm{sin}{ \\left(m \\pi/2 \\right)}( \\omega \\tau)^{m}+ \\mathrm{tan}{ \\left(m \\pi/2 \\right)}}.", "UniMER-1M_0001548": "\\tau_{ \\alpha}=t_{ \\alpha}+ \\sum_{ \\beta \\neq \\alpha}K_{ \\alpha \\beta} \\tau_{ \\beta}.", "UniMER-1M_0001567": "\\begin{array}{rl}{{ \\frac{c_{abcd}^{2}}{gD}}}&{{= \\frac{(1-a(Dk)^{2})(1-c(Dk)^{2})}{(1+b(Dk)^{2})(1+d(Dk)^{2})}}} \\\\ &{{=1-(a+b+c+d)(Dk)^{2}+ \\left((a+b)(b+c)+(a+b+c)d+d^{2} \\right)(Dk)^{4}+O \\left((Dk) \\right)^{6} \\ .}} \\end{array}", "UniMER-1M_0001573": "c", "UniMER-1M_0001581": "v_{y}", "UniMER-1M_0001588": "\\theta", "UniMER-1M_0001556": "{ \\tilde{ \\phi}}(p)", "UniMER-1M_0001595": "^{4}", "UniMER-1M_0001547": "\\vec{k_{2}}= \\vec{k_{0}}+ \\vec{k_{1}}", "UniMER-1M_0001597": "\\nablaS", "UniMER-1M_0001598": "^8", "UniMER-1M_0001565": "\\kappa_{2}=0.05~ \\mathrm{{Wm^{-1} K^{-1}}}", "UniMER-1M_0001600": "0.5", "UniMER-1M_0001554": "\\begin{array}{rl}{{ \\left( \\omega_{0} \\pm \\omega_{z} \\right)b_{ \\pm} \\hat{ \\epsilon}_{A \\pm} \\delta \\hat{ \\phi}_{ \\pm}}}&{{= \\frac{ \\mathrm{~i~} \\omega_{ci} \\rho_{i}^{2}}{2} \\Lambda_{k_{0}}^{k_{z}} \\left[c_{ \\phi}^{ \\pm} \\left( \\delta \\hat{ \\phi}_{z}- \\delta \\hat{ \\psi}_{z} \\right)+d_{ \\phi}^{ \\pm} \\delta \\hat{ \\psi}_{z} \\right] \\left( \\begin{array}{c}{{ \\delta \\hat{ \\phi}_{0}}} \\\\ {{ \\delta \\hat{ \\phi}_{0}^{*}}} \\end{array} \\right),}} \\\\ {{ \\left( \\omega_{0} \\pm \\omega_{z} \\right)b_{ \\pm} \\hat{ \\epsilon}_{A \\pm} \\delta \\hat{ \\psi}_{ \\pm}}}&{{= \\frac{ \\mathrm{~i~} \\omega_{ci} \\rho_{i}^{2}}{2} \\Lambda_{k_{0}}^{k_{z}} \\left[c_{ \\psi}^{ \\pm} \\left( \\delta \\hat{ \\phi}_{z}- \\delta \\hat{ \\psi}_{z} \\right)+d_{ \\psi}^{ \\pm} \\delta \\hat{ \\psi}_{z} \\right] \\left( \\begin{array}{c}{{ \\delta \\hat{ \\phi}_{0}}} \\\\ {{ \\delta \\hat{ \\phi}_{0}^{*}}} \\end{array} \\right),}} \\end{array}", "UniMER-1M_0001577": "0.8", "UniMER-1M_0001574": "x(t=t_{ \\tiny{ \\textrm{fix}}})", "UniMER-1M_0001571": "G_{i}^{ \\chi}(x,y)=(1- \\delta_{i2})G_{i}(x_{0},y_{0})+L_{i}^{ \\chi}(x,y)+H_{i}(x,y)+O(p^{2(n+1)}).", "UniMER-1M_0001578": "\\frac{1}{2} \\psi_{R}^{i} \\left(e^{i \\sqrt{2}X_{R}^{i}}+e^{-i \\sqrt{2}X_{R}^{i}} \\right)", "UniMER-1M_0001575": "\\begin{array}{r}{{ \\mathbb{P} \\left\\{ \\mathcal{E} \\right\\} \\triangleq \\sum_{x^{k} \\in \\mathcal{X}^{k}}P_{X^{k}} \\left(x^{k} \\right) \\sum_{s^{k} \\in \\mathcal{S}^{k}}P_{S^{k}|X^{k}} \\left(s^{k}|x^{k} \\right) \\sum_{z^{n} \\in \\mathcal{E}(s^{k},x^{k})}P_{Z^{n}|Y^{n}} \\left(z^{n}| \\varphi^{n} \\left(x^{k} \\right) \\right),}} \\end{array}", "UniMER-1M_0001579": "\\begin{array}{r}{{H_{0j}= \\frac{1}{ \\gamma} \\frac{3a}{2l}.}} \\end{array}", "UniMER-1M_0001585": "u", "UniMER-1M_0001539": "\\begin{array}{r}{{ \\begin{array}{rl}{{[b] \\frac{d}{dt}}}&{{ \\int_{ \\mathcal{V}} \\Phi( \\mathbf{v})g( \\mathbf{v},t) \\,d \\mathbf{v}}} \\\\ &{{= \\frac{1}{2} \\int_{ \\mathcal{V}^{2}}b( \\mathbf{v}, \\mathbf{v}_{ \\ast}) \\left\\langle \\Phi( \\mathbf{v}^{ \\prime})+ \\Phi( \\mathbf{v}_{ \\ast}^{ \\prime})- \\Phi( \\mathbf{v})- \\Phi( \\mathbf{v}_{ \\ast}) \\right\\rangleg( \\mathbf{v},t)g( \\mathbf{v}_{ \\ast},t) \\,d \\mathbf{v} \\,d \\mathbf{v}_{ \\ast}}} \\end{array}}} \\end{array}", "UniMER-1M_0001572": "V_{f}(t+a)=A \\cdotV_{f}(t)+B \\cdotZ", "UniMER-1M_0001559": "10", "UniMER-1M_0001583": "g_{l}(T,v)=g_{l}^{o}(T_{m},P_{0})+ \\left( \\frac{ \\par tialg_{l}}{ \\par tialT} \\right)_{P_{l}}(T-T_{m})+ \\left( \\frac{ \\par tialg_{l}}{ \\par tialP_{l}} \\right)_{T}(P_{l}-P_{0})", "UniMER-1M_0001580": "\\begin{array}{rl}{{e_{ \\mathrm{~x~}}^{ \\mathrm{~C~I~D~E~R~}}( \\mathbf{x}_{*})}}&{{=e_{ \\mathrm{~x~}}^{ \\mathrm{~L~D~A~}}(n_{*}) \\sum_{a}k_{F_{ \\mathrm{~x~}}}( \\mathbf{x}_{*}, \\mathbf{ \\tilde{x}}_{a}) \\alpha_{a}}} \\\\ {{ \\boldsymbol{ \\alpha}}}&{{= \\sum_{i} \\mathbf{ \\tilde{k}}^{i} \\left\\{ \\left[ \\mathbf{K}+ \\Sigma_{ \\mathrm{~n~o~i~s~e~}} \\right]^{-1} \\mathbf{y} \\right\\}_{i},}} \\end{array}", "UniMER-1M_0001582": "|{ \\boldsymbol{ \\Omega}|=v/a", "UniMER-1M_0001569": "a \\ll1", "UniMER-1M_0001616": "-0.12", "UniMER-1M_0001589": "- \\infty", "UniMER-1M_0001603": "n", "UniMER-1M_0001599": "^{+}", "UniMER-1M_0001618": "i", "UniMER-1M_0001621": "V", "UniMER-1M_0001594": "\\pi", "UniMER-1M_0001608": "Q", "UniMER-1M_0001610": "30", "UniMER-1M_0001611": "F^{ \\,dipole}( \\omega)= \\left(1+ \\frac{2M_{1}M_{2}( \\omega-1)}{m_{FF}^{2}-(M_{1}-M_{2})^{2}} \\right)^{-2}", "UniMER-1M_0001568": "D_{ \\alpha} \\ = \\ i \\, \\gamma^{ \\mu} \\  \\frac{ \\mathrm{sin} \\left( \\frac \\piN \\;p_{ \\mu} \\right)}{ \\frac \\piN} \\ .", "UniMER-1M_0001601": "\\mathrm{ln} \\left(1-{ \\frac{V(t)}{V_{0}}} \\right)=-{ \\frac{t}{ \\tau}} \\quad \\Longleftrightarrow \\quad t=- \\tau \\; \\mathrm{ln} \\left(1-{ \\frac{V(t)}{V_{0}}} \\right)", "UniMER-1M_0001586": "\\begin{array}{rl}{{ \\mathscrD_{x} \\mathscrD_{x}^{ \\prime}+ \\mathscrD_{y} \\mathscrD_{y}^{ \\prime}}}&{{= \\frac{(t_{x}-1)^{2}(t_{x}+1)^{2}}{4t_{x}^{2} \\Deltax^{2}} \\frac{(t_{y}+1)^{4}}{16t_{y}^{2}}+ \\frac{(t_{y}-1)^{2}(t_{y}+1)^{2}}{4t_{y}^{2} \\Deltax^{2}} \\frac{(t_{x}+1)^{4}}{16t_{x}^{2}}}} \\\\ &{{= \\frac{( \\mathrm{cos}^{2} \\beta_{x}-1)( \\mathrm{cos} \\beta_{y}+1)^{2}+( \\mathrm{cos}^{2} \\beta_{y}-1)( \\mathrm{cos} \\beta_{x}+1)^{2}}{4 \\Deltax^{2}}}} \\\\ &{{=( \\mathrm{cos} \\beta_{x}+1)( \\mathrm{cos} \\beta_{y}+1) \\frac{ \\mathrm{cos} \\beta_{x} \\mathrm{cos} \\beta_{y}-1}{2 \\Deltax^{2}}}} \\end{array}", "UniMER-1M_0001617": "\\sum_{x}f(x)= \\sum_{x}g(x)=1", "UniMER-1M_0001604": "\\mu_{ \\pm}", "UniMER-1M_0001593": "\\mathrm{cos}( \\phi_{j}(t))", "UniMER-1M_0001590": "\\nu_{ \\{q,{ \\bf 1},{ \\bf 2} \\}}={ \\frac{1}{72}}(q+1)(q+2)(q+3)^{2}(q+4)(q+5).", "UniMER-1M_0001619": "\\bar{ \\rho}=10", "UniMER-1M_0001635": "\\sigma= \\pm1", "UniMER-1M_0001620": "S>0", "UniMER-1M_0001612": "p \\geq0", "UniMER-1M_0001605": "R(m,z) \\approx \\sum_{i=0}^{n} \\frac{c_{n}}{2}E_{1+i}(m^{2})(z/5)^{2*i+1} \\quad.", "UniMER-1M_0001626": "\\delta_{ep}= \\sum_{i=1}^{N_{e}/2} \\gamma_{i} \\int \\lvert \\varphi_{i}( \\mathbf{r}) \\rvert^{2} \\lvert \\psi_{ \\varepsilon}( \\mathbf{r}) \\rvert^{2}d^{3} \\mathbf{r}", "UniMER-1M_0001640": "\\rho^{ \\mathrm{~c~a~v~}}", "UniMER-1M_0001602": "\\rho_{l}^{T}( \\omega,k)= \\frac{ \\Omega}{{ \\calF}} \\intd^{3}xdte^{-i{ \\bf k}{ \\bf x}+i \\omegat} \\left[ \\bar{ \\Pi}(r,t-i \\epsilon)- \\bar{ \\Pi}(r,t+i \\epsilon) \\right] \\,.", "UniMER-1M_0001630": "\\par tialV", "UniMER-1M_0001614": "(X_{ \\mathrm{ \\boldmath~p~}}+Y_{ \\mathrm{ \\boldmath~p~}})f:=X_{ \\mathrm{ \\boldmath~p~}}f+Y_{ \\mathrm{ \\boldmath~p~}}f, \\quad( \\lambda \\cdotX_{ \\mathrm{ \\boldmath~p~}})f:= \\lambda \\cdot(X_{ \\mathrm{ \\boldmath~p~}}f), \\quad \\forall~ \\lambda \\in \\mathbb{R},~f \\inC^{ \\infty}( \\mathrm{ \\boldmath~p~}).", "UniMER-1M_0001628": "B^{ \\prime}", "UniMER-1M_0001645": "\\Delta \\tau", "UniMER-1M_0001633": "\\tau_{S}", "UniMER-1M_0001648": "\\mathbf{Q}", "UniMER-1M_0001570": "\\mathcal{LR}= \\frac{ \\mathcal{L}_{xe}}{ \\mathcal{L}_{xe}+ \\mathcal{L}_{acc}},", "UniMER-1M_0001607": "\\frac{1}{ \\Deltat} \\mathrm{ln} \\frac{|{ \\boldsymbol \\xi}(t_{N})|}{|{ \\boldsymbol \\xi}_{0}|}", "UniMER-1M_0001631": "5 \\times10^{16}", "UniMER-1M_0001652": "C^{ \\infty}", "UniMER-1M_0001591": "r=m,m+1, . . .,n-1", "UniMER-1M_0001654": "^{c}", "UniMER-1M_0001622": "\\hat{ \\Omega} \\left[ \\hat{ \\tau} \\right]= \\Omega \\left[ \\hat{ \\tau} \\tau_{1} \\right].", "UniMER-1M_0001584": "U^{ \\prime}=SUS^{-1}= \\left[ \\begin{array}{llll}{{ \\sqrt{ \\beta_{1x}}}}&{{0}}&{{- \\sqrt{ \\beta_{2x}} \\mathrm{cos} v_{2}}}&{{ \\sqrt{ \\beta_{2x}} \\mathrm{sin} v_{2}}} \\\\ {{- \\frac{ \\alpha_{1x}}{ \\sqrt{ \\beta_{1x}}}}}&{{ \\frac{1-u}{ \\beta_{1x}}}}&{{ \\frac{ \\alpha_{2x} \\mathrm{cos} v_{2}-u \\mathrm{sin} v_{2}}{ \\sqrt{ \\beta_{2x}}}}}&{{- \\frac{ \\alpha_{2x} \\mathrm{sin} v_{2}+u \\mathrm{cos} v_{2}}{ \\sqrt{ \\beta_{2x}}}}} \\\\ {{- \\sqrt{ \\beta_{1y}} \\mathrm{cos} v_{1}}}&{{ \\sqrt{ \\beta_{1y}} \\mathrm{sin} v_{1}}}&{{ \\sqrt{ \\beta_{2y}}}}&{{0}} \\\\ {{ \\frac{ \\alpha_{1y} \\mathrm{cos} v_{1}-u \\mathrm{sin} v_{1}}{ \\sqrt{ \\beta_{1y}}}}}&{{- \\frac{ \\alpha_{1y} \\mathrm{sin} v_{1}+u \\mathrm{cos} v_{1}}{ \\sqrt{ \\beta_{1y}}}}}&{{- \\frac{ \\alpha_{2y}}{ \\sqrt{ \\beta_{2y}}}}}&{{ \\frac{1-u}{ \\sqrt{ \\beta_{2y}}}}} \\end{array} \\right]", "UniMER-1M_0001624": "\\mathrm{log}[F(x_{1})]=m \\mathrm{log}(x_{1})+b,", "UniMER-1M_0001658": "I \\equivI_{ext}+I_{self}", "UniMER-1M_0001596": ",obtainedfrom(),with", "UniMER-1M_0001632": "f_{0}", "UniMER-1M_0001641": "\\alpha", "UniMER-1M_0001642": "\\delta \\boldsymbol{a}", "UniMER-1M_0001639": "0.551", "UniMER-1M_0001643": "L=3.48 \\, \\mathrm{~ \\ normalfont~ \\ AA~}", "UniMER-1M_0001667": "\\omega_{ \\pm}", "UniMER-1M_0001627": "10", "UniMER-1M_0001669": "n", "UniMER-1M_0001613": "k/ \\omega(k)", "UniMER-1M_0001670": "D=2d", "UniMER-1M_0001587": "\\varepsilon(z)", "UniMER-1M_0001606": "r(j)= \\frac{ \\mathrm{tanh} \\left[rx_{2} \\left( \\frac{j-1}{nx_{2}}- \\frac{1}{2} \\right) \\right]}{2 \\mathrm{tanh} \\left( \\frac{rx_{2}}{2} \\right)},", "UniMER-1M_0001644": "15.5 \\times10^{-16}", "UniMER-1M_0001609": "g( \\ensuremath{ \\mathbf{r}}, \\ensuremath{ \\mathbf{r}}^{ \\prime})= \\frac{n_{2}( \\ensuremath{ \\mathbf{r}}, \\ensuremath{ \\mathbf{r}}^{ \\prime})}{n( \\ensuremath{ \\mathbf{r}})n( \\ensuremath{ \\mathbf{r}}^{ \\prime})}.", "UniMER-1M_0001657": "s_{2}", "UniMER-1M_0001653": "\\overline{{{A|}}}", "UniMER-1M_0001659": "\\left[ \\begin{array}{l}{{ \\underline{{{E}}}^{ \\mathrm{H}}(x, \\widehat{x},w, \\widehat{w})}} \\\\ {{ \\overline{{{E}}}^{ \\mathrm{H}}(x, \\widehat{x},w, \\widehat{w})}} \\end{array} \\right] \\le_{ \\mathrm{SE}} \\left[ \\begin{array}{l}{{ \\underline{{{E}}}^{ \\mathrm{Lin}}(x, \\widehat{x},w, \\widehat{w})}} \\\\ {{ \\overline{{{E}}}^{ \\mathrm{Lin}}(x, \\widehat{x},w, \\widehat{w})}} \\end{array} \\right]", "UniMER-1M_0001637": "{ \\calB}( \\bar{B}^{0} \\to \\rho^{+} \\ell \\nu)=(2.5 \\pm0.8) \\times10^{-4} \\;,", "UniMER-1M_0001625": "\\dot{n}(t)_{coll}=- \\frac{ \\lambda}{6 \\omega_{0}} \\frac{ \\par tial}{ \\par tialt^{ \\prime}} \\langle( \\phi(t))^{3} \\phi(t^{ \\prime}) \\rangle|_{t^{ \\prime}=t}", "UniMER-1M_0001636": "\\begin{array}{rl}{{ \\left[ \\begin{array}{c}{{{ \\hat{A}}( \\mathbf{k},t_{n+1})}} \\\\ {{{ \\hat{B}}_{s}( \\mathbf{k},t_{n+1})}} \\end{array} \\right]=}}&{{ \\pmb{ \\mathcal{E}}((t_{n+1}-t_{0}) \\Omega) \\left[ \\begin{array}{c}{{{ \\hat{A}}( \\mathbf{k},t_{0})}} \\\\ {{{ \\hat{B}}_{s}( \\mathbf{k},t_{0})}} \\end{array} \\right]+}} \\\\ &{{ \\sum_{m=1}^{m=M} \\sum_{j=0}^{j=m} \\mathbf{I}_{n}(t_{n+1}) \\left[ \\begin{array}{c}{{ \\hat{ \\mathcal{N}}_{A}^{(mj)}( \\mathbf{k}+(j-1) \\alpha \\mathbf{k}_{0},t_{n})}} \\\\ {{ \\hat{ \\mathcal{N}}_{B}^{(mj)}( \\mathbf{k}+(j-1) \\alpha \\mathbf{k}_{0},t_{n})}} \\end{array} \\right]+ \\mathcal{O}( \\epsilon^{2} \\varepsilon_{t} \\omega_{0} \\Deltat),}} \\end{array}", "UniMER-1M_0001634": "\\mathrm{ \\bf T}_{ \\omega} \\varphi(p)= \\sum_{| \\alpha| \\le \\omega}{ \\calP}_{ \\alpha}(p) \\, \\left[ \\intdp^{ \\prime} \\, \\delta_{ \\alpha}(p^{ \\prime}) \\varphi(p^{ \\prime}) \\right].", "UniMER-1M_0001682": "u", "UniMER-1M_0001638": "\\begin{array}{r}{{db_{ij}= \\bigg( \\frac{M_{ij}}{A^{2}}-b_{ij}b_{kl} \\frac{M_{kl}}{A^{2}}- \\frac{1}{2}b_{ij} \\frac{K_{pqkl}}{A^{3/2}} \\frac{K_{pqkl}}{A^{3/2}}-b_{pq} \\frac{K_{pqkl}}{A^{3/2}} \\frac{K_{ijkl}}{A^{3/2}}}} \\\\ {{+ \\frac{3}{2}b_{ij}b_{pq} \\frac{K_{pqkl}}{A^{3/2}}b_{mn} \\frac{K_{mnkl}}{A^{3/2}} \\bigg)dt^{ \\prime}+ \\bigg( \\frac{K_{ijkl}}{A^{3/2}}-b_{ij}b_{pq} \\frac{K_{pqkl}}{A^{3/2}} \\bigg) \\;dW_{kl}^{ \\prime}}} \\end{array}", "UniMER-1M_0001615": "z=- \\frac{2 \\gamma{ \\widehat{L}}^{ \\alpha- \\beta+1}}{D( \\alpha- \\beta+1)}", "UniMER-1M_0001666": "I( \\mathrm{~A~}, \\mathrm{~E~}) \\leqh_{2} \\left( \\frac{1+ \\left\\langle \\mathrm{exp} \\left(-2r_{ \\mathrm{~E~}}| \\alpha|^{2} \\right) \\right\\rangle_{Q_{ \\checkmark}}}{2} \\right).", "UniMER-1M_0001668": "\\begin{array}{rl}&{{2 \\left\\lceil \\frac{N_{k}(M+N/2)}{k_{r}} \\right\\rceil+2N \\beth(k_{r}-1)+2 \\left\\lceil \\frac{N_{k}M}{k_{r}} \\right\\rceil+2N \\beth(k_{r}-1)+2 \\left\\lceil \\frac{N_{k}(M+N/2)}{k_{r}^{ \\prime}} \\right\\rceil+2k_{r}^{ \\prime}}} \\\\ &{{+2 \\left\\lceil \\frac{N_{k}M)}{k_{r}^{ \\prime}} \\right\\rceil+2k_{r}^{ \\prime}+16N( \\beth-2)+12N_{k}+4 \\lceil \\mathrm{log} N_{k}(M+N/2) \\rceil+4 \\lceil \\mathrm{log} N_{k}M \\rceil,}} \\end{array}", "UniMER-1M_0001649": "^{( \\psi)}T_{ \\mu \\nu}= \\frac{i}{2} \\left[ \\bar{ \\psi} \\Gamma_{( \\mu}D_{ \\nu)} \\psi-D_{( \\nu} \\bar{ \\psi} \\Gamma_{ \\mu)} \\psi \\right]-g_{ \\mu \\nu}L_{ \\psi} \\,.", "UniMER-1M_0001690": "\\phi=n \\pi", "UniMER-1M_0001691": "200", "UniMER-1M_0001629": "{ \\psi}_{ \\alpha}(x)= \\frac{Z_{ \\alpha}}{ \\sqrt{({ \\gamma}^{0}{ \\gamma}^{-})_{{ \\alpha}{ \\alpha}}}} \\mathrm{exp}[-i \\sqrt{ \\pi}{ \\Lambda}_{ \\alpha}(x)],", "UniMER-1M_0001651": "q>0", "UniMER-1M_0001671": "{ \\bf v}=2 \\sigma{ \\bf \\nabla}S+ \\frac{e}{mc}{ \\bf A}.", "UniMER-1M_0001646": "\\begin{array}{rl}{{F_{y}(t)}}&{{= \\sum_{i} \\Biggl\\{ \\Biggl(- \\frac{c_{i}^{2}Q_{y}(0)}{4 \\omega_{i}( \\dot{ \\gamma}- \\omega_{i})}+ \\frac{c_{i}}{4}q_{iy}(0)- \\frac{c_{i}}{4 \\omega_{i}}p_{ix} \\Biggr) \\Bigl(e^{ \\lambda_{2}t^{ \\prime}}+e^{- \\lambda_{2}t^{ \\prime}} \\Bigr)}} \\end{array}", "UniMER-1M_0001664": "\\| \\mathcal{V}(t, \\cdot) \\|_{L^{ \\infty}} \\leq \\| \\mathcal{V}_{0} \\|_{L^{ \\infty}}G(0,t).", "UniMER-1M_0001673": "\\nabla \\Bar{ \\psi}", "UniMER-1M_0001650": "\\mathbf{R}* \\mathbf{x}^{(l)}= \\mathbf{D}^{(l)}( \\mathbf{R}) \\cdot \\mathbf{x}^{(l)}", "UniMER-1M_0001675": "m_{ \\chi}>m_{A^{ \\prime}}", "UniMER-1M_0001656": "{ \\calF}= \\sum_{n_{1} \\geqn_{2} \\geq . . . \\geqn_{N} \\geq0}^{ \\infty} \\operatorname{det} \\left[d_{n_{j}+N-j,i} \\right] \\operatorname{det} \\left[t_{i}^{n_{j}+N-j+ \\frac{1}{2}}-t_{i}^{-(n_{j}+N-j+ \\frac{1}{2})} \\right].", "UniMER-1M_0001678": "\\xi=0", "UniMER-1M_0001677": "\\big\\|| \\mathbf{u}|^{ \\mathrm{p}-2} \\mathbf{u}-| \\mathbf{u}_{h}|^{ \\mathrm{p}-2} \\mathbf{u}_{h} \\big\\|_{0,T}^{2} \\, \\leq \\, \\widehat{c}_{ \\mathrm{p}} \\, \\Big( \\| \\mathbf{u} \\|_{0,3( \\mathrm{p}-2);T}^{2( \\mathrm{p}-2)}+ \\| \\mathbf{u}_{h} \\|_{0,3( \\mathrm{p}-2);T}^{2( \\mathrm{p}-2)} \\Big) \\| \\mathbf{u}- \\mathbf{u}_{h} \\|_{0,6;T}^{2} \\,,", "UniMER-1M_0001703": "\\operatorname{su}(2)", "UniMER-1M_0001662": "\\hat{ \\bf n} \\cdot \\frac{ \\par tialv}{ \\par tial \\hat{ \\bf n}}=v \\,,", "UniMER-1M_0001681": "\\rho_{i}", "UniMER-1M_0001661": "\\begin{array}{r}{{w_{t}^{(0)}- \\frac{ \\bar{ \\nu}}{ \\bar{r}} \\left( \\bar{r}w_{ \\bar{r}}^{(0)} \\right)_{ \\bar{r}}=-w_{ \\bar{r}}^{(0)} \\left<u^{(2)} \\right>- \\left<u^{(1)}w_{ \\bar{r}}^{(1)} \\right>- \\frac{1}{ \\bar{r}} \\left<v^{(1)}w_{ \\theta}^{(1)} \\right>}} \\\\ {{- \\frac{w^{(0)}}{ \\sigma^{(0)}} \\up silon-w^{(0)} \\kappa^{(0)} \\left<v^{(1)} \\mathrm{sin} \\varphi-u^{(1)} \\mathrm{cos} \\varphi^{(0)} \\right> \\quad \\quad \\quad \\quad \\quad}} \\\\ {{-v^{(0)} \\kappa^{(0)} \\left<w^{(1)} \\mathrm{sin} \\varphi^{(0)} \\right>- \\frac{ \\left<P_{s}^{(1)} \\right>}{ \\rho_{0} \\; \\sigma^{(0)}}+ \\alphag \\Tilde{T}^{(0)} \\hat{ \\mathbf{y}} \\cdot \\hat{ \\pmb{ \\tau}}}} \\end{array}", "UniMER-1M_0001680": "\\hat{ \\mathbf{X}}_{ \\mathrm{~O~}}= \\left[ \\begin{array}{l}{{ \\hat{X}}} \\\\ {{ \\hat{ \\Theta}}} \\end{array} \\right] \\,, \\qquad \\hat{ \\mathbf{X}}_{ \\mathrm{~I~}}= \\left[ \\begin{array}{l}{{ \\hat{x}}} \\\\ {{ \\hat{ \\theta}}} \\end{array} \\right] \\,.", "UniMER-1M_0001655": "\\approx35", "UniMER-1M_0001710": "\\tau", "UniMER-1M_0001684": "\\sigma_{x}^{2}= \\frac{1}{3}v_{c}^{2} \\tau_{ \\theta}^{2}t^{4}", "UniMER-1M_0001708": "x_{T2}^{R} \\leftarrow \\mathcal{R}_{ \\theta} \\left( \\bar{x}_{T2} \\right)", "UniMER-1M_0001688": "D_{a}:=- \\left\\langlea \\dot{a} \\right\\rangle= \\frac{1}{2} \\sum_{i,j}(a_{i}-a_{j})^{2}R_{ij}p_{j}^{ \\mathrm{ss}},", "UniMER-1M_0001663": "\\frac{1- \\mathrm{cos} \\phi_{1}}{ \\mathrm{sin} \\phi_{1}}= \\frac{ \\int_{ \\phi_{0}}^{ \\phi_{1}} \\frac{ \\mathrm{sin} \\phid \\phi}{ \\sqrt{ \\mathrm{sin} \\phi- \\mathrm{sin} \\phi_{0}}}}{ \\int_{ \\phi_{0}}^{ \\phi_{1}} \\frac{ \\mathrm{cos} \\phid \\phi}{ \\sqrt{ \\mathrm{sin} \\phi- \\mathrm{sin} \\phi_{0}}}},", "UniMER-1M_0001705": "6.5~ \\mathrm{{kHz}}", "UniMER-1M_0001679": "[ \\delta( \\epsilon_{1},t_{1}), \\delta( \\epsilon_{2},t_{2})]g={ \\frac{t_{1} \\delta( \\epsilon_{12},t_{1})-t_{2} \\delta( \\epsilon_{12},t_{2})}{t_{1}-t_{2}}}g+ \\delta^{ \\prime}g+ \\delta^{ \\prime \\prime}g,", "UniMER-1M_0001713": "\\begin{array}{rl}&{{ \\mathrm{Var}[ \\mathcal{N}_{n}] \\leq \\frac{K^{2} \\mathbb{E}_{Y}[Y^{2}]- \\overline{{{I}}}^{2}(1- \\frac{C}{p})^{2}}{n}+ \\frac{2 \\overline{{{I}}}^{2}C}{n^{2}}(}} \\\\ &{{ \\frac{nC^{ \\prime}p}{1- \\alpha}+ \\frac{ \\alpha^{2}C^{ \\prime}p}{(1- \\alpha)^{2}}+ \\frac{n}{p(1- \\alpha)}+ \\frac{ \\alpha^{2}}{p(1- \\alpha)(1- \\alpha^{2})}).}} \\end{array}", "UniMER-1M_0001687": "H(P_{0})", "UniMER-1M_0001720": "f_{t}", "UniMER-1M_0001698": "\\begin{array}{r}{{ \\mathbb{E} \\! \\left[{ \\mathrm{ \\Large~ \\mathfrak{~}1~}}(A) \\cdot \\mathcal{U}( \\tau_{1}) \\right] \\leq \\mathbb{E} \\! \\left[( \\mathcal{U}( \\tau_{1}))^{2}e^{- \\lambda}Z_{ \\lambda}^{-1}+ \\mathcal{U}( \\tau_{1})( \\mathcal{U}( \\tau_{1})-1)(2d-2)^{2}Z_{ \\lambda}^{-2} \\right].}} \\end{array}", "UniMER-1M_0001672": "(0,1]", "UniMER-1M_0001722": "\\begin{array}{r}{{p(a_{2},c|a_{1})=p(a_{2},c).}} \\end{array}", "UniMER-1M_0001699": "\\dot{ \\epsilon}", "UniMER-1M_0001711": "j^{th}", "UniMER-1M_0001676": "\\sum_{j=- \\infty}^{ \\infty}(x-jh)^{ \\alpha} \\delta_{h}(x-jh)h= \\sum_{k=- \\infty}^{ \\infty} \\widetilde{ \\left(x^{ \\alpha} \\delta_{h} \\right)} \\left( \\frac{2 \\pik}{h} \\right)e^{2 \\piik \\frac{x}{h}}= \\left\\{ \\begin{array}{rl}&{{1, \\quad \\alpha=0}} \\\\ &{{0, \\quad \\alpha=1,2,3,...}} \\end{array} \\right.", "UniMER-1M_0001694": "\\frac{df_{ \\mathrm{{ball}}}}{d \\widetilde{ \\boldsymbol{p}}}= \\frac{ \\par tialf_{ \\mathrm{ball}}}{ \\par tial \\widehat{ \\lambda}_{ \\mathrm{{max}}}} \\bigg\\lvert_{ \\widetilde{ \\boldsymbol{p}}, \\widehat{ \\boldsymbol{p}}} \\frac{ \\par tial \\widehat{ \\lambda}_{ \\mathrm{max}}}{ \\par tial \\widetilde{ \\boldsymbol{p}}} \\bigg\\lvert_{ \\widehat{ \\boldsymbol{p}}}+ \\frac{ \\par tialf_{ \\mathrm{{ball}}}}{ \\par tial \\widehat{ \\boldsymbol{p}}} \\bigg\\lvert_{ \\widehat{ \\lambda}_{ \\mathrm{max}}, \\widetilde{ \\boldsymbol{p}}} \\frac{ \\par tial \\widehat{ \\boldsymbol{p}}}{ \\par tial \\widetilde{ \\boldsymbol{p}}} \\bigg\\lvert_{ \\widehat{ \\lambda}_{ \\mathrm{{max}}}}+ \\frac{ \\par tialf_{ \\mathrm{ball}}}{ \\par tial \\widetilde{ \\boldsymbol{p}}} \\bigg\\lvert_{ \\widehat{ \\lambda}_{ \\mathrm{{max}}}, \\widehat{ \\boldsymbol{p}}}.", "UniMER-1M_0001674": "\\Deltam^{2}=m_{ \\tilde{e},2}^{2}-m_{ \\tilde{e},1}^{2}", "UniMER-1M_0001718": "\\begin{array}{rlr}{{ \\frac{ \\overline{{{u^{ \\prime}u^{ \\prime}}}}}{u_{ \\tau}^{2}} \\Big|_{y^{+}=c}}}&&{{= \\underbrace{ \\int_{- \\infty}^{ \\mathrm{ln}(a/ \\delta)} \\frac{k_{x} \\Phi_{uu}(k_{x},c \\delta_{ \\nu})}{u_{ \\tau}^{2}} \\mathrm{d}( \\mathrm{ln} k_{x})}_{ \\mathrm{Region~I}}+ \\underbrace{ \\int_{ \\mathrm{ln}(b/ \\delta_{ \\nu})}^{ \\infty}k_{x} \\Phi_{uu}(k_{x},c \\delta_{ \\nu}) \\mathrm{d}( \\mathrm{ln} k_{x})}_{ \\mathrm{Region~II}}}} \\end{array}", "UniMER-1M_0001696": "|{ \\bf B}|=const", "UniMER-1M_0001717": "f(a,b,c)={ \\left\\{ \\begin{array}{ll}{{ \\mathrm{cos} A}}&{{{ \\mathrm{if~}} \\triangle{ \\mathrm{~is~acute}},}} \\\\ {{ \\mathrm{cos} A+ \\mathrm{sec} B \\mathrm{sec} C}}&{{{ \\mathrm{if~}} \\measuredangleA{ \\mathrm{~is~obtuse}},}} \\\\ {{ \\mathrm{cos} A- \\mathrm{sec} A}}&{{{ \\mathrm{if~either}} \\measuredangleB{ \\mathrm{~or~}} \\measuredangleC{ \\mathrm{~is~obtuse}}.}} \\end{array} \\right.}", "UniMER-1M_0001700": "\\frac{1}{k_{ \\mathrm{B}}} \\frac{d_{ \\mathrm{i}}S}{dt}= \\Omega \\,{ \\calA_{C}} \\,{ \\calJ_{C}} \\,.", "UniMER-1M_0001721": "{ \\calJ}_{ \\alpha, \\mu_{1}... \\mu_{ \\ell}}^{bJ \\elln}= \\sum_{j= \\ell-1}^{ \\ell+1}{ \\calI}_{ \\alpha \\mu_{1}... \\mu_{ \\ell}}^{bJ \\ellnj},", "UniMER-1M_0001685": "\\hat{H}_{ \\alpha \\beta}= \\frac{1}{2} \\omega \\hat{ \\sigma}_{zs}", "UniMER-1M_0001724": "\\sum_{n=1}^{ \\infty}(b_{n}-b_{n+1})", "UniMER-1M_0001704": "\\begin{array}{rlr}{{ \\psi_{X}(x,y)}}&{{=}}&{{ \\frac{1}{2 \\pi} \\sum_{m=1}^{ \\infty} \\frac{(-1)^{m}}{m} \\biggl[ \\mathrm{cos} \\biggl\\{m \\pi \\biggl(1- \\frac{|x-X|}{W} \\biggr) \\biggr\\}}} \\\\ &&{{- \\mathrm{cos} \\biggl(m \\pi \\frac{x+X}{W} \\biggr) \\biggr]e^{-m \\pi|y|/W}.}} \\end{array}", "UniMER-1M_0001707": "\\sum_{(t,t^{ \\prime}) \\in \\mathcal{E}_{ \\leq}^{(n)}} \\mathrm{log} \\ell_{t,t^{ \\prime}}^{(n)} \\leq \\frac{1}{2} \\sum_{c \\in \\mathcal{C}^{( \\delta,h)}} \\left(S_{c}^{(n)} \\mathrm{log} n+S_{c}^{(n)} \\mathrm{log} \\frac{S_{c}^{(n)}}{n}-S_{c}^{(n)}-2 \\sum_{v=1}^{n} \\mathrm{log} D_{c}^{(n)}(v)! \\right)+O( \\mathrm{log} n).", "UniMER-1M_0001714": "\\begin{array}{rlr}{{{ \\sum_{k=0}^{n+1} \\left( \\begin{array}{l}{{n+1}} \\\\ {{k}} \\end{array} \\right) \\ = \\ 1+ \\sum_{k=1}^{n} \\left( \\begin{array}{l}{{n+1}} \\\\ {{k}} \\end{array} \\right)+1 \\ =}}} \\\\ &{{=}}&{{1+ \\sum_{k=1}^{n} \\left( \\begin{array}{l}{{n}} \\\\ {{k}} \\end{array} \\right)+ \\sum_{k=1}^{n} \\left( \\begin{array}{l}{{n}} \\\\ {{k-1}} \\end{array} \\right)+1 \\ = \\ 2 \\sum_{k=0}^{n} \\left( \\begin{array}{l}{{n}} \\\\ {{k}} \\end{array} \\right) \\ = \\ 2^{n+1}.}} \\end{array}", "UniMER-1M_0001709": "\\times \\delta(p_{ \\theta}-p^{ \\prime}+ \\frac{ \\hbarm}{2}) \\delta(p_{ \\theta}-p^{ \\prime \\prime}- \\frac{ \\hbarn}{2})e^{ \\frac{2i}{ \\hbar}[-(p_{ \\theta}-p^{ \\prime})( \\theta- \\theta^{ \\prime \\prime})+(p_{ \\theta}-p^{ \\prime \\prime})( \\theta- \\theta^{ \\prime})]}f( \\theta^{ \\prime},p^{ \\prime})g( \\theta^{ \\prime \\prime},p^{ \\prime \\prime}).", "UniMER-1M_0001683": "\\gamma(s,t)=A \\mathrm{exp}[( \\alpha+i \\omega)t/ \\tau] \\mathrm{sin}( \\pis/(2L))", "UniMER-1M_0001730": "\\lceil \\mathrm{log} N_{x}N_{k} \\rceil", "UniMER-1M_0001723": "\\begin{array}{rlr}{{[ \\hat{P}_{ \\mathrm{field},k}, \\hat{A}_{j}( \\mathrm{~ \\bf~r~})]}}&{{=}}&{{ \\frac{ \\epsilon_{0}}{2} \\sum_{n=1}^{3} \\intd^{3}r^{ \\prime} \\left\\{[ \\hat{E}_{n}( \\mathrm{~ \\bf~r~}^{ \\prime}) \\par tial_{k}^{ \\prime} \\hat{A}_{n}( \\mathrm{~ \\bf~r~}^{ \\prime}), \\hat{A}_{j}( \\mathrm{~ \\bf~r~})]+... \\right\\}=}} \\end{array}", "UniMER-1M_0001727": "C", "UniMER-1M_0001701": "\\chi", "UniMER-1M_0001712": "14", "UniMER-1M_0001692": "\\varphi_{m}({ \\boldsymbol{r-R_{n}}})", "UniMER-1M_0001747": "\\hslash", "UniMER-1M_0001728": "800", "UniMER-1M_0001737": "\\pi", "UniMER-1M_0001738": "5.0", "UniMER-1M_0001734": "\\approx0.5", "UniMER-1M_0001744": "\\omega/(2 \\pi)=165 \\, \\mathrm{kHz}", "UniMER-1M_0001731": "g( \\lambda)", "UniMER-1M_0001735": "\\foralln \\in \\mathbb{N}^{*}, \\quad \\Delta_{n}( \\theta_{1}, \\theta_{2})= \\frac{1}{4n^{2}} \\left[ \\omega_{C}^{2}- \\omega_{N} \\omega_{S} \\mathrm{tan}^{2n} \\left( \\frac{ \\theta_{1}}{2} \\right) \\mathrm{cot}^{2n} \\left( \\frac{ \\theta_{2}}{2} \\right) \\right]>0.", "UniMER-1M_0001689": "\\begin{array}{rl}&{{| \\alpha_{1} \\hat{ \\zeta}^{1}+ \\alpha_{2} \\hat{ \\zeta}^{2}+ \\alpha_{3} \\hat{ \\zeta}^{3}|_{g^{*}}^{2}=0}} \\\\ {{ \\Rightarrow \\quad}}&{{( \\alpha_{1} \\alpha_{2}+ \\alpha_{1} \\alpha_{3})( \\mathrm{cos} \\theta-1)+ \\alpha_{2} \\alpha_{3} \\cdot2( \\mathrm{cos} \\theta-1)( \\mathrm{cos} \\theta+1)=0}} \\\\ {{ \\Rightarrow \\quad}}&{{ \\frac{ \\alpha_{2}+ \\alpha_{3}}{ \\alpha_{2} \\alpha_{3}}= \\frac{1}{ \\alpha_{3}}+ \\frac{1}{ \\alpha_{2}}=- \\frac{2( \\mathrm{cos} \\theta+1)}{ \\alpha_{1}}.}} \\end{array}", "UniMER-1M_0001695": "\\begin{array}{r}{{ \\frac{E_{x}^{ \\prime2}}{(E_{x \\prime}^{0})^{2}}+ \\frac{E_{y}^{ \\prime2}}{(E_{y \\prime}^{0})^{2}}=1}} \\end{array}", "UniMER-1M_0001715": "\\begin{array}{r}{{{ \\mathbb{P} \\left( \\mathrm{max}_{N(1- \\delta) \\leqn \\leqN}|X_{n}^{ \\circ}|>xN^{3/2} \\right) \\leq \\frac{1}{x^{2}N^{3}} \\sum_{n= \\lfloorN(1- \\delta) \\rfloor}^{N} \\frac{2N-2n+1}{(N-n+1)^{2}} \\, \\sigma_{n}^{2}<}} \\\\ {{ \\frac{4}{x^{2}N} \\sum_{n= \\lfloorN(1- \\delta) \\rfloor}^{N} \\mathrm{log}^{2} \\left( \\frac{N}{N-n+1} \\right) \\to \\frac{4}{x^{2}} \\, \\delta \\mathrm{log}^{2} \\delta,}} \\end{array}", "UniMER-1M_0001693": "<S/N>_{ \\DeltaV}=15.7", "UniMER-1M_0001702": "\\tilde{c} \\equiv \\tilde{ \\delta}/ \\tilde{F}=cEI/ \\lambda^{3}", "UniMER-1M_0001725": "g(|Q_{ij}|)=|Q_{ij}|^{2/3}", "UniMER-1M_0001740": "\\begin{array}{rlr}{{ \\sum \\vertM_{g}^{ \\mathrm{viol}} \\vert^{2}}}&{{=}}&{{g_{S}^{2n-4}(Q^{2})~N^{n-2}(N^{2}-1)}} \\\\ &&{{ \\times \\left( \\sum_{i<j} \\right) \\left( \\sum_{ \\mathrm{perm}} \\frac{1}{S_{12}S_{23}S_{n1}} \\right) \\frac{1}{S_{12}}~.}} \\end{array}", "UniMER-1M_0001729": "\\begin{array}{rl}{{ \\hat{q}_{ \\mathrm{~o~u~t~}}[ \\omega]}}&{{ \\approxH_{0}[ \\omega] \\left( \\hat{q}_{0}[ \\omega]- \\hat{q}_{ \\mathrm{~G~}}[ \\omega] \\right)}} \\\\ {{ \\hat{p}_{ \\mathrm{~o~u~t~}}[ \\omega]}}&{{ \\approxH_{0}[ \\omega] \\left( \\hat{p}_{0}[ \\omega]+ \\hat{p}_{ \\mathrm{~G~}}[ \\omega] \\right).}} \\end{array}", "UniMER-1M_0001741": "{ \\mathrm{inv}^{ \\prime}}= \\underset{ \\underset{i{_}{ \\xi}>i_{ \\mu}}{ \\xi< \\mu}}{ \\sum}1+ \\underset{ \\underset{i{_}{ \\mu}< \\mathrm{I}(V_{i{ \\alpha_{1}}+j{ \\alpha_{2}}})}{ \\mu>b+1}}{ \\sum}1+ \\underset{ \\underset{i{_}{ \\xi}> \\mathrm{I}(V_{i{ \\alpha_{1}}+j{ \\alpha_{2}}})}{ \\xi<b}}{ \\sum}1", "UniMER-1M_0001719": "{ \\mathfrak{sl}}_{3}={ \\mathfrak{h}} \\oplus{ \\mathfrak{g}}_{ \\lambda_{1}- \\lambda_{2}} \\oplus{ \\mathfrak{g}}_{ \\lambda_{1}- \\lambda_{3}} \\oplus{ \\mathfrak{g}}_{ \\lambda_{2}- \\lambda_{3}} \\oplus{ \\mathfrak{g}}_{ \\lambda_{2}- \\lambda_{1}} \\oplus{ \\mathfrak{g}}_{ \\lambda_{3}- \\lambda_{1}} \\oplus{ \\mathfrak{g}}_{ \\lambda_{3}- \\lambda_{2}}", "UniMER-1M_0001749": "H=H_{ \\mathrm{kin}}+H_{ \\mathrm{ext}}+H_{ \\mathrm{H}}+H_{ \\mathrm{XC}}.", "UniMER-1M_0001743": "\\bar{ \\phi}", "UniMER-1M_0001736": "\\DeltaK=- \\Delta \\Sigma= \\pm1", "UniMER-1M_0001732": "c_{g,r}= \\frac{-k_{ \\perp}k_{r}N}{(k_{r}^{2}+k_{ \\perp}^{2})^{3/2}},", "UniMER-1M_0001733": "d \\rho_{X}=-3 \\left( \\rho_{X}-3 \\, \\zeta \\, \\frac{ \\dot{y}}{y} \\right) \\frac{dy}{y} \\,,", "UniMER-1M_0001746": "\\begin{array}{rl}{{ \\dot{ \\langle \\tau_{ \\mathrm{AA}} \\rangle}}}&{{= \\gamma_{ \\mathrm{AB}}^{ \\mathrm{XB}} \\langle \\sigma_{ \\mathrm{XX}} \\tau_{ \\mathrm{BB}} \\rangle+ \\gamma_{ \\mathrm{AB}}^{ \\mathrm{YB}} \\langle \\sigma_{ \\mathrm{YY}} \\tau_{ \\mathrm{BB}} \\rangle- \\gamma_{ \\mathrm{AB}}^{ \\mathrm{XA}} \\langle \\sigma_{ \\mathrm{XX}} \\tau_{ \\mathrm{AA}} \\rangle- \\gamma_{ \\mathrm{AB}}^{ \\mathrm{YA}} \\langle \\sigma_{ \\mathrm{YY}} \\tau_{ \\mathrm{AA}} \\rangle,}} \\\\ {{ \\dot{ \\langle \\tau_{ \\mathrm{BB}} \\rangle}}}&{{= \\gamma_{ \\mathrm{AB}}^{ \\mathrm{XA}} \\langle \\sigma_{ \\mathrm{XX}} \\tau_{ \\mathrm{AA}} \\rangle+ \\gamma_{ \\mathrm{AB}}^{ \\mathrm{YA}} \\langle \\sigma_{ \\mathrm{YY}} \\tau_{ \\mathrm{AA}} \\rangle- \\gamma_{ \\mathrm{AB}}^{ \\mathrm{XB}} \\langle \\sigma_{ \\mathrm{XX}} \\tau_{ \\mathrm{BB}} \\rangle- \\gamma_{ \\mathrm{AB}}^{ \\mathrm{YB}} \\langle \\sigma_{ \\mathrm{YY}} \\tau_{ \\mathrm{BB}} \\rangle.}} \\end{array}", "UniMER-1M_0001742": "*", "UniMER-1M_0001726": "\\begin{array}{rl}{{I_{p,q}}}&{{=- \\mathrm{log} \\left(1-(p+q)+2 \\sqrt{pq} \\left( \\sqrt{pq}+ \\sqrt{(1-p)(1-q)} \\right) \\right)}} \\\\ &{{=- \\mathrm{log} \\left(1- \\left( \\sqrt{p}- \\sqrt{q} \\right)^{2}-2 \\sqrt{pq} \\left(1- \\sqrt{pq}- \\sqrt{(1-p)(1-q)} \\right) \\right)}} \\\\ &{{=- \\mathrm{log} \\left(1- \\left( \\sqrt{p}- \\sqrt{q} \\right)^{2}-2 \\sqrt{pq} \\frac{( \\sqrt{p}- \\sqrt{q})^{2}}{1- \\sqrt{pq}+ \\sqrt{(1-p)(1-q)}} \\right)}} \\\\ &{{=- \\mathrm{log} \\left(1- \\left(1+ \\frac{2 \\sqrt{pq}}{1- \\sqrt{pq}+ \\sqrt{(1-p)(1-q)}} \\right) \\left( \\sqrt{p}- \\sqrt{q} \\right)^{2} \\right).}} \\end{array}", "UniMER-1M_0001750": "S_{5}(V_{4}) \\leq \\frac{A_{3}M_{5}^{3}}{4}.", "UniMER-1M_0001748": "^{11}", "UniMER-1M_0001745": "v={ \\frac{1}{ \\nu_{i}}}{ \\frac{d[ \\mathrm{X}_{i}]}{dt}},", "UniMER-1M_0001739": "\\begin{array}{rl}{{2 \\mathrm{{Ra}} \\left| \\int_{ \\gamma^{-} \\cup \\gamma^{+}}( \\alpha+ \\kappa)u_{ \\tau}n_{1} \\ dS \\right|}}&{{ \\leqC{ \\mathrm{Ra}} \\| \\alpha+ \\kappa \\|_{ \\infty} \\|u \\|_{H^{1}} \\leqC \\| \\alpha+ \\kappa \\|_{ \\infty}^{2} \\mathrm{{Ra}}^{2}+ \\|u \\|_{H^{1}}^{2}.}} \\end{array}", "UniMER-1M_0001697": "f(t)= \\frac{|x_{0}-a|(1+ \\alpha)^{3/2}}{2 \\sqrt{ \\piA}} \\frac{1}{t^{ \\frac{3+ \\alpha}{2}}}e^{- \\frac{(x_{0}-a)^{2}(1+ \\alpha)}{4At^{1+ \\alpha}}}.", "UniMER-1M_0001770": "^{ \\ddagger \\mathsection}", "UniMER-1M_0001773": "j=1", "UniMER-1M_0001772": "\\mathrm{Re}(b_{2})<b_{0}", "UniMER-1M_0001779": "\\Omega", "UniMER-1M_0001767": "\\begin{array}{rl}&{{C_{q}(r, \\theta, \\varphi, \\omega| \\bar{r}_{ \\mathrm{tx}})= \\sum_{n=0}^{ \\infty}{ \\sum_{m=0}^{n}{{{H_{mn}R_{n}^{q}(r, \\omega)} \\mathrm{cos}(m( \\varphi-{ \\varphi_{ \\mathrm{tx}}}))}}}}} \\\\ &{{ \\timesP_{nm}( \\mathrm{cos} \\theta), \\; \\;q \\in \\{s,o \\},}} \\end{array}", "UniMER-1M_0001763": "(mn)", "UniMER-1M_0001754": "C^{(in)}=0", "UniMER-1M_0001762": "10^{-4}c", "UniMER-1M_0001784": "x", "UniMER-1M_0001752": "sss", "UniMER-1M_0001758": "\\hat{ \\mathbf{G}}_{xz}^{sc}( \\mathbf{r}_{A}; \\mathbf{r}_{D}; \\omega)= \\frac{1}{4{ \\pi}}( \\frac{ \\omega}{c})PV \\int_{0}^{ \\infty}{ \\kappa^{2}}r^{p}( \\kappa)J_{1}( \\frac{ \\kappa \\omegaR}{c})e^{2ip{ \\omega}z/c}d{ \\kappa}=- \\hat{ \\mathbf{G}}_{zx}^{sc}", "UniMER-1M_0001765": "GLS_{QCD}(Q^{2})=3 \\left[1-a-(4.583-0.333f)a^{2}-(41.441-8.02f+0.177f^{2})a^{3}- \\frac{8}{27} \\frac{ \\langle \\langleO \\rangle \\rangle}{Q^{2}} \\right]", "UniMER-1M_0001766": "g", "UniMER-1M_0001759": "d \\cdotN", "UniMER-1M_0001769": "\\vec{W}_{j}", "UniMER-1M_0001768": "( \\rho)", "UniMER-1M_0001780": "_{2}F_{1} \\left(a,1-a;c;{ \\frac{1}{2}} \\right)={ \\frac{ \\Gamma({ \\frac{1}{2}}c) \\Gamma({ \\frac{1}{2}} \\left(1+c \\right))}{ \\Gamma({ \\frac{1}{2}} \\left(c+a \\right)) \\Gamma({ \\frac{1}{2}} \\left(1+c-a \\right))}}.", "UniMER-1M_0001756": "^{-2}", "UniMER-1M_0001781": "6 \\", "UniMER-1M_0001751": "B_{2}( \\theta, \\varphi)- \\bar{B}_{2}( \\varphi)= \\varphi_{b}^{ \\prime} \\left[B_{2}( \\theta- \\iota \\Delta \\varphi, \\varphi_{b})- \\bar{B}_{2}( \\varphi_{b}) \\right]+ \\left( \\frac{B_{1}^{2}}{2B_{0}^{ \\prime}} \\right) \\frac{ \\varphi_{b}^{ \\prime \\prime}}{( \\varphi_{b}^{ \\prime})^{2}}+ \\frac{1}{2} \\left(1- \\frac{1}{ \\varphi_{b}^{ \\prime}} \\right)( \\par tial_{ \\varphi}+ \\iota \\par tial_{ \\theta}) \\left( \\frac{B_{1}^{2}}{B_{0}^{ \\prime}} \\right).", "UniMER-1M_0001797": "\\ncong", "UniMER-1M_0001777": "DR", "UniMER-1M_0001796": "L= \\frac{ \\sum_{zl} \\psi_{zl}}{V(V-1)}.", "UniMER-1M_0001783": "\\begin{array}{rlr}{{P( \\mathcal{N}_{2}| \\mathcal{N}_{1})}}&{{=}}&{{ \\binom{k_{C}-1- \\mathcal{N}_{1}}{ \\mathcal{N}_{2}}p^{ \\mathcal{N}_{2}}(1-p)^{k_{C}-1- \\mathcal{N}_{1}- \\mathcal{N}_{2}}.}} \\end{array}", "UniMER-1M_0001782": "\\approx", "UniMER-1M_0001776": "40,000;being30<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,beingarenter,havinglessthanacolleged<PERSON>ree,beingHispanic,beingmarried,havingchildren,andbeingBlack.AscanbeseeninFig.~(bluelines)aswellasTab.~,alloftheWLScoefficientsareveryweak(closeto0)andallbutonearestatisticallyinsignificant.TheonlycoefficientthatisstatisticallysignificantispositiveandcorrespondstothecharacteristicofbeingHispanic.WealsorepeatthisanalysisusingOLS(Fig.~,redlines)tonotobscuretheeffectoftheweightsof", "UniMER-1M_0001761": "<2 \\omega", "UniMER-1M_0001805": "h_{1}", "UniMER-1M_0001806": "\\lambda", "UniMER-1M_0001808": "\\xi_{ \\beta}", "UniMER-1M_0001804": "^{10}", "UniMER-1M_0001810": "| \\quad|", "UniMER-1M_0001798": "1.1819 \\pm0.0084", "UniMER-1M_0001764": "d_{ \\mathrm{OH}}=1.678 \\,ea_{0}", "UniMER-1M_0001813": "P \\llM", "UniMER-1M_0001814": "20 \\", "UniMER-1M_0001801": "\\begin{array}{rl}{{{u} \\left(x,{t}_{1} \\right)}}&{{={u} \\left(x,0 \\right)+{{t}_{1}}{u}^{{ \\prime}} \\left(x,0 \\right)+{ \\widetilde{R}_{1} \\left(x,{t}_{1} \\right)}} \\\\ &{{={ \\psi}_{0} \\left(x \\right)+{ \\tau}{ \\psi}_{1} \\left(x \\right)+{ \\widetilde{R}_{1} \\left(x,{ \\tau} \\right) \\,,}} \\end{array}", "UniMER-1M_0001755": "\\begin{array}{rlr}&&{{ \\left( \\frac{ \\par tial}{ \\par tialt}+ \\frac{{ \\bf p}}{M} \\cdot \\nabla \\right){ \\calF}=}} \\\\ &&{{- \\sum_{i} \\frac{ \\par tial}{ \\par tialp_{i}}f_{i}({ \\bf r},{ \\bf p}){ \\calF}+ \\sum_{i,j} \\frac{ \\par tial^{2}}{ \\par tialp_{i} \\, \\par tialp_{j}}D_{ij}({ \\bf r},{ \\bf p}){ \\calF} \\,,}} \\end{array}", "UniMER-1M_0001757": "\\int_{ \\mathbb{R}} \\frac{d \\hat{q}}{2 \\pi} \\frac{1+2 \\lambda-2i \\hat{q}}{2+2 \\lambda-2i \\hat{q}}e^{-2t \\hat{q}^{2}+i \\hat{y} \\hat{q}} \\simeq \\frac{1}{ \\sqrt{8 \\pit}}e^{- \\frac{ \\hat{y}^{2}}{8t}} \\frac{1+2 \\lambda+ \\frac{ \\hat{y}}{2t}}{2+2 \\lambda+ \\frac{ \\hat{y}}{2t}}", "UniMER-1M_0001791": "\\DeltaN=0, \\pm1", "UniMER-1M_0001820": "1 \\times196", "UniMER-1M_0001793": "p^{(1)}= \\binom{m+d^{(1)}}{d^{(1)}}", "UniMER-1M_0001771": "\\left. \\frac{ \\par tialf}{ \\par tialt} \\right)_{x}+D \\left. \\frac{ \\par tialf}{ \\par tialx} \\right)_{t}= \\left. \\frac{ \\par tialf}{ \\par tialt} \\right)_{z}=0.", "UniMER-1M_0001824": "n,l,m", "UniMER-1M_0001775": "0.022 \\pm0.002", "UniMER-1M_0001812": "X", "UniMER-1M_0001790": "y", "UniMER-1M_0001785": "\\theta_{200}- \\theta_{50}<- \\delta_{s} \\quad(CBL)", "UniMER-1M_0001778": "|g \\,(z)| \\, \\leq \\,c_{(g)} \\,N^{B} \\, \\left| \\frac{z}{A} \\right|^{N} \\,N! \\qquad(N \\, \\geq \\,1) \\, \\,.", "UniMER-1M_0001817": "7246", "UniMER-1M_0001788": "\\varepsilon", "UniMER-1M_0001832": "z", "UniMER-1M_0001811": "\\epsilon_{n}= \\mathrm{exp}[-HT_{-}+n \\pi \\frac{ \\vartheta_{1}^{ \\prime}}{ \\vartheta_{1}}( \\Omegay)+...]", "UniMER-1M_0001802": "f_{q, \\taulm}", "UniMER-1M_0001760": "\\begin{array}{rl}{{ \\tilde{v}_{0}(t=0)=}}&{{ \\tilde{v}_{x}(t=0)= \\tilde{v}_{y}(t=0)=0,}} \\\\ {{ \\tilde{h}_{0}(t=0)=}}&{{h_{0}.}} \\end{array}", "UniMER-1M_0001836": "R=8 \\times10^{-3}", "UniMER-1M_0001753": "\\begin{array}{rl}{{ \\tilde{ \\mathcal{M}}_{1}( \\tilde{r},t)=}}&{{ \\; \\int_{0}^{ \\tilde{r}} \\frac{1}{I(s,t)} \\left( \\int_{0}^{s} \\left(4 \\frac{ \\par tial \\tilde{ \\mathcal{M}}_{0}}{ \\par tialt}- \\tilde{u}_{1} \\frac{ \\par tial \\tilde{ \\mathcal{M}}_{0}}{ \\par tial \\tilde{r}} \\right)I( \\sigma,t) \\, \\mathrm{~d~} \\sigma \\right) \\, \\mathrm{~d~} s+B_{1}(t) \\int_{0}^{ \\tilde{r}} \\frac{1}{I(s,t)} \\, \\mathrm{~d~} s,}} \\end{array}", "UniMER-1M_0001787": "0.008", "UniMER-1M_0001819": "\\eta=1", "UniMER-1M_0001809": "\\mathcal{I}( \\mathfrak{P}_{n_{ \\textup{dim}}})", "UniMER-1M_0001800": "\\begin{array}{rl}{{(d_{f}^{*}d \\zeta)_{l}}}&{{=- \\nabla^{m}(d \\zeta)_{ml}+ \\nabla_{m}f(d \\zeta)_{ml}}} \\\\ &{{=- \\nabla^{m}( \\nabla_{m} \\zeta_{l}- \\nabla_{l} \\zeta_{m})+ \\nabla^{m}f( \\nabla_{m} \\zeta_{l}- \\nabla_{l} \\zeta_{m})}} \\\\ &{{=- \\triangle_{f} \\zeta_{l}+ \\nabla_{m} \\nabla_{l} \\zeta_{m}- \\nabla_{m}f \\nabla_{l} \\zeta_{m}}} \\\\ &{{=- \\triangle_{f} \\zeta_{l}+ \\nabla_{l} \\nabla_{m} \\zeta_{m}+R_{lm} \\zeta_{m}- \\nabla_{m}f \\nabla_{l} \\zeta_{m}}} \\\\ &{{=- \\triangle_{f} \\zeta_{l}+ \\frac{1}{4}H_{lm}^{2} \\zeta_{m},}} \\end{array}", "UniMER-1M_0001818": "E=0", "UniMER-1M_0001803": "E_{12}( \\theta)=- \\mathrm{cos}2 \\theta", "UniMER-1M_0001794": "h_{e} \\leq \\ensuremath{ \\operatorname{O} \\left(h^{ \\ast} \\left( \\frac{ \\gamma+1}{ \\gamma \\mathrm{log}{ \\left({h^{ \\ast}}^{-1} \\right)}} \\right)^{ \\frac{1}{4}} \\right)}.", "UniMER-1M_0001826": "D3", "UniMER-1M_0001792": "t=2100 \\delta/U_{ \\infty}", "UniMER-1M_0001786": "\\mathbf{10^{-3}-10}", "UniMER-1M_0001825": "_{2}", "UniMER-1M_0001799": "\\mathcal{H}_{R}(t,E, \\psi_{2},p_{2}, . . ., \\psi_{N},p_{N})=E+ \\frac{1}{2I_{1}} \\left[ \\mathsf{A}- \\sum_{k=2}^{N}p_{k} \\right]^{2}+ \\sum_{j=2}^{N} \\left[ \\frac{p_{j}^{2}}{2I_{j}}- \\mathsf{M}_{j}^{e}(t) \\psi_{j} \\right]+ \\Pi \\left( \\psi_{2}, \\psi_{3},..., \\psi_{N} \\right) \\,,", "UniMER-1M_0001830": "\\begin{array}{r}{{Q^{*}(s,a)=r(s,a)+ \\sum_{{s^{ \\prime} \\inS}} \\mathbb{P}(s^{ \\prime}|s,a) \\gamma \\mathrm{max}_{a^{ \\prime}}Q^{*}(s^{ \\prime},a^{ \\prime})}} \\\\ {{=r(s,a)+ \\mathbb{E}_{s^{ \\prime} \\inS} \\left[ \\gamma \\mathrm{max}_{a^{ \\prime}}Q^{*}(s^{ \\prime},a^{ \\prime}) \\right]~.}} \\end{array}", "UniMER-1M_0001789": "A+B \\times \\mathrm{SST}+C \\times \\mathrm{SST}^{2}", "UniMER-1M_0001841": "{ \\begin{array}{rlrl}{{ \\mathrm{min} \\sum_{i=1}^{n} \\sum_{j \\neqi,j=1}^{n}c_{ij}x_{ij}}}&{{ \\colon}}&& \\\\ {{x_{ij} \\in}}&{{ \\{0,1 \\}}}&&{{i,j=1, . . .,n;}} \\\\ {{ \\sum_{i=1,i \\neqj}^{n}x_{ij}=}}&{{1}}&&{{j=1, . . .,n;}} \\\\ {{ \\sum_{j=1,j \\neqi}^{n}x_{ij}=}}&{{1}}&&{{i=1, . . .,n;}} \\\\ {{u_{i}-u_{j}+1 \\leq}}&{{(n-1)(1-x_{ij})}}&&{{2 \\leqi \\neqj \\leqn;}} \\\\ {{2 \\lequ_{i} \\leq}}&{{n}}&&{{2 \\leqi \\leqn.}} \\end{array}}", "UniMER-1M_0001840": "\\kappa^{2}=m^{2}=6.38 \\cdot10^{-8} \\, \\mathrm{~m~}^{-2}", "UniMER-1M_0001827": "\\gamma_{32}=2 \\pi \\times8.86 \\, \\mu \\mathrm{s}^{-1}", "UniMER-1M_0001839": "m", "UniMER-1M_0001821": "\\equiv1 \\pm0.02", "UniMER-1M_0001849": "\\tilde{Q}", "UniMER-1M_0001833": "\\begin{array}{rcl}{{ \\frac{dL}{dt}}}&{{=}}&{{k_{1}(K_{M}+s)L-g^{ \\prime}(s) \\cdotf(s,c)}} \\end{array}", "UniMER-1M_0001831": "V_{ \\mathrm{PCs}}", "UniMER-1M_0001823": "p=p_{2}k^{2}+ . . .= \\bigl[ \\frac{1}{2} \\Delta(- \\nu+3B_{0}^{2}/ \\eta)- \\eta \\bigr]k^{2}+ . . .", "UniMER-1M_0001795": "2 \\pi", "UniMER-1M_0001861": "100", "UniMER-1M_0001838": "\\zeta-", "UniMER-1M_0001851": "\\Gamma", "UniMER-1M_0001847": "\\eta>0", "UniMER-1M_0001845": "n", "UniMER-1M_0001835": "\\mathcal{V}_{ij}^{hk}=1- \\frac{(P_{ij}^{hk})^{ \\mathrm{I}}}{(P_{ij}^{hk})^{ \\mathrm{D}}}= \\frac{-2 \\tau_{jk} \\tau_{ih} \\tau_{ik} \\tau_{jh}}{ \\tau_{jk}^{2} \\tau_{ih}^{2}+ \\tau_{ik}^{2} \\tau_{jh}^{2}} \\mathrm{cos} \\left( \\phi_{jk}+ \\phi_{ih}- \\phi_{ik}- \\phi_{jh} \\right)", "UniMER-1M_0001844": "\\begin{array}{rl}{{J_{x}(x)=}}&{{-D \\left( \\piR^{2}(x) \\par tial_{x} \\overline{{{ \\rho}}}_{ \\mathrm{s}}(x)+2 \\pi \\sigma(x) \\frac{eV}{k_{ \\mathrm{B}}T} \\frac{R_{ \\mathrm{t}}R_{ \\mathrm{b}}}{R(x)L} \\right)}} \\\\ &{{+Q(V) \\overline{{{ \\rho}}}_{ \\mathrm{s}}(x),}} \\end{array}", "UniMER-1M_0001871": "\\kappa^{-1}", "UniMER-1M_0001852": "x_{i} \\approx-1", "UniMER-1M_0001855": "Z_{0} \\sim \\mathcal{N}(m_{0}, \\,1)", "UniMER-1M_0001846": "r \\circ \\iota= \\operatorname{id}_{A},", "UniMER-1M_0001872": "\\tau_{S}", "UniMER-1M_0001856": "\\lambda=5", "UniMER-1M_0001850": "36(5)", "UniMER-1M_0001837": "\\mu_{1} \\mu_{2}=1", "UniMER-1M_0001828": "\\leftthreetimes", "UniMER-1M_0001857": "z \\ll1", "UniMER-1M_0001858": "\\mathrm{Tr}( \\phi_{ \\{l_{1}} . . . \\phi_{l_{p-1} \\}}F_{ \\alpha \\beta})- \\mathrm{traces}", "UniMER-1M_0001880": "0.5 \\leqA \\leq2", "UniMER-1M_0001859": "A", "UniMER-1M_0001862": "2.13 \\times10^{-299}", "UniMER-1M_0001829": "X_{i}(x+ \\ell)=X_{i}(x)=X_{i}=(0,0,X)", "UniMER-1M_0001860": "\\sigma=0.5", "UniMER-1M_0001887": "\\nLeftarrow", "UniMER-1M_0001888": "\\omega", "UniMER-1M_0001868": "3 \\times3 \\times3", "UniMER-1M_0001834": "\\begin{array}{rlr}{{ \\mathrm{~T~r~a~n~s~i~t~i~o~n~}}}&{{ \\quad}}&{{ \\mathrm{~R~a~t~e~}}} \\\\ {{(s,e,i) \\to(s-1,e+1,i)}}&{{ \\quad}}&{{ \\lambdasi;}} \\\\ {{(s,e,i) \\to(s,e-1,i+1)}}&{{ \\quad}}&{{ \\mue;}} \\\\ {{(s,e,i) \\to(s,e,i-1)}}&{{ \\quad}}&{{ \\gammai.}} \\end{array}", "UniMER-1M_0001890": "v_{pri,z}", "UniMER-1M_0001774": "F(e)Q(e)", "UniMER-1M_0001869": "\\epsilon \\nu \\ll1", "UniMER-1M_0001843": "\\begin{array}{rl}{{1}}&{{= \\int_{0}^{ \\hat{T}_{ \\varepsilon}} \\| \\int_{ \\mathbf{R}^{3}}(F_{-}^{ \\varepsilon}- \\mu_{ \\gamma^{ \\varepsilon}}e^{ \\gamma^{ \\varepsilon} \\psi^{ \\varepsilon}})d \\xi \\|_{L_{x}^{2}}dt}} \\\\ &{{ \\quad \\lesssim_{M} \\int_{0}^{ \\hat{T}_{ \\varepsilon}} \\frac{1}{ \\varepsilon} \\| \\int_{ \\mathbf{R}^{3}}v \\cdot \\nabla_{x}(F_{-}^{ \\varepsilon}- \\mu_{ \\gamma^{ \\varepsilon}}e^{ \\gamma^{ \\varepsilon} \\psi^{ \\varepsilon}})d \\xi \\|_{L_{x}^{2}}+ \\| \\par tial_{t}( \\gamma^{ \\varepsilon} \\psi^{ \\varepsilon})e^{ \\gamma^{ \\varepsilon} \\psi^{ \\varepsilon}} \\|_{L_{x}^{2}}dt}} \\\\ &{{ \\quad \\lesssim_{M} \\int_{0}^{ \\hat{T}_{ \\varepsilon}}1+ \\frac{1}{ \\varepsilon} \\sqrt{ \\widetilde{ \\mathscrD}_{-,2}^{ \\varepsilon}}dt}} \\\\ &{{ \\quad \\lesssim_{M} \\hat{T}_{ \\varepsilon}+ \\hat{T}_{ \\varepsilon}^{ \\frac{1}{2}}}} \\end{array}", "UniMER-1M_0001864": "\\prec", "UniMER-1M_0001870": "_2", "UniMER-1M_0001875": "p_{1}", "UniMER-1M_0001898": "40", "UniMER-1M_0001815": "\\frac{1}{A} \\frac{d}{dx}(A \\frac{d \\bar{P}}{dx})+k_{x}^{2} \\bar{P}=0,", "UniMER-1M_0001876": "D(X)= \\par tial_{z}(X)+[ \\phi^{ \\prime},X]+[ \\mu,X] \\nonumber", "UniMER-1M_0001904": "I_{j}", "UniMER-1M_0001905": "'", "UniMER-1M_0001878": "8 \\times8 \\times8", "UniMER-1M_0001854": "\\DeltaL=(n_{ \\mathrm{~l~i~q~u~i~d~}}-n_{ \\mathrm{~a~i~r~}}) \\Deltaz", "UniMER-1M_0001910": "d=", "UniMER-1M_0001885": "C_{v}=0", "UniMER-1M_0001908": "\\begin{array}{r}{{ \\overline{{{{u}}}}^{2}=u^{2}+2u \\left( \\frac{1}{2!} \\frac{ \\Delta^{2}}{12} \\frac{ \\par tial^{2}u}{ \\par tialx^{2}} \\right)+ \\mathcal{O} \\left( \\Delta^{4} \\right).}} \\end{array}", "UniMER-1M_0001907": "a_{ij}=a_{ij}^{+}-a_{ij}^{-}", "UniMER-1M_0001874": "\\begin{array}{l}{{I_{1}= \\operatorname{tr}(S_{ij})=e_{1}+e_{2}+e_{3},}} \\\\ {{I_{2}= \\frac{1}{2} \\left(( \\operatorname{tr}(S_{ij}))^{2}- \\operatorname{tr} \\left(S_{ij}^{2} \\right) \\right)=e_{1}e_{2}+e_{1}e_{3}+e_{2}e_{3},}} \\\\ {{I_{3}= \\operatorname{det}(S_{ij})=e_{1}e_{2}e_{3}.}} \\end{array}", "UniMER-1M_0001842": "\\Deltah_{2}=-3 \\cdot10^{5}~ \\mathrm{~J~/~k~g~}", "UniMER-1M_0001881": "16 \\pi^{2} \\frac{d}{dt}| \\xi_{L}^{u}|^{2}=2 \\left[ \\left( \\frac{1}{3}| \\xi_{L}^{u}|^{2}-G_{L}^{u} \\right)| \\xi_{L}^{u}|^{2}+ \\frac{1}{2}| \\xi_{L}^{u}|^{4} \\right] \\ .", "UniMER-1M_0001863": "\\beta_{F^{2}}(< \\phi_{0}>) \\equiv \\sum_{n=0}^{ \\infty} \\beta_{F^{2}}^{n} \\left( \\frac{< \\phi_{0}>}{M_{p}} \\right)^{n} \\;,", "UniMER-1M_0001879": "L=c_{0}s+c \\sqrt{{ \\dot{ \\bf e}}_{3}^{2}-k_{2}^{2}}+{ \\bf p}({ \\dot{ \\bf x}}-s{ \\bf e}_{1})+ \\sum_{i}{ \\bf p}_{i-1}({ \\dot{ \\bf e}}_{i-1}-k_{i-1}{ \\bf e}_{i}+k_{i-2}{ \\bf e}_{i-2})- \\sum_{i,j}d_{ij}({ \\bf e}_{i}{ \\bf e}_{j}- \\delta_{ij}),", "UniMER-1M_0001920": "r_{*}", "UniMER-1M_0001921": "n+^{12}", "UniMER-1M_0001916": "V_{p}", "UniMER-1M_0001882": "\\begin{array}{r}{{ \\frac{{ \\mathscrZ}_{ \\alpha \\beta, \\alpha \\beta}}{{ \\mathscrZ}_{ \\mathrm{pert}}} \\simeq \\frac{1}{2 \\pi \\left(- \\par tial_{x}^{2}{ \\mathcalA}_{ \\alpha \\beta}^{[-1]}(x_{mn}) \\right)^{2}} \\, \\mathrm{exp} \\left(2{ \\mathcal{A}_{ \\alpha \\beta}^{[0]}(x_{mn})+ \\widehat{{ \\mathcal{A}}_{ \\alpha \\beta, \\alpha \\beta}^{[0]}(x_{mn}) \\right){ \\mathrm{e}}^{2 \\mathsf{A}_{ \\mathrm{D}}(m,n)}+ . . ..}} \\end{array}", "UniMER-1M_0001873": "\\phi= \\phi_{0} \\mathrm{cos}( \\omegat+ \\varphi) \\,,", "UniMER-1M_0001894": "i", "UniMER-1M_0001901": "( \\boldsymbol{k}, \\boldsymbol{ \\ell})", "UniMER-1M_0001927": "\\cdot", "UniMER-1M_0001865": "\\begin{array}{rl}{{ \\mathcal{G}_{ \\underline{{{ \\psi}}}}}}&{{=-H( \\mathbf{W}^{*})- \\sum_{ \\mathbf{A} \\in \\mathbb{A}} \\delta_{ \\mathbf{A}, \\mathbf{A}^{*}} \\mathrm{ln} Z_{ \\mathbf{A}}}} \\end{array}", "UniMER-1M_0001877": "\\boldsymbol{x} \\in \\mathbb{R}^{2}", "UniMER-1M_0001889": "\\mathbf{ \\theta}= \\left( \\mathbf{ \\theta}_{1}: \\mathbf{ \\theta}_{2} \\right)", "UniMER-1M_0001896": "c_{p}= \\tilde{c}_{p}/ \\tilde{R}", "UniMER-1M_0001917": "q", "UniMER-1M_0001903": "\\bar{ \\nu}", "UniMER-1M_0001902": "^2S_{1/2} \\rightarrow \\,^{2}P_{1/2}", "UniMER-1M_0001884": "Q(z)= \\frac{1}{h^{3N-1}} \\int \\ensuremath{ \\mathrm{d}} \\mathbf{q}^{ \\prime} \\  \\int \\ensuremath{ \\mathrm{d}} \\mathbf{p}_{q}^{ \\prime} \\ e^{- \\beta \\mathcal{H}(z)}", "UniMER-1M_0001925": "\\begin{array}{r}{{E_{ \\mathrm{H}}[n]= \\frac{W}{2} \\int( \\mathrm{d} \\vec{r})( \\mathrm{d} \\vec{r}^{ \\prime}) \\frac{n( \\vec{r}) \\,n( \\vec{r}^{ \\prime})}{| \\vec{r}- \\vec{r}^{ \\prime}|}= \\frac{W}{2} \\int \\frac{( \\mathrm{d} \\vec{k})}{(2 \\pi)^{3}} \\,4 \\pi \\frac{n( \\vec{k}) \\,n(- \\vec{k})}{k^{2}} \\,,}} \\end{array}", "UniMER-1M_0001912": "\\begin{array}{rl}{{ \\chi^{2}(K \\mu,KP)}}&{{= \\int \\Big( \\frac{dK \\mu}{dKP}- \\mu( \\Omega) \\Big)^{2} \\,dKP}} \\\\ &{{= \\int \\Big( \\alpha_{+} \\Big( \\frac{dK \\mu_{+}}{dKP}-1 \\Big)- \\alpha_{-} \\Big( \\frac{dK \\mu_{-}}{dKP}-1 \\Big) \\Big)^{2} \\,dKP}} \\\\ &{{= \\alpha_{+}^{2} \\chi^{2} \\big(K \\mu_{+},KP \\big)+ \\alpha_{-}^{2} \\chi^{2} \\big(K \\mu_{-},KP \\big)}} \\\\ &{{-2 \\alpha_{+} \\alpha_{-} \\int \\Big( \\frac{dK \\mu_{+}}{dKP}-1 \\Big) \\Big( \\frac{dK \\mu_{-}}{dKP}-1 \\Big) \\,dKP}} \\\\ &{{ \\leq \\alpha_{+}^{2} \\chi^{2} \\big( \\mu_{+},P \\big)+ \\alpha_{-}^{2} \\chi^{2} \\big( \\mu_{-},P \\big)+2 \\alpha_{+} \\alpha_{-}= \\chi^{2}( \\mu,P),}} \\end{array}", "UniMER-1M_0001895": "B_{ \\mathrm{gap,i}} \\,{ \\approx} \\,230~ \\mathrm{pT}", "UniMER-1M_0001913": "\\begin{array}{rl}{{ \\frac{d}{dt} \\left\\langle \\hat{X}_{1}(0), \\vec{Y}(t) \\right\\rangle}}&{{= \\mathbf{A} \\left\\langle \\hat{X}_{1}(0), \\vec{Y}(t) \\right\\rangle,}} \\\\ {{ \\frac{d}{dt} \\left\\langle \\hat{X}_{1}(0), \\vec{X}(t) \\right\\rangle}}&{{= \\mathbf{C} \\left\\langle \\hat{X}_{1}(0), \\vec{X}(t) \\right\\rangle+ \\varepsilon \\left\\langle \\hat{X}_{1}(0), \\vec{I}(t) \\right\\rangle.}} \\end{array}", "UniMER-1M_0001915": "\\tilde{s}", "UniMER-1M_0001911": "||f_{1}||_{p_{1}}||f_{2}||_{p_{2}}||f_{3}||_{p_{3}}=(q^{-d}|S_{t}|)^{ \\frac{1}{p_{1}}}q^{- \\frac{d}{p_{2}}}(q^{-d}|S_{t}|)^{ \\frac{1}{p_{3}}} \\simq^{- \\frac{1}{p_{1}}- \\frac{d}{p_{2}}- \\frac{1}{p_{3}}},", "UniMER-1M_0001866": "q^{2} \\, \\delta_{ \\mu \\nu}-q_{ \\mu}q_{ \\nu}", "UniMER-1M_0001929": "\\begin{array}{rl}&{{ \\hat{ \\rho}= \\frac{ \\rho}{ \\rho_{0}}, \\quad \\hat{v}= \\frac{v}{a_{0}}, \\quad \\hat{ \\mathcal{T}}= \\frac{ \\mathcal{T}}{T_{0}}, \\quad \\hat{ \\Pi}= \\frac{ \\Pi}{ \\rho_{0} \\frac{k_{B}}{m_{0}}T_{0}},}} \\\\ &{{ \\hat{ \\varphi}= \\frac{ \\varphi}{t_{c} \\,a_{0}}, \\quad \\hat{x}= \\frac{x}{t_{c} \\,a_{0}}, \\quad \\hat{t}= \\frac{t}{t_{c}}, \\quad \\hat{ \\tau}= \\frac{ \\tau}{t_{c}},}} \\\\ &{{ \\hat{ \\psi}= \\frac{t_{c}}{ \\rho_{0}T_{0}} \\psi_{11}, \\quad \\hat{ \\theta}= \\frac{t_{c}}{ \\rho_{0} \\frac{k_{B}}{m_{0}}T_{0}^{2}} \\theta_{11}, \\quad \\hat{ \\kappa}= \\frac{t_{c}}{ \\rho_{0} \\frac{k_{B}}{m_{0}}T_{0}^{2}} \\kappa_{11}, \\quad \\hat{ \\phi}= \\frac{t_{c}}{ \\rho_{0} \\frac{k_{B}}{m_{0}}T_{0}^{2}} \\phi_{11}, \\quad}} \\end{array}", "UniMER-1M_0001923": "\\phi_{ \\mathrm{~m~o~d~}}=A( \\vec{r}) \\mathrm{exp}{ \\left(jS( \\vec{r}) \\right)}.", "UniMER-1M_0001933": "17", "UniMER-1M_0001936": "( \\phi(t))^{2}= \\sum_{n=0}^{ \\infty} \\Bigl( \\sum_{m=0}^{n} \\,a_{m}a_{n-m} \\Bigr) \\,e^{ \\alphant} \\,.", "UniMER-1M_0001938": "\\mathcal{A}_{p_{i}}", "UniMER-1M_0001924": "\\varepsilon_{R}( \\hat{ \\rho})= \\hat{R}( \\theta, \\mathbf{n}) \\hat{ \\rho} \\hat{R}^{ \\dagger}( \\theta, \\mathbf{n}).", "UniMER-1M_0001892": "\\nu_{ \\textrm{T}}=C_{ \\nu} \\frac{K^{2}}{ \\varepsilon} \\left({1-C_{ \\textrm{N}} \\frac{1}{K} \\frac{D}{Dt} \\frac{K^{2}}{ \\varepsilon}} \\right)", "UniMER-1M_0001934": "Obj \\left(M; \\vartheta \\right)= \\prod_{i:x_{i}<M} \\frac{f_{3}( \\vartheta;x_{i})}{F_{3}( \\vartheta;M)},", "UniMER-1M_0001937": "{E}_{q}^{ \\underline{{{a}}}}(Z(z)) \\equivD_{q}Z^{ \\underline{{{M}}}}E_{ \\underline{{{M}}}}^{ \\underline{{{a}}}}=0.", "UniMER-1M_0001867": "\\left\\{l=1,k=m=4,i=2,n=5 \\right\\}", "UniMER-1M_0001930": "{ \\mathfrak{N}}_{ \\frac{1}{2}}( \\nu; \\nu_{0}, \\theta)={ \\frac{1}{4{ \\sqrt{ \\pi}} \\theta^{3/2}}}( \\nu- \\nu_{0})^{1/2}e^{-{ \\frac{ \\nu- \\nu_{0}}{4 \\theta}}}", "UniMER-1M_0001883": "\\begin{array}{rlr}{{ \\hat{V}_{4}(x)}}&{{=}}&{{ \\Bigg\\{4~ \\Bigg[(24-6~x^{2}+x^{4})~I_{0} \\left( \\frac{x^{2}}{2} \\right)-x^{2}~(x^{2}-4)~I_{1} \\left( \\frac{x^{2}}{2} \\right) \\Bigg]^{2} \\Bigg\\}^{-1} \\times}} \\\\ &{{ \\times}}&{{ \\Bigg[(9216-7488~x^{2}+1920~x^{4}-180~x^{6}+4~x^{8}+x^{10})~I_{0} \\left( \\frac{x^{2}}{2} \\right)^{2}-}} \\\\ &{{-}}&{{2~x^{2}~(x-2)~(x+2)~(x^{2}+16)~(24-6~x^{2}+x^{4})~I_{0} \\left( \\frac{x^{2}}{2} \\right)I_{1} \\left( \\frac{x^{2}}{2} \\right)+}} \\\\ &{{+}}&{{x^{2}~(x^{2}-4)^{2}~(72+16~x^{2}+x^{4})~I_{1} \\left( \\frac{x^{2}}{2} \\right)^{2} \\Bigg].}} \\end{array}", "UniMER-1M_0001953": "w_{ \\alpha}", "UniMER-1M_0001918": "P_{ \\alpha \\to \\beta}=|S_{ \\beta \\alpha}|^{2}", "UniMER-1M_0001914": "\\operatorname{Def}(X):={ \\Bigl\\ \\{x \\midx \\inX{ \\mathrm{~and~}}(X, \\in) \\models \\phi(x,y_{1}, . . .,y_{n}) \\}: \\phi{ \\mathrm{~is~a~first-order~formula~and~}}y_{1}, . . .,y_{n} \\inX{ \\Bigr\\}}.", "UniMER-1M_0001928": "P_{ \\mathrm{ref}} \\approx6.8 \\times10^{-6}", "UniMER-1M_0001943": "5", "UniMER-1M_0001932": "w=P(FHP| \\mathbf{r}, \\mathbf{v})", "UniMER-1M_0001941": "\\begin{array}{r}{{ \\left[ \\hat{ \\lambda}_{6}, \\hat{ \\lambda}_{7} \\right]=2i \\left(- \\frac{1}{2} \\hat{ \\lambda}_{3}+ \\frac{ \\sqrt{3}}{2} \\hat{ \\lambda}_{8} \\right).}} \\end{array}", "UniMER-1M_0001962": "P", "UniMER-1M_0001931": "\\begin{array}{r}{{ \\mathcal{E}_{m,n}(u)=I_{ \\mathrm{np};n}(u)+I_{ \\mathrm{p};m,n}(u, \\psi_{u;m,n}),}} \\end{array}", "UniMER-1M_0001891": "\\begin{array}{r}{{ \\| \\mathcal{M}_{3}P_{ \\neq}[(a( \\par tial_{v}-t \\par tial_{z})P_{ \\neq}( \\Upsilon_{1} \\Pi))] \\|_{L^{2}}, \\quad \\| \\mathcal{M}_{3}P_{ \\neq}[(a \\par tial_{z}P_{ \\neq}( \\Upsilon_{1} \\Pi))] \\|_{L^{2}}}} \\end{array}", "UniMER-1M_0001949": "{K_{ \\bot}^{ \\prime}},{K_{ \\par allel}^{ \\prime}}=3 \\pi \\rho \\nud_{ \\mathrm{p}}f( \\textrm{AR})", "UniMER-1M_0001947": "\\begin{array}{rl}{{^{C} \\par tial_{t}^{ \\alpha}w(t,x)- \\mathfrak{D}w(t,x)}}&{{=0, \\quad t>0, \\, \\,x \\inG,}} \\\\ {{w(t,x)|_{_{_{t=0}}}}}&{{=w_{0}(x), \\quad w_{0} \\inL^{p}(G),}} \\\\ {{ \\par tial_{t}w(t,x)|_{_{_{t=0}}}}}&{{=w_{1}(x), \\quad w_{1} \\inL^{p}(G),}} \\end{array}", "UniMER-1M_0001922": "\\begin{array}{rl}{{ \\mathbf{P}_{0,y} \\Big( \\operatorname{sup}_{t \\leq \\tau^{ \\varepsilon}}|V_{t}^{ \\varepsilon}-y| \\geq \\varepsilon^{1- \\delta} \\Big)}}&{{= \\mathbf{P}_{0,y} \\Big( \\operatorname{sup}_{t \\leq \\tau^{ \\varepsilon}}|V_{t}^{ \\varepsilon}-y|^{2k} \\geq \\varepsilon^{2k-2k \\delta} \\Big)}} \\\\ &{{ \\leq \\varepsilon^{-2k+2k \\delta} \\mathbf{E}_{0,y} \\operatorname{sup}_{t \\leq \\tau^{ \\varepsilon}}|V^{ \\varepsilon}(t)-y|^{2k} \\leqC_{5} \\varepsilon^{2k \\delta}.}} \\end{array}", "UniMER-1M_0001945": "\\Psi_{k}", "UniMER-1M_0001971": "C^{1, \\alpha}", "UniMER-1M_0001972": "x_{A}", "UniMER-1M_0001973": "\\operatorname{lim}_{n \\rightarrow \\infty}{ \\frac{1}{10^{n}}}=0.", "UniMER-1M_0001974": "P( \\infty)", "UniMER-1M_0001940": "-y^{2} \\left({ \\frac{ \\par tial^{2}}{ \\par tialx^{2}}}+{ \\frac{ \\par tial^{2}}{ \\par tialy^{2}}} \\right)E(z,s)=s(1-s)E(z,s),", "UniMER-1M_0001955": "\\textbf{A}^{ \\mathrm{M}}= \\left( \\begin{array}{cccccc}{{0}}&{{-1}}&{{1}}&{{0}}&{{0}}&{{0}} \\\\ {{-1}}&{{0}}&{{1}}&{{0}}&{{0}}&{{0}} \\\\ {{1}}&{{1}}&{{-1}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{1}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{1}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{0}}&{{1}} \\end{array} \\right).", "UniMER-1M_0001977": "e", "UniMER-1M_0001956": "\\mathcal{U}_{ \\mathrm{A}}( \\mathbf{x})= \\int_{0}^{ \\infty} \\mathcal{U}_{ \\mathrm{A}}( \\mathbf{x},z) \\mathrm{d} z= \\frac{|A_{0}^{0}|^{2}}{16 \\pie^{2} \\hbar} \\frac{ \\varepsilon_{ \\mathrm{A}}}{ \\varepsilon_{ \\mathrm{avg}}} \\frac{1}{|J|}.", "UniMER-1M_0001976": "h_{v} \\in \\mathcal{H}= \\mathbb{R}^{C}", "UniMER-1M_0001886": "\\tau_{ \\xi \\theta}=-hh^{ \\prime} \\left(1- \\frac{ \\xi}{h} \\right) \\;,", "UniMER-1M_0001952": "\\mathcal{M}= \\mathcal{M}_{v^{r}} \\cup( \\mathcal{M}_{v^{r}})^{c}", "UniMER-1M_0001957": "n-1", "UniMER-1M_0001959": "\\deltaL_{C}= \\delta \\left\\langleL_{K} \\right\\rangle+ \\delta \\left\\langle \\Psi \\right\\rangle/2 \\pi.", "UniMER-1M_0001980": "\\kappa_{23}", "UniMER-1M_0001981": "\\rightarrow", "UniMER-1M_0001900": "V(r)= \\frac{2 \\pi}{ \\lambda}I_{n}r+ \\frac{L^{2}}{2Mr^{2}},", "UniMER-1M_0001958": "\\begin{array}{r}{{ \\bar{ \\mathbf{w}}_{i,j}^{(u)}= \\left[ \\mathbf{0}_{ \\frac{N-N_{u}}{2}}^{T}, \\mathbf{b}^{T} \\left( \\theta_{i}^{(u)},r_{i,j}^{(u)} \\right), \\mathbf{0}_{ \\frac{N-N_{u}}{2}}^{T} \\right]^{T},}} \\\\ {{i=1,...,N_{u},j=1,...,S_{u}.}} \\end{array}", "UniMER-1M_0001939": "\\lambda_{ \\mathrm{min}}( \\mathbf{M},s)= \\mathrm{min}_{v: \\|v \\|_{0} \\leqs} \\frac{v^{ \\mathrm{T}} \\mathbf{M}v}{v^{ \\mathrm{T}}v}, \\quad \\quad \\lambda_{ \\mathrm{max}}( \\mathbf{M},s)= \\mathrm{max}_{v: \\|v \\|_{0} \\leqs} \\frac{v^{ \\mathrm{T}} \\mathbf{M}v}{v^{ \\mathrm{T}}v}.", "UniMER-1M_0001950": "\\begin{array}{r}{{V^{ \\mathrm{scat}}(r, \\theta) \\approxA \\left(e^{-i \\frac{qr}{ \\hbar} \\mathrm{cos}{ \\theta}}+g( \\theta) \\frac{e^{-i \\frac{qr}{ \\hbar}}}{ \\sqrt{r}} \\right).}} \\end{array}", "UniMER-1M_0001944": "\\begin{array}{r}{{f_{i} \\left( \\mathbf{x}+ \\mathbf{c}_{i} \\Deltat,t+ \\Deltat \\right)=f_{i}( \\mathbf{x},t)- \\frac{1}{ \\tau_{ \\phi}} \\left[f_{i}( \\mathbf{x},t)-f_{i}^{ \\mathrm{eq}}( \\mathbf{x},t) \\right]+ \\Deltat \\left(1- \\frac{1}{2 \\tau_{ \\phi}} \\right)R_{i}( \\mathbf{x},t),}} \\end{array}", "UniMER-1M_0001992": "<", "UniMER-1M_0001954": "\\tau_{d}< \\tau_{b,n}", "UniMER-1M_0001946": "( \\xi=-3.7 \\times10^{-3},u^{ \\prime}/U=1 \\", "UniMER-1M_0001935": "\\begin{array}{rl}&{{ \\mathbb{P} \\left( \\tau< \\varepsilon \\right)= \\mathbb{P} \\left( \\bigcup_{l=1}^{ \\infty} \\bigcap_{k=l}^{ \\infty} \\lbrace \\tilde{ \\tau}_{k}< \\varepsilon \\rbrace \\right) \\leq \\operatorname{limsup}_{l \\to \\infty} \\mathbb{P} \\left( \\tilde{ \\tau}_{l}< \\varepsilon \\right)}} \\\\ {{ \\leq}}&{{ \\operatorname{sup}_{l \\geq1} \\mathbb{P} \\left( \\left\\lbrace \\operatorname{sup}_{t \\in[0, \\tilde{ \\tau}_{l} \\wedge \\varepsilon]} \\|u^{j_{l}} \\|_{ \\tilde{s}}> \\|u_{0}^{j_{l}} \\|_{ \\tilde{s}}+1 \\right\\rbrace \\right) \\leq \\operatorname{sup}_{l \\geq1} \\mathbb{P} \\left( \\left\\lbrace \\operatorname{sup}_{t \\in[0, \\tau_{j_{l}}^{T} \\wedge \\varepsilon]} \\|u^{j_{l}} \\|_{ \\tilde{s}}> \\|u_{0}^{j_{l}} \\|_{ \\tilde{s}}+1 \\right\\rbrace \\right).}} \\end{array}", "UniMER-1M_0001963": "\\bar{r}= \\frac{ \\mathrm{sin} \\big( \\theta- \\psi/2 \\big)}{ \\mathrm{sin} \\big( \\theta+ \\psi/2 \\big)}", "UniMER-1M_0001967": "\\epsilon_{1}=g+ \\hbarv_{F}k", "UniMER-1M_0002000": "\\alpha", "UniMER-1M_0002001": "T_{12}=T_{22}=T_{32}=T_{42}=0", "UniMER-1M_0001961": "f_{x}=- \\frac{ \\par tialU_{ \\mathrm{db}}}{ \\par tialx}", "UniMER-1M_0001966": "\\int_{ \\mathcal{S}(t)} \\left( \\frac{ \\par tial \\mathbf{B}}{ \\par tialt}+ \\mathbf{ \\nabla} \\times \\mathbf{E} \\right) \\cdot \\hat{ \\boldsymbol{n}}dS= \\int_{ \\sigma}[ \\mathbf{E}]_{-}^{+} \\cdot \\hat{ \\mathbf{t}}dl~,", "UniMER-1M_0001970": "18 \\mathrm{~p~A~}", "UniMER-1M_0001951": "\\Delta_{r}=0.01", "UniMER-1M_0002006": "1 \\", "UniMER-1M_0001978": "\\widehat{ \\DeltaF}_{ \\mathrm{Jar}}=- \\beta^{-1} \\mathrm{ln} \\, \\big(n_{s}^{-1} \\sum_{i=1}^{n_{s}}e^{- \\betaW_{ \\mathrm{trad}}^{i}} \\big)", "UniMER-1M_0001982": "(n,[n \\mathrm{log} n])=(500,3107)", "UniMER-1M_0001969": "\\begin{array}{rl}{{Z( \\mathfrak{m}) \\timesZ( \\mathfrak{m}^{ \\prime})}}&{{ \\hookrightarrow \\underbrace{Z( \\mathfrak{m}) \\timesZ( \\Delta)}_{ \\mathrm{irreducible}} \\timesZ( \\mathfrak{m}^{ \\prime} \\setminus \\Delta) \\congZ( \\Delta) \\timesZ( \\mathfrak{m}) \\timesZ( \\mathfrak{m}^{ \\prime} \\setminus \\Delta)}} \\\\ &{{ \\hookrightarrow \\underbrace{Z( \\Delta) \\timesZ( \\Delta)}_{ \\mathrm{irreducible}} \\timesZ( \\mathfrak{m} \\setminus \\Delta) \\timesZ( \\mathfrak{m}^{ \\prime} \\setminus \\Delta).}} \\end{array}", "UniMER-1M_0001986": "\\mathbf{ \\Pi}_{ \\mathrm{~n~e~q~}}^{ \\mathrm{~i~n~}}", "UniMER-1M_0001985": "\\hat{ \\par tial}_{t} \\mathrm{ln} \\hat{N}_{s}+ \\frac{1}{ \\hat{B} \\delta} \\{ \\hat{ \\phi}_{s}, \\mathrm{ln} \\hat{N}_{s} \\}= \\hat{ \\Lambda}_{cs}+ \\hat{ \\Lambda}_{Bs},", "UniMER-1M_0001942": "\\begin{array}{r}{{ \\left\\{ \\begin{array}{rl}&{{ \\frac{1}{ \\Deltax \\Deltay} \\int_{I_{i,j}^{k}}P_{1}(x,y)dxdy=u_{k}, \\quad k=1,2,4,5,}} \\\\ &{{ \\frac{1}{ \\Deltax \\Deltay} \\int_{I_{i,j}^{k}}P_{2}(x,y)dxdy=u_{k}, \\quad k=2,3,5,6,}} \\\\ &{{ \\frac{1}{ \\Deltax \\Deltay} \\int_{I_{i,j}^{k}}P_{3}(x,y)dxdy=u_{k}, \\quad k=4,5,7,8,}} \\\\ &{{ \\frac{1}{ \\Deltax \\Deltay} \\int_{I_{i,j}^{k}}P_{4}(x,y)dxdy=u_{k}, \\quad k=5,6,8,9,}} \\\\ &{{ \\frac{1}{ \\Deltax \\Deltay} \\int_{I_{i,j}^{5}}P_{n}(x,y) \\frac{x-x_{i}}{ \\Deltax}dxdy=v_{5}, \\quad n=1,2,3,4,}} \\\\ &{{ \\frac{1}{ \\Deltax \\Deltay} \\int_{I_{i,j}^{5}}P_{n}(x,y) \\frac{y-y_{j}}{ \\Deltay}dxdy=w_{5}, \\quad n=1,2,3,4.}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0002011": "\\mathrm{Re} \\left( \\omega_{ \\pm} \\right) \\neq0", "UniMER-1M_0001960": "\\nabla \\boldsymbol{v} \\bar{S}", "UniMER-1M_0002015": "\\sim1", "UniMER-1M_0001926": "\\begin{array}{rl}&{{K^{1/2}x_{i}= \\lambdaq^{i}x_{i},K^{1/2}y_{i}= \\lambdaq^{i}y_{i},L^{1/2}x_{i}= \\muq^{-i}x_{i},L^{1/2}y_{i}= \\muq^{-i}y_{i} \\mathrm{~for~} i \\in \\{0, . . .,N-1 \\};}} \\\\ &{{Ex_{N-1}=cx_{0},Ey_{N-1}=cy_{0},Ex_{i}=x_{i+1},Ey_{i}=y_{i+1} \\mathrm{~for~} i \\in \\{0, . . .,N-2 \\};}} \\\\ &{{Fx_{0}=0,Fx_{i}= \\left( \\frac{ \\mu^{2}q^{-i+1}- \\lambda^{2}q^{i-1}}{q-q^{-1}}[i] \\right)x_{i-1} \\mathrm{~for~} i \\in \\{1, . . .,N-1 \\};}} \\\\ &{{Fy_{0}=x_{0},Fy_{i}= \\left( \\frac{ \\mu^{2}q^{-i+1}- \\lambda^{2}q^{i-1}}{q-q^{-1}}[i] \\right)y_{i-1}+x_{i} \\mathrm{~for~} i \\in \\{1, . . .,N-1 \\}.}} \\end{array}", "UniMER-1M_0001987": "\\tau_{0}= \\Gamma(M_{21}^{ \\mathrm{dc}}+M_{11}^{ \\mathrm{dc}})= \\frac{ \\Gamma}{ \\mathrm{i} \\delta+( \\gamma+2 \\Gamma)}", "UniMER-1M_0002018": "\\infty", "UniMER-1M_0002017": "u(x,0)= \\left\\{ \\begin{array}{ll}{{ \\gamma \\big(1- \\mathrm{tanh}( \\frac{x}{ \\epsilon}) \\big), \\qquad}}&{{if \\qquad \\gamma \\big(1- \\mathrm{tanh}( \\frac{x}{ \\epsilon}) \\big) \\geq \\xi,}} \\\\ {{0, \\qquad}}&{{if \\qquad \\gamma \\big(1- \\mathrm{tanh}( \\frac{x}{ \\epsilon}) \\big)< \\xi,}} \\end{array} \\right.", "UniMER-1M_0002016": "f^{(n)}(r;t)=O \\bigg( \\frac{1}{(k_{0}(t)r)^{2}} \\bigg)", "UniMER-1M_0002023": "\\tau_{ \\mathrm{max}}", "UniMER-1M_0002024": "L", "UniMER-1M_0001984": "{ \\frac{ \\langlez_{2} \\rangle^{(v)}}{ \\langlez \\rangle^{(v)}}}{ \\frac{ \\langlen_{2} \\rangle^{(e)}}{ \\langlen \\rangle^{(e)}}}={ \\frac{ \\langlez(z-1) \\rangle^{(v)}}{ \\langlez \\rangle^{(v)}}}{ \\frac{ \\langlen(n-1) \\rangle^{(e)}}{ \\langlen \\rangle^{(e)}}}>1,", "UniMER-1M_0002012": "G_{q \\ell}= \\mathrm{SU}(3)_{ \\ell} \\otimes \\mathrm{SU}(3)_{q} \\otimes \\mathrm{SU}(2)_{L} \\otimes \\mathrm{U}(1)_{X},", "UniMER-1M_0002028": "h=0", "UniMER-1M_0002013": "b,c,d", "UniMER-1M_0002003": "\\mathnormal{f}=", "UniMER-1M_0002009": "\\delta \\vec{B}=(0,0,B(r) \\mathrm{cos}( \\omegat))", "UniMER-1M_0002008": "F[A, \\theta]=[ \\theta \\par tial^{0}A_{0}- \\frac{1}{ \\theta} \\par tial_{i}A_{i}]", "UniMER-1M_0001991": "\\mathrm{max}( \\Delta \\mathrm{GPE})", "UniMER-1M_0002007": "7-10", "UniMER-1M_0002002": "A", "UniMER-1M_0001979": "P( \\mathrm{~`~`~f~i~r~s~t~b~a~l~l~r~e~d~^{ \\prime}~^{ \\prime}~} \\mid \\mathrm{~`~`~s~e~c~o~n~d~b~a~l~l~b~l~u~e~^{ \\prime}~^{ \\prime}~})", "UniMER-1M_0001948": "\\varphi \\equiv \\intd \\Phi \\left[{ \\frac{3}{4}} \\left({ \\frac{B_{g}^{ \\prime}}{B_{g}}} \\right)^{2}+2{ \\frac{B_{ \\Phi}^{ \\prime}}{B_{g}}}+2{ \\frac{B_{ \\Phi}}{B_{g}}} \\right]^{1/2}", "UniMER-1M_0001975": "v_{ \\mathrm{~d~}}=d_{2}/t_{2}.", "UniMER-1M_0001989": "\\begin{array}{r}{{ \\mathrm{D}_{ \\mathrm{lc}}( \\mathbf{a}, \\mathbf{b}) \\equiv \\sqrt{ \\biggl((a_{0}^{q}a_{0}^{u}-b_{0}^{q}b_{0}^{u})^{2}+ . . .+ \\mathrm{e}^{- \\taud(n)}(a_{n}^{q}a_{n}^{u}-b_{n}^{q}b_{n}^{u})^{2} \\biggr)+ \\biggl((a_{0}^{q}a_{0}^{v}-b_{0}^{q}b_{0}^{v})^{2}+ . . .+ \\mathrm{e}^{- \\taud(n)}(a_{n}^{q}a_{n}^{v}-b_{n}^{q}b_{n}^{v})^{2} \\biggr)}~.}} \\end{array}", "UniMER-1M_0002010": "\\Omega", "UniMER-1M_0002005": "p_{ij}^{t}= \\left\\{ \\begin{array}{ll}{{ \\begin{array}{rlr} \\end{array}}} \\end{array} \\right.", "UniMER-1M_0002033": "q=0.6", "UniMER-1M_0002044": "\\psi_{0}", "UniMER-1M_0001996": "[ \\hat{ \\mathrm{H}}, \\hat{ \\mathrm{K}}]_{-}=-i \\hat{ \\mathrm{P}}+i( \\hat{ \\mathrm{M}}^{-}- \\hat{ \\mathrm{M}}^{+}).", "UniMER-1M_0001988": "\\bar{ \\phi}", "UniMER-1M_0002020": "\\beta", "UniMER-1M_0002048": "V", "UniMER-1M_0001983": "\\begin{array}{rl}{{ \\Psi_{ \\textnormal{ \\tiny{FC2}}}( \\theta)}}&{{= \\frac{ \\omega_{N}}{4 \\pi} \\int_{0}^{2 \\pi} \\int_{0}^{ \\theta_{1}} \\mathrm{log} \\Big(D( \\theta, \\theta^{ \\prime},0, \\varphi^{ \\prime}) \\Big) \\mathrm{sin}( \\theta^{ \\prime})d \\theta^{ \\prime}d \\varphi^{ \\prime}}} \\\\ &{{ \\quad+ \\frac{ \\omega_{C}}{4 \\pi} \\int_{ \\theta_{1}}^{ \\theta_{2}} \\int_{ \\theta_{0}}^{ \\pi} \\mathrm{log} \\Big(D( \\theta, \\theta^{ \\prime},0, \\varphi^{ \\prime}) \\Big) \\mathrm{sin}( \\theta^{ \\prime})d \\theta^{ \\prime}d \\varphi^{ \\prime}}} \\\\ &{{ \\quad+ \\frac{ \\omega_{S}}{4 \\pi} \\int_{ \\theta_{2}}^{ \\pi} \\int_{ \\theta_{0}}^{ \\pi} \\mathrm{log} \\Big(D( \\theta, \\theta^{ \\prime},0, \\varphi^{ \\prime}) \\Big) \\mathrm{sin}( \\theta^{ \\prime})d \\theta^{ \\prime}d \\varphi^{ \\prime}}} \\\\ &{{ \\quad+ \\frac{ \\widetilde{ \\gamma}}{4 \\pi} \\int_{0}^{2 \\pi} \\int_{0}^{ \\pi} \\mathrm{log} \\Big(D( \\theta, \\theta^{ \\prime},0, \\varphi^{ \\prime}) \\Big) \\mathrm{sin}(2 \\theta^{ \\prime})d \\theta^{ \\prime}d \\varphi^{ \\prime}.}} \\end{array}", "UniMER-1M_0002039": "\\begin{array}{rl}{{ \\overline{{{f}}}_{1}}}&{{= \\sum_{i=1}^{N} \\overline{{{ \\alpha}}}_{i} \\psi_{i} \\left( \\boldsymbol{a} \\right),}} \\\\ {{ \\sigma_{f_{1}}^{2}}}&{{= \\overline{{{ \\left( \\sum_{i=1}^{N} \\alpha_{i}^{ \\prime} \\psi_{i} \\left( \\boldsymbol{a} \\right) \\right)^{2}}}}= \\sum_{i=1}^{N} \\overline{{{{ \\alpha_{i}^{ \\prime}}^{2}}}} \\psi_{i}^{2} \\left( \\boldsymbol{a} \\right)+ \\sum_{i \\neqj}^{N(N-1)/2}2 \\overline{{{ \\alpha_{i}^{ \\prime} \\alpha_{j}^{ \\prime}}}} \\psi_{i} \\left( \\boldsymbol{a} \\right) \\psi_{j} \\left( \\boldsymbol{a} \\right),}} \\end{array}", "UniMER-1M_0001965": "\\begin{array}{rlr}&&{{ \\dot{C}_{-1}=i \\omega_{1}C_{-1}+(i+ \\alpha) \\eta_{10}e^{i \\Omegat}C_{0}+ \\left( \\epsilon_{11}|C_{-1}|^{2}+2 \\epsilon_{11}|C_{1}|^{2}+ \\epsilon_{01}|C_{0}|^{2} \\right)C_{-1},}} \\\\ &&{{ \\dot{C}_{0}=i \\omega_{0}C_{0}+(i+ \\alpha) \\eta_{10} \\left(e^{-i \\Omegat}C_{-1}+e^{i \\Omegat}C_{1} \\right)+ \\left( \\epsilon_{0}|C_{0}|^{2}+ \\epsilon_{01}|C_{-1}|^{2}+ \\epsilon_{01}|C_{1}|^{2} \\right)C_{0},}} \\\\ &&{{ \\dot{C}_{1}=i \\omega_{1}C_{1}+(i+ \\alpha) \\eta_{10}e^{-i \\Omegat}C_{0}+ \\left( \\epsilon_{11}|C_{1}|^{2}+2 \\epsilon_{11}|C_{-1}|^{2}+ \\epsilon_{01}|C_{0}|^{2} \\right)C_{1}.}} \\end{array}", "UniMER-1M_0001999": "\\begin{array}{rlr}&{{150 \\frac{ \\epsilon_{k} \\left(1- \\epsilon_{g} \\right) \\mu_{g}}{ \\epsilon_{g}d_{k}^{2}}+1.75 \\frac{ \\epsilon_{k} \\rho_{g}| \\textbf{U}_{g}- \\textbf{u}_{k}|}{d_{k}},}}&{{ \\epsilon_{g} \\le0.8,}} \\\\ &{{ \\frac{3}{4}C_{d} \\left(Re_{s,k} \\right) \\frac{ \\epsilon_{k} \\epsilon_{g} \\rho_{g}}{d_{k}}| \\textbf{U}_{g}- \\textbf{u}_{k}| \\epsilon_{g}^{-2.65},}}&{{ \\epsilon_{g}>0.8,}} \\end{array}", "UniMER-1M_0002032": "(r_{z}^{+},Y^{+})", "UniMER-1M_0002049": "\\begin{array}{rl}{{ \\left\\langle \\mathrm{cos} \\beta \\right\\rangle=}}&{{ \\  \\frac{1}{ \\Omega} \\frac{2k}{ \\sqrt{ \\pi(k+1)}} \\,,}} \\\\ {{ \\left\\langlev \\right\\rangle=}}&{{ \\  \\frac{ \\sqrt{ \\pi}}{2} \\frac{1}{ \\sqrt{k+1}} \\,,}} \\\\ {{ \\left\\langler \\right\\rangle=}}&{{ \\  \\frac{ \\mathrm{Pe}}{ \\Omega(k+1)} \\,,}} \\end{array}", "UniMER-1M_0001993": "N(t)= \\sum_{k=1}^{ \\infty}A_{k} \\cdot \\mathrm{exp} \\left(- \\left( \\frac{ \\mu_{k}}{R} \\right)^{2}Dt \\right) \\mathrm{exp} \\left( \\frac{t}{ \\tau*} \\right)", "UniMER-1M_0002056": "\\tau", "UniMER-1M_0002055": "H= \\int( \\rhoh) \\,dV,", "UniMER-1M_0002034": "d=20.362000+29.530588861 \\timesN+102.026 \\times10^{-12} \\timesN^{2}", "UniMER-1M_0002026": "\\begin{array}{rl}{{ \\mu(L \\cap(-m, \\,m))}}&{{ \\leq \\sum_{q=2}^{ \\infty} \\sum_{p=-mq}^{mq}{ \\frac{2}{q^{n}}}= \\sum_{q=2}^{ \\infty}{ \\frac{2(2mq+1)}{q^{n}}}}} \\end{array}", "UniMER-1M_0002053": "h \\gg \\mathrm{max} J_{ij}", "UniMER-1M_0002050": "\\mathrm{~s~g~n~}( \\tau)/| \\tau|^{2 \\Delta}", "UniMER-1M_0002035": "\\begin{array}{r}{{g^{*}( \\mathbf{X}( \\cdot),p)= \\mathbb{E}_{ \\omega}[ \\phi( \\mathbf{X}( \\omega;p);p)]= \\int \\phi( \\mathbf{X}( \\omega;p);p)d \\mu_{ \\omega}, \\; \\; \\; \\frac{dg^{*}}{dp}= \\mathbb{E}[ \\nabla_{p} \\phi]= \\mathbb{E}[ \\nabla_{p} \\tilde{g}].}} \\end{array}", "UniMER-1M_0002042": "A_{0}^{ \\prime}(y)(4 \\eta(y,t)+ \\sigma(y,t))= \\kappa^{2} \\deltaT_{55}(y,t),", "UniMER-1M_0002045": "\\left[ \\psi_{n}(x), \\psi_{m}(y) \\right]_{ \\pm}=0 \\quad", "UniMER-1M_0002036": "4 \\times10^{5}", "UniMER-1M_0002067": "\\mu_{N}", "UniMER-1M_0002019": "u^{3}+3u( \\theta-1)", "UniMER-1M_0002051": "d=5.597661+29.5305888610 \\timesN+(102.026 \\times10^{-12}) \\timesN^{2}", "UniMER-1M_0002041": "\\begin{array}{r}{{ \\dot{u}_{i}=- \\frac{u_{i}}{ \\tau}+I_{ext}(t)+k \\sum_{j=1}^{N}J_{ij} \\mathrm{tanh}( \\Gamma_{ij}*u_{j}),}} \\\\ {{ \\Gamma_{ij}(t)= \\frac{a_{ij}^{b_{ij}}t^{b_{ij}-1}e^{a_{ij}t}}{(b_{ij}-1)!},}} \\end{array}", "UniMER-1M_0002014": "T_{R}=2 \\pi/(k \\Deltav)", "UniMER-1M_0002062": "{ \\frac{1}{T_{c}}}={ \\frac{1}{T_{i}}}+{ \\frac{d_{i}}{d_{i}-d_{i+1}}} \\left({ \\frac{1}{T_{i+1}}}-{ \\frac{1}{T_{i}}} \\right).", "UniMER-1M_0002004": "\\par tial_{t}n_{i}=|{ \\bf j}_{e}| \\alpha_{ \\mathrm{eff}}(E),", "UniMER-1M_0002040": "{ \\left[ \\begin{array}{l}{{e+i \\ { \\overline{{{e}}}}+j \\ v+k \\ { \\overline{{{v}}}}}} \\\\ {{u_{r}+i \\ { \\overline{{{u}}}}_{ \\mathrm{ \\overline{{{r}}}}}+j \\ d_{ \\mathrm{r}}+k \\ { \\overline{{{d}}}}_{ \\mathrm{ \\overline{{{r}}}}}}} \\\\ {{u_{g}+i \\ { \\overline{{{u}}}}_{ \\mathrm{ \\overline{{{g}}}}}+j \\ d_{ \\mathrm{g}}+k \\ { \\overline{{{d}}}}_{ \\mathrm{ \\overline{{{g}}}}}}} \\\\ {{u_{b}+i \\ { \\overline{{{u}}}}_{ \\mathrm{ \\overline{{{b}}}}}+j \\ d_{ \\mathrm{b}}+k \\ { \\overline{{{d}}}}_{ \\mathrm{ \\overline{{{b}}}}}}} \\end{array} \\right]}_{ \\mathrm{L}}", "UniMER-1M_0001995": "N_{K} \\in \\mathbb{N}", "UniMER-1M_0001998": "\\begin{array}{rlr}&&{{R(q,p)= \\sum_{m=q}^{ \\infty} \\sum_{f=p}^{m}{ \\frac{(m+1-f)!(-1)^{m+f+2}}{(m+2)!^{2}}}{ \\frac{b_{H}^{f}}{h^{2(m+2)}}} \\cdotb_{H}^{(m+5/2-f)/2} \\times}} \\\\ &&{{K_{m+5/2-f}( \\sqrt{4b_{H}}) \\cdot{ \\frac{1}{ \\sqrt{ \\pi}}}.}} \\end{array}", "UniMER-1M_0002047": "\\mathbf{0.0073}", "UniMER-1M_0002078": "\\cdot", "UniMER-1M_0002080": "^{2, \\dag}", "UniMER-1M_0002060": "W= \\int_{t_{1}}^{t_{2}} \\mathbf{F} \\cdot \\mathbf{v}dt= \\int_{t_{1}}^{t_{2}}F \\,vdt= \\int_{t_{1}}^{t_{2}}ma \\,vdt=m \\int_{t_{1}}^{t_{2}}v \\,{ \\frac{dv}{dt}} \\,dt=m \\int_{v_{1}}^{v_{2}}v \\,dv={ \\frac{1}{2}}m(v_{2}^{2}-v_{1}^{2}).", "UniMER-1M_0002071": "\\widehat{V}_{ \\mathrm{~s~i~g~n~a~l~}}", "UniMER-1M_0002066": "\\begin{array}{r}{{a_{ \\mathrm{eff}} \\sim \\frac{32 \\pi \\,m_{ \\mathrm{nucleon}}}{g_{ \\piNN}^{2}m_{ \\pi}^{2}} \\sim5 \\, \\mathrm{fm} \\,,}} \\end{array}", "UniMER-1M_0002063": "={ \\frac{ \\alpha^{2}}{2Mq^{4}}}~{ \\frac{E^{ \\prime}}{E}}~4L_{ \\mu \\nu}^{(A)}~W^{ \\mu \\nu(A)} \\,.", "UniMER-1M_0002070": "\\vartheta \\le0", "UniMER-1M_0002064": "8", "UniMER-1M_0002069": "ds^{2}=2dx^{+}dx^{-}+ \\left(m_{x}^{2}(x^{+})x_{a}^{2}+m_{y}^{2}(x^{+})y_{l}^{2}+m_{z}^{2}(x^{+})z^{2} \\right)(dx^{+})^{2}+dx_{a}^{2}+dy_{l}^{2}+dz^{2}.", "UniMER-1M_0002046": "\\begin{array}{rl}{{L_{ \\delta}(t)}}&{{=- \\frac{1}{2} \\left( \\frac{ \\sigma^{2}}{4}-a \\right) \\int_{0}^{ \\infty}y^{ \\frac{4a}{ \\sigma^{2}}-2} \\left( \\ell(t,y)- \\ell(t,0) \\right)dy}} \\\\ &{{= \\frac{ \\delta}{2} \\int_{0}^{ \\infty}y^{ \\frac{4 \\delta}{ \\sigma^{2}}-1} \\left( \\ell(t,y)- \\ell(t,0) \\right)dy,}} \\end{array}", "UniMER-1M_0002054": "ECF", "UniMER-1M_0002043": "{ \\begin{array}{rl}{{P_{i,i-1}}}&{{={ \\frac{g_{i}(N-i)}{f_{i} \\cdoti+g_{i}(N-i)}} \\cdot{ \\frac{i}{N}}}} \\\\ {{P_{i,i}}}&{{=1-P_{i,i-1}-P_{i,i+1}}} \\\\ {{P_{i,i+1}}}&{{={ \\frac{f_{i} \\cdoti}{f_{i} \\cdoti+g_{i}(N-i)}} \\cdot{ \\frac{N-i}{N}}}} \\end{array}}", "UniMER-1M_0002089": "d_{0}:=d(A_{t},A_{t^{ \\prime}})", "UniMER-1M_0002058": "\\boxed{t_{ \\perp}= \\frac{E_{s,T}}{E_{s,i}}= \\cfrac{2n_{i} \\mathrm{cos}( \\theta_{i})}{n_{i} \\mathrm{cos}( \\theta_{i})+n_{T} \\mathrm{cos}( \\theta_{T})}}", "UniMER-1M_0002059": "\\xi \\sim \\frac{1}{ \\lambda^{3/7}} \\left({ \\frac{m_{P}}{T_{c}}} \\right)^{2/7}{ \\frac{1}{T_{c}}}~.", "UniMER-1M_0002031": "3.5 \\", "UniMER-1M_0002038": "{ \\tilde{D}}=NDN,{ \\tilde{ \\bf u}}=N^{-1}{ \\bf u}", "UniMER-1M_0002094": "\\delta=k_{P}^{0}L+( \\omega_{S}L_{S}+ \\omega_{I}L_{I})/c", "UniMER-1M_0002073": "\\delta^{ \\mathrm{st}} \\lambda_{*}(E; \\{p \\})", "UniMER-1M_0002030": "\\begin{array}{r}{{ \\xi_{N, \\delta}^{n}= \\xi_{N, \\delta}^{n-1}+ \\delta[ \\nu \\Delta \\xi_{N, \\delta}^{n}- \\Pi_{N}( \\mathbf{u}_{N, \\delta}^{n-1} \\cdot \\nabla \\xi_{N, \\delta}^{n})]+ \\sqrt{ \\delta} \\sum_{k=1}^{d} \\Pi_{N} \\sigma_{k} \\eta_{n}^{k}, \\quad \\mathrm{~for~} n \\geq1,}} \\end{array}", "UniMER-1M_0002081": "10.1 \\", "UniMER-1M_0002061": "\\begin{array}{rl}{{ \\left[ \\frac{i}{ \\omega_{L}} \\frac{ \\par tial}{ \\par tial \\tau}+ \\frac{c^{2}}{2 \\omega_{L}^{2}} \\Delta_{ \\perp} \\right]a}}&{{= \\frac{ \\omega_{p}^{2}}{2 \\omega_{L}^{2}n_{0}} \\left[ \\deltan_{0}(r)+ \\deltan(r, \\xi;|a_{s}|^{2}) \\right]a}} \\end{array}", "UniMER-1M_0002102": "^4", "UniMER-1M_0002082": "{ \\overline{{{ \\mathbf{x}}}}}_{1}, . . .,{ \\overline{{{ \\mathbf{x}}}}}_{N} \\in \\Omega,", "UniMER-1M_0002087": "\\begin{array}{r}{{L= \\frac{1}{2} \\omega_{ \\alpha \\beta}z^{ \\alpha} \\dot{z}^{ \\beta}-H-S_{ \\alpha}{z}^{ \\alpha} \\,,}} \\end{array}", "UniMER-1M_0002052": "\\epsilon(q, \\omega)=1-V_{q}( \\chi_{ \\up arrow \\up arrow}^{0}(q, \\omega)+ \\chi_{ \\downarrow \\downarrow}^{0}(q, \\omega))", "UniMER-1M_0002068": "p_{ij}^{ \\left[ \\beta \\right]}=w_{ji}^{ \\left[ \\beta \\right]}/ \\sum_{ \\ell=1}^{N}w_{ \\elli}^{ \\left[ \\beta \\right]}", "UniMER-1M_0002086": "d_{2}", "UniMER-1M_0002072": "\\widetilde{Re}_{opt}= \\frac{3 \\sqrt{10}}{2} \\pi,", "UniMER-1M_0002088": "\\Delta \\beta=0", "UniMER-1M_0002093": "x", "UniMER-1M_0002057": "c={(4 \\pi)}^{4/3} \\int_{0}^{ \\infty}du \\sum_{l=0}^{ \\infty} \\Big( \\frac{u^{3l}{(-1)}^{l}l!}{32 \\pi^{2}(4l+2)!}- \\frac{u^{3l+3/2}{(-1)}^{l}(2l+1)!!}{32 \\pi^{3/2}2^{l}(4l+4)!} \\Big)", "UniMER-1M_0002095": "i=1", "UniMER-1M_0002090": "n", "UniMER-1M_0002098": "\\hat{ \\rho}_{ \\bf k}^{ \\eta}", "UniMER-1M_0002117": "m_{I,n}", "UniMER-1M_0002116": "\\tilde{ \\mu}", "UniMER-1M_0002099": "\\varepsilon_{p}^{std} \\approxK_{ \\mathrm{eff}}N^{2}", "UniMER-1M_0002101": "\\begin{array}{rl}{{T}}&{{= \\sum_{pq}t_{pq}a_{p}^{ \\dagger}( \\beta, \\mu) \\tilde{a}_{q}^{ \\dagger}( \\beta, \\mu)}} \\\\ &{{+ \\frac{1}{4} \\sum_{pqrs}t_{pqrs}a_{p}^{ \\dagger}( \\beta, \\mu)a_{q}^{ \\dagger}( \\beta, \\mu) \\tilde{a}_{s}^{ \\dagger}( \\beta, \\mu) \\tilde{a}_{r}^{ \\dagger}( \\beta, \\mu).}} \\end{array}", "UniMER-1M_0002079": "\\Deltax_{ \\mathrm{base}}/ \\Deltax_{ \\mathrm{fine}}=4", "UniMER-1M_0002122": "\\eta_{x}^{ \\mathrm{~p~h~y~s~}}", "UniMER-1M_0002025": "H_{ \\frac{1}{12}}=12-3 \\left( \\mathrm{ln}{2}+{ \\frac{ \\mathrm{ln}{3}}{2}} \\right)- \\pi \\left(1+{ \\frac{ \\sqrt{3}}{2}} \\right)+2{ \\sqrt{3}} \\mathrm{ln} \\left({ \\sqrt{2-{ \\sqrt{3}}}} \\right)", "UniMER-1M_0002124": "^{2}", "UniMER-1M_0002029": "\\begin{array}{r}{{h_{x}(k_{y},k_{y})=k_{x}^{2}-k_{y}^{2},}} \\end{array}", "UniMER-1M_0002106": "D \\rightarrowA", "UniMER-1M_0002100": "\\mathbf{E}_{ \\mathbf{a}}( \\mathbf{b})=k^{3}e^{ \\mathrm{i} kr} \\{[ \\mathbf{d}_{ \\mathbf{a}}- \\hat{ \\mathbf{r}}( \\hat{ \\mathbf{r}} \\cdot \\mathbf{d}_{ \\mathbf{a}})]/(kr)+[3 \\hat{ \\mathbf{r}}( \\hat{ \\mathbf{r}} \\cdot \\mathbf{d}_{ \\mathbf{a}})- \\mathbf{d}_{ \\mathbf{a}}][1/(kr)^{3}- \\mathrm{i}/(kr)^{2}] \\}/4 \\pi \\epsilon_{0}", "UniMER-1M_0002126": "\\lambda", "UniMER-1M_0002111": "\\mathrm{sin}^{2}2 \\theta_{ \\mathrm{LSND}} \\equiv \\frac{1}{2}s_{25}^{4} \\stackrel{<}{ \\sim}1.3 \\times10^{-3}", "UniMER-1M_0002084": "\\begin{array}{rlrlrl}{{ \\textnormal{maximize}}}&&{{ \\sum_{i \\inI} \\sum_{j \\inJ} \\overline{{{c}}}_{ij}x_{ij}}} \\\\ {{ \\textnormal{subjectto}}}&&{{ \\sum_{j \\inJ}x_{ij}}}&{{ \\le \\overline{{{s}}}_{i},}}&&{{ \\foralli \\inI,}} \\\\ &&{{ \\underline{{{d}}}_{j} \\le \\sum_{i \\inI}x_{ij}}}&{{ \\le \\overline{{{d}}}_{j},}}&&{{ \\forallj \\inJ,}} \\\\ &&{{x_{ij}}}&{{ \\ge0,}}&&{{ \\foralli \\inI,j \\inJ,}} \\\\ &&{{u_{i}+v_{j}}}&{{ \\le \\overline{{{c}}}_{ij},}}&&{{ \\foralli \\inI,j \\inJ,}} \\\\ &&{{u_{i}}}&{{ \\le0,}}&&{{ \\foralli \\inI,}} \\\\ &&{{x_{ij}>0 \\ }}&{{ \\Rightarrow \\ u_{i}+v_{j}= \\overline{{{c}}}_{ij},}}&&{{ \\foralli \\inI,j \\inJ,}} \\\\ &&{{ \\sum_{j \\inJ}x_{ij}< \\underline{{{s}}}_{i} \\ }}&{{ \\Rightarrow \\ u_{i}=0,}}&&{{ \\foralli \\inI,}} \\end{array}", "UniMER-1M_0002109": "w_{j}", "UniMER-1M_0002130": "\\psi", "UniMER-1M_0002075": "\\begin{array}{rl}&{{{ \\hat{ \\bf E}}(z,t)={ \\bf E}_{c}(z,t)+{ \\hat{ \\bf E}}_{p}(z,t),}} \\\\ &{{{ \\bf E}_{c}(z,t)={ \\bf e}_{c}{ \\calE}_{c}e^{i(k_{c}z- \\omega_{c}t)}+ \\mathrm{c.c.},}} \\\\ &{{{ \\hat{ \\bf E}}_{p}(z,t)={ \\hat{ \\bf E}}_{p1}(z,t)+{ \\hat{ \\bf E}}_{p2}(z,t),}} \\\\ &{{{ \\hat{ \\bf E}}_{pj}(z,t)={ \\bf e}_{pj}{ \\calE}_{p0}{ \\hat{E}}_{pj}(z,t)e^{i(k_{p}z- \\omega_{p}t)}+ \\mathrm{H.c.},}} \\end{array}", "UniMER-1M_0002085": "u= \\par tial_{x}f \\par tial_{y}- \\par tial_{y}f \\par tial_{x}", "UniMER-1M_0002092": "\\begin{array}{rl}{{ \\vec{E}_{a}^{1}(x=0,t)}}&{{= \\Re \\left[-tre^{ik_{a}L} \\left( \\vec{X}_{a}e^{-i( \\omega_{a}t-k_{a} \\frac{L}{2}+ \\phi)}+ \\frac{ \\Delta \\vec{X}_{a}}{2} \\left(e^{-i( \\omega_{+}t-k_{+} \\frac{L}{2}+ \\phi_{+})}+e^{-i( \\omega_{-}t-k_{-} \\frac{L}{2}+ \\phi_{-})} \\right) \\right) \\right] \\,,}} \\end{array}", "UniMER-1M_0002097": "\\begin{array}{rlr}{{E_{f}}}&{{ \\approx}}&{{ \\frac{ \\piL \\, \\epsilon_{f}^{DT}}{m_{p} \\, \\rho_{p}} \\, \\frac{ \\left( \\rhoR \\right)^{3}}{H_{DT} \\left(kT_{i},u^{s} \\right)+ \\rhoR}}} \\\\ {{E_{i}}}&{{ \\approx}}&{{ \\frac{3 \\piL \\,kT_{i}}{2 \\rho_{p}^{2}} \\, \\left(n_{p}+n_{B}+n_{D}+n_{T} \\right) \\, \\left( \\rhoR \\right)^{2} \\,,}} \\end{array}", "UniMER-1M_0002110": "\\pm{ \\frac{1}{ \\sqrt{1- \\mathrm{cos}^{2} \\theta}}}", "UniMER-1M_0002114": "a_{n}= \\sum_{m}G_{m,n} \\left(f(x_{m-1})-v_{m} \\right) \\Deltat", "UniMER-1M_0002104": "\\mathcal{B}_{ \\ell}= \\left\\{ \\begin{array}{ll}{{ \\mathcal{Y}_{0} \\oplus \\mathcal{Y}_{2} \\oplus . . . \\oplus \\mathcal{Y}_{2d} \\,,}}&{{ \\mathrm{~if~}2d= \\ell}} \\\\ {{ \\mathcal{Y}_{1} \\oplus \\mathcal{Y}_{3} \\oplus . . . \\oplus \\mathcal{Y}_{2d-1} \\,,}}&{{ \\mathrm{~if~}2d-1= \\ell \\,.}} \\end{array} \\right.", "UniMER-1M_0002120": "{ \\bf x}_{1}(t)", "UniMER-1M_0002076": "\\beta_{L}= \\sqrt{ \\kappa_{P}^{2}- \\frac{ \\omega_{P}^{2}}{c_{L}^{2}}}, \\quad \\beta_{T}= \\sqrt{ \\kappa_{P}^{2}- \\frac{ \\omega_{P}^{2}}{c_{T}^{2}}}.", "UniMER-1M_0002143": "m", "UniMER-1M_0002127": "\\rho_{ \\mathrm{V}}(s)- \\rho_{ \\mathrm{A}}(s)={ \\frac{C_{ \\mathrm{BW}}}{s^{3}}}+{ \\calO}(s^{4})", "UniMER-1M_0002134": "a(= \\infty){ \\frac{a}{ \\mathrm{ln} a}}", "UniMER-1M_0002091": "{ \\bf U}={ \\bf U}_{0}+ \\delta \\hat{U}e^{i(Z-CT)}", "UniMER-1M_0002113": "\\tau_{ \\mathrm{eff}} \\simeq(115, \\,31, \\,55) \\, \\gamma_{ \\mathrm{eff}}^{-1}", "UniMER-1M_0002149": "\\begin{array}{r}{{S= \\frac{q_{v}p}{p_{si}} \\,.}} \\end{array}", "UniMER-1M_0002135": "S(0,d)A_{0}^{(0)}=B_{0}^{(0)}", "UniMER-1M_0002105": "^{10}", "UniMER-1M_0002129": "B_{0}", "UniMER-1M_0002136": "v_{p}", "UniMER-1M_0002140": "10^{6}) \\times \\rho_{ \\mathrm{~D~M~}}(R_{0})", "UniMER-1M_0002125": "K_{TRUE}(r,s)=lim_{T \\rightarrow+ \\infty} \\frac{ \\frac{1}{T} \\int_{t_{i}}^{t_{i}+T}v(r,t)v(r+s,t) \\mathop \\! \\mathrm{d} t}{ \\sqrt{ \\frac{1}{T} \\int_{t_{i}}^{t_{i}+T}v^{2}(r,t) \\mathop \\! \\mathrm{d} t} \\sqrt{ \\frac{1}{T} \\int_{t_{i}}^{t_{i}+T}v^{2}(r+s,t) \\mathop \\! \\mathrm{d} t}}.", "UniMER-1M_0002132": "\\mathbb{ \\Lambda}^{1} \\in \\mathbb{R}^{N_{1} \\times3}", "UniMER-1M_0002139": "\\nabla^{2}T^{out}=0; \\qquad \\qquad \\nabla^{2}T^{in}=0;", "UniMER-1M_0002146": "\\begin{array}{rlr}{{s_{ \\sigmav,{ \\calE}}}}&{{=}}&{{-k_{B} \\intf_{ \\sigma} \\mathrm{ln} \\left( \\frac{f_{ \\sigmaM} \\Delta^{3}v_{ \\sigma}}{n_{ \\sigma}} \\right)d^{3}v,}} \\\\ {{s_{ \\sigmav, \\mathrm{rel}}}}&{{=}}&{{-k_{B} \\intf_{ \\sigma} \\mathrm{ln} \\left( \\frac{f_{ \\sigma}}{f_{ \\sigmaM}} \\right)d^{3}v,}} \\end{array}", "UniMER-1M_0002145": "\\begin{array}{rlr}{{e(F_{d,q},A_{d,N})}}&{{ \\ge}}&{{ \\frac{1}{ \\|h_{d} \\|_{d,q}} \\sum_{ \\mathfrak{u} \\subseteq[d]} \\alpha_{1}^{d-| \\mathfrak{u}|} \\alpha_{2}^{| \\mathfrak{u}|} \\left(1- \\frac{N}{2^{| \\mathfrak{u}|}} \\right)_{+}}} \\\\ &{{=}}&{{ \\frac{1}{ \\|h_{d} \\|_{d,q}} \\sum_{k=0}^{d}{ \\binom{d}{k}} \\alpha_{1}^{d-k} \\alpha_{2}^{k} \\left(1- \\frac{N}{2^{k}} \\right)_{+}}} \\\\ &{{=}}&{{ \\frac{1}{ \\|h_{d} \\|_{d,q}} \\alpha_{1}^{d} \\sum_{k=0}^{d}{ \\binom{d}{k}} \\alpha_{3}^{k} \\left(1- \\frac{N}{2^{k}} \\right)_{+},}} \\end{array}", "UniMER-1M_0002152": "M=N/3", "UniMER-1M_0002150": "\\sigma_{j}", "UniMER-1M_0002115": "\\big| \\chi_{ \\mathrm{eff}}^{(2,3)} \\big|= \\big| \\chi^{(2,3)} \\big|/h", "UniMER-1M_0002118": "y_{ \\mathrm{CM}}={ \\frac{y_{ \\mathrm{P}}}{2}}-{ \\frac{1}{2}} \\mathrm{ln}{ \\frac{A_{ \\mathrm{T}}}{A_{ \\mathrm{P}}}} \\,.", "UniMER-1M_0002123": "\\rho(p)( \\Omega)= \\Omega+2t \\omega+t^{2} \\bar{ \\Omega}.", "UniMER-1M_0002119": "\\left[4,5,6,7,8,9,10,11,12,13,19,25 \\right]", "UniMER-1M_0002096": "\\langle{ \\bf u},{ \\bf w} \\rangle_{ \\Gamma}= \\int_{ \\Gamma}{ \\bf u} \\cdot{ \\bf w} \\, \\mathrm{~d~} \\Gamma", "UniMER-1M_0002154": "\\mathbf{I}", "UniMER-1M_0002155": "{ \\boldsymbol{ \\gamma}}^{ \\prime}(s)= \\left(- \\mathrm{sin}{ \\frac{s}{r}}, \\mathrm{cos}{ \\frac{s}{r}} \\right)", "UniMER-1M_0002141": "\\begin{array}{rl}&{{ \\widehat{ \\nu_{e^{-} \\gamma} \\nu_{ \\gammae}}f_{RREA}=K_{e^{-} \\gamma, \\gammae} \\left(e^{ \\frac{L( \\lambda_{x}- \\lambda_{RREA})}{ \\lambda_{x} \\lambda_{RREA}}}-1- \\right.}} \\\\ &{{ \\left.- \\frac{L( \\lambda_{x}- \\lambda_{RREA})}{ \\lambda_{x} \\lambda_{RREA}} \\right)f_{RREA},}} \\end{array}", "UniMER-1M_0002156": "\\boldsymbol{q}^{k}={(2 \\pi)^{d}}V^{-1}(q_{1}^{k}, . . .,q_{d}^{k})", "UniMER-1M_0002158": "Y(v_{ \\lambda},z)=e_{ \\lambda}: \\mathrm{exp} \\int \\lambda(z):=e_{ \\lambda}z^{ \\lambda} \\mathrm{exp} \\left( \\sum_{n<0} \\lambda_{n}{ \\frac{z^{-n}}{n}} \\right) \\mathrm{exp} \\left( \\sum_{n>0} \\lambda_{n}{ \\frac{z^{-n}}{n}} \\right),", "UniMER-1M_0002121": "\\begin{array}{rl}{{{ \\sf H}(k)}}&{{= \\left[ \\begin{array}{cccc}{{0}}&{{- \\sigma_{z}}}&{{0}}&{{{ \\sf h}(k)}} \\\\ {{- \\sigma_{z}}}&{{0}}&{{- \\sigma_{z}}}&{{0}} \\\\ {{0}}&{{- \\sigma_{z}}}&{{0}}&{{- \\sigma_{z}}} \\\\ {{{ \\sf h}^{*}(k)}}&{{0}}&{{- \\sigma_{z}}}&{{0}} \\end{array} \\right],}} \\end{array}", "UniMER-1M_0002108": "l=0, . . .,L^{ \\prime}", "UniMER-1M_0002176": "\\sigma_{s}=-0.05,-0.25", "UniMER-1M_0002153": "\\begin{array}{r}{{P(x_{i},y_{j})=p_{i} \\delta_{ij}, \\quad i,j=1,...,n,}} \\end{array}", "UniMER-1M_0002160": "\\omega", "UniMER-1M_0002142": "\\mathbf{M}= \\left|{ \\begin{array}{lll}{{ \\mathbf{e}_{x}}}&{{ \\mathbf{e}_{y}}}&{{ \\mathbf{e}_{z}}} \\\\ {{x_{A}-x}}&{{0}}&{{0}} \\\\ {{0}}&{{-F}}&{{0}} \\end{array}} \\right|+ \\left|{ \\begin{array}{lll}{{ \\mathbf{e}_{x}}}&{{ \\mathbf{e}_{y}}}&{{ \\mathbf{e}_{z}}} \\\\ {{-x}}&{{0}}&{{0}} \\\\ {{0}}&{{R_{0}}}&{{0}} \\end{array}} \\right|=F(x-x_{A}) \\, \\mathbf{e}_{z}-R_{0}x \\, \\mathbf{e}_{z}=-{ \\frac{Fx_{A}}{L}}(L-x) \\, \\mathbf{e}_{z} \\,.", "UniMER-1M_0002138": "\\left\\{ \\begin{array}{rl}{{ \\Deltat_{ad}}}&{{=0.25 \\frac{h}{ \\left| \\mathbf{v} \\right|_{max}}}} \\\\ {{ \\Deltat_{ac}}}&{{=0.6 \\left( \\frac{h}{c+ \\left| \\mathbf{v} \\right|_{max}} \\right),}} \\end{array} \\right.", "UniMER-1M_0002163": "N_{p}", "UniMER-1M_0002137": "L \\cap(-m,m) \\subseteq \\bigcup_{q=2}^{ \\infty}V_{n,q} \\cap(-m,m) \\subseteq \\bigcup_{q=2}^{ \\infty} \\bigcup_{p=-mq}^{mq} \\left({ \\frac{p}{q}}-{ \\frac{1}{q^{n}}},{ \\frac{p}{q}}+{ \\frac{1}{q^{n}}} \\right).", "UniMER-1M_0002183": "^1", "UniMER-1M_0002162": "\\gamma=1", "UniMER-1M_0002165": "\\sim500", "UniMER-1M_0002187": "0.36", "UniMER-1M_0002167": "t=1- \\frac{ \\mathbf{d} \\cdot \\mathbf{E}_{f}^{*} \\, \\, \\mathbf{d}^{*} \\cdot \\mathbf{E}_{f}}{D} \\,,", "UniMER-1M_0002131": "\\begin{array}{rl}&{{v_{4k-2i,2k-1+6i}^{A,i} \\otimesv_{1,1}}} \\\\ &{{+v_{4k-2i,2k-3+6i}^{A,i} \\otimesv_{1,3}}} \\\\ &{{+v_{4k-2i,2k-5+6i}^{A,i} \\otimesv_{1,5}}} \\\\ &{{+v_{4k-2i,2k-1+6i}^{B,i} \\otimesv_{1,1}}} \\end{array}", "UniMER-1M_0002190": "\\alpha_{s}", "UniMER-1M_0002133": "\\begin{array}{r}{{R_{L_{ \\mathrm{ts}}}^{N}(L, \\tau; \\tau_{0})= \\frac{R_{L_{ \\mathrm{ts}}}(L, \\tau; \\tau_{0})}{ \\int_{L_{ \\mathrm{ts}}}^{ \\infty}dL \\;R_{L_{ \\mathrm{ts}}}(L, \\tau; \\tau_{0})}}} \\end{array}", "UniMER-1M_0002169": "\\left[ \\begin{array}{l}{{(S_{0}-S_{B})- \\deltaS_{0}}} \\\\ {{(S_{0}-S_{B})+ \\epsilonS_{0}}} \\end{array} \\right]=( \\epsilon- \\delta) \\left[ \\begin{array}{l}{{A}} \\\\ {{-B}} \\end{array} \\right]- \\left[ \\begin{array}{l}{{ \\deltaC}} \\\\ {{ \\epsilonC}} \\end{array} \\right]+ \\left[ \\begin{array}{l}{{(1- \\delta) \\eta_{0}- \\eta_{B}}} \\\\ {{(1- \\epsilon) \\eta_{0}- \\eta_{B}}} \\end{array} \\right].", "UniMER-1M_0002103": "\\eta=-1.75 \\cdot10^{-2} \\left( \\frac{ \\par tial \\mathrm{ln} \\alpha_{em}}{ \\par tialz} \\right)_{|z=0}^{2} \\frac{ \\DeltaR_{Z}}{ \\Omega_{h}^{(0)}(1+w_{h}^{(0)})}", "UniMER-1M_0002194": "\\mathbf{k}", "UniMER-1M_0002171": "p_{ \\|}= \\varphi^{ \\prime}/B+r^{2} \\vartheta^{ \\prime}/(q \\beta)", "UniMER-1M_0002170": "\\chi_{e}= \\frac{ \\gammaE}{E_{c}r}=0.3 \\left( \\frac{E}{500~ \\mathrm{MeV}} \\right) \\left( \\frac{I}{10^{22}~ \\mathrm{W} \\mathrm{cm}^{-2}} \\right)^{1/2},", "UniMER-1M_0002077": "\\begin{array}{rlr}&&{{ \\frac{1}{ \\tau_{n \\mathbf{k} \\tom \\mathbf{k}+ \\mathbf{q}}^{ \\mathrm{ph}}}= \\frac{1}{N_{ \\mathrm{uc}}} \\sum_{ \\nu} \\frac{2 \\pi}{ \\hbar} \\left|g_{mn \\nu}( \\mathbf{k}, \\mathbf{q}) \\right|^{2}}} \\\\ &&{{ \\times \\big[(n_{ \\mathbf{q} \\nu}+1-f_{m \\mathbf{k}+ \\mathbf{q}}^{0}) \\delta( \\epsilon_{n \\mathbf{k}} \\!- \\! \\epsilon_{m \\mathbf{k}+ \\mathbf{q}}- \\hbar \\omega_{ \\mathbf{q} \\nu})}} \\\\ &&{{+(n_{ \\mathbf{q} \\nu}+f_{m \\mathbf{k}+ \\mathbf{q}}^{0}) \\delta( \\epsilon_{n \\mathbf{k}} \\!- \\! \\epsilon_{m \\mathbf{k}+ \\mathbf{q}}+ \\hbar \\omega_{ \\mathbf{q} \\nu}) \\big],}} \\end{array}", "UniMER-1M_0002172": "D_{ \\mu} \\langlej_{5 \\mu}^{a, \\mathrm{reg}} \\rangle_{c}=2 \\mathrm{i} m \\langle \\psi_{ \\varepsilon}^{+}T^{a} \\gamma_{5} \\psi_{ \\varepsilon} \\rangle_{c}+{ \\calA}_{5}^{a},", "UniMER-1M_0002173": "T_{[ \\mu \\nu]}^{ \\ a}= \\par tial_{ \\mu}E_{ \\nu}^{ \\underline{{{{a}}}}}- \\par tial_{ \\nu}E_{ \\mu}^{ \\underline{{{{a}}}}}", "UniMER-1M_0002147": "\\psi_{n}= \\sum_{j} \\left( \\beta_{j} \\right)^{n} \\phi^{ \\left(j \\right)},", "UniMER-1M_0002164": "\\begin{array}{rl}&{{ \\mathrm{min}_{n(x,y)} \\lVert \\mathbf{T}- \\mathbf{T}_{ \\mathrm{targ}} \\rVert_{F}+ \\lVert \\mathbf{R}- \\mathbf{0} \\rVert_{F}}} \\\\ &{{ \\mathrm{s.t.} \\  \\ n_{ \\mathrm{eff,~150nm}} \\leqn(x,y) \\leqn_{ \\mathrm{eff,~220nm}}}} \\end{array}", "UniMER-1M_0002202": "N_{c} \\leq18", "UniMER-1M_0002177": "{ \\hat{y}}_{d}", "UniMER-1M_0002161": "\\delta{ \\calL}= \\frac{d}{dt} \\,(p_{i} \\, \\deltax_{i}+ \\tilde{p}_{i} \\, \\delta \\dot{x}_{i}).", "UniMER-1M_0002205": "N", "UniMER-1M_0002157": "u(x,z,t)=U(x,z) \\mathrm{exp}(i \\omegat)", "UniMER-1M_0002180": "\\begin{array}{r}{{ \\begin{array}{rl}{{ \\frac{ \\par tialu_{ \\phi}^{(k)}}{ \\par tialt}}}&{{+ \\frac{u_{ \\xi}^{(k)}}{H_{ \\xi}} \\frac{ \\par tialu_{ \\phi}^{(k)}}{ \\par tial \\xi}+ \\frac{u_{ \\phi}^{(k)}}{H_{ \\phi}} \\frac{ \\par tialu_{ \\phi}^{(k)}}{ \\par tial \\phi}+u_{z}^{(k)} \\frac{ \\par tialu_{ \\phi}^{(k)}}{ \\par tialz}-2 \\frac{ \\mathrm{sinh} \\xi}{ \\mathrm{sin} \\phi_{0}}u_{ \\xi}^{(k)}u_{ \\phi}^{(k)}+2 \\frac{ \\mathrm{sin} \\phi}{ \\mathrm{sin} \\phi_{0}} \\left(u_{ \\xi}^{(k)} \\right)^{2}}} \\\\ {{=}}&{{- \\frac{1}{ \\rho_{k}} \\frac{1}{H_{ \\phi}} \\frac{ \\par tialp^{(k)}}{ \\par tial \\phi}+ \\frac{1}{ \\mathrm{Re}} \\frac{ \\rho_{1}}{ \\rho_{12} \\rho_{k}} \\frac{ \\mu_{12} \\mu_{k}}{ \\mu_{1}} \\Biggl[ \\frac{1}{H_{ \\xi}H_{ \\phi}} \\frac{ \\par tial}{ \\par tial \\xi} \\biggl( \\frac{H_{ \\phi}}{H_{ \\xi}} \\frac{ \\par tialu_{ \\phi}^{(k)}}{ \\par tial \\xi} \\biggr)}} \\\\ &{{+ \\frac{1}{H_{ \\xi}H_{ \\phi}} \\frac{ \\par tial}{ \\par tial \\phi} \\biggl( \\frac{H_{ \\xi}}{H_{ \\phi}} \\frac{ \\par tialu_{ \\phi}^{(k)}}{ \\par tial \\phi} \\biggr)+ \\frac{ \\par tial^{2}u_{ \\phi}^{(k)}}{ \\par tialz^{2}}+4 \\frac{ \\mathrm{sin} \\phi}{ \\mathrm{sin} \\phi_{0}} \\frac{1}{H_{ \\xi}} \\frac{ \\par tialu_{ \\xi}^{(k)}}{ \\par tial \\xi}-4 \\frac{ \\mathrm{sinh} \\xi}{ \\mathrm{sin} \\phi_{0}} \\frac{1}{H_{ \\phi}} \\frac{ \\par tialu_{ \\xi}^{(k)}}{ \\par tial \\phi}}} \\\\ &{{+4 \\frac{ \\mathrm{cos}^{2} \\phi- \\mathrm{cosh}^{2} \\xi}{ \\mathrm{sin}^{2} \\phi_{0}}u_{ \\phi}^{(k)} \\Biggr]+ \\frac{1}{ \\mathrm{Fr}} \\frac{ \\bigl(1- \\mathrm{cosh} \\xi \\mathrm{cos} \\phi \\bigr)}{ \\bigl( \\mathrm{cosh} \\xi- \\mathrm{cos} \\phi \\bigr)},}} \\end{array}}} \\end{array}", "UniMER-1M_0002188": "\\mu", "UniMER-1M_0002191": "sin( \\theta/2)=L_{cell}/(4*f)", "UniMER-1M_0002159": "{ \\vec{v}}(t)={ \\frac{d}{dt}}{ \\vec{r}}(t)=R{ \\frac{d{ \\hat{u}}_{R}}{dt}}=R{ \\frac{d \\theta}{dt}}{ \\hat{u}}_{ \\theta}(t)=R \\omega{ \\hat{u}}_{ \\theta}(t) \\ .", "UniMER-1M_0002211": "\\theta", "UniMER-1M_0002189": "\\tau", "UniMER-1M_0002166": "\\zeta= \\langle \\sqrt{ \\tau_{0}} \\alpha| \\sqrt{ \\tau_{1}} \\alpha \\rangle=e^{- \\frac{ \\mu}{2}( \\sqrt{ \\tau_{0}}- \\sqrt{ \\tau_{1}})^{2}}", "UniMER-1M_0002192": "S_{0}", "UniMER-1M_0002196": "\\# \\mathrm{~O~P~}_{0}^{ \\mathrm{~s~/~o~}}", "UniMER-1M_0002195": "p_{ \\Delta}", "UniMER-1M_0002185": "k", "UniMER-1M_0002203": "G", "UniMER-1M_0002179": "A_{1}(z)= \\sum_{n,m \\geq0}A_{nm}^{1}(z)b_{n} \\cdot \\overline{{{b}}}_{m}", "UniMER-1M_0002204": "t_{0}", "UniMER-1M_0002206": "a_{k}", "UniMER-1M_0002201": "\\mathbf{v}_{ \\alpha}= \\mathrm{const}", "UniMER-1M_0002184": "dg-da=dc-db, \\ dc=2dg.", "UniMER-1M_0002168": "\\begin{array}{r}{{ \\left(e^{n}-e^{ \\astn-1},e^{n} \\right)+ \\Deltat \\sum_{K}( \\varepsilon_{K}(c_{h}^{n}) \\nablae^{n}, \\nablae^{n})_{K}= \\left(e^{n}-e^{ \\astn-1}, \\rho^{n} \\right)}} \\\\ {{+ \\Deltat \\sum_{K}( \\varepsilon_{K}(c_{h}^{n}) \\nablae^{n}, \\nabla \\rho^{n})_{K}}} \\\\ {{+ \\Deltat \\sum_{K}( \\varepsilon_{K}(c_{h}^{n}) \\nablae^{n}, \\nablac^{n})_{K}}} \\\\ {{- \\Deltat \\sum_{K}( \\varepsilon_{K}(c_{h}^{n}) \\nablac^{n}, \\nabla \\rho^{n})_{K} \\equiv \\sum_{i=1}^{4}R_{i}.}} \\end{array}", "UniMER-1M_0002182": "\\operatorname{Spin}", "UniMER-1M_0002207": "r \\simeq12.4 \\, \\epsilon=6.2/N_{ \\mathrm{COBE}} \\sim0.11 \\;,", "UniMER-1M_0002175": "\\begin{array}{rlr}{{ \\intsech(x)dx}}&{{=}}&{{ \\pi.}} \\end{array}", "UniMER-1M_0002178": "\\mathbf{ \\hat{y}}( \\mathbf{x}_{t^{*}})", "UniMER-1M_0002209": "\\mathrm{ \\frac{BW_{20dB}}{BW_{3dB}}}", "UniMER-1M_0002213": "\\{t_{i},m_{i} \\}_{i=1}^{n} \\in \\left( \\mathbb{R}_{>0} \\times \\mathcal{M} \\right)", "UniMER-1M_0002210": "\\lambda", "UniMER-1M_0002186": "G_{Mar}^{+}( \\textbf{x}_{v}, \\textbf{x}_{r}, \\omega)=- \\int_{ \\mathcal{D}_{acq}}R( \\textbf{x}_{r}, \\textbf{x}_{s}, \\omega)f_{1}^{-}( \\textbf{x}_{s}, \\textbf{x}_{v}, \\omega)^{*} \\,d \\textbf{x}_{s}+f_{1}^{+}( \\textbf{x}_{r}, \\textbf{x}_{v}, \\omega)^{*}.", "UniMER-1M_0002236": "\\sqrt{ . . .+u}", "UniMER-1M_0002237": "\\Phi", "UniMER-1M_0002199": "\\sum_{p=1}^{n} \\mathrm{cos}^{4}(p \\theta)=3 \\sum_{p=1}^{n} \\mathrm{cos}^{2}(p \\theta) \\mathrm{sin}^{2}(p \\theta)", "UniMER-1M_0002197": "N_{ \\mathrm{~i~n~}}=2", "UniMER-1M_0002220": "\\Bigl( \\rho,~[ \\breve{ \\vec{u}} \\cdot \\nabla] \\breve{ \\vec{u}} \\cdot \\breve{ \\vec{ \\chi}}-[ \\breve{ \\vec{u}} \\cdot \\nabla] \\breve{ \\vec{ \\chi}} \\cdot \\breve{ \\vec{u}} \\Bigr)_{ \\Omega}=2 \\pi \\, \\Bigl( \\rho_{_c}r,~[ \\vec{u} \\cdot \\nabla_{c}] \\vec{u} \\cdot \\vec{ \\chi}-[ \\vec{u} \\cdot \\nabla_{c}] \\vec{ \\chi} \\cdot \\vec{u} \\Bigr).", "UniMER-1M_0002239": "+ \\hbar/2", "UniMER-1M_0002198": "{ \\tau}={ \\langle}F{ \\rangle}/A", "UniMER-1M_0002243": "\\Delta=6.3", "UniMER-1M_0002200": "(<PERSON>,<PERSON>, \\theta_{ \\mu})=(750,100,0.005)", "UniMER-1M_0002215": ">2", "UniMER-1M_0002144": "k_{1}( \\tau)=k_{1}+ \\delta_{ \\tau,0} \\bar{n}_{0}/ \\Deltat", "UniMER-1M_0002247": "190.0", "UniMER-1M_0002222": "pp", "UniMER-1M_0002221": "k/n", "UniMER-1M_0002193": "\\begin{array}{rlr}{{ \\operatorname{limsup}_{t \\to \\infty}b_{i}^{ \\leftarrow}(t) \\P( \\boldsymbol{X}+ \\boldsymbol{Y} \\intA)}}&{{ \\leq}}&{{ \\operatorname{limsup}_{t \\to \\infty}b_{i}^{ \\leftarrow}(t) \\P( \\boldsymbol{X} \\intA_{ \\varepsilon}^{+})+ \\operatorname{limsup}_{t \\to \\infty}b_{i}^{ \\leftarrow}(t) \\P( \\| \\boldsymbol{Y} \\|_{ \\infty}> \\varepsilont)}} \\\\ &{{ \\leq}}&{{ \\mu_{i}(A_{ \\varepsilon}^{+})+ \\operatorname{limsup}_{t \\to \\infty}b_{i}^{ \\leftarrow}(t)( \\gammat)^{-( \\alpha_{i}+ \\gamma)} \\mathbb{E} \\| \\boldsymbol{Y} \\|^{ \\alpha_{i}+ \\gamma}}} \\\\ &{{=}}&{{ \\mu_{i}(A_{ \\varepsilon}^{+}) \\downarrow \\mu_{i}(A) \\quad \\mathrm{~as~} \\varepsilon \\downarrow0,}} \\end{array}", "UniMER-1M_0002216": "\\begin{array}{rl}{{{ \\Xi}= \\frac{1}{v_{ \\mathrm{p}}^{2N}}}}&{{ \\sum_{n_{ \\gamma}=0}^{ \\infty} \\prod_{ \\gamma} \\frac{e^{ \\mu_{ \\gamma}n_{ \\gamma}}}{n_{ \\gamma}!v_{ \\gamma}^{n_{ \\gamma}}} \\prod_{j=1}^{2} \\int \\mathrm{ \\hat{D}} \\{ \\mathbf{R}_{j} \\} \\prod_{ \\kappa=1}^{n_{ \\gamma}} \\int \\mathrm{d} \\mathbf{r}_{ \\gamma, \\kappa} \\mathrm{exp}(-{ \\beta} \\mathcal{H}) \\prod_{ \\mathbf{r}} \\delta \\left[ \\hat{ \\phi}_{ \\mathrm{p}}( \\mathbf{r})+ \\hat{ \\phi}_{ \\mathrm{s}}( \\mathbf{r})-1 \\right]}} \\\\ &{{ \\times \\delta \\left[ \\frac{1}{N} \\int_{0}^{N} \\mathrm{d} s \\mathbf{R}_{1}(s)- \\boldsymbol{ \\xi}_{1} \\right] \\delta \\left[ \\frac{1}{N} \\int_{0}^{N} \\mathrm{d} s \\mathbf{R}_{2}(s)- \\boldsymbol{ \\xi}_{2} \\right]}} \\end{array}", "UniMER-1M_0002255": "2.5", "UniMER-1M_0002253": "A", "UniMER-1M_0002242": "\\eta_{ \\mathrm{turb}}= \\left(u_{ \\mathrm{rms}}^{ \\mathrm{max}}k_{0} \\right)^{-1},", "UniMER-1M_0002227": "\\pi", "UniMER-1M_0002233": "_3", "UniMER-1M_0002228": "\\gamma \\to0", "UniMER-1M_0002234": "\\tilde{p}(w)=( \\tau-2)w^{- \\tau}", "UniMER-1M_0002229": "2.65 \\pm1.60", "UniMER-1M_0002263": "H_{U}", "UniMER-1M_0002235": "\\boldsymbol{B_{ext}}>B_{sat}", "UniMER-1M_0002258": "\\mathbb{S}_{ \\textrm{III}}", "UniMER-1M_0002241": "R_{ \\mathrm{CO}}=1.21", "UniMER-1M_0002267": "T^{2}", "UniMER-1M_0002268": "\\Omega=0", "UniMER-1M_0002217": "\\begin{array}{rl}{{ \\left( \\left( \\left(I_{U^{ \\prime}} \\circ \\theta_{w^{-1}B \\midB} \\right)(f) \\right)(g) \\right)(m)}}&{{= \\delta_{P^{ \\prime}}(m)^{1/2} \\int_{U^{ \\prime}(F)} \\left( \\theta_{w^{-1}B \\midB}(f) \\right)(u^{ \\prime}mg)du^{ \\prime}}} \\\\ &{{= \\delta_{P^{ \\prime}}(m)^{1/2} \\int_{U^{ \\prime}(F)} \\int_{U_{J,w^{-1}B}}f(u^{ \\prime \\prime}u^{ \\prime}mg)du^{ \\prime \\prime}du^{ \\prime}}} \\\\ &{{= \\delta_{P^{ \\prime}}(m)^{1/2} \\int_{U_{J,w^{-1}B}} \\int_{U^{ \\prime}(F)}f(u^{ \\prime \\prime}u^{ \\prime}mg)du^{ \\prime}du^{ \\prime \\prime}.}} \\end{array}", "UniMER-1M_0002218": "\\psi(3770) \\toD^{0} \\bar{D}^{0} \\toanything", "UniMER-1M_0002246": ".Also,bendingpointsalongcurvesplottedinthethird-columnfiguresdemonstratethattransientstatesoccurwithin2.258< \\mu_{0}<2.475.Moreinterestingly,thethirdcolumnmanifeststhatE_{3}^{ \\prime}representsNeimark-Sackerbifurcationat \\mu_{0}=3.25636becauseofthefollowingfacts:first, \\omega_{0}and \\omega_{1}arecomplexconjugateswithmodulus1,andsecond,as \\mu_{0}variesacross3.25636fromsmallertolargervalue,topologicaltypeofE_{3}^{ \\prime}changesfromasink(stable)toasource(unstable)", "UniMER-1M_0002260": "\\mathfrak{g}_{ \\mathrm{sh}}:[-1,1] \\to[0, \\infty)", "UniMER-1M_0002274": "n^{ \\prime}", "UniMER-1M_0002256": "10", "UniMER-1M_0002225": "Z_{c}= \\frac{Z_{0}}{2 \\pi} \\mathrm{ln} \\left( \\frac{r_{o}}{r_{i}} \\right),", "UniMER-1M_0002277": "w_{u}", "UniMER-1M_0002214": "\\begin{array}{rl}{{|F_{x}( \\eta_{x}^{ \\lambda})|}}&{{ \\lesssim2^{-N_{ \\lambda}d} \\| \\eta \\|_{ \\infty}2^{N_{ \\lambda}d}2^{-N_{ \\lambda} \\bar{ \\alpha}}}} \\\\ &{{ \\quad+ \\sum_{n=N_{ \\lambda}}^{+ \\infty}2^{-nd}2^{(n-N_{ \\lambda})d} \\| \\eta \\|_{C^{ \\tilde{r}+1}} \\lambda^{-d} \\left( \\frac{2^{-n}}{ \\lambda} \\right)^{ \\tilde{r}+1} \\left(2^{-n \\bar{ \\alpha}}+2^{-n \\alpha} \\lambda^{ \\gamma- \\alpha} \\right).}} \\end{array}", "UniMER-1M_0002208": "d_{e}(t)=a(t) \\int_{t}^{ \\infty} \\frac{dt^{ \\prime}}{a(t^{ \\prime})}~ \\cdot", "UniMER-1M_0002261": "^c", "UniMER-1M_0002264": "\\mu_{ \\mathrm{~m~}}", "UniMER-1M_0002244": "W_{m(1)}=- \\bigr( \\bar{Q}_{i}R_{ \\alpha}^{i}(A+Q)- \\epsilon^{ab}E_{ \\alphaab}^{*} \\bigr)B^{ \\alpha}- \\bigr(Q_{ia}^{*}R_{ \\alpha}^{i}(A+Q)+H_{ \\alphaa} \\bigr)C^{ \\alphaa}.", "UniMER-1M_0002269": "\\mathrm{CV}", "UniMER-1M_0002266": "\\otimes", "UniMER-1M_0002238": "\\frac{1}{ \\mu_{1}v_{1}}(E_{0,I}-E_{0,R})= \\frac{E_{0,T}}{ \\mu_{2}v_{2}},", "UniMER-1M_0002273": "\\begin{array}{rl}{{ \\bigg| \\sum_{y_{ \\alpha} \\in \\ensuremath{ \\mathbb{R}}^{d} \\setminusB_{r}}}}&{{ \\int_{Q_{ \\alpha}}(J_{p}( \\phi(x+y)- \\phi(x))-J_{p}( \\phi(x+y_{ \\alpha})- \\phi(x))) \\frac{ \\, \\mathrm{d} y}{|y|^{d+sp}} \\bigg|}} \\\\ &{{ \\leqCh^{p-1} \\sum_{y_{ \\alpha} \\in \\ensuremath{ \\mathbb{R}}^{d} \\setminusB_{r}} \\int_{Q_{ \\alpha}} \\frac{ \\, \\mathrm{d} y}{|y|^{d+sp}} \\leqCh^{p-1} \\int_{ \\ensuremath{ \\mathbb{R}}^{d} \\setminusB_{r/2}} \\frac{ \\, \\mathrm{d} y}{|y|^{d+sp}} \\leqCh^{p-1}(1+r^{-sp}).}} \\end{array}", "UniMER-1M_0002232": "k_{L}=2 \\pi/ \\lambda", "UniMER-1M_0002226": "\\Gamma_{0}= \\gamma_{0}{ \\mathfrak{R}_{0}, \\qquad \\gamma_{0}={ \\frac{1}{ \\sqrt{1-v_{0}^{2}}}}, \\qquad{ \\mathfrak{R}_{0}={ \\sqrt{1+A_{0}^{2}}}.", "UniMER-1M_0002251": "\\int_{x} \\frac{ \\delta \\Gamma_{ \\kappa}}{ \\delta \\tilde{u}(t,x)}= \\int_{x} \\par tial_{t}u(t,x) \\,.", "UniMER-1M_0002281": "I_{t}=- \\frac{1}{4} \\intd^{2}x \\sqrt{G}R+ \\frac{1}{16} \\intd^{2}x \\sqrt{G}R \\hat{F}R.", "UniMER-1M_0002230": "\\begin{array}{rl}{{k_{1}(t,y)}}&{{= \\sum_{n \\geq1}K_{1}(t,n) \\mathrm{sin}(ny)}} \\\\ &{{= \\sum_{n \\geq1} \\int_{0}^{t}d \\tau_{1} \\sum_{j=0}^{ \\infty}(-1)^{n} \\int_{0}^{ \\infty}d \\tau \\mathrm{exp}(- \\lambdan^{2} \\tau)F( \\tau, \\tau_{1})K_{2}(t- \\tau_{1},n) \\mathrm{sin}(ny)}} \\\\ &{{= \\sum_{n \\geq1} \\int_{0}^{t}d \\tau_{1} \\sum_{j=0}^{ \\infty}(-1)^{n} \\int_{0}^{ \\infty}d \\tau \\mathrm{exp}(- \\lambdan^{2} \\tau)F( \\tau, \\tau_{1}) \\left[ \\frac{2}{ \\pi} \\int_{0}^{ \\pi}d \\etak_{2}(t- \\tau_{1}, \\eta) \\mathrm{sin}(n \\eta) \\right] \\mathrm{sin}(ny)}} \\\\ &{{= \\int_{0}^{t}d \\tau_{1} \\sum_{j=0}^{ \\infty}(-1)^{n} \\int_{0}^{ \\infty}d \\tauF( \\tau, \\tau_{1}) \\left[ \\frac{2}{ \\pi} \\int_{0}^{ \\pi}d \\etak_{2}(t- \\tau_{1}, \\eta) \\sum_{n \\geq1} \\mathrm{exp}(- \\lambdan^{2} \\tau) \\mathrm{sin}(n \\eta) \\mathrm{sin}(ny) \\right].}} \\end{array}", "UniMER-1M_0002283": "\\omega_{i}", "UniMER-1M_0002293": "R \\approx \\pm1.5 \\,a_{0}", "UniMER-1M_0002245": "| \\psi^{ \\prime}( \\frac{ \\pi}{4 \\lambda}) \\rangle= \\frac{1}{2}(|ee1 \\rangle+|ge2 \\rangle+|eg3 \\rangle+|gg4 \\rangle)", "UniMER-1M_0002240": "\\begin{array}{rlr}{{ \\langlep_{i}p_{j} \\rangle}}&{{=}}&{{ \\delta_{ij}+ \\lambda \\left( \\delta_{i3} \\delta_{j3}- \\frac{1}{3} \\delta_{ij} \\right),}} \\\\ {{ \\langlep_{i}p_{j}p_{k}p_{l} \\rangle}}&{{=}}&{{ \\frac{1}{5} \\left(1- \\bar{ \\lambda}- \\frac{ \\lambda}{3} \\right) \\left( \\delta_{ij} \\delta_{kl}+ \\delta_{ik} \\delta_{jl}+ \\delta_{il} \\delta_{jk} \\right)+ \\left( \\lambda-7 \\bar{ \\lambda} \\right) \\delta_{i3} \\delta_{j3} \\delta_{k3} \\delta_{l3}+}} \\\\ &&{{ \\bar{ \\lambda} \\left( \\delta_{i3} \\delta_{j3} \\delta_{kl}+ \\delta_{i3} \\delta_{k3} \\delta_{jl}+ \\delta_{i3} \\delta_{l3} \\delta_{jk}+ \\delta_{j3} \\delta_{k3} \\delta_{il}+ \\delta_{j3} \\delta_{l3} \\delta_{ik}+ \\delta_{k3} \\delta_{l3} \\delta_{ij} \\right),}} \\end{array}", "UniMER-1M_0002282": "{ \\Ref_{ \\PsiN \\to \\PsiN}}={ \\frac{s \\pi}{2}}{{ \\frac{ \\par tial}{ \\par tial \\mathrm{ln}{s}}}{ \\frac{ \\Imf_{ \\PsiN \\to \\PsiN}}{s}}} \\quad.", "UniMER-1M_0002297": "\\Gamma", "UniMER-1M_0002262": "x/h=8", "UniMER-1M_0002279": "+ \\, \\left. \\frac{1}{2} \\,G_{ij}G^{ \\alpha \\beta} \\par tial_{ \\alpha} \\Phi^{i} \\par tial_{ \\beta} \\Phi^{j} \\, \\right\\} \\left(1+ \\frac{{ \\tilde{b}}}{2 \\pi^{2} \\alpha^{ \\prime}} \\right)+ \\frac{1}{8 \\pig_{s}} \\intd^{4}x \\, \\frac{1}{4} \\,F_{ \\alpha \\beta}{ \\widetilde{F}}^{ \\alpha \\beta} \\, \\left( \\frac{c+C_{0} \\,b}{2 \\pi^{2} \\alpha^{ \\prime}} \\right)~~.", "UniMER-1M_0002224": "\\begin{array}{rlr}{{ \\hat{a}_{i} \\hat{a}_{j}- \\hat{a}_{j} \\hat{a}_{i}}}&{{=}}&{{0,}} \\\\ {{ \\hat{a}_{i}^{ \\dagger} \\hat{a}_{j}^{ \\dagger}- \\hat{a}_{j}^{ \\dagger} \\hat{a}_{i}^{ \\dagger}}}&{{=}}&{{0,}} \\\\ {{ \\hat{a}_{i} \\hat{a}_{j}^{ \\dagger}- \\hat{a}_{j}^{ \\dagger} \\hat{a}_{i}}}&{{=}}&{{ \\delta_{ij}.}} \\end{array}", "UniMER-1M_0002271": "\\frac{|ax(t)+by(t)+c|}{ \\sqrt{a^{2}+b^{2}}}", "UniMER-1M_0002305": "( \\rho, \\mathbb{C}^{5})", "UniMER-1M_0002291": "| \\omega| \\ggc_{s}k", "UniMER-1M_0002270": "\\Delta \\rho= \\rho_{ \\mathrm{air}}- \\rho_{ \\mathrm{liq}}", "UniMER-1M_0002249": "4.75", "UniMER-1M_0002254": "0.3", "UniMER-1M_0002259": "1~ \\mathrm{~M~H~z~}", "UniMER-1M_0002223": "\\xi={ \\left[ \\begin{array}{l}{{ \\xi(s,t)}} \\\\ {{ \\xi(s-1,t)}} \\\\ {{ \\vdots}} \\\\ {{ \\xi(-(s-1),t)}} \\\\ {{ \\xi(-s,t)}} \\end{array} \\right]}= \\xi(s,t){ \\left[ \\begin{array}{l}{{1}} \\\\ {{0}} \\\\ {{ \\vdots}} \\\\ {{0}} \\\\ {{0}} \\end{array} \\right]}+ \\xi(s-1,t){ \\left[ \\begin{array}{l}{{0}} \\\\ {{1}} \\\\ {{ \\vdots}} \\\\ {{0}} \\\\ {{0}} \\end{array} \\right]}+ . . .+ \\xi(-(s-1),t){ \\left[ \\begin{array}{l}{{0}} \\\\ {{0}} \\\\ {{ \\vdots}} \\\\ {{1}} \\\\ {{0}} \\end{array} \\right]}+ \\xi(-s,t){ \\left[ \\begin{array}{l}{{0}} \\\\ {{0}} \\\\ {{ \\vdots}} \\\\ {{0}} \\\\ {{1}} \\end{array} \\right]}", "UniMER-1M_0002292": "E_{sm}= \\frac{1}{8}M_{S} \\chi^{2}R^{2}w_{0}^{4}| \\tilde{h}_{m}(w_{0})|^{2},", "UniMER-1M_0002294": "6.84 \\", "UniMER-1M_0002275": "T_{ \\mathrm{inf}}=7", "UniMER-1M_0002257": "Core( \\mathbf{p}, \\mathbf{t})=( \\mathcal{C}, \\mathcal{L}( \\mathcal{C}))", "UniMER-1M_0002317": "T=24", "UniMER-1M_0002284": "\\Theta^{'} \\Theta=2m{ \\cal{S}}.", "UniMER-1M_0002319": "\\sim5", "UniMER-1M_0002304": "\\theta_{ \\pm}", "UniMER-1M_0002286": "b \\approx2", "UniMER-1M_0002302": "t \\inA^{1},u \\inV^{1},p \\inA^{3}", "UniMER-1M_0002289": "S_{e}^{ \\mathrm{LSS}}=K \\varepsilon^{1/2},", "UniMER-1M_0002280": "\\rho_{xy}=r_{xy}e^{- \\beta_{xy}r_{xy}}", "UniMER-1M_0002285": "5.46 \\times10^{-5}", "UniMER-1M_0002278": "m=1", "UniMER-1M_0002287": "g/ \\gamma_{ \\sigma} \\approx300", "UniMER-1M_0002301": "R_{k}(x)= \\sum_{j=k+1}^{ \\infty}c_{j}(x-a)^{j}=(x-a)^{k}h_{k}(x), \\qquad|x-a|<r.", "UniMER-1M_0002326": "\\begin{array}{rl}{{E[ \\alpha, \\beta; \\mu]=}}&{{-t[ \\mathrm{sin}2 \\alpha+ \\mathrm{sin}2 \\beta]+ \\frac{1}{2}U[1+ \\mathrm{cos}2 \\alpha \\mathrm{cos}2 \\beta]}} \\end{array}", "UniMER-1M_0002265": "H({ \\bf p})= \\omega({ \\bf p})= \\sqrt{{ \\bf p}^{2}+m^{2}},", "UniMER-1M_0002290": "E_{ \\mathrm{~x~c~}}[n]=E_{ \\mathrm{~x~c~}}[ \\tilde{n}, \\tilde{G}]+ \\sum_{A} \\left(E_{ \\mathrm{~x~c~}}[n_{A}^{1},G_{A}^{1}]-E_{ \\mathrm{~x~c~}}[ \\tilde{n}_{A}^{1}, \\tilde{G}_{A}^{1}] \\right),", "UniMER-1M_0002314": "\\vec{S}", "UniMER-1M_0002321": "DT", "UniMER-1M_0002311": "\\varepsilon \\colonC_{p}(X) \\otimesC^{q}(X) \\to \\mathbb{Z}", "UniMER-1M_0002316": "\\left(i \\par tial_{t} \\,- \\,H \\right)| \\nu_{i}(t) \\rangle \\,= \\,0 \\,,", "UniMER-1M_0002315": "n=10", "UniMER-1M_0002327": "\\begin{array}{rl}{{ \\mathbf{E}_{p}^{n+ \\theta}}}&{{= \\mathbf{E}^{n+ \\theta}( \\mathbf{x}_{p}^{n+1/2})= \\sum_{g} \\mathbf{E}_{g}^{n+ \\theta}W( \\mathbf{x}_{p}^{n+1/2}- \\mathbf{x}_{g})}} \\\\ {{ \\mathbf{B}_{p}^{n}}}&{{= \\mathbf{B}^{n+ \\theta}( \\mathbf{x}_{p}^{n+1/2})= \\sum_{g} \\mathbf{B}_{g}^{n}W( \\mathbf{x}_{p}^{n+1/2}- \\mathbf{x}_{g})}} \\end{array}", "UniMER-1M_0002288": "\\sum_{k=1}^{n}{ \\frac{1}{k}} \\sim \\gamma+ \\mathrm{log} n+{ \\frac{1}{2n}}- \\sum_{k=1}^{ \\infty}{ \\frac{B_{2k}}{2kn^{2k}}},", "UniMER-1M_0002295": "\\hat{ \\sigma}_{ij}^{0}= \\frac{g^{4} \\vertK_{ij} \\vert^{2}}{384 \\pi} \\frac{( \\hat{s}-M_{t}^{2})^{2}}{ \\hat{s}^{2}( \\hat{s}-M_{W}^{2})^{2}}(2 \\hat{s}+M_{t}^{2}),", "UniMER-1M_0002318": "\\frac{ \\alpha{B}(0.7R_{ \\odot}, \\theta,t- \\tau_{ \\mathrm{B}})}{1+ \\left({{B}(0.7R_{ \\odot}, \\theta,t- \\tau_{ \\mathrm{B}})}/{B_{0}} \\right)^{2}},", "UniMER-1M_0002296": "t=0.85 \\,,0.95 \\,,1 \\,,1.05 \\,,1.1 \\,,1.15 \\,,1.2 \\,,1.25 \\,,1.3", "UniMER-1M_0002303": "Loss.autograd().backward()", "UniMER-1M_0002276": "\\begin{array}{rl}{{K(i+1|i) \\frac{u(i+1)}{u(i)}[S(i+1)-S(i)]+K(i-1|i) \\frac{u(i-1)}{u(i)}[S(i-1)-S(i)]=}}&{{- \\frac{dS(i)}{dt}}} \\\\ {{K(i|i+1) \\frac{v(i+1)}{v(i)}[S(i)-S(i+1)]+K(i|i-1) \\frac{v(i-1)}{v(i)}[S(i)-S(i-1)]=}}&{{- \\frac{dS(i)}{dt}}} \\\\ {{K(i|i-1) \\frac{u(i)}{u(i-1)}P(i-1)+K(i|i+1) \\frac{u(i)}{u(i+1)}P(i+1)-}}& \\\\ {{K(i+1|i) \\frac{u(i+1)}{u(i)}P(i)-K(i-1|i) \\frac{u(i-1)}{u(i)}P(i)=}}&{{ \\frac{dP(i)}{dt}.}} \\end{array}", "UniMER-1M_0002334": "i", "UniMER-1M_0002344": "X_{t}", "UniMER-1M_0002336": "g=0", "UniMER-1M_0002345": "u(0)=0", "UniMER-1M_0002307": "t=0~s", "UniMER-1M_0002306": "\\frac{d}{dt} \\Big( \\int \\phi \\frac{ \\rho_{s}|{ \\bf u}|^{2}}{2} \\Big)= \\int \\phi \\rho_{s}{ \\bf g} \\cdot{ \\bf u}+ \\underbrace{ \\intp_{f} \\, \\mathrm{div} \\,{ \\bf u}}_{A}- \\underbrace{ \\int \\boldsymbol{ \\tau}: \\nabla{ \\bf u}}_{B}+ \\underbrace{ \\intp \\, \\mathrm{div} \\,{ \\bf u}}_{C}.", "UniMER-1M_0002323": "j,k \\in \\mathbb{Z}_{ \\geq0}", "UniMER-1M_0002349": "Ra_{c}^{ \\mathrm{ \\tiny{bulk}}}", "UniMER-1M_0002313": "\\delta \\equiv \\epsilon_{2}- \\epsilon_{1}=400~ \\textrm{cm}^{-1}", "UniMER-1M_0002309": "\\phi_{i}^{m} \\equiv \\psi_{a}^{i} \\phi_{a}^{m}", "UniMER-1M_0002322": "\\varepsilon>0", "UniMER-1M_0002312": "\\delta=( \\hat{N}_{L}- \\hat{N}_{R})/L_{x}", "UniMER-1M_0002324": "{ \\Phi_{i}} \\gets{ \\Phi_{p}}-{ \\Phi_{s}}", "UniMER-1M_0002329": "\\Delta", "UniMER-1M_0002328": "U_{0}", "UniMER-1M_0002332": "H/W", "UniMER-1M_0002361": "N^{- \\alpha}", "UniMER-1M_0002353": "\\DeltaJ_{0}=- \\intd^{2}xa(r) \\psi^{ \\dagger} \\psi.", "UniMER-1M_0002363": "a_{x}", "UniMER-1M_0002351": "\\Sigma", "UniMER-1M_0002365": "n", "UniMER-1M_0002342": "x", "UniMER-1M_0002367": "1", "UniMER-1M_0002368": "B", "UniMER-1M_0002340": "\\mathcal{U}(x_{min},x_{max})=(x>=x_{min}) \\cap(x<=x_{max}).", "UniMER-1M_0002338": "z", "UniMER-1M_0002333": "(L \\lesssim4)", "UniMER-1M_0002325": "p^{a-}=1- \\sum_{k} \\left[p_{k}^{aa}+p_{k}^{ab} \\right]", "UniMER-1M_0002354": "Q \\! \\left( \\rho,T \\right) \\!= \\!L \\! \\left( \\rho,T \\right)-H \\! \\left( \\rho,T \\right)", "UniMER-1M_0002335": "\\begin{array}{rl}{{P_{mn}}}&{{= \\frac{1}{m!n!} \\intI(x,y) \\left( \\frac{x}{2 \\sigma} \\right)^{2m} \\left( \\frac{y}{2 \\sigma} \\right)^{2n}e^{- \\frac{x^{2}+y^{2}}{4 \\sigma^{2}}} \\ dxdy.}} \\end{array}", "UniMER-1M_0002331": "U=e^{-i \\alpha \\mathbf{n} \\cdot \\mathbf{J}}", "UniMER-1M_0002330": "\\phi(x)= \\sum_{n=1}^{ \\infty}{ \\frac{f_{n}(x)}{(2ik)^{n}}}=(d/dx)( \\mathrm{log}[ik- \\kappa \\mathrm{tanh} \\kappax]- \\mathrm{log}[ik+ \\kappa])", "UniMER-1M_0002347": "{ \\frac{1}{{ \\frac{1}{N}} \\sum_{i=1}^{N}{ \\frac{{ \\hat{c}}-{ \\hat{a}}}{{ \\hat{c}}-Y_{i}}}}}={ \\frac{{ \\hat{ \\beta}}-1}{{ \\hat{ \\alpha}}+{ \\hat{ \\beta}}-1}}={ \\hat{H}}_{1-X}", "UniMER-1M_0002348": "\\begin{array}{rlr}{{ \\langle \\Phi_{ \\vartheta}( \\mu)|e^{-T( \\mu)}He^{T( \\mu)}| \\Phi_{ \\mu} \\ranglec_{ \\mu}^{ \\alpha}}}&{{+}}&{{ \\sum_{ \\nu \\;( \\neq \\mu)} \\left[ \\langle \\Phi_{ \\vartheta}( \\mu)|e^{-T( \\mu)}e^{T( \\nu)}| \\Phi_{ \\mu} \\rangleH_{ \\mu \\nu}^{ \\mathrm{eff}}c_{ \\nu}^{ \\alpha} \\right.-}} \\\\ &{{-}}&{{ \\left. \\langle \\Phi_{ \\vartheta}( \\mu)|e^{-T( \\mu)}e^{T( \\nu)}| \\Phi_{ \\nu} \\rangleH_{ \\nu \\mu}^{ \\mathrm{eff}}c_{ \\mu}^{ \\alpha} \\right]=0.}} \\end{array}", "UniMER-1M_0002308": "\\begin{array}{rl}{{ \\sum_{x \\in \\mathbb{X}}}&{{ \\bigg( \\kappa_{I} \\mu(x) \\frac{ \\pi(n-e_{x})}{ \\pi(n)}+ \\kappa_{E}(n_{x}+1) \\frac{ \\pi(n+e_{x})}{ \\pi(n)}}} \\\\ &{{+ \\sum_{ \\nu_{j} \\to \\nu_{j}^{ \\prime}} \\kappa_{j} \\binom{x- \\nu_{j}^{ \\prime}+ \\nu_{j}}{ \\nu_{j}}(n_{x- \\nu_{j}^{ \\prime}+ \\nu_{j}}+1) \\frac{ \\pi(n-e_{x}+e_{x- \\nu_{j}^{ \\prime}+ \\nu_{j}})}{ \\pi(n)} \\bigg)}} \\\\ &{{= \\sum_{x \\in \\mathbb{X} \\bigg( \\kappa_{I} \\mu(x)+n_{x} \\kappa_{E}+n_{x} \\sum_{ \\nu_{j} \\to \\nu_{j}^{ \\prime}} \\kappa_{j} \\binom{x}{ \\nu_{j}} \\bigg)}} \\\\ {{ \\sum_{x \\in \\mathbb{X}}}&{{ \\bigg( \\kappa_{I} \\mu(x) \\pi(n-e_{x})+ \\kappa_{E}(n_{x}+1) \\pi(n+e_{x})}} \\\\ &{{+ \\sum_{ \\nu_{j} \\to \\nu_{j}^{ \\prime}} \\kappa_{j} \\binom{x- \\nu_{j}^{ \\prime}+ \\nu_{j}}{ \\nu_{j}}(n_{x- \\nu_{j}^{ \\prime}+ \\nu_{j}}+1) \\pi(n-e_{x}+e_{x- \\nu_{j}^{ \\prime}+ \\nu_{j}}) \\bigg)}} \\\\ &{{= \\pi(n) \\sum_{x \\in \\mathbb{X} \\bigg( \\kappa_{I} \\mu(x)+n_{x} \\kappa_{E}+n_{x} \\sum_{ \\nu_{j} \\to \\nu_{j}^{ \\prime}} \\kappa_{j} \\binom{x}{ \\nu_{j}} \\bigg),}} \\end{array}", "UniMER-1M_0002341": "\\Lambda(T)= \\frac{h}{ \\sqrt{ \\pi2 \\mu_{a}k_{ \\mathrm{B}}T}}.", "UniMER-1M_0002379": "m_{1}", "UniMER-1M_0002382": "k_{z}", "UniMER-1M_0002376": "W( \\Phi)= \\frac{m}{2} \\Phi^{2}+ \\frac{g}{3} \\Phi^{3}.", "UniMER-1M_0002346": "\\begin{array}{rl}{{ \\varphi^{ \\mathrm{s}}( \\alpha,t)}}&{{= \\Phi^{ \\mathrm{phys}}( \\xi^{ \\mathrm{s}}( \\alpha,t), \\eta^{ \\mathrm{s}}( \\alpha,t),t)= \\varphi^{ \\mathrm{s}, \\mathrm{phys}}( \\xi^{ \\mathrm{s}}( \\alpha,t),t),}} \\\\ {{ \\varphi^{ \\mathrm{b}}( \\alpha,t)}}&{{= \\Phi^{ \\mathrm{phys}}( \\xi^{ \\mathrm{b}}( \\alpha,t), \\eta^{ \\mathrm{b}}( \\alpha,t),t),}} \\\\ {{ \\psi^{ \\mathrm{s}}( \\alpha,t)}}&{{= \\Psi^{ \\mathrm{phys}}( \\xi^{ \\mathrm{s}}( \\alpha,t), \\eta^{ \\mathrm{s}}( \\alpha,t),t)= \\psi^{ \\mathrm{s}, \\mathrm{phys}}( \\xi^{ \\mathrm{s}}( \\alpha,t),t),}} \\\\ {{ \\psi^{ \\mathrm{b}}( \\alpha,t)}}&{{= \\Psi^{ \\mathrm{phys}}( \\xi^{ \\mathrm{b}}( \\alpha,t), \\eta^{ \\mathrm{b}}( \\alpha,t),t),}} \\end{array}", "UniMER-1M_0002385": "\\bar{R}", "UniMER-1M_0002362": "x_{j,2} \\textrm{e}^{i \\mathbf{k}_{j} \\cdot \\mathbf{r}} \\mathbf{ \\hat{e}}_{j,2}", "UniMER-1M_0002339": "[F_{X}]= \\rho_{0} \\kappa_{X}( \\alpha/ \\beta)| \\par tial_{r}T_{ \\mathrm{ad}}| \\equivF_{ \\mathrm{crit}}", "UniMER-1M_0002343": "1.25 \\times10^{-2}", "UniMER-1M_0002350": "\\rho=r_{n} \\cdotr_{A}=-r_{L} \\cdotr_{A}.", "UniMER-1M_0002370": "\\sigma", "UniMER-1M_0002359": "\\begin{array}{rl}{{ \\frac{1}{2 \\tau_{i}^{2}}- \\frac{1}{2( \\tau_{i}+c_{im})^{2}}}}&{{= \\frac{c_{im}^{2}+ \\tau_{i}c_{im}+ \\tau_{i}c_{im}}{2 \\tau_{i}^{2}( \\tau_{i}+c_{im})^{2}}= \\frac{c_{im}( \\tau_{i}+c_{im})+ \\tau_{i}c_{im}}{2 \\tau_{i}^{2}( \\tau_{i}+c_{im})^{2}}}} \\\\ &{{= \\frac{c_{im}}{2 \\tau_{i}^{2}( \\tau_{i}+c_{im})}+ \\frac{c_{im}}{2 \\tau_{i}( \\tau_{i}+c_{im})^{2}} \\le \\frac{ \\sqrt{c_{im}}}{4 \\tau_{i}^{5/2}}+ \\frac{ \\sqrt{c_{im}}}{4 \\tau_{i}^{5/2}}= \\frac{ \\sqrt{c_{im}}}{2 \\tau_{i}^{5/2}},}} \\end{array}", "UniMER-1M_0002337": "\\begin{array}{rl}{{|f(r_{0} \\omega)|^{2}}}&{{ \\lesssim \\sum_{|I| \\leq2} \\int_{S_{r_{0}}}| \\Omega_{ij}^{I}f|^{2}d \\omega \\lesssim \\sum_{|I| \\leq2} \\int_{r_{0}}^{ \\infty} \\par tial_{r} \\left( \\int_{ \\mathbb{S}^{2}}| \\Omega_{ij}^{I}f(r \\omega)|^{2}d \\omega \\right)dr}} \\\\ &{{ \\lesssim \\sum_{|I| \\leq2} \\int_{r_{0}}^{ \\infty} \\int_{ \\mathbb{S}^{2}} \\Omega_{ij}^{I}f \\cdot \\par tial_{r} \\Omega_{ij}^{I}fd \\omegadr \\lesssim \\sum_{|I| \\leq2} \\int_{|x| \\geqr_{0}}(| \\Omega_{ij}^{I}f|^{2}+| \\par tial_{r} \\Omega_{ij}^{I}f|^{2})r^{-2}r^{2}d \\omegadr}} \\\\ &{{ \\lesssim(r_{0})^{-2} \\sum_{|I| \\leq2} \\int_{|x| \\geqr_{0}}| \\Omega_{ij}^{I}f|^{2}+| \\par tial_{r} \\Omega_{ij}^{I}f|^{2}dx}} \\end{array}", "UniMER-1M_0002372": "I_{g}= \\frac{1}{2 \\pi \\kappa} \\intd^{2}x \\epsilon^{ \\mu \\nu} \\Bigl( \\eta_{a}( \\par tial_{ \\mu}e_{ \\nu}^{a}+ \\omega_{ \\mu} \\epsilon_{~b}^{a}e_{ \\nu}^{b})+ \\eta_{2} \\par tial_{ \\mu} \\omega_{ \\nu}+ \\eta_{3}( \\par tial_{ \\mu}a_{ \\nu}+ \\frac{1}{2} \\epsilon_{ab}e_{ \\mu}^{a}e_{ \\nu}^{b}) \\Bigr) \\,.", "UniMER-1M_0002378": "r_{2}", "UniMER-1M_0002371": "k=0,1", "UniMER-1M_0002397": "u", "UniMER-1M_0002386": "S_{L}", "UniMER-1M_0002381": "\\langle0| \\chi^{ \\dagger} \\psi|c \\bar{c} \\rangle=2m_{ \\mathrm{pole}} \\eta^{ \\dagger} \\xi \\;.", "UniMER-1M_0002399": "\\DeltaT=T_{int}-T_{ext}=0.3", "UniMER-1M_0002401": "8 \\, \\mathrm{s}", "UniMER-1M_0002375": "474", "UniMER-1M_0002357": "\\langleS_{f_{r}} \\rangle_{+} \\equiv \\frac{S_{+}^{f_{r}}+S_{-}^{f_{r}}}{2}= \\eta_{f_{r}} \\left[ \\frac{2 \\,x_{f_{r}} \\mathrm{cos} \\delta_{f_{r}} \\mathrm{sin} \\gamma \\left\\{ \\mathrm{cos} \\phi_{q}-x_{f_{r}}^{2} \\mathrm{cos}( \\phi_{q}+2 \\gamma) \\right\\}}{(1+x_{f_{r}}^{2})^{2}-(2 \\,x_{f_{r}} \\mathrm{cos} \\delta_{f_{r}} \\mathrm{cos} \\gamma)^{2}} \\right]", "UniMER-1M_0002391": "- \\pi", "UniMER-1M_0002406": "1.05", "UniMER-1M_0002369": "\\rho_{M}= \\frac{B^{2}}{2( \\betaB^{2}+1)}= \\frac{q_{m}^{2}}{2(r^{4}+ \\betaq_{m}^{2})}.", "UniMER-1M_0002408": "h", "UniMER-1M_0002393": "Q<0", "UniMER-1M_0002389": "z=22", "UniMER-1M_0002377": "\\mathbb{E}_{ \\{X_{i} \\}_{i=1}^{n}} \\left( \\left|G( \\theta) \\right|^{2} \\right) \\leq \\frac{Z_{ \\theta}^{6}}{Z^{6}} \\left(| \\nabla_{ \\theta}L( \\theta)|^{2}+ \\frac{C^{ \\prime}}{n} \\right) \\,,", "UniMER-1M_0002394": "0", "UniMER-1M_0002352": "\\begin{array}{c}{{{ \\theta^{(j)}(z)= \\sqrt{-1} \\omega^{j/2}t^{n(1/2-j/n)^{2}}u^{-1+2j/n}(t^{2n};t^{2n})_{ \\infty}(t^{2j}u^{2};t^{2n})_{ \\infty}(t^{2(n-j)}u^{-2};t^{2n})_{ \\infty},}}} \\\\ {{{h(z)= \\sqrt{-1}t^{n/4} \\displaystyle \\frac{(t^{2n};t^{2n})_{ \\infty}^{3}}{(t^{2};t^{2})_{ \\infty}^{2}}u^{-1}(u^{2};t^{2})_{ \\infty}(t^{2}u^{-2};t^{2})_{ \\infty},}}} \\end{array}", "UniMER-1M_0002414": "\\rho_{i}", "UniMER-1M_0002415": "\\epsilon", "UniMER-1M_0002366": "L_{ \\mathrm{{gap}}} \\,= \\,25 \\, \\mathrm{~- -~} \\,40 \\, \\mathrm{{mm}}", "UniMER-1M_0002358": "(I_{ \\mathrm{pred}},I_{ \\mathrm{past}})=(0,0)", "UniMER-1M_0002360": "E=(m_{ \\mathrm{i}}-m_{ \\mathrm{f}}-m_{ \\mathrm{p}})c^{2}", "UniMER-1M_0002388": "N \\timesN", "UniMER-1M_0002403": "S", "UniMER-1M_0002374": "\\tilde{Z}( \\nu,z)=2 \\pi \\int_{-1}^{1} \\tilde{I}( \\nu, \\tau, \\theta) \\  \\mathrm{cos} \\theta \\ d \\mathrm{cos} \\theta.", "UniMER-1M_0002384": "O(p)", "UniMER-1M_0002383": "(t^{*},s^{*}),(0 \\leqt^{*} \\leq4,0 \\leqs^{*} \\leq1)", "UniMER-1M_0002407": "\\tilde{w}_{N_{s}}", "UniMER-1M_0002400": "(x+1)(8x^{3}-4x^{2}-4x+1)=0", "UniMER-1M_0002392": "\\begin{array}{r}{{g(x)= \\frac{1+ \\sum_{n=1}^{5}A_{n}x^{n}}{1+ \\sum_{n=1}^{6}B_{n}x^{n}},}} \\end{array}", "UniMER-1M_0002356": "\\varphi_{ijkl}=(1/3!) \\epsilon_{ijklmnr} \\psi_{mnr},", "UniMER-1M_0002425": "800 \\Gamma", "UniMER-1M_0002387": "Ch= \\frac{ \\sigmaB_{0}^{2}H^{2}}{ \\rho_{0} \\nu},", "UniMER-1M_0002355": "^{70} \\mathrm{Ge}(n, \\gamma) \\rightarrow \\ ^{71} \\mathrm{Ge} \\xrightarrow{ \\mathrm{~E~C~}} \\ ^{71} \\mathrm{Ga}", "UniMER-1M_0002432": "F", "UniMER-1M_0002395": "| \\alpha( \\mathbf{E}_{ \\mathrm{{inc}}}, \\omega, \\mathbf{k}_{ \\mathrm{{inc}}})|^{2}", "UniMER-1M_0002404": "^3 \\mathrm{He}+p \\rightarrow^{4} \\mathrm{He}+e^{+}+ \\nu_{e}", "UniMER-1M_0002373": "\\begin{array}{rlr}{{ \\Delta \\nu_{x}(a_{x},a_{y},a_{z})}}&{{=}}&{{C_{SC} \\frac{R}{2 \\sqrt{2} \\gamma \\sigma_{z} \\epsilon_{x}}{ \\Big\\langle} \\int_{0}^{1}du \\; \\mathrm{exp}[- \\frac{a_{z}^{2}u}{4}]I_{0} \\left( \\frac{a_{z}^{2}u}{4} \\right) \\mathrm{exp}[- \\frac{a_{x}^{2}u}{4}] \\left[I_{0}( \\frac{a_{x}^{2}u}{4})-I_{1}( \\frac{a_{x}^{2}u}{4}) \\right]}} \\\\ &&{{ \\times \\mathrm{exp}[- \\frac{a_{y}^{2}u}{4}] \\left[ \\frac{1}{[( \\sigma_{y}^{2}/ \\sigma_{x}^{2}-1)u+1]} \\right]^{1/2}I_{0} \\left( \\frac{a_{y}^{2}}{4} \\frac{u}{(1- \\sigma_{x}^{2}/ \\sigma_{y}^{2})u+ \\sigma_{x}^{2}/ \\sigma_{y}^{2}} \\right){ \\Big\\rangle_{s}}}} \\\\ && \\end{array}", "UniMER-1M_0002380": "e^{ \\nabla_{1}} \\nabla_{2}e^{- \\nabla_{1}}= \\nabla_{2},", "UniMER-1M_0002417": "\\begin{array}{rl}{{w_{1}}}&{{= \\mathrm{~L~N~}(w)|F( \\bar{x}_{k})}} \\\\ {{w_{2}}}&{{= \\mathrm{~M~H~A~}(w_{1},w_{1},w_{1})}} \\\\ {{w_{3}}}&{{=w+ \\mathrm{~L~i~n~e~a~r~}(w_{2})}} \\\\ {{w_{4}}}&{{= \\mathrm{~L~N~}(w_{3})}} \\\\ {{w_{5}}}&{{= \\mathrm{~M~L~P~}(w_{4})}} \\\\ {{w_{6}}}&{{=w_{3}+w_{5}}} \\end{array}", "UniMER-1M_0002364": "\\begin{array}{rl}&{{ \\iint_{ \\mathbb{T}^{d} \\times \\mathbb{R}^{d}}H(f^{k+1},v) \\,dxdv+ \\int_{0}^{t} \\iint_{ \\mathbb{T}^{d} \\times \\mathbb{R}^{d}}H(f^{k+1},v) \\,dxdvds- \\int_{0}^{t} \\int_{ \\mathbb{T}^{d}} \\frac{1}{2} \\rho_{f^{k}}^{ \\varepsilon}|u_{f^{k}}^{ \\varepsilon}|^{2}+ \\frac{3}{2}( \\rho_{f^{k}}^{ \\varepsilon})^{ \\gamma} \\,dxds}} \\\\ &{{= \\iint_{ \\mathbb{T}^{d} \\times \\mathbb{R}^{d}}H(f_{ \\varepsilon,0},v) \\,dxdv,}} \\end{array}", "UniMER-1M_0002410": "\\tau", "UniMER-1M_0002402": "\\Delta \\mathbf{r}_{i}= \\mathbf{r}_{i}- \\mathbf{R}", "UniMER-1M_0002420": "\\phi", "UniMER-1M_0002418": "0.05", "UniMER-1M_0002421": "^{+0.41}_{-0.34}", "UniMER-1M_0002396": "\\begin{array}{rlr}{{ \\varphi_{4}( \\vec{r}, \\vec{r}_{0})}}&{{=}}&{{{ \\textstyle \\frac{1}{4}}kr_{g} \\bigg\\{ \\Big\\{ \\Big({ \\calT}_{1111}^{ \\prime}+{ \\calT}_{2222}^{ \\prime}-6{ \\calT}_{1122}^{ \\prime} \\Big) \\mathrm{cos}4 \\phi_{ \\xi}+4 \\Big({ \\calT}_{1112}^{ \\prime}-{ \\calT}_{1222}^{ \\prime} \\Big) \\mathrm{sin}4 \\phi_{ \\xi} \\Big\\} \\times}} \\end{array}", "UniMER-1M_0002422": "K[H_{n},1]= \\left( \\frac{m \\omega \\sqrt{ \\xi \\xi_{0}}}{i \\hbar \\mathrm{sin} \\omegat} \\right) \\left( \\frac{m \\omega \\sqrt{ \\eta \\eta_{0}}}{i \\hbar \\mathrm{sin} \\omegat} \\right)J_{n} \\left( \\frac{m \\omega \\sqrt{ \\xi \\xi_{0}}}{i \\hbar \\mathrm{sin} \\omegat} \\right)J_{n} \\left( \\frac{m \\omega \\sqrt{ \\eta \\eta_{0}}}{i \\hbar \\mathrm{sin} \\omegat} \\right) \\nonumber", "UniMER-1M_0002424": "y= \\mathrm{~P~o~i~s~s~o~n~}(I_{ \\mathrm{~P~}}(q)).", "UniMER-1M_0002411": "for", "UniMER-1M_0002438": "\\tau=0", "UniMER-1M_0002444": "\\kappa", "UniMER-1M_0002412": "N_{x}+N_{z}+ \\mathrm{max} \\left\\{N_{x},N_{z} \\right\\}", "UniMER-1M_0002443": "\\gamma=0", "UniMER-1M_0002439": "n", "UniMER-1M_0002454": "10", "UniMER-1M_0002442": "\\nabla \\times \\mathbf{B}={ \\frac{1}{c^{2}}}{ \\frac{ \\par tial \\mathbf{E}}{ \\par tialt}}+ \\mu_{0} \\mathbf{j}_{ \\mathrm{e}}", "UniMER-1M_0002449": "\\begin{array}{r}{{ \\varphi_{Y}(x, \\alpha, \\beta)= \\frac{ \\Gamma( \\alpha+ \\beta)}{ \\Gamma( \\alpha)+ \\Gamma( \\beta)}x^{ \\alpha-1}(1-x)^{ \\beta-1}.}} \\end{array}", "UniMER-1M_0002437": "\\tau_{ee}/ \\tau_{ii} \\sim \\tau_{ii}/ \\tau_{ie} \\sim(m_{e}/m_{i})^{1/2}", "UniMER-1M_0002419": "\\left( \\begin{array}{c}{{ \\check{ \\mathbf{C}}_{i+m,1}}} \\\\ {{ \\check{ \\mathbf{C}}_{i+m,2}}} \\\\ {{ \\check{ \\mathbf{C}}_{i+m,3}}} \\\\ {{ \\check{ \\mathbf{C}}_{i+m,4}}} \\\\ {{ \\check{ \\mathbf{C}}_{i+m,5}}} \\end{array} \\right)= \\left( \\begin{array}{ccccc}{{ \\frac{Kq^{2}}{4c^{2}}+ \\frac{q_{n}}{2c}}}&{{- \\left( \\frac{K}{2c^{2}}u+ \\frac{n_{x}}{2c} \\right)}}&{{- \\left( \\frac{K}{2c^{2}}v+ \\frac{n_{y}}{2c} \\right)}}&{{- \\left( \\frac{K}{2c^{2}}w+ \\frac{n_{z}}{2c} \\right)}}&{{ \\frac{K}{2c^{2}}}} \\\\ {{1- \\frac{Kq^{2}}{2c^{2}}}}&{{ \\frac{Ku}{c^{2}}}}&{{ \\frac{Kv}{c^{2}}}}&{{ \\frac{Kw}{c^{2}}}}&{{- \\frac{K}{c^{2}}}} \\\\ {{-q_{l}}}&{{l_{x}}}&{{l_{y}}}&{{l_{z}}}&{{0}} \\\\ {{-q_{m}}}&{{m_{x}}}&{{m_{y}}}&{{m_{z}}}&{{0}} \\\\ {{ \\frac{Kq^{2}}{4c^{2}}- \\frac{q_{n}}{2c}}}&{{- \\left( \\frac{K}{2c^{2}}u- \\frac{n_{x}}{2c} \\right)}}&{{- \\left( \\frac{K}{2c^{2}}v- \\frac{n_{y}}{2c} \\right)}}&{{- \\left( \\frac{K}{2c^{2}}w- \\frac{n_{z}}{2c} \\right)}}&{{ \\frac{K}{2c^{2}}}} \\end{array} \\right) \\left( \\begin{array}{c}{{ \\check{ \\mathbf{U}}_{i+m,1}}} \\\\ {{ \\check{ \\mathbf{U}}_{i+m,2}}} \\\\ {{ \\check{ \\mathbf{U}}_{i+m,3}}} \\\\ {{ \\check{ \\mathbf{U}}_{i+m,4}}} \\\\ {{ \\check{ \\mathbf{U}}_{i+m,5}}} \\end{array} \\right)", "UniMER-1M_0002430": "\\zeta_{0}(x_{n})- \\delta_{ \\pi}x_{n} \\zeta_{1}(x_{n})=0 \\,,", "UniMER-1M_0002440": "y", "UniMER-1M_0002423": "\\epsilon_{s}", "UniMER-1M_0002436": "l(t_{c}) \\simeqw+{ \\frac{ \\sigma^{2}}{k_{n}}} \\left\\{ \\left({ \\frac{t_{w}}{ \\kappaM_{p}}} \\right)^{ \\frac{3}{2}}- \\left({ \\frac{t_{c}}{ \\kappaM_{p}}} \\right)^{ \\frac{3}{2}} \\right\\},", "UniMER-1M_0002450": "q_{e}", "UniMER-1M_0002416": "1- \\mathrm{~F~A~R~}=g", "UniMER-1M_0002465": "t=10", "UniMER-1M_0002409": "{ \\bf F}_{ \\mathrm{drag}}= \\intd{ \\bf r} \\left[({ \\bf u} \\cdot \\nabla){ \\bf u}+ \\nabla({p}/{ \\rho})- \\nu \\nabla^{2}{ \\bf u} \\right].", "UniMER-1M_0002456": "- \\pi", "UniMER-1M_0002458": "\\sigma", "UniMER-1M_0002459": "l=m+1", "UniMER-1M_0002445": "\\Omega", "UniMER-1M_0002428": "\\par tial_{2n+1},n \\geq0, \\, \\; \\mathrm{ \\boldmath~ \\ varsigma~}= \\sum_{n \\geq0}(n+ \\frac{1}{2})t_{2n+1} \\par tial_{2n+1}", "UniMER-1M_0002460": "_{3}", "UniMER-1M_0002453": "\\mathrm{D}_{ \\mathrm{{L}}} \\oplus \\delta_{ \\mathrm{{R}}}={ \\left[ \\begin{array}{ll}{{ \\mathrm{D}_{ \\mathrm{{L}}}}}&{{0}} \\\\ {{0}}&{{ \\delta_{ \\mathrm{{R}}}}} \\end{array} \\right]}=i{ \\left[ \\begin{array}{ll}{{I}}&{{0}} \\\\ {{0}}&{{I}} \\end{array} \\right]} \\par tial_{t}+i{ \\left[ \\begin{array}{ll}{{- \\sigma^{k}}}&{{0}} \\\\ {{0}}&{{ \\sigma^{k}}} \\end{array} \\right]} \\nabla_{k}+m{ \\left[ \\begin{array}{ll}{{ \\eta \\omegaK}}&{{0}} \\\\ {{0}}&{{- \\eta \\omegaK}} \\end{array} \\right]}", "UniMER-1M_0002433": "\\mathcal{A}=1.35", "UniMER-1M_0002455": "\\alpha", "UniMER-1M_0002452": "V_{0}", "UniMER-1M_0002435": "\\begin{array}{rlr}{{ \\mathcal{R}( \\tau^{ \\prime})}}&{{=}}&{{ \\mathcal{M}_{ \\bf u}e^{ \\hat{ \\kappa}_{ \\bf d} \\tau^{ \\prime}}- \\mathcal{M}_{ \\bf d}e^{- \\hat{ \\kappa}_{ \\bf u}( \\tau_{c}- \\tau^{ \\prime})}}} \\end{array}", "UniMER-1M_0002448": "\\nablac_{+}=- \\frac{ \\sigma}{D_{+}z_{+}F} \\nabla \\Phi", "UniMER-1M_0002413": "\\begin{array}{r}{{d_{ \\mathrm{p}}= \\frac{ \\sqrt{S_{1}^{2}+S_{2}^{2}+S_{3}^{2}}}{S_{0}},}} \\end{array}", "UniMER-1M_0002467": "2 \\times1", "UniMER-1M_0002482": "u_{0}", "UniMER-1M_0002466": "m \\timesK", "UniMER-1M_0002472": "\\hat{k}", "UniMER-1M_0002471": "d_{W}^{2}( \\rho_{k}, \\rho_{k+1})", "UniMER-1M_0002431": "\\begin{array}{rl}{{ \\frac{ \\par tialn}{ \\par tialt}}}&{{=G-k_{2}(np-n_{i}^{2})-C_{nT}+E_{nT}}} \\\\ {{ \\frac{ \\par tialp}{ \\par tialt}}}&{{=G-k_{2}(np-n_{i}^{2})-C_{pT}+E_{pT}}} \\\\ {{ \\frac{ \\par tialn_{T}}{ \\par tialt}}}&{{=C_{nT}-E_{nT}-C_{pT}+E_{pT}}} \\end{array}", "UniMER-1M_0002427": "\\pm \\left[(+1,0,0,0,0, \\,0,+1,0,0,0, \\,0, . . .,0) \\ominus(0,+1,0,0,0, \\,+1,0,0,0,0, \\,0, . . .,0) \\right]", "UniMER-1M_0002457": "m_{e}", "UniMER-1M_0002429": "\\begin{array}{rl}{{b_{k+1}^{+}=}}&{{ \\int_{0}^{+ \\infty} \\frac{ \\bar{ \\theta}^{(k+1)/2}}{(k+1)!} \\left[ \\frac{ \\xi}{ \\sqrt{ \\bar{ \\theta}}}H_{k} \\left( \\frac{ \\xi}{ \\sqrt{ \\bar{ \\theta}}} \\right)-kH_{k-1} \\left( \\frac{ \\xi}{ \\sqrt{ \\bar{ \\theta}}} \\right) \\right] \\mathcal{E}_{tr,m}( \\xi)d \\xi}} \\\\ {{=}}&{{ \\int_{0}^{+ \\infty} \\frac{ \\bar{ \\theta}^{k/2}}{(k+1)!} \\xiH_{k} \\left( \\frac{ \\xi}{ \\sqrt{ \\bar{ \\theta}}} \\right) \\mathcal{E}_{tr,m}( \\xi)d \\xi- \\frac{ \\bar{ \\theta}}{k+1}b_{k-1}^{+}}} \\\\ {{=}}&{{ \\int_{0}^{+ \\infty} \\frac{ \\bar{ \\theta}^{k/2}}{(k+1)!}( \\xi-u_{m})H_{k} \\left( \\frac{ \\xi}{ \\sqrt{ \\bar{ \\theta}}} \\right) \\mathcal{E}_{tr,m}( \\xi)d \\xi+ \\frac{u_{m}}{k+1}b_{k}^{+}- \\frac{ \\bar{ \\theta}}{k+1}b_{k-1}^{+}.}} \\end{array}", "UniMER-1M_0002469": "-56", "UniMER-1M_0002491": "M", "UniMER-1M_0002451": "\\boldsymbol{a}", "UniMER-1M_0002446": "\\theta= \\pi/4", "UniMER-1M_0002494": "^+", "UniMER-1M_0002495": "\\mathcal{J}", "UniMER-1M_0002474": "\\delta_{ \\mathrm{sf}}=2 \\pi(n_{ \\mathrm{s}}-n_{ \\mathrm{f}})d/ \\lambda= \\pi/2", "UniMER-1M_0002473": "z", "UniMER-1M_0002468": "+ \\boldsymbol{n}", "UniMER-1M_0002480": "\\lceil{ \\frac{N}{ \\lfloor{N_{t}/W_{L}} \\rfloor}} \\rceil", "UniMER-1M_0002441": "\\begin{array}{rl}{{v_{pqrs} \\approx}}&{{ \\sum_{Q}^{N_{aux}}[K_{pq}^{Q}]^{L}[K_{rs}^{Q}]^{L}+ \\langleR_{pq}^{L}R_{rs}^{S} \\rangle_{N_{s}}+ \\langleR_{pq}^{S}R_{rs}^{L} \\rangle_{N_{s}}}} \\\\ &{{+ \\langleR_{pq}^{S}R_{rs}^{S} \\rangle_{N_{s}}~.}} \\end{array}", "UniMER-1M_0002464": "\\overline{{{u_{j}^{ \\prime} \\omega^{ \\prime}}}}- \\overline{{{u_{j}^{ \\prime}}}}~ \\overline{{{ \\omega^{ \\prime}}}} \\approx \\sigma_{j}^{KEB}= \\overline{{{ \\overline{{{u_{j}^{ \\prime}}}}~ \\overline{{{ \\omega^{ \\prime}}}}}}}- \\overline{{{ \\overline{{{u_{j}^{ \\prime}}}}}}}~ \\overline{{{ \\overline{{{ \\omega^{ \\prime}}}}}}},", "UniMER-1M_0002502": "\\approx", "UniMER-1M_0002477": "q_{r}=-110/N_{r}", "UniMER-1M_0002483": "\\psi \\ ^{ \\prime}( \\mathrm{~{~ \\bf~r~}~,~t~})=S( \\mathrm{~{~ \\bf~r~}~,~t~}) \\psi( \\mathrm{~{~ \\bf~r~}~,~t~})", "UniMER-1M_0002505": "C= \\frac{4mL^{2}}{ \\pi^{2} \\eta^{2}}.", "UniMER-1M_0002398": "\\begin{array}{rl}{{ \\langle(Su_{ \\gamma}-y_{ \\mathrm{max}})_{-},g_{ \\varepsilon}(Su_{ \\gamma}-y_{ \\mathrm{max}}) \\rangle_{ \\mathcal{Y}^{*}, \\mathcal{Y}}}}&{{= \\int_{D} \\int_{ \\mathrm{min} f}^{0} \\int_{ \\mathbb{R}^{d-1}}fg_{ \\varepsilon}(f)J^{-1} \\pi( \\xi_{1}(f)) . . . \\pi( \\xi_{d})d \\xi_{2} . . .d \\xi_{d}dfdx}} \\\\ &{{ \\ge \\int_{D} \\int_{- \\infty}^{0}fg_{ \\varepsilon}(f) \\frac{1}{c}Pdfdx}} \\\\ &{{ \\ge-|D|P \\frac{1}{c} \\varepsilon^{3},}} \\end{array}", "UniMER-1M_0002479": "\\{1,2 \\}", "UniMER-1M_0002485": "| \\Deltaz| \\approx \\lambda_{ \\mathrm{~t~h~}}", "UniMER-1M_0002461": "\\begin{array}{rl}{{ \\int_{ \\mathcal{L}} \\mathrm{d} \\boldsymbol{v} \\, \\frac{ \\omega}{ \\omega- \\boldsymbol{k} \\cdot \\boldsymbol{v}} \\left( \\frac{ \\boldsymbol{k} \\cdot \\boldsymbol{v}}{ \\omega} \\right)^{2}f_{i0}( \\boldsymbol{v})}}&{{= \\int_{ \\mathcal{L}} \\mathrm{d} \\boldsymbol{v} \\, \\left( \\frac{ \\boldsymbol{k} \\cdot \\boldsymbol{v}- \\omega+ \\omega}{ \\omega- \\boldsymbol{k} \\cdot \\boldsymbol{v}} \\right) \\left( \\frac{ \\boldsymbol{k} \\cdot \\boldsymbol{v}}{ \\omega} \\right)f_{i0}( \\boldsymbol{v})}} \\\\ &{{= \\int_{ \\mathcal{L}} \\mathrm{d} \\boldsymbol{v} \\, \\frac{ \\boldsymbol{k} \\cdot \\boldsymbol{v}}{ \\omega- \\boldsymbol{k} \\cdot \\boldsymbol{v}} \\,f_{i0}( \\boldsymbol{v})}} \\\\ &{{=n_{i} \\,[ \\xi( \\omega, \\boldsymbol{k})-1] \\,.}} \\end{array}", "UniMER-1M_0002462": "S(u,O(t, \\mathbf{x}))= \\eta_{O} \\, \\big(u- \\varphi_{O}(O(t, \\mathbf{x})) \\big)^{2},", "UniMER-1M_0002486": "k", "UniMER-1M_0002489": "^{ \\circ}", "UniMER-1M_0002476": "\\widetilde{ \\nu}_{e^{-}}=P \\frac{E+E_{c}}{2E_{c}} \\left(1-exp \\left(-L \\frac{2E_{c}}{7300[kV]} \\right) \\right)", "UniMER-1M_0002488": "\\omega/2 \\pi \\approx2.501 \\,c_{T}/(2 \\piR) \\approx4.652 \\, \\mathrm{GHz}", "UniMER-1M_0002497": "{ \\textbf{y}}(t)={ \\left[ \\begin{array}{llll}{{n_{4}}}&{{n_{3}}}&{{n_{2}}}&{{n_{1}}} \\end{array} \\right]}{ \\textbf{x}}(t).", "UniMER-1M_0002498": "\\begin{array}{rl}{{ \\{F,G \\}}}&{{= \\left\\langleq, \\frac{ \\deltaF}{ \\deltau} \\cdot \\frac{ \\deltaG}{ \\deltau}^{ \\perp} \\right\\rangle- \\left\\langle \\nabla \\cdot \\frac{ \\deltaF}{ \\deltau}, \\frac{ \\deltaG}{ \\deltaD} \\right\\rangle+ \\left\\langle \\nabla \\cdot \\frac{ \\deltaG}{ \\deltau}, \\frac{ \\deltaF}{ \\deltaD} \\right\\rangle}} \\\\ &{{ \\qquad+ \\left\\langle \\nabla \\frac{ \\deltaF}{ \\deltaZ^{ \\prime}},q \\frac{ \\deltaG}{ \\deltau} \\right\\rangle- \\left\\langle \\nabla \\frac{ \\deltaG}{ \\deltaZ^{ \\prime}},q \\frac{ \\deltaF}{ \\deltau} \\right\\rangle,}} \\end{array}", "UniMER-1M_0002434": "\\begin{array}{r}{{ \\ell_{ \\operatorname{QC}} \\!= \\int \\! \\bigg( \\rho_{c} \\Big( \\dot{S}+( \\nablaS- \\mathcal{A}) \\cdot \\mathcal{X}+ \\langle \\phi, \\widehat{H}_{e} \\phi \\rangle+ \\frac{1}{2} \\|p \\|_{g^{-1}}^{2} \\Big)+ \\mathcal{O}( \\sqrt{ \\mu}) \\bigg){ \\mathrm{d}}r{ \\mathrm{d}}p,}} \\end{array}", "UniMER-1M_0002499": "x", "UniMER-1M_0002500": "<< \\sum_{n=1}^{N}p_{i}>>=M_{D}<<u_{i}>>=0,", "UniMER-1M_0002470": "P_{ \\mathrm{{S \\ emt}}}=4 \\piR_{ \\mathrm{{S}}}^{2} \\sigmaT_{ \\mathrm{{S}}}^{4} \\qquad \\qquad(1)", "UniMER-1M_0002463": "\\begin{array}{rl}{{ \\textstyle \\mathfrak{N}_{ \\theta}(x)}}&{{= \\textstyle \\theta_{ \\mathfrak{d}}+ \\theta_{(d_{ \\mathrm{in}}+1)(d+1)+1} \\bigl( \\theta_{d_{ \\mathrm{in}}(d+1)+1}+ \\sum_{i=1}^{d_{ \\mathrm{in}}} \\theta_{i}x_{i} \\bigr)}} \\\\ &{{ \\quad \\textstyle+ \\sum_{j=1}^{d} \\theta_{(d_{ \\mathrm{in}}+1)(d+1)+j+1} \\mathrm{max} \\{ \\theta_{d_{ \\mathrm{in}}(d+1)+j+1}+ \\sum_{i=1}^{d_{ \\mathrm{in}}} \\theta_{jd_{ \\mathrm{in}}+i}x_{i},0 \\},}} \\end{array}", "UniMER-1M_0002501": "s_{12}^{E} \\simeq \\frac{Y_{12}}{Y_{22}-Y_{23}Y_{32}} \\ , \\qquad s_{13}^{E} \\simeqY_{13} \\ ,", "UniMER-1M_0002481": "\\begin{array}{rl}{{ \\dot{a}=}}&{{-i \\Delta_{a}a- \\gamma_{a}a-ig_{N}c+ \\sqrt{2 \\gamma_{a}}a_{ \\mathrm{in}},}} \\\\ {{ \\dot{c}=}}&{{-i \\Delta_{c}c- \\kappa_{c}c+ig_{c}cq-ig_{N}a+E+ \\sqrt{2 \\kappa_{c}}c_{ \\mathrm{in}},}} \\\\ {{ \\dot{m}=}}&{{-i \\Delta_{m}m- \\kappa_{m}m-ig_{m}mq+ \\Omega_{d}+ \\sqrt{2 \\kappa_{m}}m_{ \\mathrm{in}},}} \\\\ {{ \\dot{q}=}}&{{ \\  \\omega_{b}p, \\, \\, \\, \\, \\, \\dot{p}=- \\omega_{b}q- \\gamma_{b}p+g_{c}c^{ \\dagger}c-g_{m}m^{ \\dagger}m+ \\xi,}} \\end{array}", "UniMER-1M_0002504": "\\begin{array}{r}{{ \\{( \\mu_{j,S_{01}}, . . ., \\mu_{j,S_{16}}) \\}_{j}= \\{(f_{S_{01}}^{ \\mathrm{C}}( \\mu_{j,S_{01}}), . . .,f_{S_{16}}^{ \\mathrm{C}}( \\mu_{j,S_{16}})) \\}_{j} \\cup \\{(f_{S_{01}}^{ \\mathrm{D}}( \\mu_{j,S_{01}}), . . .,f_{S_{16}}^{ \\mathrm{D}}( \\mu_{j,S_{16}})) \\}_{j}.}} \\end{array}", "UniMER-1M_0002509": "{ \\mathbf{J}}={ \\mathbf{L}}+{ \\mathbf{S}}", "UniMER-1M_0002528": "e_{2}", "UniMER-1M_0002510": "\\begin{array}{r}{{ \\big| \\eta_{i} \\tilde{ \\Omega}_{jk}+ \\eta_{i} \\tilde{ \\Omega}_{sk}+ \\eta_{k} \\tilde{ \\Omega}_{ji}+ \\eta_{k} \\tilde{ \\Omega}_{si} \\big| \\lesssim \\theta_{i} \\beta_{j} \\theta_{j} \\beta_{k} \\theta_{k}+ \\theta_{i} \\beta_{s} \\theta_{s} \\beta_{k} \\theta_{k}+ \\theta_{k} \\beta_{j} \\theta_{j} \\beta_{i} \\theta_{i}+ \\theta_{k} \\beta_{s} \\theta_{s} \\beta_{i} \\theta_{i}.}} \\end{array}", "UniMER-1M_0002511": "\\begin{array}{rl}{{ \\hat{ \\mu}_{k}}}&{{=(1- \\lambda) \\hat{ \\mu}_{k-1}+ \\lambda \\frac{n_{k}}{ \\Deltat_{k}}}} \\\\ {{ \\hat{ \\sigma}_{k}^{2}}}&{{=(1- \\lambda) \\hat{ \\sigma}_{k-1}^{2}+ \\lambda \\left( \\frac{n_{k}}{ \\Deltat_{k}}- \\hat{ \\mu}_{k-1} \\right)^{2}.}} \\end{array}", "UniMER-1M_0002484": "\\left( \\mathcal{L}_{i, \\,j}^{ \\mathrm{~e~d~g~e~}} \\equiv \\mathcal{A}_{f, \\,i, \\,j}^{ \\mathrm{~f~a~c~e~}} \\right)", "UniMER-1M_0002508": "\\beta_{0}", "UniMER-1M_0002533": "T=1.8", "UniMER-1M_0002534": "H", "UniMER-1M_0002478": "\\begin{array}{rl}{{L}}&{{=2a \\in[0, \\infty).}} \\end{array}", "UniMER-1M_0002493": "1_{F}", "UniMER-1M_0002514": "{ \\calL}_{i}=- \\frac{N_{c}}{2 \\pi^{2}g^{2}}{ \\frac{2}{f_{ \\pi}}} \\varepsilon^{ \\mu \\nu \\alpha \\beta}K_{ \\mu}^{*+} \\par tial_{ \\beta}K^{+} \\{{ \\frac{1}{2}} \\par tial_{ \\nu} \\rho_{ \\alpha}^{0}+{ \\frac{1}{2}} \\par tial_{ \\nu} \\omega_{ \\alpha}+{ \\frac{ \\sqrt{2}}{2}} \\par tial_{ \\nu} \\phi_{ \\alpha} \\}.", "UniMER-1M_0002487": "\\begin{array}{rl}{{ \\frac{d}{dt} \\mathcal{A}_{ \\mathrm{~p~h~}}}}&{{=-i( \\tilde{ \\omega}_{ \\mathrm{~c~a~v~}}- \\tilde{ \\omega}_{ \\mathrm{~L~}}) \\mathcal{A}_{ \\mathrm{~p~h~}}-ig_{0} \\mathrm{cos} \\varphi \\mathrm{sin} \\varphi \\cdot \\sqrt{( \\tilde{n}_{ \\mathrm{~L~}}- \\tilde{n}_{a})} \\cdotb,}} \\\\ {{ \\frac{d}{dt}b}}&{{=-i \\Omega_{ \\mathrm{~v~}}b-ig_{0} \\mathrm{cos} \\varphi \\mathrm{sin} \\varphi \\sqrt{( \\tilde{n}_{ \\mathrm{~L~}}- \\tilde{n}_{a})} \\cdot \\mathcal{A}_{ \\mathrm{~p~h~}}.}} \\end{array}", "UniMER-1M_0002539": "M=0.1", "UniMER-1M_0002490": "E= \\etaJ", "UniMER-1M_0002515": "\\rangle", "UniMER-1M_0002496": "d_{t} \\mathbf{x}= \\left[ \\begin{array}{l}{{d_{t}E_{1}}} \\\\ {{ \\vdots}} \\\\ {{d_{t}E_{K}}} \\\\ {{d_{t}I_{1}}} \\\\ {{ \\vdots}} \\\\ {{d_{t}I_{K}}} \\end{array} \\right]=f( \\mathbf{x})-w( \\mathbf{x})= \\left[ \\begin{array}{l}{{ \\Lambda_{1}S_{1}}} \\\\ {{ \\vdots}} \\\\ {{ \\Lambda_{K}S_{K} \\ }} \\\\ {{0}} \\\\ {{ \\vdots}} \\\\ {{0}} \\end{array} \\right]- \\left[ \\begin{array}{l}{{ \\PsiE_{1}}} \\\\ {{ \\vdots}} \\\\ {{ \\PsiE_{K}}} \\\\ {{ \\GammaI_{1}- \\PsiE_{1}}} \\\\ {{ \\vdots}} \\\\ {{ \\GammaI_{K}- \\PsiE_{K}}} \\end{array} \\right]", "UniMER-1M_0002517": "\\begin{array}{l}{{{ \\displaystyle \\displaystyle \\tilde{ \\calQ}_{ \\alpha}^{1}=(ip_{ \\alpha \\beta} \\tilde{W}^{ \\beta}-mW_{ \\alpha})[1+ \\mathrm{q}^{cl}(bP_{3}- \\sqrt{1-b^{2}} \\,P_{2}-P_{4})]}}} \\\\ {{{ \\displaystyle \\tilde{ \\calQ}_{ \\alpha}^{2}=(ip_{ \\alpha \\beta} \\tilde{V}^{ \\beta}-mV_{ \\alpha})[1+ \\mathrm{q}^{cl}(bP_{3}+ \\sqrt{1-b^{2}} \\,P_{2}-P_{4})] \\,.}}} \\end{array}", "UniMER-1M_0002516": "T \\setminus \\{v_{0} \\}", "UniMER-1M_0002531": "z=-h", "UniMER-1M_0002524": "T_{ \\mathrm{~i~n~t~}} \\llT_{ \\mathrm{~L~T~}}", "UniMER-1M_0002532": "\\delta \\,{ \\calG}= \\frac{1}{2} \\,{ \\calG} \\,g_{ \\mu \\nu} \\, \\deltag^{ \\mu \\nu}", "UniMER-1M_0002521": "A", "UniMER-1M_0002549": "l^{*}", "UniMER-1M_0002541": "V= \\{V_{ \\gamma}| \\gamma< \\delta< \\beta \\}", "UniMER-1M_0002544": "N \\approx6 \\times10^{4}", "UniMER-1M_0002552": "I", "UniMER-1M_0002537": "( \\nabla^{ \\mu} \\par tial_{ \\mu}+m^{2}+ \\xiR) \\phi=0.", "UniMER-1M_0002506": "\\begin{array}{rlr}&{{f}}&{{( \\mathbf{x}_{b},t+h)}} \\\\ &{{=}}&{{e^{- \\nuh} \\left[f^{M}( \\mathbf{x}_{b},t)- \\tauD_{t}f^{M}( \\mathbf{x}_{b},t) \\right]}} \\\\ &{{+}}&{{( \\gamma^{r}-e^{- \\nuh})f^{M}( \\mathbf{x}_{b},t)}} \\\\ &{{-}}&{{h \\mathbf{v} \\cdot \\left(e^{- \\nuh} \\nablaf^{M}( \\mathbf{x}_{b},t)+( \\gamma^{r}-e^{- \\nuh}) \\nablaf^{M}( \\mathbf{x}_{b},t) \\right)}} \\\\ &{{+}}&{{(1- \\gamma^{r}) \\left[f^{M}( \\mathbf{x}_{b},t)+h \\par tial_{t}f^{M}( \\mathbf{x}_{b},t) \\right]+O(h^{2})+O( \\par tial^{2})}} \\\\ &{{=}}&{{f^{M}( \\mathbf{x}_{b},t)- \\tauD_{t}f^{M}( \\mathbf{x}_{b},t)}} \\\\ &&{{+h \\par tial_{t}f^{M}( \\mathbf{x}_{b},t)+O(h^{2})+O( \\par tial^{2})}} \\end{array}", "UniMER-1M_0002526": "\\begin{array}{rl}{{ \\mathbf{D}=}}&{{ \\left\\{ \\left[ \\begin{array}{lll}{{1}}&{{0}}&{{0}} \\\\ {{0}}&{{1}}&{{0}} \\\\ {{0}}&{{0}}&{{1}} \\end{array} \\right]+ \\frac{ \\mathrm{sin}^{2}| \\mathbf{r}|}{| \\mathbf{r}|^{2}} \\left[ \\begin{array}{ccc}{{0}}&{{-r_{3}}}&{{r_{2}}} \\\\ {{r_{3}}}&{{0}}&{{-r_{1}}} \\\\ {{-r_{2}}}&{{r_{1}}}&{{0}} \\end{array} \\right] \\right.}} \\end{array}.", "UniMER-1M_0002512": "\\begin{array}{rl}{{d \\rho_{t}}}&{{=- \\rho_{t}^{2} \\left((- \\rho_{t}+2h)dt+ \\sqrt{2}dB_{t} \\right)- \\frac{ \\rho_{t}}{v_{t}} \\par tial_{u} \\alpha_{t}dt+2 \\rho_{t}^{3}dt}} \\\\ &{{=- \\rho_{t}^{2} \\left((-3 \\rho_{t}+2h)dt+ \\sqrt{2}dB_{t} \\right)- \\frac{ \\rho_{t}}{v_{t}} \\par tial_{u} \\alpha_{t}dt}} \\\\ &{{=- \\frac{ \\rho_{t}}{v_{t}} \\par tial_{u} \\left( \\frac{- \\par tial_{u} \\rho_{t}}{v_{t} \\rho_{t}} \\right)dt- \\rho_{t}^{2} \\left((-3 \\rho_{t}+2h)dt+ \\sqrt{2}dB_{t} \\right).}} \\end{array}", "UniMER-1M_0002548": "\\alpha", "UniMER-1M_0002545": "{ \\calL}=- \\kappa \\dot{B} \\left(a \\mp \\frac{1}{ \\kappa}B^{ \\prime} \\right)+i \\psi^{ \\ast} \\dot{ \\psi}-A_{0}( \\kappaB^{ \\prime}+ \\rho)- \\frac{1}{2m}|( \\par tial_{x}-ia) \\psi|^{2}- \\frac{1}{2m}B^{2} \\rho", "UniMER-1M_0002560": "a", "UniMER-1M_0002520": "\\begin{array}{rl}&{{ \\mathrm{log} \\mathbb{E}_{ \\varepsilon} \\mathrm{exp} \\left\\{ \\lambda \\operatorname{sup}_{w \\in \\mathcal{W}[0,r]}P_{n} \\varepsilon \\big(f(w,Z)-f(w^{*},Z) \\big) \\right\\}}} \\\\ &{{ \\leqslant64 \\lambdaLr \\sqrt{ \\frac{d}{n}}+ \\frac{B^{2} \\lambda^{2}e^{B \\lambda/n}}{2n} \\left( \\frac{128Lr}{B} \\sqrt{ \\frac{d}{n}}+ \\frac{L^{2}r^{2}}{B^{2}} \\right).}} \\end{array}", "UniMER-1M_0002562": "\\textbf{X}", "UniMER-1M_0002536": "k \\ne1", "UniMER-1M_0002492": "\\sum_{i=0}^{k} \\ A_{i}^{(k)}(t,{ \\bf b},{ \\bf z}){ \\calR}_{-t-i}({ \\bf b},{ \\bf z}) \\ = \\ 0", "UniMER-1M_0002564": "\\approx", "UniMER-1M_0002546": "m=1", "UniMER-1M_0002551": "\\alpha \\equiv \\mathrm{det}_{ \\alpha} \\{ \\varphi_{i}^{ \\mathrm{DF}} \\}", "UniMER-1M_0002518": "\\pi_{i}=p_{i}+i \\lambda \\sum_{j}^{ \\prime}{ \\frac{1}{(x_{i}-x_{j})}}M_{ij}", "UniMER-1M_0002507": "\\langleI_{ \\alpha} \\rangle= \\frac{2e^{2}}{h} \\sum_{ \\beta}V_{ \\beta} \\intdE \\left(- \\frac{ \\par tialf}{ \\par tialE} \\right) \\bigg[N_{ \\alpha} \\delta_{ \\alpha \\beta}-Tr(s_{ \\alpha \\beta}^{ \\dagger}s_{ \\alpha \\beta}) \\bigg].", "UniMER-1M_0002571": "\\mathrm{d} t", "UniMER-1M_0002572": "A", "UniMER-1M_0002554": "| \\bar{S}|= \\sqrt{2 \\bar{S}_{ij} \\bar{S}_{ij}}", "UniMER-1M_0002550": "\\left\\langle \\theta^{ \\mu} \\frac{ \\par tial \\mathcal{H}}{ \\par tial \\theta^{ \\mu}} \\right\\rangle= \\frac{1}{Z} \\int \\mathrm{d}^{n} \\theta \\int \\mathrm{d}^{n}p \\: \\mathrm{exp}(- \\beta \\mathcal{H}( \\theta,p)) \\theta^{ \\mu} \\frac{ \\par tial \\mathcal{H}}{ \\par tial \\theta^{ \\mu}}=- \\frac{1}{ \\betaZ} \\int \\mathrm{d}^{n} \\theta \\int \\mathrm{d}^{n}p \\: \\theta^{ \\mu} \\frac{ \\par tial}{ \\par tial \\theta^{ \\mu}} \\mathrm{exp}(- \\beta \\mathcal{H}( \\theta,p))= \\frac{n}{ \\beta}", "UniMER-1M_0002503": "G(p,p_{ \\perp})= \\frac{f(p)}{p^{2}+p_{ \\perp}^{2}+<PERSON>^{2}}.", "UniMER-1M_0002563": "x=0", "UniMER-1M_0002538": "\\begin{array}{rl}&{{ \\mathbf{E}( \\mathbf{r})=Z \\sum_{j,m} \\left[ia_{E}(j,m) \\mathbf{N}_{jm}^{h}( \\mathbf{r})+a_{M}(j,m) \\mathbf{M}_{jm}^{h}( \\mathbf{r}) \\right],}} \\\\ &{{iZ \\mathbf{H}( \\mathbf{r})= \\frac{1}{k} \\nabla \\times \\mathbf{E}( \\mathbf{r}).}} \\end{array}", "UniMER-1M_0002555": "c=- \\mathrm{ln}( \\varepsilon^{2}e)", "UniMER-1M_0002529": "\\rho_{i}^{2}=(T_{i}/m_{i})/ \\Omega_{ci}^{2}", "UniMER-1M_0002547": "\\lvertE_{ \\mathrm{AA}} \\rvert> \\lvertE_{ \\mathrm{BB}} \\rvert> \\lvertE_{ \\mathrm{AB}} \\rvert", "UniMER-1M_0002565": "h(t) \\propto \\Omega^{1/4}t^{5/12}", "UniMER-1M_0002543": "E_{ \\mathrm{cr}}=m^{2}c^{3}/(|e| \\hbar) \\ggE_{0}", "UniMER-1M_0002553": "{ \\frac{ \\par tialF(x_{i},r)}{ \\par tialr}} \\;= \\;0 \\;.", "UniMER-1M_0002556": "\\gamma=m-n", "UniMER-1M_0002558": "x_{2}=r \\mathrm{sin} \\theta", "UniMER-1M_0002569": "k", "UniMER-1M_0002557": "m \\approx{ \\sqrt{2}} \\Delta \\phi.", "UniMER-1M_0002580": "U= \\mathrm{exp} \\left({ \\frac{2i \\tau^{a} \\pi^{a}}{f}} \\right) \\in \\mathrm{SU(2)},", "UniMER-1M_0002573": "U( \\varphi) \\stackrel{G}{ \\to}g_{R}U( \\varphi)g_{L}^{-1}~.", "UniMER-1M_0002566": "\\hat{H}_{ \\mathrm{ \\ eta-spin}}(t)=J_{ \\eta,XY}(t) \\sum_{(ij)}( \\hat{ \\eta}_{i}^{x} \\hat{ \\eta}_{j}^{x}+ \\hat{ \\eta}_{i}^{y} \\hat{ \\eta}_{j}^{y})+J_{ \\eta,Z}(t) \\sum_{(ij)} \\hat{ \\eta}_{i}^{z} \\hat{ \\eta}_{j}^{z}+B_{x} \\sum_{i}(-)^{i} \\eta_{i}^{x}+B_{z} \\sum_{i}(-)^{i} \\eta_{i}^{z}", "UniMER-1M_0002570": "\\theta_{i}", "UniMER-1M_0002576": "\\mu(x(t+ \\tau)|x(t))", "UniMER-1M_0002582": "\\left[g_{op}( \\mathrm{ \\bf~r}), \\mathrm{ \\bf~A}_{op}( \\mathrm{ \\bf~r}^{ \\prime}) \\right]= \\left[- \\epsilon_{0} \\nabla \\cdot \\mathrm{ \\bf~E}_{op}( \\mathrm{ \\bf~r}), \\mathrm{ \\bf~A}_{op}( \\mathrm{ \\bf~r}^{ \\prime}) \\right]=-i \\hbar \\nabla_{ \\mathrm{ \\bf~r}} \\delta( \\mathrm{ \\bf~r}- \\mathrm{ \\bf~r}^{ \\prime})", "UniMER-1M_0002540": "B_{10}^{-}= \\sum_{i} \\big(2r_{i}^{2}p_{i,z}-z_{i}( \\mathbf{r}_{i} \\cdot \\mathbf{p}_{i}) \\big),", "UniMER-1M_0002584": "Q_{ \\lambda( \\kappa; \\sigma)}-Q_{ \\kappa( \\lambda; \\sigma)}+ \\eta_{ \\sigma[ \\lambda}Q_{ \\kappa]}{^ \\delta}_{; \\delta}=0", "UniMER-1M_0002581": "\\par tial_{-}T_{++}^{X}=-f \\par tial_{+}f \\  \\  \\ , \\  \\  \\  \\par tial_{+}T_{- -}^{X}=-f \\par tial_{-}f", "UniMER-1M_0002522": "\\theta^{0i}( \\vec{n},1) \\to \\theta^{0i}( \\vec{n},1)+b^{i}( \\vec{n})", "UniMER-1M_0002519": "\\begin{array}{rl}{{r_{1}}}&{{= \\varepsilon^{4} \\int_{0}^{ \\varepsilon^{-3}} \\sum_{x=1}^{ \\infty} \\phi_{ \\varepsilon}^{2}( \\varepsilon^{2}x) \\nabla^{+}Z_{s}(x) \\nabla^{-}Z_{s}(x)ds}} \\\\ {{r_{2}}}&{{= \\varepsilon^{4} \\int_{ \\varepsilon^{-3}}^{ \\varepsilon^{-4}t} \\sum_{x=1}^{ \\infty} \\phi_{ \\varepsilon}^{2}( \\varepsilon^{2}x) \\nabla^{+}Z_{s}(x) \\nabla^{-}Z_{s}(x)ds.}} \\end{array}", "UniMER-1M_0002574": "E_{ \\vartheta}( \\boldsymbol{r}_{d}- \\boldsymbol{r}_{i}, \\omega)", "UniMER-1M_0002561": "\\begin{array}{rl}{{ \\frac{1}{2 \\mathrm{{Pr}}} \\frac{d}{dt} \\|u \\|_{2}^{2}}}&{{ \\leq- \\| \\nablau \\|_{2}^{2}- \\int_{ \\gamma^{+} \\cup \\gamma^{-}}(2 \\alpha+ \\kappa)u_{ \\tau}^{2}+ \\epsilon \\|u_{2} \\|_{2}^{2}+ \\frac{4}{ \\epsilon}| \\Omega| \\mathrm{{Ra}}^{2} \\,.}} \\end{array}", "UniMER-1M_0002535": "d_{2}(f(x),f(y)) \\leqKd_{1}(x,y) \\quad{ \\mathrm{for~all}} \\quad x,y \\inM_{1}.", "UniMER-1M_0002585": "\\bar{p}_{ \\mathrm{WW}}= \\frac{(1-e_{1})a_{ \\mathrm{W}}^{ \\mathrm{BD}}+e_{1}a_{ \\mathrm{W}}^{ \\mathrm{BC}}}{1- \\{(1-e_{1})(a_{ \\mathrm{W}}^{ \\mathrm{GC}}-a_{ \\mathrm{W}}^{ \\mathrm{BD}})+e_{1}(a_{ \\mathrm{W}}^{ \\mathrm{GD}}-a_{ \\mathrm{W}}^{ \\mathrm{BC}}) \\}}.", "UniMER-1M_0002597": "D^{i}= \\omega^{ij}(a)( \\par tial_{j}+ \\beta \\par tial_{j}H)", "UniMER-1M_0002605": "\\mu", "UniMER-1M_0002607": "X_{ \\tau}=C_{ \\tau} \\times_{1}U_{ \\tau_{1}} \\times_{2}U_{ \\tau_{2}}", "UniMER-1M_0002587": "\\mathinner{ \\varepsilon_{e}^{ \\perp} \\mathopen{ \\left(k_{1} \\right)}}", "UniMER-1M_0002542": "\\Phi(V_{ \\mathrm{1}})+ \\Phi(V_{ \\mathrm{2}})= \\Phi(V)", "UniMER-1M_0002588": "|N(v) \\capB(u,l^{ \\prime})|> \\frac{c^{ \\star}}{ \\beta(1- \\epsilon)^{ \\alpha}}=c", "UniMER-1M_0002575": "r_{0}", "UniMER-1M_0002530": "u(z)= \\left\\{ \\begin{array}{rlr}{{ \\frac{u_{ \\tau, \\mathrm{bot}}}{ \\kappa} \\mathrm{ln} \\left( \\frac{z}{z_{0, \\mathrm{bot}}} \\right),}}&&{{ \\mathrm{if} \\ z \\leqz_{ \\mathrm{lim}},}} \\\\ {{ \\frac{u_{ \\tau, \\mathrm{up}}}{ \\kappa} \\mathrm{ln} \\left( \\frac{z}{z_{0, \\mathrm{up}}} \\right),}}&&{{ \\mathrm{if} \\ z>z_{ \\mathrm{lim}}.}} \\end{array} \\right.", "UniMER-1M_0002525": "\\begin{array}{rl}{{ \\nabla^{2} \\Psi^{*}( \\mathbf{w}_{t})}}&{{= \\left( \\nabla^{2} \\Psi( \\nabla \\Psi^{*}( \\mathbf{w}_{t})) \\right)^{-1}}} \\\\ &{{= \\mathrm{Diag}( \\mathrm{exp}(w_{t,1})^{-1}, . . ., \\mathrm{exp}(w_{t,K})^{-1})^{-1}}} \\\\ &{{= \\mathrm{Diag}( \\mathrm{exp}(w_{t,1}), . . ., \\mathrm{exp}(w_{t,K})),}} \\end{array}", "UniMER-1M_0002567": "\\gamma^{a} \\gamma^{3}+ \\gamma^{3} \\gamma^{a}=0, \\qquad", "UniMER-1M_0002591": "Q=C^{a} \\omega_{a}-{ \\frac{1}{2}}C^{b}C_{c}U_{ab}^{c}-P^{a} \\pi_{a},", "UniMER-1M_0002578": "\\begin{array}{rl}{{0 \\longrightarrow \\mathrm{Ext}_{R}^{1}( \\widetilde{H}_{ \\mathrm{f}}^{2}(G_{ \\mathbb{Q}, \\Sigma},T, \\Delta),R/I) \\longrightarrow}}&{{ \\mathrm{Hom}_{R/I}( \\widetilde{H}_{ \\mathrm{f}}^{1}(G_{ \\mathbb{Q}, \\Sigma},T/IT, \\Delta),R/I)}} \\\\ &{{ \\longrightarrow \\mathrm{Hom}_{R}( \\widetilde{H}_{ \\mathrm{f}}^{1}(G_{ \\mathbb{Q}, \\Sigma},T, \\Delta),R/I) \\longrightarrow \\mathrm{Ext}_{R}^{2}( \\widetilde{H}_{ \\mathrm{f}}^{2}(G_{ \\mathbb{Q}, \\Sigma},T, \\Delta),R/I) \\longrightarrow0.}} \\end{array}", "UniMER-1M_0002594": "k", "UniMER-1M_0002595": "H(Y)= \\mathrm{log} \\Delta", "UniMER-1M_0002600": "5^{ \\circ}", "UniMER-1M_0002603": "12.1", "UniMER-1M_0002602": "\\prod_{ \\stackrel{j{=}0}{j \\neqa,b}}^{l-1}", "UniMER-1M_0002583": "\\Tilde{C}_{+}^{vv}( \\omega)= \\int_{0}^{ \\infty} \\mathrm{d} t \\mathrm{e}^{i \\omegat}C^{vv}(t)", "UniMER-1M_0002612": "\\left( \\left( \\sum_{n=1}^{ \\infty}a_{n} \\mathrm{sin}( \\mathbf{m}n \\varphi), \\sum_{n=1}^{ \\infty}c_{n} \\mathrm{sin}( \\mathbf{m}n \\varphi) \\right) \\Big| \\left( \\sum_{n=1}^{ \\infty}b_{n} \\mathrm{sin}( \\mathbf{m}n \\varphi), \\sum_{n=1}^{ \\infty}d_{n} \\mathrm{sin}( \\mathbf{m}n \\varphi) \\right) \\right)_{2} \\triangleq \\sum_{n=1}^{ \\infty}a_{n}b_{n}+c_{n}d_{n}.", "UniMER-1M_0002599": "\\omega_{i}", "UniMER-1M_0002589": "x^{k}+y^{k} \\leq(x+y)^{k+2}", "UniMER-1M_0002593": "t_{C}< \\mathrm{min}[t_{A_{1}},t_{A_{2}},t_{B}]", "UniMER-1M_0002527": "\\begin{array}{r}{{ \\mathcal{F}_{2} \\Big( \\mathbf{D}_{u,k} \\big( \\tilde{ \\chi}_{2} \\mathcal{F}_{1}f(t,k, \\cdot) \\big) \\Big)(t,k, \\xi_{1})= \\int \\mathcal{D}(t,k, \\xi_{1}, \\xi_{2}) \\hat{f}_{k}(t, \\xi_{2})d \\xi_{2}.}} \\end{array}", "UniMER-1M_0002613": "\\frac{33833}{2592} \\frac{( \\Omega_{C})^{8}}{V^{7}}=0.018V", "UniMER-1M_0002606": "\\gamma", "UniMER-1M_0002596": "{ \\mathbf{I} \\;= \\;-{ \\mathcal{I} \\;.", "UniMER-1M_0002611": "\\omega", "UniMER-1M_0002616": "\\begin{array}{rl}{{ \\mu^{(n)}(t)}}&{{= \\iint \\! \\! . . . \\! \\! \\int_{- \\infty}^{ \\infty} \\alpha^{(n)}(t-t_{1},t-t_{2}, . . .,t-t_{n})}} \\end{array}", "UniMER-1M_0002615": "s", "UniMER-1M_0002590": "S_{21}=S_{12}", "UniMER-1M_0002623": "E_{F}", "UniMER-1M_0002598": "1<R/R_{c}< \\sigma( \\sigma+b+3)/( \\sigma-b-1)", "UniMER-1M_0002601": "P_{X}(t)= \\sum{ \\mathrm{rank}}(H^{n}(X))t^{n}", "UniMER-1M_0002609": "\\pi", "UniMER-1M_0002592": "\\begin{array}{rlr}{{ \\Delta \\chi_{ \\mathrm{DUNE}}^{2}(V_{ \\alpha \\beta}, \\delta_{ \\mathrm{CP}})}}&{{=}}&{{ \\underset{ \\{{ \\operatorname}{sin}^{2} \\theta_{23}, \\left\\vert \\Deltam_{31}^{2} \\right\\vert,o \\}}{ \\mathrm{min}} \\left[ \\chi_{ \\mathrm{DUNE}}^{2}(V_{ \\alpha \\beta}, \\boldsymbol{ \\theta},o)- \\chi_{ \\mathrm{DUNE,min}}^{2} \\right] \\;,}} \\\\ {{ \\Delta \\chi_{ \\mathrm{DUNE}}^{2}(V_{ \\alpha \\beta}, \\mathrm{sin}^{2} \\theta_{23})}}&{{=}}&{{ \\underset{ \\{{ \\delta}_{ \\mathrm{CP}}, \\left\\vert \\Deltam_{31}^{2} \\right\\vert,o \\}}{ \\mathrm{min}} \\left[ \\chi_{ \\mathrm{DUNE}}^{2}(V_{ \\alpha \\beta}, \\boldsymbol{ \\theta},o)- \\chi_{ \\mathrm{DUNE,min}}^{2} \\right] \\;,}} \\end{array}", "UniMER-1M_0002579": "\\begin{array}{rl}{{[Q_{2},P_{1}]}}&{{= \\{^{q_{2}}(t_{1},q_{1})(t_{1},q_{1})^{-1} \\midq_{2} \\inQ_{2},(t_{1},q_{1}) \\inP_{1} \\}}} \\\\ &{{= \\{(t_{1},^{q_{2}}q_{1})(t_{1},q_{1})^{-1} \\midq_{2} \\inQ_{2},(t_{1},q_{1}) \\inP_{1} \\}}} \\\\ &{{= \\{(1,^{q_{2}}q_{1}q_{1}^{-1}) \\midq_{2} \\inQ_{2},(t_{1},q_{1}) \\inP_{1} \\}}} \\end{array}", "UniMER-1M_0002622": "V_{c-p}=- \\frac{ \\alpha}{2r^{4}}", "UniMER-1M_0002586": "\\sum_{n=1}^{ \\infty}{ \\frac{ \\mu(n)}{ \\sqrt{n}}}g( \\mathrm{log} n)= \\sum_{ \\gamma}{ \\frac{h( \\gamma)}{ \\zeta^{ \\prime}(1/2+i \\gamma)}}+2 \\sum_{n=1}^{ \\infty}{ \\frac{(-1)^{n}(2 \\pi)^{2n}}{(2n)! \\zeta(2n+1)}} \\int_{- \\infty}^{ \\infty}g(x)e^{-x(2n+1/2)} \\,dx,", "UniMER-1M_0002617": "d", "UniMER-1M_0002647": "\\omega_{j}", "UniMER-1M_0002633": "G", "UniMER-1M_0002632": "x", "UniMER-1M_0002626": "\\approx20~ \\up mu", "UniMER-1M_0002645": "\\omega", "UniMER-1M_0002653": "\\sigma>0", "UniMER-1M_0002648": "U_{p,k}={I}_{k}/4 \\omega_{k}^{2}", "UniMER-1M_0002634": "\\frac{ \\par tial}{ \\par tialv_{T}}P(1,0)=- \\frac{aN_{T}}{N_{I}+N_{T}}<0", "UniMER-1M_0002644": "[ \\Delta] \\frac{d \\hat{ \\sigma}_{ \\gammab}(s,p_{T})}{dp_{T}}= \\int_{w_{m}}^{1} \\frac{dw}{w} \\frac{2p_{T}}{s \\sqrt{1-w_{m}/w}} \\left\\{[ \\Delta] \\frac{d \\hat{ \\sigma}}{dvdw}(v=v_{+})+[ \\Delta] \\frac{d \\hat{ \\sigma}}{dvdw}(v=v_{-}) \\right\\},", "UniMER-1M_0002650": "b", "UniMER-1M_0002641": "3", "UniMER-1M_0002646": "\\begin{array}{rl}{{ \\Phi_{k}^{(v)}(x)}}&{{ \\equiv \\sum_{r=0}^{k-2} \\sum_{z=r}^{ \\infty}{ \\frac{(z+1)P^{(v)}(z+1)}{ \\langlez \\rangle^{(v)}}} \\binom{z}{r}(1-x)^{r}x^{z-r}}} \\\\ &{{= \\sum_{r=0}^{k-2}{ \\frac{(1-x)^{r}}{r!}}{ \\frac{ \\mathrm{d}^{r}}{ \\mathrm{d} x^{r}}}G_{1}^{(v)}(x),}} \\\\ {{ \\Phi_{q}^{(e)}(x)}}&{{ \\equiv \\sum_{s=0}^{q-2} \\sum_{n=s}^{ \\infty}{ \\frac{(n+1)P^{(e)}(n+1)}{ \\langlen \\rangle^{(e)}}} \\binom{n}{s}(1-x)^{s}x^{n-s}}} \\\\ &{{= \\sum_{s=0}^{q-2}{ \\frac{(1-x)^{s}}{s!}}{ \\frac{ \\mathrm{d}^{s}}{ \\mathrm{d} x^{s}}}G_{1}^{(e)}(x)}} \\end{array}", "UniMER-1M_0002610": "E_{av}= \\frac{1}{ \\pi} \\int_{0}^{ \\pi}E \\;d \\theta \\;", "UniMER-1M_0002661": "\\beta_{1}", "UniMER-1M_0002631": "\\longrightarrow", "UniMER-1M_0002628": "1000", "UniMER-1M_0002608": "N_{ \\mathrm{full}} \\approx3.54 \\times10^{6}", "UniMER-1M_0002624": "S= \\sum_{i}f(P_{i}^{*}) \\mu(V_{i})", "UniMER-1M_0002643": "S_{i}= \\langle \\sigma_{i} \\rangle= \\langleu_{n, \\mathbf{k}}| \\sigma_{i}|u_{n, \\mathbf{k}} \\rangle.", "UniMER-1M_0002639": "[ \\par tialk_{s}/ \\par tial \\omega- \\par tialk_{i}/ \\par tial \\omega]_{ \\omega= \\omega_{p/2}}", "UniMER-1M_0002656": "\\nu= \\frac{1}{d-1} \\  \\,(1 \\!< \\!d \\!< \\!3), \\  \\  \\  \\  \\nu= \\frac{1}{2} \\  \\,(d \\geq3).", "UniMER-1M_0002670": "C:{ \\mathcal{X}} \\rightarrow \\Sigma^{*}", "UniMER-1M_0002614": "A", "UniMER-1M_0002672": "D_{max}", "UniMER-1M_0002658": "h", "UniMER-1M_0002636": "D \\neqf(x),", "UniMER-1M_0002630": "\\begin{array}{rl}{{ \\frac{ \\rhou^{*,n+1}- \\rhou^{n}}{ \\Deltat}}}&{{+ \\nabla \\pi^{*,n+ \\frac{1}{2}}=- \\nabla \\cdot( \\rhouu^{T})^{n}}} \\\\ {{ \\nabla \\cdotu^{*,n+1}}}&{{=0}} \\end{array}", "UniMER-1M_0002637": "\\epsilon=10^{-6}", "UniMER-1M_0002620": "V \\inI", "UniMER-1M_0002642": "\\overline{{{ \\mathbf{T}_{ \\mathfrak{u}} \\mathbb{P}_{L}}}}/ \\mathcal{G}= \\mathbf{T}_{ \\mathfrak{p}} \\left[ \\mathbb{P}_{L}/{ \\mathfrak{F} \\mathrm{~ \\textbf{~}{~o~l~}~}}( \\mathcal{G}) \\right]", "UniMER-1M_0002664": "\\mathrm{ \\bf As}^{ \\prime} \\Gamma(p_{ \\Gamma}, \\kappa)= \\sum_{n} \\kappa^{n} \\Gamma_{n}^{ \\prime}(p_{ \\Gamma}, \\kappa),", "UniMER-1M_0002649": "=W^{(1)} \\left(x_{u}+W^{(1)+}W^{(2)} \\frac{1}{N} \\sum_{r \\in \\mathcal{Z}_{w} \\backslashu} \\psi \\left(x_{u},x_{r} \\right) \\right)+W^{(2)} \\left( \\frac{1}{N} \\sum_{q \\in \\mathcal{M}_{w}} \\left( \\sum_{v \\in \\mathcal{Z}_{q}} \\psi \\left(x_{u},x_{v} \\right) \\right) \\right) \\quad,", "UniMER-1M_0002669": "v \\,= \\,a \\bigl( \\delta \\varphi+{ \\frac{{ \\dot{ \\varphi}}}{H}} \\Phi \\bigr) \\equivaQ \\,,", "UniMER-1M_0002652": "\\| \\mu \\|_{ba}= \\operatorname*{sup}_{A \\in \\Sigma}| \\mu|(A)", "UniMER-1M_0002621": "E_{a} \\ ^{ \\mu}=e^{-A/2} \\delta_{a} \\ ^{ \\mu}~, \\qquad E_{ \\mu}^{ \\theta}=-e^{-A/2}W_{ \\mu}~,", "UniMER-1M_0002654": "N=5", "UniMER-1M_0002687": "Q=1", "UniMER-1M_0002627": "\\begin{array}{rl}{{ \\mathbb{P}_{n}=}}&{{{ \\bf C}i^{n/2-1} \\int_{0}^{+ \\infty} \\frac{d( \\omega \\tau)}{( \\omega \\tau)^{D/2}}J_{n/2}[ \\sqrt{ \\kappa} \\Lambda^{2} \\frac{U_{ \\mathrm{p}}}{ \\hbar \\omega} \\omega \\tau \\gamma( \\omega \\tau) \\alpha( \\omega \\tau)]}} \\\\ &{{e^{i(n/2)( \\eta- \\varphi)} \\mathrm{exp} \\{i[ \\mathbb{S}^{( \\tau)}( \\omega \\tau)+n/2] \\omega \\tau \\}.}} \\end{array}", "UniMER-1M_0002663": "\\Omega_{[ \\mu \\nu \\rho \\sigma]}^{4}= \\left( \\omega^{+} \\right)^{4} \\varepsilon_{ \\mu \\nu \\rho \\sigma}tr \\, \\phi^{2}.", "UniMER-1M_0002655": "\\delta=(k_{L} \\cdotk_{ \\gamma})/(k_{L} \\cdotp) \\approx \\varepsilon_{ \\gamma}/ \\varepsilon_{e}", "UniMER-1M_0002692": "\\Gamma", "UniMER-1M_0002635": "i=4", "UniMER-1M_0002625": "Re_{ \\epsilon}{ \\epsilon} \\left( \\frac{{ \\par tial} \\bar{u}_{z}}{{ \\par tial} \\bar{t}}+ \\bar{u}_{r} \\frac{{ \\par tial} \\bar{u}_{z}}{{ \\par tial} \\bar{r}}+ \\bar{u}_{z} \\frac{{ \\par tial} \\bar{u}_{z}}{{ \\par tial} \\bar{z}} \\right)=- \\frac{{ \\par tial} \\bar{p}}{{ \\par tial} \\bar{z}}+{ \\epsilon} \\left( \\frac{{ \\par tial}^{2} \\bar{u}_{z}}{{ \\par tial} \\bar{z}^{2}}+ \\frac{ \\epsilon}{ \\bar{r}} \\frac{{ \\par tial}}{{ \\par tial} \\bar{r}} \\left( \\bar{r} \\frac{{ \\par tial} \\bar{u}_{z}}{{ \\par tial} \\bar{r}} \\right) \\right)", "UniMER-1M_0002695": "v(r)", "UniMER-1M_0002696": "l", "UniMER-1M_0002697": "(L,m,n_{z})", "UniMER-1M_0002657": "\\sigma_{T(L)}: \\left( \\begin{array}{llll}{{x_{1}}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{x_{2}}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{x_{3}}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{x_{4}}} \\end{array} \\right) \\mapsto \\left( \\begin{array}{llll}{{ \\sigma(x_{4})^{-1}}}&{{0}}&{{0}}&{{0}} \\\\ {{0}}&{{ \\sigma(x_{3})^{-1}}}&{{0}}&{{0}} \\\\ {{0}}&{{0}}&{{ \\sigma(x_{2})^{-1}}}&{{0}} \\\\ {{0}}&{{0}}&{{0}}&{{ \\sigma(x_{1})^{-1}}} \\end{array} \\right).", "UniMER-1M_0002700": "\\ell=m=1", "UniMER-1M_0002660": "\\beta", "UniMER-1M_0002677": "z= \\mathrm{100 \\  \\ mum}", "UniMER-1M_0002666": "R=a_{0}+a_{1}R_{1}(1)+ . . .+a_{m}R_{m}(1)=a_{1}A_{1}(1)+ . . .+a_{m}A_{m}(1).", "UniMER-1M_0002681": "\\pi/2", "UniMER-1M_0002665": "q=- \\frac{1}{3} \\frac{ \\par tialp_{l}}{ \\par tialx}( \\zeta- \\xi)^{3}+ \\frac{1}{2} \\left( \\epsilon^{2}C_{l} \\right)^{-1} \\frac{ \\par tial \\sigma}{ \\par tialx}( \\zeta- \\xi)^{2}+H( \\zeta- \\xi) \\bigg( \\frac{ \\par tialb_{2}}{ \\par tialt}-H \\frac{ \\par tialb_{1}}{ \\par tialt} \\bigg).", "UniMER-1M_0002667": "f(L)", "UniMER-1M_0002707": "q", "UniMER-1M_0002708": "V", "UniMER-1M_0002629": "\\begin{array}{r}{{ \\begin{array}{rl}{{M_{1}( \\lambda; \\vec{P})}}&{{:=- \\lambdaP_{1}+(1+ \\lambda)P_{2}+ \\frac{1}{2} \\sum_{k \\ge3}P_{k},}} \\\\ {{M_{2}( \\lambda; \\vec{P})}}&{{:=- \\lambdaP_{1}^{ \\prime}+(1+ \\lambda)P_{2}^{ \\prime}+ \\frac{1}{2} \\sum_{k \\ge3}P_{k}^{ \\prime}=(1+ \\lambda)P_{1}- \\lambdaP_{2}+ \\frac{1}{2} \\sum_{k \\ge3}P_{k},}} \\end{array}}} \\end{array}", "UniMER-1M_0002710": "5", "UniMER-1M_0002711": "{ \\bf u}", "UniMER-1M_0002690": "\\mathcal{T}_{j}=( \\mathcal{Q}_{j},W_{j})", "UniMER-1M_0002712": "^ \\mathrm{ \\ textregistered}", "UniMER-1M_0002659": "\\begin{array}{rlr}{{ \\frac{ \\sigma}{M}|_{grain}}}&{{=}}&{{ \\frac{ \\frac{ \\sigma}{M}|_{atom}}{ \\mathrm{~`~`~n~u~m~b~e~r~t~h~i~c~k~n~e~s~s~^{ \\prime}~^{ \\prime}~}}.}} \\end{array}", "UniMER-1M_0002680": "e^{-S_{ \\mathrm{eff}}^{E}(A,Z)} \\;= \\; \\mathrm{det}_{ \\mathrm{ren}} \\left(D_{A,Z} \\right)~,", "UniMER-1M_0002693": "\\begin{array}{rlr}{{ \\Delta \\v{x}}}&{{=}}&{{ \\widehat{f} \\cdot \\v{x}(t)+g \\cdot \\v{v}(t)}} \\\\ {{ \\Delta \\v{v}}}&{{=}}&{{ \\dot{f} \\cdot \\v{x}(t)+ \\widehat{ \\dot{g}} \\cdot \\v{v}(t) \\ ,}} \\end{array}", "UniMER-1M_0002683": "\\Deltav= \\vertv_{1}-v_{2} \\vert", "UniMER-1M_0002674": "s_{N}={ \\sqrt{{ \\frac{1}{N}} \\sum_{i=1}^{N} \\left(x_{i}-{ \\bar{x}} \\right)^{2}}},", "UniMER-1M_0002638": "\\par tial_{yy}u=-{ \\frac{8u_{0}}{H^{2}}} \\,,", "UniMER-1M_0002676": "\\mathcal{Y} \\sim \\em ph{I}^{n}", "UniMER-1M_0002699": "\\mathrm{ln}( \\mathrm{sinh}(z))- \\mathrm{ln}(z)= \\sum_{n=1}^{ \\infty} \\mathrm{ln} \\left({ \\frac{ \\pi^{2}n^{2}+z^{2}}{ \\pi^{2}n^{2}}} \\right)", "UniMER-1M_0002678": "P_{diss}=T_{a}{ \\dot{S}}_{i}", "UniMER-1M_0002704": "\\begin{array}{rl}{{ \\tau_{m} \\dot{R}}}&{{= \\frac{ \\gamma}{ \\pi \\tau_{m}}+2RV- \\hat{g}_{ \\mathrm{~s~y~n~}} \\tau_{m}R^{2} \\;,}} \\\\ {{ \\tau_{m} \\dot{V}}}&{{=V^{2}-( \\pi \\tau_{m}R)^{2}+ \\hat{g}_{ \\mathrm{~s~y~n~}} \\tau_{m}R[E_{ \\mathrm{~s~y~n~}}-V]+I_{0} \\;.}} \\end{array}", "UniMER-1M_0002702": "c_{G}", "UniMER-1M_0002725": "^3", "UniMER-1M_0002705": "P", "UniMER-1M_0002689": "\\begin{array}{rlr}&&{{ \\mathcal{K}(v,w) \\lvertu(v,x,t)-u(w,x,t) \\rvert^{p-2}(u(v,x,t)-u(w,x,t))( \\phi(v,x,t)- \\phi(w,x,t))}} \\\\ &&{{ \\quad=- \\mathcal{K}(v,w) \\lvertu(v,x,t)-u(w,x,t) \\rvert^{p-2}(u(v,x,t)-u(w,x,t))}} \\\\ &&{{ \\quad \\quad \\times((2k-u) \\varphi^{p}(v,x,t)-(2k-u) \\varphi^{p}(w,x,t))}} \\\\ &&{{ \\quad \\ge-c \\lvertv-w \\rvert^{-n-sp}(k-u(w,x,t))^{p-1}((2k-u) \\varphi^{p}(v,x,t)-(2k-u) \\varphi^{p}(w,x,t))}} \\\\ &&{{ \\quad \\geq-ck^{p} \\lvertv-w \\rvert^{-n-sp} \\lvert \\varphi(v,x,t)- \\varphi(w,x,t) \\rvert^{p},}} \\end{array}", "UniMER-1M_0002706": "\\kappa=0", "UniMER-1M_0002684": "\\rho(S_{t},T_{t})", "UniMER-1M_0002730": "50", "UniMER-1M_0002686": "\\begin{array}{r}{{ \\big\\langle{{ \\bf w}_{V}} \\,, \\boldsymbol{ \\nabla} \\cdot{ \\bf G} \\big\\rangle_{ \\Omega_{e}^{h}}= \\big\\langle{{ \\bf w}_{V}} \\,,{ \\bf G} \\cdot{{ \\bf n}}_{e} \\big\\rangle_{ \\Gamma_{e}^{h}}- \\big\\langle \\nabla{{ \\bf w}_{V}} \\,,{ \\bf G} \\big\\rangle_{ \\Omega_{e}^{h}} \\,,}} \\end{array}", "UniMER-1M_0002682": "gl(m|n) \\supsetgl(m|n-1) \\supset . . . \\supsetgl(m) \\supsetgl(m-1) \\supset . . . \\supsetgl(1)", "UniMER-1M_0002703": "\\beta= \\left\\{ \\begin{array}{rlr}&{{0.001 \\left( \\frac{17.3}{Re_{s}}+0.336 \\right) \\frac{ \\rho_{g}| \\textbf{U}_{g}- \\textbf{u}|}{d_{s}} \\epsilon_{s} \\epsilon_{g}^{-1.8},}}&{{ \\epsilon_{g} \\le0.94,}} \\\\ &{{ \\frac{3}{4}C_{d} \\frac{ \\rho_{g} \\epsilon_{s} \\epsilon_{g}| \\textbf{U}_{g}- \\textbf{u}|}{d_{s}} \\epsilon_{g}^{-2.65},}}&{{0.94< \\epsilon_{g} \\le0.99,}} \\\\ &{{ \\frac{3}{4}C_{d} \\frac{ \\rho_{g} \\epsilon_{g}| \\textbf{U}_{g}- \\textbf{u}|}{d_{s}},}}&{{0.99< \\epsilon_{g} \\le1.0,}} \\end{array} \\right.", "UniMER-1M_0002688": "\\frac{ \\mathrm{D}^{s}}{ \\mathrm{D} t}f= \\frac{ \\par tialf}{ \\par tialt}+ \\mathbf{ \\nabla}f \\cdot \\mathbf{v}^{s}", "UniMER-1M_0002731": "\\mathbf{WDW}^{ \\top}= \\mathbf{U} \\boldsymbol{ \\Sigma}^{2}. \\mathbf{U}^{ \\top},", "UniMER-1M_0002733": "\\Lambda", "UniMER-1M_0002671": "\\hat{J}_{x}=(1/2) \\left( \\hat{a}_{L}^{ \\dagger} \\hat{a}_{R}+ \\hat{a}_{L} \\hat{a}_{R}^{ \\dagger} \\right)", "UniMER-1M_0002715": "8 \\times10^{3}", "UniMER-1M_0002714": "p>1", "UniMER-1M_0002679": "(i \\frac{ \\par tial}{ \\par tialx_{i}^{0}}+m_{AB}- \\epsilon_{i}- \\frac{1}{2m_{AB}}{ \\bf \\nabla}_{x_{i}}^{2})f_{B;i}=0.", "UniMER-1M_0002720": "V_{1},V_{2},V_{1}^{def}", "UniMER-1M_0002668": "\\begin{array}{rl}{{ \\Delta \\eta=}}&{{ \\nabla \\cdot( \\nabla \\eta)= \\nabla \\cdot( \\frac{1}{2 \\beta}e^{ \\frac{ \\Phi}{2 \\beta}} \\nabla \\Phi)}} \\\\ {{=}}&{{ \\frac{1}{2 \\beta}( \\nablae^{ \\frac{ \\Phi}{2 \\beta}}, \\nabla \\Phi)+ \\frac{1}{2 \\beta}e^{ \\frac{ \\Phi}{2 \\beta}} \\Delta \\Phi}} \\\\ {{=}}&{{ \\frac{1}{(2 \\beta)^{2}}e^{ \\frac{ \\Phi}{2 \\beta}} \\| \\nabla \\Phi \\|^{2}+ \\frac{1}{2 \\beta}e^{ \\frac{ \\Phi}{2 \\beta}} \\Delta \\Phi}} \\\\ {{=}}&{{ \\frac{1}{2 \\beta^{2}}e^{ \\frac{ \\Phi}{2 \\beta}} \\Big( \\frac{1}{2} \\| \\nabla \\Phi \\|^{2}+ \\beta \\Delta \\Phi \\Big).}} \\end{array}", "UniMER-1M_0002709": "\\begin{array}{r}{{ \\dot{ \\mathbf{K}}= \\mathbf{h}^{-2} \\mathbf{K} \\mathbf{W}^{1, \\top} \\mathbf{G}_{1}^{ \\top} \\mathbf{W}^{1}+ \\mathbf{h}^{-1} \\mathbf{K} \\mathbf{W}^{1, \\top} \\mathbf{G}_{2}^{ \\top} \\mathbf{W}^{1}+ \\mathbf{u}_{m}^ \\textrm{ \\boldmath{g}}^{ \\top} \\mathbf{W}^{1}.}} \\end{array}", "UniMER-1M_0002721": "\\begin{array}{rl}{{ \\vec{v}}}&{{= \\left[ \\begin{array}{l}{{x-C_{x}}} \\\\ {{y-C_{y}}} \\end{array} \\right] \\mathrm{,}~~~~ \\Sigma= \\left[ \\begin{array}{ll}{{S_{xx}}}&{{S_{xy}}} \\\\ {{S_{xy}}}&{{S_{yy}}} \\end{array} \\right] \\mathrm{,}}} \\\\ {{ \\mathrm{and}~~~~ \\xi}}&{{=(A,C_{x},C_{y},S_{xx},S_{xy},S_{yy}).}} \\end{array}", "UniMER-1M_0002717": "\\cap", "UniMER-1M_0002724": "\\gamma \\approx8.5", "UniMER-1M_0002718": "f_{h}", "UniMER-1M_0002722": "\\theta_{0}= \\frac{1}{2} \\xi_{*}+i \\pi_{*}, \\quad \\Sigma_{ \\mu}=m \\xi_{ \\mu}+ \\xi_{*}p_{ \\mu},", "UniMER-1M_0002713": "\\mathrm{Cov}( \\bar{A}_{n}^{( \\alpha)}, \\bar{A}_{n}^{( \\beta)})", "UniMER-1M_0002673": "ds^{2}=l^{2}/z^{2}(-dt^{2}+d{ \\bf x}^{2}+dz^{2}).", "UniMER-1M_0002719": "\\hbar^{2} \\omega^{2}= \\hbar^{2}c^{2}k^{2}+m^{2}c^{4} \\,.", "UniMER-1M_0002716": "V=V(x^{2}+y^{2}-1) \\subseteq \\mathbf{C}^{2},", "UniMER-1M_0002723": "k_{1}=7.048 \\times10^{5}. \\;k_{2}=7.534 \\times10^{5} \\; \\mathrm{m^{3}/(mol \\timess).}", "UniMER-1M_0002726": "\\begin{array}{rlr}{{ \\Psi_{1}(x_{1},x_{2},x_{3},x_{4},x_{5},x_{6})}}&{{=}}&{{C_{0} \\wedgeC_{1} \\wedgeC_{2}}} \\\\ {{C_{0}}}&{{=}}&{{x_{1} \\veex_{2} \\veex_{3}}} \\\\ {{C_{1}}}&{{=}}&{{ \\bar{x}_{1} \\veex_{4}}} \\\\ {{C_{2}}}&{{=}}&{{x_{1} \\veex_{5} \\veex_{6}}} \\end{array}", "UniMER-1M_0002727": "v_{ \\perp}", "UniMER-1M_0002729": "\\begin{array}{r}{{L= \\underbrace{ \\rho \\pi \\frac{c^{2}}{4} \\left(U_{ \\infty} \\dot{ \\theta}+ \\frac{c}{2} \\ddot{ \\theta} \\right)}_{ \\mathrm{~a~d~d~e~d~m~a~s~s~}}+ \\underbrace{ \\rho \\piU_{ \\infty}c \\left(U_{ \\infty} \\theta+ \\frac{3c}{4} \\dot{ \\theta} \\right)}_{ \\mathrm{~q~u~a~s~i~-~s~t~e~a~d~y~}}+ \\underbrace{ \\left[C(k)-1 \\right] \\rho \\piU_{ \\infty}c \\left(U_{ \\infty} \\theta+ \\frac{3c}{4} \\dot{ \\theta} \\right)}_{ \\mathrm{~w~a~k~e~-~i~n~d~u~c~e~d~}},}} \\end{array}", "UniMER-1M_0002732": "{ \\frac{ \\par tial^{2} \\rho}{ \\par tialt^{2}}}-c_{0}^{2} \\nabla^{2} \\rho= \\nabla \\cdot \\left[ \\nabla \\cdot( \\rho \\mathbf{v} \\otimes \\mathbf{v})- \\nabla \\cdot \\sigma+ \\nablap-c_{0}^{2} \\nabla \\rho \\right],", "UniMER-1M_0002728": "\\mathrm{d} \\Xi=-2 \\, \\mathrm{cos} \\left( \\frac{1}{2} \\,{ \\tau} \\right) \\mathrm{sin} \\left( \\frac{1}{2} \\,{ \\tau} \\right) \\mathrm{d}{ \\tau}-2 \\, \\mathrm{cos} \\left( \\frac{1}{2} \\,{ \\Omega} \\right) \\mathrm{sin} \\left( \\frac{1}{2} \\,{ \\Omega} \\right) \\mathrm{d}{ \\Omega}", "UniMER-1M_0002755": "n_{e}", "UniMER-1M_0002761": "\\nu=1", "UniMER-1M_0002701": "\\begin{array}{rlr}{{{ \\bf T}_{ \\mathrm{gc} k} \\; \\equiv \\; \\mathbb{T}_{ \\mathrm{gc}} \\, \\mathrm{ \\boldmath~ \\cdot~} \\, \\par tial_{k}{ \\bf X}}}&{{=}}&{{ \\int_{P}F_{ \\mathrm{gc}} \\, \\dot{ \\bf X} \\, \\Pi_{ \\mathrm{gc} k} \\;- \\; \\left( \\mathrm{ \\boldmath~ \\cal~P~}_{ \\mathrm{gc}} \\,E_{k}+ \\frac{ \\bf B}{4 \\pi} \\,{ \\sf H}_{ \\mathrm{gc} k} \\right) \\;+ \\; \\frac{1}{4 \\pi} \\par tial_{k}{ \\bf X} \\left({ \\bf B} \\, \\mathrm{ \\boldmath~ \\cdot~} \\,{ \\bf H}_{ \\mathrm{gc}} \\;- \\; \\frac{1}{2} \\,|{ \\bf B}|^{2} \\right)}} \\\\ &&{{- \\; \\int_{P}F_{ \\mathrm{gc}} \\left[ \\mathbb{Q}_{ \\mathrm{gc}} \\, \\mathrm{ \\boldmath~ \\cdot~} \\, \\left( \\par tial_{k}{ \\bf E}+ \\frac{{ \\bf u}_{ \\mathrm{E}}}{c} \\, \\mathrm{ \\boldmath~ \\times~} \\, \\par tial_{k}{ \\bf B} \\right) \\;- \\; \\left( \\mathbb{Q}_{ \\mathrm{gc}} \\, \\mathrm{ \\boldmath~ \\cdot~} \\, \\par tial_{k}{ \\bf B} \\right) \\, \\mathrm{ \\boldmath~ \\times~} \\, \\frac{1}{c} \\left( \\dot{ \\bf X}-{ \\bf u}_{ \\mathrm{E}} \\right) \\right],}} \\end{array}", "UniMER-1M_0002737": "x_{1}", "UniMER-1M_0002763": "\\operatorname{Pr} \\left(X_{1}, . . .,X_{n} \\inD \\right)= \\int_{D}f_{X_{1}, . . .,X_{n}}(x_{1}, . . .,x_{n}) \\,dx_{1} . . .dx_{n}.", "UniMER-1M_0002742": "\\phi(t)- \\phi(t_{l})= \\omega_{0}(t-t_{l})+ \\frac{ \\Delta \\omega}{ \\Omega} \\mathrm{sin}( \\Omega(t-t_{l})+ \\xi_{l})- \\frac{ \\Delta \\omega}{ \\Omega} \\mathrm{sin}( \\xi_{l})", "UniMER-1M_0002754": "\\begin{array}{rl}{{V(r)=}}&{{ \\frac{ \\left(1-p \\right)r^{q+1}}{ \\left(q+1 \\right) \\left(q+2 \\right)} \\left(r-2- \\left(1-r \\right)q \\right)- \\frac{ \\left(1-r \\right)^{q+1}}{ \\left(q+1 \\right) \\left(q+2 \\right)} \\left(1+ \\left(q+1 \\right)r \\right)+ \\frac{p \\,r \\left(r-2 \\right)}{2}+ \\frac{p \\left(r-1 \\right)^{2} \\left(1-r \\right)^{q}}{ \\left(q+2 \\right)}.}} \\end{array}", "UniMER-1M_0002747": "\\mathrm{Tr} \\,P= \\int_{0}^{1}dx^{2}<x^{2}|P|x^{2}>= \\int_{0}^{1}dx^{2}F(x^{2})= \\theta \\, \\,.", "UniMER-1M_0002758": "E^{ext}e^{ \\etat}= \\intE_{ \\omega}^{ext}e^{-i( \\omega+i \\eta)t}d \\omega/2 \\pi", "UniMER-1M_0002762": "\\left\\lbrace \\begin{array}{c}{{ \\dot{y}^{1}= \\overline{{{v}}}^{1}(y^{1}, . . .,y^{n-1}),}} \\\\ {{ \\vdots}} \\\\ {{ \\dot{y}^{n-1}= \\overline{{{v}}}^{n-1}(y^{1}, . . .,y^{n-1}),}} \\\\ {{ \\dot{y}^{n}= \\overline{{{v}}}^{n}(y^{1}, . . .,y^{n-1}).}} \\end{array} \\right.", "UniMER-1M_0002765": "Re_{ \\delta_{0}^{*}}=Re_{ \\delta^{*}}(x=0)", "UniMER-1M_0002771": "\\hat{x}:= \\hat{q}-q_{t}", "UniMER-1M_0002770": "x", "UniMER-1M_0002752": "- \\mathrm{cos} A, \\mathrm{cos} B, \\mathrm{cos} C{ \\mathrm{~are~the~roots~of~}}x^{3}+{ \\frac{1}{2}}x^{2}-{ \\frac{1}{2}}x-{ \\frac{1}{8}}=0.", "UniMER-1M_0002764": "\\ell", "UniMER-1M_0002741": "h= \\operatorname{Tr} \\int \\! \\widehat{ \\calD} \\widehat{H} \\, \\mathrm{d}^{2}z", "UniMER-1M_0002767": "\\begin{array}{r}{{ \\beta_{-}= \\tau( \\Gamma_{s}- \\Gamma_{0})+ \\tau \\left( \\frac{ \\omega_{*i}}{ \\omega} \\right)_{s} \\left[F_{- -} \\Gamma_{s}- \\left( \\frac{k_{ \\par allel}bk_{ \\par allel}}{ \\omega^{2}} \\right)_{-} \\frac{ \\tauV_{A}^{2}F_{-}}{(1- \\omega_{*e}/ \\omega)_{-}} \\right].}} \\end{array}", "UniMER-1M_0002777": "|| \\cdot||^{ \\prime}", "UniMER-1M_0002694": "\\rho \\geq0", "UniMER-1M_0002739": "\\mathbf{v}_{i}( \\mathbf{r})=[ \\mathbf{G}( \\mathbf{r}- \\mathbf{r}_{i}^{+})- \\mathbf{G}( \\mathbf{r}- \\mathbf{r}_{i}^{-})] \\cdot \\mathbf{f}_{i} \\,,", "UniMER-1M_0002745": "r_{ij}", "UniMER-1M_0002748": "\\widetilde{ \\mathbf{v}}( \\mathbf{x,}t)=- \\mathbf{ \\nabla} \\phi( \\mathbf{x,}t) \\times \\mathbf{e}_{3}.", "UniMER-1M_0002756": "g_{ \\alpha}(r)= \\langle \\psi_{ \\alpha}^{*}(r) \\psi_{ \\alpha}(0) \\rangle", "UniMER-1M_0002773": "\\dot{y}", "UniMER-1M_0002743": "\\Omega", "UniMER-1M_0002785": "b(a)", "UniMER-1M_0002779": "W= \\sum_{t \\in \\mathbb{T}}A_{t}", "UniMER-1M_0002759": "\\lVert. \\rVert", "UniMER-1M_0002782": "\\rho", "UniMER-1M_0002751": "100", "UniMER-1M_0002789": "1.66", "UniMER-1M_0002790": "T", "UniMER-1M_0002744": "L=- \\frac{2}{k} \\hat{L},~~~~~~~~ \\bar{L}=- \\frac{2}{k} \\hat{ \\bar{L}}.", "UniMER-1M_0002766": "225^{2}=50625", "UniMER-1M_0002750": "r \\geq \\frac{l_{z}}{ \\sqrt{g(H-R \\mathrm{cos}( \\vartheta))}}", "UniMER-1M_0002791": "\\Psi_{i}", "UniMER-1M_0002795": "\\tau/T", "UniMER-1M_0002796": "ik", "UniMER-1M_0002749": "r_{2}", "UniMER-1M_0002775": "\\begin{array}{r}{{ \\langle \\chi \\rangle= \\frac{ \\int_{- \\pi}^{ \\pi} \\chiw( \\theta_{ \\mathrm{ps}}) \\ { \\mathrm{d}} \\theta_{ \\mathrm{ps}}}{ \\int_{- \\pi}^{ \\pi}w( \\theta_{ \\mathrm{ps}}) \\ { \\mathrm{d}} \\theta_{ \\mathrm{ps}}}.}} \\end{array}", "UniMER-1M_0002736": "\\xi_{ij}(t)=P(x_{t}=i,x_{t+1}=j|y,v,w; \\theta)={ \\frac{ \\alpha_{i}(t)a_{ij}^{w_{t}} \\beta_{j}(t+1)b_{j}^{v_{t+1}}(y_{t+1})}{ \\sum_{i=1}^{N} \\sum_{j=1}^{N} \\alpha_{i}(t)a_{ij}^{w_{t}} \\beta_{j}(t+1)b_{j}^{v_{t+1}}(y_{t+1})}}", "UniMER-1M_0002801": "500 \\times", "UniMER-1M_0002760": "\\begin{array}{r}{{ \\begin{array}{rl}{{M_{4,2,xx}^{ \\sigma,eq}}}&{{= \\sum_{i}f_{i}^{ \\sigma,eq}v_{ix}^{2}(v_{i \\alpha}^{2}+ \\eta_{i}^{ \\sigma2})= \\rho^{ \\sigma} \\{(D+I^{ \\sigma}+2)R^{ \\sigma2}T^{2}}} \\\\ &{{+u_{x}^{2}(u_{x}^{2}+u_{y}^{2})+R^{ \\sigma}T[u_{x}^{2}(D+I^{ \\sigma}+5)+u_{y}^{2}] \\},}} \\end{array}}} \\end{array}", "UniMER-1M_0002802": "P", "UniMER-1M_0002784": "^{25}", "UniMER-1M_0002735": "\\mathbf{A}( \\mathbf{y})= \\mathbf{J}_{ \\mathbf{y}}( \\mathbf{y})", "UniMER-1M_0002768": "| \\Delta|", "UniMER-1M_0002757": "P(k)= \\frac{m(k)}{n(k)}.", "UniMER-1M_0002786": "\\begin{array}{rl}{{{ \\frac{ \\pi}{4}}}}&{{= \\left( \\prod_{p \\equiv1{ \\mathrm{mod}{4}}}{ \\frac{p}{p-1}} \\right) \\left( \\prod_{p \\equiv3{ \\mathrm{mod}{4}}}{ \\frac{p}{p+1}} \\right)}} \\end{array}", "UniMER-1M_0002774": "\\mathbf{J}_{B}=k_{AF}^{0} \\mathbf{B}", "UniMER-1M_0002776": "f_{i}^{t+1}= \\sum_{k=1}^{N} \\Deltaf_{ki}^{t}", "UniMER-1M_0002783": "\\beta<0", "UniMER-1M_0002793": "10^{5} \\lesssimT \\lesssim \\mathrm{a \\;few} \\times10^{7}", "UniMER-1M_0002788": "E^{ \\mathrm{~K~o~o~p~m~a~n~s~}}", "UniMER-1M_0002769": "\\mathbf{F}^{ \\textrm{int}}=- \\frac{ \\par tial(E_{s}+E_{b}+E_{t})}{ \\par tial \\mathbfq}.", "UniMER-1M_0002778": "\\theta_{0}", "UniMER-1M_0002797": "<1", "UniMER-1M_0002740": "\\begin{array}{rl}{{ \\dot{ \\rho}_{aa}}}&{{= \\frac{i \\Omega_{C}}{2}e^{i \\omega_{C}t} \\rho_{ae}^{*}- \\frac{i \\Omega_{C}}{2}e^{-i \\omega_{C}t} \\rho_{ae}- \\Gamma_{1} \\rho_{aa}}} \\\\ &{{+ \\frac{ \\Gamma_{e} \\rho_{ee}}{3}}} \\\\ {{ \\dot{ \\rho}_{ab}}}&{{= \\frac{i \\Omega_{C}}{2}e^{i \\omega_{C}t} \\rho_{be}^{*}- \\frac{i \\Omega_{P}}{2}e^{-i \\omega_{P}t} \\rho_{ae}}} \\\\ &{{- \\frac{ \\rho_{ab}}{2}( \\Gamma_{1}+ \\Gamma_{2})+i \\rho_{ab}( \\omega_{b}- \\omega_{a})}} \\\\ {{ \\dot{ \\rho}_{ac}}}&{{= \\frac{i \\Omega_{C}}{2}e^{i \\omega_{C}t} \\rho_{ce}^{*}- \\frac{i \\Omega_{C}}{2}e^{-i \\omega_{C}t} \\rho_{ae}}} \\\\ &{{- \\frac{ \\rho_{ac}}{2}( \\Gamma_{a}+ \\Gamma_{c})+i \\rho_{ac}( \\omega_{c}- \\omega_{a})}} \\\\ {{ \\dot{ \\rho}_{ae}}}&{{= \\frac{i \\Omega_{C}}{2}e^{i \\omega_{C}t} \\rho_{ee}^{*}- \\frac{i \\Omega_{C}}{2}e^{-i \\omega_{C}t} \\rho_{aa}- \\frac{i \\Omega_{P}}{2}e^{-i \\omega_{P}t} \\rho_{ab}}} \\\\ &{{- \\frac{i \\Omega_{C}}{2}e^{-i \\omega_{C}t} \\rho_{ac}- \\frac{ \\rho_{ae}}{2}( \\Gamma_{a}+ \\Gamma_{e})}} \\\\ &{{+i \\rho_{ae}( \\omega_{e}- \\omega_{a})}} \\\\ {{ \\dot{ \\rho}_{bb}}}&{{= \\frac{i \\Omega_{P}}{2}e^{i \\omega_{P}t} \\rho_{be}^{*}- \\frac{i \\Omega_{P}}{2}e^{-i \\omega_{P}t} \\rho_{be}- \\Gamma_{2} \\rho_{bb}}} \\\\ &{{+ \\frac{ \\Gamma_{e} \\rho_{ee}}{3}}} \\end{array}", "UniMER-1M_0002805": "g= \\Delta", "UniMER-1M_0002819": "k=15", "UniMER-1M_0002794": "u_{n}=u_{n-1}+u_{n-2}", "UniMER-1M_0002804": "\\mathbf{L}^{( \\alpha)}", "UniMER-1M_0002780": "\\mathbb{Z}^{u}=[ \\mathbb{Z}^{u}, \\textbf{u}_{h}^{1}( \\mu_{N}^{*}), \\textbf{u}_{h}^{2}( \\mu_{N}^{*}), . . ., \\textbf{u}_{h}^{L}( \\mu_{N}^{*})]", "UniMER-1M_0002810": "\\beta={ \\frac{k \\Deltat}{2}}", "UniMER-1M_0002792": "p( \\xi)", "UniMER-1M_0002787": "\\begin{array}{ll}{{{ \\theta_{1} \\approx2e^{ \\chi_{12}^{R}} \\mathrm{sin} \\chi_{12}^{J}, \\quad \\theta_{3} \\approx \\mathrm{sin} \\chi_{33}^{J}+2 \\mathrm{sin} \\chi_{11}^{J}, \\quad \\theta_{5} \\approx2(1-e^{ \\chi_{13}^{R}} \\mathrm{cos} \\chi_{13}^{J}),}}} \\\\ {{{ \\theta_{2} \\approx2(1-e^{ \\chi_{12}^{R}} \\mathrm{cos} \\chi_{12}^{J}), \\quad \\theta_{4} \\approx2e^{ \\chi_{13}^{R}} \\mathrm{sin} \\chi_{13}^{J}, \\quad \\theta_{6} \\approx2e^{ \\chi_{23}^{R}} \\mathrm{sin} \\chi_{23}^{J},}}} \\\\ {{{ \\theta_{7} \\approx2(1-e^{ \\chi_{23}^{R}} \\mathrm{cos} \\chi_{23}^{J}), \\quad \\theta_{8} \\approx- \\sqrt{3} \\mathrm{sin} \\chi_{33}^{J},}}} \\end{array}", "UniMER-1M_0002826": "_ \\sun", "UniMER-1M_0002823": "\\mathrm{sin} \\theta_{i}/l_{c}", "UniMER-1M_0002800": "\\begin{array}{rlr}{{ \\sum_{k \\in \\hat{I}_{N}} \\left|k( \\overline{{{p}}}k^{2}+ \\overline{{{q}}})^{-1/2} \\sum_{ \\omega \\inI_{j} \\capI_{k}} \\omega \\right|}}&{{ \\leq}}&{{ \\sum_{ \\omega \\inI_{j}}|j+ \\omega_{ \\mathrm{max}}| \\sum_{ \\eta \\inI_{0}} \\left| \\frac{j+2 \\omega_{ \\mathrm{max}}}{( \\overline{{{p}}}(j-2 \\omega_{ \\mathrm{max}})^{2}+ \\overline{{{q}}})^{1/2}} \\right|.}} \\end{array}", "UniMER-1M_0002753": "\\overline{{{v}}}_{Ma}= \\frac{1}{ \\inth \\,dx} \\int \\left( \\epsilon^{2}C_{l} \\right)^{-1} \\frac{ \\par tial \\sigma}{ \\par tialx}h \\bigg( \\frac{h}{2}- \\xi+ \\frac{H}{m} \\bigg) \\,dx,", "UniMER-1M_0002827": "qL", "UniMER-1M_0002734": "\\mathrm{tr}([T^{a},T^{b}] \\,T^{c})= \\frac{i}{2}f_{abc}= \\mathrm{tr}([T^{b},T^{c}] \\,T^{a})", "UniMER-1M_0002815": "N", "UniMER-1M_0002816": "\\sigma_{12}= \\frac{w_{m_{1},n_{1}}+w_{m_{2},n_{2}}}{2 \\left| \\rho_{2}- \\rho_{1} \\right|}", "UniMER-1M_0002806": "\\vec{ \\Gamma}_{ \\nu} \\times \\par tial_{ \\mu} \\vec{R}^{ \\mu \\nu} \\,+ \\, \\vec{q} \\, \\frac{d \\Lambda}{dq_{0}} \\,=0.", "UniMER-1M_0002835": "\\approx0.12", "UniMER-1M_0002822": "{ \\calA}_{tot}^{ \\gamma}(b \\rightarrows+ \\gamma)=F_{2}{ \\calO}_{LR}^{ \\gamma}", "UniMER-1M_0002811": "pt", "UniMER-1M_0002807": "1- \\Delta^{ \\prime}f \\Delta^{ \\dag}-U^{ \\prime}U^{ \\dag} \\subset \\Gamma.", "UniMER-1M_0002772": "\\varrho( \\mathbf{x}_{k}) \\approx \\sum_{n} \\varrho( \\mathbf{x}_{n})L( \\mathbf{x}_{k}, \\mathbf{x}_{n})", "UniMER-1M_0002840": "T_{d}", "UniMER-1M_0002808": "\\begin{array}{r}{{v_{t}^{(0)}+w^{(0)} \\hat{ \\tau}_{t} \\cdot \\hat{ \\theta}+u^{(1)}v_{ \\bar{r}}^{(1)}+ \\frac{v^{(1)}v_{ \\theta}^{(1)}}{ \\bar{r}}+ \\frac{v^{(0)}v_{ \\theta}^{(2)}}{ \\bar{r}}+ \\frac{v^{(0)}u^{(2)}}{ \\bar{r}} \\quad}} \\\\ {{+ \\frac{v^{(1)}u^{(1)}}{ \\bar{r}}+ \\frac{w^{(0)}v_{s}^{(1)}}{ \\sigma^{(0)}}-2w^{(0)}w^{(1)} \\kappa^{(0)} \\mathrm{sin} \\varphi^{(0)}}} \\\\ {{- \\left(w^{(0)} \\right)^{(2)} \\left( \\frac{ \\kappa \\sigma \\mathrm{sin} \\varphi}{h_{3}} \\right)^{(1)}+ \\frac{ \\theta^{(0)} \\cdot \\dot{X}_{s}^{(0)}w^{(0)}}{ \\sigma^{(0)}}+u^{(2)}v_{ \\bar{r}}^{(0)}}} \\\\ {{=- \\frac{1}{ \\bar{r}} \\frac{P_{ \\theta}^{(2)}}{ \\rho_{0}}+ \\frac{ \\bar{ \\nu}}{ \\bar{r}} \\left( \\bar{r}v_{ \\bar{r}}^{(0)} \\right)_{ \\bar{r}}- \\frac{ \\bar{ \\nu}v^{(0)}}{ \\bar{r^{2}}}+ \\alphag \\Tilde{T}^{(0)} \\hat{ \\mathbf{y}} \\cdot \\hat{ \\pmb{ \\theta}}}} \\end{array}", "UniMER-1M_0002843": "\\Omega", "UniMER-1M_0002803": "\\begin{array}{rl}{{Q_{2}^{M}=}}&{{ \\sum_{i=1}^{N} \\mathrm{exp} \\left(- \\frac{ \\Omega^{2}}{ \\Gamma}(N-i) \\tau \\right) \\mathrm{exp} \\left(- \\mathrm{i} \\delta(N-i) \\left(T+ \\tau \\right) \\right)}} \\end{array}", "UniMER-1M_0002798": "\\phi_{0} \\left( \\mathbf{r}; \\mathbf{R},t \\right)", "UniMER-1M_0002781": "N^{l+1}= \\mathrm{max}(1,Y^{l}) \\  \\mathrm{~w~h~e~r~e~} Y^{l}=N^{l} \\times \\left\\lfloor \\frac{N_{obj}}{N^{l}} \\right\\rfloor+ \\mathcal{B} \\left(N^{l}, \\frac{N_{obj}}{N^{l}}- \\left\\lfloor \\frac{N_{obj}}{N^{l}} \\right\\rfloor \\right).", "UniMER-1M_0002799": "V_{en}( \\textbf{r}, \\textbf{R})=2 \\pia_{s}^{T}(k) \\delta( \\textbf{r}- \\textbf{R}),", "UniMER-1M_0002746": "\\sigma_{ \\mathrm{XY}}^{2}= \\frac{1}{N} \\sum_{i=1}^{N}X_{i}Y_{i},", "UniMER-1M_0002828": "\\frac{ \\mathrm{sin}^{2}( \\frac{ \\omega \\Deltat}{2})}{v^{2} \\Deltat^{2}}= \\frac{( \\sum_{i=1}^{r}c_{i} \\mathrm{sin}((i- \\frac{1}{2})k_{x} \\Deltax))^{2}}{ \\Deltax^{2}}+ \\frac{( \\sum_{i=1}^{r}c_{i} \\mathrm{sin}((i- \\frac{1}{2})k_{y} \\Deltay))^{2}}{ \\Deltay^{2}}+ \\frac{( \\sum_{i=1}^{r}c_{i} \\mathrm{sin}((i- \\frac{1}{2})k_{z} \\Deltaz))^{2}}{ \\Deltaz^{2}}.", "UniMER-1M_0002850": "Y", "UniMER-1M_0002851": "g", "UniMER-1M_0002813": "\\begin{array}{rl}&{{a= \\frac{1}{2} \\left( \\theta^{2}- \\frac{1}{3} \\right) \\mu, \\quad b= \\frac{1}{2} \\left( \\theta^{2}- \\frac{1}{3} \\right)(1- \\mu), \\quad c= \\frac{1}{2}(1- \\theta^{2}) \\nu, \\quad d= \\frac{1}{2}(1- \\theta^{2})(1- \\nu) \\ ,}} \\\\ &{{ \\tilde{a}= \\mu \\left( \\theta- \\frac{1}{2} \\right)-(1- \\mu) \\left( \\frac{1}{2} \\left[( \\theta-1)^{2}- \\frac{1}{3} \\right] \\right), \\quad \\tilde{c}=( \\theta-1) \\ ,}} \\end{array}", "UniMER-1M_0002832": "P_{n_{1}n_{2}}=| \\psi_{n_{1}} \\rangle \\langle \\psi_{n_{1}}|+| \\psi_{n_{2}} \\rangle \\langle \\psi_{n_{2}}|", "UniMER-1M_0002854": "N=1", "UniMER-1M_0002825": "|P \\rangle", "UniMER-1M_0002814": "\\begin{array}{r}{{ \\int_{0}^{ \\infty} \\frac{u^{ \\delta-1} \\mathrm{d} u}{(u+ \\thetar^{ \\alpha})^{k}}=( \\thetar^{ \\alpha})^{ \\delta-k} \\! \\! \\times \\! \\! \\frac{(-1)^{k+1} \\pi}{ \\mathrm{sin}( \\pi \\delta)} \\! \\! \\times \\! \\! \\frac{ \\Gamma( \\delta)}{ \\Gamma(k) \\Gamma( \\delta \\!- \\!k \\!+ \\!1)},}} \\end{array}", "UniMER-1M_0002817": "\\Theta{ \\hat{B}(0)}{ \\Theta}^{-1} \\:= \\:-{ \\hat{B}(0)} \\:.", "UniMER-1M_0002820": "\\begin{array}{rl}&{{F_{ij}:= \\left\\{ \\begin{array}{ll}{{ \\frac{1}{D_{jj}-D_{ii}},}}&{{ \\mathrm{if~D_{jj}-D_{ii}~ \\neq~0~}.}} \\\\ {{0,}}&{{ \\mathrm{otherwise}.}} \\end{array} \\right.}} \\\\ &{{G_{ij}:= \\left\\{ \\begin{array}{ll}{{0,}}&{{ \\mathrm{if~D_{jj}-D_{ii}~ \\neq~0~}.}} \\\\ {{(S^{ \\dagger} \\frac{dS}{da})_{ij},}}&{{ \\mathrm{otherwise}.}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0002837": "\\hat{ \\Theta}", "UniMER-1M_0002833": "\\gamma \\sim \\nu_{0}/ \\ell_{0}^{2}", "UniMER-1M_0002862": "r_{c}", "UniMER-1M_0002824": "z", "UniMER-1M_0002821": "T^{[1]}", "UniMER-1M_0002846": "P=1", "UniMER-1M_0002844": "c_{m+2}= \\frac{2m \\omega}{ \\hbar} \\frac{ \\hbar \\omega(m+ \\frac{1}{2})-E_{n}}{(m+2)(m+1)}c_{m}.", "UniMER-1M_0002867": "i=2", "UniMER-1M_0002845": "\\begin{array}{r}{{ \\frac{1-M^{2}}{c_{s}} \\par tial_{s}(v_{s})=2 \\frac{M}{c_{s}} \\par tial_{s}(c_{s}) \\ ~~~+(1+M^{2}) \\frac{S_{par}}{nc_{s}}+ \\frac{M}{B} \\par tial_{s}(B)- \\frac{MS_{mom}}{m_{i}nc_{s}^{2}}}} \\end{array}", "UniMER-1M_0002841": "\\mathbf{v}", "UniMER-1M_0002812": "\\omega_{l}", "UniMER-1M_0002848": "\\eta(f_{m})= \\frac{ \\sqrt{2}f_{m}}{ \\gamma/(2 \\pi)} \\mathcal{L}^{ \\frac{1}{2}}(f_{m}).", "UniMER-1M_0002872": "t=0.1", "UniMER-1M_0002830": "m", "UniMER-1M_0002874": "F=3/2", "UniMER-1M_0002847": "v_{r}", "UniMER-1M_0002849": "t", "UniMER-1M_0002818": "T_{s}", "UniMER-1M_0002829": "56 \\  \\", "UniMER-1M_0002858": "0", "UniMER-1M_0002880": "\\omega", "UniMER-1M_0002881": "\\gamma \\rightarrow0", "UniMER-1M_0002834": "\\nabla_{ \\perp}^{2}= \\frac{ \\par tial^{2}}{ \\par tialx^{2}}+ \\frac{ \\par tial^{2}}{ \\par tialy^{2}}.", "UniMER-1M_0002831": "l_{X}(Y)=XY,X \\in{ \\mathfrak{g}},Y \\inU({ \\mathfrak{g}})", "UniMER-1M_0002859": "(2ac)^{2}+(2bd)^{2}=(a^{2}-b^{2}+c^{2}-d^{2})^{2}", "UniMER-1M_0002809": "\\begin{array}{rl}{{f_{m^{ \\star}}(x)=}}&{{ \\int_{- \\infty}^{+ \\infty} \\deltaf \\left(x,v, \\frac{ \\sqrt{2m^{ \\star}}}{k_{0}} \\right) \\psi_{m^{ \\star}}(v)dv \\sim(-1)^{ \\frac{m^{ \\star}}{2}} \\frac{1}{ \\sqrt{ \\pi}} \\sqrt[4]{ \\frac{2}{m}} \\frac{A}{ \\sqrt{2 \\pi}} \\int_{- \\infty}^{+ \\infty} \\mathrm{cos} \\left( \\sqrt{2m^{ \\star}}v \\right) \\mathrm{cos} \\left(k_{0}x- \\sqrt{2m^{ \\star}}v \\right)e^{- \\frac{v^{2}}{2}}dv}} \\\\ {{=}}&{{(-1)^{ \\frac{m^{ \\star}}{2}} \\frac{A}{ \\sqrt{ \\pi \\sqrt{8m^{ \\star}}}} \\left(1+e^{-4m^{ \\star}} \\right) \\mathrm{cos}(k_{0}x) \\sim(-1)^{ \\frac{m^{ \\star}}{2}} \\frac{A}{ \\sqrt{ \\pi \\sqrt{8m^{ \\star}}}} \\mathrm{cos}(k_{0}x) \\Rightarrow \\langlef_{m^{ \\star}}^{2} \\rangle_{x} \\propto \\frac{1}{ \\sqrt{m^{ \\star}}},}} \\end{array}", "UniMER-1M_0002861": "j_{5}^{ \\mu \\,a}=- \\frac{F}{ \\sqrt{2}} \\par tial^{ \\mu} \\Phi^{a} \\ .", "UniMER-1M_0002863": "r_{1}>r_{2}=r_{3} \\Longrightarrow \\frac{r_{1}-r_{2}}{r_{1}-r_{3}}=1", "UniMER-1M_0002836": "\\left( \\begin{array}{l}{{{ \\tilde{ \\mathcal{F}}^{+}}}} \\\\ {{{ \\tilde{G}_{+}}}} \\end{array} \\right)= \\mathcal{S} \\left( \\begin{array}{l}{{{ \\mathcal{F}^{+}}}} \\\\ {{{G_{+}}}} \\end{array} \\right)= \\left( \\begin{array}{ll}{{{A}}}&{{{B}}} \\\\ {{{C}}}&{{{D}}} \\end{array} \\right) \\left( \\begin{array}{l}{{{ \\mathcal{F}^{+}}}} \\\\ {{{G_{+}}}} \\end{array} \\right) \\,.", "UniMER-1M_0002860": "E^{*}", "UniMER-1M_0002865": "e_{M}^{ \\alpha}e_{ \\beta}^{M}= \\delta_{ \\beta}^{ \\alpha}.", "UniMER-1M_0002839": "{ \\frac{dq^{s}}{dt}}=w^{s}, \\qquad{ \\frac{d}{dt}} \\left({ \\frac{ \\par tialT}{ \\par tialw^{s}}} \\right)-{ \\frac{ \\par tialT}{ \\par tialq^{s}}}=Q_{s}, \\qquad s=1, \\, . . ., \\,n", "UniMER-1M_0002838": "[{ \\bf V}^{-1} \\;{ \\bf B}, \\;{ \\bf S}^{a_{0}i}]=0 \\; \\; \\mathrm{for} \\; \\eta^{a_{0}a_{0}} \\; \\eta^{ii}=-1", "UniMER-1M_0002866": "V_{ \\mathrm{dir}}^{(3)}", "UniMER-1M_0002868": "b", "UniMER-1M_0002871": "\\simN^{^{- \\frac{2}{5}}}", "UniMER-1M_0002869": "v=0", "UniMER-1M_0002877": "\\langleT_{ \\mathrm{~f~i~r~s~t~-~n~u~c~}} \\rangle", "UniMER-1M_0002878": "N=0", "UniMER-1M_0002900": "[0,R^{2})", "UniMER-1M_0002901": "\\omega", "UniMER-1M_0002879": "p", "UniMER-1M_0002904": "C", "UniMER-1M_0002870": "L_{0} \\left[ \\phi^{(n_{1},n_{2},...,n_{k})} \\right]= \\left(h+ \\sum_{i=1}^{k}n_{i} \\right) \\phi^{(n_{1},n_{2},...,n_{k})} \\,.", "UniMER-1M_0002906": "\\vert", "UniMER-1M_0002856": "A= \\sum_{xy}xy(q_{xy}-a_{x}b_{y})/ \\sigma_{a} \\sigma_{b},", "UniMER-1M_0002908": "n=.1", "UniMER-1M_0002855": "\\langleN_{i} \\rangle={ \\frac{g_{i}}{e^{( \\varepsilon_{i}- \\mu)/kT}}}={ \\frac{N}{Z}} \\,g_{i}e^{- \\varepsilon_{i}/kT},", "UniMER-1M_0002910": "\\vec{F}", "UniMER-1M_0002852": "\\begin{array}{rl}{{ \\mathscr{S} \\mathrm{t}_{ \\mathbb{G}_{m,Y}}(T,D_{X/Y} \\times_{ \\mathbb{A}_{Y}^{1}} \\mathbb{G}_{m,Y})}}&{{ \\simeq \\mathscr{S} \\mathrm{t}_{ \\mathbb{A}_{Y}^{1}}(T,D_{X/Y}) \\simeq \\mathscr{S} \\mathrm{t}_{Y}(T \\times_{ \\mathbb{A}_{ \\mathscr{C}}^{1}} \\{0 \\},X)}} \\end{array}", "UniMER-1M_0002905": "i", "UniMER-1M_0002873": "\\psi", "UniMER-1M_0002876": "\\Delta \\nu_{x,bb}= \\frac{r_{p}N_{p} \\beta_{x}^{*}}{2 \\pi \\gamma} \\frac{1}{ \\sigma_{x}^{*}( \\sigma_{x}^{*}+ \\sigma_{y}^{*})}, \\; \\; \\; \\Delta \\nu_{y,bb}= \\frac{r_{p}N_{p} \\beta_{y}^{*}}{2 \\pi \\gamma} \\frac{1}{ \\sigma_{y}^{*}( \\sigma_{x}^{*}+ \\sigma_{y}^{*})}", "UniMER-1M_0002875": "f_{ \\pi}^{2}(T)=f_{ \\pi}^{2} \\left[1- \\frac{N-2}{12} \\, \\frac{T^{2}}{f_{ \\pi}^{2}} \\right] \\,.", "UniMER-1M_0002914": "\\begin{array}{r}{{ \\int_{V} \\int_{V} \\mathbf{P}^{*}( \\mathbf{x}) \\mathbb{G}_{0}( \\mathbf{x}, \\mathbf{x}^{ \\prime}) \\mathbf{P}( \\mathbf{x}^{ \\prime}) \\, \\mathrm{d} \\mathbf{x} \\, \\mathrm{d} \\mathbf{x}^{ \\prime}+ \\int_{V} \\mathbf{P}^{*}( \\mathbf{x}) \\xi( \\mathbf{x}) \\mathbf{P}( \\mathbf{x}) \\, \\mathrm{d} \\mathbf{x}=- \\int_{V} \\mathbf{P}^{*}( \\mathbf{x}) \\mathbf{E}_{ \\mathrm{inc}}( \\mathbf{x}) \\, \\mathrm{d} \\mathbf{x}.}} \\end{array}", "UniMER-1M_0002882": "\\sum_{ \\alpha=1}^{n+1}x_{ \\alpha}^{2}-r^{2}=0.", "UniMER-1M_0002889": "xF_{3}(x,Q^{2})=x^{ \\alpha}(1-x)^{ \\beta} \\sum_{n=0}^{N_{max}} \\Theta_{n}^{ \\alpha, \\beta}(x) \\sum_{j=0}^{n}c_{j}^{(n)}( \\alpha, \\beta)M_{j+2}(Q^{2})", "UniMER-1M_0002911": "\\tilde{U}( \\phi)= \\tilde{U}_{R}^{(1-U( \\phi))},", "UniMER-1M_0002909": "0.5", "UniMER-1M_0002922": "0", "UniMER-1M_0002891": "\\frac{ \\par tialP_{ii}}{ \\par tialW_{ij}^{ \\dagger}}=M_{jk}W_{ki}+H_{jk} \\Lambda_{ki}=0.", "UniMER-1M_0002915": "+4586^{ \\prime \\prime} \\mathrm{sin}(2D-l)", "UniMER-1M_0002888": "y^{ \\prime \\prime}(t_{0})={ \\frac{ \\par tialf}{ \\par tialt}}(t_{0},y(t_{0}))+{ \\frac{ \\par tialf}{ \\par tialy}}(t_{0},y(t_{0})) \\,f(t_{0},y(t_{0})).", "UniMER-1M_0002927": "\\omega^{ \\ast}", "UniMER-1M_0002853": "B_{ \\varphi}^{hf}= \\frac{E_{0}^{hf}}{J_{1} \\left(p_{01} \\right)} \\frac{ \\omega \\varepsilon_{0} \\mu_{0}}{k_{ \\perp}}J_{1}^{ \\prime} \\left(k_{ \\perp}r \\right) \\mathrm{sin}( \\omegat- \\varphi+ \\psi_{0}),", "UniMER-1M_0002883": "-1", "UniMER-1M_0002907": "n^{7}", "UniMER-1M_0002864": "G_{h}^{Y-v}", "UniMER-1M_0002903": "r_{s} \\left(t \\right)= \\left( \\frac{t}{B} \\right)^{ \\frac{1}{ \\lambda}}", "UniMER-1M_0002913": "Z", "UniMER-1M_0002894": "E", "UniMER-1M_0002918": "\\boldsymbol{A}^{ \\mathrm{~H~F~}}+ \\Bar{ \\boldsymbol{A}}^{ \\mathrm{~G~F~}2}", "UniMER-1M_0002917": "\\alpha= \\frac{1}{2} \\frac{M_{*}}{m} \\left( \\frac{r}{a} \\right)^{3} \\frac{k_{L}}{C} \\Omega.", "UniMER-1M_0002884": "omg", "UniMER-1M_0002936": "K_{7}", "UniMER-1M_0002921": "\\tau_{ \\mathrm{total}}/ \\tau_{ \\mathrm{step}}=N.", "UniMER-1M_0002890": "\\sc riptstyle({ \\frac{1}{3}} \\piR^{2}D).", "UniMER-1M_0002931": "\\mathbf{x}_{j}^{b,i},j=1, . . .,B_{i}", "UniMER-1M_0002886": "\\begin{array}{rlrl}&{{q_{1}= \\tilde{r}( \\omegak_{4})^{ \\frac{1}{2}}r_{1}( \\omegak_{4}),}}&&{{ \\bar{q}_{1}= \\tilde{r}( \\omegak_{4})^{- \\frac{1}{2}}r_{2}( \\omegak_{4})= \\tilde{r}( \\omegak_{4})^{ \\frac{1}{2}} \\overline{{{r_{1}( \\omegak_{4})}}},}} \\\\ &{{q_{3}=| \\tilde{r}( \\frac{1}{k_{4}})|^{ \\frac{1}{2}}r_{1}( \\frac{1}{k_{4}}),}}&&{{ \\bar{q}_{3}=-| \\tilde{r}( \\frac{1}{k_{4}})|^{- \\frac{1}{2}}r_{2}( \\frac{1}{k_{4}})=| \\tilde{r}( \\frac{1}{k_{4}})|^{ \\frac{1}{2}} \\overline{{{r_{1}( \\frac{1}{k_{4}})}}}.}} \\end{array}", "UniMER-1M_0002897": "\\mathrm{s_{A}^{2} \\mathrm{z_{A} \\mathrm{s_{B}^{2} \\mathrm{ \\overline{{{z}}}_{B}( \\mathrm{ \\overline{{{x}}}_{A} \\mathrm{x_{B}- \\mathrm{ \\overline{{{y}}}_{A} \\mathrm{y_{B})}}}}}}}}", "UniMER-1M_0002944": "G(x)", "UniMER-1M_0002912": "\\begin{array}{rl}{{p_{x}}}&{{= \\frac{ \\varepsilon_{0} \\varepsilon_{ \\mathrm{d}}E_{x}}{1/ \\alpha_{xx}^{ \\mathrm{p1}}- \\alpha_{xx}^{ \\mathrm{p2}}S/ \\alpha_{xx}^{ \\mathrm{p1}}},}} \\\\ {{m_{y}}}&{{= \\frac{H_{y}}{1/ \\alpha_{yy}^{ \\mathrm{m1}}- \\alpha_{yy}^{ \\mathrm{m2}}S/ \\alpha_{yy}^{ \\mathrm{m1}}},}} \\end{array}", "UniMER-1M_0002893": "a_{ \\mathrm{e}}=a_{ \\mathrm{o}}=a_{ \\perp}=a_{ \\par allel}=a_{t}", "UniMER-1M_0002933": "\\mathbf{a}=(a_{1},a_{2},a_{3})=a_{1}(1,0,0)+a_{2}(0,1,0)+a_{3}(0,0,1),", "UniMER-1M_0002934": "\\begin{array}{rl}{{ \\boldsymbol{u} \\cdot \\boldsymbol{n_{s}}}}&{{=0}} \\\\ {{ \\boldsymbol{ \\sigma \\cdotn_{s}}}}&{{=- \\frac{1}{ \\beta} \\left( \\begin{array}{ll}{{ \\eta_{e}}}&{{- \\eta_{o}}} \\\\ {{ \\eta_{o}}}&{{ \\eta_{e}}} \\end{array} \\right)( \\boldsymbol{u} \\cdot \\boldsymbol{t_{s}}) \\boldsymbol{t_{s}}}} \\end{array}", "UniMER-1M_0002940": "\\rho_{T}", "UniMER-1M_0002896": "B=ke^{2 \\phi}d \\gamma \\wedged \\bar{ \\gamma}", "UniMER-1M_0002920": "\\frac{dg( \\rho_{i})}{d \\rho_{i}} \\big|_{ \\rho_{i}= \\rho^{*(1)}}<0", "UniMER-1M_0002948": "{ \\bf q} \\equiv{ \\bf k}-{ \\bf k}^{ \\prime}", "UniMER-1M_0002926": "x \\sqrt{ \\frac{ \\pi}{2x}} \\mathrm{besselj}(n+ \\frac{1}{2},x)", "UniMER-1M_0002885": "m_{ \\infty}", "UniMER-1M_0002942": "\\Delta \\nu_{ \\mathrm{~L~C~}}= \\nu/Q", "UniMER-1M_0002924": "T=T_{ \\mathrm{conv}}+T_{ \\mathrm{div}} \\ ,", "UniMER-1M_0002929": "\\phi( \\lambda)= \\int_{- \\infty}^{ \\infty}f(x)e^{i \\lambdax} \\,dx.", "UniMER-1M_0002919": "v_{A}^{2} \\ggc_{s}^{2}", "UniMER-1M_0002943": "\\begin{array}{r}{{ \\frac{ \\par tialf}{ \\par tialt}-E \\xi \\frac{ \\par tialf}{ \\par tialp}-E \\frac{1- \\xi^{2}}{p} \\frac{ \\par tialf}{ \\par tial \\xi}= \\frac{ \\epsilon}{p^{2}} \\frac{ \\par tial}{ \\par tialp} \\Big[p^{2} \\frac{ \\par tialf}{ \\par tialp} \\Big]+ \\frac{ \\epsilon}{p^{2}} \\frac{ \\par tial}{ \\par tial \\xi} \\Big[(1- \\xi^{2}) \\frac{ \\par tialf}{ \\par tial \\xi} \\Big].}} \\end{array}", "UniMER-1M_0002928": "\\sc riptstyle59/60^{n+1}+59/60^{n+2}+ . . .=1/60^{n}", "UniMER-1M_0002938": "\\Theta_{i}", "UniMER-1M_0002952": "l", "UniMER-1M_0002930": "\\begin{array}{rlr}{{ \\omega_{p}}}&{{=}}&{{ \\sqrt{ \\frac{e^{2}n_{e}}{ \\epsilon_{0}m_{e}}} \\,,}} \\\\ {{G \\left(x \\right)}}&{{ \\approx}}&{{ \\frac{1}{2 \\sqrt{ \\pi}} \\, \\int_{x}^{ \\infty} \\frac{d \\tau}{ \\tau} \\, \\sqrt{1- \\frac{x}{ \\tau}} \\,Ei \\left( \\tau \\right) \\,.}} \\end{array}", "UniMER-1M_0002959": "\\phi(E)", "UniMER-1M_0002887": "\\sum_{ij \\inA}w_{ij}x_{ij}", "UniMER-1M_0002945": "A", "UniMER-1M_0002950": "+3", "UniMER-1M_0002971": "b", "UniMER-1M_0002895": "A= \\frac{ \\gammah_{0}^{3} \\pi^{4}}{3 \\muL_{x}^{4}}", "UniMER-1M_0002939": "\\begin{array}{r}{{{S_{14}^{q}= \\frac{2e^{2}}{h} \\sum_{ \\gamma, \\delta} \\intdE[A_{ \\gamma \\delta}(1)A_{ \\delta \\gamma}(4)]}}} \\\\ {{{(f_{ \\gamma}(E)[1-f_{ \\delta}(E)]+[1-f_{ \\gamma}(E)]f_{ \\delta}(E)).}}} \\end{array}", "UniMER-1M_0002949": "\\begin{array}{ccccc}{{{ \\Phi(x^{6})}}}&{{{=}}}&{{{ \\Phi(-x^{6})}}}&{{{=}}}&{{{ \\zeta_{ \\Phi} \\hat{P}_{6} \\cdot \\Phi(x^{6}+2 \\piR_{6}),}}} \\\\ {{{ \\Phi^{c}(x^{6})}}}&{{{=}}}&{{{- \\Phi^{c}(-x^{6})}}}&{{{=}}}&{{{ \\zeta_{ \\Phi} \\hat{P}_{6} \\cdot \\Phi^{c}(x^{6}+2 \\piR_{6}),}}} \\end{array}", "UniMER-1M_0002946": "\\Deltau*w_{r,s}=u* \\Deltaw_{r,s}=u* \\chi_{r}-u* \\chi_{s}=0", "UniMER-1M_0002932": "\\phi<1", "UniMER-1M_0002958": "s", "UniMER-1M_0002964": "N", "UniMER-1M_0002980": "-7.0", "UniMER-1M_0002981": "x", "UniMER-1M_0002935": "y", "UniMER-1M_0002899": "\\begin{array}{rl}{{ \\hat{q}_{ \\theta, \\mathrm{out}}^{-}[ \\Omega]}}&{{=e^{r_{ \\mathrm{s}}} \\hat{q}_{ \\theta, \\mathrm{s}}[ \\Omega]}} \\\\ {{ \\hat{q}_{ \\theta, \\mathrm{s}}[ \\Omega]}}&{{=G \\, \\hat{q}_{ \\theta, \\mathrm{in}}[ \\Omega]+ \\sqrt{G^{2}-1} \\, \\hat{q}_{ \\theta, \\mathrm{G}}[ \\Omega]}} \\\\ {{ \\hat{q}_{ \\theta, \\mathrm{out}}^{+}[ \\Omega]}}&{{=- \\sqrt{ \\eta} \\, \\hat{q}_{ \\theta, \\mathrm{out}}^{-}[ \\Omega]+ \\sqrt{1- \\eta} \\, \\hat{q}_{ \\theta,0}[ \\Omega]}} \\\\ {{ \\hat{q}_{ \\theta, \\mathrm{out}}[ \\Omega]}}&{{= \\sqrt{1- \\eta} \\, \\hat{q}_{ \\theta, \\mathrm{out}}^{-}[ \\Omega]+ \\sqrt{ \\eta} \\, \\hat{q}_{ \\theta,0}[ \\Omega]}} \\\\ {{ \\hat{q}_{ \\theta, \\mathrm{in}}[ \\Omega]}}&{{=e^{i \\Omega \\tau} \\hat{q}_{ \\theta, \\mathrm{out}}^{+}[ \\Omega],}} \\end{array}", "UniMER-1M_0002955": "\\begin{array}{r}{{u({ \\bf x})= \\overline{{{u}}}_{j}+ \\left[({ \\bf x}-{ \\bf x}_{j}) \\cdot \\nabla- \\int_{V_{j}} \\left\\{({ \\bf x}-{ \\bf x}_{j}) \\cdot \\nabla \\right\\} \\,dV \\right]u_{j}+ \\frac{1}{2} \\left[ \\left\\{({ \\bf x}-{ \\bf x}_{j}) \\cdot \\nabla \\right\\}^{2}- \\int_{V_{j}} \\left\\{({ \\bf x}-{ \\bf x}_{j}) \\cdot \\nabla \\right\\}^{2}dV \\right]u_{j},}} \\end{array}", "UniMER-1M_0002986": "T", "UniMER-1M_0002987": "=", "UniMER-1M_0002961": "<10", "UniMER-1M_0002892": "\\begin{array}{rlr}{{ \\overrightarrow{ \\lambda}}}&{{=}}&{{ \\left( \\begin{array}{c}{{ \\lambda_{1}}} \\\\ {{ \\lambda_{2}}} \\\\ {{ \\lambda_{3}}} \\\\ {{ \\lambda_{4}}} \\\\ {{ \\lambda_{5}}} \\\\ {{ \\lambda_{6}}} \\\\ {{ \\lambda_{7}}} \\\\ {{ \\lambda_{8}}} \\end{array} \\right)}} \\end{array}", "UniMER-1M_0002990": "\\theta", "UniMER-1M_0002963": "\\mathbf{h}_{t}=(0.75,0.85,0,0,0,0,0,0,0,0.015)", "UniMER-1M_0002992": "t", "UniMER-1M_0002967": "y_{t}=( \\DeltaC_{t}, \\DeltaRc_{t}, \\DeltaD_{t})^{ \\prime}", "UniMER-1M_0002988": "\\pm", "UniMER-1M_0002962": "\\sigma_{t}( \\alpha)= \\sqrt{ \\Big( \\mathscr{P}( \\alpha) \\sigma_{t_{c}} \\Big)^{2}+ \\Big( \\sigma_{ej}^{F}( \\alpha) \\Big)^{2}}~.", "UniMER-1M_0002996": "x", "UniMER-1M_0002997": "2x_{0}= \\lambda+x_{0}-4 \\Gamma \\mu \\sigma^{2}/3", "UniMER-1M_0002925": "B=B_{0}- \\frac{1}{2}(1-D_{0})- \\sum \\Phi_{i}|m_{1/2}|e^{i \\phi_{i}}", "UniMER-1M_0002999": "p(h)", "UniMER-1M_0002969": "I= \\frac{1}{2 \\kappa^{2}} \\intd^{D}X \\sqrt{-g} \\left(R- \\frac{1}{2}( \\nabla \\phi)^{2}- \\frac{e^{- \\alpha \\phi}}{2(d+1)!}F_{d+1}^{2} \\right),", "UniMER-1M_0002956": "k=0", "UniMER-1M_0002941": "R[ \\omega]= \\frac{1}{ \\left( \\frac{ \\kappa}{2} \\right)^{2}+ \\left( \\omega- \\Omega \\right)^{2}},", "UniMER-1M_0002975": "\\omega_{2}=2 \\omega_{3})", "UniMER-1M_0002960": "\\varepsilon", "UniMER-1M_0002976": "2", "UniMER-1M_0002937": "Q_{ \\mathrm{TLS,T}}^{ \\mathrm{C1}}=(27.8 \\pm0.4) \\times10^{3}", "UniMER-1M_0002973": "2d", "UniMER-1M_0002953": "i(2k-1) \\ q[_{a{_{1}}} \\chi_{a{_{2}}} . . ._{a{_{2k-1}}}] \\ = \\ 2 \\par tial{_{a}} \\chi_{a{_{1}}} . . ._{a_{2k-1}a}", "UniMER-1M_0003009": "P", "UniMER-1M_0002954": "\\ F_{VW}(r)=-{ \\frac{d}{dr}}U(r)", "UniMER-1M_0003011": "\\beta=1- \\gamma", "UniMER-1M_0002977": "V^{t}", "UniMER-1M_0002978": "\\begin{array}{rl}&{{2+4u^{2}-4u( \\mathrm{cos}( \\omega)+ \\mathrm{cos}((1-m) \\omega))+2 \\mathrm{cos}(m \\omega)=}} \\\\ &{{4 \\mathrm{cos}^{2} \\left[ \\frac{m \\omega}{2} \\right] \\mathrm{sin}^{2} \\left[ \\frac{(m-2) \\omega}{2} \\right]+ \\left[2u-2 \\mathrm{cos} \\left( \\frac{m \\omega}{2} \\right) \\mathrm{cos} \\left( \\frac{(2-m) \\omega}{2} \\right) \\right]^{2}}} \\end{array}", "UniMER-1M_0002957": "R= \\sqrt[6]{2C_{4}/m_{i} \\omega^{2}}", "UniMER-1M_0002979": "k_{f}", "UniMER-1M_0002985": "h=0 \\", "UniMER-1M_0002991": "G(x^{ \\prime},t^{ \\prime};x^{ \\prime},t^{ \\prime})=i \\int \\int \\frac{d \\omega^{ \\prime \\prime}}{2 \\pi} \\frac{dk^{ \\prime \\prime}}{2 \\pi} \\frac{1}{ \\omega^{2}-k^{2}-4+i \\rho} \\left(1+K_{0}(k^{ \\prime \\prime})e^{-2ik^{ \\prime \\prime}x^{ \\prime}} \\right).", "UniMER-1M_0002968": "Nd \\timesNd", "UniMER-1M_0002995": "m=M_{v}+5 \\cdot( \\mathrm{log}_{10}3.64-1)=2.6", "UniMER-1M_0002982": "( \\mu/ \\epsilon)^{1/2}", "UniMER-1M_0002989": "\\Omega_{i}", "UniMER-1M_0002972": "\\mathfrak{p}_{1}, \\mathfrak{p}_{2}, \\mathfrak{p}_{3}, . . ., \\mathfrak{p}_{n}, . . .", "UniMER-1M_0002998": "7", "UniMER-1M_0003025": "\\Delta<0", "UniMER-1M_0003001": "with", "UniMER-1M_0003002": "\\lambda= \\left({{ \\tau_{h}}-0.5} \\right)c_{s}^{2} \\Deltat", "UniMER-1M_0002965": "\\xi_{i} \\equiv \\mathrm{log} \\frac{Y_{i}}{Y(N_{i})}= \\mathrm{log} \\frac{Y_{i}}{Y_{0}N_{i}^{ \\beta}},", "UniMER-1M_0003029": "B \\par allela", "UniMER-1M_0002983": "^4", "UniMER-1M_0003005": "T^{ \\prime}=T(z^{ \\prime})", "UniMER-1M_0003004": "T_{LB} \\sim5400", "UniMER-1M_0003000": "\\kappa^{ \\prime}=- \\nabla \\cdot \\hat{n}^{ \\prime}", "UniMER-1M_0003006": "63 \\times", "UniMER-1M_0003007": "\\alpha_{m}^{ \\prime \\prime}", "UniMER-1M_0003037": "m= \\gammam_{0}", "UniMER-1M_0002966": "\\mathrm{M}", "UniMER-1M_0003013": "2,742", "UniMER-1M_0003040": "^{th}", "UniMER-1M_0003014": "\\mu", "UniMER-1M_0003010": "a_{t}", "UniMER-1M_0003015": "f(t)=-{ \\frac{ \\par tialS(t)}{ \\par tialt}}.", "UniMER-1M_0003044": "(t_{n+1}- \\tau)^{ \\alpha-1}", "UniMER-1M_0002994": "\\mathrm{s_{A}^{2} \\mathrm{z_{A}^{2} \\mathrm{s_{B}^{2}( \\mathrm{x_{A} \\mathrm{ \\overline{{{x}}}_{B}- \\mathrm{y_{A} \\mathrm{ \\overline{{{y}}}_{B})+ \\mathrm{s_{A}^{2} \\mathrm{s_{B}^{2} \\mathrm{z_{B}^{2}( \\mathrm{x_{A} \\mathrm{ \\overline{{{x}}}_{B}- \\mathrm{y_{A} \\mathrm{ \\overline{{{y}}}_{B})}}}}}}}}}}}}}}", "UniMER-1M_0003021": "\\boldsymbol{x}_{t}= \\boldsymbol{w}^{T} \\dot{ \\boldsymbol{g}_{t}}", "UniMER-1M_0003047": "E", "UniMER-1M_0003043": "{ \\bf v}=S{ \\bf v}_{s}+(1-S){ \\bf v}_{w}", "UniMER-1M_0003035": "n-m", "UniMER-1M_0003030": "\\mathbf{x} \\in \\mathbb{R}^{d}", "UniMER-1M_0003020": "\\begin{array}{rl}{{|A|}}&{{= \\left|A_{( \\frac{2}{3},1]} \\right|+|B_{1}|= \\left|A_{( \\frac{2}{3},1]} \\right|+ \\left|E_{B_{1}} \\right|+ \\left|O_{B_{1}} \\right|}} \\\\ &{{ \\leqslant \\left|A_{( \\frac{2}{3},1]} \\right|+ \\frac{n}{3}+3- \\left|A_{( \\frac{1}{2},1]}^{0(3)} \\right|- \\left|A_{( \\frac{1}{2},1]}^{1(3)} \\right|- \\left|A_{( \\frac{1}{2},1]}^{2(3)} \\right|}} \\\\ &{{ \\leqslant \\frac{n}{3}+3- \\left|A_{( \\frac{1}{2}, \\frac{2}{3}]} \\right|,}} \\end{array}", "UniMER-1M_0002993": "\\frac{b_{1 \\!/ \\!2}(u)}{u^{2}} \\; \\sim \\; \\varepsilon_{ \\mathrm{ \\sc riptsize{shear}}} \\;.", "UniMER-1M_0003008": "\\begin{array}{rl}{{ \\left\\langle{{ \\Theta}^{2}} \\right\\rangle=}}&{{2n_{ \\beta}| \\vec{v}_{ \\alpha}| \\tau_{h} \\underbrace{ \\frac{ \\mu^{2}}{m_{ \\alpha}^{2}} \\int_{0}^{2 \\pi} \\int_{ \\theta_{p}}^{ \\theta_{n}}(1- \\mathrm{cos}( \\theta)) \\sigma( \\theta) \\mathrm{sin}( \\theta) \\mathrm{d} \\theta \\mathrm{d} \\varphi}_{ \\mathrm{~m~o~m~e~n~t~u~m~t~r~a~n~s~f~e~r~c~r~o~s~s~-~s~e~c~t~i~o~n~}}}} \\\\ {{=}}&{{4 \\pin_{ \\beta}| \\vec{v}_{ \\alpha}| \\tau_{h}b_{0}^{2} \\left. \\mathrm{ln} \\left[ \\mathrm{sin} \\left( \\frac{ \\theta}{2} \\right) \\right] \\right|_{ \\theta_{p}}^{ \\theta_{n}}.}} \\end{array}", "UniMER-1M_0003003": "\\begin{array}{rlr}{{J_{ij}}}&{{=}}&{{ \\delta_{ij}+a_{i}q_{j} \\tau \\biggl( \\mathrm{cos}{( \\phi_{0})}+ \\frac{ \\xi \\tau}{2} \\mathrm{cos}{(2 \\phi_{0})}}} \\end{array}", "UniMER-1M_0003056": "\\alpha", "UniMER-1M_0003048": "\\rho(i,j)= \\frac{ \\mathrm{cov}(C_{i}^{*},C_{j}^{*})}{[ \\mathrm{cov}(C_{i}^{*},C_{i}^{*}) \\, \\mathrm{cov}(C_{j}^{*},C_{j}^{*})]^{1/2}}~.", "UniMER-1M_0003058": "F_{3}H_{n}/F_{2}H_{n}=0", "UniMER-1M_0003031": "k=0", "UniMER-1M_0003060": "H", "UniMER-1M_0003018": "\\Deltag(Q^{2})= \\left\\langlep^{ \\mu}{ \\frac{1}{2}} \\left| \\intd^{3} \\vec{x}( \\vec{E} \\times \\vec{A})^{z} \\right|p^{ \\mu}{ \\frac{1}{2}} \\right\\rangle \\ .", "UniMER-1M_0003012": "\\rho_{g}(x_{l})= \\frac{Cov \\left[ \\phi_{0}(x_{l}), \\phi_{g}(x_{l}) \\right]}{ \\sigma \\left[ \\phi_{0}(x_{l}) \\right] \\sigma \\left[ \\phi_{g}(x_{l}) \\right]}", "UniMER-1M_0003023": "\\textbf{W}_{i}= \\left( \\rho_{i}, \\rho_{i} \\textbf{U}_{i}, \\rho_{i}E_{i} \\right)", "UniMER-1M_0003032": "\\kappa_{opt}", "UniMER-1M_0003036": "| \\mu_{1})= \\left[ \\begin{array}{c}{{1}} \\\\ {{0}} \\\\ {{ \\vdots}} \\\\ {{0}} \\end{array} \\right], \\quad| \\mu_{2})= \\left[ \\begin{array}{c}{{0}} \\\\ {{1}} \\\\ {{ \\vdots}} \\\\ {{0}} \\end{array} \\right], \\quad . . ., \\quad| \\mu_{2n})= \\left[ \\begin{array}{c}{{0}} \\\\ {{0}} \\\\ {{ \\vdots}} \\\\ {{1}} \\end{array} \\right].", "UniMER-1M_0003053": "\\phi_{3}=3.66 \\times10^{-3}", "UniMER-1M_0003038": "\\begin{array}{rlr}{{ \\left\\|[ \\mathcal{G}_{ \\nu}(z)]^{-1}-[ \\mathcal{G}_{ \\nu}^{i}(z)]^{-1} \\right\\|_{ \\mathcal{B}( \\mathfrak{H}^{( \\nu)})}}}&{{ \\leq}}&{{ \\left\\|[ \\mathcal{G}_{ \\nu}^{i}(z)]^{-1} \\right\\|_{ \\mathcal{B}( \\mathfrak{H}^{( \\nu)})} \\left\\| \\left[ \\mathcal{S}_{ \\nu}(z)- \\mathcal{S}_{ \\nu}^{i}(z) \\right][ \\mathcal{G}_{ \\nu}(z)]^{-1} \\right\\|_{ \\mathcal{B}( \\mathfrak{H}^{( \\nu)})}}} \\\\ &{{ \\leq}}&{{ \\frac{1}{| \\! \\operatorname{Im}z|} \\left\\| \\left[ \\mathcal{S}_{ \\nu}(z)- \\mathcal{S}_{ \\nu}^{i}(z) \\right][ \\mathcal{G}_{ \\nu}(z)]^{-1} \\right\\|_{ \\mathcal{B}( \\mathfrak{H}^{( \\nu)})} \\to0,}} \\end{array}", "UniMER-1M_0003042": "\\begin{array}{rl}{{e_{x}}}&{{= \\frac{E_{x^{ \\prime}}}{ \\sqrt{3}}- \\frac{E_{y^{ \\prime}}(3+ \\sqrt{3})}{6}+ \\frac{E_{z^{ \\prime}}(3- \\sqrt{3})}{6}}} \\\\ {{e_{y}}}&{{= \\frac{E_{x^{ \\prime}}}{ \\sqrt{3}}+ \\frac{E_{y^{ \\prime}}(3- \\sqrt{3})}{6}- \\frac{E_{z^{ \\prime}}(3+ \\sqrt{3})}{6}}} \\\\ {{e_{z}}}&{{= \\frac{E_{x^{ \\prime}}}{ \\sqrt{3}}+ \\frac{E_{y^{ \\prime}}}{ \\sqrt{3}}+ \\frac{E_{z^{ \\prime}}}{ \\sqrt{3}}}} \\end{array}", "UniMER-1M_0003041": "\\mu \\in \\mathcal{D}_{ \\mathrm{~v~a~l~i~d~a~t~i~o~n~}}= \\{0.4 \\}", "UniMER-1M_0003024": "\\Delta \\omega^{ \\prime}=0.001 \\omega_{0}", "UniMER-1M_0003054": "{f(x)= \\frac{2 \\Deltap}{L_{x}} \\frac{W}{ \\rhoU^{2}},}", "UniMER-1M_0003073": "19", "UniMER-1M_0003059": "l_{c}", "UniMER-1M_0003039": "\\Pi", "UniMER-1M_0003046": "^{+}S_{2}= \\frac{i}{4}{^+ \\tau} \\int_{M}eF^{ \\mu \\nui}F^{ \\alpha \\betaj}g_{ij} \\, \\,{^+{ \\calR}}_{ \\mu \\nu}^{ab} \\, \\,{^+{ \\calR}}_{ \\alpha \\beta}^{cd} \\, \\epsilon_{abcd} \\,.", "UniMER-1M_0003052": "\\begin{array}{rl}{{2 \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}}}&{{ \\let^{-( \\frac{1}{4}- \\frac{ \\delta}{2})} \\sqrt{86M^{2}K^{6}t^{ \\frac{3}{2}}+ \\sum_{s=0}^{t-1} \\lVert \\mathbf{Q}_{s} \\rVert_{2}^{2}},}} \\\\ &{{ \\Updownarrow}} \\\\ {{4 \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{2}}}&{{ \\let^{- \\frac{1}{2}+ \\delta} \\left(86M^{2}K^{6}t^{ \\frac{3}{2}}+ \\sum_{s=0}^{t-1} \\lVert \\mathbf{Q}_{s} \\rVert_{2}^{2} \\right),}} \\\\ &{{ \\Uparrow(a)}} \\\\ {{4 \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{2}}}&{{ \\let^{- \\frac{1}{2}+ \\delta} \\left(86M^{2}K^{6}t^{ \\frac{3}{2}}+ \\frac{1}{3MK^{3}} \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{3} \\right)}} \\\\ &{{ \\Updownarrow}} \\\\ {{4 \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{2}}}&{{ \\let^{- \\frac{1}{2}+ \\delta} \\left( \\frac{1}{3} \\cdot258M^{2}K^{6}t^{ \\frac{3}{2}}+ \\frac{2}{3} \\cdot \\frac{1}{2MK^{3}} \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{3} \\right),}} \\\\ &{{ \\Uparrow(b)}} \\\\ {{4 \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{2}}}&{{ \\let^{- \\frac{1}{2}+ \\delta} \\cdot258^{ \\frac{1}{3}}2^{- \\frac{2}{3}}t^{ \\frac{1}{2}} \\lVert \\mathbf{Q}_{t-1} \\rVert_{1}^{2}}} \\\\ &{{ \\Updownarrow}} \\\\ {{4}}&{{ \\let^{ \\delta} \\left( \\frac{258}{4} \\right)^{ \\frac{1}{3}},}} \\end{array}", "UniMER-1M_0003050": "a_{4}=in_{2}k_{0}L_{abs}f_{4312}a_{1}a_{2}a_{3}^{*} \\mathrm{exp}(-i \\Delta \\betaz)", "UniMER-1M_0003079": "A^{ \\mathrm{T}}", "UniMER-1M_0003080": "\\xi=ka", "UniMER-1M_0003027": "\\begin{array}{r}{{c_{||}^{2}= \\frac{E^{ \\textrm{sph}}+2E^{ \\textrm{dev}}}{3 \\rho}, \\quad \\gamma_{1}= \\rhoc-3E^{ \\textrm{sph}} \\chi^{2}T_{0}, \\quad \\gamma_{2}= \\rhoc- \\frac{6E^{ \\textrm{dev}}E^{ \\textrm{sph}} \\chi^{2}T_{0}}{E^{ \\textrm{sph}}+2E^{ \\textrm{dev}}}, \\quad E^{ \\textrm{sph}}=3K, \\quad E^{ \\textrm{dev}}=2G}} \\end{array}", "UniMER-1M_0003063": "\\omega_{z}", "UniMER-1M_0003082": "80 \\", "UniMER-1M_0003026": "\\eta_{m \\pm}=2 \\pi \\intf(r)R_{m \\pm \\Deltal}R_{m}^{*}rdr", "UniMER-1M_0003022": "{ \\frac{4k+1}{ \\beta}}=aS_{2k}+b(R_{2k}+R_{2k+1}+S_{2k}^{2})", "UniMER-1M_0003045": "\\beta<1", "UniMER-1M_0003086": "L", "UniMER-1M_0003051": "w( \\ell)", "UniMER-1M_0003065": "A_{LL}^{Z^{0}}(y) \\sim \\Sigma_{i}{ \\frac{ \\Deltaq_{i}(x_{a}) \\Delta \\bar{q}_{i}(x_{b})+ \\Delta \\bar{q}_{i}(x_{a}) \\Deltaq_{i}(x_{b})}{q_{i}(x_{a}) \\bar{q}_{i}(x_{b})+ \\bar{q}_{i}(x_{a})q_{i}(x_{b})}} \\nonumber", "UniMER-1M_0003092": "<n>", "UniMER-1M_0003093": "a \\to \\infty", "UniMER-1M_0003069": "X", "UniMER-1M_0003055": "E(t)=E_{0}e^{- \\frac{t}{ \\tau_{r}}}+E_{ \\infty} \\left(1-e^{- \\frac{t}{ \\tau_{r}}} \\right).", "UniMER-1M_0003066": "N( \\vec{x}_{m})^{ \\zeta}", "UniMER-1M_0003057": "\\mu_{ \\mathrm{eff}}= \\left( \\mu+ \\mu_{t} \\right)", "UniMER-1M_0003019": "\\left[A_{i}( \\vec{x}),A_{j}( \\vec{y}) \\right]= \\frac{i}{ \\kappa} \\varepsilon^{ij} \\delta^{2}( \\vec{x}- \\vec{y}) \\;,", "UniMER-1M_0003085": "t_{ij}=U_{i} \\capU_{j} \\toG", "UniMER-1M_0003071": "\\mathrm{~m~}/ \\sqrt{ \\mathrm{~H~z~}}", "UniMER-1M_0003076": "\\mathrm{SU}(n-1) \\to \\mathrm{SU}(n) \\toS^{2n-1}.", "UniMER-1M_0003081": "\\mathbf{H}_{ \\mathrm{tot}}= \\mathbf{H}_{ \\mathrm{i}}+ \\mathbf{H}_{ \\mathrm{r}}.", "UniMER-1M_0003062": "\\Sigma( \\Psi_{p}^{ \\mathrm{tot}})= \\nu_{0}^{2}- \\frac{1}{3} \\sum_{i=1} \\nu_{i}^{2}= \\frac{4}{3} \\nu_{0}^{2}- \\frac{1}{3} \\:,", "UniMER-1M_0003100": "t \\rightarrow \\infty", "UniMER-1M_0003078": "\\begin{array}{c}{{{ \\alpha(0)=0,}}} \\\\ {{{0 \\leq \\alpha( \\infty) \\equiv \\alpha_{ \\infty}<2 \\pi,}}} \\end{array}", "UniMER-1M_0003028": "\\rho(k)={ \\frac{1}{2E_{k}}} \\, \\left({ \\frac{2 \\Gamma_{k}}{(k^{0}{-}E_{k})^{2}+ \\Gamma_{k}^{2}}}-{ \\frac{2 \\Gamma_{k}}{(k^{0}{+}E_{k})^{2}+ \\Gamma_{k}^{2}}} \\right) \\times \\left(1+{ \\calO}( \\lambda^{2}) \\right) \\;,", "UniMER-1M_0003109": "_2", "UniMER-1M_0003089": "L", "UniMER-1M_0003061": "\\begin{array}{rlr}{{ \\frac{ \\sigma}{M}|_{composed}}}&{{=}}&{{ \\frac{ \\sigma}{M}|_{grain}* \\frac{M_{grain}}{M},}} \\end{array}", "UniMER-1M_0003112": "s", "UniMER-1M_0003064": "\\Re \\langle \\psi,B \\psi \\rangle \\ge \\mu \\lVert \\psi \\rVert_{L_{x}^{p+2}}^{p+2} \\mathrm{~f~o~r~a~l~l~} \\psi \\inH^{1}( \\mathbb{T}^{2})", "UniMER-1M_0003074": "\\Gamma_{max}t \\lesssim10", "UniMER-1M_0003068": "A= \\sqrt{a+ \\frac{1}{ \\sqrt{a+ \\frac{1}{ \\sqrt{a}}}}}+ \\sqrt{b}", "UniMER-1M_0003070": "\\begin{array}{rl}{{I \\dot{ \\boldsymbol{ \\omega}}_{ \\mathrm{~f~}}}}&{{= \\mathbf{m}_{ \\mathrm{~f~}} \\times \\mathbf{B}_{ \\mathrm{~r~}}- \\zeta_{ \\mathrm{~r~o~t~}} \\boldsymbol{ \\omega}_{ \\mathrm{~f~}}}} \\\\ {{ \\mathfrak{m} \\dot{ \\mathbf{v}}_{ \\mathrm{~f~}}}}&{{= \\mathbf{F}_{ \\mathrm{~d~i~p~}}- \\zeta_{ \\mathrm{~t~r~a~n~s~}} \\mathbf{v}_{ \\mathrm{~f~}}-g \\hat{ \\mathbf{z}}}} \\end{array}", "UniMER-1M_0003103": "X \\subseteq \\mathbb{C}^{n}", "UniMER-1M_0003116": "u_{y}(y,t)", "UniMER-1M_0003075": "\\dot{ \\psi}=- \\mathrm{~d~i~a~g~}( \\lambda_{2},..., \\lambda_{N \\cdotM}) \\psi(t)+ \\xi(t)", "UniMER-1M_0003077": "\\begin{array}{rlr}{{u_{1}(y)}}&{{=}}&{{ \\mathrm{exp} \\left[- \\frac{1}{2~ \\sqrt{E}}~ \\mathrm{exp}(2~y)+ \\frac{y}{2} \\sqrt{1-4~ \\epsilon_{1}} \\right]L_{- \\frac{1}{2}+ \\frac{E^{ \\frac{3}{2}}}{4}- \\frac{1}{4} \\sqrt{1-4 \\epsilon_{1}}}^{ \\frac{1}{2} \\sqrt{1-4 \\epsilon_{1}}} \\left[ \\frac{ \\mathrm{exp}(2~y)}{ \\sqrt{E}} \\right].}} \\end{array}", "UniMER-1M_0003122": "c", "UniMER-1M_0003114": "\\begin{array}{rl}{{ \\frac{ \\par tial \\mathbf{b}^{ \\prime}}{ \\par tialt}- \\eta \\nabla^{2} \\mathbf{b}^{ \\prime}+ \\left( \\left\\langle \\mathbf{U} \\right\\rangle \\cdot \\nabla \\right) \\mathbf{b}^{ \\prime}- \\left( \\left\\langle \\mathbf{B} \\right\\rangle \\cdot \\nabla \\right) \\mathbf{u}^{ \\prime}+ \\left( \\mathbf{u}^{ \\prime} \\cdot \\nabla \\right) \\left\\langle \\mathbf{B} \\right\\rangle- \\left( \\mathbf{b}^{ \\prime} \\cdot \\nabla \\right) \\left\\langle \\mathbf{U} \\right\\rangle}} \\end{array}", "UniMER-1M_0003091": "u=u^{ \\prime}/U_{s}", "UniMER-1M_0003107": "\\begin{array}{rlrl}{{{2} \\frac{d}{dt} \\Phi_{t}( \\xi)}}&{{= \\mathbf{v}_{0}^{ \\pm}( \\Phi_{t}( \\xi),t)}}&{{ \\quad}}&{{ \\mathrm{for~all~} \\xi \\in \\overline{{{ \\Omega_{0}^{ \\pm}}}},t \\in[0,T_{0}],}} \\\\ {{ \\Phi_{0}( \\xi)}}&{{= \\xi}}&{{ \\quad}}&{{ \\mathrm{for~all~} \\xi \\in \\overline{{{ \\Omega_{0}^{ \\pm}}}}.}} \\end{array}", "UniMER-1M_0003126": "\\Re", "UniMER-1M_0003127": "\\begin{array}{r}{{ \\mu_{p}= \\mu_{s}+ \\mu_{i}.}} \\end{array}", "UniMER-1M_0003104": "0= \\left[ \\frac{ \\par tial}{ \\par tialk_{1}} \\mathrm{ln} P(k_{1}|K,S) \\right]_{k_{1}=k^{*}},", "UniMER-1M_0003097": "\\begin{array}{rl}{{ \\alpha \\colon \\mathcal{E} \\left( \\mathbb{K} \\right)}}&{{ \\longrightarrow \\mathcal{W} \\left( \\mathbb{K} \\right)}} \\\\ {{( \\hat{x}, \\hat{y})}}&{{ \\longmapsto(x,y)= \\left(x_{1} \\frac{1+ \\hat{y}}{1- \\hat{y}},y_{1} \\frac{(1+ \\hat{y})}{ \\hat{x}(1- \\hat{y})} \\right)}} \\\\ {{ \\beta \\colon \\mathcal{W} \\left( \\mathbb{K} \\right)}}&{{ \\longrightarrow \\mathcal{E} \\left( \\mathbb{K} \\right)}} \\\\ {{(x,y)}}&{{ \\longmapsto( \\hat{x}, \\hat{y})= \\left( \\frac{y_{1}x}{x_{1}y}, \\frac{x-x_{1}}{x+x_{1}} \\right),}} \\end{array}", "UniMER-1M_0003072": "\\mathbb{Z}[t_{1}, . . .,t_{k}]", "UniMER-1M_0003096": "\\bar{T}^{ \\mu \\nu}= \\bar{T}_{q}^{ \\mu \\nu}+ \\bar{T}_{g}^{ \\mu \\nu},", "UniMER-1M_0003110": "{C}_{6}^{(1)}", "UniMER-1M_0003121": "(1,3,{ \\bar{3}})", "UniMER-1M_0003124": "\\sqrt[[objectObject]]{4}", "UniMER-1M_0003016": "\\begin{array}{rl}{{ \\operatorname{lim}_{t \\to \\infty}}}&{{ \\int_{t/2-T}^{t/2+T}dt_{2} \\;e^{-t_{2}H} \\;{ \\epsilon}V_{ \\Psi} \\;e^{-t_{1}H}= \\int_{-T}^{T}dt_{1} \\; \\Pi_{0} \\;{ \\epsilon}V_{ \\Psi} \\; \\Pi_{0}}} \\\\ &{{= \\int_{-T}^{T}dt_{1} \\;0= \\mathcal{O}(T) \\cdot0=0.}} \\end{array}", "UniMER-1M_0003094": "R_{q} \\equivg_{Vq}/g_{Aq}=1-4|Q_{q}|s^{2}+ \\frac{3|Q_{q}|}{4 \\pi(c^{2}-s^{2})} \\bar{ \\alpha}V_{Rq}(t,h) \\; \\;.", "UniMER-1M_0003113": "\\begin{array}{rl}{{=}}&{{ \\operatorname{argmax}_{ \\theta} \\left[ \\operatorname{lim}_{h \\to0^{+}}{ \\frac{1}{h}} \\int_{x_{j}}^{x_{j}+h}f(x \\mid \\theta) \\,dx \\right]= \\operatorname{argmax}_{ \\theta}f(x_{j} \\mid \\theta).}} \\end{array}", "UniMER-1M_0003120": "Q \\left(z+z^{ \\prime} \\right) \\equivQ^{ \\prime}= \\left[ \\begin{array}{ll}{{a^{ \\prime}}}&{{b^{ \\prime}}} \\\\ {{b^{ \\prime}}}&{{c^{ \\prime}}} \\end{array} \\right]= \\left[ \\begin{array}{ll}{{a+bz^{ \\prime}+ \\left(cz^{ \\prime}+b \\right)z^{ \\prime}}}&{{b+cz^{ \\prime}}} \\\\ {{b+cz^{ \\prime}}}&{{c}} \\end{array} \\right].", "UniMER-1M_0003137": "x>1", "UniMER-1M_0003139": "h_{n+1}=h_{n}+h_{n}r_{n}", "UniMER-1M_0003145": "\\theta=3", "UniMER-1M_0003146": "\\phi_{1}", "UniMER-1M_0003147": "8^{ \\circ}", "UniMER-1M_0003117": "\\begin{array}{rl}{{f(w_{t+1})-f(w_{t}) \\le}}&{{ \\gamma \\| \\nablaf(w_{t}) \\|^{2- \\beta}+ \\frac{ \\gamma}{6} \\Big( \\mathcal{O}( \\gamma) \\| \\nablaf(w_{t}) \\|^{2-2 \\beta}}} \\\\ &{{+ \\mathcal{O}( \\gamma) \\| \\nablaf(w_{t}) \\|^{2+ \\alpha-2 \\beta}+ \\mathcal{O}( \\gamma^{ \\frac{1}{1- \\alpha}}) \\| \\nablaf(w_{t}) \\|^{ \\frac{(2- \\alpha)(1- \\beta)}{1- \\alpha}} \\Big).}} \\end{array}", "UniMER-1M_0003149": "\\omega_{i}", "UniMER-1M_0003150": "\\sigma", "UniMER-1M_0003125": ">1", "UniMER-1M_0003115": "\\frac{C_{ \\mathrm{HF}}}{C_{ \\mathrm{L2}}} \\simeq140/1.7", "UniMER-1M_0003105": "Q^{2} \\in \\left\\{0,Q_{ \\mathrm{max}}^{2} \\right\\}", "UniMER-1M_0003123": "\\frac{ \\Lambda_{FSS}^{eff}}{ \\Lambda_{FSS}}-1", "UniMER-1M_0003033": "\\begin{array}{rl}&{{ \\big(u_{1}h_{i \\delta}( \\alpha_{1}) \\prod_{ \\ell \\in[ \\Delta] \\setminus \\{i \\}} \\tilde{f}_{ \\ell}( \\alpha_{1}), . . .,u_{ \\Delta}h_{i \\delta}( \\alpha_{ \\Delta}) \\prod_{ \\ell \\in[ \\Delta] \\setminus \\{i \\}} \\tilde{f}_{ \\ell}( \\alpha_{ \\Delta}),}} \\\\ &{{v_{1}h_{i \\delta}( \\beta_{1}) \\prod_{ \\ell \\in[ \\Delta] \\setminus \\{i \\}} \\tilde{f}_{ \\ell}( \\beta_{1}), . . .,v_{k}h_{i \\delta}( \\beta_{k}) \\prod_{ \\ell \\in[ \\Delta] \\setminus \\{i \\}} \\tilde{f}_{ \\ell}( \\beta_{k}) \\big)}} \\\\ &{{ \\cdot( \\phi( \\alpha_{1}), . . ., \\phi( \\alpha_{ \\Delta}), \\phi( \\beta_{1}), . . ., \\phi( \\beta_{k}))^{T}=0.}} \\end{array}", "UniMER-1M_0003131": "| \\psi \\rangle", "UniMER-1M_0003140": "\\sigma_{ \\kappa}", "UniMER-1M_0003095": "\\begin{array}{rl}{{{ \\bf r}_{V}}}&{{= \\sum_{e}{ \\bf r}_{Ve}= \\sum_{e} \\big\\langle{{ \\bf w}_{V}} \\,, \\par tial_{{t}}{ \\bf U} \\big\\rangle_{ \\Omega_{e}^{h}}+ \\big\\langle{{ \\bf w}_{V}} \\,, \\nabla \\cdot{ \\bf F} \\big\\rangle_{ \\Omega_{e}^{h}}- \\big\\langle{{ \\bf w}_{V}} \\,, \\nabla \\cdot{ \\bf Q} \\big\\rangle_{ \\Omega_{e}^{h}}- \\big\\langle{{ \\bf w}_{V}} \\,,{ \\bf S} \\big\\rangle_{ \\Omega_{e}^{h}}={ \\bf 0}}} \\\\ {{{ \\bf r}_{E}}}&{{= \\sum_{e}{ \\bf r}_{Ee}= \\sum_{e} \\big\\langle{{ \\bf w}_{E}} \\,,{ \\bf E} \\big\\rangle_{ \\Omega_{e}^{h}}- \\big\\langle{{ \\bf w}_{E}} \\,, \\nabla \\cdot{ \\bf G} \\big\\rangle_{ \\Omega_{e}^{h}}={ \\bf 0} \\,.}} \\end{array}", "UniMER-1M_0003099": "C_{1}=x^{1} \\mathrm{cos}( \\xix^{2})+x^{2} \\mathrm{sin}( \\xix^{2}) \\ , \\  \\  \\ C_{2}=x^{2} \\mathrm{cos}( \\xix^{2})-x^{1} \\mathrm{sin}( \\xix^{2}) \\ .", "UniMER-1M_0003161": "\\zeta^{i}", "UniMER-1M_0003130": "\\begin{array}{rl}{{ \\left( \\frac{ \\delta}{ \\delta \\zeta_{1}} \\left[G_{12} \\frac{ \\delta \\phi}{ \\delta \\zeta_{2}} \\right] \\right)_{i,j,k}=}}&{{ \\left[G_{12} \\frac{ \\delta \\phi}{ \\delta \\zeta_{2}} \\right]_{i+1/2,j,k}- \\left[G_{12} \\frac{ \\delta \\phi}{ \\delta \\zeta_{2}} \\right]_{i-1/2,j,k}}} \\\\ {{=}}&{{+ \\frac{G_{12}^{2}|_{i+1,j,k}}{2} \\left[+ \\frac{ \\phi|_{i,j+1,k}+ \\phi|_{i+1,j+1,k}}{2} \\right.}} \\end{array}", "UniMER-1M_0003101": "\\frac{1}{ \\sqrt{- \\operatorname{det}G}} \\, \\par tial_{ \\mu} \\left( \\sqrt{- \\operatorname{det}G} \\,G^{ \\mu \\nu} \\, \\par tial_{ \\nu} \\eta_{a} \\right)= \\frac{1}{2} \\,T(x) \\, \\delta^{d}(x)", "UniMER-1M_0003143": "\\vec{F}_{T}= \\frac{d \\vec{P}_{T}}{dt}= \\frac{ \\mu_{0}}{2 \\pi} \\sigma \\frac{dJ_{0}}{dt}wab^{2} \\left( \\tilde{ \\Lambda}( \\frac{a}{ \\Delta_{1}}, \\frac{b}{ \\Delta_{1}})- \\tilde{ \\Lambda}( \\frac{a}{ \\Delta_{2}}, \\frac{b}{ \\Delta_{2}}) \\right) \\hat{x}.", "UniMER-1M_0003135": "\\sim6~ \\", "UniMER-1M_0003111": "a_{i}", "UniMER-1M_0003132": "\\tilde{ \\nu}_{i}(l) \\proptol^{d_{ \\mathrm{~f~}}(i)}", "UniMER-1M_0003088": "\\begin{array}{r}{{( \\lambda_{1x})^{m_{x}^{1}}( \\lambda_{1y})^{m_{y}^{1}}( \\lambda_{2x})^{m_{x}^{2}}( \\lambda_{2y})^{m_{y}^{2}}}} \\\\ {{ \\left(i \\frac{ \\mathcal{D} \\cdot \\hat{ \\mu}_{2}}{ \\hat{ \\mu}_{1} \\cdot \\hat{ \\lambda}_{2}} \\right)^{m_{x}^{1}+m_{y}^{1}} \\left(i \\frac{ \\mathcal{D} \\cdot \\hat{ \\mu}_{1}}{ \\hat{ \\mu}_{2} \\cdot \\hat{ \\lambda}_{1}} \\right)^{m_{x}^{2}+m_{y}^{2}}}} \\end{array}", "UniMER-1M_0003090": "n^{B}", "UniMER-1M_0003106": "{ \\bf k}_{f}", "UniMER-1M_0003138": "\\Delta \\mathbf{d}", "UniMER-1M_0003144": "\\zeta_{2} \\sim0.725", "UniMER-1M_0003128": "\\mathrm{8 \\times10^{3}}", "UniMER-1M_0003087": "P_{1}(B^{0}->K^{ \\star} \\mu^{+} \\mu^{-})", "UniMER-1M_0003173": "\\mu_{|1- \\lambda_{ \\mathrm{~e~x~t~}}|}", "UniMER-1M_0003164": "s_{ \\gamma}", "UniMER-1M_0003167": "a_{1}=0.0061", "UniMER-1M_0003157": "\\int_{ \\mathscr{R}_{ \\pm}^{m}} \\varphi \\, \\Bigl(r- \\, \\Deltat \\,[ \\vec{W}^{m} \\cdot \\vec{e}_{1}] \\Bigr) \\, \\mathcal{J}^{m} \\, \\mathrm{d} r \\mathrm{d} z= \\int_{ \\mathscr{R}_{ \\pm}^{m-1}} \\varphi \\circ \\mathcal{ \\vec{A}}^{m}[t_{m-1}]^{-1} \\,r \\, \\mathrm{d} r \\mathrm{d} z.", "UniMER-1M_0003151": "\\mathbf{U}q={ \\frac{q}{ \\lVertq \\rVert}}.", "UniMER-1M_0003148": "\\theta= \\mathrm{exp}(2i \\pi(v_{1}J_{45}+v_{2}J_{67}+v_{3}J_{89}))", "UniMER-1M_0003152": "\\beta", "UniMER-1M_0003119": "S_{n}^{e^{ \\prime}n^{ \\prime}/II}=V \\mathrm{exp}(-2i \\pie^{ \\prime}n/k)/2 \\mathrm{sin}( \\pie^{ \\prime}/k)", "UniMER-1M_0003162": "\\Delta_{ \\psi(t)}X", "UniMER-1M_0003129": "v_{t}= \\sqrt{2k_{B}T_{r}/m_{r}}", "UniMER-1M_0003153": "P_{0}V_{0}^{ \\gamma}=PV^{ \\gamma}= \\operatorname{constant}.", "UniMER-1M_0003142": "\\mu \\_{ \\Sigma,c, \\mathrm{<PERSON><PERSON>}}= \\sigma \\Deltat \\,V_{c}^{-1/3}", "UniMER-1M_0003175": "l_{2}", "UniMER-1M_0003170": "k", "UniMER-1M_0003156": "f^{i}", "UniMER-1M_0003108": "\\operatorname{lim}_{t \\rightarrow0^{+}}y(t)=0", "UniMER-1M_0003188": "0.1", "UniMER-1M_0003181": "j", "UniMER-1M_0003190": "i,j", "UniMER-1M_0003169": "50 \\", "UniMER-1M_0003171": "\\vec{ \\mathcal{F}}_{e}( \\vec{Q}) \\equiv \\vec{E}_{e}( \\vec{Q}) \\: \\hat{i}+ \\vec{F}_{e}( \\vec{Q}) \\: \\hat{j}+ \\vec{G}_{e}( \\vec{Q}) \\: \\hat{k} \\mathrm{~,~}", "UniMER-1M_0003154": "[ \\, \\phi(x), \\par tial_{-} \\phi(y) \\,]_{x^{+}=y^{+}}= \\frac{i}{2} \\delta(x^{-}-y^{-}) \\delta^{2}(x_{ \\perp}-y_{ \\perp}),", "UniMER-1M_0003166": "\\begin{array}{rl}{{ \\xi_{1} \\big((T_{n,3}^{a,b})^{c} \\big)=}}&{{~ \\sqrt{ \\frac{4n+1+ \\sqrt{(4n+1)^{2}-64(a+1)(b+1)}}{2}} \\quad \\mathrm{and}}} \\\\ {{ \\xi_{2} \\big((T_{n,3}^{a,b})^{c} \\big)=}}&{{~ \\sqrt{ \\frac{4n+1- \\sqrt{(4n+1)^{2}-64(a+1)(b+1)}}{2}}.}} \\end{array}", "UniMER-1M_0003155": "N=40", "UniMER-1M_0003165": "\\Psi^{0}=N \\hat{A}[ \\Psi_{A} \\Psi_{B}]", "UniMER-1M_0003168": "E_{barrier} \\approxE_{P} \\left({ \\frac{R}{ \\ell_{P}}} \\right)^{2} \\left({ \\frac{T}{T_{P}}} \\right),", "UniMER-1M_0003158": "B_{ \\mu}^{a}= \\sum_{p=0}^{ \\infty} \\Lambda^{p} \\stackrel{({p})}{B_{ \\mu}^{a}}", "UniMER-1M_0003174": "\\Delta=0.2", "UniMER-1M_0003159": "10g", "UniMER-1M_0003163": "\\begin{array}{r}{{ \\mathrm{~b~i~t~v~a~l~u~e~}= \\left\\{ \\begin{array}{ll}{{1}}&{{ \\ x_{ \\phi}>x_{0}}} \\\\ {{0}}&{{ \\ x_{ \\phi}<-x_{0}}} \\\\ {{ \\mathrm{~i~n~c~o~n~c~l~u~s~i~v~e~}}}&{{-x_{0}<x_{ \\phi}<x_{0}.}} \\end{array} \\right.}} \\end{array}", "UniMER-1M_0003176": "^ \\mathsection", "UniMER-1M_0003177": "(( \\widehat{B}_{1})_{x})_{ij}+(( \\widehat{B}_{2})_{y})_{ij}", "UniMER-1M_0003172": "\\frac{ \\par tialG}{ \\par tialu_{k}}= \\frac{1}{T} \\int_{0}^{T} \\! \\! \\mathrm{d} t \\; \\mathrm{Tr}[A \\frac{ \\par tial \\rho_{u}}{ \\par tialu_{k}}(t)],", "UniMER-1M_0003184": "\\mathbf{V}", "UniMER-1M_0003189": "L^{2}", "UniMER-1M_0003193": "\\simeq-2", "UniMER-1M_0003195": "2.5", "UniMER-1M_0003216": "\\rho_{ \\psi}", "UniMER-1M_0003197": "\\left\\{Q_{i},Q_{j} \\right\\}=2k \\, \\epsilon_{ij} \\,.", "UniMER-1M_0003198": "d \\ggh", "UniMER-1M_0003191": "\\mathsf{U}_{ \\varepsilon}= \\mathrm{exp}_{ \\mathrm{id}}( \\mathsf{B}_{ \\varepsilon}^{s})", "UniMER-1M_0003220": "^{4}", "UniMER-1M_0003187": "\\int_{ \\mathbb{R}^{n}} \\int_{ \\mathbb{R}^{n}} \\varphi( \\mathbf{t}) \\varphi( \\mathbf{s}) \\prod_{i=1}^{n}|t_{i}-s_{i}|^{2H-2} \\ensuremath{ \\mathrm{d}} \\mathbf{t} \\ensuremath{ \\mathrm{d}} \\mathbf{s} \\leC_{H}^{n} \\left( \\int_{ \\mathbb{R}^{n}} \\left| \\varphi( \\mathbf{t}) \\right|^{1/H} \\ensuremath{ \\mathrm{d}} \\mathbf{t} \\right)^{2H},", "UniMER-1M_0003182": "Re \\ge55", "UniMER-1M_0003215": "C_{ \\mathrm{O}_{2}}=1 \\; \\mathrm{mol}/ \\mathrm{m}^{3}", "UniMER-1M_0003205": "x", "UniMER-1M_0003204": "y-y_{1}={ \\frac{y_{2}-y_{1}}{x_{2}-x_{1}}}(x-x_{1}).", "UniMER-1M_0003208": "\\gamma", "UniMER-1M_0003194": "\\Delta \\Sigma_{np}= \\frac{g^{2}}{6} \\frac{ \\pi^{2}}{(2 \\pi)^{4}} \\int_{0}^{ \\infty} \\frac{d \\alpha}{ \\alpha^{2}}e^{- \\alpham^{2}- \\frac{ \\tilde{p}^{2}}{4 \\alpha}},", "UniMER-1M_0003183": "f_{i}: \\mathbb{R}^{m} \\to \\mathbb{R}^{n}", "UniMER-1M_0003207": "\\Delta \\tilde{ \\Lambda}=| \\bar{ \\tilde{ \\Lambda}}_{ \\mathrm{num.}}- \\bar{ \\tilde{ \\Lambda}}_{ \\mathrm{ana.}}|=1.7710-1.820=0.049", "UniMER-1M_0003219": "\\begin{array}{rl}{{U}}&{{= \\left[ \\begin{array}{ll}{{U_{ \\widehat{k}}}}&{{ \\widehat{U}}} \\end{array} \\right], \\quad V= \\left[ \\begin{array}{ll}{{V_{ \\widehat{k}}}}&{{ \\widehat{V}}} \\end{array} \\right], \\quad W= \\left[ \\begin{array}{ll}{{W_{ \\widehat{k}}}}&{{ \\widehat{W}}} \\end{array} \\right], \\quad Z= \\left[ \\begin{array}{ll}{{Z_{ \\widehat{k}}}}&{{ \\widehat{Z}}} \\end{array} \\right],}} \\\\ {{D_{A}}}&{{= \\operatorname{diag} \\left(D_{A_{ \\widehat{k}}}, \\widehat{D}_{A} \\right), \\quad D_{B}= \\operatorname{diag} \\left(D_{B_{ \\widehat{k}}}, \\widehat{D}_{B} \\right), \\quad D_{G}= \\operatorname{diag} \\left(D_{G_{ \\widehat{k}}}, \\widehat{D}_{G} \\right),}} \\end{array}", "UniMER-1M_0003210": "\\alpha: \\mathcal{M}_{6 \\times6} \\left( \\mathbb{R} \\right) \\to \\mathbb{R}^{4}", "UniMER-1M_0003211": "\\sigma", "UniMER-1M_0003202": "t>0", "UniMER-1M_0003180": "\\begin{array}{r}{{ \\left( \\begin{array}{ll}{{F_{ \\alpha, \\beta}^{(12)}}}&{{F_{ \\beta, \\alpha}^{(12)}}} \\end{array} \\right) \\left( \\begin{array}{ll}{{(r_{ \\alpha}-r_{ \\alpha, \\beta})z_{1}-1}}&{{ \\frac{r_{ \\beta}}{r_{ \\alpha}}r_{ \\alpha, \\beta}z_{1}}} \\\\ {{ \\frac{r_{ \\alpha}}{r_{ \\beta}}r_{ \\beta, \\alpha}z_{1}}}&{{(r_{ \\beta}-r_{ \\beta, \\alpha})z_{1}-1}} \\end{array} \\right)=}} \\\\ {{- \\left( \\begin{array}{ll}{{F_{ \\alpha, \\beta}^{()}}}&{{F_{ \\beta, \\alpha}^{()}}} \\end{array} \\right) \\left( \\begin{array}{ll}{{(r_{ \\alpha}-r_{ \\alpha, \\beta})z_{2}-1}}&{{ \\frac{r_{ \\beta}}{r_{ \\alpha}}r_{ \\alpha, \\beta}z_{2}}} \\\\ {{ \\frac{r_{ \\alpha}}{r_{ \\beta}}r_{ \\beta, \\alpha}z_{2}}}&{{(r_{ \\beta}-r_{ \\beta, \\alpha})z_{2}-1}} \\end{array} \\right)}} \\end{array}", "UniMER-1M_0003192": "[b_{i},b_{j}^{ \\dagger}]= \\delta_{ij}(1+2 \\omegaL_{i})", "UniMER-1M_0003212": "\\Leftrightarrow", "UniMER-1M_0003209": "a( \\Omega)", "UniMER-1M_0003186": "\\mathcal{F}( \\rho_{ \\mathcal{T}}^, \\rho_{ \\widetilde{ \\mathcal{T}}})= \\left[ \\mathrm{~T~r~} \\left( \\sqrt{ \\sqrt{ \\rho_{ \\mathcal{T}}} \\rho_{ \\widetilde{ \\mathcal{T}}} \\sqrt{ \\rho_{ \\mathcal{T}}}} \\right) \\right]^{2},", "UniMER-1M_0003230": "...", "UniMER-1M_0003213": "c=-Im( \\lambda)/ \\alpha", "UniMER-1M_0003201": "|T| \\leqC(g_{1}+ . . .g_{m});", "UniMER-1M_0003203": "M_{33}/M_{11}=0.964", "UniMER-1M_0003243": "x", "UniMER-1M_0003218": "T_{ \\mathrm{residual}}=p_{0}+ \\frac{p_{1}}{ \\sqrt{A_{i}}}+ \\frac{p_{2}}{A_{i}}+p_{3} \\cdotA_{i}", "UniMER-1M_0003199": "\\tau^{ \\prime}=F(y^{ \\prime})", "UniMER-1M_0003222": "\\begin{array}{rlr}{{C^{ijkl}}}&{{=}}&{{S^{ijkl}+A^{ijkl}}} \\end{array}", "UniMER-1M_0003214": "\\begin{array}{rl}{{ \\pi_{*,*}^{A(2)_{*}}(g^{-1} \\mathbb{F}_{2})}}&{{= \\mathbb{F}_{2}[h_{2,1}^{ \\pm},v_{1},v_{2}^{8}],}} \\\\ {{ \\pi_{*,*}^{A(2)_{*}}(g^{-1} \\underline{{{ \\mathrm{bo}}}}_{1})}}&{{= \\mathbb{F}_{2}[h_{2,1}^{ \\pm},v_{1},v_{2}^{8}]/(v_{1}) \\{t_{1,1} \\},}} \\\\ {{ \\pi_{*,*}^{A(2)_{*}}(g^{-1} \\underline{{{ \\mathrm{bo}}}}_{1}^{ \\otimes{2}})}}&{{= \\mathbb{F}_{2}[h_{2,1}^{ \\pm},v_{1},v_{2}^{8}]/(v_{1}^{2}) \\{Q_{1}(x_{1,1}x_{1,2}) \\}.}} \\end{array}", "UniMER-1M_0003200": "\\tilde{D}_{n}( \\Deltat)", "UniMER-1M_0003223": "k"}