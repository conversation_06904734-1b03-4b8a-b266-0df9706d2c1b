# LaTeX规范化对比结果
# 生成时间: 2025-07-29 17:54:30
# 总样本数: 1000
# 格式: 原始LaTeX | 规范化LaTeX
# ==========================================

1 / k _ { L } w _ { 0 } \gg \omega _ { p } ^ { 2 } / \omega _ { L } ^ { 2 } | 1/k_{L} w_{0} \gg \omega_{p}^{2}/\omega_{L}^{2}
d = 2 0 . 3 6 2 0 0 0 + 2 9 . 5 3 0 5 8 8 8 6 1 \times N + 1 0 2 . 0 2 6 \times 1 0 ^ { - 1 2 } \times N ^ { 2 } | d=2 0.3 6 2 0 0 0+2 9.5 3 0 5 8 8 8 6 1 \times N+1 0 2.0 2 6 \times 1 0^{-1 2} \times N^{2}
\mathbb { C } | \mathbb{C}
t _ { \textrm { s } } ^ { * } = 4 | t_{\textrm{s}}^{*}=4
| { \vec { h } } | = \sqrt { h _ { x } ^ { 2 } + h _ { y } ^ { 2 } + h _ { z } ^ { 2 } } | |{\vec{h}} |=\sqrt{h_{x}^{2}+h_{y}^{2}+h_{z}^{2}}
\left\{ \begin{array} { l l } { u _ { j } ^ { h } \left( x _ { 0 _ { k } } \right) } & { = \sum _ { i } { N } _ { i j } ^ { u } \left( { x } _ { 0 _ { k } } \right) \tilde { u } _ { i } , } \\ { l ^ { \mathrm { c } ^ { h } } \left( x _ { 0 _ { k } } \right) } & { = \sum _ { i } { N } _ { i } ^ { l ^ { \mathrm { c } } } \left( { x } _ { 0 _ { k } } \right) \tilde { l } _ { i } ^ { \mathrm { c } } , } \end{array} \right. \qquad \forall x _ { 0 _ { k } } \in \Omega _ { 0 } , | \left\{\begin{array}{ll}{{u_{j}^{h} \left(x_{0_{k}} \right)}} &{{=\sum_{i}{N}_{i j}^{u} \left({x}_{0_{k}} \right) \tilde{u}_{i},}} \\{{l^{\mathrm{c}^{h}} \left(x_{0_{k}} \right)}} &{{=\sum_{i}{N}_{i}^{l^{\mathrm{c}}} \left({x}_{0_{k}} \right) \tilde{l}_{i}^{\mathrm{c}},}} \end{array} \right.\qquad \forall x_{0_{k}} \in \Omega_{0},
\left[ \nabla ^ { 2 } + \left( \frac { n ^ { 2 } \omega ^ { 2 } } { c ^ { 2 } } - \mu _ { \gamma } ^ { 2 } \right) \right] { \bf A } = \left( 1 - n ^ { 2 } \right) \nabla \left( \nabla \cdot { \bf A } \right) \, , | \left[\nabla^{2}+\left(\frac{n^{2} \omega^{2}}{c^{2}}-\mu_{\gamma}^{2} \right) \right]{\bf A}=\left(1-n^{2} \right) \nabla \left(\nabla \cdot{\bf A} \right) \,,
\sqrt { n } | \sqrt{n}
\Gamma _ { 2 , \epsilon } ^ { \mathrm { O s c } , \theta } | \Gamma_{2,\epsilon}^{\mathrm{Osc},\theta}
\sim 1 . 3 \times 1 0 ^ { 1 2 } | \sim 1.3 \times 1 0^{1 2}
\begin{array} { r l } & { \mathbb { E } _ { t } \left[ \mathcal { L } ( \omega ^ { t + 1 } ) \; \middle | \; u ^ { t + 1 } \right] - \mathcal { L } ( \omega ^ { t } ) } \\ & { = - \delta _ { \omega } ^ { t } \mathbb { R } \left( \mathbb { E } _ { t } \left[ \left\langle \nabla _ { \omega } \mathcal { L } ( \omega ^ { t } ) , \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \right\rangle \; \middle | \; u ^ { t + 1 } \right] \right) + \frac { L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } } { 2 } \mathbb { E } _ { t } \left[ \| \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \| _ { 2 } ^ { 2 } \; \middle | \; u ^ { t + 1 } \right] } \\ & { = - \delta _ { \omega } ^ { t } \mathbb { R } \left( \left\langle \nabla _ { \omega } \mathcal { L } ( \omega ^ { t } ) , \mathbb { E } _ { t } \left[ \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \; \middle | \; u ^ { t + 1 } \right] \right\rangle \right) + \frac { L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } } { 2 } \mathbb { E } _ { t } \left[ \| \tilde { \nabla } _ { \omega } \mathcal { L } ( \omega ^ { t } ) \| _ { 2 } ^ { 2 } \; \middle | \; u ^ { t + 1 } \right] } \\ & { \leq \frac { - 2 \delta _ { \omega } ^ { t } + L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } } { 2 } \| \nabla _ { \omega } \mathcal { L } ( \omega ^ { t } ) \| _ { 2 } ^ { 2 } + \frac { L _ { \omega } ( \delta _ { \omega } ^ { t } ) ^ { 2 } \sigma ^ { 2 } } { 2 } . } \end{array} | \begin{array}{rl} &{{\mathbb{E}_{t} \left[\mathcal{L}(\omega^{t+1}) \;\middle | \;u^{t+1} \right]-\mathcal{L}(\omega^{t})}} \\ &{{=-\delta_{\omega}^{t} \mathbb{R} \left(\mathbb{E}_{t} \left[\left\langle \nabla_{\omega} \mathcal{L}(\omega^{t}),\tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \right\rangle \;\middle | \;u^{t+1} \right] \right)+\frac{L_{\omega}(\delta_{\omega}^{t})^{2}}{2} \mathbb{E}_{t} \left[\| \tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \|_{2}^{2} \;\middle | \;u^{t+1} \right]}} \\ &{{=-\delta_{\omega}^{t} \mathbb{R} \left(\left\langle \nabla_{\omega} \mathcal{L}(\omega^{t}),\mathbb{E}_{t} \left[\tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \;\middle | \;u^{t+1} \right] \right\rangle \right)+\frac{L_{\omega}(\delta_{\omega}^{t})^{2}}{2} \mathbb{E}_{t} \left[\| \tilde{\nabla}_{\omega} \mathcal{L}(\omega^{t}) \|_{2}^{2} \;\middle | \;u^{t+1} \right]}} \\ &{{\leq \frac{-2 \delta_{\omega}^{t}+L_{\omega}(\delta_{\omega}^{t})^{2}}{2} \| \nabla_{\omega} \mathcal{L}(\omega^{t}) \|_{2}^{2}+\frac{L_{\omega}(\delta_{\omega}^{t})^{2} \sigma^{2}}{2}.}} \end{array}
D ( R _ { 1 } , R _ { 2 } ) = W _ { 1 } ( f _ { R _ { 1 } } , f _ { R _ { 2 } } ) . | D(R_{1},R_{2})=W_{1}(f_{R_{1}},f_{R_{2}}).
x = - R | x=-R
v _ { T } | v_{T}
k _ { 2 } | k_{2}
v | v
\begin{array} { r l } { H / \hbar = } & { { } \ \omega _ { c } c ^ { \dagger } c + \omega _ { m } m ^ { \dagger } m + \frac { \omega _ { a } } { 2 } S _ { z } + \frac { \omega _ { b } } { 2 } \left( q ^ { 2 } + p ^ { 2 } \right) } \end{array} | \begin{array}{rl}{{H/\hbar=}} &{{ \ \omega_{c} c^{\dagger} c+\omega_{m} m^{\dagger} m+\frac{\omega_{a}}{2} S_{z}+\frac{\omega_{b}}{2} \left(q^{2}+p^{2} \right)}} \end{array}
r _ { i } ^ { c r o p } = \frac { N _ { { \mathrm { s h o c k } } , i } ^ { c r o p } } { N _ { \mathrm { I d e n t i f y } } } | r_{i}^{c r o p}=\frac{N_{{\mathrm{shock}},i}^{c r o p}}{N_{\mathrm{Identify}}}
\begin{array} { r l } { p _ { 1 } } & { = x + y , p _ { 2 } = x - y , } \\ { z _ { x } } & { \le x ^ { 2 } , z _ { y } \le y ^ { 2 } , } \\ { z _ { p _ { 1 } } } & { \ge p _ { 1 } ^ { 2 } , z _ { p _ { 2 } } \ge p _ { 2 } ^ { 2 } , } \\ { z } & { \le z _ { x } + z _ { y } - z _ { p _ { 1 } } , \, z \ge z _ { p _ { 2 } } - z _ { x } - z _ { y } . } \end{array} | \begin{array}{rl}{{p_{1}}} &{{=x+y,p_{2}=x-y,}} \\{{z_{x}}} &{{\le x^{2},z_{y} \le y^{2},}} \\{{z_{p_{1}}}} &{{\ge p_{1}^{2},z_{p_{2}} \ge p_{2}^{2},}} \\{{z}} &{{\le z_{x}+z_{y}-z_{p_{1}},\,z \ge z_{p_{2}}-z_{x}-z_{y}.}} \end{array}
\mathcal { M } _ { \mathcal { K } } t _ { n - 1 } | \mathcal{M}_{\mathcal{K}} t_{n-1}
R _ { e } | R_{e}
\begin{array} { r l } { r _ { 3 } ( t _ { 0 } + \varepsilon ) = } & { \varepsilon ^ { 3 / 2 } \sum _ { i \in S _ { 1 } } \sum _ { { ( j _ { 1 } , j _ { 2 } ) \in S _ { 2 } } } [ f _ { i } , f _ { j _ { 1 } } ] ( x ^ { 0 } , t _ { 0 } ) a _ { i } ( x ^ { 0 } , t _ { 0 } ) \sqrt { \frac { | a _ { j _ { 1 } j _ { 2 } } ( x ^ { 0 } , t _ { 0 } ) | } { \pi \kappa _ { j _ { 1 } j _ { 2 } } } } } \\ & { + \frac { \varepsilon ^ { 2 } } { 2 } \sum _ { i _ { 1 } , i _ { 2 } \in S _ { 1 } } L _ { f _ { i _ { 2 } } } f _ { i _ { 1 } } ( x ^ { 0 } , t _ { 0 } ) a _ { i 1 } ( x ^ { 0 } , t _ { 0 } ) a _ { i 2 } ( x ^ { 0 } , t _ { 0 } ) . } \end{array} | \begin{array}{rl}{{r_{3}(t_{0}+\varepsilon)=}} &{{\varepsilon^{3/2} \sum_{i \in S_{1}} \sum_{{(j_{1},j_{2}) \in S_{2}}}[f_{i},f_{j_{1}}](x^{0},t_{0}) a_{i}(x^{0},t_{0}) \sqrt{\frac{| a_{j_{1} j_{2}}(x^{0},t_{0}) |}{\pi \kappa_{j_{1} j_{2}}}}}} \\ &{{+\frac{\varepsilon^{2}}{2} \sum_{i_{1},i_{2} \in S_{1}} L_{f_{i_{2}}} f_{i_{1}}(x^{0},t_{0}) a_{i 1}(x^{0},t_{0}) a_{i 2}(x^{0},t_{0}).}} \end{array}
^ { \circ } | ^{\circ}
\begin{array} { r l } { - \frac { \epsilon } { 2 } \int _ { \mathcal { D } _ { \epsilon } } \partial _ { R } \bigl ( W _ { \epsilon } ( 1 + \epsilon R ) \bigr ) \tilde { \zeta } ^ { 2 } \, \mathrm { d } X \, } & { = \, - \frac { \epsilon ^ { 2 } } { 2 } \int _ { \mathcal { D } _ { \epsilon } } W _ { \epsilon } \tilde { \zeta } ^ { 2 } \, \mathrm { d } X - \frac { \epsilon } { 2 } \int _ { \mathcal { D } _ { \epsilon } } \bigl ( \partial _ { R } W _ { \epsilon } \bigr ) \tilde { \zeta } \tilde { \eta } \, \mathrm { d } X } \\ { \, } & { \le \, - \frac { \epsilon ^ { 2 } } { 4 } \int _ { \mathcal { D } _ { \epsilon } } W _ { \epsilon } \tilde { \zeta } ^ { 2 } \, \mathrm { d } X + \frac { 1 } { 4 } \int _ { \mathcal { D } _ { \epsilon } } \frac { ( \partial _ { R } W _ { \epsilon } ) ^ { 2 } } { W _ { \epsilon } } \, \tilde { \eta } ^ { 2 } \, \mathrm { d } X \, , } \end{array} | \begin{array}{rl}{{-\frac{\epsilon}{2} \int_{\mathcal{D}_{\epsilon}} \partial_{R} \bigl(W_{\epsilon}(1+\epsilon R) \bigr) \tilde{\zeta}^{2} \,\mathrm{d} X \,}} &{{=\,-\frac{\epsilon^{2}}{2} \int_{\mathcal{D}_{\epsilon}} W_{\epsilon} \tilde{\zeta}^{2} \,\mathrm{d} X-\frac{\epsilon}{2} \int_{\mathcal{D}_{\epsilon}} \bigl(\partial_{R} W_{\epsilon} \bigr) \tilde{\zeta} \tilde{\eta} \,\mathrm{d} X}} \\{{\,}} &{{\le \,-\frac{\epsilon^{2}}{4} \int_{\mathcal{D}_{\epsilon}} W_{\epsilon} \tilde{\zeta}^{2} \,\mathrm{d} X+\frac{1}{4} \int_{\mathcal{D}_{\epsilon}} \frac{(\partial_{R} W_{\epsilon})^{2}}{W_{\epsilon}} \,\tilde{\eta}^{2} \,\mathrm{d} X \,,}} \end{array}
n _ { y } , n _ { z } \in \mathbb { N } _ { > 0 } | n_{y},n_{z} \in \mathbb{N}_{>0}
\begin{array} { r } { K ( t , f , \dot { \mathrm { H } } ^ { s , p } ( \mathbb { R } _ { + } ^ { n } ) , \dot { \mathrm { D } } _ { p } ^ { s } ( \mathring { \Delta } _ { \mathcal { J } } ) ) \leqslant \lVert a \rVert _ { \dot { \mathrm { H } } ^ { s , p } ( \mathbb { R } _ { + } ^ { n } ) } + t \lVert { \Delta } _ { \mathcal { J } } b \rVert _ { \dot { \mathrm { H } } ^ { s , p } ( \mathbb { R } _ { + } ^ { n } ) } \mathrm { . ~ } } \end{array} | \begin{array}{r}{{K(t,f,\dot{\mathrm{H}}^{s,p}(\mathbb{R}_{+}^{n}),\dot{\mathrm{D}}_{p}^{s}(\mathring{\Delta}_{\mathcal{J}})) \leq slant \lVert a \rVert_{\dot{\mathrm{H}}^{s,p}(\mathbb{R}_{+}^{n})}+t \lVert{\Delta}_{\mathcal{J}} b \rVert_{\dot{\mathrm{H}}^{s,p}(\mathbb{R}_{+}^{n})} \mathrm{.~}}} \end{array}
J _ { \phi } | J_{\phi}
\begin{array} { r l } { \bar { U } _ { n } } & { = R _ { n } \tilde { U } _ { n } = \left( \left[ \begin{array} { l l l } { 0 _ { 4 \times 4 } } & { 0 _ { 4 \times 4 } } & { \left[ \begin{array} { l l l l } { 0 } & { 0 } & { 0 } & { 1 } \\ { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 1 } & { 0 } \\ { 0 } & { 1 } & { 0 } & { 0 } \end{array} \right] } \\ { \left[ \begin{array} { l l l l } { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 1 } & { 0 } \\ { 0 } & { 1 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 1 } \end{array} \right] } & { 0 _ { 4 \times 4 } } & { 0 _ { 4 \times 4 } } \\ { 0 _ { 4 \times 4 } } & { \left[ \begin{array} { l l l l } { 0 } & { 1 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 1 } \\ { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 1 } & { 0 } \end{array} \right] } & { 0 _ { 4 \times 4 } } \end{array} \right] + \alpha _ { n } ^ { \ell } P _ { \alpha _ { n } } ( \ell ) \right) \times \left[ \begin{array} { l } { 0 } \\ { 0 } \\ { U _ { n } ^ { [ 2 , 2 ] } } \\ { 0 } \\ { U _ { n } ^ { [ 3 , 3 ] } } \\ { 0 } \\ { 0 } \\ { 0 } \\ { U _ { n } ^ { [ 2 , 1 ] } } \\ { 0 } \\ { 0 } \\ { 0 } \end{array} \right] } \\ & { = [ 0 , U _ { n } ^ { [ 2 , 1 ] } , 0 , 0 , 0 , U _ { n } ^ { [ 2 , 2 ] } , 0 , 0 , 0 , 0 , U _ { n } ^ { [ 3 , 3 ] } , 0 ] ^ { T } + P _ { \alpha _ { n } } ( 2 \ell ) } \\ & { = \left[ 0 , \sum _ { i = 1 } ^ { \ell } \frac { 1 } { \alpha _ { n } ^ { i } } \Delta _ { i } ^ { [ 2 , 1 ] } , 0 , 0 , 0 , \sum _ { i = 1 } ^ { \ell } \frac { 1 } { \alpha _ { n } ^ { i } } \Delta _ { i } ^ { [ 2 , 2 ] } , 0 , 0 , 0 , 0 , \sum _ { i = 1 } ^ { \ell } \frac { 1 } { \alpha _ { n } ^ { i } } \Delta _ { i } ^ { [ 3 , 3 ] } , 0 \right] ^ { T } + P _ { \alpha _ { n } } ( 2 \ell ) } \end{array} | \begin{array}{rl}{{\bar{U}_{n}}} &{{=R_{n} \tilde{U}_{n}=\left(\left[\begin{array}{lll}{{0_{4 \times 4}}} &{{0_{4 \times 4}}} &{{\left[\begin{array}{llll}{{0}} &{{0}} &{{0}} &{{1}} \\{{1}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{1}} &{{0}} \\{{0}} &{{1}} &{{0}} &{{0}} \end{array} \right]}} \\{{\left[\begin{array}{llll}{{1}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{1}} &{{0}} \\{{0}} &{{1}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{0}} &{{1}} \end{array} \right]}} &{{0_{4 \times 4}}} &{{0_{4 \times 4}}} \\{{0_{4 \times 4}}} &{{\left[\begin{array}{llll}{{0}} &{{1}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{0}} &{{1}} \\{{1}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{1}} &{{0}} \end{array} \right]}} &{{0_{4 \times 4}}} \end{array} \right]+\alpha_{n}^{\ell} P_{\alpha_{n}}(\ell) \right) \times \left[\begin{array}{l}{{0}} \\{{0}} \\{{U_{n}^{[2,2]}}} \\{{0}} \\{{U_{n}^{[3,3]}}} \\{{0}} \\{{0}} \\{{0}} \\{{U_{n}^{[2,1]}}} \\{{0}} \\{{0}} \\{{0}} \end{array} \right]}} \\ &{{=[0,U_{n}^{[2,1]},0,0,0,U_{n}^{[2,2]},0,0,0,0,U_{n}^{[3,3]},0]^{T}+P_{\alpha_{n}}(2 \ell)}} \\ &{{=\left[0,\sum_{i=1}^{\ell} \frac{1}{\alpha_{n}^{i}} \Delta_{i}^{[2,1]},0,0,0,\sum_{i=1}^{\ell} \frac{1}{\alpha_{n}^{i}} \Delta_{i}^{[2,2]},0,0,0,0,\sum_{i=1}^{\ell} \frac{1}{\alpha_{n}^{i}} \Delta_{i}^{[3,3]},0 \right]^{T}+P_{\alpha_{n}}(2 \ell)}} \end{array}
\omega | \omega
d = 2 | d=2
\mathbb { E } _ { 0 } = \frac { 1 } { 2 } \| u _ { 0 } \| _ { L ^ { 2 } } ^ { 2 } | \mathbb{E}_{0}=\frac{1}{2} \| u_{0} \|_{L^{2}}^{2}
y = 0 | y=0
\mathrm { I n d } | \mathrm{Ind}
e _ { \mathrm { x } } = - { \frac { ( v _ { \mathrm { x f } } - r \omega _ { \mathrm { f } } ) - ( u _ { \mathrm { x f } } - R \Omega _ { \mathrm { f } } ) } { ( v _ { \mathrm { x i } } - r \omega _ { \mathrm { i } } ) - ( u _ { \mathrm { x i } } - R \Omega _ { \mathrm { i } } ) } } , | e_{\mathrm{x}}=-{\frac{(v_{\mathrm{xf}}-r \omega_{\mathrm{f}})-(u_{\mathrm{xf}}-R \Omega_{\mathrm{f}})}{(v_{\mathrm{xi}}-r \omega_{\mathrm{i}})-(u_{\mathrm{xi}}-R \Omega_{\mathrm{i}})}},
\begin{array} { r l } & { \frac { \omega _ { p j } ^ { 2 } p _ { \perp } ^ { 2 } \overleftrightarrow { \mathbf { S } } f _ { j } } { \left[ p _ { \parallel } - \frac { m _ { j } ( \omega - n \Omega _ { j } ) } { k _ { \parallel } } \right] \omega ^ { 2 } } } \\ & { = \frac { \omega _ { p j } ^ { 2 } p _ { \perp } ^ { 2 } \overleftrightarrow { \mathbf { S } } f _ { j } \left[ p _ { \parallel } + \frac { m _ { j } ( \omega + n \Omega _ { j } ) } { k _ { \parallel } } \right] } { \left[ p _ { \parallel } - \frac { m _ { j } ( \omega - n \Omega _ { j } ) } { k _ { \parallel } } \right] \left[ p _ { \parallel } + \frac { m _ { j } ( \omega + n \Omega _ { j } ) } { k _ { \parallel } } \right] \omega ^ { 2 } } . } \end{array} | \begin{array}{rl} &{{\frac{\omega_{p j}^{2} p_{\perp}^{2} \overleftrightarrow{\mathbf{S}} f_{j}}{\left[p_{\parallel}-\frac{m_{j}(\omega-n \Omega_{j})}{k_{\parallel}} \right] \omega^{2}}}} \\ &{{=\frac{\omega_{p j}^{2} p_{\perp}^{2} \overleftrightarrow{\mathbf{S}} f_{j} \left[p_{\parallel}+\frac{m_{j}(\omega+n \Omega_{j})}{k_{\parallel}} \right]}{\left[p_{\parallel}-\frac{m_{j}(\omega-n \Omega_{j})}{k_{\parallel}} \right] \left[p_{\parallel}+\frac{m_{j}(\omega+n \Omega_{j})}{k_{\parallel}} \right] \omega^{2}}.}} \end{array}
\mathsf { A } = I _ { 1 } \dot { \theta } _ { 1 } + I _ { 2 } \dot { \theta } _ { 2 } \, , | \mathsf{A}=I_{1} \dot{\theta}_{1}+I_{2} \dot{\theta}_{2} \,,
S | S
1 . 6 \times 1 0 ^ { - 9 } | 1.6 \times 1 0^{-9}
i | i
x _ { \star } | x_{\star}
1 2 | 1 2
3 a ^ { 2 } b ^ { 3 } + 5 a ^ { 3 } b ^ { 2 } - \frac { a ^ { 5 } b ^ { 8 } } { 2 } | 3 a^{2} b^{3}+5 a^{3} b^{2}-\frac{a^{5} b^{8}}{2}
{ \cal Z } ( s ) = { \cal N } \int D B D B ^ { \dagger } e ^ { - \left( S ( B , B ^ { \dagger } ) \; + \; \int d ^ { 3 } x s _ { \mu } J ^ { \mu } \right) \; } . | {\cal Z}(s)={\cal N} \int D B D B^{\dagger} e^{-\left(S(B,B^{\dagger}) \;+\;\int d^{3} x s_{\mu} J^{\mu} \right) \;}.
\delta \psi _ { A \vert \mu } = \mathrm { d e r i v a t i v e ~ t e r m s } \, + \, S _ { A B } \left( \phi \right) \, \gamma _ { \mu } \, \epsilon ^ { B } ~ , | \delta \psi_{A \vert \mu}=\mathrm{derivative~terms} \,+\,S_{A B} \left(\phi \right) \,\gamma_{\mu} \,\epsilon^{B} ~,
\Sigma _ { e } \geq \Lambda _ { e } ^ { - } \qquad \mathrm { a n d } \qquad 1 - \Sigma _ { \mu } \geq \Lambda _ { \mu } ^ { - } \, , | \Sigma_{e} \geq \Lambda_{e}^{-} \qquad \mathrm{and} \qquad 1-\Sigma_{\mu} \geq \Lambda_{\mu}^{-} \,,
\begin{array} { r l } { \mathbf { f } ( \lambda ) } & { = \left( \begin{array} { l } { f _ { 1 } ( \lambda ) } \\ { f _ { 2 } ( \lambda ) } \end{array} \right) = \left( \begin{array} { l } { D _ { \nu } \left( \sqrt { 2 } ( \lambda + x ) \right) } \\ { - D _ { \nu - 1 } \left( \sqrt { 2 } ( \lambda + x ) \right) } \end{array} \right) , } \\ { \mathbf { h } ( \lambda ) } & { = \left( \begin{array} { l } { h _ { 1 } ( \lambda ) } \\ { h _ { 2 } ( \lambda ) } \end{array} \right) = \gamma \left( \begin{array} { l } { D _ { \nu - 1 } \left( \sqrt { 2 } ( \lambda + x ) \right) } \\ { D _ { \nu } \left( \sqrt { 2 } ( \lambda + x ) \right) } \end{array} \right) , } \end{array} | \begin{array}{rl}{{\mathbf{f}(\lambda)}} &{{=\left(\begin{array}{l}{{f_{1}(\lambda)}} \\{{f_{2}(\lambda)}} \end{array} \right)=\left(\begin{array}{l}{{D_{\nu} \left(\sqrt{2}(\lambda+x) \right)}} \\{{-D_{\nu-1} \left(\sqrt{2}(\lambda+x) \right)}} \end{array} \right),}} \\{{\mathbf{h}(\lambda)}} &{{=\left(\begin{array}{l}{{h_{1}(\lambda)}} \\{{h_{2}(\lambda)}} \end{array} \right)=\gamma \left(\begin{array}{l}{{D_{\nu-1} \left(\sqrt{2}(\lambda+x) \right)}} \\{{D_{\nu} \left(\sqrt{2}(\lambda+x) \right)}} \end{array} \right),}} \end{array}
^ * | ^*
\begin{array} { r l r } { \hat { \mathcal { M } } _ { 3 } ( \delta \phi ) } & { { } \equiv } & { \hat { \mathcal { M } } ( { \bf \hat { n } _ { 3 } } , \delta \phi ) } \end{array} | \begin{array}{rlr}{{\hat{\mathcal{M}}_{3}(\delta \phi)}} &{{ \equiv}} &{{\hat{\mathcal{M}}({\bf \hat{n}_{3}},\delta \phi)}} \end{array}
2 . 1 K | 2.1 K
x _ { 3 } ( t ) | x_{3}(t)
\begin{array} { r } { \begin{array} { r } { d { \mathbf { A } } ^ { * } = \mathcal { F } ( { \mathbf { A } } ^ { * } ) d t ^ { * } + d { \mathbf { F } } ^ { * } . } \end{array} } \end{array} | \begin{array}{r}{{\begin{array}{r}{{d{\mathbf{A}}^{*}=\mathcal{F}({\mathbf{A}}^{*}) d t^{*}+d{\mathbf{F}}^{*}.}} \end{array}}} \end{array}
\mathrm { C a } = { \frac { \mu V } { \gamma } } | \mathrm{Ca}={\frac{\mu V}{\gamma}}
( m \delta _ { I J } - \Theta _ { \alpha \beta } \mathcal { A } _ { I } ^ { \alpha } \mathcal { A } _ { J } ^ { \beta } ) \ddot { r } ^ { J } - \frac { 1 } { R } \Theta _ { 3 \beta } \mathcal { A } _ { K } ^ { \beta } \dot { r } ^ { K } \mathcal { A } _ { J } ^ { \bar { I } } \dot { r } ^ { J } = 0 , \ \ I = 4 , 5 | (m \delta_{I J}-\Theta_{\alpha \beta} \mathcal{A}_{I}^{\alpha} \mathcal{A}_{J}^{\beta}) \ddot{r}^{J}-\frac{1}{R} \Theta_{3 \beta} \mathcal{A}_{K}^{\beta} \dot{r}^{K} \mathcal{A}_{J}^{\bar{I}} \dot{r}^{J}=0,\ \ I=4,5
\sum _ { l = 0 } ^ { \infty } \beta _ { l } x ^ { l } = \zeta ( x ) \exp { ( - x ^ { 2 } / 4 ) } , | \sum_{l=0}^{\infty} \beta_{l} x^{l}=\zeta(x) \mathrm{exp}{(-x^{2}/4)},
\alpha _ { f } | \alpha_{f}
f | f
G | G
\varepsilon _ { 2 } = 1 6 | \varepsilon_{2}=1 6
\frac { \delta } { \delta \phi _ { 1 } ( \textbf { x } ) } \mathcal { S } _ { g y } ^ { p } \circ \hat { \chi } ( \textbf { x } ) = \int \frac { \delta } { \delta \phi _ { 1 } ( \textbf { x } ) } d \Omega _ { g y } d t \mathcal { H } F _ { e } . | \frac{\delta}{\delta \phi_{1}(\textbf{x})} \mathcal{S}_{g y}^{p} \circ \hat{\chi}(\textbf{x})=\int \frac{\delta}{\delta \phi_{1}(\textbf{x})} d \Omega_{g y} d t \mathcal{H} F_{e}.
{ \frac { P _ { 1 } } { T _ { 1 } } } = { \frac { P _ { 2 } } { T _ { 2 } } } \qquad { \mathrm { o r } } \qquad P _ { 1 } T _ { 2 } = P _ { 2 } T _ { 1 } . | {\frac{P_{1}}{T_{1}}}={\frac{P_{2}}{T_{2}}} \qquad{\mathrm{or}} \qquad P_{1} T_{2}=P_{2} T_{1}.
\Phi _ { n } ( i x ) = i ^ { n } \Psi _ { n } ( x ) | \Phi_{n}(i x)=i^{n} \Psi_{n}(x)
D | D
q < 1 | q<1
\Psi _ { \{ N _ { \mathrm { ~ \bf ~ k ~ } \alpha } \} } ( \{ Q _ { \mathrm { ~ \bf ~ k ~ } \alpha } \} ) = \prod _ { \mathrm { ~ \bf ~ k ~ } \alpha } \psi _ { N _ { \mathrm { ~ \bf ~ k ~ } \alpha } } ( Q _ { \mathrm { ~ \bf ~ k ~ } \alpha } ) \; \; \; , \; \; \; N _ { \mathrm { ~ \bf ~ k ~ } \alpha } = 0 , 1 , 2 , 3 , . . . | \Psi_{\{N_{\mathrm{~\bf~k~} \alpha} \}}(\{Q_{\mathrm{~\bf~k~} \alpha} \})=\prod_{\mathrm{~\bf~k~} \alpha} \psi_{N_{\mathrm{~\bf~k~} \alpha}}(Q_{\mathrm{~\bf~k~} \alpha}) \;\;\;,\;\;\;N_{\mathrm{~\bf~k~} \alpha}=0,1,2,3,...
T ^ { \mu \nu } | T^{\mu \nu}
c \neq 0 | c \neq 0
7 \% | 7 \
{ \bf A } _ { 2 } ( { \bf r } ) = { \bf E } ^ { ( 2 ) } ( { \bf r } ) / | { \bf E } ^ { ( 2 ) } ( { \bf r } ) | | {\bf A}_{2}({\bf r})={\bf E}^{(2)}({\bf r})/|{\bf E}^{(2)}({\bf r}) |
T _ { X \rightarrow Y } = \sum { p ( y _ { i + 1 } , y ^ { i } , x _ { i } ) \log \frac { p ( y _ { i + 1 } | y ^ { i } , x _ { i } ) } { p ( y _ { i + 1 } | y ^ { i } ) } } , | T_{X \rightarrow Y}=\sum{p(y_{i+1},y^{i},x_{i}) \mathrm{log} \frac{p(y_{i+1} | y^{i},x_{i})}{p(y_{i+1} | y^{i})}},
1 5 \% | 1 5 \
\mu | \mu
Q _ { m - 1 / 2 } \left( z \right) | Q_{m-1/2} \left(z \right)
I _ { j } ^ { 2 } = I _ { \infty } ^ { 2 } + \operatorname* { m a x } _ { i } \left( \frac { A _ { w } } { A } I _ { i } ^ { + } \right) ^ { 2 } , | I_{j}^{2}=I_{\infty}^{2}+\mathrm{max}_{i} \left(\frac{A_{w}}{A} I_{i}^{+} \right)^{2},
\xi ^ { 1 0 * } \left( x \right) = \pi _ { \psi } \left( x \right) | \xi^{1 0*} \left(x \right)=\pi_{\psi} \left(x \right)
\begin{array} { r } { \vec { E } ( x , y ) = E _ { 0 } ( \hat { x } \cos { \theta } + \hat { y } \sin { \theta } ) , } \end{array} | \begin{array}{r}{{\vec{E}(x,y)=E_{0}(\hat{x} \mathrm{cos}{\theta}+\hat{y} \mathrm{sin}{\theta}),}} \end{array}
\tau _ { 1 } | \tau_{1}
\bar { R } = \sum _ { k = 1 } ^ { M } p _ { k } \operatorname* { m a x } \{ 0 , R _ { k } \} , | \bar{R}=\sum_{k=1}^{M} p_{k} \mathrm{max} \{0,R_{k} \},
l | l
\infty , | \infty,
g _ { \mathrm { t a r g e t } } ^ { \mathrm { ( m i n ) } } = \lambda _ { \mathrm { b } } v ^ { + } + d | g_{\mathrm{target}}^{\mathrm{(min)}}=\lambda_{\mathrm{b}} v^{+}+d
| u | = 2 | | u |=2
\boldsymbol { \mathrm J } | \boldsymbol{\mathrm{J}
I = ( 0 , \; 1 ) . | I=(0,\;1).
\times _ { \mathrm { ~ n ~ e ~ w ~ } } | \times_{\mathrm{~n~e~w~}}
L _ { i } | L_{i}
< r _ { V } ^ { 2 } > = \frac { 6 } { \pi } \int _ { 4 m _ { \pi } ^ { 2 } } ^ { \infty } \frac { I m V ( z ) d z } { z ^ { 2 } } | <r_{V}^{2}>=\frac{6}{\pi} \int_{4 m_{\pi}^{2}}^{\infty} \frac{I m V(z) d z}{z^{2}}
m \le 3 | m \le 3
\left( T _ { 1 } , T _ { 2 } , \cdots T _ { n } \right) | \left(T_{1},T_{2},. . . T_{n} \right)
q _ { x } ( 0 ) = q _ { y } ( 0 ) = 0 . 6 5 | q_{x}(0)=q_{y}(0)=0.6 5
K _ { \mu } ( k ) = \frac { \exp [ i a k _ { \mu } - 1 ] } { i a } | K_{\mu}(k)=\frac{\mathrm{exp}[i a k_{\mu}-1]}{i a}
t / t _ { \mathrm { t a n k } } | t/t_{\mathrm{tank}}
\begin{array} { r l } { d V } & { { } = ( \partial _ { \sigma } x \cdot ( \partial _ { s _ { 1 } } x \times \partial _ { s _ { 2 } } x ) ) \, d \sigma \, d s _ { 1 } \, d s _ { 2 } , } \end{array} | \begin{array}{rl}{{d V}} &{{=(\partial_{\sigma} x \cdot(\partial_{s_{1}} x \times \partial_{s_{2}} x)) \,d \sigma \,d s_{1} \,d s_{2},}} \end{array}
I { = } 6 { \times } 1 0 ^ { 1 4 } \; \mathrm { W } \ \mathrm { c m } ^ { - 2 } | I{=} 6{\times} 1 0^{1 4} \;\mathrm{W} \ \mathrm{cm}^{-2}
\times \, \frac { \omega } { 2 i \pi \hbar \sin \omega \Delta t } \, e ^ { - i \frac { \pi } { 2 } | \ell | } \, e ^ { \frac { i \omega } { 2 \hbar } \frac { \cos \omega \Delta t } { \sin \omega \Delta t } ( r _ { f } ^ { 2 } + r _ { i } ^ { 2 } ) } \, J _ { | \ell | } \left( \frac { \omega r _ { f } \, r _ { i } } { \hbar \sin \omega \Delta t } \right) \ \ \ . | \times \,\frac{\omega}{2 i \pi \hbar \mathrm{sin} \omega \Delta t} \,e^{-i \frac{\pi}{2} | \ell |} \,e^{\frac{i \omega}{2 \hbar} \frac{\mathrm{cos} \omega \Delta t}{\mathrm{sin} \omega \Delta t}(r_{f}^{2}+r_{i}^{2})} \,J_{| \ell |} \left(\frac{\omega r_{f} \,r_{i}}{\hbar \mathrm{sin} \omega \Delta t} \right) \ \ \.
T _ { \lambda \mu \nu } ^ { A V V } = 2 \varepsilon _ { \lambda \mu \nu \xi } ( k _ { 1 } + k _ { 2 } ) _ { \sigma } ( \triangle _ { \xi \sigma } ) + N A T | T_{\lambda \mu \nu}^{A V V}=2 \varepsilon_{\lambda \mu \nu \xi}(k_{1}+k_{2})_{\sigma}(\triangle_{\xi \sigma})+N A T
\left( \bigcup _ { i \in I } A _ { i } \right) ^ { 0 } = \bigcap _ { i \in I } A _ { i } ^ { 0 } . | \left(\bigcup_{i \in I} A_{i} \right)^{0}=\bigcap_{i \in I} A_{i}^{0}.
- R = 2 \alpha ^ { 2 } \left( - N ( \psi ^ { \prime } ) ^ { 2 } - { \frac { 4 } { x ^ { 2 } } } F ^ { - 1 } E _ { V } - { \frac { 2 } { x ^ { 2 } } } E _ { H } \right) \ . | -R=2 \alpha^{2} \left(-N(\psi^{\prime})^{2}-{\frac{4}{x^{2}}} F^{-1} E_{V}-{\frac{2}{x^{2}}} E_{H} \right) \.
p | p
k = g = 0 . 8 7 \pm 0 . 0 2 | k=g=0.8 7 \pm 0.0 2
O ( q _ { c } ^ { 2 } ) \sim O ( N ^ { - 2 / 3 } ) | O(q_{c}^{2}) \sim O(N^{-2/3})
P _ { e e } ^ { \mathrm { J S } } \simeq 1 - \sin ^ { 2 } 2 \omega \sin ^ { 2 } ( \pi L / L _ { \mathrm { o s c } } ) \ . | P_{e e}^{\mathrm{JS}} \sim eq 1-\mathrm{sin}^{2} 2 \omega \mathrm{sin}^{2}(\pi L/L_{\mathrm{osc}}) \.
K n | K n
\begin{array} { r l r } { o _ { r } ( x , y ) } & { = } & { \textrm { R e L U } ( W _ { 3 , r } \star \textrm { R e L U } ( W _ { 2 , r } \star \textrm { R e L U } ( W _ { 1 , r } \star v _ { r } ( x , y ) ) ) ) } \\ { o _ { a } ( x , y ) } & { = } & { \textrm { R e L U } ( W _ { 3 , a } \star \textrm { R e L U } ( W _ { 2 , a } \star \textrm { R e L U } ( W _ { 1 , a } \star \{ s ( x , y ) , c ( x , y ) \} ) ) ) } \\ { \pi ( x , y ) } & { = } & { \sigma ( W _ { 3 } \star \textrm { R e L U } ( W _ { 2 } \star \textrm { R e L U } ( W _ { 1 } \star \{ o _ { r } ( x , y ) , o _ { a } ( x , y ) \} ) ) ) } \end{array} | \begin{array}{rlr}{{o_{r}(x,y)}} &{{=}} &{{\textrm{R e L U}(W_{3,r} \star \textrm{R e L U}(W_{2,r} \star \textrm{R e L U}(W_{1,r} \star v_{r}(x,y))))}} \\{{o_{a}(x,y)}} &{{=}} &{{\textrm{R e L U}(W_{3,a} \star \textrm{R e L U}(W_{2,a} \star \textrm{R e L U}(W_{1,a} \star \{s(x,y),c(x,y) \})))}} \\{{\pi(x,y)}} &{{=}} &{{\sigma(W_{3} \star \textrm{R e L U}(W_{2} \star \textrm{R e L U}(W_{1} \star \{o_{r}(x,y),o_{a}(x,y) \})))}} \end{array}
h ( x , t ) = Z ( t ) + \theta ( t ) x , | h(x,t)=Z(t)+\theta(t) x,
i = 0 , 1 , \cdots , 2 9 | i=0,1,. . .,2 9
- { A ^ { \alpha ; \beta } } _ { ; \beta } + { R ^ { \alpha } } _ { \beta } A ^ { \beta } = 0 | -{A^{\alpha;\beta}}_{;\beta}+{R^{\alpha}}_{\beta} A^{\beta}=0
\begin{array} { r l } { \mathbb { N } } & { = \hat { p } _ { 0 , \psi } \mathbb { N } ^ { p _ { 0 } } + \hat { T } _ { 0 , \psi } \mathbb { N } ^ { T _ { 0 } } , } \\ { \mathbb { T } } & { = \hat { p } _ { 0 , \psi } \mathbb { T } ^ { p _ { 0 } } + \hat { T } _ { 0 , \psi } \mathbb { T } ^ { T _ { 0 } } , } \\ { \mathbb { U } } & { = \hat { p } _ { 0 , \psi } \mathbb { U } ^ { p _ { 0 } } + \hat { T } _ { 0 , \psi } \mathbb { U } ^ { T _ { 0 } } , } \end{array} | \begin{array}{rl}{{\mathbb{N}}} &{{=\hat{p}_{0,\psi} \mathbb{N}^{p_{0}}+\hat{T}_{0,\psi} \mathbb{N}^{T_{0}},}} \\{{\mathbb{T}}} &{{=\hat{p}_{0,\psi} \mathbb{T}^{p_{0}}+\hat{T}_{0,\psi} \mathbb{T}^{T_{0}},}} \\{{\mathbb{U}}} &{{=\hat{p}_{0,\psi} \mathbb{U}^{p_{0}}+\hat{T}_{0,\psi} \mathbb{U}^{T_{0}},}} \end{array}
1 . 0 5 | 1.0 5
1 0 4 . 6 | 1 0 4.6
- E _ { i } ( - z ) = \int _ { z } ^ { \infty } \! \! d t \, { \mathrm e } ^ { - t } t ^ { - 1 } = \int _ { 1 } ^ { \infty } \! \! d t { \mathrm e } ^ { - t z } t ^ { - 1 } \ , | -E_{i}(-z)=\int_{z}^{\infty} \! \! d t \,{\mathrm{e}^{-t} t^{-1}=\int_{1}^{\infty} \! \! d t{\mathrm{e}^{-t z} t^{-1} \,
r _ { \delta } ( x ) = x / ( x + \delta ) \quad , | r_{\delta}(x)=x/(x+\delta) \quad,
\partial _ { m } J _ { i j } = - \frac { 2 } { x ^ { 2 } } ( x _ { m } J _ { i j } - x _ { m } \delta _ { i j } + x _ { i } \delta _ { j m } + x _ { j } \delta _ { i m } ) , | \partial_{m} J_{i j}=-\frac{2}{x^{2}}(x_{m} J_{i j}-x_{m} \delta_{i j}+x_{i} \delta_{j m}+x_{j} \delta_{i m}),
\looparrowright | \looparrowright
\lambda = \frac { 2 \pi } { k } e ^ { P \tau + h / 2 } = \frac { \pi P } { k } e ^ { - 2 P \tau } l _ { z } . | \lambda=\frac{2 \pi}{k} e^{P \tau+h/2}=\frac{\pi P}{k} e^{-2 P \tau} l_{z}.
\begin{array} { r } { \beta _ { 3 i + 2 } = \left\{ \begin{array} { l l } { ( s ( \alpha _ { 3 i + 1 } ) \oplus \beta _ { 3 i } ) \cdot a _ { 3 i + 2 } } & { \mathrm { i f ~ } | \alpha _ { 3 i + 1 } | \geq } \\ & { | \alpha _ { 3 i + 2 } | } \\ { ( s ( \alpha _ { 3 i + 2 } ) \oplus \beta _ { 3 i } \oplus \beta _ { 3 i + 1 } ) \cdot a _ { 3 i + 2 } } & { \mathrm { o t h e r w i s e , } } \end{array} \right. } \end{array} | \begin{array}{r}{{\beta_{3 i+2}=\left\{\begin{array}{ll}{{(s(\alpha_{3 i+1}) \oplus \beta_{3 i}) \cdot a_{3 i+2}}} &{{\mathrm{if~} | \alpha_{3 i+1} | \geq}} \\ &{{| \alpha_{3 i+2} |}} \\{{(s(\alpha_{3 i+2}) \oplus \beta_{3 i} \oplus \beta_{3 i+1}) \cdot a_{3 i+2}}} &{{\mathrm{otherwise,}}} \end{array} \right.}} \end{array}
\begin{array} { r l } { \langle f , \mathbf { p } _ { \chi } ^ { \prime } | \Delta H _ { \chi T } | i , \mathbf { p } _ { \chi } \rangle } & { \equiv \! \int \! \frac { d ^ { 3 } \mathbf { q } } { ( 2 \pi ) ^ { 3 } } \, \langle \mathbf { p } _ { \chi } ^ { \prime } | \mathcal { O } _ { \chi } ( \mathbf { q } ) | \mathbf { p } _ { \chi } \rangle \times \langle f | \mathcal { O } _ { T } ( \mathbf { q } ) | i \rangle } \\ & { = \frac { 1 } { V } \sqrt { \frac { \pi \bar { \sigma } ( q ) } { \mu _ { \chi } ^ { 2 } } } \langle f | \mathcal { O } _ { T } ( \mathbf { q } ) | i \rangle , } \end{array} | \begin{array}{rl}{{\langle f,\mathbf{p}_{\chi}^{\prime} | \Delta H_{\chi T} | i,\mathbf{p}_{\chi} \rangle}} &{{\equiv \! \int \! \frac{d^{3} \mathbf{q}}{(2 \pi)^{3}} \,\langle \mathbf{p}_{\chi}^{\prime} | \mathcal{O}_{\chi}(\mathbf{q}) | \mathbf{p}_{\chi} \rangle \times \langle f | \mathcal{O}_{T}(\mathbf{q}) | i \rangle}} \\ &{{=\frac{1}{V} \sqrt{\frac{\pi \bar{\sigma}(q)}{\mu_{\chi}^{2}}} \langle f | \mathcal{O}_{T}(\mathbf{q}) | i \rangle,}} \end{array}
M | M
V | V
I _ { j , m } \left( \varphi \right) = - T r \ln \left( - i \partial _ { \mu } \gamma _ { \mu } + r m r + r \varphi _ { a } \Gamma _ { a } r \right) - \frac 1 2 \varphi \left( V - j \right) ^ { - 1 } \varphi | I_{j,m} \left(\varphi \right)=-T r \mathrm{ln} \left(-i \partial_{\mu} \gamma_{\mu}+r m r+r \varphi_{a} \Gamma_{a} r \right)-\frac{1}{2} \varphi \left(V-j \right)^{-1} \varphi
G = \frac { 1 } { 2 } \left[ \frac { \mathbb { C } _ { 1 1 } - \mathbb { C } _ { 1 2 } + 3 \mathbb { C } _ { 4 4 } } { 5 } + \frac { 5 \mathbb { C } _ { 4 4 } \left( \mathbb { C } _ { 1 1 } - \mathbb { C } _ { 1 2 } \right) } { 4 \mathbb { C } _ { 4 4 } + 3 \left( \mathbb { C } _ { 1 1 } - \mathbb { C } _ { 1 2 } \right) } \right] | G=\frac{1}{2} \left[\frac{\mathbb{C}_{1 1}-\mathbb{C}_{1 2}+3 \mathbb{C}_{4 4}}{5}+\frac{5 \mathbb{C}_{4 4} \left(\mathbb{C}_{1 1}-\mathbb{C}_{1 2} \right)}{4 \mathbb{C}_{4 4}+3 \left(\mathbb{C}_{1 1}-\mathbb{C}_{1 2} \right)} \right]
^ 6 | ^6
U _ { \lambda } = { \bf C } \cdot { \bf 1 } \oplus [ U _ { \lambda } , U _ { \lambda } ] , | U_{\lambda}={\bf C} \cdot{\bf 1} \oplus[U_{\lambda},U_{\lambda}],
R = 0 | R=0
N _ { + } | N_{+}
\begin{array} { r l } { \left\| \mathrm { t r _ { 0 } } { \mathcal { V } } \right\| _ { L ^ { 2 } ( \Omega ) } ^ { 2 } } & { \lesssim \int _ { 0 } ^ { y _ { 0 } } y ^ { \alpha } \| \nabla \mathcal { V } ( y ) \| _ { L ^ { 2 } ( \Omega ) } ^ { 2 } d y } \\ & { \qquad + \int _ { \Omega } \int _ { 0 } ^ { y _ { 0 } } y ^ { 2 + 3 \alpha } | \partial _ { y } \chi | ^ { 2 } \mathcal { U } ^ { 2 } d y d x + \int _ { \Omega } \int _ { 0 } ^ { y _ { 0 } } y ^ { \alpha } \Big | \int _ { y } ^ { \mathcal { Y } } { \tau ^ { \alpha } \mathcal { U } ( x , \tau ) \, d \tau } \Big | ^ { 2 } d y d x . } \end{array} | \begin{array}{rl}{{\left\| \mathrm{tr_{0}}{\mathcal{V}} \right\|_{L^{2}(\Omega)}^{2}}} &{{\lesssim \int_{0}^{y_{0}} y^{\alpha} \| \nabla \mathcal{V}(y) \|_{L^{2}(\Omega)}^{2} d y}} \\ &{{\qquad+\int_{\Omega} \int_{0}^{y_{0}} y^{2+3 \alpha} | \partial_{y} \chi |^{2} \mathcal{U}^{2} d y d x+\int_{\Omega} \int_{0}^{y_{0}} y^{\alpha} \Big| \int_{y}^{\mathcal{Y}}{\tau^{\alpha} \mathcal{U}(x,\tau) \,d \tau} \Big|^{2} d y d x.}} \end{array}
4 0 9 6 | 4 0 9 6
K _ { \perp \theta } = 0 . 0 2 K _ { \parallel } f _ { \perp \theta } . | K_{\perp \theta}=0.0 2 K_{\parallel} f_{\perp \theta}.
\sigma _ { k } ( - x , x , x _ { 1 } , \ldots , x _ { n } ) = \sigma _ { k } ( x _ { 1 } , \ldots , x _ { n } ) - x ^ { 2 } \sigma _ { k - 2 } ( x _ { 1 } , \ldots , x _ { n } ) \, \, , | \sigma_{k}(-x,x,x_{1},. . .,x_{n})=\sigma_{k}(x_{1},. . .,x_{n})-x^{2} \sigma_{k-2}(x_{1},. . .,x_{n}) \,\,,
\Pi _ { i j } ^ { \mu \nu } = - i g ^ { \mu \nu } ( A _ { i j } + q ^ { 2 } F _ { i j } ( q ^ { 2 } ) ) + q ^ { \mu } q ^ { \nu } \ \mathrm { t e r m s } , | \Pi_{i j}^{\mu \nu}=-i g^{\mu \nu}(A_{i j}+q^{2} F_{i j}(q^{2}))+q^{\mu} q^{\nu} \ \mathrm{terms},
B = 4 | B=4
[ \sqrt { f } ] | [\sqrt{f}]
\tilde { \nu } _ { t _ { 3 } t _ { 4 } } ^ { t _ { 1 } t _ { 2 } } = \tilde { v } _ { t _ { 3 } t _ { 4 } } ^ { t _ { 1 } t _ { 2 } } | \tilde{\nu}_{t_{3} t_{4}}^{t_{1} t_{2}}=\tilde{v}_{t_{3} t_{4}}^{t_{1} t_{2}}
w _ { j k } ( \theta _ { j } ) = ( 1 + \cos \theta _ { j } ) s _ { j k } + \mathcal O ( s _ { j k } ^ { 2 } ) | w_{j k}(\theta_{j})=(1+\mathrm{cos} \theta_{j}) s_{j k}+\mathcal{O}(s_{j k}^{2})
k = 2 / L | k=2/L
- 3 / 2 | -3/2
N | N
\sqrt { 2 } | \sqrt{2}
r = R _ { \mathrm { ~ m ~ a ~ x ~ } } | r=R_{\mathrm{~m~a~x~}}
( { \pmb S } _ { \alpha } ( { \pmb x } , { \pmb \xi } ) , \mu { \pmb \Sigma } _ { \alpha } ( { \pmb x } , { \pmb \xi } ) ) | ({\pm b{S}_{\alpha}({\pm b{x},{\pm b{\xi}),\mu{\pm b{\Sigma}_{\alpha}({\pm b{x},{\pm b{\xi}))
\left. \begin{array} { l } { \displaystyle ( \hat { x } , \hat { z } , \hat { \xi } , \hat { \zeta } ) = \hat { R } _ { 0 } ( x , \epsilon z , \epsilon \xi , \epsilon \zeta ) , \quad \hat { t } = \frac { \hat { R } _ { 0 } } { { \hat { U } } } t ; } \\ { \displaystyle \hat { \sigma } = \hat { \sigma } _ { 0 } \sigma , \quad ( \hat { p } _ { l } , \hat { p } _ { s } , \hat { \Pi } ) = \frac { \hat { \eta } _ { l } { \hat { U } } } { \epsilon ^ { 2 } \hat { R } _ { 0 } } ( p _ { l } , p _ { s } , \Pi ) ; } \\ { \displaystyle ( \hat { u } _ { x } , \hat { u } _ { z } ) = \hat { R } _ { 0 } ( u _ { x } , \epsilon u _ { z } ) , \quad ( \hat { v } _ { x } , \hat { v } _ { z } ) = \hat { U } ( v _ { x } , \epsilon v _ { z } ) ; } \\ { \displaystyle \hat { T } = \hat { T } _ { r e f } + T \Delta \hat { T } , \quad \hat { J } = \frac { \hat { \lambda } \Delta \hat { T } } { \hat { L } _ { v } \hat { h } _ { 0 } } J , \quad \hat { \rho ^ { v } } = \hat { \rho } _ { r e f } ^ { v } \rho ^ { v } . } \end{array} \right\} | \left.\begin{array}{l}{{\displaystyle(\hat{x},\hat{z},\hat{\xi},\hat{\zeta})=\hat{R}_{0}(x,\epsilon z,\epsilon \xi,\epsilon \zeta),\quad \hat{t}=\frac{\hat{R}_{0}}{{\hat{U}}} t;}} \\{{\displaystyle \hat{\sigma}=\hat{\sigma}_{0} \sigma,\quad(\hat{p}_{l},\hat{p}_{s},\hat{\Pi})=\frac{\hat{\eta}_{l}{\hat{U}}}{\epsilon^{2} \hat{R}_{0}}(p_{l},p_{s},\Pi);}} \\{{\displaystyle(\hat{u}_{x},\hat{u}_{z})=\hat{R}_{0}(u_{x},\epsilon u_{z}),\quad(\hat{v}_{x},\hat{v}_{z})=\hat{U}(v_{x},\epsilon v_{z});}} \\{{\displaystyle \hat{T}=\hat{T}_{r e f}+T \Delta \hat{T},\quad \hat{J}=\frac{\hat{\lambda} \Delta \hat{T}}{\hat{L}_{v} \hat{h}_{0}} J,\quad \hat{\rho^{v}}=\hat{\rho}_{r e f}^{v} \rho^{v}.}} \end{array} \right\}
\phi _ { 0 } \simeq M _ { 3 } \: \mathrm { e x p } \left[ { \frac { \pi ^ { 2 } m ^ { 2 } } { 2 g _ { 3 } ^ { 2 } M _ { 3 } ^ { 2 } } } \right] . | \phi_{0} \sim eq M_{3} \:\mathrm{exp} \left[{\frac{\pi^{2} m^{2}}{2 g_{3}^{2} M_{3}^{2}}} \right].
\mathrm { ~ W ~ } _ { 3 } = \frac { 1 } { \sqrt { 3 } } \Big ( \vert 0 0 1 \rangle + \vert 0 1 0 \rangle + \vert 1 0 0 \rangle \Big ) . | \mathrm{~W~}_{3}=\frac{1}{\sqrt{3}} \Big(\vert 0 0 1 \rangle+\vert 0 1 0 \rangle+\vert 1 0 0 \rangle \Big).
\approx | \approx
B \gamma _ { s s } - a ^ { 2 } K \gamma = 0 | B \gamma_{s s}-a^{2} K \gamma=0
a | a
\begin{array} { r l } { \hat { x } _ { k } } & { { } = \sum _ { j = 0 } ^ { N - 1 } x _ { j } \Psi ^ { j k / N } , } \\ { \hat { x } _ { k _ { 2 } , k _ { 1 } } } & { { } = \sum _ { j _ { 1 } = 0 } ^ { n _ { 1 } - 1 } \sum _ { j _ { 2 } = 0 } ^ { n _ { 2 } - 1 } x _ { j _ { 1 } , j 2 } \Psi ^ { j _ { 2 } k _ { 2 } / n _ { 2 } } \Psi ^ { j _ { 1 } k _ { 2 } / N } \Psi ^ { j _ { 1 } k _ { 1 } / n _ { 1 } } . } \end{array} | \begin{array}{rl}{{\hat{x}_{k}}} &{{=\sum_{j=0}^{N-1} x_{j} \Psi^{j k/N},}} \\{{\hat{x}_{k_{2},k_{1}}}} &{{=\sum_{j_{1}=0}^{n_{1}-1} \sum_{j_{2}=0}^{n_{2}-1} x_{j_{1},j 2} \Psi^{j_{2} k_{2}/n_{2}} \Psi^{j_{1} k_{2}/N} \Psi^{j_{1} k_{1}/n_{1}}.}} \end{array}
\alpha _ { n } ( \omega , k _ { z } ) = \frac { \varepsilon _ { 0 } } { \varepsilon _ { 1 } - \varepsilon _ { 0 } } + \frac { 1 } { 2 } \sum _ { l = \pm 1 } \left[ 1 + \frac { | \lambda _ { 1 } | } { | \lambda _ { 0 } | } \frac { I _ { n + l } ( | \lambda _ { 0 } | r _ { c } ) K _ { n } ( | \lambda _ { 1 } | r _ { c } ) } { I _ { n } ( | \lambda _ { 0 } | r _ { c } ) K _ { n + l } ( | \lambda _ { 1 } | r _ { c } ) } \right] ^ { - 1 } . | \alpha_{n}(\omega,k_{z})=\frac{\varepsilon_{0}}{\varepsilon_{1}-\varepsilon_{0}}+\frac{1}{2} \sum_{l=\pm 1} \left[1+\frac{| \lambda_{1} |}{| \lambda_{0} |} \frac{I_{n+l}(| \lambda_{0} | r_{c}) K_{n}(| \lambda_{1} | r_{c})}{I_{n}(| \lambda_{0} | r_{c}) K_{n+l}(| \lambda_{1} | r_{c})} \right]^{-1}.
\lambda | \lambda
\langle \hat { g } _ { x } \rangle = \langle \hat { g } _ { x } ^ { c l } \rangle , ~ ~ ~ ~ \langle \hat { g } _ { x } ^ { n c } \rangle = 0 , | \langle \hat{g}_{x} \rangle=\langle \hat{g}_{x}^{c l} \rangle,~ ~ ~ ~ \langle \hat{g}_{x}^{n c} \rangle=0,
\delta | \delta
d s ^ { 2 } = g _ { \alpha \beta } d x ^ { \alpha } d x ^ { \beta } + g _ { \iota \iota } d x ^ { \iota } d x ^ { \iota } \; . | d s^{2}=g_{\alpha \beta} d x^{\alpha} d x^{\beta}+g_{\iota \iota} d x^{\iota} d x^{\iota} \;.
\begin{array} { r } { \mathbf { H } = \left[ \begin{array} { l l l } { H _ { x } } & { H _ { x y } } & { H _ { x z } } \\ { 0 } & { H _ { y } } & { H _ { y z } } \\ { 0 } & { 0 } & { H _ { z } } \end{array} \right] } \end{array} | \begin{array}{r}{{\mathbf{H}=\left[\begin{array}{lll}{{H_{x}}} &{{H_{x y}}} &{{H_{x z}}} \\{{0}} &{{H_{y}}} &{{H_{y z}}} \\{{0}} &{{0}} &{{H_{z}}} \end{array} \right]}} \end{array}
p _ { 2 } | p_{2}
T _ { b } ^ { \prime } = | T _ { b } - 8 T _ { c } | = 2 . 7 \; n s | T_{b}^{\prime}=| T_{b}-8 T_{c} |=2.7 \;n s
( \sqrt { \mathrm { ~ \textit ~ { ~ N ~ M ~ S ~ E ~ } ~ } } < 1 \ | (\sqrt{\mathrm{~\textit~{~N~M~S~E~} ~}}<1 \
\angle A A ^ { \prime } D = \angle A A ^ { \prime } E = 9 0 ^ { \circ } | \angle A A^{\prime} D=\angle A A^{\prime} E=9 0^{\circ}
n | n
\Lambda | \Lambda
x | x
\begin{array} { r } { \left( \nabla \times { \mathbf B } \right) \times { \mathbf B } = - \mu _ { 0 } \nabla \cdot \boldsymbol { \pi } . } \end{array} | \begin{array}{r}{{\left(\nabla \times{\mathbf{B} \right) \times{\mathbf{B}=-\mu_{0} \nabla \cdot \boldsymbol{\pi}.}} \end{array}
\begin{array} { r l } { I _ { d } \left( \boldsymbol { q } _ { 0 } , \boldsymbol { s } \right) } & { = \left| \int P \left( \boldsymbol { x } \right) O \left( \boldsymbol { x } - \boldsymbol { s } \right) \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \boldsymbol { x } \right] d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \boldsymbol { s } \right] \int P \left( \boldsymbol { x } \right) O \left( \boldsymbol { x } - \boldsymbol { s } \right) \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \left( \boldsymbol { x } - \boldsymbol { s } \right) \right] d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| \int P \left( \boldsymbol { x } \right) O \left( \boldsymbol { x } - \boldsymbol { s } \right) \exp \left[ - i 2 \pi \boldsymbol { q } _ { 0 } \left( \boldsymbol { x } - \boldsymbol { s } \right) \right] d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| \int P \left( \boldsymbol { x } \right) O _ { \boldsymbol { q } _ { 0 } } ^ { \prime } \left( \boldsymbol { s } - \boldsymbol { x } \right) d \boldsymbol { x } \right| ^ { 2 } } \\ & { = \left| P \left( \boldsymbol { s } \right) \otimes O _ { \boldsymbol { q } _ { 0 } } ^ { \prime } \left( \boldsymbol { s } \right) \right| ^ { 2 } , } \end{array} | \begin{array}{rl}{{I_{d} \left(\boldsymbol{q}_{0},\boldsymbol{s} \right)}} &{{=\left| \int P \left(\boldsymbol{x} \right) O \left(\boldsymbol{x}-\boldsymbol{s} \right) \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \boldsymbol{x} \right] d \boldsymbol{x} \right|^{2}}} \\ &{{=\left| \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \boldsymbol{s} \right] \int P \left(\boldsymbol{x} \right) O \left(\boldsymbol{x}-\boldsymbol{s} \right) \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \left(\boldsymbol{x}-\boldsymbol{s} \right) \right] d \boldsymbol{x} \right|^{2}}} \\ &{{=\left| \int P \left(\boldsymbol{x} \right) O \left(\boldsymbol{x}-\boldsymbol{s} \right) \mathrm{exp} \left[-i 2 \pi \boldsymbol{q}_{0} \left(\boldsymbol{x}-\boldsymbol{s} \right) \right] d \boldsymbol{x} \right|^{2}}} \\ &{{=\left| \int P \left(\boldsymbol{x} \right) O_{\boldsymbol{q}_{0}}^{\prime} \left(\boldsymbol{s}-\boldsymbol{x} \right) d \boldsymbol{x} \right|^{2}}} \\ &{{=\left| P \left(\boldsymbol{s} \right) \otimes O_{\boldsymbol{q}_{0}}^{\prime} \left(\boldsymbol{s} \right) \right|^{2},}} \end{array}
\begin{array} { r l } { E ^ { ( 1 ) } ( x , z ) } & { { } = 1 + \sum _ { n } r _ { n } \cos ( 2 \pi n x / L ) \mathrm { ~ , ~ } } \\ { H ^ { ( 1 ) } ( x , z ) } & { { } = \alpha _ { 0 } - \sum _ { n } r _ { n } \alpha _ { n } \cos ( 2 \pi n x / L ) \mathrm { ~ , ~ } } \end{array} | \begin{array}{rl}{{E^{(1)}(x,z)}} &{{=1+\sum_{n} r_{n} \mathrm{cos}(2 \pi n x/L) \mathrm{~,~}}} \\{{H^{(1)}(x,z)}} &{{=\alpha_{0}-\sum_{n} r_{n} \alpha_{n} \mathrm{cos}(2 \pi n x/L) \mathrm{~,~}}} \end{array}
| \phi _ { 3 } \rangle = ( | t _ { 0 } \rangle - | t _ { 1 } \rangle - | t _ { 2 } \rangle + | t _ { 3 } \rangle ) / 2 | | \phi_{3} \rangle=(| t_{0} \rangle-| t_{1} \rangle-| t_{2} \rangle+| t_{3} \rangle)/2
A _ { \gamma } | A_{\gamma}
\epsilon = \delta / H | \epsilon=\delta/H
( d = 3 ) | (d=3)
\Phi ( \theta ) = \chi ^ { 2 } ( y | \theta ) / 2 | \Phi(\theta)=\chi^{2}(y | \theta)/2
Y | Y
M = B | M=B
\overline { { \mathcal { I } } } _ { \mathrm { o p t } } ( \mathcal { A } ) | \overline{{{\mathcal{I}}}}_{\mathrm{opt}}(\mathcal{A})
N = n _ { c } \mathcal { P } , | N=n_{c} \mathcal{P},
I _ { B 0 , 0 } ^ { L } | I_{B 0,0}^{L}
_ { a } ^ { A B } D _ { t } ^ { - \alpha } f ( t ) = _ { a } ^ { A B } I _ { t } ^ { \alpha } f ( t ) = { \frac { 1 - \alpha } { A B ( \alpha ) } } f ( t ) + { \frac { \alpha } { A B ( \alpha ) \Gamma ( \alpha ) } } \int _ { a } ^ { t } \left( t - \tau \right) ^ { \alpha - 1 } f ( \tau ) \, d \tau , | _{a}^{A B} D_{t}^{-\alpha} f(t)=_{a}^{A B} I_{t}^{\alpha} f(t)={\frac{1-\alpha}{A B(\alpha)}} f(t)+{\frac{\alpha}{A B(\alpha) \Gamma(\alpha)}} \int_{a}^{t} \left(t-\tau \right)^{\alpha-1} f(\tau) \,d \tau,
N _ { v } | N_{v}
f ^ { ( 6 ) } ( x ) = 7 2 0 | f^{(6)}(x)=7 2 0
\nu _ { T } = \frac { - ( \hat { \partial } _ { k } \widetilde { u } _ { i } ) ( \hat { \partial } _ { k } \widetilde { u } _ { j } ) \widetilde { S } _ { i j } } { ( \partial _ { l } \widetilde { u } _ { m } ) ( \partial _ { l } \widetilde { u } _ { m } ) } , | \nu_{T}=\frac{-(\hat{\partial}_{k} \widetilde{u}_{i})(\hat{\partial}_{k} \widetilde{u}_{j}) \widetilde{S}_{i j}}{(\partial_{l} \widetilde{u}_{m})(\partial_{l} \widetilde{u}_{m})},
E _ { \mathrm { c } } = \frac { e ^ { 2 } } { \pi } \int _ { 0 } ^ { \infty } d q \left[ S ( q ) - S ^ { ( 0 ) } ( q ) \right] . | E_{\mathrm{c}}=\frac{e^{2}}{\pi} \int_{0}^{\infty} d q \left[S(q)-S^{(0)}(q) \right].
\hat { H } ^ { \mathrm { ~ e ~ l ~ } } ( \eta ) = \hat { H } ^ { \mathrm { ~ e ~ l ~ } } - i \, \eta \, \hat { W } ( r ) ~ . | \hat{H}^{\mathrm{~e~l~}}(\eta)=\hat{H}^{\mathrm{~e~l~}}-i \,\eta \,\hat{W}(r) ~.
u ( x , t ) = \rho _ { i } ( t ) u _ { i } ^ { * } ( x , t ) | u(x,t)=\rho_{i}(t) u_{i}^{*}(x,t)
\hat { T } | \hat{T}
\int \limits _ { 0 } ^ { R } \frac { 2 x d x } { 1 + x ^ { 2 } } = \log ( 1 + R ^ { 2 } ) | \int_{0}^{R} \frac{2 x d x}{1+x^{2}}=\mathrm{log}(1+R^{2})
A ^ { \prime } = g A g ^ { - 1 } + g d g ^ { - 1 } , \ \ \ \omega ^ { \prime } = \omega , \ \ \ F ^ { \prime } = g F g ^ { - 1 } , \ \ \ \ a n d \ \ \ \ \Omega ^ { \prime } = \Omega . | A^{\prime}=g A g^{-1}+g d g^{-1},\ \ \ \omega^{\prime}=\omega,\ \ \ F^{\prime}=g F g^{-1},\ \ \ \ a n d \ \ \ \ \Omega^{\prime}=\Omega.
v _ { r e l } = v _ { r e l a t i v e } = v _ { S ^ { \prime } / S } \equiv d x ^ { \prime } / d t ^ { \prime } | v_{r e l}=v_{r e l a t i v e}=v_{S^{\prime}/S} \equiv d x^{\prime}/d t^{\prime}
{ \frac { B } { s } } ~ = ~ { \frac { 4 5 c _ { n } c _ { s } } { \pi g _ { * } } } { \frac { A } { 4 \lambda T _ { b } } } \epsilon ~ \simeq ~ { \frac { 0 . 0 1 \epsilon A } { \lambda T _ { b } } } . | {\frac{B}{s}} ~=~{\frac{4 5 c_{n} c_{s}}{\pi g_{*}}}{\frac{A}{4 \lambda T_{b}}} \epsilon ~ \sim eq ~{\frac{0.0 1 \epsilon A}{\lambda T_{b}}}.
Z | Z
8 9 9 9 | 8 9 9 9
^ { 8 7 } | ^{8 7}
1 0 ^ { - 2 } | 1 0^{-2}
w \in \mathcal { O } _ { v , M } | w \in \mathcal{O}_{v,M}
n - 1 | n-1
\Vert \hat { T } _ { * } \phi _ { 0 } \Vert _ { L ^ { 2 } } \leq \Vert \hat { T } _ { * } \Vert _ { L ^ { 2 } } \Vert \phi _ { 0 } \Vert _ { L ^ { 2 } } = \Vert \hat { T } _ { * } \Vert _ { L ^ { 2 } } | \Vert \hat{T}_{*} \phi_{0} \Vert_{L^{2}} \leq \Vert \hat{T}_{*} \Vert_{L^{2}} \Vert \phi_{0} \Vert_{L^{2}}=\Vert \hat{T}_{*} \Vert_{L^{2}}
\begin{array} { r l } { W _ { 2 } ^ { 2 } \big ( \nu ^ { m , t } , \exp ( \nabla h _ { \delta } ^ { n , t } ) _ { \# } \mu ^ { n , t } \big ) } & { = W _ { 2 } ^ { 2 } \big ( \phi ( 1 , \cdot ) _ { \# } \mu ^ { n , t } , \exp ( \nabla h _ { \delta } ^ { n , t } ) _ { \# } \mu ^ { n , t } \big ) } \\ & { \lesssim \int _ { \mathcal { M } } \Big ( \vert \rho _ { \delta } - \rho \vert + \vert \rho _ { t } - \rho \vert + \frac { 1 } { \log ^ { \upsilon } ( n ) } \Big ) ^ { 2 } \vert \nabla h _ { \delta } ^ { n , t } \vert ^ { 2 } } \\ & { \leq \big ( \| \rho _ { \delta } - \rho \| _ { \mathrm { L } ^ { 2 ( \frac { \bar { q } } { 2 } ) ^ { \prime } } } ^ { 2 } + \| \rho _ { t } - \rho \| _ { \mathrm { L } ^ { 2 ( \frac { \bar { q } } { 2 } ) ^ { \prime } } } ^ { 2 } + \frac { 1 } { \log ^ { \upsilon } ( n ) } \big ) \Big ( \int _ { \mathcal { M } } \vert \nabla h _ { \delta } ^ { n , t } \vert ^ { \bar { q } } \Big ) ^ { \frac { 2 } { \bar { q } } } . } \end{array} | \begin{array}{rl}{{W_{2}^{2} \big(\nu^{m,t},\mathrm{exp}(\nabla h_{\delta}^{n,t})_{\#} \mu^{n,t} \big)}} &{{=W_{2}^{2} \big(\phi(1,\cdot)_{\#} \mu^{n,t},\mathrm{exp}(\nabla h_{\delta}^{n,t})_{\#} \mu^{n,t} \big)}} \\ &{{\lesssim \int_{\mathcal{M}} \Big(\vert \rho_{\delta}-\rho \vert+\vert \rho_{t}-\rho \vert+\frac{1}{\mathrm{log}^{\up silon}(n)} \Big)^{2} \vert \nabla h_{\delta}^{n,t} \vert^{2}}} \\ &{{\leq \big(\| \rho_{\delta}-\rho \|_{\mathrm{L}^{2(\frac{\bar{q}}{2})^{\prime}}}^{2}+\| \rho_{t}-\rho \|_{\mathrm{L}^{2(\frac{\bar{q}}{2})^{\prime}}}^{2}+\frac{1}{\mathrm{log}^{\up silon}(n)} \big) \Big(\int_{\mathcal{M}} \vert \nabla h_{\delta}^{n,t} \vert^{\bar{q}} \Big)^{\frac{2}{\bar{q}}}.}} \end{array}
N ^ { \mathrm { X } } ( \tau ^ { 1 } ) = N ^ { \mathrm { X } } ( \tau _ { - } ^ { 1 } ) - 2 | N^{\mathrm{X}}(\tau^{1})=N^{\mathrm{X}}(\tau_{-}^{1})-2
\begin{array} { r l } & { \boldsymbol { b } _ { 1 } = [ \boldsymbol { h } ^ { \top } , \boldsymbol { f } _ { 1 } ^ { \top } , \boldsymbol { 0 } _ { ( ( N _ { t } - 1 ) ( Q _ { t } + 1 ) Q _ { x } ) } ^ { \top } ] ^ { \top } , } \\ & { \boldsymbol { b } _ { i } = [ \boldsymbol { 0 } _ { ( ( ( i - 1 ) ( Q _ { t } + 1 ) + 1 ) Q _ { x } ) } ^ { \top } , \boldsymbol { f } _ { i } ^ { \top } , \boldsymbol { 0 } _ { ( ( N _ { t } - i ) ( Q _ { t } + 1 ) Q _ { x } ) } ^ { \top } ] , \quad \mathrm { f o r } \; i = 2 , \cdots , N _ { t } . } \end{array} | \begin{array}{rl} &{{\boldsymbol{b}_{1}=[\boldsymbol{h}^{\top},\boldsymbol{f}_{1}^{\top},\boldsymbol{0}_{((N_{t}-1)(Q_{t}+1) Q_{x})}^{\top}]^{\top},}} \\ &{{\boldsymbol{b}_{i}=[\boldsymbol{0}_{(((i-1)(Q_{t}+1)+1) Q_{x})}^{\top},\boldsymbol{f}_{i}^{\top},\boldsymbol{0}_{((N_{t}-i)(Q_{t}+1) Q_{x})}^{\top}],\quad \mathrm{for} \;i=2,. . .,N_{t}.}} \end{array}
^ { 4 0 } | ^{4 0}
\left\{ \begin{array} { c c c } { { \lambda x ^ { + } } } & { { = } } & { { e ^ { \lambda \sigma ^ { + } } } } \\ { { \lambda \left( x ^ { - } + \Delta _ { q } \right) } } & { { = } } & { { - e ^ { - \lambda \sigma ^ { - } } } } \end{array} \ \ \ \ , \right. | \left\{\begin{array}{ccc}{{{\lambda x^{+}}}} &{{{=}}} &{{{e^{\lambda \sigma^{+}}}}} \\{{{\lambda \left(x^{-}+\Delta_{q} \right)}}} &{{{=}}} &{{{-e^{-\lambda \sigma^{-}}}}} \end{array} \ \ \ \,\right.
4 6 | 4 6
\chi ^ { 2 } | \chi^{2}
\begin{array} { r l } { \frac { | T _ { n } + B ( 0 , \| h \| _ { 2 } ) | - | T _ { n } | } { | T _ { n } | } } & { = \sum _ { j = 0 } ^ { d - 1 } \frac { \mu _ { j } ( T _ { n } ) \| h \| _ { 2 } ^ { d - j } } { | T _ { n } | } } \\ & { = \sum _ { j = 0 } ^ { d - 1 } \mu _ { j } \left( \frac { T _ { n } } { | T _ { n } | ^ { 1 / d } } \right) \cdot \left( \frac { \| h \| _ { 2 } } { | T _ { n } | ^ { 1 / d } } \right) ^ { d - j } , } \end{array} | \begin{array}{rl}{{\frac{| T_{n}+B(0,\| h \|_{2}) |-| T_{n} |}{| T_{n} |}}} &{{=\sum_{j=0}^{d-1} \frac{\mu_{j}(T_{n}) \| h \|_{2}^{d-j}}{| T_{n} |}}} \\ &{{=\sum_{j=0}^{d-1} \mu_{j} \left(\frac{T_{n}}{| T_{n} |^{1/d}} \right) \cdot \left(\frac{\| h \|_{2}}{| T_{n} |^{1/d}} \right)^{d-j},}} \end{array}
x | x
^ { - 1 } | ^{-1}
\sigma | \sigma
\begin{array} { r l } { \hat { \boldsymbol { \Phi } } _ { \alpha } = } & { ~ \hat { \boldsymbol { \Phi } } _ { \alpha } \left( \phi _ { \alpha } , \nabla \phi _ { \alpha } , \mathrm { d i v } \mathbf { v } _ { \alpha } , \mathbf { q } _ { \alpha } , \gamma _ { \alpha } \right) , } \\ { \hat { s } _ { \alpha } = } & { ~ \hat { s } _ { \alpha } \left( r _ { \alpha } \right) , } \\ { \hat { \mathbf { T } } _ { \alpha } = } & { ~ \hat { \mathbf { T } } _ { \alpha } ( \phi _ { \alpha } , \nabla \phi _ { \alpha } , \mathbf { D } _ { \alpha } , \pi _ { \alpha } , p ) , } \\ { \hat { \gamma } _ { \alpha } = } & { ~ \hat { \gamma } _ { \alpha } \left( \phi _ { \alpha } , \nabla \phi _ { \alpha } , p , \left\{ \psi _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } , \left\{ \mu _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } \right) , } \\ { \hat { \boldsymbol { \pi } } _ { \alpha } = } & { ~ \hat { \boldsymbol { \pi } } _ { \alpha } \left( \phi _ { \alpha } , \nabla \phi _ { \alpha } , \left\{ \mathbf { v } _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } , \left\{ \gamma _ { \beta } \right\} _ { { \beta } = 1 , \dots , N } \right) , } \end{array} | \begin{array}{rl}{{\hat{\boldsymbol{\Phi}}_{\alpha}=}} &{{~ \hat{\boldsymbol{\Phi}}_{\alpha} \left(\phi_{\alpha},\nabla \phi_{\alpha},\mathrm{div} \mathbf{v}_{\alpha},\mathbf{q}_{\alpha},\gamma_{\alpha} \right),}} \\{{\hat{s}_{\alpha}=}} &{{~ \hat{s}_{\alpha} \left(r_{\alpha} \right),}} \\{{\hat{\mathbf{T}}_{\alpha}=}} &{{~ \hat{\mathbf{T}}_{\alpha}(\phi_{\alpha},\nabla \phi_{\alpha},\mathbf{D}_{\alpha},\pi_{\alpha},p),}} \\{{\hat{\gamma}_{\alpha}=}} &{{~ \hat{\gamma}_{\alpha} \left(\phi_{\alpha},\nabla \phi_{\alpha},p,\left\{\psi_{\beta} \right\}_{{\beta}=1,. . .,N},\left\{\mu_{\beta} \right\}_{{\beta}=1,. . .,N} \right),}} \\{{\hat{\boldsymbol{\pi}}_{\alpha}=}} &{{~ \hat{\boldsymbol{\pi}}_{\alpha} \left(\phi_{\alpha},\nabla \phi_{\alpha},\left\{\mathbf{v}_{\beta} \right\}_{{\beta}=1,. . .,N},\left\{\gamma_{\beta} \right\}_{{\beta}=1,. . .,N} \right),}} \end{array}
p = 0 | p=0
\sim | \sim
{ \cal L } = \bar { \psi } ^ { ( i ) } \mathrm { i } \partial \! \! \! / \, \psi ^ { ( i ) } + \frac { g ^ { 2 } } { 2 } ( \bar { \psi } ^ { ( i ) } \psi ^ { ( i ) } ) ^ { 2 } \ , | {\cal L}=\bar{\psi}^{(i)} \mathrm{i} \partial \! \! \!/\,\psi^{(i)}+\frac{g^{2}}{2}(\bar{\psi}^{(i)} \psi^{(i)})^{2} \,
{ I } = \{ k _ { 0 } , k _ { 0 } + 1 , \ldots , k _ { 0 } + N _ { s } - 1 \} , | {I}=\{k_{0},k_{0}+1,. . .,k_{0}+N_{s}-1 \},
n < 0 | n<0
\sim 2 0 . 5 | \sim 2 0.5
\mathcal { V } = e ^ { K } \left( ( W _ { I } ^ { * } + K _ { I } W ^ { * } ) ( K ^ { - 1 } ) _ { J } ^ { I } ( W ^ { J } + K ^ { J } W ) - 3 | W | ^ { 2 } \right) | \mathcal{V}=e^{K} \left((W_{I}^{*}+K_{I} W^{*})(K^{-1})_{J}^{I}(W^{J}+K^{J} W)-3 | W |^{2} \right)
3 0 \, \mathrm { \ u p m u m } \times 5 \, \mathrm { \ u p m u m } | 3 0 \,\mathrm{\up mum} \times 5 \,\mathrm{\up mum}
V _ { 2 } = 4 . 0 ( 1 ) E _ { r } ^ { ( 7 5 2 ) } | V_{2}=4.0(1) E_{r}^{(7 5 2)}
\chi _ { x y z , \mathrm { n o r m } } ^ { ( 2 ) } | \chi_{x y z,\mathrm{norm}}^{(2)}
\delta > 0 | \delta>0
1 6 | 1 6
\begin{array} { r } { f ( x ) + g ( x ) = \left( \begin{array} { l } { q _ { 1 } + k _ { 1 } } \\ { q _ { 2 } + k _ { 2 } } \\ { \cdots } \\ { q _ { n } + k _ { n } } \end{array} \right) } \end{array} | \begin{array}{r}{{f(x)+g(x)=\left(\begin{array}{l}{{q_{1}+k_{1}}} \\{{q_{2}+k_{2}}} \\{{. . .}} \\{{q_{n}+k_{n}}} \end{array} \right)}} \end{array}
\theta = \operatorname { a r c c o s } ( 1 / \sqrt { 3 } ) = 0 . 9 5 5 | \theta=\mathrm{arccos}(1/\sqrt{3})=0.9 5 5
k \in \left\lbrace 1 , \dots , N _ { \mathrm { v e r } } \right\rbrace | k \in \left\lbrace 1,. . .,N_{\mathrm{ver}} \right\rbrace
L = \lambda | L=\lambda
4 ^ { t h } | 4^{t h}
\begin{array} { r } { \langle \hat { f } _ { k } ^ { \mathrm { ~ \tiny ~ B ~ } } ( t ) \hat { f } _ { k ^ { \prime } } ^ { \mathrm { ~ \tiny ~ B ~ } } ( 0 ) \rangle _ { \mathrm { ~ \tiny ~ B ~ } } = \delta _ { k k ^ { \prime } } \eta _ { k } e ^ { - \gamma _ { k } t } , } \\ { \langle \hat { f } _ { k } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( 0 ) \hat { f } _ { k ^ { \prime } } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( t ) \rangle _ { \mathrm { ~ \tiny ~ B ~ } } = \delta _ { k k ^ { \prime } } \eta _ { k } ^ { * } e ^ { - \gamma _ { \bar { k } } t } , } \\ { \langle \hat { f } _ { \bar { k } } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( 0 ) \hat { f } _ { \bar { k } ^ { \prime } } ^ { \dag \mathrm { ~ \tiny ~ B ~ } } ( t ) \rangle _ { \mathrm { ~ \tiny ~ B ~ } } = \delta _ { k k ^ { \prime } } \eta _ { \bar { k } } ^ { * } e ^ { - \gamma _ { k } t } , } \end{array} | \begin{array}{r}{{\langle \hat{f}_{k}^{\mathrm{~\tiny~B~}}(t) \hat{f}_{k^{\prime}}^{\mathrm{~\tiny~B~}}(0) \rangle_{\mathrm{~\tiny~B~}}=\delta_{k k^{\prime}} \eta_{k} e^{-\gamma_{k} t},}} \\{{\langle \hat{f}_{k}^{\dag \mathrm{~\tiny~B~}}(0) \hat{f}_{k^{\prime}}^{\dag \mathrm{~\tiny~B~}}(t) \rangle_{\mathrm{~\tiny~B~}}=\delta_{k k^{\prime}} \eta_{k}^{*} e^{-\gamma_{\bar{k}} t},}} \\{{\langle \hat{f}_{\bar{k}}^{\dag \mathrm{~\tiny~B~}}(0) \hat{f}_{\bar{k}^{\prime}}^{\dag \mathrm{~\tiny~B~}}(t) \rangle_{\mathrm{~\tiny~B~}}=\delta_{k k^{\prime}} \eta_{\bar{k}}^{*} e^{-\gamma_{k} t},}} \end{array}
\begin{array} { r l } { D _ { A } ^ { z } \equiv } & { { } ~ \sigma _ { A } ^ { \dagger } \sigma _ { A } - \sigma _ { A } \sigma _ { A } ^ { \dagger } + \sigma _ { 4 4 } - \sigma _ { 3 3 } + \sigma _ { 2 2 } - \sigma _ { 1 1 } } \\ { = } & { { } D _ { A } ^ { \dagger } D _ { A } - D _ { A } D _ { A } ^ { \dagger } } \\ { D _ { B } ^ { z } \equiv } & { { } ~ \sigma _ { B } ^ { \dagger } \sigma _ { B } - \sigma _ { B } \sigma _ { B } ^ { \dagger } + \sigma _ { 4 4 } - \sigma _ { 2 2 } + \sigma _ { 3 3 } - \sigma _ { 1 1 } } \\ { = } & { { } D _ { B } ^ { \dagger } D _ { B } - D _ { B } D _ { B } ^ { \dagger } . } \end{array} | \begin{array}{rl}{{D_{A}^{z} \equiv}} &{{ ~ \sigma_{A}^{\dagger} \sigma_{A}-\sigma_{A} \sigma_{A}^{\dagger}+\sigma_{4 4}-\sigma_{3 3}+\sigma_{2 2}-\sigma_{1 1}}} \\{{=}} &{{ D_{A}^{\dagger} D_{A}-D_{A} D_{A}^{\dagger}}} \\{{D_{B}^{z} \equiv}} &{{ ~ \sigma_{B}^{\dagger} \sigma_{B}-\sigma_{B} \sigma_{B}^{\dagger}+\sigma_{4 4}-\sigma_{2 2}+\sigma_{3 3}-\sigma_{1 1}}} \\{{=}} &{{ D_{B}^{\dagger} D_{B}-D_{B} D_{B}^{\dagger}.}} \end{array}
\gamma _ { p } \approx 2 \pi \times 4 2 . 5 8 | \gamma_{p} \approx 2 \pi \times 4 2.5 8
k | k
N _ { s } | N_{s}
p _ { a } ( t ) = \frac { t _ { r } ^ { a _ { r } } e ^ { - t _ { r } } } { a _ { r } ! } , \quad \alpha _ { r } \to \infty . | p_{a}(t)=\frac{t_{r}^{a_{r}} e^{-t_{r}}}{a_{r} !},\quad \alpha_{r} \to \infty.
\alpha < 3 / 2 | \alpha<3/2
\boldsymbol { v } = \frac { F } { 8 \pi \eta } \left( \boldsymbol { G } ^ { \infty } + \boldsymbol { G } \right) \, , \qquad p = \frac { F } { 4 \pi } \left( P ^ { \infty } + P \right) \, , | \boldsymbol{v}=\frac{F}{8 \pi \eta} \left(\boldsymbol{G}^{\infty}+\boldsymbol{G} \right) \,,\qquad p=\frac{F}{4 \pi} \left(P^{\infty}+P \right) \,,
\gamma | \gamma
\bar { P } _ { z , \mathrm { N S } } = P _ { 0 } ( 1 + 1 / G T _ { 1 } ) | \bar{P}_{z,\mathrm{NS}}=P_{0}(1+1/G T_{1})
\widetilde { p } = { \mathbf { p } } \cdot d { \mathbf { x } } \otimes d ^ { 3 } x \in \Lambda ^ { 1 } ( \mathbb { R } ^ { 3 } ) \otimes \mathrm { D e n } ( \mathbb { R } ^ { 3 } ) ) | \widetilde{p}={\mathbf{p}} \cdot d{\mathbf{x}} \otimes d^{3} x \in \Lambda^{1}(\mathbb{R}^{3}) \otimes \mathrm{Den}(\mathbb{R}^{3}))
H ( x _ { + } ) = \int _ { - \infty } ^ { \infty } { \frac { d k _ { + } } { 2 \pi } } \int { \frac { d ^ { 2 } \widetilde { k } } { ( 2 \pi ) ^ { 2 } } } \sqrt { 2 } \Psi ^ { \dagger } \left( x _ { + } , k _ { + } , \widetilde { k } \right) i \partial _ { + } \Psi \left( x _ { + } , k _ { + } , \widetilde { k } \right) \; . | H(x_{+})=\int_{-\infty}^{\infty}{\frac{d k_{+}}{2 \pi}} \int{\frac{d^{2} \widetilde{k}}{(2 \pi)^{2}}} \sqrt{2} \Psi^{\dagger} \left(x_{+},k_{+},\widetilde{k} \right) i \partial_{+} \Psi \left(x_{+},k_{+},\widetilde{k} \right) \;.
\eta | \eta
Q _ { i n t } = ( 4 . 7 - 5 . 7 ) \times 1 0 ^ { 5 } | Q_{i n t}=(4.7-5.7) \times 1 0^{5}
g ( \omega ) = \sum _ { n = 0 } ^ { \infty } \tilde { a } _ { n } \omega ^ { n } , | g(\omega)=\sum_{n=0}^{\infty} \tilde{a}_{n} \omega^{n},
y = L / 2 | y=L/2
k _ { i } | k_{i}
\begin{array} { r l } & { v _ { 4 k - 3 - 2 i , 2 k + 6 i } ^ { A , i + 1 } \otimes v _ { 1 , 3 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k + 2 + 6 i } ^ { A , i + 1 } \otimes v _ { 1 , 1 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k + 1 + 6 i } ^ { A , i + 1 } \otimes v _ { 1 , 2 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k - 1 + 6 i } ^ { A , i } \otimes v _ { 1 , 4 } } \\ & { + v _ { 4 k - 3 - 2 i , 2 k - 1 + 6 i } ^ { B , i } \otimes v _ { 1 , 4 } } \end{array} | \begin{array}{rl} &{{v_{4 k-3-2 i,2 k+6 i}^{A,i+1} \otimes v_{1,3}}} \\ &{{+v_{4 k-3-2 i,2 k+2+6 i}^{A,i+1} \otimes v_{1,1}}} \\ &{{+v_{4 k-3-2 i,2 k+1+6 i}^{A,i+1} \otimes v_{1,2}}} \\ &{{+v_{4 k-3-2 i,2 k-1+6 i}^{A,i} \otimes v_{1,4}}} \\ &{{+v_{4 k-3-2 i,2 k-1+6 i}^{B,i} \otimes v_{1,4}}} \end{array}
\mathcal D ~ \circ ~ \mathcal E ( \mathcal X ) | \mathcal{D} ~ \circ ~ \mathcal{E}(\mathcal{X})
M \times M | M \times M
\begin{array} { r } { { \overline { { \mathbf { e } _ { j } } } } ( s ) = \mathbf { r } ^ { ( j ) } ( s ) - \sum _ { i = 1 } ^ { j - 1 } \langle \mathbf { r } ^ { ( j ) } ( s ) , \mathbf { e } _ { i } ( s ) \rangle \, \mathbf { e } _ { i } ( s ) . } \end{array} | \begin{array}{r}{{{\overline{{{\mathbf{e}_{j}}}}}(s)=\mathbf{r}^{(j)}(s)-\sum_{i=1}^{j-1} \langle \mathbf{r}^{(j)}(s),\mathbf{e}_{i}(s) \rangle \,\mathbf{e}_{i}(s).}} \end{array}
\widehat \delta ^ { 2 } = 0 . 5 | \widehat{\delta}^{2}=0.5
\lambda = \bar { U } ^ { - 1 } p ^ { - 1 } \bar { m } , ~ ~ ~ ~ ~ ( \bar { m } = m ^ { - 1 } ) | \lambda=\bar{U}^{-1} p^{-1} \bar{m},~ ~ ~ ~ ~(\bar{m}=m^{-1})
\mathbf { u } = [ \rho , \boldsymbol { \rho } \mathbf { v } , E ] ^ { T } = [ \rho , \rho u , \rho v , \rho w , E ] ^ { T } | \mathbf{u}=[\rho,\boldsymbol{\rho} \mathbf{v},E]^{T}=[\rho,\rho u,\rho v,\rho w,E]^{T}
\begin{array} { r l r } { \sum \vert M _ { g } ^ { \mathrm { ~ v ~ i ~ o ~ l ~ } } \vert ^ { 2 } } & { { } = } & { g _ { S } ^ { 2 n - 4 } ( Q ^ { 2 } ) ~ N ^ { n - 2 } ( N ^ { 2 } - 1 ) } \end{array} | \begin{array}{rlr}{{\sum \vert M_{g}^{\mathrm{~v~i~o~l~}} \vert^{2}}} &{{=}} &{{g_{S}^{2 n-4}(Q^{2}) ~ N^{n-2}(N^{2}-1)}} \end{array}
s n u . c | s n u.c
{ \begin{array} { r l } { c _ { T } ( k ) } & { = A k ^ { a } } \\ { c _ { T - 1 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b } } } \\ { c _ { T - 2 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } } } } \\ & { \dots } \\ { c _ { 2 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } + \ldots + a ^ { T - 2 } b ^ { T - 2 } } } } \\ { c _ { 1 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } + \ldots + a ^ { T - 2 } b ^ { T - 2 } + a ^ { T - 1 } b ^ { T - 1 } } } } \\ { c _ { 0 } ( k ) } & { = { \frac { A k ^ { a } } { 1 + a b + a ^ { 2 } b ^ { 2 } + \ldots + a ^ { T - 2 } b ^ { T - 2 } + a ^ { T - 1 } b ^ { T - 1 } + a ^ { T } b ^ { T } } } } \end{array} } | {\begin{array}{rl}{{c_{T}(k)}} &{{=A k^{a}}} \\{{c_{T-1}(k)}} &{{={\frac{A k^{a}}{1+a b}}}} \\{{c_{T-2}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}}}}} \\ &{{. . .}} \\{{c_{2}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}+. . .+a^{T-2} b^{T-2}}}}} \\{{c_{1}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}+. . .+a^{T-2} b^{T-2}+a^{T-1} b^{T-1}}}}} \\{{c_{0}(k)}} &{{={\frac{A k^{a}}{1+a b+a^{2} b^{2}+. . .+a^{T-2} b^{T-2}+a^{T-1} b^{T-1}+a^{T} b^{T}}}}} \end{array}}
\begin{array} { r l } { \hat { R } ( t ) } & { { } = \hat { R } _ { 1 } ( t ) + \hat { R } _ { 2 } ( t ) + \hat { R } _ { 3 } ( t ) + \dots . } \end{array} | \begin{array}{rl}{{\hat{R}(t)}} &{{=\hat{R}_{1}(t)+\hat{R}_{2}(t)+\hat{R}_{3}(t)+. . ..}} \end{array}
N - 1 | N-1
2 \times 1 0 ^ { 1 8 } m ^ { - 3 } | 2 \times 1 0^{1 8} m^{-3}
\begin{array} { r } { p _ { v v ^ { \prime } } ( \Omega ) = - \langle f _ { v } ^ { \Omega } ( r ) | \frac { d U _ { \mathrm { ~ t ~ o ~ t ~ } } ^ { \Omega } ( r ) / d \Omega } { E _ { v } ^ { \Omega } - E _ { v ^ { \prime } } ^ { \Omega } } | f _ { v ^ { \prime } } ^ { \Omega } ( r ) \rangle } \end{array} | \begin{array}{r}{{p_{v v^{\prime}}(\Omega)=-\langle f_{v}^{\Omega}(r) | \frac{d U_{\mathrm{~t~o~t~}}^{\Omega}(r)/d \Omega}{E_{v}^{\Omega}-E_{v^{\prime}}^{\Omega}} | f_{v^{\prime}}^{\Omega}(r) \rangle}} \end{array}
f ( g , \alpha _ { \mathrm { ~ s ~ o ~ c ~ i ~ a ~ l ~ } } ) | f(g,\alpha_{\mathrm{~s~o~c~i~a~l~}})
1 / 1 4 | 1/1 4
N \to \infty \ , \quad \mathrm { ~ g _ { \mathrm { ~ s } } ~ , ~ \ a l p h a ^ { \prime } ~ , a n d ~ s ~ f i x e d \ , } \quad R = ( 4 \pi \alpha ^ { 2 } g _ { \mathrm { s } } N ) ^ { 1 / 4 } \ , \quad \omega = \frac { 1 } { 2 } R s ^ { 1 / 2 } \ . | N \to \infty \,\quad \mathrm{~g_{\mathrm{~s}} ~,~ \ a l p h a^{\prime} ~,a n d ~ s ~ f i x e d \,} \quad R=(4 \pi \alpha^{2} g_{\mathrm{s}} N)^{1/4} \,\quad \omega=\frac{1}{2} R s^{1/2} \.
^ { * \dagger } | ^{*\dagger}
\ddot { \Sigma } _ { n } ^ { \phi } + ( n ^ { 2 } + \frac { m ^ { 2 } } { l ^ { 2 } } ) \Sigma _ { n } ^ { \phi } = \tilde { U } _ { n } ^ { \phi } , | \ddot{\Sigma}_{n}^{\phi}+(n^{2}+\frac{m^{2}}{l^{2}}) \Sigma_{n}^{\phi}=\tilde{U}_{n}^{\phi},
n e a r | n e a r
[ \lambda _ { \mathrm { F } } , \lambda _ { \mathrm { u b } } ] | [\lambda_{\mathrm{F}},\lambda_{\mathrm{ub}}]
\sqrt { ( \ell + 1 ) ^ { 2 } - m ^ { 2 } } K _ { \ell + 1 } ^ { m } ( x ) = ( 2 \ell + 1 ) x K _ { \ell } ^ { m } ( x ) - \sqrt { \ell ^ { 2 } - m ^ { 2 } } K _ { \ell - 1 } ^ { m } ( x ) | \sqrt{(\ell+1)^{2}-m^{2}} K_{\ell+1}^{m}(x)=(2 \ell+1) x K_{\ell}^{m}(x)-\sqrt{\ell^{2}-m^{2}} K_{\ell-1}^{m}(x)
\alpha _ { \mathrm { ~ h ~ o ~ m ~ o ~ p ~ h ~ i ~ l ~ y ~ } } > 0 . 5 ) | \alpha_{\mathrm{~h~o~m~o~p~h~i~l~y~}}>0.5)
N > 2 | N>2
\sim 1 0 | \sim 1 0
\overline { { \mathcal { E } } } _ { r } ^ { ( 0 ) } | \overline{{{\mathcal{E}}}}_{r}^{(0)}
S = \int d ^ { 4 } x \sqrt { | g _ { E } | } \left[ \hat { R } ( \hat { g } _ { E } ) - { \textstyle \frac { 1 } { 2 } } \frac { \partial _ { \hat { \mu } } \hat { \lambda } \partial ^ { \hat { \mu } } \bar { \hat { \lambda } } } { \left( \Im \mathrm { m } \hat { \lambda } \right) ^ { 2 } } + { \textstyle \frac { 1 } { 4 } } \hat { F } ^ { I } \ { } ^ { \star } \tilde { \hat { F } ^ { I } } \right] \, . | S=\int d^{4} x \sqrt{| g_{E} |} \left[\hat{R}(\hat{g}_{E})-{\textstyle \frac{1}{2}} \frac{\partial_{\hat{\mu}} \hat{\lambda} \partial^{\hat{\mu}} \bar{\hat{\lambda}}}{\left(\Im \mathrm{m} \hat{\lambda} \right)^{2}}+{\textstyle \frac{1}{4}} \hat{F}^{I} \^{\star} \tilde{\hat{F}^{I}} \right] \,.
R = X ( \alpha ) Y ( \beta ) Z ( \gamma ) | R=X(\alpha) Y(\beta) Z(\gamma)
\delta | \delta
\vec { \sigma } = \frac { \vec { \sigma } _ { E } } { 1 - \left( \displaystyle \frac { \vec { \sigma } _ { E } } { c } \right) ^ { 2 } } , | \vec{\sigma}=\frac{\vec{\sigma}_{E}}{1-\left(\displaystyle \frac{\vec{\sigma}_{E}}{c} \right)^{2}},
P _ { F } ( q ) = P ( q ) \equiv q ^ { 2 } \left( 1 + r _ { F } ( q ) \right) ^ { 2 } \; . | P_{F}(q)=P(q) \equiv q^{2} \left(1+r_{F}(q) \right)^{2} \;.
p = 3 0 | p=3 0
\begin{array} { r } { r ( t _ { 1 } ) < \frac { \alpha p _ { r } } { \alpha p _ { r } + l _ { i } } < r ( t _ { 2 } ) . } \end{array} | \begin{array}{r}{{r(t_{1})<\frac{\alpha p_{r}}{\alpha p_{r}+l_{i}}<r(t_{2}).}} \end{array}
\begin{array} { r l r } { x \oplus y } & { = } & { f ( f ^ { - 1 } ( x ) + f ^ { - 1 } ( y ) ) , } \\ { x \ominus y } & { = } & { f ( f ^ { - 1 } ( x ) - f ^ { - 1 } ( y ) ) , } \\ { x \otimes y } & { = } & { f ( f ^ { - 1 } ( x ) f ^ { - 1 } ( y ) ) , } \\ { x \oslash y } & { = } & { f ( f ^ { - 1 } ( x ) / f ^ { - 1 } ( y ) ) . } \end{array} | \begin{array}{rlr}{{x \oplus y}} &{{=}} &{{f(f^{-1}(x)+f^{-1}(y)),}} \\{{x \ominus y}} &{{=}} &{{f(f^{-1}(x)-f^{-1}(y)),}} \\{{x \otimes y}} &{{=}} &{{f(f^{-1}(x) f^{-1}(y)),}} \\{{x \oslash y}} &{{=}} &{{f(f^{-1}(x)/f^{-1}(y)).}} \end{array}
( 1 + 2 \rho ) ^ { - 2 } | (1+2 \rho)^{-2}
\mathbb { E } ( X _ { j } ) = 0 | \mathbb{E}(X_{j})=0
Z ( \beta ) = \int _ { q ( 0 ) = q ( \beta ) } [ { \cal D } q ] \ \exp ( - S [ q ; \beta ] ) | Z(\beta)=\int_{q(0)=q(\beta)}[{\cal D} q] \ \mathrm{exp}(-S[q;\beta])
X = 1 | X=1
k = 0 . 6 | k=0.6
\frac { A } { 2 \pi } \int d ^ { 2 } x ~ \epsilon ^ { \mu \nu } ~ \partial _ { \mu } \omega _ { \nu } + \frac { B } { 2 \pi } \int d ^ { 2 } x ~ \epsilon ^ { \mu \nu } ~ \partial _ { \mu } a _ { \nu } = ( 2 - 2 g ) A + \frac { B V } { 2 \pi } = N , | \frac{A}{2 \pi} \int d^{2} x ~ \epsilon^{\mu \nu} ~ \partial_{\mu} \omega_{\nu}+\frac{B}{2 \pi} \int d^{2} x ~ \epsilon^{\mu \nu} ~ \partial_{\mu} a_{\nu}=(2-2 g) A+\frac{B V}{2 \pi}=N,
\frac { d B _ { p } ( t ) } { d t } = \delta _ { H } ( t ) H ( t ) - h ( t ) . | \frac{d B_{p}(t)}{d t}=\delta_{H}(t) H(t)-h(t).
- 1 | -1
\beta _ { 0 } | \beta_{0}
\sum F _ { i } = m { \frac { d V } { d t } } + v _ { \mathrm { e } } { \frac { d m } { d t } } | \sum F_{i}=m{\frac{d V}{d t}}+v_{\mathrm{e}}{\frac{d m}{d t}}
\lambda | \lambda
[ \nabla _ { T } ^ { 2 } + ( \frac { \Omega _ { m l k } ^ { 2 } } { v _ { L } ^ { 2 } } - q _ { m l } ^ { 2 } ) ] { \delta \rho _ { k } ^ { \: m l } } = 0 . | [\nabla_{T}^{2}+(\frac{\Omega_{m l k}^{2}}{v_{L}^{2}}-q_{m l}^{2})]{\delta \rho_{k}^{\:m l}}=0.
\kappa ( t ) | \kappa(t)
R = \left( \begin{array} { l l } { 0 } & { - 1 } \\ { 1 } & { 0 } \end{array} \right) , \quad Q = \left( \begin{array} { l l } { 0 } & { R } \\ { R } & { 0 } \end{array} \right) , \quad \mathbf { s } _ { t } = ( \hat { \mathbf { J } } _ { t } ^ { s } , \hat { \mathbf { M } } _ { t } ^ { s } ) , \quad \mathbf { s } _ { x } = ( \hat { J } _ { x } ^ { s } , \hat { M } _ { x } ^ { s } ) | R=\left(\begin{array}{ll}{{0}} &{{-1}} \\{{1}} &{{0}} \end{array} \right),\quad Q=\left(\begin{array}{ll}{{0}} &{{R}} \\{{R}} &{{0}} \end{array} \right),\quad \mathbf{s}_{t}=(\hat{\mathbf{J}}_{t}^{s},\hat{\mathbf{M}}_{t}^{s}),\quad \mathbf{s}_{x}=(\hat{J}_{x}^{s},\hat{M}_{x}^{s})
0 . 3 | 0.3
. . . | ...
\Omega | \Omega
\Delta = - ( - ) ^ { \epsilon _ { A } } \frac { \delta ^ { R } } { \delta \Phi ^ { A } } \frac { \delta ^ { R } } { \delta \Phi _ { A } ^ { * } } | \Delta=-(-)^{\epsilon_{A}} \frac{\delta^{R}}{\delta \Phi^{A}} \frac{\delta^{R}}{\delta \Phi_{A}^{*}}
\mathcal { L } _ { \mathrm { ~ d ~ } } | \mathcal{L}_{\mathrm{~d~}}
\bigstar | \bigstar
\operatorname { G a l } ( K _ { \infty } / K ) \simeq \mathbb { Z } _ { p } . | \operatorname{G a l}(K_{\infty}/K) \sim eq \mathbb{Z}_{p}.
Q _ { \gamma } \ = \ { \frac { 2 ^ { n + 3 } \Gamma ( { \frac { n } { 2 } } + 3 ) \Gamma ( { \frac { n } { 2 } } + 4 ) \zeta ( { \frac { n } { 2 } } + 3 ) \zeta ( { \frac { n } { 2 } } + 4 ) } { ( n + 4 ) \pi ^ { 2 } } } { \frac { T ^ { n + 7 } } { M _ { S } ^ { n + 2 } } } \ , | Q_{\gamma} \=\{\frac{2^{n+3} \Gamma({\frac{n}{2}}+3) \Gamma({\frac{n}{2}}+4) \zeta({\frac{n}{2}}+3) \zeta({\frac{n}{2}}+4)}{(n+4) \pi^{2}}}{\frac{T^{n+7}}{M_{S}^{n+2}}} \,
\operatorname* { s u p } _ { t \in [ 0 , T ] } \lVert \partial _ { x } \tilde { u } _ { n } \rVert _ { L ^ { q } ( \Omega ^ { * } ) } \leq C _ { 1 } ( \varphi _ { 0 } , g _ { 0 } , T , q ) , \ \forall q \in [ 1 , + \infty ) | \operatorname{s u p}_{t \in[0,T]} \lVert \partial_{x} \tilde{u}_{n} \rVert_{L^{q}(\Omega^{*})} \leq C_{1}(\varphi_{0},g_{0},T,q),\ \forall q \in[1,+\infty)
\frac { u _ { B } } { u _ { A } } = \, \frac { \left( 3 \ c _ { s 3 } c _ { s 1 } \rho _ { 3 } \rho _ { 1 } - c _ { s 3 } \rho _ { 2 } \rho _ { 3 } c _ { s 2 } + c _ { s 1 } \rho _ { 2 } \rho _ { 1 } c _ { s 2 } + \rho _ { 2 } ^ { 2 } c _ { s 2 } ^ { 2 } \right) } { ( c _ { s 3 } \rho _ { 3 } + \rho _ { 2 } c _ { s 2 } ) ( c _ { s 1 } \rho _ { 1 } + \rho _ { 2 } c _ { s 2 } ) } \ , | \frac{u_{B}}{u_{A}}=\,\frac{\left(3 \ c_{s 3} c_{s 1} \rho_{3} \rho_{1}-c_{s 3} \rho_{2} \rho_{3} c_{s 2}+c_{s 1} \rho_{2} \rho_{1} c_{s 2}+\rho_{2}^{2} c_{s 2}^{2} \right)}{(c_{s 3} \rho_{3}+\rho_{2} c_{s 2})(c_{s 1} \rho_{1}+\rho_{2} c_{s 2})} \,
\vec { c } | \vec{c}
\gamma | \gamma
\kappa _ { L } \equiv \kappa _ { 0 } + \kappa _ { 1 } + \kappa _ { 2 } | \kappa_{L} \equiv \kappa_{0}+\kappa_{1}+\kappa_{2}
\sigma \equiv \sigma _ { \mathrm { i } } \equiv \frac { B ^ { 2 } } { 4 \pi n _ { \mathrm { i } } m _ { \mathrm { i } } c ^ { 2 } } \qquad \beta \equiv \beta _ { \mathrm { i } } \equiv \frac { 8 \pi n _ { \mathrm { i } } k T _ { \mathrm { i } } } { B ^ { 2 } } \, , | \sigma \equiv \sigma_{\mathrm{i}} \equiv \frac{B^{2}}{4 \pi n_{\mathrm{i}} m_{\mathrm{i}} c^{2}} \qquad \beta \equiv \beta_{\mathrm{i}} \equiv \frac{8 \pi n_{\mathrm{i}} k T_{\mathrm{i}}}{B^{2}} \,,
\int _ { - \infty } ^ { x } d x ^ { \prime } \, \rho _ { \mathrm { p } } ( \eta ; x ^ { \prime } , t ) | \int_{-\infty}^{x} d x^{\prime} \,\rho_{\mathrm{p}}(\eta;x^{\prime},t)
\begin{array} { r } { H _ { 2 } = \epsilon _ { L } \sum _ { \nu = 1 } ^ { N _ { f } } a _ { L \nu } ^ { \dag } a _ { L \nu } + \epsilon _ { f } ^ { 0 } \sum _ { \nu = 1 } ^ { N _ { f } } a _ { f \nu } ^ { \dag } a _ { f \nu } + \epsilon _ { c } a _ { c } ^ { \dag } a _ { c } + \frac { V } { \sqrt { N _ { f } } } \sum _ { \nu = 1 } ^ { N _ { f } } ( a _ { L \nu } ^ { \dag } a _ { f \nu } + a _ { L \nu } a _ { f \nu } ^ { \dag } ) - U _ { f c } \sum _ { \nu = 1 } ^ { N _ { f } } a _ { f \nu } ^ { \dag } a _ { f \nu } ( 1 - a _ { c } ^ { \dag } a _ { c } ) , } \end{array} | \begin{array}{r}{{H_{2}=\epsilon_{L} \sum_{\nu=1}^{N_{f}} a_{L \nu}^{\dag} a_{L \nu}+\epsilon_{f}^{0} \sum_{\nu=1}^{N_{f}} a_{f \nu}^{\dag} a_{f \nu}+\epsilon_{c} a_{c}^{\dag} a_{c}+\frac{V}{\sqrt{N_{f}}} \sum_{\nu=1}^{N_{f}}(a_{L \nu}^{\dag} a_{f \nu}+a_{L \nu} a_{f \nu}^{\dag})-U_{f c} \sum_{\nu=1}^{N_{f}} a_{f \nu}^{\dag} a_{f \nu}(1-a_{c}^{\dag} a_{c}),}} \end{array}
1 5 | 1 5
F ( x , y , B ) | F(x,y,B)
\Psi _ { i n } ^ { * } = S \Psi _ { o u t } ^ { * } , \quad \Psi _ { o u t } = S \Psi _ { i n } | \Psi_{i n}^{*}=S \Psi_{o u t}^{*},\quad \Psi_{o u t}=S \Psi_{i n}
\langle j ( x _ { 1 } ) j _ { \nu } ( x _ { 2 } ) \rangle \ne 0 , | \langle j(x_{1}) j_{\nu}(x_{2}) \rangle \ne 0,
F ^ { 2 } \, \tilde { A } _ { \pi K } ^ { \mathrm { C D } } = \tilde { \Gamma } _ { \pi } ( 2 M _ { K } ^ { 2 } ) + \tilde { \Delta } _ { \pi K } ~ . | F^{2} \,\tilde{A}_{\pi K}^{\mathrm{CD}}=\tilde{\Gamma}_{\pi}(2 M_{K}^{2})+\tilde{\Delta}_{\pi K} ~.
\phi | \phi
H _ { 3 } P O _ { 4 } : H _ { 2 } O _ { 2 } : H _ { 2 } O | H_{3} P O_{4}:H_{2} O_{2}:H_{2} O
\mu | \mu
M | M
| F _ { p } ( z ) | \le 1 | | F_{p}(z) | \le 1
U \neq 0 | U \neq 0
T _ { y } | T_{y}
u _ { i } | u_{i}
Z Z Z Z | Z Z Z Z
\mathrm { k } _ { a } \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu } A _ { \sigma } ^ { a } = \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu } | \mathrm{k}_{a} \frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu} A_{\sigma}^{a}=\frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu}
( \Delta _ { \theta , j } ) _ { 1 , \nu } = \delta _ { \nu - 1 , j , \theta } = \frac { 1 } { N } \sum _ { k = 0 } ^ { N - 1 } ( - J _ { 0 , k } ) ^ { \theta - \nu } \frac { \operatorname* { d e t } ( M _ { \nu - 1 , k } ) } { \operatorname* { d e t } ( M _ { \theta , k } ) } \exp ( \mathrm { i } 2 \pi k j / N ) , \qquad \mathrm { f o r ~ } \nu = 1 , 2 , \ldots , \theta , | (\Delta_{\theta,j})_{1,\nu}=\delta_{\nu-1,j,\theta}=\frac{1}{N} \sum_{k=0}^{N-1}(-J_{0,k})^{\theta-\nu} \frac{\operatorname{d e t}(M_{\nu-1,k})}{\operatorname{d e t}(M_{\theta,k})} \mathrm{exp}(\mathrm{i} 2 \pi k j/N),\qquad \mathrm{for~} \nu=1,2,. . .,\theta,
\gamma _ { R } | \gamma_{R}
4 | 4
P = \sum _ { j = - \infty } ^ { \mathrm { f i n i t e } } a _ { j } ( x ) \partial ^ { j } | P=\sum_{j=-\infty}^{\mathrm{finite}} a_{j}(x) \partial^{j}
\begin{array} { r } { \dot { \bar { \mathbf { r } } } _ { 1 } = \frac { 5 } { 1 6 \pi \eta _ { \mathrm { s } } } \left[ \left( \mathbf { I } - \frac { 2 } { 5 } \, \mu \boldsymbol { \epsilon } \right) \cdot \left( \beta \mathbf { f } _ { 1 } + \ln \frac { \left| \mathbf { K } _ { 1 2 } ^ { - } \right| } { \left| \mathbf { K } _ { 1 2 } ^ { + } \right| } \mathbf { f } _ { 2 } \right) + \frac { 3 } { 5 } \left( \frac { \mathbf { K } _ { 1 2 } ^ { + } \mathbf { K } _ { 1 2 } ^ { + } } { \left| \mathbf { K } _ { 1 2 } ^ { + } \right| ^ { 2 } } - \frac { \mathbf { K } _ { 1 2 } ^ { - } \mathbf { K } _ { 1 2 } ^ { - } } { \left| \mathbf { K } _ { 1 2 } ^ { - } \right| ^ { 2 } } \right) \cdot \mathbf { f } _ { 2 } \right] + \mathbf { U } _ { 1 2 } \, , } \end{array} | \begin{array}{r}{{\dot{\bar{\mathbf{r}}}_{1}=\frac{5}{1 6 \pi \eta_{\mathrm{s}}} \left[\left(\mathbf{I}-\frac{2}{5} \,\mu \boldsymbol{\epsilon} \right) \cdot \left(\beta \mathbf{f}_{1}+\mathrm{ln} \frac{\left| \mathbf{K}_{1 2}^{-} \right|}{\left| \mathbf{K}_{1 2}^{+} \right|} \mathbf{f}_{2} \right)+\frac{3}{5} \left(\frac{\mathbf{K}_{1 2}^{+} \mathbf{K}_{1 2}^{+}}{\left| \mathbf{K}_{1 2}^{+} \right|^{2}}-\frac{\mathbf{K}_{1 2}^{-} \mathbf{K}_{1 2}^{-}}{\left| \mathbf{K}_{1 2}^{-} \right|^{2}} \right) \cdot \mathbf{f}_{2} \right]+\mathbf{U}_{1 2} \,,}} \end{array}
\mathcal { T } _ { 2 } \big [ \mathcal { T } _ { N } ^ { - 1 } [ f ] \big ] = f | \mathcal{T}_{2} \big[\mathcal{T}_{N}^{-1}[f] \big]=f
\displaystyle \frac { e _ { 2 } } { e _ { 1 } + 2 e _ { 2 } - 2 e _ { 1 } e _ { 2 } } | \displaystyle \frac{e_{2}}{e_{1}+2 e_{2}-2 e_{1} e_{2}}
F T | F T
V ( r , J ) \ \sim ( { \frac { J ( J + 1 ) } { r ^ { 2 } } } ) \ + \ C r ^ { ( 2 p - 1 ) } . | V(r,J) \ \sim({\frac{J(J+1)}{r^{2}}}) \+\ C r^{(2 p-1)}.
\begin{array} { r } { R _ { \widehat { L } _ { \mathrm { t s } } } ( \widehat { L } , t ; t _ { f } | \widehat { L } _ { 0 } ) = \int _ { - \infty } ^ { t } d t _ { 0 } \rho _ { \widehat { L } _ { \mathrm { t s } } } ( t _ { f } | \widehat { L } _ { 0 } , t _ { 0 } ) R _ { \widehat { L } _ { \mathrm { t s } } } ( \widehat { L } , t | \widehat { L } _ { 0 } , t _ { 0 } ; t _ { f } ) \ , } \end{array} | \begin{array}{r}{{R_{\widehat{L}_{\mathrm{ts}}}(\widehat{L},t;t_{f} | \widehat{L}_{0})=\int_{-\infty}^{t} d t_{0} \rho_{\widehat{L}_{\mathrm{ts}}}(t_{f} | \widehat{L}_{0},t_{0}) R_{\widehat{L}_{\mathrm{ts}}}(\widehat{L},t | \widehat{L}_{0},t_{0};t_{f}) \,}} \end{array}
f ( x ) = x _ { 0 } ^ { - 1 } \left( x / x _ { 0 } \right) ^ { - \alpha - 1 } | f(x)=x_{0}^{-1} \left(x/x_{0} \right)^{-\alpha-1}
P | P
\ell _ { m + N } | \ell_{m+N}
\zeta ( s ) = \sum _ { n = 1 } ^ { \infty } n ^ { - s } | \zeta(s)=\sum_{n=1}^{\infty} n^{-s}
Z [ e , \pi ] = \int [ D e _ { i } ^ { a } ] [ D \pi _ { c } ^ { j } ] \ J _ { 0 } \ d e t M _ { \alpha \beta } \, d e l t a ( H _ { \perp } ) \delta ( H _ { i } ) \delta ( J _ { a b } ) \ e x p \frac { i } { \hbar } S , | Z[e,\pi]=\int[D e_{i}^{a}][D \pi_{c}^{j}] \ J_{0} \ d e t M_{\alpha \beta} \,d e l t a(H_{\perp}) \delta(H_{i}) \delta(J_{a b}) \ e x p \frac{i}{\hbar} S,
a = \frac { 1 } { B _ { 0 } } , \; \; \; \; \; \; b = \frac { 1 } { B _ { 0 } } \left( - B _ { 1 } + \frac { 1 } { 2 } \right) \; . | a=\frac{1}{B_{0}},\;\;\;\;\;\;b=\frac{1}{B_{0}} \left(-B_{1}+\frac{1}{2} \right) \;.
D = \left( \begin{array} { l l l } { D _ { x x } } & { i D _ { x y } } & { D _ { x z } } \\ { - i D _ { x y } } & { D _ { y y } } & { i D _ { y z } } \\ { D _ { x z } } & { - i D _ { y z } } & { D _ { z z } } \end{array} \right) , | D=\left(\begin{array}{lll}{{D_{x x}}} &{{i D_{x y}}} &{{D_{x z}}} \\{{-i D_{x y}}} &{{D_{y y}}} &{{i D_{y z}}} \\{{D_{x z}}} &{{-i D_{y z}}} &{{D_{z z}}} \end{array} \right),
c _ { N V ^ { - } } \Gamma _ { N V ^ { - } } = c _ { N V ^ { 0 } } \Gamma _ { N V ^ { 0 } } | c_{N V^{-}} \Gamma_{N V^{-}}=c_{N V^{0}} \Gamma_{N V^{0}}
( x , y ) | (x,y)
\begin{array} { r l } { b = \, } & { \varsigma ( x _ { 2 } ) + \varsigma ( y _ { 1 } ) + \varsigma ( y _ { 2 } ) - \varsigma ( x _ { 1 } y _ { 1 } ) + \varsigma ( x _ { 2 } y _ { 1 } ) + \varsigma ( x _ { 1 } y _ { 2 } ) + \varsigma ( x _ { 2 } y _ { 2 } ) } \\ & { - \varsigma ( x _ { 1 } ) \varsigma ( y _ { 1 } ) - \varsigma ( x _ { 2 } ) \varsigma ( y _ { 1 } ) - \varsigma ( x _ { 2 } ) \varsigma ( y _ { 2 } ) - \varsigma ( x _ { 1 } ) ^ { 2 } - \varsigma ( y _ { 2 } ) ^ { 2 } \, . } \end{array} | \begin{array}{rl}{{b=\,}} &{{\varsigma(x_{2})+\varsigma(y_{1})+\varsigma(y_{2})-\varsigma(x_{1} y_{1})+\varsigma(x_{2} y_{1})+\varsigma(x_{1} y_{2})+\varsigma(x_{2} y_{2})}} \\ &{{-\varsigma(x_{1}) \varsigma(y_{1})-\varsigma(x_{2}) \varsigma(y_{1})-\varsigma(x_{2}) \varsigma(y_{2})-\varsigma(x_{1})^{2}-\varsigma(y_{2})^{2} \,.}} \end{array}
n | n
i | i
\begin{array} { r } { \tau _ { \Pi } = \frac { 2 p T ( D - 3 ) } { 3 D \zeta } , \quad \eta = \frac { 2 ( D - 3 ) } { 3 D } p \tau _ { \Pi } , \quad p = \frac { k _ { \textrm { B } } } { m } \rho T , \quad e = \frac { D } { 2 } \frac { k _ { \textrm { B } } } { m } T , \quad \zeta = \zeta ( \rho , T ) > 0 \ \ \textrm { f o r a n y } \ \ \{ \rho , T \} . } \end{array} | \begin{array}{r}{{\tau_{\Pi}=\frac{2 p T(D-3)}{3 D \zeta},\quad \eta=\frac{2(D-3)}{3 D} p \tau_{\Pi},\quad p=\frac{k_{\textrm{B}}}{m} \rho T,\quad e=\frac{D}{2} \frac{k_{\textrm{B}}}{m} T,\quad \zeta=\zeta(\rho,T)>0 \ \ \textrm{f o r a n y} \ \ \{\rho,T \}.}} \end{array}
\begin{array} { r l } { \mathscr { F } _ { \varepsilon } ( q _ { \varepsilon } ) } & { = \int _ { \Omega } W ^ { \varepsilon } [ q _ { \varepsilon } ] \, \mathrm { d } x , \qquad W ^ { \varepsilon } [ q _ { \varepsilon } ] = W _ { \mathrm { e l a s t } } ( \boldsymbol { F } , \psi ) + W _ { \mathrm { p h a s e } } ^ { \varepsilon } ( \psi , \boldsymbol { F } ^ { - T } \nabla \psi ) , } \end{array} | \begin{array}{rl}{{\mathscr{F}_{\varepsilon}(q_{\varepsilon})}} &{{=\int_{\Omega} W^{\varepsilon}[q_{\varepsilon}] \,\mathrm{d} x,\qquad W^{\varepsilon}[q_{\varepsilon}]=W_{\mathrm{elast}}(\boldsymbol{F},\psi)+W_{\mathrm{phase}}^{\varepsilon}(\psi,\boldsymbol{F}^{-T} \nabla \psi),}} \end{array}
< 5 | <5
a _ { k } | a_{k}
S _ { n n } = \exp \left( + \pi \sum _ { m \neq n } \frac { | { \cal G } _ { n m } | ^ { 2 } } { | b _ { n } - b _ { m } | } \right) . | S_{n n}=\mathrm{exp} \left(+\pi \sum_{m \neq n} \frac{|{\cal G}_{n m} |^{2}}{| b_{n}-b_{m} |} \right).
^ c | ^c
e = 1 | e=1
\cal { R } | \cal{R}
\tau _ { k } A _ { k i } ^ { t o t } | \tau_{k} A_{k i}^{t o t}
x , y | x,y
0 . 0 6 | 0.0 6
I _ { T } | I_{T}
Z = 1 3 5 | Z=1 3 5
\Omega | \Omega
6 . 5 \times 1 0 ^ { - 3 } | 6.5 \times 1 0^{-3}
\mathrm { E } | \mathrm{E}
m = n + 4 | m=n+4
\bar { \Theta } ( \mathrm { d o w n } ) \sim \left[ 9 . 0 \times 1 0 ^ { - 3 } \mathrm { I m } X _ { s d } ^ { I } + 6 . 7 \mathrm { I m } X _ { b d } ^ { I } + 2 . 6 \mathrm { I m } X _ { b s } ^ { I } \right] \times 1 0 ^ { - 7 } \ . | \bar{\Theta}(\mathrm{down}) \sim \left[9.0 \times 1 0^{-3} \mathrm{Im} X_{s d}^{I}+6.7 \mathrm{Im} X_{b d}^{I}+2.6 \mathrm{Im} X_{b s}^{I} \right] \times 1 0^{-7} \.
^ { 1 2 } | ^{1 2}
S + ( w ) | S+(w)
\begin{array} { r l } { \operatorname* { m i n } _ { \mathbf { X } } \quad } & { \operatorname { T r } ( \mathbf { X } ) } \\ { \mathrm { s . t . } \quad } & { \operatorname { T r } \left( \mathbf { A } _ { j } \mathbf { X } \right) = y _ { j } , } \\ & { \sum _ { r } \left( \sum _ { s } \left| \mathbf { X } _ { r s } \right| ^ { 2 } \right) ^ { \frac { 1 } { 2 } } < \eta , ~ \mathbf { X } \succeq 0 . } \end{array} | \begin{array}{rl}{{\mathrm{min}_{\mathbf{X}} \quad}} &{{\operatorname{T r}(\mathbf{X})}} \\{{\mathrm{s.t.} \quad}} &{{\operatorname{T r} \left(\mathbf{A}_{j} \mathbf{X} \right)=y_{j},}} \\ &{{\sum_{r} \left(\sum_{s} \left| \mathbf{X}_{r s} \right|^{2} \right)^{\frac{1}{2}}<\eta,~ \mathbf{X} \succ eq 0.}} \end{array}
\zeta ( x ) | \zeta(x)
\Delta _ { x } ^ { H C } / \Delta _ { 0 } | \Delta_{x}^{H C}/\Delta_{0}
\phi | \phi
\delta \omega | \delta \omega
\begin{array} { r } { { _ 2 F _ { 1 } } ( 1 + m , 1 + q ; 1 ; z ) = \sum _ { k = 0 } ^ { + \infty } \frac { \Gamma ( m + k + 1 ) ^ { 2 } ( - 1 ) ^ { m + q + 1 } } { \Gamma ( 1 + m ) \Gamma ( 1 + q ) \Gamma ( m - q + k + 1 ) } \frac { 1 } { k ! } \big ( \frac { 1 } { z } \big ) ^ { k + m + 1 } . } \end{array} | \begin{array}{r}{{{_2 F_{1}}(1+m,1+q;1;z)=\sum_{k=0}^{+\infty} \frac{\Gamma(m+k+1)^{2}(-1)^{m+q+1}}{\Gamma(1+m) \Gamma(1+q) \Gamma(m-q+k+1)} \frac{1}{k !} \big(\frac{1}{z} \big)^{k+m+1}.}} \end{array}
o | o
6 , 2 9 9 | 6,2 9 9
\varepsilon ^ { i k j } G _ { j m } \gamma _ { p } + \varepsilon ^ { i k j } E _ { j m } = 2 ( - \delta ^ { a i } \gamma _ { p } + 2 \varepsilon ^ { a i n } \Phi ^ { n } ) G _ { a b } \varepsilon ^ { b k m } + ( \delta ^ { a i } - 2 \gamma _ { p } \varepsilon ^ { a i n } \Phi ^ { n } ) ( \Gamma _ { a b m } - \gamma _ { p } S _ { a b m } ) ( \delta ^ { b k } - 2 \gamma _ { p } \varepsilon ^ { b k s } \Phi ^ { s } ) , | \varepsilon^{i k j} G_{j m} \gamma_{p}+\varepsilon^{i k j} E_{j m}=2(-\delta^{a i} \gamma_{p}+2 \varepsilon^{a i n} \Phi^{n}) G_{a b} \varepsilon^{b k m}+(\delta^{a i}-2 \gamma_{p} \varepsilon^{a i n} \Phi^{n})(\Gamma_{a b m}-\gamma_{p} S_{a b m})(\delta^{b k}-2 \gamma_{p} \varepsilon^{b k s} \Phi^{s}),
\mathrm { d } T = \frac { T } { c _ { p } } \mathrm { d } \eta - \frac { T } { c _ { p } } \frac { \partial \hat { \mu } } { \partial T } \mathrm { d } S + \Gamma \mathrm { d } p | \mathrm{d} T=\frac{T}{c_{p}} \mathrm{d} \eta-\frac{T}{c_{p}} \frac{\partial \hat{\mu}}{\partial T} \mathrm{d} S+\Gamma \mathrm{d} p
a ^ { - 2 } + b ^ { - 2 } = d ^ { - 2 } | a^{-2}+b^{-2}=d^{-2}
\psi _ { n } ( x , t ) | \psi_{n}(x,t)
\beta = 4 \beta _ { c } | \beta=4 \beta_{c}
\rho _ { \infty } = 1 1 . 4 7 | \rho_{\infty}=1 1.4 7
\left( \begin{array} { c } { E _ { x } } \\ { H _ { y } } \\ { E _ { y } } \\ { H _ { x } } \end{array} \right) _ { z = z _ { 0 } + d } = \mathbf M ( d ) \left( \begin{array} { c } { E _ { x } } \\ { H _ { y } } \\ { E _ { y } } \\ { H _ { x } } \end{array} \right) _ { z = z _ { 0 } } . | \left(\begin{array}{c}{{E_{x}}} \\{{H_{y}}} \\{{E_{y}}} \\{{H_{x}}} \end{array} \right)_{z=z_{0}+d}=\mathbf{M}(d) \left(\begin{array}{c}{{E_{x}}} \\{{H_{y}}} \\{{E_{y}}} \\{{H_{x}}} \end{array} \right)_{z=z_{0}}.
R = \lvert \rho \rvert - \frac { ( 1 - \rho ) \Omega _ { E } } { \omega _ { L , 1 3 1 } } ( \sin ^ { 2 } \beta + \cos ^ { 2 } \beta \cos \alpha ) . | R=\lvert \rho \rvert-\frac{(1-\rho) \Omega_{E}}{\omega_{L,1 3 1}}(\mathrm{sin}^{2} \beta+\mathrm{cos}^{2} \beta \mathrm{cos} \alpha).
A _ { 1 } ^ { 2 } + A _ { 2 } ^ { 2 } + A _ { 3 } ^ { 2 } = M ( x _ { 1 } ^ { 2 } ( 2 { \theta } ) + x _ { 2 } ^ { 2 } ( 2 { \theta } ) ) | A_{1}^{2}+A_{2}^{2}+A_{3}^{2}=M(x_{1}^{2}(2{\theta})+x_{2}^{2}(2{\theta}))
\begin{array} { r l } & { h ( t ) = - A _ { 1 } \frac { \pi ^ { \frac { d } { 2 } } t ^ { r } \Gamma ( 2 - \frac { r + d } { 2 } ) } { \Gamma ( 2 - \frac { r } { 2 } ) } { _ 2 F _ { 1 } } ( - \frac { r } { 2 } , 1 - \frac { r + d } { 2 } ; 2 - \frac { r } { 2 } ; \frac { 1 } { t ^ { 2 } } ) + \frac { t ^ { 2 } } { 2 \tau } } \\ & { = - \frac { \Gamma ( 2 - \frac { r } { 2 } ) } { \pi ^ { \frac { d } { 2 } } \Gamma ( 2 - \frac { r + d } { 2 } ) } \frac { \pi ^ { \frac { d } { 2 } } t ^ { r } \Gamma ( 2 - \frac { r + d } { 2 } ) } { \Gamma ( 2 - \frac { r } { 2 } ) } { _ 2 F _ { 1 } } ( - \frac { r } { 2 } , 1 - \frac { r + d } { 2 } ; 2 - \frac { r } { 2 } ; \frac { 1 } { t ^ { 2 } } ) + \frac { r \Gamma ( 2 - \frac { r } 2 ) \Gamma ( \frac { d + r } { 2 } ) t ^ { 2 } } { 2 \Gamma ( 1 + \frac { d } { 2 } ) } } \\ & { = - t ^ { r } { _ 2 F _ { 1 } } ( - \frac { r } { 2 } , 1 - \frac { r + d } { 2 } ; 2 - \frac { r } { 2 } ; \frac { 1 } { t ^ { 2 } } ) + \frac { r \Gamma ( 2 - \frac { r } 2 ) \Gamma ( \frac { d + r } { 2 } ) t ^ { 2 } } { 2 \Gamma ( 1 + \frac { d } { 2 } ) } . } \end{array} | \begin{array}{rl} &{{h(t)=-A_{1} \frac{\pi^{\frac{d}{2}} t^{r} \Gamma(2-\frac{r+d}{2})}{\Gamma(2-\frac{r}{2})}{_2 F_{1}}(-\frac{r}{2},1-\frac{r+d}{2};2-\frac{r}{2};\frac{1}{t^{2}})+\frac{t^{2}}{2 \tau}}} \\ &{{=-\frac{\Gamma(2-\frac{r}{2})}{\pi^{\frac{d}{2}} \Gamma(2-\frac{r+d}{2})} \frac{\pi^{\frac{d}{2}} t^{r} \Gamma(2-\frac{r+d}{2})}{\Gamma(2-\frac{r}{2})}{_2 F_{1}}(-\frac{r}{2},1-\frac{r+d}{2};2-\frac{r}{2};\frac{1}{t^{2}})+\frac{r \Gamma(2-\frac{r} 2) \Gamma(\frac{d+r}{2}) t^{2}}{2 \Gamma(1+\frac{d}{2})}}} \\ &{{=-t^{r}{_2 F_{1}}(-\frac{r}{2},1-\frac{r+d}{2};2-\frac{r}{2};\frac{1}{t^{2}})+\frac{r \Gamma(2-\frac{r} 2) \Gamma(\frac{d+r}{2}) t^{2}}{2 \Gamma(1+\frac{d}{2})}.}} \end{array}
^ { - 1 } | ^{-1}
n | n
5 ~ \mathrm { m d e g } | 5 ~ \mathrm{mdeg}
\begin{array} { r l } { \mathcal { G } \; : } & { \; \mathbb { Z } \times \mathbb { L } ^ { \infty } ( \mathcal { U } ) \longrightarrow \mathbb { L } ^ { \infty } ( \mathcal { U } ) } \\ & { ( z , u ) \mapsto ( \textbf { g } _ { 1 } ( z , u _ { 1 } ) , \textbf { g } _ { 2 } ( z , u _ { 2 } ) , \textbf { g } _ { 3 } ( z , u _ { 3 } ) ) , } \end{array} | \begin{array}{rl}{{\mathcal{G} \;:}} &{{\;\mathbb{Z} \times \mathbb{L}^{\infty}(\mathcal{U}) \longrightarrow \mathbb{L}^{\infty}(\mathcal{U})}} \\ &{{(z,u) \mapsto(\textbf{g}_{1}(z,u_{1}),\textbf{g}_{2}(z,u_{2}),\textbf{g}_{3}(z,u_{3})),}} \end{array}
0 . 0 5 | 0.0 5
\begin{array} { r l } { G _ { S } } & { { } = \frac { 1 } { 2 \alpha \rho \left| \chi _ { \mathrm { ~ r ~ f ~ l ~ } } \right| ^ { 2 } Y ^ { \mathrm { ~ b ~ g ~ } } S _ { \mathrm { ~ m ~ c ~ } } } \biggl [ 2 N _ { c } ^ { b } ( 1 - Y ^ { \mathrm { ~ b ~ g ~ } } ) + 2 N _ { r } ^ { b } \biggr . } \end{array} | \begin{array}{rl}{{G_{S}}} &{{=\frac{1}{2 \alpha \rho \left| \chi_{\mathrm{~r~f~l~}} \right|^{2} Y^{\mathrm{~b~g~}} S_{\mathrm{~m~c~}}} \biggl[2 N_{c}^{b}(1-Y^{\mathrm{~b~g~}})+2 N_{r}^{b} \biggr.}} \end{array}
t = 0 | t=0
\mathbf { b } | \mathbf{b}
M _ { + 1 } ^ { 2 } = \frac { \lambda _ { 7 } } { 2 } ( v _ { \eta } ^ { 2 } + v _ { \rho } ^ { 2 } ) - \frac { A } { \sqrt 2 } \left( \frac { 1 } { v _ { \rho } ^ { 2 } } + \frac { 1 } { v _ { \chi } ^ { 2 } } \right) , \; M _ { + 2 } ^ { 2 } = \frac { \lambda _ { 8 } } { 2 } ( v _ { \eta } ^ { 2 } + v _ { \chi } ^ { 2 } ) - \frac { A } { \sqrt 2 } \left( \frac { 1 } { v _ { \rho } ^ { 2 } } + \frac { 1 } { v _ { \chi } ^ { 2 } } \right) , \; | M_{+1}^{2}=\frac{\lambda_{7}}{2}(v_{\eta}^{2}+v_{\rho}^{2})-\frac{A}{\sqrt 2} \left(\frac{1}{v_{\rho}^{2}}+\frac{1}{v_{\chi}^{2}} \right),\;M_{+2}^{2}=\frac{\lambda_{8}}{2}(v_{\eta}^{2}+v_{\chi}^{2})-\frac{A}{\sqrt 2} \left(\frac{1}{v_{\rho}^{2}}+\frac{1}{v_{\chi}^{2}} \right),\;
- ( 1 / 2 ) \operatorname { R e } \int _ { V } \mathbf { J } ^ { * } \cdot \mathbf { E } | -(1/2) \operatorname{R e} \int_{V} \mathbf{J}^{*} \cdot \mathbf{E}
\langle a _ { n } ^ { 2 } \rangle = \frac { 2 k _ { B } T } { \gamma \pi ^ { 2 } } \frac { L _ { x } } { L _ { y } } \frac { 1 } { n ^ { 2 } } , | \langle a_{n}^{2} \rangle=\frac{2 k_{B} T}{\gamma \pi^{2}} \frac{L_{x}}{L_{y}} \frac{1}{n^{2}},
\begin{array} { r l } { \bigg | \sum _ { y _ { \alpha } \in B _ { 1 } \setminus B _ { r } } } & { \int _ { Q _ { \alpha } } ( J _ { p } ( \phi ( x + y ) - \phi ( x ) ) - J _ { p } ( \phi ( x + y _ { \alpha } ) - \phi ( x ) ) ) \frac { \, \mathrm { d } y } { | y | ^ { d + s p } } \bigg | } \\ & { \leq C h \sum _ { y _ { \alpha } \in B _ { 1 } \setminus B _ { r } } \int _ { Q _ { \alpha } } ( | \phi ( x + y ) - \phi ( x ) | ^ { p - 2 } + | \phi ( x + y _ { \alpha } ) - \phi ( x ) | ^ { p - 2 } ) \frac { \, \mathrm { d } y } { | y | ^ { d + s p } } } \\ & { \leq C h \int _ { B _ { 3 / 2 } \setminus B _ { r / 2 } } \ | y | ^ { p - 2 } \frac { \, \mathrm { d } y } { | y | ^ { d + s p } } \leq C h ( 1 + r ^ { p - 2 - s p } ) . } \end{array} | \begin{array}{rl}{{\bigg| \sum_{y_{\alpha} \in B_{1} \setminus B_{r}}}} &{{\int_{Q_{\alpha}}(J_{p}(\phi(x+y)-\phi(x))-J_{p}(\phi(x+y_{\alpha})-\phi(x))) \frac{\,\mathrm{d} y}{| y |^{d+s p}} \bigg|}} \\ &{{\leq C h \sum_{y_{\alpha} \in B_{1} \setminus B_{r}} \int_{Q_{\alpha}}(| \phi(x+y)-\phi(x) |^{p-2}+| \phi(x+y_{\alpha})-\phi(x) |^{p-2}) \frac{\,\mathrm{d} y}{| y |^{d+s p}}}} \\ &{{\leq C h \int_{B_{3/2} \setminus B_{r/2}} \ | y |^{p-2} \frac{\,\mathrm{d} y}{| y |^{d+s p}} \leq C h(1+r^{p-2-s p}).}} \end{array}
\begin{array} { r l } { \mathrm { F e } + \frac { 1 } { 2 } \mathrm { O } _ { 2 } } & { { } = \mathrm { F e O } , } \\ { \mathrm { F e O } + \frac { 1 } { 4 } \mathrm { O } _ { 2 } } & { { } = \mathrm { F e O } _ { 1 . 5 } . } \end{array} | \begin{array}{rl}{{\mathrm{Fe}+\frac{1}{2} \mathrm{O}_{2}}} &{{=\mathrm{FeO},}} \\{{\mathrm{FeO}+\frac{1}{4} \mathrm{O}_{2}}} &{{=\mathrm{FeO}_{1.5}.}} \end{array}
- { \frac { S _ { 1 } } { c ^ { 2 } | S | ^ { 2 } } } , { \frac { S _ { 2 } } { c ^ { 2 } | S | ^ { 2 } } } , c G | -{\frac{S_{1}}{c^{2} | S |^{2}}},{\frac{S_{2}}{c^{2} | S |^{2}}},c G
\| u \| _ { C ^ { 0 , \alpha } ( B _ { r } ) } \le \frac { C ( N , p , \mu , \alpha , \omega _ { A _ { \pm } , r ^ { * } } ) } { ( 1 - r ) ^ { \alpha } } \Bigg ( \| u \| _ { L ^ { \infty } ( B _ { 1 } ) } + \| f _ { + } \| _ { L ^ { N } ( B _ { 1 } ) } ^ { \frac { 1 } { p - 1 } } + \| f _ { - } \| _ { L ^ { N } ( B _ { 1 } ) } ^ { \frac { 1 } { p - 1 } } \Bigg ) | \| u \|_{C^{0,\alpha}(B_{r})} \le \frac{C(N,p,\mu,\alpha,\omega_{A_{\pm},r^{*}})}{(1-r)^{\alpha}} \Bigg(\| u \|_{L^{\infty}(B_{1})}+\| f_{+} \|_{L^{N}(B_{1})}^{\frac{1}{p-1}}+\| f_{-} \|_{L^{N}(B_{1})}^{\frac{1}{p-1}} \Bigg)
J _ { c o l l i s i o n } = { \frac { 1 } { 4 } } n { \bar { v } } = { \frac { n } { 4 } } { \sqrt { \frac { 8 k _ { B } T } { \pi m } } } . | J_{c o l l i s i o n}={\frac{1}{4}} n{\bar{v}}={\frac{n}{4}}{\sqrt{\frac{8 k_{B} T}{\pi m}}}.
\nabla _ { \mathbf { v } } \mathbf { u } | \nabla_{\mathbf{v}} \mathbf{u}
P \sim 1 | P \sim 1
0 . 6 | 0.6
\beta | \beta
\textrm { D a } _ { d } \gg 1 | \textrm{D a}_{d} \gg 1
\emptyset | \em ptyset
\begin{array} { r } { S _ { U U } ( f ) = \int _ { - \infty } ^ { \infty } R _ { U U } ( \tau ) e ^ { - 2 \pi i f \tau } d \tau = g _ { \mathrm { ~ e ~ l ~ } } ^ { 2 } \eta ^ { 2 } \langle \hat { S } _ { y , \mathrm { ~ i ~ n ~ } } \rangle ^ { 2 } \chi ^ { 2 } S _ { z z } ( f ) + \frac { 1 } { 2 } g _ { \mathrm { ~ e ~ l ~ } } ^ { 2 } \eta \cos ^ { 2 } \alpha \langle \hat { S } _ { 0 , \mathrm { ~ i ~ n ~ } } \rangle . } \end{array} | \begin{array}{r}{{S_{U U}(f)=\int_{-\infty}^{\infty} R_{U U}(\tau) e^{-2 \pi i f \tau} d \tau=g_{\mathrm{~e~l~}}^{2} \eta^{2} \langle \hat{S}_{y,\mathrm{~i~n~}} \rangle^{2} \chi^{2} S_{z z}(f)+\frac{1}{2} g_{\mathrm{~e~l~}}^{2} \eta \mathrm{cos}^{2} \alpha \langle \hat{S}_{0,\mathrm{~i~n~}} \rangle.}} \end{array}
\hat { \Gamma } = \left( \begin{array} { l l l l } { \gamma } & { 0 } & { 0 } & { 0 } \\ { 0 } & { \gamma } & { 0 } & { 0 } \\ { 0 } & { 0 } & { \gamma } & { 0 } \\ { 0 } & { 0 } & { 0 } & { \gamma + \Gamma } \end{array} \right) , | \hat{\Gamma}=\left(\begin{array}{llll}{{\gamma}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{\gamma}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{\gamma}} &{{0}} \\{{0}} &{{0}} &{{0}} &{{\gamma+\Gamma}} \end{array} \right),
\hat { f } _ { i j k } ^ { l , - 1 } = \hat { f } _ { i j k } ^ { l , 0 } ; \: \hat { f } _ { i j k } ^ { l , N _ { x } + 2 } = \hat { f } _ { i j k } ^ { l , N _ { x } + 1 } . | \hat{f}_{i j k}^{l,-1}=\hat{f}_{i j k}^{l,0};\:\hat{f}_{i j k}^{l,N_{x}+2}=\hat{f}_{i j k}^{l,N_{x}+1}.
\begin{array} { r l } { S _ { 3 } ^ { u } = } & { { \nu _ { \tau } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } \\ { \overline { { b ^ { r } } } { C _ { 1 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } & { \overline { { b ^ { g } } } { C _ { 2 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + \overline { { b ^ { b } } } { C _ { 3 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } \\ { t ^ { r } { C _ { 3 } ^ { \dagger } } { C _ { 2 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } & { t ^ { g } { C _ { 1 } ^ { \dagger } } { C _ { 3 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + t ^ { b } { C _ { 2 } ^ { \dagger } } { C _ { 1 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } + } \\ & { \tau ^ { + } { C _ { 3 } ^ { \dagger } } { C _ { 2 } ^ { \dagger } } { C _ { 1 } ^ { \dagger } } \omega _ { 3 } \omega _ { 3 } ^ { \dagger } } \end{array} \qquad \begin{array} { r l } { S _ { 3 } ^ { d } = } & { { \overline { { \nu } } _ { \tau } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } \\ { { b } ^ { r } { C _ { 1 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } & { { b } ^ { g } { C _ { 2 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + { b } ^ { b } { C _ { 3 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } \\ { \overline { { t ^ { r } } } { C _ { 3 } } { C _ { 2 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } & { \overline { { t ^ { g } } } { C _ { 1 } } { C _ { 3 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + \overline { { t ^ { b } } } { C _ { 2 } } { C _ { 1 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } + } \\ & { \tau ^ { - } { C _ { 3 } } { C _ { 2 } } { C _ { 1 } } \omega _ { 3 } ^ { \dagger } \omega _ { 3 } } \end{array} | \begin{array}{rl}{{S_{3}^{u}=}} &{{{\nu_{\tau}} \omega_{3} \omega_{3}^{\dagger}+}} \\{{\overline{{{b^{r}}}}{C_{1}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} &{{\overline{{{b^{g}}}}{C_{2}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+\overline{{{b^{b}}}}{C_{3}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} \\{{t^{r}{C_{3}^{\dagger}}{C_{2}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} &{{t^{g}{C_{1}^{\dagger}}{C_{3}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+t^{b}{C_{2}^{\dagger}}{C_{1}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}+}} \\ &{{\tau^{+}{C_{3}^{\dagger}}{C_{2}^{\dagger}}{C_{1}^{\dagger}} \omega_{3} \omega_{3}^{\dagger}}} \end{array} \qquad \begin{array}{rl}{{S_{3}^{d}=}} &{{{\overline{{{\nu}}}_{\tau}} \omega_{3}^{\dagger} \omega_{3}+}} \\{{{b}^{r}{C_{1}} \omega_{3}^{\dagger} \omega_{3}+}} &{{{b}^{g}{C_{2}} \omega_{3}^{\dagger} \omega_{3}+{b}^{b}{C_{3}} \omega_{3}^{\dagger} \omega_{3}+}} \\{{\overline{{{t^{r}}}}{C_{3}}{C_{2}} \omega_{3}^{\dagger} \omega_{3}+}} &{{\overline{{{t^{g}}}}{C_{1}}{C_{3}} \omega_{3}^{\dagger} \omega_{3}+\overline{{{t^{b}}}}{C_{2}}{C_{1}} \omega_{3}^{\dagger} \omega_{3}+}} \\ &{{\tau^{-}{C_{3}}{C_{2}}{C_{1}} \omega_{3}^{\dagger} \omega_{3}}} \end{array}
\widehat { \cal M } _ { \chi } \ \approx \ \mathrm { d i a g } \, \bigg ( \, m , \ \sqrt { m ^ { 2 } + \frac { 1 } { R ^ { 2 } } } \ , \ \sqrt { m ^ { 2 } + \frac { 1 } { R ^ { 2 } } } \ , \ \cdots , \ \sqrt { m ^ { 2 } + \frac { n ^ { 2 } } { R ^ { 2 } } } \ , \ \sqrt { m ^ { 2 } + \frac { n ^ { 2 } } { R ^ { 2 } } } \ , \ \cdots \, \bigg ) \, . | \widehat{\cal M}_{\chi} \ \approx \ \mathrm{diag} \,\bigg(\,m,\ \sqrt{m^{2}+\frac{1}{R^{2}}} \,\ \sqrt{m^{2}+\frac{1}{R^{2}}} \,\ . . .,\ \sqrt{m^{2}+\frac{n^{2}}{R^{2}}} \,\ \sqrt{m^{2}+\frac{n^{2}}{R^{2}}} \,\ . . . \,\bigg) \,.
\tilde { B } _ { \mu } ^ { ( j ; m ) } = \frac { 1 } { 1 - | u | ^ { 2 } } ( \partial _ { \mu } u P _ { m } ^ { ( j ) } + \partial _ { \mu } \bar { u } P _ { - m } ^ { ( j ) } ) | \tilde{B}_{\mu}^{(j;m)}=\frac{1}{1-| u |^{2}}(\partial_{\mu} u P_{m}^{(j)}+\partial_{\mu} \bar{u} P_{-m}^{(j)})
\boldsymbol { u } | \boldsymbol{u}
\sigma = 1 | \sigma=1
\varepsilon ^ { 2 } | \varepsilon^{2}
\mathbb { E } | \mathbb{E}
B _ { 0 } | B_{0}
\Phi ( x ) = \oint { \frac { d z } { z } } \exp \left( \sum _ { m \neq 0 } { \frac { i x _ { m } } { 2 Q m z ^ { m } } } \right) . | \Phi(x)=\oint{\frac{d z}{z}} \mathrm{exp} \left(\sum_{m \neq 0}{\frac{i x_{m}}{2 Q m z^{m}}} \right).
x z | x z
L | L
x = \pm 5 0 | x=\pm 5 0
\psi | \psi
\epsilon \to 0 | \epsilon \to 0
K _ { h } | K_{h}
G ( f ) _ { \beta } ^ { ( n ) } = \Sigma _ { m = 0 } ^ { n } \{ \tilde { \Theta } _ { \beta } ^ { ( n - m ) } , \tilde { f } ^ { ( m ) } \} _ { ( q ) } + \Sigma _ { m = 0 } ^ { ( n - 2 ) } \{ \tilde { \Theta } _ { \beta } ^ { ( n - m ) } , \tilde { f } ^ { ( m + 2 ) } \} _ { ( \phi ) } + \{ \tilde { \Theta } _ { \beta } ^ { ( n + 1 ) } , \tilde { f } ^ { ( 1 ) } \} _ { ( \phi ) } | G(f)_{\beta}^{(n)}=\Sigma_{m=0}^{n} \{\tilde{\Theta}_{\beta}^{(n-m)},\tilde{f}^{(m)} \}_{(q)}+\Sigma_{m=0}^{(n-2)} \{\tilde{\Theta}_{\beta}^{(n-m)},\tilde{f}^{(m+2)} \}_{(\phi)}+\{\tilde{\Theta}_{\beta}^{(n+1)},\tilde{f}^{(1)} \}_{(\phi)}
d | d
q _ { \mathrm { l } } ^ { * } = \frac { D q _ { \mathrm { t r } } ^ { * } } { \left( D - 1 \right) \phi + 1 } \, , | q_{\mathrm{l}}^{*}=\frac{D q_{\mathrm{tr}}^{*}}{\left(D-1 \right) \phi+1} \,,
\pi / 2 | \pi/2
f ( \epsilon ) \equiv [ 1 + ( \epsilon T _ { 2 } / 2 ) ^ { 2 } ] | f(\epsilon) \equiv[1+(\epsilon T_{2}/2)^{2}]
b _ { m _ { 1 } , m _ { 2 } } ^ { 2 } ( f _ { 1 } , f _ { 2 } ) | b_{m_{1},m_{2}}^{2}(f_{1},f_{2})
t = \mathrm { i } \sqrt { 1 - r ^ { 2 } } | t=\mathrm{i} \sqrt{1-r^{2}}
\tau = \operatorname* { m a x } \{ \tau _ { x } , \tau _ { y } \} | \tau=\mathrm{max} \{\tau_{x},\tau_{y} \}
N = 5 1 2 | N=5 1 2
Z _ { M } ( f ( \rho ) , g ( \lambda ) ) = Z _ { W } ( \lambda , \rho ) \; \; , | Z_{M}(f(\rho),g(\lambda))=Z_{W}(\lambda,\rho) \;\;,
\tau _ { i j } ^ { N } = \mu \dot { \gamma } _ { i j } – p \delta _ { i j } | \tau_{i j}^{N}=\mu \dot{\gamma}_{i j} – p \delta_{i j}
\hat { H } = \frac { h \nu _ { c } } { 2 } \left( \hat { X } ^ { 2 } + \hat { Y } ^ { 2 } \right) | \hat{H}=\frac{h \nu_{c}}{2} \left(\hat{X}^{2}+\hat{Y}^{2} \right)
w ^ { \mathrm { B L M } } ( \mu ) = \int _ { 0 } ^ { \infty } \; \frac { \mathrm { d } \lambda ^ { 2 } } { \lambda ^ { 2 } } \; W ( \lambda ^ { 2 } ) \, \phi ^ { \mathrm { B L M } } ( \lambda ^ { 2 } ; \mu ) \; \; . | w^{\mathrm{BLM}}(\mu)=\int_{0}^{\infty} \;\frac{\mathrm{d} \lambda^{2}}{\lambda^{2}} \;W(\lambda^{2}) \,\phi^{\mathrm{BLM}}(\lambda^{2};\mu) \;\;.
\xi \sim 3 . 5 - 4 | \xi \sim 3.5-4
^ \circ | ^\circ
C _ { 0 } | C_{0}
\frac { C _ { \alpha } } { D _ { \alpha } } h ^ { 2 } \sum _ { j _ { 1 } = 0 } ^ { N - 1 } \sum _ { j _ { 2 } = 0 } ^ { N - 1 } \sum _ { \mathbf { m } \in \mathbb { Z } ^ { 2 } } \left[ \frac { u _ { \epsilon } ( \mathbf { y } ) - u _ { \epsilon } ( \mathbf { x } ) } { | \mathbf { x } - ( \mathbf { y } + \mathbf { m } ) | ^ { 2 + 2 \alpha } } \right] + 1 = 0 \, . | \frac{C_{\alpha}}{D_{\alpha}} h^{2} \sum_{j_{1}=0}^{N-1} \sum_{j_{2}=0}^{N-1} \sum_{\mathbf{m} \in \mathbb{Z}^{2}} \left[\frac{u_{\epsilon}(\mathbf{y})-u_{\epsilon}(\mathbf{x})}{| \mathbf{x}-(\mathbf{y}+\mathbf{m}) |^{2+2 \alpha}} \right]+1=0 \,.
M = 1 8 | M=1 8
a _ { 1 } = - n _ { 1 } \, , \quad a _ { 2 } = - n _ { 2 } \, , \quad n _ { 1 } , \, n _ { 2 } = 0 , 1 , 2 , . . . \, . | a_{1}=-n_{1} \,,\quad a_{2}=-n_{2} \,,\quad n_{1},\,n_{2}=0,1,2,...\,.
1 0 ^ { 6 } \le R a \le 1 0 ^ { 9 } | 1 0^{6} \le R a \le 1 0^{9}
\sigma _ { 0 } ^ { n } ( \lambda ) = s ( \lambda - { \frac { ( - ) ^ { n } } { \alpha _ { 0 } } } ) = { \frac { 1 } { 2 \cosh \Bigl ( \pi ( \lambda - { \frac { ( - ) ^ { n } } { \alpha _ { 0 } } } ) \Bigr ) } } \, , | \sigma_{0}^{n}(\lambda)=s(\lambda-{\frac{(-)^{n}}{\alpha_{0}}})={\frac{1}{2 \mathrm{cosh} \Bigl(\pi(\lambda-{\frac{(-)^{n}}{\alpha_{0}}}) \Bigr)}} \,,
{ \tau _ { i j } } = \overline { { { u _ { i } } { u _ { j } } } } - { { \bar { u } } _ { i } } { { \bar { u } } _ { j } } | {\tau_{i j}}=\overline{{{{u_{i}}{u_{j}}}}}-{{\bar{u}}_{i}}{{\bar{u}}_{j}}
t | t
8 0 \times 8 0 \times 3 2 | 8 0 \times 8 0 \times 3 2
\ln \, P _ { \mathrm { { s } } } ^ { \mathrm { { s u b } } } = \ln \, P _ { \mathrm { { l } } } ^ { \mathrm { { s u b } } } - { \frac { \Delta _ { \mathrm { { f u s } } } H } { R } } \left( { \frac { 1 } { T _ { \mathrm { { s u b } } } } } - { \frac { 1 } { T _ { \mathrm { { f u s } } } } } \right) | \mathrm{ln} \,P_{\mathrm{{s}}}^{\mathrm{{sub}}}=\mathrm{ln} \,P_{\mathrm{{l}}}^{\mathrm{{sub}}}-{\frac{\Delta_{\mathrm{{fus}}} H}{R}} \left({\frac{1}{T_{\mathrm{{sub}}}}}-{\frac{1}{T_{\mathrm{{fus}}}}} \right)
N _ { \mathrm { ~ r ~ a ~ d ~ } } \approx \frac { \mu _ { 0 } \pi ^ { 2 } r _ { 1 } ^ { 2 } n ^ { 2 } } { 2 l c } \Big ( \frac { r _ { 2 } \omega _ { 0 } } { c _ { 3 } } \Big ) ^ { 2 } , | N_{\mathrm{~r~a~d~}} \approx \frac{\mu_{0} \pi^{2} r_{1}^{2} n^{2}}{2 l c} \Big(\frac{r_{2} \omega_{0}}{c_{3}} \Big)^{2},
\begin{array} { r l r } { \bigtriangleup \phi } & { { } = 0 } & { ( x , z ) \in \Omega } \\ { \partial _ { z } \phi } & { { } = 0 } & { z = - H } \\ { \partial _ { x } \phi } & { { } = 0 } & { ( x , z ) \in \Gamma } \\ { \partial _ { z } \phi } & { { } = \frac { \omega ^ { 2 } } { g } \phi } & { z = 0 } \\ { \left( \frac { \partial } { \partial | x | } - \mathrm { i } k _ { 0 } \right) ( \phi - \phi ^ { \mathrm { I n } } ) } & { { } \to 0 } & { \mathrm { ~ a ~ s ~ \ } x \to \infty } \\ { \sqrt { x ^ { 2 } + ( z + d ) ^ { 2 } } \| \nabla \phi \| } & { { } \to 0 } & { \mathrm { ~ a ~ s ~ \ } \sqrt { x ^ { 2 } + ( z + d ) ^ { 2 } } \to 0 , } \end{array} | \begin{array}{rlr}{{\bigtriangleup \phi}} &{{=0}} &{{(x,z) \in \Omega}} \\{{\partial_{z} \phi}} &{{=0}} &{{z=-H}} \\{{\partial_{x} \phi}} &{{=0}} &{{(x,z) \in \Gamma}} \\{{\partial_{z} \phi}} &{{=\frac{\omega^{2}}{g} \phi}} &{{z=0}} \\{{\left(\frac{\partial}{\partial | x |}-\mathrm{i} k_{0} \right)(\phi-\phi^{\mathrm{In}})}} &{{ \to 0}} &{{\mathrm{~a~s~\} x \to \infty}} \\{{\sqrt{x^{2}+(z+d)^{2}} \| \nabla \phi \|}} &{{ \to 0}} &{{\mathrm{~a~s~\} \sqrt{x^{2}+(z+d)^{2}} \to 0,}} \end{array}
A _ { \mathrm { J } } | A_{\mathrm{J}}
\begin{array} { r l } { \mathrm { R e a l } \left\{ r _ { k } ( y ) \right\} } & { = \frac { 2 } { D _ { k } ^ { 2 } } \left( C _ { 3 } ( y ) \cos \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \cosh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right. } \\ & { \quad \quad \quad \quad \quad \left. + C _ { 4 } ( y ) \sin \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \sinh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right) , } \\ { \mathrm { I m a g } \left\{ r _ { k } ( y ) \right\} } & { = \frac { 2 } { D _ { k } ^ { 2 } } \left( C _ { 4 } ( y ) \cos \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \cosh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right. } \\ & { \quad \quad \quad \quad \quad \left. - C _ { 4 } 3 y ) \sin \left( \frac { \ell } { 2 a } r _ { h } \sin \left( \frac { \theta _ { h } } { 2 } \right) \right) \sinh \left( \frac { \ell } { 2 a } r _ { h } \cos \left( \frac { \theta _ { h } } { 2 } \right) \right) \right) , } \\ { \mathrm { R e a l } \left\{ s _ { k } \right\} } & { = \frac { \ell ^ { 2 } r _ { \ell } } { D _ { k } ^ { 1 } } \left( C _ { k } ^ { 1 } \sin \left( \frac { h } { a } r _ { \ell } \sin \left( \frac { \theta _ { \ell } } { 2 } \right) \right) + C _ { k } ^ { 2 } \sinh \left( \frac { h } { a } r _ { \ell } \cos \left( \frac { \theta _ { \ell } } { 2 } \right) \right) \right) , } \\ { \mathrm { I m a g } \left\{ s _ { k } \right\} } & { = \frac { \ell ^ { 2 } r _ { \ell } } { D _ { k } ^ { 1 } } \left( C _ { k } ^ { 2 } \sin \left( \frac { h } { a } r _ { \ell } \sin \left( \frac { \theta _ { \ell } } { 2 } \right) \right) - C _ { k } ^ { 1 } \sinh \left( \frac { h } { a } r _ { \ell } \cos \left( \frac { \theta _ { \ell } } { 2 } \right) \right) \right) , } \end{array} | \begin{array}{rl}{{\mathrm{Real} \left\{r_{k}(y) \right\}}} &{{=\frac{2}{D_{k}^{2}} \left(C_{3}(y) \mathrm{cos} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{cosh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right.}} \\ &{{\quad \quad \quad \quad \quad \left.+C_{4}(y) \mathrm{sin} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{sinh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right),}} \\{{\mathrm{Imag} \left\{r_{k}(y) \right\}}} &{{=\frac{2}{D_{k}^{2}} \left(C_{4}(y) \mathrm{cos} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{cosh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right.}} \\ &{{\quad \quad \quad \quad \quad \left.-C_{4} 3 y) \mathrm{sin} \left(\frac{\ell}{2 a} r_{h} \mathrm{sin} \left(\frac{\theta_{h}}{2} \right) \right) \mathrm{sinh} \left(\frac{\ell}{2 a} r_{h} \mathrm{cos} \left(\frac{\theta_{h}}{2} \right) \right) \right),}} \\{{\mathrm{Real} \left\{s_{k} \right\}}} &{{=\frac{\ell^{2} r_{\ell}}{D_{k}^{1}} \left(C_{k}^{1} \mathrm{sin} \left(\frac{h}{a} r_{\ell} \mathrm{sin} \left(\frac{\theta_{\ell}}{2} \right) \right)+C_{k}^{2} \mathrm{sinh} \left(\frac{h}{a} r_{\ell} \mathrm{cos} \left(\frac{\theta_{\ell}}{2} \right) \right) \right),}} \\{{\mathrm{Imag} \left\{s_{k} \right\}}} &{{=\frac{\ell^{2} r_{\ell}}{D_{k}^{1}} \left(C_{k}^{2} \mathrm{sin} \left(\frac{h}{a} r_{\ell} \mathrm{sin} \left(\frac{\theta_{\ell}}{2} \right) \right)-C_{k}^{1} \mathrm{sinh} \left(\frac{h}{a} r_{\ell} \mathrm{cos} \left(\frac{\theta_{\ell}}{2} \right) \right) \right),}} \end{array}
\langle \mathbf { R } \cdot \mathbf { R } \rangle = 3 N b ^ { 2 } | \langle \mathbf{R} \cdot \mathbf{R} \rangle=3 N b^{2}
T < | T<
k _ { 2 } \! = \! \frac { k _ { \mathrm { o } } } { \beta _ { v } } ( 1 - \frac { k _ { \mathrm { o } } } { k _ { 1 } } ) | k_{2} \!=\! \frac{k_{\mathrm{o}}}{\beta_{v}}(1-\frac{k_{\mathrm{o}}}{k_{1}})
\| \cdot \| | \| \cdot \|
\begin{array} { r l } { \frac { 2 + \sqrt { b c } } { \sqrt { b c } + a } } & { = \frac { \sqrt { [ a ( b + c + b c ) + b c ] [ 4 a ( b + c + b c ) + ( b + c + b c - a ) ^ { 2 } ] } + \sqrt { b c } ( a + b + c + b c ) } { \left( \sqrt { b c } + a \right) ( a + b + c + b c ) } } \\ & { \ge \frac { 2 a ( b + c + b c ) + \sqrt { b c } ( b + c + b c - a ) + \sqrt { b c } ( a + b + c + b c ) } { \left( \sqrt { b c } + a \right) ( a + b + c + b c ) } } \\ & { = \frac { 2 ( b + c + b c ) } { a + b + c + b c } . } \end{array} | \begin{array}{rl}{{\frac{2+\sqrt{b c}}{\sqrt{b c}+a}}} &{{=\frac{\sqrt{[a(b+c+b c)+b c][4 a(b+c+b c)+(b+c+b c-a)^{2}]}+\sqrt{b c}(a+b+c+b c)}{\left(\sqrt{b c}+a \right)(a+b+c+b c)}}} \\ &{{\ge \frac{2 a(b+c+b c)+\sqrt{b c}(b+c+b c-a)+\sqrt{b c}(a+b+c+b c)}{\left(\sqrt{b c}+a \right)(a+b+c+b c)}}} \\ &{{=\frac{2(b+c+b c)}{a+b+c+b c}.}} \end{array}
W _ { 1 - 2 } = \int P d V , | W_{1-2}=\int P d V,
E \neq V | E \neq V
\langle \cdot \rangle _ { \mathrm { ~ d ~ a ~ t ~ a ~ } } | \langle \cdot \rangle_{\mathrm{~d~a~t~a~}}
\begin{array} { r l } { \hat { \rho } } & { { } = \otimes _ { \lambda } \, \hat { \rho } _ { \lambda } \, , } \\ { \hat { \rho } _ { \lambda } } & { { } = \frac { 1 } { \cosh \xi _ { \lambda } } \sum _ { n = 0 } ^ { \infty } \left( \operatorname { t a n h } \xi _ { \lambda } \right) ^ { 2 n } \left( \frac { ( 2 n ) ! } { 2 ^ { n } n ! } \right) ^ { 2 } \sum _ { k = 0 } ^ { n } \frac { \eta _ { \lambda } ^ { 2 ( 2 n - k ) } ( 1 - \eta _ { \lambda } ^ { 2 } ) ^ { k } } { k ! ( 2 n - k ) ! } | 2 n - k \rangle _ { \lambda } \langle 2 n - k | _ { \lambda } \, . } \end{array} | \begin{array}{rl}{{\hat{\rho}}} &{{=\otimes_{\lambda} \,\hat{\rho}_{\lambda} \,,}} \\{{\hat{\rho}_{\lambda}}} &{{=\frac{1}{\mathrm{cosh} \xi_{\lambda}} \sum_{n=0}^{\infty} \left(\mathrm{tanh} \xi_{\lambda} \right)^{2 n} \left(\frac{(2 n) !}{2^{n} n !} \right)^{2} \sum_{k=0}^{n} \frac{\eta_{\lambda}^{2(2 n-k)}(1-\eta_{\lambda}^{2})^{k}}{k !(2 n-k) !} | 2 n-k \rangle_{\lambda} \langle 2 n-k |_{\lambda} \,.}} \end{array}
1 6 | 1 6
\Sigma ^ { \mathrm { i n e l } } ( \varepsilon ) = \Sigma ^ { \mathrm { t o t a l } } ( \varepsilon ) - \Sigma ^ { \mathrm { t r } } ( \varepsilon ) | \Sigma^{\mathrm{inel}}(\varepsilon)=\Sigma^{\mathrm{total}}(\varepsilon)-\Sigma^{\mathrm{tr}}(\varepsilon)
0 . 6 | 0.6
N _ { A } | N_{A}
\mathcal { J _ { \mathrm { L T 1 } } } = \left( \begin{array} { l l l } { * * * } & { 0 } & { 0 } \\ { 0 } & { * * * } & { 0 } \\ { 0 } & { 0 } & { * * * } \end{array} \right) , \quad \mathcal { J _ { \mathrm { L T 2 } } } = \left( \begin{array} { l l l } { * * * } & { - 1 7 } & { 2 4 } \\ { - 1 6 } & { * * * } & { - 1 4 } \\ { 2 4 } & { - 1 4 } & { * * * } \end{array} \right) , | \mathcal{J_{\mathrm{LT1}}}=\left(\begin{array}{lll}{{***}} &{{0}} &{{0}} \\{{0}} &{{***}} &{{0}} \\{{0}} &{{0}} &{{***}} \end{array} \right),\quad \mathcal{J_{\mathrm{LT2}}}=\left(\begin{array}{lll}{{***}} &{{-1 7}} &{{2 4}} \\{{-1 6}} &{{***}} &{{-1 4}} \\{{2 4}} &{{-1 4}} &{{***}} \end{array} \right),
d s ^ { 2 } = e ^ { A } \left( - d t ^ { 2 } e ^ { 2 f } + d z ^ { 2 } \right) + e ^ { - A } \left( d r ^ { 2 } e ^ { - 2 f } + r ^ { 2 } d \Omega _ { 3 } ^ { 2 } \right) , | d s^{2}=e^{A} \left(-d t^{2} e^{2 f}+d z^{2} \right)+e^{-A} \left(d r^{2} e^{-2 f}+r^{2} d \Omega_{3}^{2} \right),
^ { \mathrm { ~ t ~ h ~ } } | ^{\mathrm{~t~h~}}
2 \mu _ { \mathrm { B } } | 2 \mu_{\mathrm{B}}
\begin{array} { r l } { \mathcal { U } _ { n , m } ^ { \epsilon } ( x , y , z ) \approx } & { \mathrm { i } ^ { m } \bigg ( \mathcal { U } _ { n , m } - \mathrm { i } \frac { \epsilon } { 2 } \Big ( A _ { n } \mathcal { U } _ { n + 2 , m } + B _ { n } \mathcal { U } _ { n - 2 , m } } \\ & { - A _ { m } \mathcal { U } _ { n , m + 2 } - B _ { m } \mathcal { U } _ { n , m - 2 } \Big ) \bigg ) } \end{array} | \begin{array}{rl}{{\mathcal{U}_{n,m}^{\epsilon}(x,y,z) \approx}} &{{\mathrm{i}^{m} \bigg(\mathcal{U}_{n,m}-\mathrm{i} \frac{\epsilon}{2} \Big(A_{n} \mathcal{U}_{n+2,m}+B_{n} \mathcal{U}_{n-2,m}}} \\ &{{-A_{m} \mathcal{U}_{n,m+2}-B_{m} \mathcal{U}_{n,m-2} \Big) \bigg)}} \end{array}
\begin{array} { r l } { E ^ { ( 5 , 0 ) } } & { { } = \langle \chi \lvert { \cal { E } } ^ { ( 5 , 0 ) } ( R ) \rvert \chi \rangle } \end{array} | \begin{array}{rl}{{E^{(5,0)}}} &{{=\langle \chi \lvert{\cal{E}}^{(5,0)}(R) \rvert \chi \rangle}} \end{array}
\pi / 2 | \pi/2
J | J
\overline { { u _ { i } ^ { \prime } u _ { j } ^ { \prime } } } | \overline{{{u_{i}^{\prime} u_{j}^{\prime}}}}
\Delta = - b _ { 2 } ^ { 2 } b _ { 8 } + 9 b _ { 2 } b _ { 4 } b _ { 6 } - 8 b _ { 4 } ^ { 3 } - 2 7 b _ { 6 } ^ { 2 } . | \Delta=-b_{2}^{2} b_{8}+9 b_{2} b_{4} b_{6}-8 b_{4}^{3}-2 7 b_{6}^{2}.
{ \bf r } _ { v } = ( X , 0 ) | {\bf r}_{v}=(X,0)
{ \sqrt [ [object Object] ] { | a _ { n } | } } \leq k < 1 , | {\sqrt[[o b j e c t O b j e c t]]{| a_{n} |}} \leq k<1,
\sum _ { T _ { L } } s t r ( T _ { L } ^ { a _ { 1 } } \ldots T _ { L } ^ { a _ { N } } ) - \sum _ { T _ { R } } s t r ( T _ { R } ^ { a _ { 1 } } \ldots T _ { R } ^ { a _ { N } } ) , | \sum_{T_{L}} s t r(T_{L}^{a_{1}} . . . T_{L}^{a_{N}})-\sum_{T_{R}} s t r(T_{R}^{a_{1}} . . . T_{R}^{a_{N}}),
\begin{array} { r l r } & { } & { P _ { C _ { k , m } \rightarrow C _ { k , m - 1 } } ( k , m ) = \sum _ { k _ { i } } P _ { C - D } ( k _ { i } ) \left( \frac { 1 } { N _ { D - C } ( k _ { i } ) } \right. } \\ & { } & { \left. \times \phi ( \pi _ { C - D } ( k _ { i } ) , \pi _ { C } ( k , m ) ) + \frac { N _ { D - C } ( k _ { i } ) - 1 } { N _ { D - C } ( k _ { i } ) } \right. } \\ & { } & { \left. \times \sum _ { k _ { j } } P _ { D - C } ( k _ { j } ) \phi ( \pi _ { C - D } ( k _ { i } ) , \pi _ { D - C } ( k _ { j } ) ) \right) . } \end{array} | \begin{array}{rlr} & &{{P_{C_{k,m} \rightarrow C_{k,m-1}}(k,m)=\sum_{k_{i}} P_{C-D}(k_{i}) \left(\frac{1}{N_{D-C}(k_{i})} \right.}} \\ & &{{\left.\times \phi(\pi_{C-D}(k_{i}),\pi_{C}(k,m))+\frac{N_{D-C}(k_{i})-1}{N_{D-C}(k_{i})} \right.}} \\ & &{{\left.\times \sum_{k_{j}} P_{D-C}(k_{j}) \phi(\pi_{C-D}(k_{i}),\pi_{D-C}(k_{j})) \right).}} \end{array}
R _ { 1 2 } d K _ { 1 } R _ { 2 1 } K _ { 2 } + R _ { 1 2 } K _ { 1 } R _ { 2 1 } d K _ { 2 } = d K _ { 2 } R _ { 1 2 } K _ { 1 } R _ { 2 1 } + K _ { 2 } R _ { 1 2 } d K _ { 1 } R _ { 2 1 } \quad . | R_{1 2} d K_{1} R_{2 1} K_{2}+R_{1 2} K_{1} R_{2 1} d K_{2}=d K_{2} R_{1 2} K_{1} R_{2 1}+K_{2} R_{1 2} d K_{1} R_{2 1} \quad.
\ell ( t ) \sim t ^ { \alpha } | \ell(t) \sim t^{\alpha}
\Omega \left( \mathbf { R } \right) = c P \left( \mathbf { R } \right) | \Omega \left(\mathbf{R} \right)=c P \left(\mathbf{R} \right)
{ \mathrm { B S _ { 1 } } } | {\mathrm{BS_{1}}}
G _ { B } ^ { C } ( \lambda ) \equiv \, i \, \langle C | [ Q , B ( 0 ) ] _ { \mathrm { E T } } | C \rangle _ { \lambda } \; , | G_{B}^{C}(\lambda) \equiv \,i \,\langle C |[Q,B(0)]_{\mathrm{ET}} | C \rangle_{\lambda} \;,
{ \mathcal P } , | {\mathcal{P},
\sim 1 | \sim 1
\mathrm { 2 s ^ { 2 } \, 2 p ^ { 2 } ( ^ { 3 } P ) \, 3 s ~ ^ { 2 } P _ { 1 / 2 } } | \mathrm{2s^{2} \,2 p^{2}(^{3} P) \,3 s ~^{2} P_{1/2}}
\alpha _ { T } | \alpha_{T}
^ { 8 5 } | ^{8 5}
{ ( 3 ) } | {(3)}
I ( \epsilon ) \sim e ^ { - b / \epsilon } \epsilon ^ { m } \sum _ { n = 0 } ^ { \infty } a _ { n } \epsilon ^ { n } \mathrm { ~ a ~ s ~ } \epsilon \to 0 , | I(\epsilon) \sim e^{-b/\epsilon} \epsilon^{m} \sum_{n=0}^{\infty} a_{n} \epsilon^{n} \mathrm{~a~s~} \epsilon \to 0,
\alpha | \alpha
x , y | x,y
\dot { \varphi } _ { a } ( k ) \mp \omega \epsilon _ { a b } \varphi _ { b } ( k ) = 0 | \dot{\varphi}_{a}(k) \mp \omega \epsilon_{a b} \varphi_{b}(k)=0
\langle . \rangle | \langle.\rangle
\left( \begin{array} { l l l l } { P _ { 1 1 } } & { P _ { 1 2 } } & { P _ { 1 3 } } & { P _ { 1 p } } \\ { P _ { 2 1 } } & { P _ { 2 2 } } & { P _ { 2 3 } } & { P _ { 2 p } } \\ { P _ { 3 1 } } & { P _ { 3 1 } } & { P _ { 3 2 } } & { P _ { 3 3 } } \\ { P _ { p 1 } } & { P _ { p 2 } } & { P _ { p 3 } } & { P _ { p p } } \end{array} \right) \left( \begin{array} { l } { \eta _ { 1 } } \\ { \eta _ { 2 } } \\ { \eta _ { 3 } } \\ { \eta _ { p } } \end{array} \right) = \left( \begin{array} { l } { 0 } \\ { 0 } \\ { 0 } \\ { 0 } \end{array} \right) | \left(\begin{array}{llll}{{P_{1 1}}} &{{P_{1 2}}} &{{P_{1 3}}} &{{P_{1 p}}} \\{{P_{2 1}}} &{{P_{2 2}}} &{{P_{2 3}}} &{{P_{2 p}}} \\{{P_{3 1}}} &{{P_{3 1}}} &{{P_{3 2}}} &{{P_{3 3}}} \\{{P_{p 1}}} &{{P_{p 2}}} &{{P_{p 3}}} &{{P_{p p}}} \end{array} \right) \left(\begin{array}{l}{{\eta_{1}}} \\{{\eta_{2}}} \\{{\eta_{3}}} \\{{\eta_{p}}} \end{array} \right)=\left(\begin{array}{l}{{0}} \\{{0}} \\{{0}} \\{{0}} \end{array} \right)
\ell \leq 5 | \ell \leq 5
\begin{array} { r l } { \langle \Delta t \rangle _ { l o n g } } & { \approx S _ { 1 } ( t _ { o n } , m = 0 ) S _ { 2 } ( t _ { e n d } , m = 0 ) \frac { 1 } { \alpha } \ln { \bigg ( 1 + \frac { m ( e ^ { \alpha \tau } - r ) } { f _ { e n d } } \bigg ) } \bigg ( 1 - \frac { \mu ( t _ { e n d } , m = 0 ) } { \beta } \ln { \bigg ( 1 + \frac { \beta } { \mu ( t _ { e n d } , m = 0 ) } \bigg ) } \bigg ) . } \end{array} | \begin{array}{rl}{{\langle \Delta t \rangle_{l o n g}}} &{{\approx S_{1}(t_{o n},m=0) S_{2}(t_{e n d},m=0) \frac{1}{\alpha} \mathrm{ln}{\bigg(1+\frac{m(e^{\alpha \tau}-r)}{f_{e n d}} \bigg)} \bigg(1-\frac{\mu(t_{e n d},m=0)}{\beta} \mathrm{ln}{\bigg(1+\frac{\beta}{\mu(t_{e n d},m=0)} \bigg)} \bigg).}} \end{array}
\theta | \theta
t _ { 1 } , \ldots , t _ { n - 1 } | t_{1},. . .,t_{n-1}
\begin{array} { r l r } { \delta \left< \hat { \mathcal X } \right> } & { { } = } & { \Omega _ { a } \frac { d } { d \Omega _ { a } } \left. \left< \hat { \mathcal X } \right> \right| _ { \Omega _ { a } = 0 } } \end{array} | \begin{array}{rlr}{{\delta \left<\hat{\mathcal{X} \right>}} &{{=}} &{{\Omega_{a} \frac{d}{d \Omega_{a}} \left.\left<\hat{\mathcal{X} \right>\right|_{\Omega_{a}=0}}} \end{array}
A ^ { 4 } = { \frac { 1 } { \lambda ^ { 2 } { R _ { A } } ^ { 2 } } } { \frac { ( R _ { A } - D ) ( D - R _ { A } + R _ { B } ) } { D ( D + R _ { B } ) } } \, , | A^{4}={\frac{1}{\lambda^{2}{R_{A}}^{2}}}{\frac{(R_{A}-D)(D-R_{A}+R_{B})}{D(D+R_{B})}} \,,
a _ { 2 } ( x ) = \frac { 1 - \alpha ^ { 2 } } { r ^ { 4 } } \left[ ( \alpha ^ { 2 } - 1 ) \left( \frac 1 { 9 0 } + 2 \left( \frac 1 6 - \xi \right) ^ { 2 } \right) + \frac 2 3 \left( \frac 1 5 - \xi \right) \alpha ^ { 2 } \right] + \frac { q ^ { 2 } } { 6 r ^ { 4 } } \ . | a_{2}(x)=\frac{1-\alpha^{2}}{r^{4}} \left[(\alpha^{2}-1) \left(\frac{1}{9 0}+2 \left(\frac{1}{6}-\xi \right)^{2} \right)+\frac{2}{3} \left(\frac{1}{5}-\xi \right) \alpha^{2} \right]+\frac{q^{2}}{6 r^{4}} \.
4 . 5 | 4.5
\xi ( t ) | \xi(t)
{ \frac { { \overline { { I A } } } \cdot { \overline { { I A } } } } { { \overline { { C A } } } \cdot { \overline { { A B } } } } } + { \frac { { \overline { { I B } } } \cdot { \overline { { I B } } } } { { \overline { { A B } } } \cdot { \overline { { B C } } } } } + { \frac { { \overline { { I C } } } \cdot { \overline { { I C } } } } { { \overline { { B C } } } \cdot { \overline { { C A } } } } } = 1 | {\frac{{\overline{{{I A}}}} \cdot{\overline{{{I A}}}}}{{\overline{{{C A}}}} \cdot{\overline{{{A B}}}}}}+{\frac{{\overline{{{I B}}}} \cdot{\overline{{{I B}}}}}{{\overline{{{A B}}}} \cdot{\overline{{{B C}}}}}}+{\frac{{\overline{{{I C}}}} \cdot{\overline{{{I C}}}}}{{\overline{{{B C}}}} \cdot{\overline{{{C A}}}}}}=1
- 5 7 . 2 | -5 7.2
S | S
V ^ { ( 4 ) } ( q _ { \mathrm { ~ r ~ e ~ f ~ } } ) _ { i j k l } | V^{(4)}(q_{\mathrm{~r~e~f~}})_{i j k l}
\nabla _ { s } | \nabla_{s}
\beta _ { r m } = R e ( \sqrt { n _ { m } ^ { 2 } \omega ^ { 2 } / c ^ { 2 } - h ^ { 2 } } ) | \beta_{r m}=R e(\sqrt{n_{m}^{2} \omega^{2}/c^{2}-h^{2}})
^ + | ^+
J = A ( x ) + i \theta ^ { \alpha } \lambda _ { \alpha } ( x ) + i \theta ^ { \alpha } \theta _ { \alpha } B ( x ) \; , | J=A(x)+i \theta^{\alpha} \lambda_{\alpha}(x)+i \theta^{\alpha} \theta_{\alpha} B(x) \;,
d s ^ { 2 } | _ { M 2 } = - 4 ( | y | ^ { 2 } + y _ { 3 } ^ { 2 } ) ( d x _ { + } ) ^ { 2 } - { \frac { 2 } { i } } ( W ^ { \prime } d ( e ^ { - 2 i x _ { + } } y ) - \overline { { { W ^ { \prime } } } } d ( e ^ { 2 i x _ { + } } \overline { { { y } } } ) ) d x _ { + } + d y d \overline { { { y } } } + ( d y _ { 3 } ) ^ { 2 } | d s^{2} |_{M 2}=-4(| y |^{2}+y_{3}^{2})(d x_{+})^{2}-{\frac{2}{i}}(W^{\prime} d(e^{-2 i x_{+}} y)-\overline{{{{W^{\prime}}}}} d(e^{2 i x_{+}} \overline{{{{y}}}})) d x_{+}+d y d \overline{{{{y}}}}+(d y_{3})^{2}
w _ { i } = \frac { \partial } { \partial z ^ { i } } \ . | w_{i}=\frac{\partial}{\partial z^{i}} \.
\alpha ^ { \prime } m ^ { 2 } \simeq 4 n - 7 \, H ^ { 2 } \alpha ^ { \prime } n ^ { 2 } + \cdots \; . | \alpha^{\prime} m^{2} \sim eq 4 n-7 \,H^{2} \alpha^{\prime} n^{2}+. . . \;.
\Sigma _ { \mathrm { m i n } } ^ { ( 2 ) } = \frac { k _ { \mathrm { B } } } { 2 } \ln \frac { T _ { \mathrm { R } } + T _ { f } } { T _ { \mathrm { R } } + T _ { i } } - \frac { k _ { \mathrm { B } } \ln \frac { T _ { \mathrm { R } } + T _ { f } } { T _ { \mathrm { R } } + T _ { i } } } { 2 \left( 1 + \frac { \tau } { 2 \Delta t } \ln \frac { T _ { \mathrm { R } } + T _ { f } } { T _ { \mathrm { R } } + T _ { i } } \right) } . | \Sigma_{\mathrm{min}}^{(2)}=\frac{k_{\mathrm{B}}}{2} \mathrm{ln} \frac{T_{\mathrm{R}}+T_{f}}{T_{\mathrm{R}}+T_{i}}-\frac{k_{\mathrm{B}} \mathrm{ln} \frac{T_{\mathrm{R}}+T_{f}}{T_{\mathrm{R}}+T_{i}}}{2 \left(1+\frac{\tau}{2 \Delta t} \mathrm{ln} \frac{T_{\mathrm{R}}+T_{f}}{T_{\mathrm{R}}+T_{i}} \right)}.
\ensuremath { f _ { \mathrm { G W } } } = 0 . 2 1 | \ensuremath{f_{\mathrm{GW}}}=0.2 1
S ^ { 2 } P \mapsto T M / P | S^{2} P \mapsto T M/P
\mathcal { M } _ { i } = 1 + \frac { \pi } { 4 } \chi _ { \rho } \chi _ { c } \chi _ { h } K \sin ^ { 2 } \theta _ { i } , ~ \mathcal { I } = 1 + \frac { 3 \pi } { 1 6 } \chi _ { \rho } \chi _ { h } K , ~ \widehat { \mathcal { M } } _ { i } = \frac { 3 \pi } { 8 } \frac { \chi _ { \rho } \chi _ { h } } { \chi _ { c } } K \sin \theta _ { i } , ~ \widehat { \mathcal { I } } _ { i } = \frac { \pi } { 8 } \chi _ { \rho } \chi _ { c } ^ { 2 } \chi _ { h } K \sin \theta _ { i } , | \mathcal{M}_{i}=1+\frac{\pi}{4} \chi_{\rho} \chi_{c} \chi_{h} K \mathrm{sin}^{2} \theta_{i},~ \mathcal{I}=1+\frac{3 \pi}{1 6} \chi_{\rho} \chi_{h} K,~ \widehat{\mathcal{M}}_{i}=\frac{3 \pi}{8} \frac{\chi_{\rho} \chi_{h}}{\chi_{c}} K \mathrm{sin} \theta_{i},~ \widehat{\mathcal{I}}_{i}=\frac{\pi}{8} \chi_{\rho} \chi_{c}^{2} \chi_{h} K \mathrm{sin} \theta_{i},
\begin{array} { r l } { X } & { = \frac { 1 } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } } \, , } \\ { Y _ { 0 } } & { = \frac { 2 p _ { 0 } \bar { d } _ { 0 } } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } \sum _ { i } \vert M _ { 0 , i } ( 0 ) \vert ^ { 2 } } } \\ { Y _ { 1 } } & { = \frac { 2 p _ { 1 } \bar { d } _ { 0 } } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } \sum _ { i } \vert M _ { 0 , i } ( 0 ) \vert ^ { 2 } } } \\ { Z _ { i } } & { = \frac { 4 d _ { 0 } p _ { i } \bar { \hat { e } } ^ { * } \hat { e } _ { i } } { \vert \hat { e } _ { \perp } { \cdot } \partial _ { \Delta \lambda } \Delta \hat { e } ( 0 ) \vert ^ { 2 } \sum _ { i } \vert M _ { 0 , i } ( 0 ) \vert ^ { 2 } } \, . } \end{array} | \begin{array}{rl}{{X}} &{{=\frac{1}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2}} \,,}} \\{{Y_{0}}} &{{=\frac{2 p_{0} \bar{d}_{0}}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2} \sum_{i} \vert M_{0,i}(0) \vert^{2}}}} \\{{Y_{1}}} &{{=\frac{2 p_{1} \bar{d}_{0}}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2} \sum_{i} \vert M_{0,i}(0) \vert^{2}}}} \\{{Z_{i}}} &{{=\frac{4 d_{0} p_{i} \bar{\hat{e}}^{*} \hat{e}_{i}}{\vert \hat{e}_{\perp}{\cdot} \partial_{\Delta \lambda} \Delta \hat{e}(0) \vert^{2} \sum_{i} \vert M_{0,i}(0) \vert^{2}} \,.}} \end{array}
k = 2 . 3 | k=2.3
\Delta x | \Delta x
H _ { e B H } | H_{e B H}
6 | 6
G ^ { ( n ) } | G^{(n)}
p _ { \parallel } ( v ) : = s _ { \infty } ( | v | ) | p_{\parallel}(v):=s_{\infty}(| v |)
\langle \Phi , H _ { B } \Phi \rangle \; \geq \; E _ { 0 } \Vert \Phi \Vert ^ { 2 } \ , | \langle \Phi,H_{B} \Phi \rangle \;\geq \;E_{0} \Vert \Phi \Vert^{2} \,
_ 2 | _2
\alpha < \pi / 2 | \alpha<\pi/2
\begin{array} { r l } { \left| \Phi \left( 2 \right) \right\rangle = } & { { } a \left| 0 \right\rangle \otimes \left| \textrm { A l i c e m e a s u r e d 0 } \right\rangle \otimes \left| \textrm { B o b r e c e i v e d 0 } \right\rangle + } \end{array} | \begin{array}{rl}{{\left| \Phi \left(2 \right) \right\rangle=}} &{{ a \left| 0 \right\rangle \otimes \left| \textrm{A l i c e m e a s u r e d 0} \right\rangle \otimes \left| \textrm{B o b r e c e i v e d 0} \right\rangle+}} \end{array}
\begin{array} { r l } { \mathcal { U } _ { n } ( x , z ) } & { = \left( \frac { 2 } { \pi } \right) ^ { 1 / 4 } \left( \frac { \exp ( \mathrm { i } ( 2 n + 1 ) \Psi ( z ) ) } { 2 ^ { n } n ! w ( z ) } \right) ^ { 1 / 2 } } \\ & { \times H _ { n } \left( \frac { \sqrt { 2 } x } { w ( z ) } \right) \exp \left( - \mathrm { i } \frac { k x ^ { 2 } } { 2 R _ { c } ( z ) } - \frac { x ^ { 2 } } { w ^ { 2 } ( z ) } \right) \, , } \end{array} | \begin{array}{rl}{{\mathcal{U}_{n}(x,z)}} &{{=\left(\frac{2}{\pi} \right)^{1/4} \left(\frac{\mathrm{exp}(\mathrm{i}(2 n+1) \Psi(z))}{2^{n} n ! w(z)} \right)^{1/2}}} \\ &{{\times H_{n} \left(\frac{\sqrt{2} x}{w(z)} \right) \mathrm{exp} \left(-\mathrm{i} \frac{k x^{2}}{2 R_{c}(z)}-\frac{x^{2}}{w^{2}(z)} \right) \,,}} \end{array}
\mathcal { E } _ { t } : = \sum _ { x \in \mathbb { T } _ { L } } P _ { \mathbf { a } } ( t , x ) ^ { 2 } , ~ ~ \mathcal { D } _ { t } = \sum _ { e \in E \left( \mathbb { T } _ { L } \right) } \mathbf { a } ( t , e ) ( \nabla P _ { \mathbf { a } } ( t , e ) ) ^ { 2 } ~ ~ \mathrm { a n d } ~ ~ \mathcal { N } _ { t } : = \sum _ { x \in \mathbb { T } _ { L } } | x | _ { * } ^ { p } P _ { \mathbf { a } } ( t , x ) ^ { 2 } , | \mathcal{E}_{t}:=\sum_{x \in \mathbb{T}_{L}} P_{\mathbf{a}}(t,x)^{2},~ ~ \mathcal{D}_{t}=\sum_{e \in E \left(\mathbb{T}_{L} \right)} \mathbf{a}(t,e)(\nabla P_{\mathbf{a}}(t,e))^{2} ~ ~ \mathrm{and} ~ ~ \mathcal{N}_{t}:=\sum_{x \in \mathbb{T}_{L}} | x |_{*}^{p} P_{\mathbf{a}}(t,x)^{2},
L ( \underline { { \theta } } , \underline { { a } } ) = p _ { 1 } W \left( \frac { a _ { 1 } } { \theta _ { 1 } } - 1 \right) + p _ { 2 } W \left( \frac { a _ { 2 } } { \theta _ { 2 } } - 1 \right) , \; \underline { { \theta } } = ( \theta _ { 1 } , \theta _ { 2 } ) \in \Theta _ { 0 } , \; \underline { { a } } = ( a _ { 1 } , a _ { 2 } ) \in \mathcal { A } = \Re _ { + + } ^ { 2 } , | L(\underline{{{\theta}}},\underline{{{a}}})=p_{1} W \left(\frac{a_{1}}{\theta_{1}}-1 \right)+p_{2} W \left(\frac{a_{2}}{\theta_{2}}-1 \right),\;\underline{{{\theta}}}=(\theta_{1},\theta_{2}) \in \Theta_{0},\;\underline{{{a}}}=(a_{1},a_{2}) \in \mathcal{A}=\Re_{++}^{2},
{ \begin{array} { r l } { \mathbf { B } = \mathbf { T } \times \mathbf { N } } & { = { \frac { 1 } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \left( b \sin { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { i } - b \cos { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { j } + a \mathbf { k } \right) } \\ { { \frac { d \mathbf { B } } { d s } } } & { = { \frac { 1 } { a ^ { 2 } + b ^ { 2 } } } \left( b \cos { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { i } + b \sin { \frac { s } { \sqrt { a ^ { 2 } + b ^ { 2 } } } } \mathbf { j } + 0 \mathbf { k } \right) } \end{array} } | {\begin{array}{rl}{{\mathbf{B}=\mathbf{T} \times \mathbf{N}}} &{{={\frac{1}{\sqrt{a^{2}+b^{2}}}} \left(b \mathrm{sin}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{i}-b \mathrm{cos}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{j}+a \mathbf{k} \right)}} \\{{{\frac{d \mathbf{B}}{d s}}}} &{{={\frac{1}{a^{2}+b^{2}}} \left(b \mathrm{cos}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{i}+b \mathrm{sin}{\frac{s}{\sqrt{a^{2}+b^{2}}}} \mathbf{j}+0 \mathbf{k} \right)}} \end{array}}
c = \lim \limits _ { k \rightarrow + \infty } \Delta ( k ) | c=\operatorname*{l i m}_{k \rightarrow+\infty} \Delta(k)
\frac { C B } { B F } = \frac { D B } { P C } | \frac{C B}{B F}=\frac{D B}{P C}
2 H _ { 2 } O \cong 2 H _ { 2 } \uparrow + O _ { 2 } \uparrow | 2 H_{2} O \cong 2 H_{2} \up arrow+O_{2} \up arrow
F _ { \star } = - ( \beta r _ { 0 } ) r _ { 0 } \cos 2 \theta _ { 0 } , | F_{\star}=-(\beta r_{0}) r_{0} \mathrm{cos} 2 \theta_{0},
\int _ { 0 } ^ { \infty } \! d q e ^ { - q } q ^ { \alpha } p _ { k _ { 1 } } ^ { \alpha } ( q ) p _ { k _ { 2 } } ^ { \alpha } ( q ) = \Gamma ( k _ { 1 } + 1 ) \Gamma ( k _ { 1 } + \alpha + 1 ) \delta _ { k _ { 1 } k _ { 2 } } | \int_{0}^{\infty} \! d q e^{-q} q^{\alpha} p_{k_{1}}^{\alpha}(q) p_{k_{2}}^{\alpha}(q)=\Gamma(k_{1}+1) \Gamma(k_{1}+\alpha+1) \delta_{k_{1} k_{2}}
\int { \cal D } A { \cal D } [ \Lambda ] { \cal D } \lambda \quad e ^ { F ( A , \psi , \overline { \psi } , \phi ) } e ^ { ( A , d \lambda + i \ast d \Lambda ) } = \int { \cal D } A _ { h } e ^ { F ( A _ { h } , \psi , \overline { \psi } , \phi ) } . | \int{\cal D} A{\cal D}[\Lambda]{\cal D} \lambda \quad e^{F(A,\psi,\overline{{\psi}},\phi)} e^{(A,d \lambda+i \ast d \Lambda)}=\int{\cal D} A_{h} e^{F(A_{h},\psi,\overline{{\psi}},\phi)}.
{ \frac { d \rho } { d t } } + 3 ( \rho + p ) { \frac { d a } { d t } } / a = 0 . | {\frac{d \rho}{d t}}+3(\rho+p){\frac{d a}{d t}}/a=0.
{ \frac { S O ( 1 , 5 ) } { S O ( 5 ) } } \times { \frac { S O ( 6 ) } { S O ( 6 ) } } | {\frac{S O(1,5)}{S O(5)}} \times{\frac{S O(6)}{S O(6)}}
z \left( X _ { i _ { 1 } } \right) = \left\{ X _ { i _ { 1 } , 1 } , \ldots , X _ { i _ { 1 } , m _ { 2 } \left( i _ { 1 } \right) } \right\} \quad . | z \left(X_{i_{1}} \right)=\left\{X_{i_{1},1},. . .,X_{i_{1},m_{2} \left(i_{1} \right)} \right\} \quad.
- \hat { h } _ { \mu \nu } ^ { \prime \prime } + { \frac { ( a ^ { 3 / 2 } ) ^ { \prime \prime } } { a ^ { 3 / 2 } } } \hat { h } _ { \mu \nu } = { \frac { m ^ { 2 } } { H ^ { 2 } } } \hat { h } _ { \mu \nu } , | -\hat{h}_{\mu \nu}^{\prime \prime}+{\frac{(a^{3/2})^{\prime \prime}}{a^{3/2}}} \hat{h}_{\mu \nu}={\frac{m^{2}}{H^{2}}} \hat{h}_{\mu \nu},
{ \frac { e } { \pi } } J _ { \mu } = i \epsilon _ { \mu \nu \lambda } \partial _ { \nu } ( V ^ { \ast } \partial _ { \lambda } V ) | {\frac{e}{\pi}} J_{\mu}=i \epsilon_{\mu \nu \lambda} \partial_{\nu}(V^{\ast} \partial_{\lambda} V)
\vec { x } = \vec { z } _ { i } ( t ) \, | \vec{x}=\vec{z}_{i}(t) \,
s l ( 3 | 2 ) \sim s \widetilde { { \cal H } } / \{ B _ { n } ^ { ( 2 ) } , V _ { r } ^ { ( 5 / 2 ) } , \bar { V } _ { r } ^ { ( 5 / 2 ) } , \ldots \} . | s l(3 | 2) \sim s \widetilde{{\cal H}}/\{B_{n}^{(2)},V_{r}^{(5/2)},\bar{V}_{r}^{(5/2)},. . . \}.
d \tilde { \tau } = { \frac { d \tilde { t } } { \prod _ { k } e ^ { \tilde { \alpha } _ { k } } } } | d \tilde{\tau}={\frac{d \tilde{t}}{\prod_{k} e^{\tilde{\alpha}_{k}}}}
{ \cal L } ( \Psi _ { Q } , \Psi _ { \bar { Q } } , \lambda _ { N _ { c } } ) + \bar { { \cal L } } ( T ) \sim { \cal L } ( \Psi _ { q } , \Psi _ { \bar { q } } , \lambda _ { \tilde { N } _ { c } } ) | {\cal L}(\Psi_{Q},\Psi_{\bar{Q}},\lambda_{N_{c}})+\bar{{\cal L}}(T) \sim{\cal L}(\Psi_{q},\Psi_{\bar{q}},\lambda_{\tilde{N}_{c}})
S = \int _ { M ^ { 2 } } ^ { } U ( \rho , q ^ { 2 } ) { \frac { 1 } { 2 } } e ^ { a } \varepsilon _ { a b } \wedge e ^ { b } | S=\int_{M^{2}}^ U(\rho,q^{2}){\frac{1}{2}} e^{a} \varepsilon_{a b} \wedge e^{b}
w = \frac { N _ { 0 } + 6 N _ { 1 / 2 } + 1 2 N _ { 1 } } { 1 2 0 \cdot ( 4 \pi ) ^ { 2 } } , b = - \, | w=\frac{N_{0}+6 N_{1/2}+1 2 N_{1}}{1 2 0 \cdot(4 \pi)^{2}},b=-\,
{ \cal L } _ { \mathrm { Y M } } = - \frac { 1 } { 4 \Omega _ { D - 1 } \alpha } \mathrm { t r } | {\cal L}_{\mathrm{YM}}=-\frac{1}{4 \Omega_{D-1} \alpha} \mathrm{tr}
\hat { K } _ { \hat { a } \hat { b } \hat { c } } = { \textstyle \frac { 1 } { 2 } } \left( \hat { T } _ { \hat { a } \hat { c } \hat { b } } + \hat { T } _ { \hat { b } \hat { c } \hat { a } } - \hat { T } _ { \hat { a } \hat { b } \hat { c } } \right) , | \hat{K}_{\hat{a} \hat{b} \hat{c}}={\textstyle \frac{1}{2}} \left(\hat{T}_{\hat{a} \hat{c} \hat{b}}+\hat{T}_{\hat{b} \hat{c} \hat{a}}-\hat{T}_{\hat{a} \hat{b} \hat{c}} \right),
\varphi _ { A _ { 1 } . . . A _ { n } a _ { 1 } . . . a _ { m } } ( y ) = \int _ { { \cal F } \times S ^ { 2 } } \omega ( p , \zeta ) \ \widetilde { \varphi } ( p , \zeta ) \Phi ( p , \zeta | y ) _ { A _ { 1 } . . . A _ { n } a _ { 1 } . . . a _ { m } } \ \in { \cal H } _ { E _ { o } , s } | \varphi_{A_{1}...A_{n} a_{1}...a_{m}}(y)=\int_{{\cal F} \times S^{2}} \omega(p,\zeta) \ \widetilde{\varphi}(p,\zeta) \Phi(p,\zeta | y)_{A_{1}...A_{n} a_{1}...a_{m}} \ \in{\cal H}_{E_{o},s}
\frac { \gamma _ { n _ { 1 } } } { d _ { \beta = [ R _ { 0 } ] } } = \frac { 2 \cdot 4 ^ { n _ { 1 } } { \cal F } _ { n _ { 1 } } } { \Gamma ( 4 n _ { 1 } - 2 ) } . | \frac{\gamma_{n_{1}}}{d_{\beta=[R_{0}]}}=\frac{2 \cdot 4^{n_{1}}{\cal F}_{n_{1}}}{\Gamma(4 n_{1}-2)}.
\vec { b } _ { V V } ^ { \prime } = R _ { x } ( \phi ) R _ { z } ( v ) \hat { x } . | \vec{b}_{V V}^{\prime}=R_{x}(\phi) R_{z}(v) \hat{x}.
| V _ { B } \rangle = \mathrm { e x p } \left( \frac { 1 } { 2 } \sum _ { r , s = 1 } ^ { 3 } \sum _ { m , n = - \infty } ^ { \infty } \sum _ { I = 1 } ^ { 8 } a _ { r m } ^ { I \dagger } \overline { { N } } _ { m n } ^ { r s } a _ { s n } ^ { I \dagger } \right) | 0 \rangle . | | V_{B} \rangle=\mathrm{exp} \left(\frac{1}{2} \sum_{r,s=1}^{3} \sum_{m,n=-\infty}^{\infty} \sum_{I=1}^{8} a_{r m}^{I \dagger} \overline{{{N}}}_{m n}^{r s} a_{s n}^{I \dagger} \right) | 0 \rangle.
| \psi ( \alpha ) \rangle = e ^ { - \alpha ^ { 2 } / 2 } \sum _ { n = 0 } ^ { \infty } { \frac { \alpha ^ { n } } { \sqrt { n ! } } } | n \rangle . | | \psi(\alpha) \rangle=e^{-\alpha^{2}/2} \sum_{n=0}^{\infty}{\frac{\alpha^{n}}{\sqrt{n !}}} | n \rangle.
\tilde { \Pi } _ { a b } \equiv \frac { 2 } { \sqrt { - \gamma } } \frac { \delta S _ { c t } } { \delta \gamma ^ { a b } } | \tilde{\Pi}_{a b} \equiv \frac{2}{\sqrt{-\gamma}} \frac{\delta S_{c t}}{\delta \gamma^{a b}}
\partial ( h ^ { - 1 } \overline { { \partial } } h \wedge \omega ) = 0 | \partial(h^{-1} \overline{{{\partial}}} h \wedge \omega)=0
\phi _ { L } ^ { a b } = \frac 1 2 ( \phi ^ { a b } + \frac 1 2 \epsilon ^ { a b c d } \phi ^ { c d } ) \quad \quad \phi _ { R } ^ { a b } = \frac 1 2 ( \phi ^ { a b } - \frac 1 2 \epsilon ^ { a b c d } \phi ^ { c d } ) | \phi_{L}^{a b}=\frac{1}{2}(\phi^{a b}+\frac{1}{2} \epsilon^{a b c d} \phi^{c d}) \quad \quad \phi_{R}^{a b}=\frac{1}{2}(\phi^{a b}-\frac{1}{2} \epsilon^{a b c d} \phi^{c d})
{ \cal A } = { \frac { 3 } { 2 } } \sqrt { \pi } \lambda \int d t { \frac { c } { a ^ { i } a ^ { i } } } . | {\cal A}={\frac{3}{2}} \sqrt{\pi} \lambda \int d t{\frac{c}{a^{i} a^{i}}}.
\int _ { \sum D _ { k } } { \bf n } d a = 0 , | \int_{\sum D_{k}}{\bf n} d a=0,
\{ D _ { F } ( x _ { 1 } - x _ { 4 } ) , D _ { F } ( x _ { 2 } - x _ { 4 } ) , D _ { F } ( x _ { 3 } - x _ { 4 } ) \} _ { \star } , \mathrm { f o r } \theta _ { 0 i } = 0 , | \{D_{F}(x_{1}-x_{4}),D_{F}(x_{2}-x_{4}),D_{F}(x_{3}-x_{4}) \}_{\star},\mathrm{for} \theta_{0 i}=0,
\mathrm { \ k a p p a } \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu } A _ { \sigma } = \frac { 1 } { 2 \pi } \varepsilon ^ { 0 \nu \sigma } \partial _ { \nu } | \mathrm{\kappa} \frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu} A_{\sigma}=\frac{1}{2 \pi} \varepsilon^{0 \nu \sigma} \partial_{\nu}
\tilde { L _ { 1 } } ( t ) ( \alpha ^ { * } , \alpha ) = \frac { g ^ { 2 } } { 2 } \left( \frac { 1 } { 3 ! } V _ { a b c } W ^ { a b c } + \alpha _ { a } ^ { * } ( V _ { c d } ^ { a } W _ { b } ^ { c d } + V _ { b c d } W ^ { c d a } ) \alpha ^ { b } \right) | \tilde{L_{1}}(t)(\alpha^{*},\alpha)=\frac{g^{2}}{2} \left(\frac{1}{3 !} V_{a b c} W^{a b c}+\alpha_{a}^{*}(V_{c d}^{a} W_{b}^{c d}+V_{b c d} W^{c d a}) \alpha^{b} \right)
{ \cal A } _ { e h } ( \tau ) = ( 1 - e ^ { - \tau } ) { \cal A } _ { d S } , | {\cal A}_{e h}(\tau)=(1-e^{-\tau}){\cal A}_{d S},
\left( \begin{matrix} { a ^ { \prime \prime } } & { b ^ { \prime \prime } } \\ { c ^ { \prime \prime } } & { d ^ { \prime \prime } } \\ \end{matrix} \right) = \left( \begin{matrix} { a } & { b } \\ { c } & { d } \\ \end{matrix} \right) + \left( \begin{matrix} { a ^ { \prime } } & { b ^ { \prime } } \\ { c ^ { \prime } } & { d ^ { \prime } } \\ \end{matrix} \right) | \left(\begin{matrix}{{a^{\prime \prime}}} &{{b^{\prime \prime}}} \\{{c^{\prime \prime}}} &{{d^{\prime \prime}}} \\ \end{matrix} \right)=\left(\begin{matrix}{{a}} &{{b}} \\{{c}} &{{d}} \\ \end{matrix} \right)+\left(\begin{matrix}{{a^{\prime}}} &{{b^{\prime}}} \\{{c^{\prime}}} &{{d^{\prime}}} \\ \end{matrix} \right)
m _ { 1 } \ldots m _ { n } ( a _ { 1 } b _ { 1 } ^ { - 1 } a _ { 1 } ^ { - 1 } b _ { 1 } ) \ldots ( a _ { g } b _ { g } ^ { - 1 } a _ { g } ^ { - 1 } b _ { g } ) = i d . | m_{1} . . . m_{n}(a_{1} b_{1}^{-1} a_{1}^{-1} b_{1}) . . .(a_{g} b_{g}^{-1} a_{g}^{-1} b_{g})=i d.
\eta = 4 \alpha [ - m _ { 1 } + m _ { 2 } + m _ { 3 } + s _ { 1 } + s _ { 2 } + s _ { 3 } + s _ { 4 } + t - 2 N ( r _ { 2 } + 2 s _ { 3 } + s _ { 4 } ) ] . | \eta=4 \alpha[-m_{1}+m_{2}+m_{3}+s_{1}+s_{2}+s_{3}+s_{4}+t-2 N(r_{2}+2 s_{3}+s_{4})].
\big \langle { \cal V } _ { h _ { \mu \nu } } \big \rangle _ { \mathrm { d i s k } _ { p } } = \langle h _ { \mu \nu } | \mathrm { D } p \rangle , | \big\langle{\cal V}_{h_{\mu \nu}} \big\rangle_{\mathrm{disk}_{p}}=\langle h_{\mu \nu} | \mathrm{D} p \rangle,
[ D _ { i } , \hat { F } _ { i k } ] = - [ D _ { 0 } , \hat { F } _ { 0 k } ] | [D_{i},\hat{F}_{i k}]=-[D_{0},\hat{F}_{0 k}]
\gamma _ { S _ { 1 } } ^ { 2 } = \gamma _ { S _ { 2 } } ^ { 2 } = \gamma _ { S _ { 3 } } ^ { 2 } . | \gamma_{S_{1}}^{2}=\gamma_{S_{2}}^{2}=\gamma_{S_{3}}^{2}.
\left( \begin{array} { c } { F } \\ { * F } \\ \end{array} \right) \rightarrow \left( \begin{array} { l l } { \cos \alpha } & { \sin \alpha } \\ { - \sin \alpha } & { \cos \alpha } \\ \end{array} \right) \left( \begin{array} { c } { F } \\ { * F } \\ \end{array} \right) . | \left(\begin{array}{c}{{F}} \\{{*F}} \end{array} \right) \rightarrow \left(\begin{array}{ll}{{\mathrm{cos} \alpha}} &{{\mathrm{sin} \alpha}} \\{{-\mathrm{sin} \alpha}} &{{\mathrm{cos} \alpha}} \end{array} \right) \left(\begin{array}{c}{{F}} \\{{*F}} \end{array} \right).
L _ { n } = \sum _ { m } \frac 1 2 \alpha _ { n - m } \alpha _ { m } , \tilde { L } _ { n } = \sum _ { m } \frac 1 2 \tilde { \alpha } _ { n - m } \tilde { \alpha } _ { m } , | L_{n}=\sum_{m} \frac{1}{2} \alpha_{n-m} \alpha_{m},\tilde{L}_{n}=\sum_{m} \frac{1}{2} \tilde{\alpha}_{n-m} \tilde{\alpha}_{m},
d s ^ { 2 } = - d t ^ { 2 } + a ( t ) ^ { 2 } \left( d \chi ^ { 2 } + \sinh ^ { 2 } \chi d \Omega _ { 2 } ^ { 2 } \right) , | d s^{2}=-d t^{2}+a(t)^{2} \left(d \chi^{2}+\mathrm{sinh}^{2} \chi d \Omega_{2}^{2} \right),
\nu _ { T O T A L } ( p ) = \nu _ { 1 } ( p ) + \nu _ { 2 } ( p ) + \nu _ { 3 } ( p ) = \frac { p } { 1 2 } ( p ^ { 2 } + 2 p + 2 ) | \nu_{T O T A L}(p)=\nu_{1}(p)+\nu_{2}(p)+\nu_{3}(p)=\frac{p}{1 2}(p^{2}+2 p+2)
\mathrm { \ r h o } _ { 0 } = \mathcal { J } ^ { 0 } \left( y \right) , | \mathrm{\rho}_{0}=\mathcal{J}^{0} \left(y \right),
k _ { 0 \pm } ^ { 2 } = \frac { 1 } { 2 } \left[ \left( s ^ { 2 } + 2 \overrightarrow { k } ^ { 2 } \right) \pm \sqrt { s ^ { 4 } + 4 v _ { o } ^ { 2 } \overrightarrow { k } ^ { 2 } } \right] , | k_{0 \pm}^{2}=\frac{1}{2} \left[\left(s^{2}+2 \overrightarrow{k}^{2} \right) \pm \sqrt{s^{4}+4 v_{o}^{2} \overrightarrow{k}^{2}} \right],
\partial _ { \mu } = \frac { \partial } { \partial \xi ^ { \mu } } | \partial_{\mu}=\frac{\partial}{\partial \xi^{\mu}}
\frac { \kappa + \alpha } { ( \kappa - a _ { 1 } ) ( \kappa - \kappa _ { 1 } ) ( \kappa - \kappa _ { 2 } ) } | \frac{\kappa+\alpha}{(\kappa-a_{1})(\kappa-\kappa_{1})(\kappa-\kappa_{2})}
T _ { F } = { \frac { i } { 2 } } \sum _ { a = 0 } ^ { 3 } \psi ^ { a } \partial X ^ { a } + { \mathrm { h . c . } } . | T_{F}={\frac{i}{2}} \sum_{a=0}^{3} \psi^{a} \partial X^{a}+{\mathrm{h.c.}}.
\overline { { \sigma } } ^ { \prime } ( s ) = \overline { { \sigma } } _ { a } ^ { \prime } ( s ) \Theta ( s - 4 ) + \overline { { \sigma } } _ { b } ^ { \prime } ( s ) \Theta ( s - 9 ) . | \overline{{{\sigma}}}^{\prime}(s)=\overline{{{\sigma}}}_{a}^{\prime}(s) \Theta(s-4)+\overline{{{\sigma}}}_{b}^{\prime}(s) \Theta(s-9).
\left( \begin{array} { c } { n } \\ { m } \\ \end{array} \right) = \frac { n ( n - 1 ) . . . ( n - m + 1 ) } { m ! } , { } { } \left( \begin{array} { c } { n } \\ { 0 } \\ \end{array} \right) = 1 , | \left(\begin{array}{c}{{n}} \\{{m}} \end{array} \right)=\frac{n(n-1)...(n-m+1)}{m !}, \left(\begin{array}{c}{{n}} \\{{0}} \end{array} \right)=1,
M _ { n } = \sqrt { m ^ { 2 } + { \mathcal { B } } ( 2 n + 1 ) } . | M_{n}=\sqrt{m^{2}+{\mathcal{B}}(2 n+1)}.
x _ { \alpha ^ { \vee } } = Z _ { \alpha } - i \pi - x _ { \alpha } | x_{\alpha^{\vee}}=Z_{\alpha}-i \pi-x_{\alpha}
\left\{ - \frac { d ^ { 2 } } { d r ^ { 2 } } + \frac { \gamma } { r } + V ( r ) \right\} u = k ^ { 2 } u | \left\{-\frac{d^{2}}{d r^{2}}+\frac{\gamma}{r}+V(r) \right\} u=k^{2} u
7 \times 1 5 4 \neq - 1 3 6 2 | 7 \times 1 5 4 \neq-1 3 6 2
\begin{align*}\frac{4q^2+12q+15+ 8p(q+2)+4p^2}{4\{2\}}b^2\ + \frac{(3+2p)[q^2+3q+3+2p(q+1)]}{2}b\bigg]\ ,\end{align*} | \begin{align*}\frac{4q^2+12q+15+8p(q+2)+4p^2}{4\{2\}}b^2\+\frac{(3+2p)[q^2+3q+3+2p(q+1)]}{2}b\bigg]\,\end{align*}
\begin{align*}\dot{x}=e^{2S(x)}S'(x)+e^{S(x)}\eta\end{align*} | \begin{align*}\dot{x}=e^{2S(x)}S'(x)+e^{S(x)}\eta\end{align*}
\begin{align*}\hat{H}=\sqrt{\frac{2\Omega}{\mu}}[\hat{a}_+^{\dag} \hat{a}_+ +\hat{a}_-^{\dag}\hat{a}_- +1 ] - \Omega \theta [\hat{a}_-^{\dag}\hat{a}_- - \hat{a}_+^{\dag} \hat{a}_+].\end{align*} | \begin{align*}\hat{H}=\sqrt{\frac{2\Omega}{\mu}}[\hat{a}_+^{\dag} \hat{a}_++\hat{a}_-^{\dag}\hat{a}_-+1]-\Omega \theta[\hat{a}_-^{\dag}\hat{a}_- -\hat{a}_+^{\dag} \hat{a}_+].\end{align*}
\begin{align*}\sum_{i<j} \sqrt{l_i l_j} \leq \frac{n-1}{2}.\end{align*} | \begin{align*}\sum_{i<j} \sqrt{l_i l_j} \leq \frac{n-1}{2}.\end{align*}
\begin{align*}g_{\mu \nu} = g^{(0)}_{\mu \nu} + g^{(1)}_{\mu \nu} + \cdots\end{align*} | \begin{align*}g_{\mu \nu}=g^{(0)}_{\mu \nu}+g^{(1)}_{\mu \nu}+. . .\end{align*}
\begin{align*}{\textstyle \left(L,\left\{ \prod^{p,q}\right\} _{p+q=n}\right)}\end{align*} | \begin{align*}{\textstyle \left(L,\left\{\prod^{p,q}\right\}_{p+q=n}\right)}\end{align*}
\begin{align*}L_Xf\omega = fL_X\omega+i_{[X,f]}\omega.\end{align*} | \begin{align*}L_Xf\omega=fL_X\omega+i_{[X,f]}\omega.\end{align*}
\begin{align*}^3R_{mn} = \frac {1}{2}Tr[J^P_mJ^P_n + J^Q_mJ^Q_n],\end{align*} | \begin{align*}^3R_{mn}=\frac{{1}{2}Tr[J^P_mJ^P_n}{+} J^Q_mJ^Q_n],\end{align*}
\begin{align*}\left(\chi_{1}, \chi_{2}, \chi_{3}, \chi_{4} \right) = (z, t, P, E ) .\end{align*} | \begin{align*}\left(\chi_{1},\chi_{2},\chi_{3},\chi_{4} \right)=(z,t,P,E).\end{align*}
\begin{align*}\frac{\sqrt{-\hat{g}}}{\hat{g}_{str}} \frac{ \hat{g}^{00} \hat{g}^{11} F_{01} } {\sqrt{ 1+(2\pi\alpha^\prime)^2 \hat{g}^{00}\hat{g}^{11} F_{01}^2 }} = B_{23} =\frac{s}{2\pi R^2},\end{align*} | \begin{align*}\frac{\sqrt{-\hat{g}}}{\hat{g}_{str}} \frac{\hat{g}^{00} \hat{g}^{11} F_{01}}{\sqrt{1+(2\pi\alpha^\prime)^2 \hat{g}^{00}\hat{g}^{11} F_{01}^2}}=B_{23}=\frac{s}{2\pi R^2},\end{align*}
\begin{align*}G(\Psi):=(4-2d)\omega Q(\Psi)+(3-d)\mathbf{c}\cdot \mathbf{P}(\Psi).\end{align*} | \begin{align*}G(\Psi):=(4-2d)\omega Q(\Psi)+(3-d)\mathbf{c}\cdot \mathbf{P}(\Psi).\end{align*}
\begin{align*}\left\vert f\left( z\right) \right\vert \geq\left\vert g\left( z\right)\right\vert -\left\vert g\left( z\right) -f\left( z\right) \right\vert>s-\left( s-n\right) =n\end{align*} | \begin{align*}\left\vert f\left(z\right) \right\vert \geq\left\vert g\left(z\right)\right\vert-\left\vert g\left(z\right)-f\left(z\right) \right\vert>s-\left(s-n\right)=n\end{align*}
\begin{align*}U(I)=\{u|u_{1:k}\ge 0 \ \ u_{1:k}<0\ \}.\end{align*} | \begin{align*}U(I)=\{u|u_{1:k}\ge 0 \ \ u_{1:k}<0\ \}.\end{align*}
\begin{align*} \int_0^T g(X_t^\dagger,t)\,{\rm d}X_t^\dagger = \lim_{\Delta t\to 0} \sum_{i=1}^L g(X_{t_n}^\dagger,t_n)(X_{t_{n+1}}^\dagger -X_{t_n}^\dagger )\end{align*} | \begin{align*} \int_0^T g(X_t^\dagger,t)\,{\rm d}X_t^\dagger=\lim_{\Delta t\to 0} \sum_{i=1}^L g(X_{t_n}^\dagger,t_n)(X_{t_{n+1}}^\dagger-X_{t_n}^\dagger)\end{align*}
\begin{align*}E_{22}(t)\dot x_2=A_{22}(t)x_2\end{align*} | \begin{align*}E_{22}(t)\dot x_2=A_{22}(t)x_2\end{align*}
\begin{align*}\ell_{j}^{\ast}\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}}=\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}},j=1,2.\end{align*} | \begin{align*}\ell_{j}^{\ast}\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}}=\frac{\sigma(z)}{[\sigma(z-p)\sigma(z+p)]^{\frac{1}{2}}},j=1,2.\end{align*}
\begin{align*}\mu_g(A)\le \frac{C}{\lambda} |z|^{\frac{n-2}{2}} \mu_g(E)^{\frac{1}{p_2}}\mu_g(A)^{\frac{1}{q_2'}}= \frac{C}{\lambda} |z|^{-\frac{1}{n+1}}\mu_g(E)^{\frac{n^2+4n-1}{2n(n+1)}} \mu_g(A)^{\frac{n+1}{2n}} R_0^\frac{n-1}{2} \,.\end{align*} | \begin{align*}\mu_g(A)\le \frac{C}{\lambda} |z|^{\frac{n-2}{2}} \mu_g(E)^{\frac{1}{p_2}}\mu_g(A)^{\frac{1}{q_2'}}=\frac{C}{\lambda} |z|^{-\frac{1}{n+1}}\mu_g(E)^{\frac{n^2+4n-1}{2n(n+1)}} \mu_g(A)^{\frac{n+1}{2n}} R_0^\frac{n-1}{2} \,.\end{align*}
\begin{align*}\Phi\left(\Psi(\theta)\right)= \begin{pmatrix} -XZ+L_{1}(1-Z-\mu(1-Y)) \\ -L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y) \\ \frac{1}{L_{2}}((-XZ+1-Z-\mu(1-Y))+\mu)-L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y))) \end{pmatrix} .\end{align*} | \begin{align*}\Phi\left(\Psi(\theta)\right)=\begin{pmatrix}-XZ+L_{1}(1-Z-\mu(1-Y)) \\-L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y) \\ \frac{1}{L_{2}}((-XZ+1-Z-\mu(1-Y))+\mu)-L_{3}YZ+\frac{L_{4}}{L_{2}}(1-Y))) \end{pmatrix}.\end{align*}
\begin{align*}j(x,\xi) := i\xi_n + F(x',\xi') \end{align*} | \begin{align*}j(x,\xi):=i\xi_n+F(x',\xi') \end{align*}
\begin{align*}w_r(\tau) = e^{\tau \Delta}w_r(0) &+ \int_0^\tau e^{(\tau - s)\Delta}\big(e^u \Delta \phi_r - 2\nabla \cdot (e^u \nabla \phi_r)\big) ds \\&+ \int_0^\tau e^{(\tau - s)\Delta}e^u\phi_r (\partial_\tau u - \Delta u - |\nabla u|^2)ds,\end{align*} | \begin{align*}w_r(\tau)=e^{\tau \Delta}w_r(0) &+\int_0^\tau e^{(\tau-s)\Delta}\big(e^u \Delta \phi_r-2\nabla \cdot(e^u \nabla \phi_r)\big) ds \\&+\int_0^\tau e^{(\tau-s)\Delta}e^u\phi_r(\partial_\tau u-\Delta u-|\nabla u|^2)ds,\end{align*}
\begin{align*}\left[V_{v}\right]:=\bigoplus_{\varepsilon_v}\left[V_{v}\right]^{\varepsilon_v}\end{align*} | \begin{align*}\left[V_{v}\right]:=\bigoplus_{\varepsilon_v}\left[V_{v}\right]^{\varepsilon_v}\end{align*}
\begin{align*} \Big|\omega(P_E(n)) -\sum_{i=1}^r \omega(P_i(n)) \Big| \leq C'.\end{align*} | \begin{align*} \Big|\omega(P_E(n))-\sum_{i=1}^r \omega(P_i(n)) \Big| \leq C'.\end{align*}
\begin{align*}s:=\dim_{A}(K_{v}(\omega))<t:=\frac{-\log(\mathfrak{P}(\epsilon))}{\log(\epsilon)}.\end{align*} | \begin{align*}s:=\dim_{A}(K_{v}(\omega))<t:=\frac{-\log(\mathfrak{P}(\epsilon))}{\log(\epsilon)}.\end{align*}
\begin{align*} p = \pi_{W, T}.\end{align*} | \begin{align*} p=\pi_{W,T}.\end{align*}
\begin{align*} 0 \to \mathbb{Q}_p \to B_{max,\mathbb{Q}_p}^{\phi_p = 1} \to B_{dR}/B^+_{dR} \to 0\end{align*} | \begin{align*} 0 \to \mathbb{Q}_p \to B_{max,\mathbb{Q}_p}^{\phi_p=1} \to B_{dR}/B^+_{dR} \to 0\end{align*}
\begin{align*}A\|f\|^2\leq \sum_{m=1}^L\sum_{(j, k) \in \mathbb Z \times \mathbb N_0}\left|<f, D^j_{\mathfrak p}T_{k}\psi_m>\right|^2 \leq B\|f\|^2,\end{align*} | \begin{align*}A\|f\|^2\leq \sum_{m=1}^L\sum_{(j,k) \in \mathbb{Z} \times \mathbb{N_0}\left|<f,} D^j_{\mathfrak p}T_{k}\psi_m>\right|^2 \leq B\|f\|^2,\end{align*}
\begin{align*} |c_M|_1 &\leq \binom{n}{n-1} \cdot |c_N|_1 \cdot |c|_1 \leq n \cdot |c_N|_1 \cdot \epsilon.\end{align*} | \begin{align*} |c_M|_1 &\leq \binom{n}{n-1} \cdot |c_N|_1 \cdot |c|_1 \leq n \cdot |c_N|_1 \cdot \epsilon.\end{align*}
\begin{align*}\lambda=\lim_{n\to\infty} \lambda_n.\end{align*} | \begin{align*}\lambda=\lim_{n\to\infty} \lambda_n.\end{align*}
\begin{align*}\sum_{n \geq 1} x_n z^n =\frac{\sum_{j \in J}z^j}{1-\sum_{j \in J}z^j},\end{align*} | \begin{align*}\sum_{n \geq 1} x_n z^n=\frac{\sum_{j \in J}z^j}{1-\sum_{j \in J}z^j},\end{align*}
\begin{align*}u(x,t)=\sum_{k=1}^{\infty} u_k(t) \sin(k \pi x), \end{align*} | \begin{align*}u(x,t)=\sum_{k=1}^{\infty} u_k(t) \sin(k \pi x),\end{align*}
\begin{align*} \| e^{(t-s) \Delta } F_1 (s) \| _{ L^1 } & \le \| F_1 (s) \| _{ L^1 }= \int _{ \{ |x|<\sqrt s \} } s^{-\frac {\alpha +1} {\alpha }} \Bigl| f \Bigl( \frac {x} {\sqrt s} \Bigr) \Bigr|^{\alpha +1} dx \\ & = \int _{ \{ |x|< 1 \} } |f(x)|^{\alpha +1}dx \end{align*} | \begin{align*} \| e^{(t-s) \Delta} F_1(s) \|_{L^1} & \le \| F_1(s) \|_{L^1}=\int_{\{|x|<\sqrt s \}} s^{-\frac{\alpha+1}{\alpha}} \Bigl| f \Bigl(\frac{{x}}{{\sqrt} s} \Bigr) \Bigr|^{\alpha+1} dx \\ &=\int_{\{|x|<1 \}} |f(x)|^{\alpha+1}dx \end{align*}
\begin{align*} \begin{cases} v_i(x,t)=S_{d,i}(t,t_{1,ex}(x,t))S_{c,i}(t_{i,ex}(x,t),t_{1,en}(x,t))S_{d,i}(t_{i,en}(x,t),0)v_{i,0}(x) \forall\:i\in[1,p], \\ v_i(x,t)=S_{d,i}(t,0)v_{i,0}(x) \forall\:i\in[p+1,n]. \end{cases} \end{align*} | \begin{align*} \begin{cases} v_i(x,t)=S_{d,i}(t,t_{1,ex}(x,t))S_{c,i}(t_{i,ex}(x,t),t_{1,en}(x,t))S_{d,i}(t_{i,en}(x,t),0)v_{i,0}(x) \forall\:i\in[1,p],\\ v_i(x,t)=S_{d,i}(t,0)v_{i,0}(x) \forall\:i\in[p+1,n].\end{cases} \end{align*}
\begin{align*} \nabla c\cdot \nu=(\gamma-c)g\quad\mbox{on}\partial\Omega. \end{align*} | \begin{align*} \nabla c\cdot \nu=(\gamma-c)g\quad\text{CONTENTPROTECTED0}\partial\Omega.\end{align*}
\begin{align*}&\|I_1(x)\|_{L^{2,1} } = \frac{1} {\pi} \| \widehat{(z^{-1} \bar{r}_1)}(2x)\|_{L^{2,1}_x} = \frac{1}{\pi} \| z^{-1} \bar{r}_1(z) \|_{ H^1_z}. \end{align*} | \begin{align*}&\|I_1(x)\|_{L^{2,1}}=\frac{1}{\pi} \| \widehat{(z^{-1} \bar{r}_1)}(2x)\|_{L^{2,1}_x}=\frac{1}{\pi} \| z^{-1} \bar{r}_1(z) \|_{H^1_z}.\end{align*}
\begin{align*} d(X( \bar u)) - L_X g (\nabla \bar u ,\cdot )&= \nabla^2 \bar u (X, \cdot ) - \nabla X (\nabla \bar u,\cdot).\end{align*} | \begin{align*} d(X(\bar{u))}-L_X g(\nabla \bar{u},\cdot)&=\nabla^2 \bar{u}(X,\cdot)-\nabla X(\nabla \bar{u,\cdot).\end{align*}}
\begin{align*} g(0)g(\infty)=&\frac{(x_0+\sqrt{a}x_1)(\sqrt{b}x_2+\sqrt{ab}x_3)}{(\sqrt{b}x_2-\sqrt{ab}x_3)(x_0-\sqrt{a}x_1)}>0\\ \Leftrightarrow\ &(x_0^2-ax_1^2)(bx_2^2-abx_3^2)>0\\ \Leftrightarrow\ &(x_0^2-ax_1^2)(x_0^2-ax_1^2-1)>0\end{align*} | \begin{align*} g(0)g(\infty)=&\frac{(x_0+\sqrt{a}x_1)(\sqrt{b}x_2+\sqrt{ab}x_3)}{(\sqrt{b}x_2-\sqrt{ab}x_3)(x_0-\sqrt{a}x_1)}>0\\ \Leftrightarrow\ &(x_0^2-ax_1^2)(bx_2^2-abx_3^2)>0\\ \Leftrightarrow\ &(x_0^2-ax_1^2)(x_0^2-ax_1^2-1)>0\end{align*}
\begin{align*} \frac{d\mu_S}{d\mu_T} =|h|^2 \end{align*} | \begin{align*} \frac{d\mu_S}{d\mu_T}=|h|^2 \end{align*}
\begin{gather*}K=\sum _{i=-\mu}^\mu K_p,\end{gather*} | \begin{gather*}K=\sum_{i=-\mu}^\mu K_p,\end{gather*}
\begin{align*}\frac{d}{dz}W_{\alpha,\beta}^{\gamma,\sigma}(z)=\frac{\gamma}{\sigma}W_{\alpha,\beta+\alpha}^{\gamma+1,\sigma+1}(z),\end{align*} | \begin{align*}\frac{d}{dz}W_{\alpha,\beta}^{\gamma,\sigma}(z)=\frac{\gamma}{\sigma}W_{\alpha,\beta+\alpha}^{\gamma+1,\sigma+1}(z),\end{align*}
\begin{align*} \left( A^{\alpha} g \right)(x) = \lim\limits_{\varepsilon \rightarrow 0+} \left( \left( J^{\alpha}_{\left( A + \varepsilon \right)^{-1}} \right)^{-1} g \right) (x) \end{align*} | \begin{align*} \left(A^{\alpha} g \right)(x)=\lim\limits_{\varepsilon \rightarrow 0+} \left(\left(J^{\alpha}_{\left(A+\varepsilon \right)^{-1}} \right)^{-1} g \right)(x) \end{align*}
\begin{align*}A\left( \widetilde{u}\right) =f\left( x_{1}-x_{0}\right) \chi \left( \overline{x}-\overline{x}^{0}\right) ,\forall x_{0}\in \left[ 0,1\right] ,\end{align*} | \begin{align*}A\left(\widetilde{u}\right)=f\left(x_{1}-x_{0}\right) \chi \left(\overline{x}-\overline{x}^{0}\right),\forall x_{0}\in \left[0,1\right],\end{align*}
\begin{align*} v_{3}(S_{k}(2x))= v_{3}(S_{k}(x))= \gamma+ 2d- 1\end{align*} | \begin{align*} v_{3}(S_{k}(2x))=v_{3}(S_{k}(x))=\gamma+2d-1\end{align*}
\begin{align*} \frac{dx^i}{d\tau}=f(x^1,\ldots,x^n)\ X^i(x^1,\ldots,x^n),i=1,\ldots,n. \end{align*} | \begin{align*} \frac{dx^i}{d\tau}=f(x^1,. . .,x^n)\ X^i(x^1,. . .,x^n),i=1,. . .,n.\end{align*}
\begin{align*} -\nabla_{g}^2 f_{\sigma}(x) = \lambda(\sigma) f_{\sigma}(x) \;,\end{align*} | \begin{align*}-\nabla_{g}^2 f_{\sigma}(x)=\lambda(\sigma) f_{\sigma}(x) \;,\end{align*}
\begin{align*}\nu_{(a^-, i)}^{\hat{S}^k} = \nu_{(a_k^-, i_k)}^* - \frac{w_{(a^-, i)}^{\hat{S}^{k-1}}}{w_{(a^-, i)}^{\hat{S}^{k}}} \big(\nu_{(a_k^-, i_k)}^* - \nu_{(a^-, i)}^{\hat{S}^{k-1}}\big), (a^-, i) \in \hat{N}.\end{align*} | \begin{align*}\nu_{(a^-,i)}^{\hat{S}^k}=\nu_{(a_k^-,i_k)}^*-\frac{w_{(a^-,i)}^{\hat{S}^{k-1}}}{w_{(a^-,i)}^{\hat{S}^{k}}} \big(\nu_{(a_k^-,i_k)}^*-\nu_{(a^-,i)}^{\hat{S}^{k-1}}\big),(a^-,i) \in \hat{N}.\end{align*}
\begin{align*} \sum_{j=0}^{n}\binom{n}{j}a_m(j+1)c_{m,r}(n-j)=b_{m-\ell-1}(n+1)+ b_{\ell-1}(n+1) - \frac{a_m(n+2)}{m}.\end{align*} | \begin{align*} \sum_{j=0}^{n}\binom{n}{j}a_m(j+1)c_{m,r}(n-j)=b_{m-\ell-1}(n+1)+b_{\ell-1}(n+1)-\frac{a_m(n+2)}{m}.\end{align*}
\begin{align*} \kappa_{\beta}= \begin{cases} |\beta|-1 &\ \ \beta\neq [1,2,1,2,2]\\ |\beta|+1 &\ \ \beta=[1,2,1,2,2] \end{cases}.\end{align*} | \begin{align*} \kappa_{\beta}=\begin{cases} |\beta|-1 &\ \ \beta\neq[1,2,1,2,2]\\ |\beta|+1 &\ \ \beta=[1,2,1,2,2] \end{cases}.\end{align*}
\begin{align*}G_t^p(x,y,N)&=\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=1\big)}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=0\big)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\\&\geq\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{p}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{1-f(T,p)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\end{align*} | \begin{align*}G_t^p(x,y,N)&=\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=1\big)}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{P_N^p\big(\eta_t(x)=0\big)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\\&\geq\frac{\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{p}\Big]\Big[1-\frac{P_N^p\big(A^c_{x,y}(T)\big)}{1-f(T,p)}\Big]}{P_N^p\big(A_{x,y}(T)\big)}\end{align*}
\begin{align*} f_a(z)=\cos(a(z-ir))\cos(a(z+ir))=\tfrac{1}{2}\big(\cos(2az)+\cosh(2ar)\big).\end{align*} | \begin{align*} f_a(z)=\cos(a(z-ir))\cos(a(z+ir))=\tfrac{1}{2}\big(\cos(2az)+\cosh(2ar)\big).\end{align*}
\begin{align*}{\cal E}_h(u,v)=\int_a^bu'(t)\overline{v(t)}dt-\int_a^bh(t)\{u(t)\overline{v(t)}\}'dt\ ,\end{align*} | \begin{align*}{\cal E}_h(u,v)=\int_a^bu'(t)\overline{v(t)}dt-\int_a^bh(t)\{u(t)\overline{v(t)}\}'dt\,\end{align*}
\begin{gather*}\theta_{p}^0 = dp^0, \theta_{p}^i = d p^i, \quad\theta_x = dx, \theta_y^0 = dy^0 - p^0 dx, \theta_y^i = dy^i - p^i dx, \\ \theta_{z}^i = dz^i - p^0 dy^i - p^i dy^0 + p^0 p^i dx.\end{gather*} | \begin{gather*}\theta_{p}^0=dp^0,\theta_{p}^i=d p^i,\quad\theta_x=dx,\theta_y^0=dy^0-p^0 dx,\theta_y^i=dy^i-p^i dx,\\ \theta_{z}^i=dz^i-p^0 dy^i-p^i dy^0+p^0 p^i dx.\end{gather*}
\begin{align*}W^\lambda= \{w\in W\,|\,w(\Pi_\lambda)\subset\Delta^+\}\end{align*} | \begin{align*}W^\lambda=\{w\in W\,|\,w(\Pi_\lambda)\subset\Delta^+\}\end{align*}
\begin{align*} \tilde{C}_{ij}(\alpha)= \sum_{k=1}^D\theta_{k 1} f_{\alpha - e_i - e_j - e_k} + (1-\delta_{|\alpha|,M})( \alpha_1 + 1) f_{\alpha-e_i-e_j+e_1}.\end{align*} | \begin{align*} \tilde{C}_{ij}(\alpha)=\sum_{k=1}^D\theta_{k 1} f_{\alpha-e_i-e_j-e_k}+(1-\delta_{|\alpha|,M})(\alpha_1+1) f_{\alpha-e_i-e_j+e_1}.\end{align*}
\begin{align*} ||F_{6}(t,r)||_{L^{2}(r dr)}^{2} &\leq C \int_{\frac{t}{4}}^{\frac{t}{2}} \frac{\lambda(t)^{4} r (v_{4}(t,r)^{2}+v_{5}(t,r)^{2})}{r^{8}} dr + C \int_{\frac{t}{2}}^{\infty} \frac{\lambda(t)^{4} (v_{4}^{2}+v_{5}^{2}) r dr}{r^{8}}\\&\leq \frac{C}{t^{8} \log^{10b+4N-2}(t)} \end{align*} | \begin{align*} ||F_{6}(t,r)||_{L^{2}(r dr)}^{2} &\leq C \int_{\frac{t}{4}}^{\frac{t}{2}} \frac{\lambda(t)^{4} r(v_{4}(t,r)^{2}+v_{5}(t,r)^{2})}{r^{8}} dr+C \int_{\frac{t}{2}}^{\infty} \frac{\lambda(t)^{4}(v_{4}^{2}+v_{5}^{2}) r dr}{r^{8}}\\&\leq \frac{C}{t^{8} \log^{10b+4N-2}(t)} \end{align*}
\begin{align*}\sum_{\mathsf{m=1}}^{+\infty }\sum_{\mathsf{n=1}}^{+\infty }r_{\mathsf{m,n}}^{2}=Z_{\left( \alpha -1\right) \beta }Z_{\left( \alpha +1\right) \beta}<+\infty \end{align*} | \begin{align*}\sum_{\mathsf{m=1}}^{+\infty}\sum_{\mathsf{n=1}}^{+\infty}r_{\mathsf{m,n}}^{2}=Z_{\left(\alpha-1\right) \beta}Z_{\left(\alpha+1\right) \beta}<+\infty \end{align*}
\begin{align*} \mathcal{W}^p(M) := \int_M H^p \, dS, p\geq 1,\end{align*} | \begin{align*} \mathcal{W}^p(M):=\int_M H^p \,dS,p\geq 1,\end{align*}
\begin{align*}g_2(a,b)&=-g_1(b^{-1},b)+ g_1(b^{-1},b)g_1(a, a^{-1})\\&=-g_1(a,b)+ g_1(a,b)g_1(a, a^{-1})\\&=-g_1(a,b)+ g_1(a,a^{-1}b), \end{align*} | \begin{align*}g_2(a,b)&=-g_1(b^{-1},b)+g_1(b^{-1},b)g_1(a,a^{-1})\\&=-g_1(a,b)+g_1(a,b)g_1(a,a^{-1})\\&=-g_1(a,b)+g_1(a,a^{-1}b),\end{align*}
\begin{align*} \gamma_\textnormal{eq}\left( \textnormal{confluence}\left(J^{K\textnormal{th},\textnormal{eq}}\right)(z,Q) \right) = J^{\textnormal{coh},\textnormal{eq}}(z,Q) \end{align*} | \begin{align*} \gamma_\textnormal{eq}\left(\textnormal{confluence}\left(J^{K\textnormal{th},\textnormal{eq}}\right)(z,Q) \right)=J^{\textnormal{coh},\textnormal{eq}}(z,Q) \end{align*}
\begin{align*} \frac{1}{n}\max_{x_2^n} C(\mathcal G_{x_2^n}) &\geq \log\max_{x_2^n} \mathsf{LP}(\mathcal G_{x_2^n})^{1/n}\\&= \log\max_{x_2^n} \prod_{i=1}^{n} \mathsf{LP}(\mathcal G_{x_{2i}})^{1/n}\\&= \log \max_{x_2}\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\log\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\bar{C}(\mathcal G_{x_{2}}).\end{align*} | \begin{align*} \frac{1}{n}\max_{x_2^n} C(\mathcal G_{x_2^n}) &\geq \log\max_{x_2^n} \mathsf{LP}(\mathcal G_{x_2^n})^{1/n}\\&=\log\max_{x_2^n} \prod_{i=1}^{n} \mathsf{LP}(\mathcal G_{x_{2i}})^{1/n}\\&=\mathrm{log} \max_{x_2}\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\log\mathsf{LP}(\mathcal G_{x_{2}})\\&=\max_{x_2}\bar{C}(\mathcal G_{x_{2}}).\end{align*}
\begin{align*}N(Q(u)\cdot x)=-u\frac{\partial}{\partial u}(Q(u))x+ Q(u) N(x)\end{align*} | \begin{align*}N(Q(u)\cdot x)=-u\frac{\partial}{\partial u}(Q(u))x+Q(u) N(x)\end{align*}
\begin{align*}(M\lambda)_i:=\bigcup_{j=1}^k(M_{ij}\cap\lambda_j),i=1,\ldots,k, \lambda\in {\mathcal{L}_{[n]:k}}.\end{align*} | \begin{align*}(M\lambda)_i:=\bigcup_{j=1}^k(M_{ij}\cap\lambda_j),i=1,. . .,k,\lambda\in{\mathcal{L}_{[n]:k}}.\end{align*}
\begin{align*} L_{g_0}^1u=-\frac{n-4}{8(n-2)}R_{g_0} \Delta_{g_0}u- \frac{n(n-1)(n-4)}{8}u+\frac{n-4}{4(n-2)}\langle Ric_{g_0},\nabla_{g_0}^2u\rangle_{g_0}\end{align*} | \begin{align*} L_{g_0}^1u=-\frac{n-4}{8(n-2)}R_{g_0} \Delta_{g_0}u-\frac{n(n-1)(n-4)}{8}u+\frac{n-4}{4(n-2)}\langle Ric_{g_0},\nabla_{g_0}^2u\rangle_{g_0}\end{align*}
\begin{align*} \delta x^{-1} g(t,u,z) f =h \delta , \end{align*} | \begin{align*} \delta x^{-1} g(t,u,z) f=h \delta,\end{align*}
\begin{align*} \widetilde{\mu_j^n}(p)\ =\ \widetilde{\beta_i^n}(p)\ +\ \lambda_k, \ \ \mbox{ for } i,j=1,2,\ldots,\ \ k=0,1, \ldots\end{align*} | \begin{align*} \widetilde{\mu_j^n}(p)\=\ \widetilde{\beta_i^n}(p)\+\ \lambda_k,\ \\text{CONTENTPROTECTED0}i,j=1,2,. . .,\ \ k=0,1,. . .\end{align*}
\begin{gather*}h^{pq}_{kl}=-h^{pk}h^{ql},\\h^{pq}_{kl,rs}=h^{pr}h^{ks}h^{ql}+h^{pk}h^{qr}h^{ls},\\D_jh^{pq}=h^{pq}_{kl}D_jh_{kl},\\D^2_{ij}h^{pq}=h^{pq}_{kl}D^2_{ij}h_{kl}+h^{pq}_{kl,rs}D_ih_{rs}D_jh_{kl}.\end{gather*} | \begin{gather*}h^{pq}_{kl}=-h^{pk}h^{ql},\\h^{pq}_{kl,rs}=h^{pr}h^{ks}h^{ql}+h^{pk}h^{qr}h^{ls},\\D_jh^{pq}=h^{pq}_{kl}D_jh_{kl},\\D^2_{ij}h^{pq}=h^{pq}_{kl}D^2_{ij}h_{kl}+h^{pq}_{kl,rs}D_ih_{rs}D_jh_{kl}.\end{gather*}
\begin{align*}c_{n}=\left( \int\psi_{+}(x,\mathrm{i}\kappa_{n})^{2}\mathrm{d}x\right)^{-1/2}\end{align*} | \begin{align*}c_{n}=\left(\int\psi_{+}(x,\mathrm{i}\kappa_{n})^{2}\mathrm{d} x\right)^{-1/2}\end{align*}
\begin{align*}d'(x,y) :=\begin{cases}\bar{d}(x,y), & \mbox{ if } \bar{d}(x,y) <1 \,,\\ 1, &\mbox{ otherwise }.\end{cases}\end{align*} | \begin{align*}d'(x,y):=\begin{cases}\bar{d}(x,y),&\text{CONTENTPROTECTED0}\bar{d}(x,y)<1 \,,\\ 1,&\text{CONTENTPROTECTED1}.\end{cases}\end{align*}
\begin{align*} \mathbf{P}(A) = \mathbf{P}(\theta_{\tau} A).\end{align*} | \begin{align*} \mathbf{P}(A)=\mathbf{P}(\theta_{\tau} A).\end{align*}
\begin{align*}|y|^2=|\zeta_0+et|^2=1+2t(\zeta_0, e)+t^2\geqslant1-2t+t^2=(1-t)^2\geqslant 1/4\,.\end{align*} | \begin{align*}|y|^2=|\zeta_0+et|^2=1+2t(\zeta_0,e)+t^2\geq slant1-2t+t^2=(1-t)^2\geq slant 1/4\,.\end{align*}
\begin{align*}{}~~~~~~~~~~~~~~~~~~~~~~~~~~~\times W'(a_4|c_2dc_3|a_2b_3a_1|c_5)\stackrel{-}{W}(d|a_1a_3a_2|c_4c_5c_6|b_4)\end{align*} | \begin{align*}~~~~~~~~~~~~~~~~~~~~~~~~~~~\times W'(a_4|c_2dc_3|a_2b_3a_1|c_5)\stackrel{-}{W}(d|a_1a_3a_2|c_4c_5c_6|b_4)\end{align*}
\begin{align*}i\rho^{{\mbox{\scriptsize ren}}} = i\rho_0 - f_1^{{\mbox{\scriptsize ren}}}\end{align*} | \begin{align*}i\rho^{{\text{CONTENTPROTECTED0}}}=i\rho_0-f_1^{{\text{CONTENTPROTECTED1}}}\end{align*}
\begin{align*}\langle f|\exp[-i\hat{H}(t_{f}-t_{i})]|i\rangle\end{align*} | \begin{align*}\langle f|\exp[-i\hat{H}(t_{f}-t_{i})]|i\rangle\end{align*}
\begin{align*}{\mbox{e}}^{2\phi} = \sqrt{2} |P| \, {\mbox{e}}^{2\phi_0} \,\mbox{exp}(\frac{w}{\sqrt{2} P}) \ .\end{align*} | \begin{align*}{\text{CONTENTPROTECTED0}}^{2\phi}=\sqrt{2} |P| \,{\text{CONTENTPROTECTED1}}^{2\phi_0} \,\text{CONTENTPROTECTED2}(\frac{w}{\sqrt{2} P}) \.\end{align*}
\begin{align*}M\geq \sqrt {Q^2 + P^2}= |z_1|\ ,\end{align*} | \begin{align*}M\geq \sqrt{{Q^2}+P^2}=|z_1|\,\end{align*}
\begin{align*}S_{\rm grav}=2M^{3}r_c\int d^{4}x\int dy\ln^{2/3}T^2\bar{R} .\end{align*} | \begin{align*}S_{\rm grav}=2M^{3}r_c\int d^{4}x\int dy\ln^{2/3}T^2\bar{R}.\end{align*}
\begin{align*}{\cal L}=\partial_+\phi\partial_-\phi-m^2\phi^2/2\,.\end{align*} | \begin{align*}{\cal L}=\partial_+\phi\partial_-\phi-m^2\phi^2/2\,.\end{align*}
\begin{align*}{e_t}^0= \frac p r - A_t.\end{align*} | \begin{align*}{e_t}^0=\frac{p}{r}-A_t.\end{align*}
\begin{align*}E= {1 \over 2G g^{1/2}(L)}\int_{x_+}^{L}(G^0_0+T^0_0)dx+{1 \over 12\pi\beta_H g^{1/2}(L)}+E_{surf} ,\end{align*} | \begin{align*}E={1 \over 2G g^{1/2}(L)}\int_{x_+}^{L}(G^0_0+T^0_0)dx+{1 \over 12\pi\beta_H g^{1/2}(L)}+E_{surf},\end{align*}
\begin{align*}H^{(1)} = \int d^2x \left[ m \Phi^0 \partial_i A^i + \frac{m^2}{\sqrt{\kappa}} \epsilon_{ij} \Phi^i A^j + \sqrt{\kappa} \Phi^i \partial_i A^0 - \Phi^3 ( m A^0 - \frac{\kappa}{m} \epsilon_{ij} \partial^i A^j ) \right].\end{align*} | \begin{align*}H^{(1)}=\int d^2x \left[m \Phi^0 \partial_i A^i+\frac{m^2}{\sqrt{\kappa}} \epsilon_{ij} \Phi^i A^j+\sqrt{\kappa} \Phi^i \partial_i A^0-\Phi^3(m A^0-\frac{\kappa}{m} \epsilon_{ij} \partial^i A^j) \right].\end{align*}
\begin{align*}(\Psi_1,\Psi_2)=\int_{-\infty}^{+\infty}\Psi_1^*(x)\Psi_2(x)dx,\end{align*} | \begin{align*}(\Psi_1,\Psi_2)=\int_{-\infty}^{+\infty}\Psi_1^*(x)\Psi_2(x)dx,\end{align*}
\begin{align*}\mathbb P\left(\overline{X}_{e(q)}\in dz\right)=\sum_{k=1}^{M}C^q_{k}e^{-\beta_{k,q}z}dz,\end{align*} | \begin{align*}\mathbb P\left(\overline{X}_{e(q)}\in dz\right)=\sum_{k=1}^{M}C^q_{k}e^{-\beta_{k,q}z}dz,\end{align*}
\begin{align*} \lim_{q\to 1}(1-q)^{-n}P_n\bigl((1-q)\eta;q^{\alpha};q\bigr) =n!\,L^{(\alpha)}_n(\eta).\end{align*} | \begin{align*} \lim_{q\to 1}(1-q)^{-n}P_n\bigl((1-q)\eta;q^{\alpha};q\bigr)=n!\,L^{(\alpha)}_n(\eta).\end{align*}
\begin{align*}\nu = \sup\{\kappa_x \mid \exists \alpha \in A \exists p \in G [a^p(\alpha) = x]\}.\end{align*} | \begin{align*}\nu=\sup\{\kappa_x \mid \exists \alpha \in A \exists p \in G[a^p(\alpha)=x]\}.\end{align*}
\begin{align*} X^{(1)}_{m,h}(n;\mu)&=\begin{cases}1 & m\,|\,n \ \ \ \ h=\frac{n}{m}, \\0 & .\end{cases}\end{align*} | \begin{align*} X^{(1)}_{m,h}(n;\mu)&=\begin{cases}1 & m\,|\,n \ \ \ \ h=\frac{n}{m},\\0 &.\end{cases}\end{align*}
\begin{align*}\theta^{k-l}(is)\theta^l(st)\theta^{k-l}(is)&=\theta^k(it)\\(jt)\theta^k(it)(jt)&=\theta^k(ij),\end{align*} | \begin{align*}\theta^{k-l}(is)\theta^l(st)\theta^{k-l}(is)&=\theta^k(it)\\(jt)\theta^k(it)(jt)&=\theta^k(ij),\end{align*}
\begin{align*}\beta_{s,a^1}^1=\frac{\left[r^1(s,a^1,a_s^2)-r^1(s,a_s^1,a_s^2)\right]}{\left[r^1(s,a^1,a_{s}^2)-\sum_{s'\in S}p(s'|s,a^1,a_s^2)r^1(s',a_{s'}^1,a_{s'}^2)\right]}.\end{align*} | \begin{align*}\beta_{s,a^1}^1=\frac{\left[r^1(s,a^1,a_s^2)-r^1(s,a_s^1,a_s^2)\right]}{\left[r^1(s,a^1,a_{s}^2)-\sum_{s'\in S}p(s'|s,a^1,a_s^2)r^1(s',a_{s'}^1,a_{s'}^2)\right]}.\end{align*}
\begin{align*}M^k = \bigcup_{\underline{k}: \sum ik_i=k} M^k_{\underline{k}},\end{align*} | \begin{align*}M^k=\bigcup_{\underline{k}:\sum ik_i=k} M^k_{\underline{k}},\end{align*}
\begin{align*}\Phi_{1}(x) = \frac{4}{pq}x+O(x^{2})\end{align*} | \begin{align*}\Phi_{1}(x)=\frac{4}{pq}x+O(x^{2})\end{align*}
\begin{align*}&F_{\varepsilon,\delta}(t,x,w,p,X)=\min\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\\&F^{\varepsilon,\delta}(t,x,w,p,X)=\max\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\end{align*} | \begin{align*}&F_{\varepsilon,\delta}(t,x,w,p,X)=\min\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\\&F^{\varepsilon,\delta}(t,x,w,p,X)=\max\{F(t',x',w,p,X) \mid |t-t'|\le M\delta^{1/2},x'\in\overline{B(x,M\varepsilon^{1/2})}\},\end{align*}
\begin{align*}T(\sigma \cdot \sigma')(\alpha) = T(\sigma')\circ T(\sigma)(\alpha).\end{align*} | \begin{align*}T(\sigma \cdot \sigma')(\alpha)=T(\sigma')\circ T(\sigma)(\alpha).\end{align*}
\begin{align*}\dim(\Sigma(s,\theta)\cap \Xi(e_1,e_2)) = 1.\end{align*} | \begin{align*}\dim(\Sigma(s,\theta)\cap \Xi(e_1,e_2))=1.\end{align*}
\begin{align*} [(M,K,\xi);D]=[(M,K,\xi);D_h] \end{align*} | \begin{align*}[(M,K,\xi);D]=[(M,K,\xi);D_h] \end{align*}
\begin{align*}H_n^{(JW)}=\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n-1}\sum_{a,b=1}^2{\alpha}_{a,b,j}\sigma_{j}^{(a)}\sigma_{ j +1 }^{(b)}+\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n}{\alpha}_{3,0,j}\sigma_j^{(3)}\end{align*} | \begin{align*}H_n^{(JW)}=\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n-1}\sum_{a,b=1}^2{\alpha}_{a,b,j}\sigma_{j}^{(a)}\sigma_{j+1}^{(b)}+\frac{1}{\sqrt{\mathcal{C}}}\sum_{j=1}^{n}{\alpha}_{3,0,j}\sigma_j^{(3)}\end{align*}
\begin{align*}|\{\mu| \mu \in D, h_{1,1}(\mu)=r\}| = |\{\mu| \mu \in D, a_2(\mu)=r\}|\end{align*} | \begin{align*}|\{\mu| \mu \in D,h_{1,1}(\mu)=r\}|=|\{\mu| \mu \in D,a_2(\mu)=r\}|\end{align*}
\begin{align*}x\star_{1}^{n} x-y\star_{1}^{n} y=\frac{1}{3}\sqrt{\frac{15}{2}}\frac{\mu_{0}\mu_{2}}{\mu_{1}^{2}}(x^{2}-y^{2}),\end{align*} | \begin{align*}x\star_{1}^{n} x-y\star_{1}^{n} y=\frac{1}{3}\sqrt{\frac{15}{2}}\frac{\mu_{0}\mu_{2}}{\mu_{1}^{2}}(x^{2}-y^{2}),\end{align*}
\begin{align*} \hat f(p_k, q_k) := \frac 1{(2\pi)^2} \int d^2 x_k d^2y_k e^{-i(x_k \cdot p_k +y_k \cdot q_k)} f(x_k, y_k) . \end{align*} | \begin{align*} \hat{f(p_k,} q_k):=\frac{1{(2\pi)^2}}{\int} d^2 x_k d^2y_k e^{-i(x_k \cdot p_k+y_k \cdot q_k)} f(x_k,y_k).\end{align*}
\begin{align*}\langle v_1,v_1\rangle=3\alpha^2,\quad\langle v_1,v_2\rangle=\frac{1}{3}\langle\alpha^\perp,\alpha^\perp\rangle=\alpha^2,\quad\\\langle v_2,v_2\rangle=\frac{1}{9}(\langle\alpha^\perp,\alpha^\perp\rangle+\langle\beta^\perp,\beta^\perp\rangle)=\frac{\alpha^2+\beta^2}{3}.\end{align*} | \begin{align*}\langle v_1,v_1\rangle=3\alpha^2,\quad\langle v_1,v_2\rangle=\frac{1}{3}\langle\alpha^\perp,\alpha^\perp\rangle=\alpha^2,\quad\\\langle v_2,v_2\rangle=\frac{1}{9}(\langle\alpha^\perp,\alpha^\perp\rangle+\langle\beta^\perp,\beta^\perp\rangle)=\frac{\alpha^2+\beta^2}{3}.\end{align*}
\begin{align*}\overline{\rho}^{(2)}(\widetilde{M}) \doteq \sum_{i =1}^r \overline{\rho}^{(2)}(\widetilde{M_i}).\end{align*} | \begin{align*}\overline{\rho}^{(2)}(\widetilde{M}) \doteq \sum_{i=1}^r \overline{\rho}^{(2)}(\widetilde{M_i}).\end{align*}
\begin{align*}\Upsilon_\eta(A) = \bigcup_{i=-1}^{k-1} \bigcup_{\sigma \in X(i) \setminus S_\eta^i(A)} \Gamma^{k+1}(I^\sigma( E(I_\sigma(S_\eta^{i+1}(A)),I_\sigma(S_\eta^{i+1}(A)))))\end{align*} | \begin{align*}\Upsilon_\eta(A)=\bigcup_{i=-1}^{k-1} \bigcup_{\sigma \in X(i) \setminus S_\eta^i(A)} \Gamma^{k+1}(I^\sigma(E(I_\sigma(S_\eta^{i+1}(A)),I_\sigma(S_\eta^{i+1}(A)))))\end{align*}
\begin{align*}{\rm adj} \left( A + v u^{\sf T} \right) = {\rm adj}(A) +{\rm adj}(A) u^{\sf T} {\rm adj}(A) v -{\rm adj}(A) v u^{\sf T} {\rm adj}(A) \;.\end{align*} | \begin{align*}{\rm adj} \left(A+v u^{\sf T} \right)={\rm adj}(A)+{\rm adj}(A) u^{\sf T}{\rm adj}(A) v-{\rm adj}(A) v u^{\sf T}{\rm adj}(A) \;.\end{align*}
\begin{align*} &\sum^n_{\ell=0}t^{-\ell}\mathrm{STr\,}\alpha_\ell(x)dv_X(x) \\&=\frac{1}{2\pi}\left[\mathrm{Td_b\,}(\nabla^{T^{1,0}X},T^{1,0}X)\wedge\mathrm{ch_b\,}(\nabla^{E},E)\wedge e^{-m\frac{d\omega_0}{2\pi}}\wedge\omega_0\right]_{2n+1}(x).\end{align*} | \begin{align*} &\sum^n_{\ell=0}t^{-\ell}\mathrm{STr\,}\alpha_\ell(x)dv_X(x) \\&=\frac{1}{2\pi}\left[\mathrm{Td_b\,}(\nabla^{T^{1,0}X},T^{1,0}X)\wedge\mathrm{ch_b\,}(\nabla^{E},E)\wedge e^{-m\frac{d\omega_0}{2\pi}}\wedge\omega_0\right]_{2n+1}(x).\end{align*}
\begin{align*}E \hat{V}_{2}(Z\cap A)&= \gamma e^{-\gamma EV_3(K)}w^{(2)}Dc_1^\top EV_{2}(K)+O(a)\end{align*} | \begin{align*}E \hat{V}_{2}(Z\cap A)&=\gamma e^{-\gamma EV_3(K)}w^{(2)}Dc_1^\top EV_{2}(K)+O(a)\end{align*}
\begin{align*}\lim _{|z| \rightarrow \infty}z(\Psi^-_{11}(x;z)-e^{-ic_-(x)})=\widehat{\Psi}^-_{11}(x).\end{align*} | \begin{align*}\lim_{|z| \rightarrow \infty}z(\Psi^-_{11}(x;z)-e^{-ic_-(x)})=\widehat{\Psi}^-_{11}(x).\end{align*}
\begin{align*}L_\delta \omega_\delta=l_{1,\delta}+l_{2,\delta}+R_\delta(\omega_\delta),\end{align*} | \begin{align*}L_\delta \omega_\delta=l_{1,\delta}+l_{2,\delta}+R_\delta(\omega_\delta),\end{align*}
\begin{align*}\log\det (u_{,ij})=-v_jx^j+u_{,i}\xi^i+c,\end{align*} | \begin{align*}\log\det(u_{,ij})=-v_jx^j+u_{,i}\xi^i+c,\end{align*}
\begin{align*}\beta(a + 1) = \begin{cases} 1, & a + 1 \in \mathbb{P},\\ 0, & a + 1 \not \in \mathbb{P}. \end{cases}\end{align*} | \begin{align*}\beta(a+1)=\begin{cases} 1,& a+1 \in \mathbb{P},\\ 0,& a+1 \not \in \mathbb{P}.\end{cases}\end{align*}
\begin{align*}\mathcal{T}(x, \textbf{1}) = \log \log x+C(\textbf{1})+o(1), \end{align*} | \begin{align*}\mathcal{T}(x,\textbf{1})=\mathrm{log} \mathrm{log} x+C(\textbf{1})+o(1),\end{align*}
\begin{align*} \ P_{n}^{(\alpha,\beta)}(x) =\frac{(\alpha+1)_n}{n!} \sum_{k=0}^{\infty}\frac{(-n)_k(n+\alpha+\beta+1)_k}{k!\, (\alpha+1)_k} \left(\frac{1-x}{2}\right)^k, n=1,2,\dots.\end{align*} | \begin{align*} \ P_{n}^{(\alpha,\beta)}(x)=\frac{(\alpha+1)_n}{n!} \sum_{k=0}^{\infty}\frac{(-n)_k(n+\alpha+\beta+1)_k}{k!\,(\alpha+1)_k} \left(\frac{1-x}{2}\right)^k,n=1,2,. . ..\end{align*}
\begin{align*} B_m=\overset{1}{B}_m+\overset{2}{B}_m,\end{align*} | \begin{align*} B_m=\overset{1}{B}_m+\overset{2}{B}_m,\end{align*}
\begin{align*}\lim_{L\to\infty}\frac{1}{2L+1}|\{x\in[-L,L]:\sigma_{t_0}(x)=1\}|\end{align*} | \begin{align*}\lim_{L\to\infty}\frac{1}{2L+1}|\{x\in[-L,L]:\sigma_{t_0}(x)=1\}|\end{align*}
\begin{gather*}{\rm Hilb}\big(6T_n^{!},t\big)=\sum_{k=0}^{n-1} {n \brace n-k } t^{k}.\end{gather*} | \begin{gather*}{\rm Hilb}\big(6T_n^{!},t\big)=\sum_{k=0}^{n-1}{n \brace n-k} t^{k}.\end{gather*}
\begin{align*}\begin{pmatrix}\mathbf{f}(t)\\\mathbf{f}(t+\varepsilon)\\\vdots\\\mathbf{f}(t+(N-1)\varepsilon)\end{pmatrix}=\mathbf{D}\begin{pmatrix}\mathbf{u}(t+(N-1)\varepsilon)\\\vdots\\\mathbf{u}(t-N\varepsilon)\end{pmatrix} \end{align*} | \begin{align*}\begin{pmatrix}\mathbf{f}(t)\\\mathbf{f}(t+\varepsilon)\\\vdots\\\mathbf{f}(t+(N-1)\varepsilon)\end{pmatrix}=\mathbf{D}\begin{pmatrix}\mathbf{u}(t+(N-1)\varepsilon)\\\vdots\\\mathbf{u}(t-N\varepsilon)\end{pmatrix} \end{align*}
\begin{align*} \Pi(0;x)h = \lim_{n\to \infty} \nabla_x Y(0;x,n)(I-B)^{1-\theta}Pg[h] = \nabla_x Y(0;x)(I-B)^{1-\theta}[h] \end{align*} | \begin{align*} \Pi(0;x)h=\lim_{n\to \infty} \nabla_x Y(0;x,n)(I-B)^{1-\theta}Pg[h]=\nabla_x Y(0;x)(I-B)^{1-\theta}[h] \end{align*}
\begin{align*} d_1 \geq d_2 \geq \dots \geq d_n \geq 0 m_1 \geq m_2 \geq \dots \geq m_r \sum_{i=2}^n d_i \geq m_1 + m_2.\end{align*} | \begin{align*} d_1 \geq d_2 \geq . . . \geq d_n \geq 0 m_1 \geq m_2 \geq . . . \geq m_r \sum_{i=2}^n d_i \geq m_1+m_2.\end{align*}
\begin{align*}\tau\le C_2N^2+\sum_{i=0}^K C_3(i+1)^{-2}N^2\le T'.\end{align*} | \begin{align*}\tau\le C_2N^2+\sum_{i=0}^K C_3(i+1)^{-2}N^2\le T'.\end{align*}
\begin{align*}\sum_{i=1}^n \frac{\pi^2- \phi_i^2}{24 \pi \phi_i}=\frac{1}{6} \left(\frac{n-1}{n-2}\right).\end{align*} | \begin{align*}\sum_{i=1}^n \frac{\pi^2-\phi_i^2}{24 \pi \phi_i}=\frac{1}{6} \left(\frac{n-1}{n-2}\right).\end{align*}
\begin{align*}0\leq \phi(p_v)=\phi(s_\mu^* s_\mu)=e^{\beta|\mu|}\phi(s_\mu s_\mu^*)\leq e^{\beta|\mu|}\phi(p_{r(\mu)}).\end{align*} | \begin{align*}0\leq \phi(p_v)=\phi(s_\mu^*s_\mu)=e^{\beta|\mu|}\phi(s_\mu s_\mu^*)\leq e^{\beta|\mu|}\phi(p_{r(\mu)}).\end{align*}
\begin{align*} V_{r,t}^f=\{ \mathfrak e^f d x^{\kappa_d}\mid (d, \kappa_d) \in \mathcal {D}_{r,t}^f\times \mathbf N_f \}.\end{align*} | \begin{align*} V_{r,t}^f=\{\mathfrak{e^f} d x^{\kappa_d}\mid(d,\kappa_d) \in \mathcal{{D}_{r,t}^f\times} \mathbf{N_f} \}.\end{align*}
\begin{align*}B = \epsilon_{ij} \partial_{i} A^{j} = -\frac{1}{2e} {\nabla}^2 \ln \rho.\end{align*} | \begin{align*}B=\epsilon_{ij} \partial_{i} A^{j}=-\frac{1}{2e}{\nabla}^2 \mathrm{ln} \rho.\end{align*}
\begin{align*} \chi(j) = \left\{ \begin{array}{cl} 1 & {\rm if} \ \ j=2 \\ 2 & {\rm if} \ \ j=1 \end{array} \right. \ .\end{align*} | \begin{align*} \chi(j)=\left\{\begin{array}{cl} 1 &{\rm if} \ \ j=2 \\ 2 &{\rm if} \ \ j=1 \end{array} \right.\.\end{align*}
\begin{align*}b_{\scriptscriptstyle{l-1}}\,a^l=-\pi^{-2}\,(-1)^m\,i^{l}\, e^{i \delta_{\scriptscriptstyle{l}}}\,\Delta_{\scriptscriptstyle{l}}\,( 1 + \mbox{O} (\Delta_{\scriptscriptstyle{l}}^2) )\,.\end{align*} | \begin{align*}b_{\scriptscriptstyle{l-1}}\,a^l=-\pi^{-2}\,(-1)^m\,i^{l}\,e^{i \delta_{\scriptscriptstyle{l}}}\,\Delta_{\scriptscriptstyle{l}}\,(1+\text{CONTENTPROTECTED0}(\Delta_{\scriptscriptstyle{l}}^2))\,.\end{align*}
\begin{align*}\Phi(x)\rightarrow \Phi'(x')=\lambda^{-\Delta}\Phi(x) \,,\end{align*} | \begin{align*}\Phi(x)\rightarrow \Phi'(x')=\lambda^{-\Delta}\Phi(x) \,,\end{align*}
\begin{align*}X^{(I)}(0,\tau_0) = X^{(I)}(\sigma_0, \tau_0) = X^{(I)}(2\pi,\tau_0)\end{align*} | \begin{align*}X^{(I)}(0,\tau_0)=X^{(I)}(\sigma_0,\tau_0)=X^{(I)}(2\pi,\tau_0)\end{align*}
\begin{align*}\left. \frac{d V(T)}{d T} \right|_{T=0} = 0.\end{align*} | \begin{align*}\left.\frac{d V(T)}{d T} \right|_{T=0}=0.\end{align*}
\begin{align*}K^{H^3}_t(\sigma)=\frac{e^{-ta^2}e^{-\sigma^2/4t}}{(4\pi t)^{3/2}}\:,\end{align*} | \begin{align*}K^{H^3}_t(\sigma)=\frac{e^{-ta^2}e^{-\sigma^2/4t}}{(4\pi t)^{3/2}}\:,\end{align*}
\begin{align*}\sum_s \phi_s = 0 \, , \qquad n^a_1 \nabla_a \phi_1 = n^a_2 \nabla_a \phi_2 =\cdots = n^a_n \nabla_a \phi_n \, . \end{align*} | \begin{align*}\sum_s \phi_s=0 \,,\qquad n^a_1 \nabla_a \phi_1=n^a_2 \nabla_a \phi_2=. . .=n^a_n \nabla_a \phi_n \,.\end{align*}
\begin{align*}[T_a,T_b]= \varepsilon_{abc}T^c\; ,\end{align*} | \begin{align*}[T_a,T_b]=\varepsilon_{abc}T^c\;,\end{align*}
\begin{align*}dl^2= \overline g_{\, mn} \, d\sigma^m\, d\sigma^n= -e^2(\, \tau\, )\,\, d\tau^2 +h_{ij}(\, \vec s\, )\, \, ds^i\, \, ds^j\end{align*} | \begin{align*}dl^2=\overline{g_{\,} mn} \,d\sigma^m\,d\sigma^n=-e^2(\,\tau\,)\,\,d\tau^2+h_{ij}(\,\vec{s\,})\,\,ds^i\,\,ds^j\end{align*}
\begin{align*}E_{n,B}=\frac1{2\pi^2}\int_0^\Lambda\,k^{2n}C(k)_B \coth\frac{\beta W_k}2\,dk\ ,\qquad n\ge1\ .\end{align*} | \begin{align*}E_{n,B}=\frac1{2\pi^2}\int_0^\Lambda\,k^{2n}C(k)_B \coth\frac{\beta W_k}2\,dk\,\qquad n\ge1\.\end{align*}
\begin{align*}T_xT_x^*T_yT_y^*=\begin{cases}T_{x \vee y} T_{x \vee y}^*&\\0&\end{cases}\end{align*} | \begin{align*}T_xT_x^*T_yT_y^*=\begin{cases}T_{x \vee y} T_{x \vee y}^*&\\0&\end{cases}\end{align*}
\begin{align*}&|\partial^\zeta_x(\nabla \phi^2 \cdot Q(v))-\nabla \phi^2 \cdot \partial^\zeta_x Q(v)|_2\\\leq &C(|\nabla \phi|^2_\infty |\nabla v |_2+|\phi|_\infty|\nabla^2 \phi|_2 |\nabla v|_\infty\big)\leq Cc^3_3;\end{align*} | \begin{align*}&|\partial^\zeta_x(\nabla \phi^2 \cdot Q(v))-\nabla \phi^2 \cdot \partial^\zeta_x Q(v)|_2\\\leq &C(|\nabla \phi|^2_\infty |\nabla v |_2+|\phi|_\infty|\nabla^2 \phi|_2 |\nabla v|_\infty\big)\leq Cc^3_3;\end{align*}
\begin{align*}J = I \cap R_{N-1}.\end{align*} | \begin{align*}J=I \cap R_{N-1}.\end{align*}
\begin{align*} \rho(p,lr,ld+\delta)=l^2 r(d-r)-l(gr+r-d)+\delta \geq -lr(lr+2),\end{align*} | \begin{align*} \rho(p,lr,ld+\delta)=l^2 r(d-r)-l(gr+r-d)+\delta \geq-lr(lr+2),\end{align*}
\begin{align*}h(X^j_t,\bar{X}^j_t) = I_1 + I_2' + I_2'' + I_3 + I_4 + I_5 + \int_0^t \left( \frac{\partial h}{\partial x}b_2(X^j_s) + \frac{\partial h}{\partial y}b_2(\bar{X}^j_s)\right)dW^j_s. \end{align*} | \begin{align*}h(X^j_t,\bar{X}^j_t)=I_1+I_2'+I_2''+I_3+I_4+I_5+\int_0^t \left(\frac{\partial h}{\partial x}b_2(X^j_s)+\frac{\partial h}{\partial y}b_2(\bar{X}^j_s)\right)dW^j_s.\end{align*}
\begin{align*}\delta(P) = \liminf_{h\to\infty} \delta(P; Z),\end{align*} | \begin{align*}\delta(P)=\liminf_{h\to\infty} \delta(P;Z),\end{align*}
\begin{align*}\hat{c}:=\left\{\begin{aligned}&\lambda^c,& e\subset\partial K^{in},\\&c,& e\subset\partial K^{out}.\\\end{aligned}\right.\end{align*} | \begin{align*}\hat{c}:=\left\{\begin{aligned}&\lambda^c,& e\subset\partial K^{in},\\&c,& e\subset\partial K^{out}.\\\end{aligned}\right.\end{align*}
\begin{align*}E^x[g(X_{T_{ab}})]=g(x)+ \int_a^b \frac{(s(x\wedge y)-s(a))(s(b)-s(x\vee y))}{s(b)-s(a)}g(y)\mu_A(dy).\end{align*} | \begin{align*}E^x[g(X_{T_{ab}})]=g(x)+\int_a^b \frac{(s(x\wedge y)-s(a))(s(b)-s(x\vee y))}{s(b)-s(a)}g(y)\mu_A(dy).\end{align*}
\begin{align*} A = \left(\prod_{i=1}^n[a_i,c_i]\right) \times [0,a], B= \left(\prod_{i=1}^n[b_i,d_i]\right) \times [0,b]. \end{align*} | \begin{align*} A=\left(\prod_{i=1}^n[a_i,c_i]\right) \times[0,a],B=\left(\prod_{i=1}^n[b_i,d_i]\right) \times[0,b].\end{align*}
\begin{align*} \big[ I-(z-b)u_{1,j}^*R_j^*(\bar z)H_{1,j}^{-1}R_j(b)v_j \big] Q_{3,j}^{*}(b)-Q_{3,j}^{*}(z)=0. \end{align*} | \begin{align*} \big[I-(z-b)u_{1,j}^*R_j^*(\bar z)H_{1,j}^{-1}R_j(b)v_j \big] Q_{3,j}^{*}(b)-Q_{3,j}^{*}(z)=0.\end{align*}
\begin{align*}Q_C^1 \, Q_C^2 \leq {k^2 \over 4} \,.\end{align*} | \begin{align*}Q_C^1 \,Q_C^2 \leq{k^2 \over 4} \,.\end{align*}
\begin{align*}\int _{0} ^{1} dv'(z-v)^{k_{0}.q_{0}}(1-v')^{p_{0}.q_{0}}(v')^{q_{0}.l_{0}-2}\end{align*} | \begin{align*}\int_{0}^{1} dv'(z-v)^{k_{0}.q_{0}}(1-v')^{p_{0}.q_{0}}(v')^{q_{0}.l_{0}-2}\end{align*}
\begin{align*}X_d=2ig_L\sum_{\Xi\in\Delta_L}x_d(\Xi\cdot q, \xi)E_d(\Xi),\quad Y_d=ig_L\sum_{\Xi\in\Delta_L}y_d(\Xi\cdot q, \xi)E_d(\Xi),\quad E_d(\Xi)_{\Upsilon \Omega}=\delta_{\Upsilon-\Omega,2\Xi}, \end{align*} | \begin{align*}X_d=2ig_L\sum_{\Xi\in\Delta_L}x_d(\Xi\cdot q,\xi)E_d(\Xi),\quad Y_d=ig_L\sum_{\Xi\in\Delta_L}y_d(\Xi\cdot q,\xi)E_d(\Xi),\quad E_d(\Xi)_{\Upsilon \Omega}=\delta_{\Upsilon-\Omega,2\Xi},\end{align*}
\begin{align*}\left<T^\mu{}_\mu(x)\right>={a^{-N}\over 2^{N-1}\pi^{N/2}\Gamma \left(\frac N2\right)} \sum_{n=0}^{\frac N2-1}c^N_{2n+1}\left[ \frac{(-1)^{n+1}}{2(n+1)}4^{-n-1}-2H_n(0)\right].\end{align*} | \begin{align*}\left<T^\mu_\mu(x)\right>={a^{-N}\over 2^{N-1}\pi^{N/2}\Gamma \left(\frac N2\right)} \sum_{n=0}^{\frac N2-1}c^N_{2n+1}\left[\frac{(-1)^{n+1}}{2(n+1)}4^{-n-1}-2H_n(0)\right].\end{align*}
\begin{align*}1=\alpha_{1}^{2}=\alpha_{2}^{2}=\alpha_{3}^{2}\,.\end{align*} | \begin{align*}1=\alpha_{1}^{2}=\alpha_{2}^{2}=\alpha_{3}^{2}\,.\end{align*}
\begin{align*}\omega ^2=k^2, \quad k^2 \equiv {{n^2} \over {a^2}} ,\end{align*} | \begin{align*}\omega^2=k^2,\quad k^2 \equiv{{n^2} \over{a^2}},\end{align*}
\begin{align*}ds^2=\frac{1}{H^2}[-(dq^0)^2+\sum_{i=1}^{3}(dq^{i})^2],\;\;\;\;\;\;\;\;\eta_{\mu\nu}q^\mu q^\nu=1.\end{align*} | \begin{align*}ds^2=\frac{1}{H^2}[-(dq^0)^2+\sum_{i=1}^{3}(dq^{i})^2],\;\;\;\;\;\;\;\;\eta_{\mu\nu}q^\mu q^\nu=1.\end{align*}
\begin{align*}\phi _{\mathbf{k}}\left( x\right) =A\phi _{\mathbf{k,}BD}\left( x\right)+B\phi _{\mathbf{k,}BD}\left( \overline{x}\right) ,\end{align*} | \begin{align*}\phi_{\mathbf{k}}\left(x\right)=A\phi_{\mathbf{k,}BD}\left(x\right)+B\phi_{\mathbf{k,}BD}\left(\overline{x}\right),\end{align*}
\begin{align*}\omega_0(x)^2-g_s\left(\frac{\omega_0(x)}{x}-\omega_0'(x)\right)+f(x)-2\omega_0(x)W'(x) =0, \end{align*} | \begin{align*}\omega_0(x)^2-g_s\left(\frac{\omega_0(x)}{x}-\omega_0'(x)\right)+f(x)-2\omega_0(x)W'(x)=0,\end{align*}
\begin{align*}{\cal L} = {\cal L}_0 + {\cal L}_1 + {\cal L}_2.\end{align*} | \begin{align*}{\cal L}={\cal L}_0+{\cal L}_1+{\cal L}_2.\end{align*}
\begin{align*}{SO(n\!-\!1,1)\over SO(n\!-\!1)}\ .\qquad (n\geq 3)\end{align*} | \begin{align*}{SO(n\!-\!1,1)\over SO(n\!-\!1)}\.\qquad(n\geq 3)\end{align*}
\begin{align*} R_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}&=\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}})-\dim(\overline{H}_{\bar{f}|_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}}^{\perp_{\bar{f}}})\\ &=R_f-(l_0-1)-\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}}\bigcap (\overline{H}_{l_0-1}^{\perp_{\bar{f}}})^{\perp_{\bar{f}}})\\ &=R_f-2(l_0-1)\geq 2.\end{align*} | \begin{align*} R_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}&=\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}})-\dim(\overline{H}_{\bar{f}|_{\overline{H}_{l_0-1}^{\perp_{\bar{f}}}}}^{\perp_{\bar{f}}})\\ &=R_f-(l_0-1)-\dim(\overline{H}_{l_0-1}^{\perp_{\bar{f}}}\bigcap(\overline{H}_{l_0-1}^{\perp_{\bar{f}}})^{\perp_{\bar{f}}})\\ &=R_f-2(l_0-1)\geq 2.\end{align*}
\begin{align*}\mathcal{N}(T)&=||\mathbf{F}||^2_{s,\ast,T}+(||\dot{{\mathbf U}}||^2_{W^{2,\infty}_{\ast}(\Omega_T)}+||\varphi||^2_{W^{2,\infty}(\Gamma_T)}+||\mathbf{F}||^2_{W^{1,\infty}_{\ast}(\Omega_T)})||\hat{W}||^2_{s+4,\ast,T}.\end{align*} | \begin{align*}\mathcal{N}(T)&=||\mathbf{F}||^2_{s,\ast,T}+(||\dot{{\mathbf U}}||^2_{W^{2,\infty}_{\ast}(\Omega_T)}+||\varphi||^2_{W^{2,\infty}(\Gamma_T)}+||\mathbf{F}||^2_{W^{1,\infty}_{\ast}(\Omega_T)})||\hat{W}||^2_{s+4,\ast,T}.\end{align*}
\begin{align*}C_{p,q}^{(\nu)}(t) \coloneqq \begin{cases}C e^{-td^\nu} & (t\ge 1) \\ C t^{-\mu_\nu} & (0<t\le 1), \end{cases} \mu_\nu \coloneqq \max \Big\{ \frac{d}{\nu} \Big(\frac{1}{\min\{q,q'\}}-\frac{1}{\max\{p,p'\}} \Big) , 0\Big\},\end{align*} | \begin{align*}C_{p,q}^{(\nu)}(t) \coloneqq \begin{cases}C e^{-td^\nu} &(t\ge 1) \\ C t^{-\mu_\nu} &(0<t\le 1),\end{cases} \mu_\nu \coloneqq \mathrm{max} \Big\{\frac{d}{\nu} \Big(\frac{1}{\min\{q,q'\}}-\frac{1}{\max\{p,p'\}} \Big),0\Big\},\end{align*}
\begin{align*}2^{J_i}=2^{\mathfrak{k}_J}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}=2^{k}\prod_{j=1}^{J}(\mathfrak{s}_j)^{\mathfrak{d}_j}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}.\end{align*} | \begin{align*}2^{J_i}=2^{\mathfrak{k}_J}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}=2^{k}\prod_{j=1}^{J}(\mathfrak{s}_j)^{\mathfrak{d}_j}\prod_{j:\mathfrak{d}_j\leq i}(\mathfrak{s}_j)^{i-\mathfrak{d}_j}.\end{align*}
\begin{align*}\xi u + E_{\lambda}( u_{0}, v_{0} )\leq - \int_{0}^{u} H_{\lambda}(s) \, ds + E_{\lambda}( u_{0}, v_{0} )= \frac{1}{2} u_{t}^2,\end{align*} | \begin{align*}\xi u+E_{\lambda}(u_{0},v_{0})\leq-\int_{0}^{u} H_{\lambda}(s) \,ds+E_{\lambda}(u_{0},v_{0})=\frac{1}{2} u_{t}^2,\end{align*}
\begin{align*}\sigma_{g^{-1}}(y^*)_{((i',j'),1_H)} & = (y^*)_{g((i',j'),1_H)} \\& = (y^*)_{((i,j)+\varphi_h(i',j'),h)} \\& = (y^{h^{-1}})_{(i',j')+\varphi_{h^{-1}}(i,j)} \\& = (\sigma_{-\varphi_{h^{-1}}(i,j)}(y^{h^{-1}}))_{(i',j')}.\end{align*} | \begin{align*}\sigma_{g^{-1}}(y^*)_{((i',j'),1_H)} &=(y^*)_{g((i',j'),1_H)} \\&=(y^*)_{((i,j)+\varphi_h(i',j'),h)} \\&=(y^{h^{-1}})_{(i',j')+\varphi_{h^{-1}}(i,j)} \\&=(\sigma_{-\varphi_{h^{-1}}(i,j)}(y^{h^{-1}}))_{(i',j')}.\end{align*}
\begin{align*}\tilde L_m B_n =\sum_{k=1}^m \left[B_k,B_{m+n-k}\right] + nB_{m+n} n\geq 0, m\geq -1.\end{align*} | \begin{align*}\tilde L_m B_n=\sum_{k=1}^m \left[B_k,B_{m+n-k}\right]+nB_{m+n} n\geq 0,m\geq-1.\end{align*}
\begin{align*}x^{\ell_1}_\gamma = x^{\ell_2}_\gamma,\forall \ell_1,\ell_2: \gamma \in \mathcal{I}_{\ell_1}^1\cap \mathcal{I}_{\ell_2}^1.\end{align*} | \begin{align*}x^{\ell_1}_\gamma=x^{\ell_2}_\gamma,\forall \ell_1,\ell_2:\gamma \in \mathcal{I}_{\ell_1}^1\cap \mathcal{I}_{\ell_2}^1.\end{align*}
\begin{align*}\Delta(\lambda)\;=\;\Delta_{He^0_\lambda}\;=\; A\otimes_B He^0_\lambda,\qquad\mbox{and}\overline{\Delta}(\lambda)\;=\;\Delta_{L^0(\lambda)}\;=\; A\otimes_B L^0(\lambda).\end{align*} | \begin{align*}\Delta(\lambda)\;=\;\Delta_{He^0_\lambda}\;=\;A\otimes_B He^0_\lambda,\qquad\text{CONTENTPROTECTED0}\overline{\Delta}(\lambda)\;=\;\Delta_{L^0(\lambda)}\;=\;A\otimes_B L^0(\lambda).\end{align*}
\begin{align*}\mathcal{A}_4:=\{ (j_1, j_2, j_3, j_4)\in \mathbb{Z}^4\setminus\{\textbf{0}\}\, :\, &j_1+j_2+j_3+j_4=0, j_1^3+j_2^3+j_3^3+j_4^3\neq 0,\\& \mbox{and at most one among}\,\,j_1, j_2, j_3, j_4\,\,\mbox{outside}\,\,S\}.\end{align*} | \begin{align*}\mathcal{A}_4:=\{(j_1,j_2,j_3,j_4)\in \mathbb{Z}^4\setminus\{\textbf{0}\}\,:\,&j_1+j_2+j_3+j_4=0,j_1^3+j_2^3+j_3^3+j_4^3\neq 0,\\&\text{CONTENTPROTECTED0}\,\,j_1,j_2,j_3,j_4\,\,\text{CONTENTPROTECTED1}\,\,S\}.\end{align*}
\begin{align*} \gamma'(d)=\frac{D}{d}\sum_{(D,w)D\mid cd}\chi(w/(cd,w))(cd,w)\mu(c)c^{-2}.\end{align*} | \begin{align*} \gamma'(d)=\frac{D}{d}\sum_{(D,w)D\mid cd}\chi(w/(cd,w))(cd,w)\mu(c)c^{-2}.\end{align*}
\begin{align*} \frac{T}{4}\int_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon}\log \left(\frac{\sigma^2+T^2}{4}\right)\ d\sigma=\frac{T}{4}\left[\sigma\log \left(\frac{\sigma^2+T^2}{4}\right)-2\sigma+2T\arctan\left(\frac{\sigma}{T}\right)\right]\Biggr|_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon} \end{align*} | \begin{align*} \frac{T}{4}\int_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon}\log \left(\frac{\sigma^2+T^2}{4}\right)\ d\sigma=\frac{T}{4}\left[\sigma\log \left(\frac{\sigma^2+T^2}{4}\right)-2\sigma+2T\arctan\left(\frac{\sigma}{T}\right)\right]\Biggr|_{\frac{1}{2}-\epsilon}^{\frac{1}{2}+\epsilon} \end{align*}
\begin{align*}\tau_t(a_n\cdots a_1vb_1\cdots b_m)=t\cdot \tau(a_n\cdots a_1\tau'(v)b_1\cdots b_m)+o(t)\end{align*} | \begin{align*}\tau_t(a_n. . . a_1vb_1. . . b_m)=t\cdot \tau(a_n. . . a_1\tau'(v)b_1. . . b_m)+o(t)\end{align*}
\begin{align*}G_B(R) = (B \otimes_F R)^\times\end{align*} | \begin{align*}G_B(R)=(B \otimes_F R)^\times\end{align*}
\begin{align*}\tilde{m}_n=\begin{cases}m_n-q_{n-1}, \ell_n=1\\m_n+q_{n-1}, \ell_n=-1\end{cases}.\end{align*} | \begin{align*}\tilde{m}_n=\begin{cases}m_n-q_{n-1},\ell_n=1\\m_n+q_{n-1},\ell_n=-1\end{cases}.\end{align*}
\begin{align*}|\varphi(z)| & \leq C_{\varepsilon}\mathrm{e}^{\varepsilon|z|}\left(\sum_{n=0}^{\infty}(n!)^{2}2^{nq}|\varphi_{n}|^{2}\right)^{1/2}\left(\sum_{n=0}^{\infty}2^{-nq}\sigma_{\varepsilon}^{-2n}\right)^{1/2} \\ & =C_{\varepsilon}(1-2^{-q}\sigma_{\varepsilon}^{-2})^{-1/2}\|\varphi\|_{q,1,\pi_{\lambda,\beta}}\mathrm{e}^{\varepsilon|z|},\end{align*} | \begin{align*}|\varphi(z)| & \leq C_{\varepsilon}\mathrm{e}^{\varepsilon|z|}\left(\sum_{n=0}^{\infty}(n!)^{2}2^{nq}|\varphi_{n}|^{2}\right)^{1/2}\left(\sum_{n=0}^{\infty}2^{-nq}\sigma_{\varepsilon}^{-2n}\right)^{1/2} \\ &=C_{\varepsilon}(1-2^{-q}\sigma_{\varepsilon}^{-2})^{-1/2}\|\varphi\|_{q,1,\pi_{\lambda,\beta}}\mathrm{e}^{\varepsilon|z|},\end{align*}
\begin{align*}a_{n,p,10}= \sum_{s=1}^n \binom{n-1}{s} r_{s,p,10} = \sum_{s=1}^n \binom{n-1}{s-1} \binom{p+s-2}{s-1}=\sum_{s=0}^{n-1} \binom{n-1}{s} \binom{p+s-1}{s}.\end{align*} | \begin{align*}a_{n,p,10}=\sum_{s=1}^n \binom{n-1}{s} r_{s,p,10}=\sum_{s=1}^n \binom{n-1}{s-1} \binom{p+s-2}{s-1}=\sum_{s=0}^{n-1} \binom{n-1}{s} \binom{p+s-1}{s}.\end{align*}
\begin{align*} \begin{aligned} \Delta A_0 \ = \ J_0, \Delta \partial_t A_0 \ = \ \nabla^i J_i. \end{aligned}\end{align*} | \begin{align*} \begin{aligned} \Delta A_0 \=\ J_0,\Delta \partial_t A_0 \=\ \nabla^i J_i.\end{aligned}\end{align*}
\begin{align*}\delta (s)=\left( s,\int \left( \int \cosh (bs)\kappa (s)\,ds\right) \,ds,\int \left(\int \sinh (bs)\kappa (s)\,ds\right) \,ds\right).\end{align*} | \begin{align*}\delta(s)=\left(s,\int \left(\int \mathrm{cosh}(bs)\kappa(s)\,ds\right) \,ds,\int \left(\int \mathrm{sinh}(bs)\kappa(s)\,ds\right) \,ds\right).\end{align*}
\begin{align*} \tilde X^{(\lambda)}(\infty) = \delta (\Pi_X(U^{(\lambda)}(\infty))-\gamma n).\end{align*} | \begin{align*} \tilde{X^{(\lambda)}(\infty)}=\delta(\Pi_X(U^{(\lambda)}(\infty))-\gamma n).\end{align*}
\begin{align*}\begin{multlined}[t][12.5cm]\Psi(x,t,g,\vartheta)=t\psi\left(\tilde{\mu}_{g^{-1}}^{G}\circ\tilde{\mu}_{-\vartheta}^{T}(x),x\right)-\nu_{T}\cdot\vartheta.\end{multlined}\end{align*} | \begin{align*}\begin{multlined}[t][12.5cm]\Psi(x,t,g,\vartheta)=t\psi\left(\tilde{\mu}_{g^{-1}}^{G}\circ\tilde{\mu}_{-\vartheta}^{T}(x),x\right)-\nu_{T}\cdot\vartheta.\end{multlined}\end{align*}
\begin{align*} \lim_{k\to\infty} C^{n_k} \max_{0 \leq j \leq n_k - 1}|\alpha(j) - \alpha(j \pm n_k)|=0\end{align*} | \begin{align*} \lim_{k\to\infty} C^{n_k} \max_{0 \leq j \leq n_k-1}|\alpha(j)-\alpha(j \pm n_k)|=0\end{align*}
\begin{align*}\frac{\partial \phi_b}{\partial x}=W_b~\frac{\partial f}{\partial x}+V_b~\frac{\partial g}{\partial x};~~~\frac{\partial \phi_c}{\partial y}=W_c~\frac{\partial f}{\partial y}+V_c~\frac{\partial g}{\partial y}\end{align*} | \begin{align*}\frac{\partial \phi_b}{\partial x}=W_b~\frac{\partial f}{\partial x}+V_b~\frac{\partial g}{\partial x};~~~\frac{\partial \phi_c}{\partial y}=W_c~\frac{\partial f}{\partial y}+V_c~\frac{\partial g}{\partial y}\end{align*}
\begin{align*}(h^**k)(yx^{-1}) = \sum_z \bar h(z^{-1})k(z^{-1}yx^{-1}) = \sum_z \bar h(z^{-1}y^{-1})k(z^{-1}x^{-1}).\end{align*} | \begin{align*}(h^**k)(yx^{-1})=\sum_z \bar{h(z^{-1})k(z^{-1}yx^{-1})}=\sum_z \bar{h(z^{-1}y^{-1})k(z^{-1}x^{-1}).\end{align*}}
\begin{align*}\int_{D \setminus D_{t} } \langle A \nabla u_0, \nabla u_0 \rangle - k^2 \int_{D \setminus D_{t} } \Sigma |u_0|^2 - \int_{\partial D_t \setminus \Gamma} A \nabla u_0 \cdot \nu \; \bar u_0 = 0. \end{align*} | \begin{align*}\int_{D \setminus D_{t}} \langle A \nabla u_0,\nabla u_0 \rangle-k^2 \int_{D \setminus D_{t}} \Sigma |u_0|^2-\int_{\partial D_t \setminus \Gamma} A \nabla u_0 \cdot \nu \;\bar{u_0}=0.\end{align*}
\begin{align*}V_{\psi}&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\gamma^i(\overline{\nabla}_i\psi)+\psi^2-\frac{\gamma^i(\overline{\nabla}_i\psi)+(\overline{\nabla}_i\psi)\gamma^i}{2}+L^iL_i\\&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\frac{1}{2}[\gamma^i,\overline{\nabla}_i\psi]+\psi^2+L^iL_i.\end{align*} | \begin{align*}V_{\psi}&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\gamma^i(\overline{\nabla}_i\psi)+\psi^2-\frac{\gamma^i(\overline{\nabla}_i\psi)+(\overline{\nabla}_i\psi)\gamma^i}{2}+L^iL_i\\&=\gamma^i\gamma^j\frac{R_{ij}^{\widetilde{\nabla}}}{2}+\frac{1}{2}[\gamma^i,\overline{\nabla}_i\psi]+\psi^2+L^iL_i.\end{align*}
\begin{align*}\mathcal L(\lambda):&=\sum_{k\geq 0\atop i_1\geq \dots\geq i_k, i_j\in\mathbb Z}\mathbb Ax^-_{i_1}\cdots x^-_{i_k}v_\lambda\subset \mathcal N_q^-v_\lambda=\tilde M_q(\lambda)\end{align*} | \begin{align*}\mathcal L(\lambda):&=\sum_{k\geq 0\atop i_1\geq . . .\geq i_k,i_j\in\mathbb Z}\mathbb Ax^-_{i_1}. . . x^-_{i_k}v_\lambda\subset \mathcal{N_q^-v_\lambda=\tilde} M_q(\lambda)\end{align*}
\begin{align*}\frac{\mathbb E_G\gamma_G}{v(G)} =\frac12\int\frac{x^2}{1+x^2}\mathrm d\rho_G(x).\end{align*} | \begin{align*}\frac{\mathbb E_G\gamma_G}{v(G)}=\frac12\int\frac{x^2}{1+x^2}\mathrm d\rho_G(x).\end{align*}
\begin{align*}\partial_1 \, Q_k^n(x,y) & = (n-k) \left[P_k^{n-1}(x,y) + k\, P_{k-1}^{n-2}(x,y)\right], \\\partial_2 \, Q_k^n(x,y) & = k \left[P_{k-1}^{n-1}(x,y) + (n-k) \,P_{k-1}^{n-2}(x,y)\right].\end{align*} | \begin{align*}\partial_1 \,Q_k^n(x,y) &=(n-k) \left[P_k^{n-1}(x,y)+k\,P_{k-1}^{n-2}(x,y)\right],\\\partial_2 \,Q_k^n(x,y) &=k \left[P_{k-1}^{n-1}(x,y)+(n-k) \,P_{k-1}^{n-2}(x,y)\right].\end{align*}
\begin{align*} k_m=d_1+d_2,\qquad{k_m^S=d_2},\end{align*} | \begin{align*} k_m=d_1+d_2,\qquad{k_m^S=d_2},\end{align*}
\begin{align*}I(1-s,[\rho(d(h))\varphi]')=|\det h|^{-s}I(1-s,\varphi').\end{align*} | \begin{align*}I(1-s,[\rho(d(h))\varphi]')=|\det h|^{-s}I(1-s,\varphi').\end{align*}
\begin{align*}w_{r_k}(y):= \frac{w(r_k y)}{ r_k^{-\frac{n+1}{2}} r_k^{\frac{2s-1}{2}} \left\| y_{n+1}^{\frac{1-2s}{2}} w \right\|_{L^2(B_{r_k}^+)} }.\end{align*} | \begin{align*}w_{r_k}(y):=\frac{w(r_k y)}{r_k^{-\frac{n+1}{2}} r_k^{\frac{2s-1}{2}} \left\| y_{n+1}^{\frac{1-2s}{2}} w \right\|_{L^2(B_{r_k}^+)}}.\end{align*}
\begin{align*}R^{i}\pi'_{*}(\bigwedge^{p}(\check{\mathcal{E}})\otimes p^{*}(Q))=0\end{align*} | \begin{align*}R^{i}\pi'_{*}(\bigwedge^{p}(\check{\mathcal{E}})\otimes p^{*}(Q))=0\end{align*}
\begin{align*}\hat{\psi}(p,t)=\mathcal{F}\left\{\psi(x,t);p\right\}=\frac{1}{2\pi{}\hslash{}}\int_{-\infty{}}^{\infty{}}e^{-ipx/\hslash{}}\\psi(x,t)\ dx\end{align*} | \begin{align*}\hat{\psi}(p,t)=\mathcal{F}\left\{\psi(x,t);p\right\}=\frac{1}{2\pi\hslash}\int_{-\infty}^{\infty}e^{-ipx/\hslash}\\psi(x,t)\ dx\end{align*}
\begin{align*} \mathcal{I}_{2,+}(t,x) = \int_0^t \int_0^{\infty} \int_y^{\infty} \frac{\partial G_{t-s}}{\partial x}(x-z) \psi(s,z)\sigma_s(y) dz W(ds,dy)\end{align*} | \begin{align*} \mathcal{I}_{2,+}(t,x)=\int_0^t \int_0^{\infty} \int_y^{\infty} \frac{\partial G_{t-s}}{\partial x}(x-z) \psi(s,z)\sigma_s(y) dz W(ds,dy)\end{align*}
\begin{align*}h_{R_f}=h_K {f \over e_f}\prod_{p|f}\left(1-\left({\Delta\over p}\right){1\over p}\right),\end{align*} | \begin{align*}h_{R_f}=h_K{f \over e_f}\prod_{p|f}\left(1-\left({\Delta\over p}\right){1\over p}\right),\end{align*}
\begin{align*} I+C_\Sigma^+ A_-\big(v_A v_B^{-1} - I\big)B_-^{-1} - A_+B_+^{-1} = I+C_\Sigma^- A_-\big(v_A v_B^{-1} - I\big)B_-^{-1} - A_-B_-^{-1}. \end{align*} | \begin{align*} I+C_\Sigma^+A_-\big(v_A v_B^{-1}-I\big)B_-^{-1}-A_+B_+^{-1}=I+C_\Sigma^-A_-\big(v_A v_B^{-1}-I\big)B_-^{-1}-A_-B_-^{-1}.\end{align*}
\begin{align*} g(\gamma) - C(\gamma) = p(\{n_{i, k}\}, j).\end{align*} | \begin{align*} g(\gamma)-C(\gamma)=p(\{n_{i,k}\},j).\end{align*}
\begin{align*}Y = \mu + \sigma A W + \sigma \alpha S + \sigma [B W ]^{\frac{1}{2}} U,\end{align*} | \begin{align*}Y=\mu+\sigma A W+\sigma \alpha S+\sigma[B W]^{\frac{1}{2}} U,\end{align*}
\begin{align*}r_2= 2-\beta-\beta_1, p_2=\frac{2}{r_2}=\frac{2}{2-\beta-\beta_1}, q_2= \frac{2}{1-r_2}=\frac{2}{\beta+\beta_1-1}. \end{align*} | \begin{align*}r_2=2-\beta-\beta_1,p_2=\frac{2}{r_2}=\frac{2}{2-\beta-\beta_1},q_2=\frac{2}{1-r_2}=\frac{2}{\beta+\beta_1-1}.\end{align*}
\begin{align*}&( u_0,u_{t_1}^*u_{t_1},....,u_{t_N}^*u_{t_N},1_{mN}) \\ &=( 1_m,u_{t_1}^*,....,u_{t_N}^*,1_{mN})( u_0,u_{t_1},....,u_{t_N},1_{mN})\end{align*} | \begin{align*}&(u_0,u_{t_1}^*u_{t_1},....,u_{t_N}^*u_{t_N},1_{mN}) \\ &=(1_m,u_{t_1}^*,....,u_{t_N}^*,1_{mN})(u_0,u_{t_1},....,u_{t_N},1_{mN})\end{align*}
\begin{align*} \hat{s}'(g) = 1\end{align*} | \begin{align*} \hat{s}'(g)=1\end{align*}
\begin{align*}\hat \Delta \circ \hat \sigma_t = (\hat \tau_{t} \otimes \hat \sigma_{t})\hat \Delta,\end{align*} | \begin{align*}\hat \Delta \circ \hat{\sigma_t}=(\hat \tau_{t} \otimes \hat{\sigma_{t})\hat} \Delta,\end{align*}
\begin{align*}2(\Delta u)'(1)u'(1)+(1-\sigma)(1+\sigma)(u'(1))^2=-\dfrac{p+3}{p+1}\dfrac{1}{\pi}\int_B u^{p+1}\end{align*} | \begin{align*}2(\Delta u)'(1)u'(1)+(1-\sigma)(1+\sigma)(u'(1))^2=-\dfrac{p+3}{p+1}\dfrac{1}{\pi}\int_B u^{p+1}\end{align*}
\begin{align*} \tilde{y} &= y = y',\\ \mu_k(\tilde{z})&=\mu_k(z)=\mu_k(z'),\\ \sigma_k(\tilde{z}) &= (\max(s_0,s'_0),\dots,\max(s_r,s'_r)). \end{align*} | \begin{align*} \tilde{y} &=y=y',\\ \mu_k(\tilde{z})&=\mu_k(z)=\mu_k(z'),\\ \sigma_k(\tilde{z}) &=(\max(s_0,s'_0),. . .,\max(s_r,s'_r)).\end{align*}
\begin{align*}\| b_\ell \|_X \le (\tilde\lambda+C_Br)\|e_\ell\|_D+2C_B\|u_\ell^\star\|_D \|e_\ell\|_W,\ell=0,\dotsc,M-1 .\end{align*} | \begin{align*}\| b_\ell \|_X \le(\tilde\lambda+C_Br)\|e_\ell\|_D+2C_B\|u_\ell^\star\|_D \|e_\ell\|_W,\ell=0,. . .c,M-1.\end{align*}
\begin{align*}C\::\: x^py^q(ax+by+cz)^r-z^{p+q+r}=0,\end{align*} | \begin{align*}C\::\:x^py^q(ax+by+cz)^r-z^{p+q+r}=0,\end{align*}
\begin{align*}F\left( 0,x\right) =\frac{1}{x}.\end{align*} | \begin{align*}F\left(0,x\right)=\frac{1}{x}.\end{align*}
\begin{align*}O_j^{h_1^{-1}}\subseteq(H\cap P_i^{g_j})^{h_1^{-1}}=H^{h_1^{-1}}\cap P_i^{p''g}=H\cap P_i^g\end{align*} | \begin{align*}O_j^{h_1^{-1}}\subset eq(H\cap P_i^{g_j})^{h_1^{-1}}=H^{h_1^{-1}}\cap P_i^{p''g}=H\cap P_i^g\end{align*}
\begin{align*}\Sigma_\omega=\{\sigma_I: \bar{I}\in \mathcal A_\omega\}.\end{align*} | \begin{align*}\Sigma_\omega=\{\sigma_I:\bar{I}\in \mathcal{A_\omega\}.\end{align*}}
\begin{align*}K_{(10)}^{(\ell+1)} =K_{(10)}^{(1)}\prod_{\ell'=1}^\ell \chi_{||}^{(\ell')}.\end{align*} | \begin{align*}K_{(10)}^{(\ell+1)}=K_{(10)}^{(1)}\prod_{\ell'=1}^\ell \chi_{||}^{(\ell')}.\end{align*}
\begin{align*}\sigma_{} (\mathcal{P}) = [0, + \infty).\end{align*} | \begin{align*}\sigma_(\mathcal{P})=[0,+\infty).\end{align*}
\begin{align*} \sum_{n \leq x / d^{2} s,\,(n, s) = 1} a(d^{2} n s + k) = \sum_{\delta \mid s}\mu (\delta) \ \ \sum_{ \delta n_{1} \leq x/ d^{2} s} a( d^{2}\, \delta\, n_{1}\, s + k).\end{align*} | \begin{align*} \sum_{n \leq x/d^{2} s,\,(n,s)=1} a(d^{2} n s+k)=\sum_{\delta \mid s}\mu(\delta) \ \ \sum_{\delta n_{1} \leq x/d^{2} s} a(d^{2}\,\delta\,n_{1}\,s+k).\end{align*}
\begin{align*}N_J \le 2^{-js(2-\alpha)} 2^{js(1+\alpha)} = 2^{js(2\alpha-1)}.\end{align*} | \begin{align*}N_J \le 2^{-js(2-\alpha)} 2^{js(1+\alpha)}=2^{js(2\alpha-1)}.\end{align*}
\begin{align*} \sum_{n=1}^Me^{2\pi \textnormal{i}h(n)\alpha}\end{align*} | \begin{align*} \sum_{n=1}^Me^{2\pi \textnormal{i}h(n)\alpha}\end{align*}
\begin{align*}\left\{\begin{array}{ll}-\Delta_p u=\lambda |u|^{p-2}u-g(x,u) & \mbox{in }\Omega,\\\displaystyle{\frac{\partial u}{\partial n_p}}+\beta(x)|u|^{p-2}u=0 & \mbox{on } \partial\Omega,\\\end{array}\right.\end{align*} | \begin{align*}\left\{\begin{array}{ll}-\Delta_p u=\lambda |u|^{p-2}u-g(x,u) &\text{CONTENTPROTECTED0}\Omega,\\\displaystyle{\frac{\partial u}{\partial n_p}}+\beta(x)|u|^{p-2}u=0 &\text{CONTENTPROTECTED1}\partial\Omega,\\\end{array}\right.\end{align*}
\begin{align*}(f)(\partial_i,\partial_j) &= \nabla_i \nabla_j f\\&= \partial_i \partial_j f - {\omega^k}_{ij} \partial_k f.\end{align*} | \begin{align*}(f)(\partial_i,\partial_j) &=\nabla_i \nabla_j f\\&=\partial_i \partial_j f-{\omega^k}_{ij} \partial_k f.\end{align*}
\begin{align*}\ell_{\lambda}^{O(k+1)}:=\inf\{\tau>0:O(k+1)(PS)_{\tau}\mathcal{J}_{\lambda}\}.\end{align*} | \begin{align*}\ell_{\lambda}^{O(k+1)}:=\inf\{\tau>0:O(k+1)(PS)_{\tau}\mathcal{J}_{\lambda}\}.\end{align*}
\begin{align*}dX_t=-\frac{\sigma\left(e^{X_t}\right)}{2} dt +dW_t+ \frac{\sigma_2-\sigma_1}{\sigma_1+\sigma_2}dL_{t}^{\left(0\right)}(X)=\mu(X_t) dt +dW_t+(p-q)dL_{t}^{\left(0\right)}(X)\end{align*} | \begin{align*}dX_t=-\frac{\sigma\left(e^{X_t}\right)}{2} dt+dW_t+\frac{\sigma_2-\sigma_1}{\sigma_1+\sigma_2}dL_{t}^{\left(0\right)}(X)=\mu(X_t) dt+dW_t+(p-q)dL_{t}^{\left(0\right)}(X)\end{align*}
\begin{align*}\tilde{u}_s(a,t) := \hat{u}(a + s \nu(a),t),\forall (a,t) \in \mathcal{M} \times [0,T],\forall s \in [-\delta, \delta].\end{align*} | \begin{align*}\tilde{u}_s(a,t):=\hat{u}(a+s \nu(a),t),\forall(a,t) \in \mathcal{M} \times[0,T],\forall s \in[-\delta,\delta].\end{align*}
\begin{align*}\mathbf{x}_{\mathrm{d}}(i) = \sum_{z=1}^{Z}\beta_z\mathbf{a}(\mu^{\mathrm{r}}_{z},\mu^{\mathrm{t}}_{z},\gamma_z,\eta_z)\exp(-j(i-1)\eta_z)\end{align*} | \begin{align*}\mathbf{x}_{\mathrm{d}}(i)=\sum_{z=1}^{Z}\beta_z\mathbf{a}(\mu^{\mathrm{r}}_{z},\mu^{\mathrm{t}}_{z},\gamma_z,\eta_z)\exp(-j(i-1)\eta_z)\end{align*}
\begin{align*} \tilde{S}_{Y_p}\cap[N_{Y_p}/2, N_{Y_p}-K]=& \{ \xi r_-+\xi r_+,\; (\xi-1)r_-+(\xi+1)r_+,\dots,\;r_-+(2\xi-1)r_+, \\ &(2\xi+1)r_-,2\xi r_-+r_+,\dots,\; (\xi+3)r_-+(\xi-2)r_+, \; (\xi+2)r_-+(\xi-1)r_+ \}.\end{align*} | \begin{align*} \tilde{S}_{Y_p}\cap[N_{Y_p}/2,N_{Y_p}-K]=& \{\xi r_-+\xi r_+,\;(\xi-1)r_-+(\xi+1)r_+,. . .,\;r_-+(2\xi-1)r_+,\\ &(2\xi+1)r_-,2\xi r_-+r_+,. . .,\;(\xi+3)r_-+(\xi-2)r_+,\;(\xi+2)r_-+(\xi-1)r_+\}.\end{align*}
\begin{align*}(Hf)(x)=\sum_{n=1}^{N}(H_{J_{n}^{x}}f)(x). \end{align*} | \begin{align*}(Hf)(x)=\sum_{n=1}^{N}(H_{J_{n}^{x}}f)(x).\end{align*}
\begin{align*}J_\mu^2=-|\mu|^2 I\end{align*} | \begin{align*}J_\mu^2=-|\mu|^2 I\end{align*}
\begin{align*}\begin{aligned}\Theta_\gamma (v) = 1 \textrm{ if } 0 \leq \gamma \leq 1 \,, \textrm{ and } \Theta_\gamma (v) = \nu^{-1} (v) \textrm{ if } - 3 < \gamma < 0 \,.\end{aligned}\end{align*} | \begin{align*}\begin{aligned}\Theta_\gamma(v)=1 \textrm{if} 0 \leq \gamma \leq 1 \,,\textrm{and} \Theta_\gamma(v)=\nu^{-1}(v) \textrm{if}-3<\gamma<0 \,.\end{aligned}\end{align*}
\begin{align*}\alpha=s+\sum_{j=1}^d \frac{a_j}{\tau_j}-\frac{\nu}{\tau_{}}.\end{align*} | \begin{align*}\alpha=s+\sum_{j=1}^d \frac{a_j}{\tau_j}-\frac{\nu}{\tau_}.\end{align*}
\begin{align*}(x+6)((x+4)I+A[\mathcal C_i])^{-1} = \frac{1}{q_i(-x-4)} \sum_{k=1}^{\deg m_i} D^k m_i(-x-4) A[\mathcal C_i]^{k-1}\end{align*} | \begin{align*}(x+6)((x+4)I+A[\mathcal C_i])^{-1}=\frac{1}{q_i(-x-4)} \sum_{k=1}^{\deg m_i} D^k m_i(-x-4) A[\mathcal C_i]^{k-1}\end{align*}
\begin{align*} \dfrac{1}{\Phi_1[\alpha_0,\beta_0, L_1(0), N_1(0)]}+\dfrac{u_c }{\Phi_2[\beta_0,\infty,L_2(u_c),N_2(u_c)]}=\dfrac{2l_m\gamma_m(\beta_0)^{\nu+1}}{\theta_b-\theta_m}\end{align*} | \begin{align*} \dfrac{1}{\Phi_1[\alpha_0,\beta_0,L_1(0),N_1(0)]}+\dfrac{u_c}{\Phi_2[\beta_0,\infty,L_2(u_c),N_2(u_c)]}=\dfrac{2l_m\gamma_m(\beta_0)^{\nu+1}}{\theta_b-\theta_m}\end{align*}
\begin{align*}TM \oplus TM := \{(\xi , \eta) : \xi, \eta \in T_xM ,x \in M \}.\end{align*} | \begin{align*}TM \oplus TM:=\{(\xi,\eta):\xi,\eta \in T_xM,x \in M \}.\end{align*}
\begin{align*}y_{n-1}^{(l)}|_{\mu=\tfrac{1}{n}}\stackrel{ \rho}{\approx} \tfrac{n!}{(n{-}l)!(l{-}1)!} \displaystyle\sum_{k=0}^{n{-}l} \binom{n{-}l}{k} \tfrac{(-1)^k}{k{+}l}\left(\tfrac{n-l-k}{n}\right)^{n-l}= 1.\end{align*} | \begin{align*}y_{n-1}^{(l)}|_{\mu=\tfrac{1}{n}}\stackrel{\rho}{\approx} \tfrac{n!}{(n{-}l)!(l{-}1)!} \displaystyle\sum_{k=0}^{n{-}l} \binom{n{-}l}{k} \tfrac{(-1)^k}{k{+}l}\left(\tfrac{n-l-k}{n}\right)^{n-l}=1.\end{align*}
\begin{align*}\begin{cases}\overline u_t\geq \overline u(t)( a_0(t)-a_1(t)\overline u(t) -a_2(t)\underline v(t))\\\underline v_t\leq \underline v(t)( b_0(t)-b_1(t)\overline u(t) -b_2(t)\underline v(t)).\end{cases}\end{align*} | \begin{align*}\begin{cases}\overline u_t\geq \overline{u(t)(} a_0(t)-a_1(t)\overline u(t)-a_2(t)\underline v(t))\\\underline v_t\leq \underline{v(t)(} b_0(t)-b_1(t)\overline u(t)-b_2(t)\underline v(t)).\end{cases}\end{align*}
\begin{align*}\chi (M_n (\pi/2))=&2 \sum_{s=1}^m (-1)^{m+s} \left(s- \big\lfloor s/2 \big\rfloor \right) {n \choose m-s} \\=& 2(-1)^m \sum_{s=1}^m (-1)^s \big\lfloor (s+1)/2 \big\rfloor {2m+1 \choose m-s} \\=& 2 (-1)^{m+1} \sum_{i=0}^{m+1} (-1)^i\big\lfloor i /2\big\rfloor {2m+1 \choose m+i} .\end{align*} | \begin{align*}\chi(M_n(\pi/2))=&2 \sum_{s=1}^m(-1)^{m+s} \left(s-\big\lfloor s/2 \big\rfloor \right){n \choose m-s} \\=& 2(-1)^m \sum_{s=1}^m(-1)^s \big\lfloor(s+1)/2 \big\rfloor{2m+1 \choose m-s} \\=& 2(-1)^{m+1} \sum_{i=0}^{m+1}(-1)^i\big\lfloor i/2\big\rfloor{2m+1 \choose m+i}.\end{align*}
\begin{align*}\alpha(X(p_m),H_{t_m}^{-1}(X(p_m))) = 0\end{align*} | \begin{align*}\alpha(X(p_m),H_{t_m}^{-1}(X(p_m)))=0\end{align*}
\begin{align*}\mathcal{A}_{\cap}^{(N)}=\left\{ A:A=A_{j_{1}}^{(1)}\cap A_{j_{2}}^{(2)}\cap...\cap A_{j_{N}}^{(N)},j_{k}\leqslant m_{k},k\leqslant N\right\}.\end{align*} | \begin{align*}\mathcal{A}_{\cap}^{(N)}=\left\{A:A=A_{j_{1}}^{(1)}\cap A_{j_{2}}^{(2)}\cap...\cap A_{j_{N}}^{(N)},j_{k}\leq slant m_{k},k\leqslant N\right\}.\end{align*}
\begin{align*} X_i&=\partial_{x_i} +2y_i \partial_t,\ i=1, .., n\\X_{n+j}&= \partial_{y_j} - 2x_j \partial_t,\ j=1, .., n\end{align*} | \begin{align*} X_i&=\partial_{x_i}+2y_i \partial_t,\ i=1,..,n\\X_{n+j}&=\partial_{y_j}-2x_j \partial_t,\ j=1,..,n\end{align*}
\begin{align*}T_j(-z):=F(j)\cdot\left(\right).\end{align*} | \begin{align*}T_j(-z):=F(j)\cdot\left(\right).\end{align*}
\begin{align*} B(u_k)&=\int_{\Omega}(|x|^{-\mu}*F(u_k))f^{'}(u_k) u_k^2 ~dx +\int_{\Omega} (|x|^{-\mu}*f(u_k)u_k)f(u_k)u_k ~dx\\ &\leq C(n, \mu) \left(\|f(u_k)u_k\|^2_{L^{2n/(2n-\mu)}(\Omega)} + \|F(u_k)\|_{L^{2n/(2n-\mu)}(\Omega)} \|f^{'}(u_k) (u_k)^2\|_{L^{2n/(2n-\mu)}(\Omega)}\right). \end{align*} | \begin{align*} B(u_k)&=\int_{\Omega}(|x|^{-\mu}*F(u_k))f^{'}(u_k) u_k^2 ~dx+\int_{\Omega}(|x|^{-\mu}*f(u_k)u_k)f(u_k)u_k ~dx\\ &\leq C(n,\mu) \left(\|f(u_k)u_k\|^2_{L^{2n/(2n-\mu)}(\Omega)}+\|F(u_k)\|_{L^{2n/(2n-\mu)}(\Omega)} \|f^{'}(u_k)(u_k)^2\|_{L^{2n/(2n-\mu)}(\Omega)}\right).\end{align*}
\begin{align*}B(t,x; \eta) \ &= \ \big\{(s,y) \in (0,T) \times H: \max \{|x-y|, |t-s|\}< \eta\big\}, \\\partial B(t,x; \eta) \ &= \ \big\{(s,y) \in (0,T) \times H: \max \{|x-y|, |t-s|\}= \eta\big\}.\end{align*} | \begin{align*}B(t,x;\eta) \ &=\ \big\{(s,y) \in(0,T) \times H:\mathrm{max} \{|x-y|,|t-s|\}<\eta\big\},\\\partial B(t,x;\eta) \ &=\ \big\{(s,y) \in(0,T) \times H:\mathrm{max} \{|x-y|,|t-s|\}=\eta\big\}.\end{align*}
\begin{align*}\begin{aligned}\rho(x)\partial_t^2 w_t^n - \operatorname{div}(K(x)\nabla w_t^n) \rightarrow 0~ \hbox{ in }~H^{-1}_{loc}(\Omega \times (0,T)), \\\rho(x)\partial_t^2 z_t^n - \operatorname{div}(K(x)\nabla z_t^n) \rightarrow 0~ \hbox{ in }~H^{-1}_{loc}(\Omega \times (0,T)),\end{aligned}\end{align*} | \begin{align*}\begin{aligned}\rho(x)\partial_t^2 w_t^n-\operatorname{div}(K(x)\nabla w_t^n) \rightarrow 0~\text{CONTENTPROTECTED0}~H^{-1}_{loc}(\Omega \times(0,T)),\\\rho(x)\partial_t^2 z_t^n-\operatorname{div}(K(x)\nabla z_t^n) \rightarrow 0~\text{CONTENTPROTECTED1}~H^{-1}_{loc}(\Omega \times(0,T)),\end{aligned}\end{align*}
\begin{align*}\widehat{m}_{13}=-\widetilde{m}_{31}, \widehat{m}_{23}=-\widetilde{m}_{32}.\end{align*} | \begin{align*}\widehat{m}_{13}=-\widetilde{m}_{31},\widehat{m}_{23}=-\widetilde{m}_{32}.\end{align*}
\begin{align*}H_{\alpha}g=\lambda g, \,\ f\in C^{+}[0,1]\end{align*} | \begin{align*}H_{\alpha}g=\lambda g,\,\ f\in C^{+}[0,1]\end{align*}
\begin{align*}\ & \lim _{k\to \omega }\| \tilde{\alpha} _n (x_k) -\theta _t(x_k)\| _{\varphi \circ \theta _s}^\sharp \\ &=\mathrm{weak}\lim _{k \to \omega }\frac{1}{2} ( |\tilde{\alpha } _n (x_k) -\theta _t(x_k )|^2 +|(\tilde{\alpha }_n (x_k)-\theta _t(x_k))^*|^2 ) \\& =2\delta >0\end{align*} | \begin{align*}\ & \lim_{k\to \omega}\| \tilde{\alpha}_n(x_k)-\theta_t(x_k)\|_{\varphi \circ \theta_s}^\sharp \\ &=\mathrm{weak}\lim_{k \to \omega}\frac{1}{2}(|\tilde{\alpha}_n(x_k)-\theta_t(x_k)|^2+|(\tilde{\alpha}_n(x_k)-\theta_t(x_k))^*|^2) \\&=2\delta>0\end{align*}
\begin{align*}X_{1}(u)+X_{5}(u)&\equiv \frac{2}{5}up \pmod{p^2}, X_3(u) \equiv \frac{1}{30}up \pmod{p^2} \textrm{and} \\X_2(u)+X_4(u)&\equiv -\frac{1}{10}up \pmod{p^2}.\end{align*} | \begin{align*}X_{1}(u)+X_{5}(u)&\equiv \frac{2}{5}up \pm od{p^2},X_3(u) \equiv \frac{1}{30}up \pm od{p^2} \textrm{and} \\X_2(u)+X_4(u)&\equiv-\frac{1}{10}up \pm od{p^2}.\end{align*}
\begin{align*}y''=2y^3+\tilde zy+C.\end{align*} | \begin{align*}y''=2y^3+\tilde zy+C.\end{align*}
\begin{align*}a_k\,:=\,|A_{M^k}(u,B_1)\cap Q|\;\;\;\;\;\mbox{and}\;\;\;\;\;b_k\,:=\,\left|\left\lbrace x\in Q\,|\,m(f^p)(x)\,\geq\,(CM^k)^p \right\rbrace\right|.\end{align*} | \begin{align*}a_k\,:=\,|A_{M^k}(u,B_1)\cap Q|\;\;\;\;\;\text{CONTENTPROTECTED0}\;\;\;\;\;b_k\,:=\,\left|\left\lbrace x\in Q\,|\,m(f^p)(x)\,\geq\,(CM^k)^p \right\rbrace\right|.\end{align*}
\begin{align*}g_{1}=x_{0}^{n+1}-x_{3}^{n}&=\sum_{\mu=1}^{n-1}\alpha_{\mu}\left[x_{2}^{(n+1)-\mu}x_{3}^{\mu}-x_{0}^{(n+1)-\mu}x_{1}^{\mu+1}\right]\\&+\sum_{t=0}^{n}\beta_{t}\left[x_{1}^{(n+1)-t}x_{3}^{t}-x_{0}^{n-t}x_{2}^{t+1}\right]+\gamma_{2}(x_{1}x_{2}-x_{0}x_{3}),\end{align*} | \begin{align*}g_{1}=x_{0}^{n+1}-x_{3}^{n}&=\sum_{\mu=1}^{n-1}\alpha_{\mu}\left[x_{2}^{(n+1)-\mu}x_{3}^{\mu}-x_{0}^{(n+1)-\mu}x_{1}^{\mu+1}\right]\\&+\sum_{t=0}^{n}\beta_{t}\left[x_{1}^{(n+1)-t}x_{3}^{t}-x_{0}^{n-t}x_{2}^{t+1}\right]+\gamma_{2}(x_{1}x_{2}-x_{0}x_{3}),\end{align*}
\begin{align*} d M_\eta(t)& =e^{-\eta t}\frac{\sqrt{2\gamma}}{m}v(t)\, dW_0(t)+\frac{e^{-\eta t}}{m}\sum_{k=1}^N \sqrt{2\lambda_k}z_k(t)\, dW_k(t)\\&+e^{-\eta t}\sum_{k>N}\sqrt{2\lambda_k}k^{-2s}z_k(t)\, dW_k(t).\end{align*} | \begin{align*} d M_\eta(t)&=e^{-\eta t}\frac{\sqrt{2\gamma}}{m}v(t)\,dW_0(t)+\frac{e^{-\eta t}}{m}\sum_{k=1}^N \sqrt{2\lambda_k}z_k(t)\,dW_k(t)\\&+e^{-\eta t}\sum_{k>N}\sqrt{2\lambda_k}k^{-2s}z_k(t)\,dW_k(t).\end{align*}
\begin{align*}f=\sum_{j=j_0}^{\infty}\sum_{k=0}^{2^{j}-1}f_{jk}\psi_{jk},\end{align*} | \begin{align*}f=\sum_{j=j_0}^{\infty}\sum_{k=0}^{2^{j}-1}f_{jk}\psi_{jk},\end{align*}
\begin{align*}C_{\operatorname*{DtN},k}:=\left\Vert T_{k}\right\Vert _{\mathbf{H}_{\operatorname*{div}}^{-1/2}\left( \Gamma\right) \leftarrow\mathbf{H}_{\operatorname*{curl}}^{-1/2}\left( \Gamma\right) }<\infty.\end{align*} | \begin{align*}C_{\operatorname*{DtN},k}:=\left\Vert T_{k}\right\Vert_{\mathbf{H}_{\operatorname*{div}}^{-1/2}\left(\Gamma\right) \leftarrow\mathbf{H}_{\operatorname*{curl}}^{-1/2}\left(\Gamma\right)}<\infty.\end{align*}
\begin{align*}\rho^\textrm{T}_i = \sum\limits_{k=1}^K \pi_{i,k} \upsilon^\textrm{T}_{i,k}.\end{align*} | \begin{align*}\rho^\textrm{T}_i=\sum\limits_{k=1}^K \pi_{i,k} \up silon^\textrm{T}_{i,k}.\end{align*}
\begin{align*}A(R) = \frac{(N-3)!(N-2p)}{(N-p-1)!(p-1)!} \; \; ,\end{align*} | \begin{align*}A(R)=\frac{(N-3)!(N-2p)}{(N-p-1)!(p-1)!} \;\;,\end{align*}
\begin{align*}{\cal{A}} = c_1\frac{\Gamma(t) \Gamma(s)}{\Gamma(1+s+t)} (s a_1 -ta_2) \delta^{p+1}(p_1+p_2)\end{align*} | \begin{align*}{\cal{A}}=c_1\frac{\Gamma(t) \Gamma(s)}{\Gamma(1+s+t)}(s a_1-ta_2) \delta^{p+1}(p_1+p_2)\end{align*}
\begin{align*}g(x) = \exp (\Gamma_{\mu} x^{\mu})\end{align*} | \begin{align*}g(x)=\mathrm{exp}(\Gamma_{\mu} x^{\mu})\end{align*}
\begin{align*}e^{ c \phi} e^{f(q^{a})} - e^{-2 c \phi } e^{-2 f(q^{a})} = e^{\phi} [\,c+ c_{1} + Y_{0}X_{0} f(q^{a}) \,] - e^{-2 \phi} [\, c + c_{1}- Y_{1}X_{0} f(q^{a}) \,] .\end{align*} | \begin{align*}e^{c \phi} e^{f(q^{a})}-e^{-2 c \phi} e^{-2 f(q^{a})}=e^{\phi}[\,c+c_{1}+Y_{0}X_{0} f(q^{a}) \,]-e^{-2 \phi}[\,c+c_{1}-Y_{1}X_{0} f(q^{a}) \,].\end{align*}
\begin{align*}\frac{E_D (B)}{V} \, = \, \frac{1}{8 \pi^2} \, \zeta\left( \frac{3}{2} \right) \, (gB)^{3/2} \; .\end{align*} | \begin{align*}\frac{E_D(B)}{V} \,=\,\frac{1}{8 \pi^2} \,\zeta\left(\frac{3}{2} \right) \,(gB)^{3/2} \;.\end{align*}
\begin{align*}{\cal S}^{D0} = \frac{1}{2 g_{S}} \int dt~{\rm Tr} \left(G_{ij} \dot{X}^i \dot{X}^j+ \frac{1}{2}\frac{1}{(2\pi)^2} G_{ij} G_{kl} [X^i,X^k] [X^j,X^l]+ \right.\end{align*} | \begin{align*}{\cal S}^{D0}=\frac{1}{2 g_{S}} \int dt~{\rm Tr} \left(G_{ij} \dot{X}^i \dot{X}^j+\frac{1}{2}\frac{1}{(2\pi)^2} G_{ij} G_{kl}[X^i,X^k][X^j,X^l]+\right.\end{align*}
\begin{align*}\langle \psi (x) \, \bar{\psi}(\tilde{x})\rangle_w =\langle1\rangle_w\Omega^{-1}(x)\Omega^{-1}(\tilde{x})\, S_o(x,\,\tilde{x})\end{align*} | \begin{align*}\langle \psi(x) \,\bar{\psi}(\tilde{x})\rangle_w=\langle1\rangle_w\Omega^{-1}(x)\Omega^{-1}(\tilde{x})\,S_o(x,\,\tilde{x})\end{align*}
\begin{align*}\ln \left[ \frac{ K-\sigma}{K } \right] = -\int_{0}^{\sigma} d s \frac{1}{ K -s},\end{align*} | \begin{align*}\ln \left[\frac{K-\sigma}{K} \right]=-\int_{0}^{\sigma} d s \frac{1}{K-s},\end{align*}
\begin{align*}\int _{\left|m_{eff}^{2}\right|^{1/2}\beta }^{\infty }\, dy\, \left(y^{2}+\left|m_{eff}^{2}\right|\beta ^{2}\right)^{1/2}e^{-2\pi rny\lambda /\beta }=\frac{\left|m_{eff}^{2}\right|^{1/2}\beta ^{2}}{4\pi rn\lambda }\times \left[H_{1}\left(\eta \lambda \right)-N_{1}\left(\eta \lambda \right)\right],\end{align*} | \begin{align*}\int_{\left|m_{eff}^{2}\right|^{1/2}\beta}^{\infty}\,dy\,\left(y^{2}+\left|m_{eff}^{2}\right|\beta^{2}\right)^{1/2}e^{-2\pi rny\lambda/\beta}=\frac{\left|m_{eff}^{2}\right|^{1/2}\beta^{2}}{4\pi rn\lambda}\times \left[H_{1}\left(\eta \lambda \right)-N_{1}\left(\eta \lambda \right)\right],\end{align*}
\begin{align*} \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(1)} = 0 \qquad \hbox{and}\qquad \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(2)} = 0 \quad ,\end{align*} | \begin{align*} \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(1)}=0 \qquad\text{CONTENTPROTECTED0}\qquad \xi_0 \partial_{\xi_0} \tilde{\Pi}^{(2)}=0 \quad,\end{align*}
\begin{align*}\frac{d^2R}{d\rho^2}+\frac{2}{\rho}\frac{dR}{d\rho}-\frac{l(l+1)}{\rho^2}R+ \frac{\lambda}{\rho}R - \frac{1}{4}R = 0,\end{align*} | \begin{align*}\frac{d^2R}{d\rho^2}+\frac{2}{\rho}\frac{dR}{d\rho}-\frac{l(l+1)}{\rho^2}R+\frac{\lambda}{\rho}R-\frac{1}{4}R=0,\end{align*}
\begin{align*}& (\Sigma_{m})_{1,j} = (\Sigma_{m})_{j,1} = \delta_{1,j}-\tfrac{1}{\sqrt{2}}\delta_{2,j}, & & 1 \leq j \leq m. \\& (\Sigma_{m})_{i,j} = \delta_{i,j}-\tfrac{1}{2}\delta_{i,j+1}- \tfrac{1}{2}\delta_{i,j-1}, & & 2 \leq i,j \leq m.\end{align*} | \begin{align*}&(\Sigma_{m})_{1,j}=(\Sigma_{m})_{j,1}=\delta_{1,j}-\tfrac{1}{\sqrt{2}}\delta_{2,j},& & 1 \leq j \leq m.\\&(\Sigma_{m})_{i,j}=\delta_{i,j}-\tfrac{1}{2}\delta_{i,j+1}-\tfrac{1}{2}\delta_{i,j-1},& & 2 \leq i,j \leq m.\end{align*}
\begin{align*}-z^T(T)Hz(T) = \int_0^T\left\{ [(A+G)z +g]^T Pz +z^TP[(A+G)z +g] +z^T \dot P z\right\}(t)dt.\end{align*} | \begin{align*}-z^T(T)Hz(T)=\int_0^T\left\{[(A+G)z+g]^T Pz+z^TP[(A+G)z+g]+z^T \dot{P} z\right\}(t)dt.\end{align*}
\begin{align*}\frac{1}{\left( 1+x\right) ^{s+1}}w_{n}^{\left( s+1\right) }\left(\frac{-x}{1+x};\alpha,\beta,r\right) =\frac{1}{2\pi i\Gamma\left(s+1\right) }{\displaystyle\int\limits_{a-i\infty}^{a+i\infty}}\left( r-\beta t\mid\alpha\right) _{n}x^{-t}\Gamma\left( t\right)\Gamma\left( s+1-t\right) dt, \end{align*} | \begin{align*}\frac{1}{\left(1+x\right)^{s+1}}w_{n}^{\left(s+1\right)}\left(\frac{-x}{1+x};\alpha,\beta,r\right)=\frac{1}{2\pi i\Gamma\left(s+1\right)}{\displaystyle\int\limits_{a-i\infty}^{a+i\infty}}\left(r-\beta t\mid\alpha\right)_{n}x^{-t}\Gamma\left(t\right)\Gamma\left(s+1-t\right) dt,\end{align*}
\begin{align*}A(s):=\int_0^s e^{\frac{G(t)}{p-1}} \ dt,\end{align*} | \begin{align*}A(s):=\int_0^s e^{\frac{G(t)}{p-1}} \ dt,\end{align*}
\begin{align*}{\bf{Y}}\left[ i \right] = \mathsf{H}{\bf{X}}\left[ i \right] + {\bf{W}}\left[ i \right]\end{align*} | \begin{align*}{\bf{Y}}\left[i \right]=\mathsf{H}{\bf{X}}\left[i \right]+{\bf{W}}\left[i \right]\end{align*}
\begin{align*}K(r) =& \int_0^{r} g(s) 2 \pi s ds,\\L(r) =& \sqrt{\frac{K(r)}{\pi}},\end{align*} | \begin{align*}K(r)=& \int_0^{r} g(s) 2 \pi s ds,\\L(r)=& \sqrt{\frac{K(r)}{\pi}},\end{align*}
\begin{align*}\deg^\vee = \frac{1}{2}(s_1 + \cdots +s_{2k})\end{align*} | \begin{align*}\deg^\vee=\frac{1}{2}(s_1+. . .+s_{2k})\end{align*}
\begin{align*}\nu _{\mathcal{X}_{2}}(s)=\mu \int_{0}^{+\infty }\frac{e^{-s^{2}/2z}}{\sqrt{2\pi z^{3}}}e^{-\rho z}dz=\frac{\mu }{|s|}e^{-\sqrt{2\rho }|s|},\end{align*} | \begin{align*}\nu_{\mathcal{X}_{2}}(s)=\mu \int_{0}^{+\infty}\frac{e^{-s^{2}/2z}}{\sqrt{2\pi z^{3}}}e^{-\rho z}dz=\frac{\mu}{|s|}e^{-\sqrt{2\rho}|s|},\end{align*}
\begin{align*}D(L)=\{ u\in D(\widehat{L}) : \; [(I-K \widehat{L})u]|_{\partial\Omega}=0 \}, \end{align*} | \begin{align*}D(L)=\{u\in D(\widehat{L}):\;[(I-K \widehat{L})u]|_{\partial\Omega}=0 \},\end{align*}
\begin{align*}\int_0^1\zeta(a,x)\zeta(b,x)\,dx=\left\{B(a+b-1,1-a)+B(a+b-1,1-b)\right\}\zeta(a+b-1)\end{align*}%\end{align*} | \begin{align*}\int_0^1\zeta(a,x)\zeta(b,x)\,dx=\left\{B(a+b-1,1-a)+B(a+b-1,1-b)\right\}\zeta(a+b-1)\end{align*}%\end{align*}
\begin{align*}\sum_{\varnothing\neq J\subset\{1,\ldots,n\}} (-1)^{\#J-1} f_r(\Pi_J) = (-1)^d \left(\binom {n}{r+1} - f_r(\Pi)\right).\end{align*} | \begin{align*}\sum_{\varnothing\neq J\subset\{1,. . .,n\}}(-1)^{\#J-1} f_r(\Pi_J)=(-1)^d \left(\binom{n}{r+1}-f_r(\Pi)\right).\end{align*}
\begin{align*}f(m_2 + e) = &\ f(m_2) + a^{(m_2)} = f(m_1 + re) + a^{(m_2)} = f(m_1) +a^{(m_1)} r + a^{(m_2)}\\= &\ f(m_1 + (r+1)e) = f(m_1) + a^{(m_1)} (r+1).\end{align*} | \begin{align*}f(m_2+e)=&\ f(m_2)+a^{(m_2)}=f(m_1+re)+a^{(m_2)}=f(m_1)+a^{(m_1)} r+a^{(m_2)}\\=&\ f(m_1+(r+1)e)=f(m_1)+a^{(m_1)}(r+1).\end{align*}
\begin{align*}\begin{cases}\partial_t w -\partial_x^2w + x^2 w = 0 , t\in (0, \tau),\ x \in (-L, \, L) \\w(t, -L)=u_{-L}(t), w(t, L)=u_L(t), t\in (0, \tau) \\w(0, x)=f, x \in (-L, \, L).\end{cases}\end{align*} | \begin{align*}\begin{cases}\partial_t w-\partial_x^2w+x^2 w=0,t\in(0,\tau),\ x \in(-L,\,L) \\w(t,-L)=u_{-L}(t),w(t,L)=u_L(t),t\in(0,\tau) \\w(0,x)=f,x \in(-L,\,L).\end{cases}\end{align*}
\begin{align*}R\langle \Omega^1_R\{-1\}\rangle =R\langle \frac{\xi_1}{I}, \cdots, \frac{\xi_n}{I}\rangle,R^{PD}(1)= R\{\frac{\xi_1}{I}, \dots, \frac{\xi_n}{I}\}^{PD,\wedge}.\end{align*} | \begin{align*}R\langle \Omega^1_R\{-1\}\rangle=R\langle \frac{\xi_1}{I},. . .,\frac{\xi_n}{I}\rangle,R^{PD}(1)=R\{\frac{\xi_1}{I},. . .,\frac{\xi_n}{I}\}^{PD,\wedge}.\end{align*}
\begin{align*}\widehat{\theta}_n - \theta_0 = -\frac{\dot{\mathbb{M}}_n(\theta_0)}{\int_0^1 \ddot{\mathbb{M}}_n(\theta_0 + t(\widehat{\theta}_n - \theta_0))dt}. \end{align*} | \begin{align*}\widehat{\theta}_n-\theta_0=-\frac{\dot{\mathbb{M}}_n(\theta_0)}{\int_0^1 \ddot{\mathbb{M}}_n(\theta_0+t(\widehat{\theta}_n-\theta_0))dt}.\end{align*}
\begin{align*} \chi(|N|)= X^{-1}_{S_{i_0}}([a_{i_0-1},\mu_1,\mu_2,\dots,\mu_{t-1} \mid b_{i_0},\nu_1,\nu_2,\dots,\nu_{t-1}]+E) \end{align*} | \begin{align*} \chi(|N|)=X^{-1}_{S_{i_0}}([a_{i_0-1},\mu_1,\mu_2,. . .,\mu_{t-1} \mid b_{i_0},\nu_1,\nu_2,. . .,\nu_{t-1}]+E) \end{align*}
\begin{align*}\gamma_{ij} = \gamma_{0i} \pm \gamma_{0j}.\end{align*} | \begin{align*}\gamma_{ij}=\gamma_{0i} \pm \gamma_{0j}.\end{align*}
\begin{align*}\beta_i|_{\frak{t}^\sigma} =\alpha_{i+1}|_{\frak{t}^\sigma}\ (1\leq i\leq n-1),\beta_n|_{\frak{t}^\sigma}=\beta_{n+1}|_{\frak{t}^\sigma} =\alpha_{n+1}|_{\frak{t}^\sigma}\end{align*} | \begin{align*}\beta_i|_{\frak{t}^\sigma}=\alpha_{i+1}|_{\frak{t}^\sigma}\(1\leq i\leq n-1),\beta_n|_{\frak{t}^\sigma}=\beta_{n+1}|_{\frak{t}^\sigma}=\alpha_{n+1}|_{\frak{t}^\sigma}\end{align*}
\begin{align*}\bar{\xi}_A(D^n,n^{-1}\delta_n x^n) = \zeta_A(D,x) + \frac{1}{\log(D)} \int_x^{xD} \frac{K_A(u)}{u} \mathrm{d}u.\end{align*} | \begin{align*}\bar{\xi}_A(D^n,n^{-1}\delta_n x^n)=\zeta_A(D,x)+\frac{1}{\log(D)} \int_x^{xD} \frac{K_A(u)}{u} \mathrm{d} u.\end{align*}
\begin{align*}\sum_{h=1}^H b(h)\xi(h) = {\bf 0},\end{align*} | \begin{align*}\sum_{h=1}^H b(h)\xi(h)={\bf 0},\end{align*}
\begin{align*} \begin{aligned}&-\dot{v}(t) + A^*(t)v(t) = g(t) &&\mbox{in $V^*$, $t \in (0,T)$}, \\&v(T) = \xi &&\mbox{in $H$},\end{aligned}\end{align*} | \begin{align*} \begin{aligned}&-\dot{v}(t)+A^*(t)v(t)=g(t) &&\text{CONTENTPROTECTED0},\\&v(T)=\xi &&\text{CONTENTPROTECTED1},\end{aligned}\end{align*}
\begin{align*}f^q(\beta, u)=\lim_{N\to \infty}\frac{1}{N}\log Z_N^{\beta, u, q}=\lim_{N\to \infty}\frac{1}{N}E^Q[\log Z_N^{\beta, u, q}]\end{align*} | \begin{align*}f^q(\beta,u)=\lim_{N\to \infty}\frac{1}{N}\log Z_N^{\beta,u,q}=\lim_{N\to \infty}\frac{1}{N}E^Q[\log Z_N^{\beta,u,q}]\end{align*}
\begin{align*}p_{n+1}^a = \lim_{s\to\infty} \left(1-\frac{Q_n(as)}{\zeta(as)}\right)^{-1/s}\end{align*} | \begin{align*}p_{n+1}^a=\lim_{s\to\infty} \left(1-\frac{Q_n(as)}{\zeta(as)}\right)^{-1/s}\end{align*}
\begin{align*}\textbf{h}_{1,k'} = \phi_{1,k'} \hat{\textbf{h}}_{1,k} + \textbf{w}_{1,k'},\end{align*} | \begin{align*}\textbf{h}_{1,k'}=\phi_{1,k'} \hat{\textbf{h}}_{1,k}+\textbf{w}_{1,k'},\end{align*}
\begin{align*}\limsup_{t \to -\infty} \frac{F(t)}{t^2}=0.\end{align*} | \begin{align*}\limsup_{t \to-\infty} \frac{F(t)}{t^2}=0.\end{align*}
\begin{align*}\Pi^{(p)}=\sum_{d=1}^{p}M(d) \sum_{k=1}^{\infty} \mathrm{S}(\cdot\mid d, \alpha=k)\bigg{/} \sum_{\tilde{d}=1}^{p}M(\tilde{d}).\end{align*} | \begin{align*}\Pi^{(p)}=\sum_{d=1}^{p}M(d) \sum_{k=1}^{\infty} \mathrm{S}(\cdot\mid d,\alpha=k)\bigg{/} \sum_{\tilde{d}=1}^{p}M(\tilde{d}).\end{align*}
\begin{align*}a_{i_1} + a_{i_4 } = a_{i_2 } + a_{i_3 } \end{align*} | \begin{align*}a_{i_1}+a_{i_4}=a_{i_2}+a_{i_3} \end{align*}
\begin{align*}P_\lambda=\sum_{i_0,\ldots,i_{n+1}=0}^{d-2}c_{(i_0,\ldots,i_{n+1})}x_0^{i_0}\cdots x_{n+1}^{i_{n+1}}.\end{align*} | \begin{align*}P_\lambda=\sum_{i_0,. . .,i_{n+1}=0}^{d-2}c_{(i_0,. . .,i_{n+1})}x_0^{i_0}. . . x_{n+1}^{i_{n+1}}.\end{align*}
\begin{align*}f\big[U\to V\big] = \big(\mathcal{W}f\big)\big[U\to V\big].\end{align*} | \begin{align*}f\big[U\to V\big]=\big(\mathcal{W}f\big)\big[U\to V\big].\end{align*}
\begin{align*}[H]_{y,z}=\frac{H(y)-H(z)}{y-z} y \ne z\end{align*} | \begin{align*}[H]_{y,z}=\frac{H(y)-H(z)}{y-z} y \ne z\end{align*}
\begin{align*}dX_t = \widetilde\mu(X_t)\, dt + \widetilde\sigma(X_t)\, dW_t\end{align*} | \begin{align*}dX_t=\widetilde\mu(X_t)\,dt+\widetilde\sigma(X_t)\,dW_t\end{align*}
\begin{align*}H(t;x,y,1,u,1,v)&=\frac{xvt^2(1-yr)}{(1-ytu)(1-tuv(y-yr+1))(tux+y^{-1}-tu)}\\&\quad+\frac{yu^2vt^2(1-v)(1-yr)}{(1-ytu)(1-tuv(y-yr+1))}F(t;x,y,1,u,1,v).\end{align*} | \begin{align*}H(t;x,y,1,u,1,v)&=\frac{xvt^2(1-yr)}{(1-ytu)(1-tuv(y-yr+1))(tux+y^{-1}-tu)}\\&\quad+\frac{yu^2vt^2(1-v)(1-yr)}{(1-ytu)(1-tuv(y-yr+1))}F(t;x,y,1,u,1,v).\end{align*}
\begin{align*} \omega_\lambda(t) &:= \frac{\phi_\lambda''(t)\,t - \phi_\lambda'(t)}{\phi_\lambda'(t)}.\end{align*} | \begin{align*} \omega_\lambda(t) &:=\frac{\phi_\lambda''(t)\,t-\phi_\lambda'(t)}{\phi_\lambda'(t)}.\end{align*}
\begin{align*} \Lambda(f \otimes \mathrm{Ad}(g), k) = \frac{4 L(1,\pi,\mathrm{ad})L(1,\tau,\mathrm{ad})}{\langle \mathbf h, \mathbf h \rangle\langle \mathbf g, \mathbf g\rangle\langle \pmb{\phi}, \pmb{\phi} \rangle} \left( \prod_v \mathcal I_v^{\sharp}(\mathbf h, \mathbf g, \pmb{\phi})^{-1}\right) \mathcal Q(\mathbf h, \mathbf g, \pmb{\phi}).\end{align*} | \begin{align*} \Lambda(f \otimes \mathrm{Ad}(g),k)=\frac{4 L(1,\pi,\mathrm{ad})L(1,\tau,\mathrm{ad})}{\langle \mathbf{h,} \mathbf{h} \rangle\langle \mathbf{g,} \mathbf{g\rangle\langle} \pm b{\phi},\pm b{\phi} \rangle} \left(\prod_v \mathcal{I_v^{\sharp}(\mathbf} h,\mathbf{g,} \pm b{\phi})^{-1}\right) \mathcal{Q(\mathbf} h,\mathbf{g,} \pm b{\phi}).\end{align*}
\begin{align*}c_{i, j} := \sum_{i(\ell) = i} \epsilon_\ell^j x_\ell.\end{align*} | \begin{align*}c_{i,j}:=\sum_{i(\ell)=i} \epsilon_\ell^j x_\ell.\end{align*}
\begin{align*}\int_{t_2}^{t_1} f(t) \, dt = 0.\end{align*} | \begin{align*}\int_{t_2}^{t_1} f(t) \,dt=0.\end{align*}
\begin{align*} {\mathbb P }_{0} \Big( \int^{t}_{0} \big \lvert {\mathbb E}_{0} \big[ \lvert b ( s, X_{s}, F_{s})\rvert \, \big \vert \, \mathcal F_{T}^{X} \big] \big \rvert ^{2} {\mathrm d} s < \infty \Big) \, =\, 1 \, ; 0 \le t \le T \, . \end{align*} | \begin{align*}{\mathbb P}_{0} \Big(\int^{t}_{0} \big\lvert{\mathbb E}_{0} \big[\lvert b(s,X_{s},F_{s})\rvert \,\big\vert \,\mathcal{F_{T}^{X}} \big] \big\rvert^{2}{\mathrm d} s<\infty \Big) \,=\,1 \,;0 \le t \le T \,.\end{align*}
\begin{align*}\begin{aligned} w_0^+\circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^+\circ X^{-1}_\gamma\right)(s,0)&=-a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma,\\w_0^- \circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^-\circ X^{-1}_\gamma\right)(s,0)&=a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma.\end{aligned}\end{align*} | \begin{align*}\begin{aligned} w_0^+\circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^+\circ X^{-1}_\gamma\right)(s,0)&=-a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma,\\w_0^-\circ X^{-1}_\gamma(s,0)+\frac{\eta}{\lambda\mu_\lambda}\left(\partial_n w_0^-\circ X^{-1}_\gamma\right)(s,0)&=a_0\eta+b_0+2\log\mu_\lambda-\log h_\gamma.\end{aligned}\end{align*}
\begin{align*}DG(\lambda,b,\Omega,0,0)(h_{1},h_{2})=\left(\begin{array}{c}D_{f_{1}}G_{1}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{1}(\lambda,b,\Omega,0,0)h_{2}\\D_{f_{1}}G_{2}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{2}(\lambda,b,\Omega,0,0)h_{2}\end{array}\right).\end{align*} | \begin{align*}DG(\lambda,b,\Omega,0,0)(h_{1},h_{2})=\left(\begin{array}{c}D_{f_{1}}G_{1}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{1}(\lambda,b,\Omega,0,0)h_{2}\\D_{f_{1}}G_{2}(\lambda,b,\Omega,0,0)h_{1}+D_{f_{2}}G_{2}(\lambda,b,\Omega,0,0)h_{2}\end{array}\right).\end{align*}
\begin{align*}A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2},0\right\}\\A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}+{d_1+d_2-a\choose 2},0\right\}\\A=max&\Big\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}-{d_3+1\choose 2}+{d_1+d_2-a\choose 2}+\\&+{d_2+d_3-a\choose 2}+{d_3+d_1-a\choose 2}-{d_1+d_2+d_3-2a-1\choose 2},0\Big\}\end{align*} | \begin{align*}A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2},0\right\}\\A=max&\left\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}+{d_1+d_2-a\choose 2},0\right\}\\A=max&\Big\{{a+2\choose 2}-{d_1+1\choose 2}-{d_2+1\choose 2}-{d_3+1\choose 2}+{d_1+d_2-a\choose 2}+\\&+{d_2+d_3-a\choose 2}+{d_3+d_1-a\choose 2}-{d_1+d_2+d_3-2a-1\choose 2},0\Big\}\end{align*}
\begin{align*}m_\lambda=\sum_{(i_1,\dots,i_l)\in \mathbb N^l} x^{\lambda_1}_{i_1}\dots x^{\lambda_l}_{i_l}.\end{align*} | \begin{align*}m_\lambda=\sum_{(i_1,. . .,i_l)\in \mathbb{N^l}} x^{\lambda_1}_{i_1}. . . x^{\lambda_l}_{i_l}.\end{align*}
\begin{align*} \mathcal{G}(v, x) &= x - \sum_{w \in c(v)} \dfrac{1}{\mathcal{G}(w, x)} (\forall v \in V(T)) , \end{align*} | \begin{align*} \mathcal{G}(v,x) &=x-\sum_{w \in c(v)} \dfrac{1}{\mathcal{G}(w,x)}(\forall v \in V(T)),\end{align*}
\begin{align*} P_F(t)\cdot\hat{L}_{xy}=\frac{1}{n}\left(\frac{3}{4(4n+1)}-t\right),\ \ P_F(t)\cdot\hat{R}_0=P_F(t)\cdot\hat{R}_1=\frac{3}{4(4n+1)}-t.\end{align*} | \begin{align*} P_F(t)\cdot\hat{L}_{xy}=\frac{1}{n}\left(\frac{3}{4(4n+1)}-t\right),\ \ P_F(t)\cdot\hat{R}_0=P_F(t)\cdot\hat{R}_1=\frac{3}{4(4n+1)}-t.\end{align*}
d s ^ { 2 } = e ^ { 2 U } ( r ) d t ^ { 2 } + e ^ { - 2 U } ( r ) d r ^ { 2 } + R ^ { 2 } ( r ) d ^ { 2 } \Omega \ , | d s^{2}=e^{2 U}(r) d t^{2}+e^{-2 U}(r) d r^{2}+R^{2}(r) d^{2} \Omega \,
\pi _ { 0 } ( { \cal A } ( K _ { 1 } ) \vee { \cal A } ( K _ { \infty } ) ) \subset \pi _ { 0 } ( { \cal A } ( { \cal C } ) ) ^ { \prime } . | \pi_{0}({\cal A}(K_{1}) \vee{\cal A}(K_{\infty})) \subset \pi_{0}({\cal A}({\cal C}))^{\prime}.
\langle { \bf x } \vert U _ { 1 } ( t - t ^ { \prime } ) \vert { \bf x } \, ^ { \prime } \rangle = 2 { \frac { \partial } { \partial t ^ { \prime } } } D ^ { - } ( x ^ { \prime } - x ) , | \langle{\bf x} \vert U_{1}(t-t^{\prime}) \vert{\bf x} \,^{\prime} \rangle=2{\frac{\partial}{\partial t^{\prime}}} D^{-}(x^{\prime}-x),
C _ { s p i n } = \left( \begin{matrix} { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 1 } \\ { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { - 1 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { - 1 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 1 } & { 0 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { 0 } & { 0 } & { 1 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { 0 } & { - 1 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } \\ { 0 } & { - 1 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } \\ { 1 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } & { 0 } \\ \end{matrix} \right) \ . | C_{s p i n}=\left(\begin{matrix}{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{1}} \\{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{-1}} &{{0}} \\{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{-1}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{0}} &{{1}} &{{0}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{0}} &{{0}} &{{1}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{-1}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} \\{{0}} &{{-1}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} \\{{1}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} &{{0}} \\ \end{matrix} \right) \.
R _ { 1 2 } ( - \zeta ) = - \sigma _ { 1 } ^ { z } R _ { 1 2 } ( \zeta ) \sigma _ { 1 } ^ { z } ; | R_{1 2}(-\zeta)=-\sigma_{1}^{z} R_{1 2}(\zeta) \sigma_{1}^{z};
A ( \tau , N ) = \alpha _ { 0 } + \sum _ { k = 1 } ^ { \infty } \alpha _ { k } q ^ { k } | A(\tau,N)=\alpha_{0}+\sum_{k=1}^{\infty} \alpha_{k} q^{k}
Z = e ^ { - { \frac { 1 } { g } } - i \chi _ { 0 } } . | Z=e^{-{\frac{1}{g}}-i \chi_{0}}.
( P \cdot J ) _ { \alpha \beta } \tilde { F } ^ { \beta } = i \epsilon _ { \: \alpha \beta } ^ { \mu } p _ { \mu } \tilde { F } ^ { \beta } = - s m \tilde { F } _ { \alpha } | (P \cdot J)_{\alpha \beta} \tilde{F}^{\beta}=i \epsilon_{\:\alpha \beta}^{\mu} p_{\mu} \tilde{F}^{\beta}=-s m \tilde{F}_{\alpha}
{ \cal A } _ { p } = i \sqrt { \frac { 2 } { \pi } } { 2 ^ { ( 7 - p ) / 2 } } { \Gamma ( \frac { 9 - p } { 2 } ) } \mu ^ { ( p - 8 ) / 2 ( 7 - p ) } . | {\cal A}_{p}=i \sqrt{\frac{2}{\pi}}{2^{(7-p)/2}}{\Gamma(\frac{9-p}{2})} \mu^{(p-8)/2(7-p)}.
w ^ { T } \, X \, w \; = \; ( w ^ { T } \, X \, w ) ^ { T } \; = \; w ^ { T } \, X ^ { T } \, w \, . | w^{T} \,X \,w \;=\;(w^{T} \,X \,w)^{T} \;=\;w^{T} \,X^{T} \,w \,.
\tilde { T } ^ { - 1 } b \tilde { T } = b a , \; \; \tilde { T } ^ { - 1 } a \tilde { T } = a , \; \; \tilde { T } ^ { - 1 } \sigma \tilde { T } = \sigma . | \tilde{T}^{-1} b \tilde{T}=b a,\;\;\tilde{T}^{-1} a \tilde{T}=a,\;\;\tilde{T}^{-1} \sigma \tilde{T}=\sigma.
\tilde { u } = v ^ { 2 N - 4 } \left\langle \mathrm { T r } ( \tilde { q } q \tilde { q } q ) - { \frac { 1 } { 2 } } \mathrm { T r } ( \tilde { q } q ) \mathrm { T r } ( \tilde { q } q ) \right\rangle \ . | \tilde{u}=v^{2 N-4} \left\langle \mathrm{Tr}(\tilde{q} q \tilde{q} q)-{\frac{1}{2}} \mathrm{Tr}(\tilde{q} q) \mathrm{Tr}(\tilde{q} q) \right\rangle \.
\tilde { L } _ { D S } = { \cal R } L _ { D S } = \frac { \alpha ^ { \prime } \hbar H } { c ^ { 2 } } | \tilde{L}_{D S}={\cal R} L_{D S}=\frac{\alpha^{\prime} \hbar H}{c^{2}}
D _ { B } = { \frac { \alpha ^ { \prime } } { 4 \pi } } \cdot { \frac { 1 } { 2 } } \int _ { | z | \le 1 } d ^ { 2 } z { \frac { 1 } { | z | ^ { 2 } } } z ^ { L _ { 0 } } { \bar { z } } ^ { { \tilde { L } } _ { 0 } } | D_{B}={\frac{\alpha^{\prime}}{4 \pi}} \cdot{\frac{1}{2}} \int_{| z | \le 1} d^{2} z{\frac{1}{| z |^{2}}} z^{L_{0}}{\bar{z}}^{{\tilde{L}}_{0}}
k _ { 1 } ^ { 2 } ( s ) \, = \, - \, \ddot { x } _ { \mu } \, \ddot { x } ^ { \mu } \, = \, - \ddot { x } ^ { 2 } \, { . } | k_{1}^{2}(s) \,=\,-\,\ddot{x}_{\mu} \,\ddot{x}^{\mu} \,=\,-\ddot{x}^{2} \,{.}
\partial _ { + } ( \partial _ { x } \phi ) = 0 | \partial_{+}(\partial_{x} \phi)=0
\Theta _ { 1 } ( x ) = T _ { 2 } ( x ) , ~ ~ ~ \Theta _ { 2 } ( x ) = T _ { 4 } ( x ) | \Theta_{1}(x)=T_{2}(x),~ ~ ~ \Theta_{2}(x)=T_{4}(x)
L = \partial ^ { m } + \sum _ { j = 0 } ^ { m - 2 } U _ { j } \partial ^ { j } = \prod _ { j = 1 } ^ { m } ( \partial + P _ { j } ) | L=\partial^{m}+\sum_{j=0}^{m-2} U_{j} \partial^{j}=\prod_{j=1}^{m}(\partial+P_{j})
\phi _ { a } \Big ( \prod _ { i \in I } \Gamma ^ { i } ( t ) \Big ) : = \prod _ { i \in I } \Gamma ^ { i } ( a ) ~ , | \phi_{a} \Big(\prod_{i \in I} \Gamma^{i}(t) \Big):=\prod_{i \in I} \Gamma^{i}(a) ~,
{ \mit \Phi } _ { \sigma _ { 2 } \sigma _ { 1 } } = { \mit \Phi } _ { \sigma _ { 2 } \sigma _ { 1 } } ( \vec { k } , \hat { n } ) . | {\mit \Phi}_{\sigma_{2} \sigma_{1}}={\mit \Phi}_{\sigma_{2} \sigma_{1}}(\vec{k},\hat{n}).
( \lambda + ( u - v ) P ) L ( u ) _ { s } \otimes L ( v ) _ { s } = L ( v ) _ { s } \otimes L ( u ) _ { s } ( \lambda + ( u - v ) P ) | (\lambda+(u-v) P) L(u)_{s} \otimes L(v)_{s}=L(v)_{s} \otimes L(u)_{s}(\lambda+(u-v) P)
Z ^ { ( \mathrm { c o v ) } } ( G , q ) = \sum _ { \{ h _ { i } \} } \left[ N ^ { | h | } \Lambda ( h ) \right] ^ { 2 G - 2 } q ^ { | h | } ~ , | Z^{(\mathrm{cov)}}(G,q)=\sum_{\{h_{i} \}} \left[N^{| h |} \Lambda(h) \right]^{2 G-2} q^{| h |} ~,
\bar { \partial } J ^ { a } = - \eta \; \partial K ^ { a } \; + \; { \cal O } ( \epsilon ^ { 2 } ) , | \bar{\partial} J^{a}=-\eta \;\partial K^{a} \;+\;{\cal O}(\epsilon^{2}),
\gamma ( e ) = d i a g ( 0 , e ^ { 2 \pi i / N } , . . . , e ^ { 2 \pi i ( N - 1 ) / N } ) | \gamma(e)=d i a g(0,e^{2 \pi i/N},...,e^{2 \pi i(N-1)/N})
Q _ { s } = \int _ { \Sigma } ^ { } ( \nabla ^ { \ast } E ) _ { s } = \int _ { \Sigma } ^ { } \tilde { K } _ { s } \in \hbar \cdot { \bf Z } \; , | Q_{s}=\int_{\Sigma}^(\nabla^{\ast} E)_{s}=\int_{\Sigma}^ \tilde{K}_{s} \in \hbar \cdot{\bf Z} \;,
M = T \pm 2 \quad \mathrm { a n d } \quad N = Y , \qquad \mathrm { f o r } \quad | T | \ge | Y | | M=T \pm 2 \quad \mathrm{and} \quad N=Y,\qquad \mathrm{for} \quad | T | \ge | Y |
\phi ^ { \alpha } ( x ^ { 0 } , x ^ { 1 } ) \longrightarrow \phi ^ { \alpha } ( x ^ { 0 } , x ^ { 1 } ) \, + \, f ^ { \alpha } ( x ^ { 0 } ) , | \phi^{\alpha}(x^{0},x^{1}) \longrightarrow \phi^{\alpha}(x^{0},x^{1}) \,+\,f^{\alpha}(x^{0}),
X = i ( q - q ^ { - 1 } ) ^ { - 1 } \bigl ( q ^ { ( \hat { z } + 1 / 2 ) } - q ^ { - ( \hat { z } + 1 / 2 ) } \bigr ) \hat { p } ^ { - 1 } . | X=i(q-q^{-1})^{-1} \bigl(q^{(\hat{z}+1/2)}-q^{-(\hat{z}+1/2)} \bigr) \hat{p}^{-1}.
\langle \, , \, \rangle : ( a , u ) \rightarrow \langle a , u \rangle \quad \forall \, a \in { \cal A } \, , \quad \forall \, u \in { \cal U } \, , | \langle \,,\,\rangle:(a,u) \rightarrow \langle a,u \rangle \quad \forall \,a \in{\cal A} \,,\quad \forall \,u \in{\cal U} \,,
F _ { \theta \phi } = \sqrt { B ^ { 2 } - \tau _ { 0 } ^ { 4 } } s i n h \theta . | F_{\theta \phi}=\sqrt{B^{2}-\tau_{0}^{4}} s i n h \theta.
\frac { d } { d x } \left( \frac { \delta F } { \delta \partial _ { x } T } \right) - \frac { d F } { d T } = 0 | \frac{d}{d x} \left(\frac{\delta F}{\delta \partial_{x} T} \right)-\frac{d F}{d T}=0
\Pi _ { \mu \nu } \big | _ { \mathrm { o d d } } = g ^ { 2 } c _ { v } \frac { 7 } { 1 2 \pi } \epsilon _ { \mu \rho \nu } p ^ { \rho } . | \Pi_{\mu \nu} \big|_{\mathrm{odd}}=g^{2} c_{v} \frac{7}{1 2 \pi} \epsilon_{\mu \rho \nu} p^{\rho}.
\frac { D ( a ) } { a } = { } _ { 2 } F _ { 1 } \left[ - \frac { 1 } { 3 w } , \frac { w - 1 } { 2 w } , 1 - \frac { 5 } { 6 w } , - a ^ { - 3 w } \frac { 1 - \Omega _ { N R } } { \Omega _ { N R } } \right] | \frac{D(a)}{a}=_{2} F_{1} \left[-\frac{1}{3 w},\frac{w-1}{2 w},1-\frac{5}{6 w},-a^{-3 w} \frac{1-\Omega_{N R}}{\Omega_{N R}} \right]
\delta C _ { k } ^ { ( 2 k + 2 ) } [ f ] = 0 \mathrm { ~ a s ~ } g _ { \mu \nu } \rightarrow e ^ { 2 \delta \omega } g _ { \mu \nu } , \: \: \: f \rightarrow e ^ { - 2 \delta \omega } f | \delta C_{k}^{(2 k+2)}[f]=0 \mathrm{~as~} g_{\mu \nu} \rightarrow e^{2 \delta \omega} g_{\mu \nu},\:\:\:f \rightarrow e^{-2 \delta \omega} f
\Omega _ { \left( \mu \nu \xi \right) } = g _ { \alpha \beta } \Omega _ { \left( \mu \nu \xi \right) } ^ { \left\{ \alpha \beta \right\} } \, . | \Omega_{\left(\mu \nu \xi \right)}=g_{\alpha \beta} \Omega_{\left(\mu \nu \xi \right)}^{\left\{\alpha \beta \right\}} \,.
\partial ( ( 1 / c ) E \wedge v + e _ { 5 } B \cdot v ) = j / \varepsilon _ { 0 } c . | \partial((1/c) E \wedge v+e_{5} B \cdot v)=j/\varepsilon_{0} c.
\left( \omega _ { \alpha \beta } \right) = \left( \begin{array} { c c c } { g _ { \mu \nu } } & { 0 } & { 0 } \\ { 0 } & { 0 } & { - 1 } \\ { 0 } & { - 1 } & { 0 } \\ \end{array} \right) , | \left(\omega_{\alpha \beta} \right)=\left(\begin{array}{ccc}{{g_{\mu \nu}}} &{{0}} &{{0}} \\{{0}} &{{0}} &{{-1}} \\{{0}} &{{-1}} &{{0}} \end{array} \right),
` ` \sigma _ { z } " \psi ( \bar { q } , \sigma ) = \sigma \psi ( \bar { q } , \sigma ) | ` ` \sigma_{z} " \psi(\bar{q},\sigma)=\sigma \psi(\bar{q},\sigma)
W ( \phi ) = - \frac { 2 ( d - 1 ) } { l } + { \frac { 1 } { 2 } } \sum _ { a } \lambda _ { a } \, \phi _ { a } ^ { 2 } + \frac { 1 } { 3 ! } \, \sum _ { a , b , c } \lambda _ { a b c } \, \phi _ { a } \phi _ { b } \phi _ { c } + \cdots , | W(\phi)=-\frac{2(d-1)}{l}+{\frac{1}{2}} \sum_{a} \lambda_{a} \,\phi_{a}^{2}+\frac{1}{3 !} \,\sum_{a,b,c} \lambda_{a b c} \,\phi_{a} \phi_{b} \phi_{c}+. . .,
t _ { a b } = \left( \begin{array} { c c } { 1 } & { \omega ^ { - 1 } } \\ { \omega } & { 1 } \\ \end{array} \right) . | t_{a b}=\left(\begin{array}{cc}{{1}} &{{\omega^{-1}}} \\{{\omega}} &{{1}} \end{array} \right).
S = \int d ^ { 2 } x \, G _ { a b } ( x ) \partial _ { \mu } \xi ^ { a } \partial ^ { \mu } \xi ^ { b } \, . | S=\int d^{2} x \,G_{a b}(x) \partial_{\mu} \xi^{a} \partial^{\mu} \xi^{b} \,.
\{ \Gamma _ { \alpha } , \Gamma _ { \beta } \} = \{ \gamma _ { \alpha } , \gamma _ { \beta } \} = \eta _ { \alpha \beta } | \{\Gamma_{\alpha},\Gamma_{\beta} \}=\{\gamma_{\alpha},\gamma_{\beta} \}=\eta_{\alpha \beta}
\theta ( x , y , \tilde { x } , \tilde { y } , p , q ) = \Lambda ( x + \tilde { x } , y + \tilde { y } , p , q ) | \theta(x,y,\tilde{x},\tilde{y},p,q)=\Lambda(x+\tilde{x},y+\tilde{y},p,q)
{ \tilde { R } } _ { m n } \, ^ { p q } \Gamma _ { p q } \eta _ { \pm } = 0 \, . | {\tilde{R}}_{m n} \,^{p q} \Gamma_{p q} \eta_{\pm}=0 \,.
V _ { \alpha } ( \theta , \epsilon , \delta ) \Psi [ C ] \stackrel { \delta , \epsilon \rightarrow 0 } { \longrightarrow } v _ { \alpha } \left( C ( \theta ) \right) \Psi [ C ] | V_{\alpha}(\theta,\epsilon,\delta) \Psi[C] \stackrel{\delta{,} \epsilon \rightarrow 0}{\longrightarrow} v_{\alpha} \left(C(\theta) \right) \Psi[C]
X _ { \alpha \beta } ^ { i j } = x _ { \alpha \beta } ^ { i j } ( p _ { a } \rightarrow \pi _ { a } ) + e y _ { \alpha \beta } ^ { i j } | X_{\alpha \beta}^{i j}=x_{\alpha \beta}^{i j}(p_{a} \rightarrow \pi_{a})+e y_{\alpha \beta}^{i j}
\gamma _ { \Psi } a _ { 1 } = \gamma _ { \Psi } ( - b ^ { * } C ) = \overline { { C } } ^ { * } C . | \gamma_{\Psi} a_{1}=\gamma_{\Psi}(-b^{*} C)=\overline{{{C}}}^{*} C.
\mathrm { ~ \frac { 1 } { 2 } ~ } ( S _ { m ( 0 ) } , S _ { m ( 0 ) } ) ^ { a } + V _ { m } ^ { a } S _ { m ( 0 ) } = 0 , \qquad \mathrm { ~ \frac { 1 } { 2 } ~ } \{ S _ { m ( 0 ) } , S _ { m ( 0 ) } \} _ { \alpha } + V _ { \alpha } S _ { m ( 0 ) } = 0 . | \mathrm{~\frac{1}{2} ~}(S_{m(0)},S_{m(0)})^{a}+V_{m}^{a} S_{m(0)}=0,\qquad \mathrm{~\frac{1}{2} ~} \{S_{m(0)},S_{m(0)} \}_{\alpha}+V_{\alpha} S_{m(0)}=0.
B _ { [ J ] i a } = { \frac { S _ { i a } } { \sqrt { S _ { i J } } } } \; \; . | B_{[J] i a}={\frac{S_{i a}}{\sqrt{S_{i J}}}} \;\;.
( E _ { 2 s } - Q - Q ^ { - 1 } ) A ( k ) = A ( k - 1 ) + A ( k + 1 ) , \quad 2 \leq k \leq N - 2 | (E_{2 s}-Q-Q^{-1}) A(k)=A(k-1)+A(k+1),\quad 2 \leq k \leq N-2
( \psi _ { f } ^ { + } ( { \bf 1 } ) _ { \alpha \beta } ) = \epsilon _ { \alpha \beta } \eta _ { f } ^ { + } ( { \bf 1 } ) , | (\psi_{f}^{+}({\bf 1})_{\alpha \beta})=\epsilon_{\alpha \beta} \eta_{f}^{+}({\bf 1}),
x _ { \mu } ^ { \prime 2 } = x ^ { \prime } \overline { { x } } ^ { \prime } = L x \overline { { L } } ^ { * } L ^ { * } \overline { { x } } \overline { { L } } = x \overline { { x } } = x _ { \mu } ^ { 2 } , | x_{\mu}^{\prime 2}=x^{\prime} \overline{{{x}}}^{\prime}=L x \overline{{{L}}}^{*} L^{*} \overline{{{x}}} \overline{{{L}}}=x \overline{{{x}}}=x_{\mu}^{2},
f _ { 0 } = - \frac { 3 } { 2 } z ( 1 - z ^ { 2 } ) , \; f _ { 1 } = \frac { 3 } { 2 } ( 1 - z ^ { 2 } ) , \; f _ { 2 } = \frac { 9 } { 4 } z ^ { 2 } ( 1 - \frac { 1 } { 3 } z ^ { 2 } ) ^ { 2 } - 1 . | f_{0}=-\frac{3}{2} z(1-z^{2}),\;f_{1}=\frac{3}{2}(1-z^{2}),\;f_{2}=\frac{9}{4} z^{2}(1-\frac{1}{3} z^{2})^{2}-1.
b _ { \alpha } = x ^ { \mu } \left( \gamma _ { \mu } \right) _ { \alpha } ^ { \beta } a _ { \beta } . | b_{\alpha}=x^{\mu} \left(\gamma_{\mu} \right)_{\alpha}^{\beta} a_{\beta}.
{ \tilde { j } } _ { \mu } ( x ) = - { \tilde { g } } [ { \bar { \psi } } ( x ) \gamma _ { \mu } T ^ { i } \psi ( x ) ] \tau _ { i } , | {\tilde{j}}_{\mu}(x)=-{\tilde{g}}[{\bar{\psi}}(x) \gamma_{\mu} T^{i} \psi(x)] \tau_{i},
( \dot { x } _ { \mu } - v J _ { \mu } ) ^ { 2 } + e ^ { 2 } m ^ { 2 } = 0 , \quad \dot { x } J - v J ^ { 2 } - s m e = 0 . | (\dot{x}_{\mu}-v J_{\mu})^{2}+e^{2} m^{2}=0,\quad \dot{x} J-v J^{2}-s m e=0.
\left( \vec { \sigma } + \vec { \tau } \right) \Xi = 0 | \left(\vec{\sigma}+\vec{\tau} \right) \Xi=0
s _ { n } \rightarrow { \frac { 1 } { 4 n } } e ^ { - ( 1 + \sqrt { \mu } ) \bar { A } _ { + } n } ~ ~ i f ~ ~ A _ { + } \rightarrow \infty | s_{n} \rightarrow{\frac{1}{4 n}} e^{-(1+\sqrt{\mu}) \bar{A}_{+} n} ~ ~ i f ~ ~ A_{+} \rightarrow \infty
\begin{array} { l l } { ( 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ - \frac { 7 } { 2 } ) \longrightarrow } & { ~ ( 0 ~ 0 ~ 0 ) ( 0 ~ 0 ~ 0 ~ - \frac { 7 } { 2 } ) ( 2 ) \oplus ( 0 ~ 0 ~ 1 ) ( 0 ~ 0 ~ 0 ~ - \frac { 5 } { 2 } ) ( 1 ) } \\ { } & { \oplus ~ ( 0 ~ 1 ~ 0 ) ( 0 ~ 0 ~ 0 ~ - \frac { 3 } { 2 } ) ( 0 ) \oplus ( 1 ~ 0 ~ 0 ) ( 0 ~ 0 ~ 0 ~ - \frac { 1 } { 2 } ) ( - 1 ) } \\ { } & { \oplus ~ ( 0 ~ 0 ~ 0 ) ( 0 ~ 0 ~ 0 ~ \frac { 1 } { 2 } ) ( - 2 ) , } \\ \end{array} | \begin{array}{ll}{{(0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~ 0 ~-\frac{7}{2}) \longrightarrow}} &{{~(0 ~ 0 ~ 0)(0 ~ 0 ~ 0 ~-\frac{7}{2})(2) \oplus(0 ~ 0 ~ 1)(0 ~ 0 ~ 0 ~-\frac{5}{2})(1)}} \\ &{{\oplus ~(0 ~ 1 ~ 0)(0 ~ 0 ~ 0 ~-\frac{3}{2})(0) \oplus(1 ~ 0 ~ 0)(0 ~ 0 ~ 0 ~-\frac{1}{2})(-1)}} \\ &{{\oplus ~(0 ~ 0 ~ 0)(0 ~ 0 ~ 0 ~ \frac{1}{2})(-2),}} \end{array}
E _ { n } \sim p _ { - } ^ { 2 } \tilde { \mu } ^ { 2 } L ^ { 2 } , \ \ \ \ \mathrm { f o r } \ \ p _ { - } ^ { 2 } | \mu ^ { 2 } | L ^ { 4 } \gg n ^ { 2 } , | E_{n} \sim p_{-}^{2} \tilde{\mu}^{2} L^{2},\ \ \ \ \mathrm{for} \ \ p_{-}^{2} | \mu^{2} | L^{4} \gg n^{2},
S _ { ( n ) } = \int d ^ { 4 } x \Bigl [ \frac { 1 } { 6 } \, H _ { ( n ) } ^ { \mu \nu \rho } H _ { ( n ) \mu \nu \rho } ^ { \ast } - \frac { 2 n ^ { 2 } \pi ^ { 2 } } { R ^ { 2 } } \Bigl ( i B _ { ( n ) } ^ { \mu \nu } - \frac { R } { 2 n \pi } \, F _ { ( n ) } ^ { \mu \nu } \Bigr ) \Bigl ( - \, i B _ { ( n ) \mu \nu } ^ { \ast } - \frac { R } { 2 n \pi } \, F _ { ( n ) \mu \nu } ^ { \ast } \Bigr ) \Bigr ] | S_{(n)}=\int d^{4} x \Bigl[\frac{1}{6} \,H_{(n)}^{\mu \nu \rho} H_{(n) \mu \nu \rho}^{\ast}-\frac{2 n^{2} \pi^{2}}{R^{2}} \Bigl(i B_{(n)}^{\mu \nu}-\frac{R}{2 n \pi} \,F_{(n)}^{\mu \nu} \Bigr) \Bigl(-\,i B_{(n) \mu \nu}^{\ast}-\frac{R}{2 n \pi} \,F_{(n) \mu \nu}^{\ast} \Bigr) \Bigr]
\begin{array} { c } { \{ A _ { i } , \underline { { g } } ( \xi ) \} = - \partial _ { i } \xi \ , } \\ { \{ E _ { i } , \underline { { g } } ( \xi ) \} = 0 \ , } \\ { \{ \psi , \underline { { g } } ( \xi ) \} = \xi \psi \ . } \\ \end{array} | \begin{array}{c}{{\{A_{i},\underline{{{g}}}(\xi) \}=-\partial_{i} \xi \,}} \\{{\{E_{i},\underline{{{g}}}(\xi) \}=0 \,}} \\{{\{\psi,\underline{{{g}}}(\xi) \}=\xi \psi \.}} \end{array}
\left\langle w ^ { \alpha } ( T ) \right\rangle ^ { 1 / \alpha } = \frac { \left\langle w ^ { \alpha } ( 1 ) \right\rangle ^ { 1 / \alpha ( 0 ) } } { L ^ { p } } | \left\langle w^{\alpha}(T) \right\rangle^{1/\alpha}=\frac{\left\langle w^{\alpha}(1) \right\rangle^{1/\alpha(0)}}{L^{p}}
\sigma = { \frac { 1 } { 4 5 } } \left( - N _ { 0 } - \frac 7 4 N _ { 1 / 2 } + 1 3 N _ { 1 } + { \frac { 2 3 3 } { 4 } } N _ { 3 / 2 } - 2 1 2 N _ { 2 } \right) ~ ~ ~ . | \sigma={\frac{1}{4 5}} \left(-N_{0}-\frac{7}{4} N_{1/2}+1 3 N_{1}+{\frac{2 3 3}{4}} N_{3/2}-2 1 2 N_{2} \right) ~ ~ ~.
y ^ { 2 } = x ^ { 3 } + \sum _ { k = - 4 } ^ { 4 } \sum _ { l = 0 } ^ { 1 2 - n k } f _ { k l } z ^ { \prime l } z ^ { 4 + k } \ldots a ^ { 4 - 6 k } x + \sum _ { k = - 6 } ^ { 6 } \sum _ { l = 0 } ^ { 1 2 - n k } g _ { k l } z ^ { \prime l } z ^ { 6 + k } \ldots a ^ { 6 - 6 k } | y^{2}=x^{3}+\sum_{k=-4}^{4} \sum_{l=0}^{1 2-n k} f_{k l} z^{\prime l} z^{4+k} . . . a^{4-6 k} x+\sum_{k=-6}^{6} \sum_{l=0}^{1 2-n k} g_{k l} z^{\prime l} z^{6+k} . . . a^{6-6 k}
B ( x ) = \sum _ { i } B _ { i } ^ { l o c } ( x ) | B(x)=\sum_{i} B_{i}^{l o c}(x)
p _ { i } = \Omega _ { i \, i _ { 1 } i _ { 2 } i _ { 3 } i _ { 4 } } \epsilon _ { i _ { 1 } i _ { 2 } i _ { 3 } i _ { 4 } j _ { 1 } j _ { 2 } j _ { 3 } j _ { 4 } } c _ { j _ { 1 } } c _ { j _ { 2 } } c _ { j _ { 3 } } c _ { j _ { 4 } } \quad , | p_{i}=\Omega_{i \,i_{1} i_{2} i_{3} i_{4}} \epsilon_{i_{1} i_{2} i_{3} i_{4} j_{1} j_{2} j_{3} j_{4}} c_{j_{1}} c_{j_{2}} c_{j_{3}} c_{j_{4}} \quad,
U ^ { I } ( i ) = P e x p ( \int _ { s ( i ) } ^ { t ( i ) } A ^ { I } ) . | U^{I}(i)=P e x p(\int_{s(i)}^{t(i)} A^{I}).
x _ { 1 } ^ { 2 } + x _ { 2 } ^ { 2 } + x _ { 3 } ^ { 2 } = 1 | x_{1}^{2}+x_{2}^{2}+x_{3}^{2}=1
2 M _ { * } ^ { 4 } ( R _ { A B } - g _ { A B } R ) = - \frac { 1 } { 2 } g _ { A B } \Lambda _ { B } + 8 \pi \delta ( r - a ) T _ { A B } ^ { a } + 8 \pi \delta ( r - b ) T _ { A B } ^ { b } \ . | 2 M_{*}^{4}(R_{A B}-g_{A B} R)=-\frac{1}{2} g_{A B} \Lambda_{B}+8 \pi \delta(r-a) T_{A B}^{a}+8 \pi \delta(r-b) T_{A B}^{b} \.
I _ { e l } = I - \frac { 1 } { 4 \pi } \int _ { \Sigma } d ^ { 3 } x \sqrt { h } F ^ { \mu \nu } n _ { \mu } A _ { \nu } , | I_{e l}=I-\frac{1}{4 \pi} \int_{\Sigma} d^{3} x \sqrt{h} F^{\mu \nu} n_{\mu} A_{\nu},
\gamma _ { \Omega h } ^ { T } = \pm \gamma _ { \Omega h } . | \gamma_{\Omega h}^{T}=\pm \gamma_{\Omega h}.
\left[ \frac { 1 } { \sqrt { - g } } \frac { \partial } { \partial x ^ { i } } \left( \sqrt { - g } g ^ { i j } \frac { \partial } { \partial x ^ { i } } \right) - m _ { 0 } ^ { 2 } \right] \Phi ( x ) = 0 ~ . | \left[\frac{1}{\sqrt{-g}} \frac{\partial}{\partial x^{i}} \left(\sqrt{-g} g^{i j} \frac{\partial}{\partial x^{i}} \right)-m_{0}^{2} \right] \Phi(x)=0 ~.
f _ { k } ( \rho ) = \left\{ \begin{array} { l l } { C _ { k , n } . e ^ { - \frac { \rho ^ { 2 } } { 2 } } \rho ^ { k } L _ { n } ^ { k } ( \rho ^ { 2 } ) } & { \textrm { s i k \geq 0 } } \\ { C _ { k , n } . e ^ { - \frac { \rho ^ { 2 } } { 2 } } \rho ^ { - k } L _ { n + k } ^ { - k } ( \rho ^ { 2 } ) } & { \textrm { s i - n \leq k < 0 } \ } \\ \end{array} \right. . | f_{k}(\rho)=\left\{\begin{array}{ll}{{C_{k,n}.e^{-\frac{\rho^{2}}{2}} \rho^{k} L_{n}^{k}(\rho^{2})}} &{{\textrm{s i k \geq 0}}} \\{{C_{k,n}.e^{-\frac{\rho^{2}}{2}} \rho^{-k} L_{n+k}^{-k}(\rho^{2})}} &{{\textrm{s i-n \leq k<0} \}} \end{array} \right..
\frac { 1 } { 2 \pi } \varepsilon ^ { \mu \nu \sigma } \partial _ { \mu } \mathrm { T r } | \frac{1}{2 \pi} \varepsilon^{\mu \nu \sigma} \partial_{\mu} \mathrm{Tr}
d s _ { ( b _ { 0 } , \bar { b } _ { 0 } ) } ^ { 2 } = l ^ { 2 } \left[ - d \tau ^ { 2 } + \frac { b _ { 0 } } { c / 6 } \frac { d z ^ { 2 } } { z ^ { 2 } } + \frac { \bar { b } _ { 0 } } { c / 6 } \frac { d \bar { z } ^ { 2 } } { \bar { z } ^ { 2 } } + \left( e ^ { - 2 \tau } + \frac { b _ { 0 } \bar { b } _ { 0 } } { ( c / 6 ) ^ { 2 } z ^ { 2 } \bar { z } ^ { 2 } } e ^ { 2 \tau } \right) d z d \bar { z } \right] , | d s_{(b_{0},\bar{b}_{0})}^{2}=l^{2} \left[-d \tau^{2}+\frac{b_{0}}{c/6} \frac{d z^{2}}{z^{2}}+\frac{\bar{b}_{0}}{c/6} \frac{d \bar{z}^{2}}{\bar{z}^{2}}+\left(e^{-2 \tau}+\frac{b_{0} \bar{b}_{0}}{(c/6)^{2} z^{2} \bar{z}^{2}} e^{2 \tau} \right) d z d \bar{z} \right],
\left[ \hat { M } _ { \hat { a } \hat { b } } , \hat { M } _ { \hat { c } \hat { d } } \right] = - \hat { \eta } _ { \hat { a } \hat { c } } \hat { M } _ { \hat { b } \hat { d } } - \hat { \eta } _ { \hat { b } \hat { d } } \hat { M } _ { \hat { a } \hat { c } } + \hat { \eta } _ { \hat { a } \hat { d } } \hat { M } _ { \hat { b } \hat { c } } + \hat { \eta } _ { \hat { b } \hat { c } } \hat { M } _ { \hat { a } \hat { d } } \, . | \left[\hat{M}_{\hat{a} \hat{b}},\hat{M}_{\hat{c} \hat{d}} \right]=-\hat{\eta}_{\hat{a} \hat{c}} \hat{M}_{\hat{b} \hat{d}}-\hat{\eta}_{\hat{b} \hat{d}} \hat{M}_{\hat{a} \hat{c}}+\hat{\eta}_{\hat{a} \hat{d}} \hat{M}_{\hat{b} \hat{c}}+\hat{\eta}_{\hat{b} \hat{c}} \hat{M}_{\hat{a} \hat{d}} \,.
\partial _ { 0 } \psi ^ { \alpha } ( x ) = \{ \psi ^ { \alpha } , H \} _ { D } = M _ { \alpha \beta } \partial _ { 1 } \psi ^ { \beta } ( x ) \, , | \partial_{0} \psi^{\alpha}(x)=\{\psi^{\alpha},H \}_{D}=M_{\alpha \beta} \partial_{1} \psi^{\beta}(x) \,,
\delta _ { j _ { 1 } 0 } \delta _ { j _ { 2 } 0 } = \int _ { 0 } ^ { \frac { 2 \pi } { l } } d k \int _ { 0 } ^ { l / A } d q e ^ { - i j _ { 1 } l k + i j _ { 2 } l \tau _ { 2 } q } | \tilde { c } ( k , q ) | ^ { 2 } \sum _ { n = 0 } ^ { A - 1 } | C _ { 0 } ( k , q + \frac { l n } { A } ) | ^ { 2 } . | \delta_{j_{1} 0} \delta_{j_{2} 0}=\int_{0}^{\frac{2 \pi}{l}} d k \int_{0}^{l/A} d q e^{-i j_{1} l k+i j_{2} l \tau_{2} q} | \tilde{c}(k,q) |^{2} \sum_{n=0}^{A-1} | C_{0}(k,q+\frac{l n}{A}) |^{2}.
\begin{array} { c c c c } { z : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , e ^ { 2 \pi i z } x ^ { 6 , 7 } , e ^ { - 2 \pi i z } x ^ { 8 , 9 } \right) } \\ { y : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( e ^ { 2 \pi i y } x ^ { 2 , 3 } , e ^ { - 2 \pi i y } x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } \\ { x : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( e ^ { - 2 \pi i x } x ^ { 2 , 3 } , x ^ { 4 , 5 } , e ^ { 2 \pi i x } x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } \\ { w : } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , x ^ { 6 , 7 } , x ^ { 8 , 9 } \right) } & { \to } & { \left( x ^ { 2 , 3 } , x ^ { 4 , 5 } , e ^ { - 2 \pi i w } x ^ { 6 , 7 } , e ^ { - 2 \pi i w } x ^ { 8 , 9 } \right) } \\ \end{array} | \begin{array}{cccc}{{z:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(x^{2,3},x^{4,5},e^{2 \pi i z} x^{6,7},e^{-2 \pi i z} x^{8,9} \right)}} \\{{y:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(e^{2 \pi i y} x^{2,3},e^{-2 \pi i y} x^{4,5},x^{6,7},x^{8,9} \right)}} \\{{x:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(e^{-2 \pi i x} x^{2,3},x^{4,5},e^{2 \pi i x} x^{6,7},x^{8,9} \right)}} \\{{w:}} &{{\left(x^{2,3},x^{4,5},x^{6,7},x^{8,9} \right)}} &{{\to}} &{{\left(x^{2,3},x^{4,5},e^{-2 \pi i w} x^{6,7},e^{-2 \pi i w} x^{8,9} \right)}} \end{array}
H _ { 1 } = 1 + { \frac { g \alpha ^ { \prime } N _ { 1 } } { v r ^ { 2 } } } , \ \ \ \ H _ { 5 } = 1 + { \frac { g \alpha ^ { \prime } N _ { 5 } } { r ^ { 2 } } } , | H_{1}=1+{\frac{g \alpha^{\prime} N_{1}}{v r^{2}}},\ \ \ \ H_{5}=1+{\frac{g \alpha^{\prime} N_{5}}{r^{2}}},
\frac { \partial ^ { [ 2 ( n - l ) / l ] } } { \partial x _ { i _ { 1 } , 1 } \dots \partial x _ { i _ { [ 2 ( n - l ) / l ] } , 1 } } , | \frac{\partial^{[2(n-l)/l]}}{\partial x_{i_{1},1} . . . \partial x_{i_{[2(n-l)/l]},1}},
{ \cal V } _ { R } = \partial X ( z ) \; \bar { \partial } \widetilde { X } ( \bar { z } ) ~ . | {\cal V}_{R}=\partial X(z) \;\bar{\partial} \widetilde{X}(\bar{z}) ~.
\left[ i \gamma ^ { \mu } \partial _ { \mu } - m \right] \Psi ( x ^ { \mu } ) = 0 \quad , \quad \hbar = c = 1 \quad , | \left[i \gamma^{\mu} \partial_{\mu}-m \right] \Psi(x^{\mu})=0 \quad,\quad \hbar=c=1 \quad,
{ { M _ { V } } _ { \alpha } } ^ { \beta } = \delta _ { \alpha } ^ { \beta } ( - \Box ) - { R _ { \alpha } } ^ { \beta } \ . | {{M_{V}}_{\alpha}}^{\beta}=\delta_{\alpha}^{\beta}(-\Box)-{R_{\alpha}}^{\beta} \.
2 \kappa ^ { 2 } e ^ { - 1 } L = R + 4 m ^ { 2 } e ^ { ( 2 \lambda _ { 1 } + 2 \lambda _ { 2 } ) } - 5 \partial _ { \mu } ( \lambda _ { 1 } + \lambda _ { 2 } ) ^ { 2 } - \partial _ { \mu } ( \lambda _ { 1 } - \lambda _ { 2 } ) ^ { 2 } - e ^ { - 4 \lambda _ { 1 } } { F _ { \mu \nu } ^ { ( 1 ) } } ^ { 2 } - e ^ { - 4 \lambda _ { 2 } } { F _ { \mu \nu } ^ { ( 2 ) } } ^ { 2 } . | 2 \kappa^{2} e^{-1} L=R+4 m^{2} e^{(2 \lambda_{1}+2 \lambda_{2})}-5 \partial_{\mu}(\lambda_{1}+\lambda_{2})^{2}-\partial_{\mu}(\lambda_{1}-\lambda_{2})^{2}-e^{-4 \lambda_{1}}{F_{\mu \nu}^{(1)}}^{2}-e^{-4 \lambda_{2}}{F_{\mu \nu}^{(2)}}^{2}.
\hat { H } = \frac { 1 } { 2 } \hat { p } ^ { 2 } + \frac { m ^ { 2 } } { 2 } \hat { q } ^ { 2 } + \frac { \lambda } { 4 } \hat { q } ^ { 4 } . | \hat{H}=\frac{1}{2} \hat{p}^{2}+\frac{m^{2}}{2} \hat{q}^{2}+\frac{\lambda}{4} \hat{q}^{4}.
M _ { B P S } ^ { 2 } = { \frac { 1 } { 8 g _ { s } ^ { 2 } } } \vec { Q } _ { R } ^ { 2 } , | M_{B P S}^{2}={\frac{1}{8 g_{s}^{2}}} \vec{Q}_{R}^{2},
\left\{ \not \! \partial ( \phi + \psi / 2 ) + e ^ { 3 \psi / 8 + \phi / 2 } \gamma _ { 7 } \left( { \bf F } ^ { 1 } + \kappa { \bf F } ^ { 2 } \right) - i s _ { z } e ^ { \phi } \gamma _ { 7 } \not \! \partial \kappa \right\} \epsilon _ { 6 } = 0 , | \left\{\not \! \partial(\phi+\psi/2)+e^{3 \psi/8+\phi/2} \gamma_{7} \left({\bf F}^{1}+\kappa{\bf F}^{2} \right)-i s_{z} e^{\phi} \gamma_{7} \not \! \partial \kappa \right\} \epsilon_{6}=0,
\langle \mu \mu \mu \mu \rangle = | z _ { 1 2 } z _ { 3 4 } | ^ { \frac 1 2 } | x ( 1 - x ) | ^ { \frac 1 2 } \left( F ( x ) \overline { { F ( 1 - x ) } } + F ( 1 - x ) \overline { { F ( x ) } } \right) , | \langle \mu \mu \mu \mu \rangle=| z_{1 2} z_{3 4} |^{\frac{1}{2}} | x(1-x) |^{\frac{1}{2}} \left(F(x) \overline{{{F(1-x)}}}+F(1-x) \overline{{{F(x)}}} \right),
V _ { Q } ^ { \prime } \, = \, \frac { { \hbar } ^ { 2 } } { 8 M } \left\{ R + \frac { ( \partial _ { a } f ) ( \partial ^ { a } f ) } { 4 M } \left[ 3 g _ { \phantom { 0 } , 0 } ^ { i j } \, g _ { i j , 0 } - { \left( g ^ { i j } \, g _ { i j , 0 } \right) } ^ { 2 } \right] \right\} . | V_{Q}^{\prime} \,=\,\frac{{\hbar}^{2}}{8 M} \left\{R+\frac{(\partial_{a} f)(\partial^{a} f)}{4 M} \left[3 g_{\phantom{0},0}^{i j} \,g_{i j,0}-{\left(g^{i j} \,g_{i j,0} \right)}^{2} \right] \right\}.
M _ { S } = \widetilde { M } _ { S } + i { \cal A } \times I , | M_{S}=\widetilde{M}_{S}+i{\cal A} \times I,
\Box _ { q } \, l _ { q } = q ^ { - 4 } l _ { q } \, \Box _ { q } + q ^ { - 2 } s + ( q ^ { 2 } + 1 ) \; . | \Box_{q} \,l_{q}=q^{-4} l_{q} \,\Box_{q}+q^{-2} s+(q^{2}+1) \;.
T _ { \mu \nu } ^ { a } = D _ { \mu } e _ { v } ^ { a } - D _ { v } e _ { \mu } ^ { a } . | T_{\mu \nu}^{a}=D_{\mu} e_{v}^{a}-D_{v} e_{\mu}^{a}.
\gamma ^ { i } M = \xi ^ { i } M \gamma ^ { i } , \quad M ^ { T } = \eta M , | \gamma^{i} M=\xi^{i} M \gamma^{i},\quad M^{T}=\eta M,
P V \frac { 1 } { r ^ { + } } = \frac { 1 } { 2 } \left[ \frac { 1 } { r ^ { + } + i \epsilon } + \frac { 1 } { r ^ { + } - i \epsilon } \right] \, \, , | P V \frac{1}{r^{+}}=\frac{1}{2} \left[\frac{1}{r^{+}+i \epsilon}+\frac{1}{r^{+}-i \epsilon} \right] \,\,,
S [ A _ { i j } ] = - \int d x ^ { 0 } d ^ { 5 } x B ^ { i j } \partial _ { 0 } A _ { i j } - \int d x ^ { 0 } H | S[A_{i j}]=-\int d x^{0} d^{5} x B^{i j} \partial_{0} A_{i j}-\int d x^{0} H
\psi _ { S E 2 } ( x , { \bf p } ; s _ { 1 } , s _ { 2 } ) = - \int { \frac { ( 1 - x ) d p ^ { - } } { 2 \pi } } { \frac { \bar { u } ( x P ^ { + } , { \bf p } ; s _ { 1 } ) \gamma ^ { + } \Phi ( p ) \Sigma _ { 1 } ( p - P ) v ( ( 1 - x ) P ^ { + } , - { \bf p } ; s _ { 2 } ) } { ( p - P ) ^ { 2 } - m ^ { 2 } + i \epsilon } } , | \psi_{S E 2}(x,{\bf p};s_{1},s_{2})=-\int{\frac{(1-x) d p^{-}}{2 \pi}}{\frac{\bar{u}(x P^{+},{\bf p};s_{1}) \gamma^{+} \Phi(p) \Sigma_{1}(p-P) v((1-x) P^{+},-{\bf p};s_{2})}{(p-P)^{2}-m^{2}+i \epsilon}},
c h _ { \Gamma } ^ { \gamma } ( E ) = \sum _ { i = 1 } ^ { s } \lambda _ { i } c h ( E ^ { i } ) | c h_{\Gamma}^{\gamma}(E)=\sum_{i=1}^{s} \lambda_{i} c h(E^{i})
+ { \frac { \bar { \kappa } } { 2 \pi } } \left( \bar { S } + \bar { Q } _ { ( m ) } \right) + \int _ { C _ { \infty } } \sqrt { \gamma } d ^ { 2 } y \bar { B } \bar { N } ~ ~ ~ . | +{\frac{\bar{\kappa}}{2 \pi}} \left(\bar{S}+\bar{Q}_{(m)} \right)+\int_{C_{\infty}} \sqrt{\gamma} d^{2} y \bar{B} \bar{N} ~ ~ ~.
\partial _ { R } \left[ \sqrt { \frac { C K ^ { d - 2 } } { A } } R ^ { d - 2 } \partial _ { R } H \right] = 0 \ . | \partial_{R} \left[\sqrt{\frac{C K^{d-2}}{A}} R^{d-2} \partial_{R} H \right]=0 \.
\kappa _ { m } = { \frac { g - 2 } { 2 } } { \frac { \delta \alpha _ { i } } { \beta _ { i } + 1 / ( 4 ( | \delta - m | - 1 ) ) } } | \kappa_{m}={\frac{g-2}{2}}{\frac{\delta \alpha_{i}}{\beta_{i}+1/(4(| \delta-m |-1))}}
K _ { M N } = h _ { M } ^ { P } h _ { N } ^ { Q } \bigtriangledown _ { _ P } n _ { Q } \quad , | K_{M N}=h_{M}^{P} h_{N}^{Q} \bigtriangledown_{_P} n_{Q} \quad,
I ( z ) = \mathrm { T r } \left( { \frac { z } { z + D _ { F } ^ { \dagger } D _ { F } } } - { \frac { z } { z + D _ { F } D _ { F } ^ { \dagger } } } \right) = \int _ { 0 } ^ { \infty } d \lambda { \frac { z } { z + \lambda } } \left( { \frac { d n _ { + } ( \lambda ) } { d \lambda } } - { \frac { d n _ { - } ( \lambda ) } { d \lambda } } \right) . | I(z)=\mathrm{Tr} \left({\frac{z}{z+D_{F}^{\dagger} D_{F}}}-{\frac{z}{z+D_{F} D_{F}^{\dagger}}} \right)=\int_{0}^{\infty} d \lambda{\frac{z}{z+\lambda}} \left({\frac{d n_{+}(\lambda)}{d \lambda}}-{\frac{d n_{-}(\lambda)}{d \lambda}} \right).
{ \cal O } [ A B C ] = { \cal O } [ A ] + { \cal O } [ B ] + { \cal O } [ C ] , | {\cal O}[A B C]={\cal O}[A]+{\cal O}[B]+{\cal O}[C],
\delta _ { Q + S } \hat { s } = 0 \ \Rightarrow \ \left[ 4 i s ( \epsilon _ { 4 } ^ { - \alpha } + x _ { 4 } ^ { \alpha \dot { \beta } } \bar { \eta } _ { 4 \dot { \beta } } ^ { - } ) \left( { \frac { x _ { 1 4 } } { x _ { 1 4 } ^ { 2 } } } - { \frac { x _ { 3 4 } } { x _ { 3 4 } ^ { 2 } } } \right) _ { \alpha \dot { \alpha } } + \sum _ { a = 1 } ^ { 4 } ( \epsilon _ { a } ^ { + \alpha } + x _ { a } ^ { \alpha \dot { \beta } } \bar { \eta } _ { a \dot { \beta } } ^ { + } ) S _ { a 4 \; \alpha \dot { \alpha } } \right] \bar { \theta } _ { 4 } ^ { + \dot { \alpha } } = 0 \; . | \delta_{Q+S} \hat{s}=0 \ \Rightarrow \ \left[4 i s(\epsilon_{4}^{-\alpha}+x_{4}^{\alpha \dot{\beta}} \bar{\eta}_{4 \dot{\beta}}^{-}) \left({\frac{x_{1 4}}{x_{1 4}^{2}}}-{\frac{x_{3 4}}{x_{3 4}^{2}}} \right)_{\alpha \dot{\alpha}}+\sum_{a=1}^{4}(\epsilon_{a}^{+\alpha}+x_{a}^{\alpha \dot{\beta}} \bar{\eta}_{a \dot{\beta}}^{+}) S_{a 4 \;\alpha \dot{\alpha}} \right] \bar{\theta}_{4}^{+\dot{\alpha}}=0 \;.
L = \frac { 1 } { 2 } \phi ^ { * } ( P ^ { \mu } - e A ^ { \mu } ) \phi ( P _ { \mu } - e A _ { \mu } ) - \frac { 1 } { 2 } m _ { 0 } ^ { 2 } | \phi | ^ { 2 } | L=\frac{1}{2} \phi^{*}(P^{\mu}-e A^{\mu}) \phi(P_{\mu}-e A_{\mu})-\frac{1}{2} m_{0}^{2} | \phi |^{2}
\Gamma ^ { a } = \dot { \lambda } ^ { a } + \chi ^ { a } ( q ^ { i } , p _ { i } , \lambda ^ { a } ) , | \Gamma^{a}=\dot{\lambda}^{a}+\chi^{a}(q^{i},p_{i},\lambda^{a}),
\partial _ { \mp } { ^ { ( \pm ) } \xi } ^ { i } = 0 | \partial_{\mp}{^{(\pm)} \xi}^{i}=0
b _ { 1 } = 1 , \; \; \; \; \; b _ { 2 } = 0 , \; \; \; \; \; b _ { 3 } = 0 , | b_{1}=1,\;\;\;\;\;b_{2}=0,\;\;\;\;\;b_{3}=0,
\Gamma ( a ) \lhd X = a \lhd \tilde { \Gamma } ( X ) \, . | \Gamma(a) \lhd X=a \lhd \tilde{\Gamma}(X) \,.
\left( - \nabla ^ { 2 } + U ^ { \prime \prime } ( \varphi ) \right) \psi _ { n } = \lambda _ { n } \psi _ { n } \; , | \left(-\nabla^{2}+U^{\prime \prime}(\varphi) \right) \psi_{n}=\lambda_{n} \psi_{n} \;,
{ \frac { 1 } { 2 } } \left( a ^ { \dagger } - 2 g q ^ { 2 } \right) a \Phi _ { N + 1 } ^ { ( - ) } = E _ { N + 1 } ^ { ( - ) } \Phi _ { N + 1 } ^ { ( - ) } . | {\frac{1}{2}} \left(a^{\dagger}-2 g q^{2} \right) a \Phi_{N+1}^{(-)}=E_{N+1}^{(-)} \Phi_{N+1}^{(-)}.
\partial _ { 0 } P _ { \mu } : = \partial _ { 0 } \int d ^ { 3 } x \, T _ { 0 \mu } = 0 . | \partial_{0} P_{\mu}:=\partial_{0} \int d^{3} x \,T_{0 \mu}=0.
a = \sum _ { i , j } ( 1 + \alpha _ { i j } ) p _ { i } d _ { f } p _ { j } . | a=\sum_{i,j}(1+\alpha_{i j}) p_{i} d_{f} p_{j}.
\langle \lambda \lambda \rangle \propto e ^ { 2 \pi i k / T _ { G } } \, , \qquad k = 0 , 1 , . . . , T _ { G } - 1 \, , | \langle \lambda \lambda \rangle \propto e^{2 \pi i k/T_{G}} \,,\qquad k=0,1,...,T_{G}-1 \,,
\alpha ( t ) = \alpha _ { 0 } e ^ { - i \omega ( t - t _ { 0 } ) } \ \ \ , \ \ \ \alpha ^ { * } ( t ) = \alpha _ { 0 } ^ { * } e ^ { i \omega ( t - t _ { 0 } ) } \ , | \alpha(t)=\alpha_{0} e^{-i \omega(t-t_{0})} \ \ \,\ \ \ \alpha^{*}(t)=\alpha_{0}^{*} e^{i \omega(t-t_{0})} \,
J _ { 0 } = \frac { c } { 1 2 } ( A ^ { 3 } + 2 n + 1 ) , \qquad L _ { 0 } = ( 1 - \gamma ^ { 2 } ) \frac { c } { 2 4 } + \frac { c } { 2 4 } ( A ^ { 3 } + 2 n + 1 ) ^ { 2 } . | J_{0}=\frac{c}{1 2}(A^{3}+2 n+1),\qquad L_{0}=(1-\gamma^{2}) \frac{c}{2 4}+\frac{c}{2 4}(A^{3}+2 n+1)^{2}.
{ \delta _ { \rho } } { \hat { G } _ { D \varepsilon } } ( \xi , \xi ) = { \frac { \rho ( \xi ) } { 4 \pi } } , \quad \xi \not \in { B } . | {\delta_{\rho}}{\hat{G}_{D \varepsilon}}(\xi,\xi)={\frac{\rho(\xi)}{4 \pi}},\quad \xi \not \in{B}.
A _ { i } ( x ) \rightarrow \lambda A _ { i } ( \lambda x ) , | A_{i}(x) \rightarrow \lambda A_{i}(\lambda x),
\omega _ { \Lambda } = 2 - 2 n ( L - 1 ) - E _ { \phi } ( n + 1 ) . \qquad | \omega_{\Lambda}=2-2 n(L-1)-E_{\phi}(n+1).\qquad
\left\{ \begin{cases} { \delta ^ { ( 2 ) } x _ { \mu } ^ { i } } & { = 0 } \\ { \delta ^ { ( 2 ) } \xi ^ { i } } & { = \epsilon _ { 2 } } \\ \end{cases} \right. . | \left\{\left\{\begin{1.2}{{\delta^{(2)} x_{\mu}^{i}}} &{{=0}} \\{{\delta^{(2)} \xi^{i}}} &{{=\epsilon_{2}}} \\ \end{1.2} \right.\right..
\begin{array} { c c } { V ( x ) = i \, Z } & { \ \ \ \ \mathrm { R e } \ x < 0 } \\ { V ( x ) = - i \, Z } & { \ \ \ \ \mathrm { R e } \ x > 0 . } \\ \end{array} | \begin{array}{cc}{{V(x)=i \,Z}} &{{\ \ \ \ \mathrm{Re} \ x<0}} \\{{V(x)=-i \,Z}} &{{\ \ \ \ \mathrm{Re} \ x>0.}} \end{array}
P = \frac { 2 ^ { 8 } } { 3 ^ { 4 } 5 ^ { 2 } 7 ^ { 2 } 1 1 } \frac { 2 \kappa ^ { 2 } N ^ { 2 } T _ { 0 } ^ { 2 } } { \Omega _ { 8 } } \left( \omega _ { 1 } ^ { 1 2 } R _ { 1 } ^ { 4 } + \omega _ { 2 } ^ { 1 2 } R _ { 2 } ^ { 4 } + \omega _ { 3 } ^ { 1 2 } R _ { 3 } ^ { 4 } \right) | P=\frac{2^{8}}{3^{4} 5^{2} 7^{2} 1 1} \frac{2 \kappa^{2} N^{2} T_{0}^{2}}{\Omega_{8}} \left(\omega_{1}^{1 2} R_{1}^{4}+\omega_{2}^{1 2} R_{2}^{4}+\omega_{3}^{1 2} R_{3}^{4} \right)
