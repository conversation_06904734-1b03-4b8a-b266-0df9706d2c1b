#!/usr/bin/env python3
"""
LaTeX Token Length and Complexity Analyzer
分析LaTeX代码的token长度和复杂程度
"""

import re
import json
import csv
import argparse
import statistics
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Any
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

class LaTeXAnalyzer:
    """LaTeX代码分析器"""
    
    def __init__(self):
        # 定义不同复杂度的LaTeX命令及其权重
        self.command_weights = {
            # 简单命令 (权重1)
            'simple': {
                'sin', 'cos', 'tan', 'log', 'ln', 'exp', 'alpha', 'beta', 'gamma', 'delta',
                'epsilon', 'theta', 'lambda', 'mu', 'nu', 'pi', 'rho', 'sigma', 'tau', 'phi',
                'chi', 'psi', 'omega', 'hat', 'tilde', 'bar', 'dot', 'ddot', 'vec', 'mathbf',
                'mathrm', 'mathit', 'mathcal', 'left', 'right', 'big', 'Big', 'bigg', 'Bigg'
            },
            # 中等命令 (权重2)
            'medium': {
                'frac', 'sqrt', 'binom', 'choose', 'over', 'atop', 'above', 'operatorname',
                'text', 'mbox', 'hbox', 'vbox', 'phantom', 'vphantom', 'hphantom'
            },
            # 复杂命令 (权重3)
            'complex': {
                'int', 'sum', 'prod', 'lim', 'sup', 'inf', 'max', 'min', 'arg', 'det',
                'gcd', 'lcm', 'mod', 'bmod', 'pmod', 'pod', 'oint', 'iint', 'iiint'
            },
            # 环境命令 (权重4)
            'environment': {
                'begin', 'end', 'matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix',
                'smallmatrix', 'array', 'align', 'aligned', 'gather', 'gathered',
                'split', 'multline', 'cases', 'equation', 'eqnarray'
            }
        }
        
        # 括号类型
        self.brackets = {
            '(': ')', '[': ']', '{': '}', 
            '\\{': '\\}', '\\[': '\\]', '\\(': '\\)'
        }
    
    def tokenize_latex(self, formula: str) -> Dict[str, Any]:
        """
        对LaTeX公式进行token化分析
        """
        # 清理公式
        formula = formula.strip()
        if not formula:
            return self._empty_result()
        
        # 基础统计
        char_count = len(formula)
        space_tokens = len(formula.split())
        
        # 提取LaTeX命令
        commands = re.findall(r'\\[a-zA-Z]+', formula)
        command_count = len(commands)
        
        # 提取数学符号和变量
        # 移除LaTeX命令后的剩余内容
        no_commands = re.sub(r'\\[a-zA-Z]+', '', formula)
        # 提取字母、数字、符号
        symbols = re.findall(r'[a-zA-Z0-9]', no_commands)
        symbol_count = len(symbols)
        
        # 计算嵌套深度
        nesting_depth = self._calculate_nesting_depth(formula)
        
        # 计算命令复杂度
        command_complexity = self._calculate_command_complexity(commands)
        
        # 计算环境复杂度
        environment_complexity = self._calculate_environment_complexity(formula)
        
        # 综合复杂度评分
        complexity_score = (
            space_tokens * 0.5 +
            command_count * 1.0 +
            nesting_depth * 2.0 +
            command_complexity +
            environment_complexity
        )
        
        return {
            'formula': formula,
            'char_count': char_count,
            'space_tokens': space_tokens,
            'command_count': command_count,
            'symbol_count': symbol_count,
            'nesting_depth': nesting_depth,
            'command_complexity': command_complexity,
            'environment_complexity': environment_complexity,
            'complexity_score': complexity_score,
            'commands': commands
        }
    
    def _empty_result(self) -> Dict[str, Any]:
        """返回空公式的分析结果"""
        return {
            'formula': '',
            'char_count': 0,
            'space_tokens': 0,
            'command_count': 0,
            'symbol_count': 0,
            'nesting_depth': 0,
            'command_complexity': 0,
            'environment_complexity': 0,
            'complexity_score': 0,
            'commands': []
        }
    
    def _calculate_nesting_depth(self, formula: str) -> int:
        """计算嵌套深度"""
        max_depth = 0
        current_depth = 0
        
        i = 0
        while i < len(formula):
            char = formula[i]
            
            # 处理转义字符
            if char == '\\' and i + 1 < len(formula):
                next_char = formula[i + 1]
                if next_char in '{[]()':
                    i += 2
                    continue
            
            # 计算嵌套
            if char in '({[':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char in ')}]':
                current_depth = max(0, current_depth - 1)
            
            i += 1
        
        return max_depth
    
    def _calculate_command_complexity(self, commands: List[str]) -> float:
        """计算命令复杂度"""
        complexity = 0
        
        for cmd in commands:
            cmd_name = cmd[1:]  # 移除反斜杠
            
            if cmd_name in self.command_weights['simple']:
                complexity += 1
            elif cmd_name in self.command_weights['medium']:
                complexity += 2
            elif cmd_name in self.command_weights['complex']:
                complexity += 3
            elif cmd_name in self.command_weights['environment']:
                complexity += 4
            else:
                complexity += 0.5  # 未知命令
        
        return complexity
    
    def _calculate_environment_complexity(self, formula: str) -> float:
        """计算环境复杂度"""
        # 查找begin/end环境
        begin_matches = re.findall(r'\\begin\{([^}]+)\}', formula)
        end_matches = re.findall(r'\\end\{([^}]+)\}', formula)
        
        # 环境复杂度基于环境数量和类型
        complexity = 0
        for env in begin_matches:
            if env in ['matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix']:
                complexity += 3
            elif env in ['align', 'aligned', 'gather', 'gathered']:
                complexity += 2
            elif env in ['array', 'cases']:
                complexity += 2.5
            else:
                complexity += 1
        
        return complexity

    def analyze_file(self, filepath: str) -> List[Dict[str, Any]]:
        """分析LaTeX文件"""
        results = []

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            print(f"正在分析文件: {filepath}")
            print(f"总行数: {len(lines)}")

            for i, line in enumerate(lines, 1):
                if i % 1000 == 0:
                    print(f"已处理: {i}/{len(lines)} 行")

                result = self.tokenize_latex(line)
                result['line_number'] = i
                results.append(result)

            print("文件分析完成!")
            return results

        except FileNotFoundError:
            print(f"错误: 找不到文件 {filepath}")
            return []
        except Exception as e:
            print(f"错误: 分析文件时出现异常 {e}")
            return []

    def generate_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成统计信息"""
        if not results:
            return {}

        # 过滤掉空公式
        non_empty_results = [r for r in results if r['char_count'] > 0]

        if not non_empty_results:
            return {'error': '没有有效的LaTeX公式'}

        # 提取各项指标
        char_counts = [r['char_count'] for r in non_empty_results]
        space_tokens = [r['space_tokens'] for r in non_empty_results]
        command_counts = [r['command_count'] for r in non_empty_results]
        symbol_counts = [r['symbol_count'] for r in non_empty_results]
        nesting_depths = [r['nesting_depth'] for r in non_empty_results]
        complexity_scores = [r['complexity_score'] for r in non_empty_results]

        # 计算统计量
        stats = {
            'total_formulas': len(results),
            'valid_formulas': len(non_empty_results),
            'empty_formulas': len(results) - len(non_empty_results),

            'char_count': {
                'mean': statistics.mean(char_counts),
                'median': statistics.median(char_counts),
                'std': statistics.stdev(char_counts) if len(char_counts) > 1 else 0,
                'min': min(char_counts),
                'max': max(char_counts)
            },

            'space_tokens': {
                'mean': statistics.mean(space_tokens),
                'median': statistics.median(space_tokens),
                'std': statistics.stdev(space_tokens) if len(space_tokens) > 1 else 0,
                'min': min(space_tokens),
                'max': max(space_tokens)
            },

            'command_count': {
                'mean': statistics.mean(command_counts),
                'median': statistics.median(command_counts),
                'std': statistics.stdev(command_counts) if len(command_counts) > 1 else 0,
                'min': min(command_counts),
                'max': max(command_counts)
            },

            'complexity_score': {
                'mean': statistics.mean(complexity_scores),
                'median': statistics.median(complexity_scores),
                'std': statistics.stdev(complexity_scores) if len(complexity_scores) > 1 else 0,
                'min': min(complexity_scores),
                'max': max(complexity_scores)
            },

            'nesting_depth': {
                'mean': statistics.mean(nesting_depths),
                'median': statistics.median(nesting_depths),
                'std': statistics.stdev(nesting_depths) if len(nesting_depths) > 1 else 0,
                'min': min(nesting_depths),
                'max': max(nesting_depths)
            }
        }

        # 复杂度分级
        complexity_levels = self._categorize_complexity(complexity_scores)
        stats['complexity_distribution'] = complexity_levels

        # 最复杂和最简单的公式
        sorted_results = sorted(non_empty_results, key=lambda x: x['complexity_score'])
        stats['simplest_formulas'] = sorted_results[:5]
        stats['most_complex_formulas'] = sorted_results[-5:]

        return stats

    def _categorize_complexity(self, complexity_scores: List[float]) -> Dict[str, int]:
        """将复杂度分级"""
        if not complexity_scores:
            return {}

        # 定义复杂度阈值
        q25 = np.percentile(complexity_scores, 25)
        q50 = np.percentile(complexity_scores, 50)
        q75 = np.percentile(complexity_scores, 75)

        categories = {'简单': 0, '中等': 0, '复杂': 0, '极复杂': 0}

        for score in complexity_scores:
            if score <= q25:
                categories['简单'] += 1
            elif score <= q50:
                categories['中等'] += 1
            elif score <= q75:
                categories['复杂'] += 1
            else:
                categories['极复杂'] += 1

        return categories

    def save_detailed_results(self, results: List[Dict[str, Any]], filename: str = 'latex_analysis_detailed.csv'):
        """保存详细分析结果到CSV文件"""
        if not results:
            print("没有结果可保存")
            return

        # 过滤掉空公式
        non_empty_results = [r for r in results if r['char_count'] > 0]

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'line_number', 'char_count', 'space_tokens', 'command_count',
                'symbol_count', 'nesting_depth', 'command_complexity',
                'environment_complexity', 'complexity_score', 'formula'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in non_empty_results:
                # 截断过长的公式以便CSV显示
                formula_display = result['formula'][:100] + '...' if len(result['formula']) > 100 else result['formula']

                writer.writerow({
                    'line_number': result['line_number'],
                    'char_count': result['char_count'],
                    'space_tokens': result['space_tokens'],
                    'command_count': result['command_count'],
                    'symbol_count': result['symbol_count'],
                    'nesting_depth': result['nesting_depth'],
                    'command_complexity': result['command_complexity'],
                    'environment_complexity': result['environment_complexity'],
                    'complexity_score': result['complexity_score'],
                    'formula': formula_display
                })

        print(f"详细结果已保存到: {filename}")

    def generate_visualizations(self, results: List[Dict[str, Any]], stats: Dict[str, Any], output_filename: str = 'latex_complexity_analysis.png', title_suffix: str = ''):
        """生成可视化图表"""
        if not results:
            print("没有数据可视化")
            return

        # 过滤掉空公式
        non_empty_results = [r for r in results if r['char_count'] > 0]

        if not non_empty_results:
            print("没有有效数据可视化")
            return

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        main_title = f'LaTeX公式复杂度分析报告{title_suffix}'
        fig.suptitle(main_title, fontsize=16, fontweight='bold')

        # 1. Token长度分布
        space_tokens = [r['space_tokens'] for r in non_empty_results]
        axes[0, 0].hist(space_tokens, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('Token长度分布')
        axes[0, 0].set_xlabel('Token数量')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 复杂度分布
        complexity_scores = [r['complexity_score'] for r in non_empty_results]
        axes[0, 1].hist(complexity_scores, bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0, 1].set_title('复杂度分布')
        axes[0, 1].set_xlabel('复杂度分数')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 复杂度等级饼图
        if 'complexity_distribution' in stats:
            labels = list(stats['complexity_distribution'].keys())
            sizes = list(stats['complexity_distribution'].values())
            colors = ['lightgreen', 'gold', 'orange', 'red']
            axes[1, 0].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            axes[1, 0].set_title('复杂度等级分布')

        # 4. Token长度vs复杂度散点图
        char_counts = [r['char_count'] for r in non_empty_results]
        axes[1, 1].scatter(char_counts, complexity_scores, alpha=0.6, color='purple')
        axes[1, 1].set_title('字符数 vs 复杂度')
        axes[1, 1].set_xlabel('字符数')
        axes[1, 1].set_ylabel('复杂度分数')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(output_filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"可视化图表已保存为: {output_filename}")

    def generate_report(self, stats: Dict[str, Any], filename: str = 'latex_analysis_report.txt'):
        """生成文本报告"""
        if not stats or 'error' in stats:
            print("无法生成报告：没有有效数据")
            return

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("LaTeX公式Token长度和复杂度分析报告\n")
            f.write("=" * 60 + "\n\n")

            # 基本统计
            f.write("1. 基本统计信息\n")
            f.write("-" * 30 + "\n")
            f.write(f"总公式数量: {stats['total_formulas']}\n")
            f.write(f"有效公式数量: {stats['valid_formulas']}\n")
            f.write(f"空公式数量: {stats['empty_formulas']}\n\n")

            # Token长度统计
            f.write("2. Token长度分析\n")
            f.write("-" * 30 + "\n")
            char_stats = stats['char_count']
            f.write(f"字符数统计:\n")
            f.write(f"  平均值: {char_stats['mean']:.2f}\n")
            f.write(f"  中位数: {char_stats['median']:.2f}\n")
            f.write(f"  标准差: {char_stats['std']:.2f}\n")
            f.write(f"  最小值: {char_stats['min']}\n")
            f.write(f"  最大值: {char_stats['max']}\n\n")

            token_stats = stats['space_tokens']
            f.write(f"空格分割Token统计:\n")
            f.write(f"  平均值: {token_stats['mean']:.2f}\n")
            f.write(f"  中位数: {token_stats['median']:.2f}\n")
            f.write(f"  标准差: {token_stats['std']:.2f}\n")
            f.write(f"  最小值: {token_stats['min']}\n")
            f.write(f"  最大值: {token_stats['max']}\n\n")

            # 复杂度统计
            f.write("3. 复杂度分析\n")
            f.write("-" * 30 + "\n")
            complexity_stats = stats['complexity_score']
            f.write(f"复杂度分数统计:\n")
            f.write(f"  平均值: {complexity_stats['mean']:.2f}\n")
            f.write(f"  中位数: {complexity_stats['median']:.2f}\n")
            f.write(f"  标准差: {complexity_stats['std']:.2f}\n")
            f.write(f"  最小值: {complexity_stats['min']:.2f}\n")
            f.write(f"  最大值: {complexity_stats['max']:.2f}\n\n")

            # 复杂度分布
            if 'complexity_distribution' in stats:
                f.write("4. 复杂度等级分布\n")
                f.write("-" * 30 + "\n")
                total = sum(stats['complexity_distribution'].values())
                for level, count in stats['complexity_distribution'].items():
                    percentage = (count / total) * 100
                    f.write(f"{level}: {count} ({percentage:.1f}%)\n")
                f.write("\n")

            # 最简单的公式
            f.write("5. 最简单的公式示例\n")
            f.write("-" * 30 + "\n")
            for i, formula in enumerate(stats['simplest_formulas'], 1):
                f.write(f"{i}. 复杂度: {formula['complexity_score']:.2f}\n")
                f.write(f"   公式: {formula['formula'][:100]}{'...' if len(formula['formula']) > 100 else ''}\n\n")

            # 最复杂的公式
            f.write("6. 最复杂的公式示例\n")
            f.write("-" * 30 + "\n")
            for i, formula in enumerate(stats['most_complex_formulas'], 1):
                f.write(f"{i}. 复杂度: {formula['complexity_score']:.2f}\n")
                f.write(f"   公式: {formula['formula'][:100]}{'...' if len(formula['formula']) > 100 else ''}\n\n")

            f.write("=" * 60 + "\n")
            f.write("报告生成完成\n")
            f.write("=" * 60 + "\n")

        print(f"分析报告已保存到: {filename}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='LaTeX公式Token长度和复杂度分析器')
    parser.add_argument('input_file', help='输入的LaTeX文件路径')
    parser.add_argument('--output-prefix', default='latex_analysis', help='输出文件前缀')
    parser.add_argument('--no-viz', action='store_true', help='不生成可视化图表')

    args = parser.parse_args()

    # 创建分析器
    analyzer = LaTeXAnalyzer()

    # 分析文件
    print("开始分析LaTeX文件...")
    results = analyzer.analyze_file(args.input_file)

    if not results:
        print("分析失败，请检查输入文件")
        return

    # 生成统计信息
    print("生成统计信息...")
    stats = analyzer.generate_statistics(results)

    if 'error' in stats:
        print(f"统计生成失败: {stats['error']}")
        return

    # 保存详细结果
    print("保存详细结果...")
    analyzer.save_detailed_results(results, f"{args.output_prefix}_detailed.csv")

    # 生成报告
    print("生成分析报告...")
    analyzer.generate_report(stats, f"{args.output_prefix}_report.txt")

    # 生成可视化
    if not args.no_viz:
        print("生成可视化图表...")
        try:
            # 根据输入文件名生成图表文件名
            import os
            base_name = os.path.splitext(os.path.basename(args.input_file))[0]
            chart_filename = f"{args.output_prefix}_{base_name}_complexity_analysis.png"
            title_suffix = f" - {base_name}"
            analyzer.generate_visualizations(results, stats, chart_filename, title_suffix)
        except Exception as e:
            print(f"可视化生成失败: {e}")
            print("可能需要安装matplotlib和seaborn: pip install matplotlib seaborn")

    # 打印简要统计
    print("\n" + "=" * 50)
    print("分析完成！简要统计:")
    print("=" * 50)
    print(f"总公式数量: {stats['total_formulas']}")
    print(f"有效公式数量: {stats['valid_formulas']}")
    print(f"平均Token长度: {stats['space_tokens']['mean']:.2f}")
    print(f"平均复杂度: {stats['complexity_score']['mean']:.2f}")
    print("=" * 50)


if __name__ == "__main__":
    main()
