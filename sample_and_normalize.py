#!/usr/bin/env python3
"""
从labels.json中随机抽样1000个样本，使用规范化脚本处理，并输出对比结果
"""

import json
import random
import sys
from pathlib import Path

# 导入规范化脚本
from normalize_optimized import normalize_latex_string

def load_labels_json(file_path):
    """加载labels.json文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载labels.json文件失败: {e}")
        return None

def random_sample(data, sample_size=1000):
    """从数据中随机抽样"""
    if not data:
        return []

    # 获取所有的值（LaTeX公式）
    formulas = list(data.values())
    total_count = len(formulas)

    if total_count <= sample_size:
        print(f"数据总量({total_count})小于等于抽样数量({sample_size})，返回全部数据")
        return formulas

    # 随机抽样
    indices = random.sample(range(total_count), sample_size)

    # 按索引获取样本
    samples = [formulas[i] for i in indices]
    return samples

def process_samples(samples):
    """处理样本，返回原始和规范化后的结果对"""
    results = []
    total = len(samples)

    print(f"开始处理{total}个样本...")

    for i, original_formula in enumerate(samples):
        if i % 100 == 0:
            print(f"处理进度: {i}/{total}")

        try:
            # 使用规范化脚本处理
            normalized_formula = normalize_latex_string(original_formula)
            results.append((original_formula, normalized_formula))
        except Exception as e:
            print(f"处理第{i+1}个样本时出错: {e}")
            # 出错时保持原样
            results.append((original_formula, original_formula))

    # 对结果进行乱序排列
    random.shuffle(results)

    print(f"处理完成，共处理{len(results)}个样本")
    return results

def save_results(results, output_file="normalized_comparison.txt"):
    """保存结果到txt文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, (original, normalized) in enumerate(results, 1):
                # 每行格式：处理前公式 | 处理后公式
                f.write(f"{original} | {normalized}\n")
        
        print(f"结果已保存到: {output_file}")
        print(f"共保存{len(results)}行数据")
        
    except Exception as e:
        print(f"保存结果失败: {e}")

def main():
    """主函数"""
    
    # 文件路径
    labels_file = "labels.json"
    output_file = "normalized_comparison.txt"
    
    # 检查文件是否存在
    if not Path(labels_file).exists():
        print(f"错误: 找不到文件 {labels_file}")
        return
    
    print("=== LaTeX公式规范化对比工具 ===")
    print(f"输入文件: {labels_file}")
    print(f"输出文件: {output_file}")
    print(f"抽样数量: 100")
    print()
    
    # 1. 加载数据
    print("1. 加载labels.json文件...")
    data = load_labels_json(labels_file)
    if not data:
        return
    
    print(f"   加载完成，共{len(data)}条数据")
    
    # 2. 随机抽样
    print("2. 进行随机抽样...")
    samples = random_sample(data, 100)
    print(f"   抽样完成，获得{len(samples)}个样本")
    
    # 3. 处理样本
    print("3. 使用规范化脚本处理样本...")
    results = process_samples(samples)
    
    # 4. 保存结果
    print("4. 保存结果...")
    save_results(results, output_file)
    
    print("\n=== 处理完成 ===")
    
    # 显示一些统计信息
    if results:
        changed_count = sum(1 for orig, norm in results if orig != norm)
        print(f"统计信息:")
        print(f"  总样本数: {len(results)}")
        print(f"  发生变化的样本数: {changed_count}")
        print(f"  变化率: {changed_count/len(results)*100:.1f}%")

if __name__ == "__main__":
    main()
