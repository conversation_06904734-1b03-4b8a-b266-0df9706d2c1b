"""
结构规范化器

实现FR2需求：结构规范化
- 对齐环境统一：将功能类似的LaTeX环境统一为单一标准形式
- 矩阵环境统一：仅将smallmatrix环境统一为matrix环境
"""

import re
from typing import Dict, List, Tuple

from ..utils.helpers import normalize_environment_name


class StructureNormalizer:
    """
    结构规范化器
    
    将功能或渲染效果类似的LaTeX环境统一为单一的标准形式。
    """
    
    def __init__(self):
        """初始化结构规范化器"""
        # 对齐环境映射：统一为aligned
        self.alignment_environments = {
            'split', 'align', 'alignedat', 'alignat', 'eqnarray'
        }
        
        # 矩阵环境映射：仅smallmatrix统一为matrix
        self.matrix_environments = {
            'smallmatrix': 'matrix'
        }
        
        # 需要保持不变的矩阵环境
        self.preserved_matrix_environments = {
            'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix', 'matrix'
        }
    
    def normalize(self, latex_code: str) -> str:
        """
        执行结构规范化
        
        Args:
            latex_code: 输入的LaTeX代码
            
        Returns:
            规范化后的LaTeX代码
        """
        if not latex_code:
            return latex_code
        
        result = latex_code
        
        # 第一阶段：对齐环境统一
        result = self._normalize_alignment_environments(result)
        
        # 第二阶段：矩阵环境统一
        result = self._normalize_matrix_environments(result)
        
        return result
    
    def _normalize_alignment_environments(self, text: str) -> str:
        """
        统一对齐环境
        
        将split, align, alignedat, alignat, eqnarray及其带星号版本
        统一为aligned环境
        
        Args:
            text: 输入文本
            
        Returns:
            处理后的文本
        """
        result = text
        
        # 处理每种对齐环境
        for env in self.alignment_environments:
            # 处理不带星号的版本
            pattern = f'\\\\begin{{{env}}}(.*?)\\\\end{{{env}}}'
            replacement = r'\\begin{aligned}\1\\end{aligned}'
            result = re.sub(pattern, replacement, result, flags=re.DOTALL)
            
            # 处理带星号的版本
            pattern_star = f'\\\\begin{{{env}\\*}}(.*?)\\\\end{{{env}\\*}}'
            replacement_star = r'\\begin{aligned}\1\\end{aligned}'
            result = re.sub(pattern_star, replacement_star, result, flags=re.DOTALL)
        
        return result
    
    def _normalize_matrix_environments(self, text: str) -> str:
        """
        统一矩阵环境
        
        仅将smallmatrix环境统一为matrix环境
        其他矩阵环境保持原样
        
        Args:
            text: 输入文本
            
        Returns:
            处理后的文本
        """
        result = text
        
        # 只处理smallmatrix环境
        for source_env, target_env in self.matrix_environments.items():
            # 处理不带星号的版本
            pattern = f'\\\\begin{{{source_env}}}(.*?)\\\\end{{{source_env}}}'
            replacement = f'\\\\begin{{{target_env}}}\\1\\\\end{{{target_env}}}'
            result = re.sub(pattern, replacement, result, flags=re.DOTALL)
            
            # 处理带星号的版本
            pattern_star = f'\\\\begin{{{source_env}\\*}}(.*?)\\\\end{{{source_env}\\*}}'
            replacement_star = f'\\\\begin{{{target_env}}}\\1\\\\end{{{target_env}}}'
            result = re.sub(pattern_star, replacement_star, result, flags=re.DOTALL)
        
        return result
    
    def extract_environments(self, text: str) -> List[Tuple[str, str, str]]:
        """
        提取文本中的所有环境
        
        Args:
            text: 输入文本
            
        Returns:
            环境列表，每个元素为(环境名, 环境内容, 完整环境文本)
        """
        environments = []
        
        # 匹配所有环境
        pattern = r'\\begin\{([^}]+)\}(.*?)\\end\{\1\}'
        matches = re.finditer(pattern, text, re.DOTALL)
        
        for match in matches:
            env_name = match.group(1)
            env_content = match.group(2)
            full_env = match.group(0)
            environments.append((env_name, env_content, full_env))
        
        return environments
    
    def validate_environment_structure(self, text: str) -> Tuple[bool, List[str]]:
        """
        验证环境结构的正确性
        
        Args:
            text: 要验证的文本
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查环境匹配
        begin_pattern = r'\\begin\{([^}]+)\}'
        end_pattern = r'\\end\{([^}]+)\}'
        
        begins = re.findall(begin_pattern, text)
        ends = re.findall(end_pattern, text)
        
        # 统计每个环境的开始和结束次数
        begin_count = {}
        for env in begins:
            begin_count[env] = begin_count.get(env, 0) + 1
        
        end_count = {}
        for env in ends:
            end_count[env] = end_count.get(env, 0) + 1
        
        # 检查匹配
        all_envs = set(begin_count.keys()) | set(end_count.keys())
        for env in all_envs:
            begin_num = begin_count.get(env, 0)
            end_num = end_count.get(env, 0)
            if begin_num != end_num:
                errors.append(f"环境{env}不匹配：{begin_num}个begin，{end_num}个end")
        
        return len(errors) == 0, errors
    
    def get_environment_statistics(self, text: str) -> Dict[str, any]:
        """
        获取环境统计信息
        
        Args:
            text: 要分析的文本
            
        Returns:
            统计信息字典
        """
        environments = self.extract_environments(text)
        
        stats = {
            'total_environments': len(environments),
            'environment_types': {},
            'alignment_environments': 0,
            'matrix_environments': 0,
            'other_environments': 0
        }
        
        for env_name, _, _ in environments:
            # 移除星号后缀进行分类
            base_env = env_name.rstrip('*')
            
            # 统计环境类型
            if env_name in stats['environment_types']:
                stats['environment_types'][env_name] += 1
            else:
                stats['environment_types'][env_name] = 1
            
            # 分类统计
            if base_env in self.alignment_environments:
                stats['alignment_environments'] += 1
            elif base_env in self.matrix_environments or base_env in self.preserved_matrix_environments:
                stats['matrix_environments'] += 1
            else:
                stats['other_environments'] += 1
        
        return stats
    
    def preview_normalization(self, text: str) -> Dict[str, any]:
        """
        预览规范化效果
        
        Args:
            text: 输入文本
            
        Returns:
            预览信息字典
        """
        original_envs = self.extract_environments(text)
        normalized_text = self.normalize(text)
        normalized_envs = self.extract_environments(normalized_text)
        
        changes = []
        
        # 比较环境变化
        for i, (orig_env, _, _) in enumerate(original_envs):
            if i < len(normalized_envs):
                norm_env, _, _ = normalized_envs[i]
                if orig_env != norm_env:
                    changes.append({
                        'original': orig_env,
                        'normalized': norm_env,
                        'type': 'environment_change'
                    })
        
        return {
            'original_environments': len(original_envs),
            'normalized_environments': len(normalized_envs),
            'changes': changes,
            'original_text': text,
            'normalized_text': normalized_text
        }
