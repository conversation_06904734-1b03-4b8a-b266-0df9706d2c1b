# 导入正则表达式模块，用于模式匹配和文本替换
import re

# 从utils.latex模块导入change_all函数，这是一个强大的LaTeX命令替换工具
from ..utils.latex import change_all
# 从format模块导入format_latex函数，用于最终的LaTeX格式化
from .format import format_latex


def _rm_dollar_surr(content):
    """
    移除LaTeX公式中的美元符号包围

    这个函数用于处理混合在LaTeX中的美元符号数学模式，
    将 $...$ 形式的内联数学模式转换为普通文本

    Args:
        content: 包含美元符号的LaTeX字符串

    Returns:
        移除美元符号后的LaTeX字符串
    """
    # 编译正则表达式模式：
    # \\[a-zA-Z]+\$.*?\$ - 匹配LaTeX命令后跟美元符号的模式
    # \$.*?\$ - 匹配普通的美元符号包围的内容
    pattern = re.compile(r"\\[a-zA-Z]+\$.*?\$|\$.*?\$")
    # 查找所有匹配的模式
    matches = pattern.findall(content)

    # 遍历所有匹配项
    for match in matches:
        # 检查匹配项是否不是以LaTeX命令开头（即不是 \command$...$）
        if not re.match(r"\\[a-zA-Z]+", match):
            # 移除美元符号，保留中间的内容
            new_match = match.strip("$")
            # 用空格包围的内容替换原始匹配项
            content = content.replace(match, " " + new_match + " ")

    return content


def to_katex(formula: str) -> str:
    """
    将LaTeX公式转换为KaTeX兼容格式

    这个函数处理LaTeX公式字符串，将其转换为与KaTeX渲染兼容的格式。
    它移除不支持的命令和结构，简化LaTeX环境，并为Web显示优化公式。

    主要处理步骤：
    1. 移除容器命令（mbox, hbox, makebox等）
    2. 清理定位和缩放命令
    3. 统一样式命令
    4. 简化大小调节命令
    5. 合并连续的文本命令
    6. 最终格式化

    Args:
        formula: 要转换的LaTeX公式字符串

    Returns:
        KaTeX兼容的公式字符串
    """
    # 初始化结果字符串
    res = formula

    # === 第一阶段：移除容器命令 ===

    # 移除 \mbox 命令及其花括号，保留内容
    # \mbox 用于在数学模式中插入文本，KaTeX中可以直接使用文本
    res = change_all(res, r"\mbox ", r" ", r"{", r"}", r"", r"")  # 处理带空格的\mbox
    res = change_all(res, r"\mbox", r" ", r"{", r"}", r"", r"")   # 处理不带空格的\mbox

    # 移除 \hbox 命令及其复杂的尺寸参数
    # \hbox to 宽度{内容} -> \hbox{内容}，然后移除\hbox
    res = re.sub(r"\\hbox to ?-? ?\d+\.\d+(pt)?\{", r"\\hbox{", res)  # 简化带尺寸的hbox
    res = change_all(res, r"\hbox", r" ", r"{", r"}", r"", r" ")        # 移除hbox命令

    # 移除 \raise 命令（用于垂直定位）
    # \raise 数值pt 或 \raise -数值pt -> 空格
    res = re.sub(r"\\raise ?-? ?\d+\.\d+(pt)?", r" ", res)

    # 移除 \makebox 命令及其尺寸参数
    # \makebox[宽度]{内容} -> \makebox{内容}，然后移除\makebox
    res = re.sub(r"\\makebox ?\[\d+\.\d+(pt)?\]\{", r"\\makebox{", res)  # 简化带尺寸的makebox
    res = change_all(res, r"\makebox", r" ", r"{", r"}", r"", r" ")       # 移除makebox命令

    # === 第二阶段：移除定位和缩放命令 ===

    # 移除 \raisebox 和 \scalebox 命令的参数
    # 这些命令用于精确的定位和缩放，在KaTeX中通常不需要
    res = re.sub(r"\\raisebox\{-? ?\d+\.\d+(pt)?\}\{", r"\\raisebox{", res)  # 简化raisebox参数
    res = re.sub(r"\\scalebox\{-? ?\d+\.\d+(pt)?\}\{", r"\\scalebox{", res)   # 简化scalebox参数
    res = change_all(res, r"\scalebox", r" ", r"{", r"}", r"", r" ")           # 移除scalebox命令
    res = change_all(res, r"\raisebox", r" ", r"{", r"}", r"", r" ")           # 移除raisebox命令
    res = change_all(res, r"\vbox", r" ", r"{", r"}", r"", r" ")               # 移除vbox命令

    # === 第三阶段：处理字体大小命令 ===

    # 定义所有字体大小命令列表
    # 这些命令在KaTeX中的支持有限，通常可以忽略
    origin_instructions = [
        r"\Huge",        # 最大字体
        r"\huge",        # 很大字体
        r"\LARGE",       # 大字体
        r"\Large",       # 较大字体
        r"\large",       # 稍大字体
        r"\normalsize",  # 正常字体
        r"\small",       # 小字体
        r"\footnotesize", # 脚注字体
        r"\tiny",        # 最小字体
    ]
    # 处理美元符号包围的字体大小命令
    # 将 $\Large$ 形式转换为 {\Large} 形式（虽然这个循环实际上没有改变）
    for old_ins, new_ins in zip(origin_instructions, origin_instructions):
        res = change_all(res, old_ins, new_ins, r"$", r"$", "{", "}")

    # === 第四阶段：统一粗体命令 ===

    # 将所有粗体命令统一为 \bm（bold math）
    res = change_all(res, r"\mathbf", r"\bm", r"{", r"}", r"{", r"}")      # \mathbf -> \bm
    res = change_all(res, r"\boldmath ", r"\bm", r"{", r"}", r"{", r"}")   # \boldmath -> \bm (带空格)
    res = change_all(res, r"\boldmath", r"\bm", r"{", r"}", r"{", r"}")    # \boldmath -> \bm (不带空格)
    res = change_all(res, r"\boldmath ", r"\bm", r"$", r"$", r"{", r"}")   # 美元符号中的\boldmath -> \bm
    res = change_all(res, r"\boldmath", r"\bm", r"$", r"$", r"{", r"}")    # 美元符号中的\boldmath -> \bm

    # 处理 \scriptsize 命令（小字体）
    res = change_all(res, r"\scriptsize", r"\scriptsize", r"$", r"$", r"{", r"}")

    # 统一强调命令：\emph -> \textit
    res = change_all(res, r"\emph", r"\textit", r"{", r"}", r"{", r"}")    # \emph -> \textit
    res = change_all(res, r"\emph ", r"\textit", r"{", r"}", r"{", r"}")   # \emph -> \textit (带空格)

    # === 第五阶段：移除所有粗体命令 ===
    # 在统一为\bm后，现在完全移除粗体效果（KaTeX中粗体支持有限）
    res = change_all(res, r"\bm", r" ", r"{", r"}", r"", r"")

    # === 第六阶段：处理大小调节命令 ===

    # 定义所有大小调节命令列表
    # 这些命令用于调节括号、分隔符等的大小
    origin_instructions = [
        r"\left",    # 左自动调节
        r"\middle",  # 中间自动调节
        r"\right",   # 右自动调节
        r"\big",     # 大
        r"\Big",     # 更大
        r"\bigg",    # 很大
        r"\Bigg",    # 最大
        r"\bigl",    # 左大
        r"\Bigl",    # 左更大
        r"\biggl",   # 左很大
        r"\Biggl",   # 左最大
        r"\bigm",    # 中大
        r"\Bigm",    # 中更大
        r"\biggm",   # 中很大
        r"\Biggm",   # 中最大
        r"\bigr",    # 右大
        r"\Bigr",    # 右更大
        r"\biggr",   # 右很大
        r"\Biggr",   # 右最大
    ]
    # 移除这些命令的花括号，保留命令本身
    # 例如：\left{(} -> \left(
    for origin_ins in origin_instructions:
        res = change_all(res, origin_ins, origin_ins, r"{", r"}", r"", r"")

    # === 第七阶段：处理显示数学环境 ===

    # 将 \[...\] 显示数学环境转换为内联形式并添加换行
    # \[公式\] -> 公式\newline
    res = re.sub(r"\\\[(.*?)\\\]", r"\1\\newline", res)

    # 如果结果以\newline结尾，移除它（避免末尾多余的换行）
    if res.endswith(r"\newline"):
        res = res[:-8]  # 移除最后8个字符（\newline的长度）

    # === 第八阶段：清理间距命令 ===

    # 移除多个连续的间距命令，用单个空格替换
    # 这些LaTeX间距命令在KaTeX中可以简化为普通空格
    res = re.sub(r"(\\,){1,}", " ", res)     # 移除一个或多个 \, (细间距)
    res = re.sub(r"(\\!){1,}", " ", res)     # 移除一个或多个 \! (负间距)
    res = re.sub(r"(\\;){1,}", " ", res)     # 移除一个或多个 \; (中间距)
    res = re.sub(r"(\\:){1,}", " ", res)     # 移除一个或多个 \: (中细间距)
    res = re.sub(r"\\vspace\{.*?}", "", res) # 移除所有垂直间距命令

    # === 第九阶段：合并连续的文本命令 ===

    def merge_texts(match):
        """
        合并连续的\text{}命令

        将 \text{a}\text{b} 合并为 \text{ab}
        这样可以减少冗余并提高渲染效率

        Args:
            match: 正则匹配对象，包含连续的\text{}命令

        Returns:
            合并后的单个\text{}命令
        """
        # 获取匹配的完整文本
        texts = match.group(0)
        # 提取所有\text{}中的内容并连接
        merged_content = "".join(re.findall(r"\\text\{([^}]*)\}", texts))
        # 返回合并后的单个\text{}命令
        return f"\\text{{{merged_content}}}"

    # 查找并合并连续的\text{}命令（2个或更多）
    res = re.sub(r"(\\text\{[^}]*\}\s*){2,}", merge_texts, res)

    # === 第十阶段：最终清理 ===

    # 移除残留的\bf命令（粗体命令的另一种形式）
    res = res.replace(r"\bf ", "")

    # 移除美元符号包围（调用之前定义的辅助函数）
    res = _rm_dollar_surr(res)

    # 将多个连续空格合并为单个空格
    res = re.sub(r" +", " ", res)

    # === 第十一阶段：最终格式化 ===

    # 移除首尾空白字符
    res = res.strip()
    # 使用format_latex进行最终的LaTeX格式化
    # 这会处理缩进、换行等格式问题
    res = format_latex(res)

    return res
