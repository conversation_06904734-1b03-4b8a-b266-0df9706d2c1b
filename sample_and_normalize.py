#!/usr/bin/env python3
"""
LaTeX规范化批量处理脚本 - 简化输出版本
只显示规范前后的LaTeX代码对比，窗口简化
"""

import json
import random
import sys
from pathlib import Path

# 导入新的规范化系统
try:
    from latex_normalizer import LaTeXNormalizer
    print("✓ 使用新的模块化规范化系统")
    USE_NEW_SYSTEM = True
except ImportError:
    # 降级到旧系统
    try:
        from normalize_optimized import normalize_latex_string
        print("✓ 使用 normalize_optimized.py")
        USE_NEW_SYSTEM = False
    except ImportError:
        try:
            from normalize import normalize_latex_string
            print("✓ 使用 normalize.py")
            USE_NEW_SYSTEM = False
        except ImportError:
            print("✗ 错误：无法导入任何规范化函数")
            sys.exit(1)

def normalize_latex(latex_input):
    """规范化LaTeX字符串"""
    if USE_NEW_SYSTEM:
        normalizer = LaTeXNormalizer()
        return normalizer.normalize(latex_input)
    else:
        return normalize_latex_string(latex_input)


def load_labels_json(file_path):
    """加载labels.json文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"✗ 加载labels.json文件失败: {e}")
        return None


def random_sample(data, sample_size=100):
    """从数据中随机抽样"""
    if not data:
        return []

    # 获取所有的键值对
    items = list(data.items())
    total_count = len(items)

    if total_count <= sample_size:
        print(f"数据总量({total_count})小于等于抽样数量({sample_size})，返回全部数据")
        return items

    # 随机抽样
    indices = random.sample(range(total_count), sample_size)

    # 按索引获取样本
    samples = [items[i] for i in indices]
    return samples

def process_and_display_samples(samples):
    """处理样本并直接显示对比结果"""
    total = len(samples)
    changed_count = 0
    error_count = 0

    print("\n" + "=" * 80)
    print("LaTeX规范化对比结果")
    print("=" * 80)

    for i, (filename, original_formula) in enumerate(samples, 1):
        try:
            # 使用规范化脚本处理
            normalized_formula = normalize_latex(original_formula)

            # 检查是否有变化
            has_changed = original_formula != normalized_formula
            if has_changed:
                changed_count += 1

            # 显示对比结果
            print(f"\n[{i}/{total}] {filename}")
            print("─" * 60)
            print(f"规范前: {original_formula}")
            print(f"规范后: {normalized_formula}")

            if has_changed:
                print("状态: ✓ 已规范化")

                # 简单的改进分析
                length_change = len(normalized_formula) - len(original_formula)
                if length_change < 0:
                    print(f"      长度减少 {-length_change} 字符")
                elif length_change > 0:
                    print(f"      长度增加 {length_change} 字符")
                else:
                    print("      长度不变")
            else:
                print("状态: ─ 无变化")

        except Exception as e:
            error_count += 1
            print(f"\n[{i}/{total}] {filename}")
            print("─" * 60)
            print(f"规范前: {original_formula}")
            print(f"错误: {e}")
            print("状态: ✗ 处理失败")

    # 显示统计信息
    print("\n" + "=" * 80)
    print("统计信息")
    print("=" * 80)
    print(f"总样本数: {total}")
    print(f"成功处理: {total - error_count} ({(total-error_count)/total*100:.1f}%)")
    print(f"发生变化: {changed_count} ({changed_count/total*100:.1f}%)")
    print(f"处理失败: {error_count} ({error_count/total*100:.1f}%)")

    return changed_count, error_count

def main():
    """主函数"""

    # 检查命令行参数
    if len(sys.argv) > 1:
        labels_file = sys.argv[1]
        sample_count = int(sys.argv[2]) if len(sys.argv) > 2 else 50
    else:
        labels_file = "labels.json"
        sample_count = 50

    # 检查文件是否存在
    if not Path(labels_file).exists():
        print(f"✗ 错误: 找不到文件 {labels_file}")
        print(f"用法: python {sys.argv[0]} [labels.json] [样本数量]")
        return

    print("LaTeX规范化批量处理工具 (简化输出版本)")
    print(f"输入文件: {labels_file}")
    print(f"抽样数量: {sample_count}")

    # 1. 加载数据
    print(f"\n正在加载 {labels_file}...")
    data = load_labels_json(labels_file)
    if not data:
        return

    print(f"✓ 加载完成，共 {len(data)} 条数据")

    # 2. 随机抽样
    print(f"正在抽样 {sample_count} 个样本...")
    samples = random_sample(data, sample_count)
    print(f"✓ 抽样完成，获得 {len(samples)} 个样本")

    # 3. 处理样本并显示结果
    changed_count, error_count = process_and_display_samples(samples)

if __name__ == "__main__":
    main()
