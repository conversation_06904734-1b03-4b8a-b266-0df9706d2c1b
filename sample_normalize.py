#!/usr/bin/env python3
"""
从labels.json中均匀抽取1000个样本进行规范化处理
生成包含输入和输出对比的txt文档
"""

import json
import random
import sys
from pathlib import Path
from datetime import datetime

# 导入规范化函数
try:
    from normalize import normalize_latex_string
    print("使用 normalize.py 中的规范化函数")
except ImportError:
    try:
        from normalize_optimized import normalize_latex_string
        print("使用 normalize_optimized.py 中的规范化函数")
    except ImportError:
        print("错误：无法导入规范化函数，请确保 normalize.py 或 normalize_optimized.py 存在")
        sys.exit(1)

def load_labels_json(file_path):
    """加载labels.json文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载 {len(data)} 个样本")
        return data
    except Exception as e:
        print(f"加载labels.json失败: {e}")
        return None

def uniform_sample(data, sample_size=1000):
    """均匀抽取样本"""
    if len(data) <= sample_size:
        print(f"数据总量({len(data)})小于等于抽样数量({sample_size})，返回全部数据")
        return data
    
    # 计算抽样间隔
    keys = list(data.keys())
    total_size = len(keys)
    interval = total_size / sample_size
    
    sampled_keys = []
    for i in range(sample_size):
        index = int(i * interval)
        if index < total_size:
            sampled_keys.append(keys[index])
    
    # 构建抽样数据
    sampled_data = {key: data[key] for key in sampled_keys}
    print(f"均匀抽取了 {len(sampled_data)} 个样本")
    return sampled_data

def normalize_samples(sampled_data):
    """对抽样数据进行规范化处理"""
    results = []
    total = len(sampled_data)

    print(f"开始规范化处理 {total} 个样本...")

    # 临时禁用规范化脚本的详细输出
    import sys
    from io import StringIO

    for i, (filename, latex_input) in enumerate(sampled_data.items(), 1):
        try:
            # 捕获规范化脚本的输出
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            # 进行规范化处理
            latex_output = normalize_latex_string(latex_input)

            # 恢复输出
            sys.stdout = old_stdout

            # 记录结果
            results.append({
                'filename': filename,
                'input': latex_input,
                'output': latex_output,
                'changed': latex_input != latex_output
            })

            # 显示进度
            if i % 100 == 0 or i == total:
                print(f"进度: {i}/{total} ({i/total*100:.1f}%)")

        except Exception as e:
            # 恢复输出
            sys.stdout = old_stdout
            print(f"处理样本 {filename} 时出错: {e}")
            # 记录错误情况
            results.append({
                'filename': filename,
                'input': latex_input,
                'output': f"ERROR: {str(e)}",
                'changed': True
            })

    return results

def save_results_to_txt(results, output_file):
    """将结果保存为txt文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入文件头
            f.write("=" * 80 + "\n")
            f.write("LaTeX规范化处理结果对比\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总样本数: {len(results)}\n")
            
            # 统计信息
            changed_count = sum(1 for r in results if r['changed'])
            unchanged_count = len(results) - changed_count
            f.write(f"发生变化的样本: {changed_count}\n")
            f.write(f"未发生变化的样本: {unchanged_count}\n")
            f.write("=" * 80 + "\n\n")
            
            # 写入每个样本的详细信息
            for i, result in enumerate(results, 1):
                f.write(f"样本 {i}: {result['filename']}\n")
                f.write("-" * 60 + "\n")
                f.write(f"输入:\n{result['input']}\n\n")
                f.write(f"输出:\n{result['output']}\n\n")
                
                if result['changed']:
                    f.write("状态: 已规范化 ✓\n")
                else:
                    f.write("状态: 无变化\n")
                
                f.write("=" * 80 + "\n\n")
        
        print(f"结果已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"保存结果失败: {e}")
        return False

def main():
    """主函数"""
    # 配置参数
    labels_file = "labels.json"
    sample_size = 1000
    output_file = f"latex_normalization_samples_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    print("LaTeX规范化抽样测试工具")
    print("=" * 50)
    
    # 检查文件是否存在
    if not Path(labels_file).exists():
        print(f"错误：找不到文件 {labels_file}")
        return
    
    # 加载数据
    print(f"正在加载 {labels_file}...")
    data = load_labels_json(labels_file)
    if data is None:
        return
    
    # 均匀抽样
    print(f"正在均匀抽取 {sample_size} 个样本...")
    sampled_data = uniform_sample(data, sample_size)
    
    # 规范化处理
    results = normalize_samples(sampled_data)
    
    # 保存结果
    print(f"正在保存结果到 {output_file}...")
    if save_results_to_txt(results, output_file):
        print("处理完成！")
        
        # 显示统计信息
        changed_count = sum(1 for r in results if r['changed'])
        print(f"\n统计信息:")
        print(f"总样本数: {len(results)}")
        print(f"发生变化: {changed_count} ({changed_count/len(results)*100:.1f}%)")
        print(f"未发生变化: {len(results)-changed_count} ({(len(results)-changed_count)/len(results)*100:.1f}%)")
    else:
        print("保存失败！")

if __name__ == "__main__":
    main()
