"""
空格规范化器

实现FR1需求：空格规范化
- 移除规则：移除对渲染无影响的冗余空格
- 保留规则：保留有实际渲染意义的空格命令
"""

import re
from typing import List, Set

from ..utils.helpers import is_spacing_command, clean_whitespace


class SpacingNormalizer:
    """
    空格规范化器
    
    智能地管理LaTeX源码中的所有空格，移除对渲染无影响的冗余空格，
    同时保留有实际渲染意义的空格命令。
    """
    
    def __init__(self):
        """初始化空格规范化器"""
        # 需要保留的间距命令白名单
        self.spacing_commands = {
            # 窄/中/大间距
            '\\,', '\\:', '\\;', '\\thinspace', '\\medspace', '\\thickspace',
            # 负间距
            '\\!', '\\negthinspace', '\\negmedspace', '\\negthickspace',
            # 强制空格
            '\\ ',
            # em间距
            '\\quad', '\\qquad', '\\enspace'
        }
    
    def normalize(self, latex_code: str) -> str:
        """
        执行空格规范化
        
        Args:
            latex_code: 输入的LaTeX代码
            
        Returns:
            规范化后的LaTeX代码
        """
        if not latex_code:
            return latex_code
        
        result = latex_code
        
        # 第一阶段：保护重要的LaTeX结构
        result, protected_items = self._protect_spacing_commands(result)
        
        # 第二阶段：清理不必要的空格
        result = self._remove_redundant_spaces(result)
        
        # 第三阶段：恢复被保护的结构
        result = self._restore_protected_items(result, protected_items)
        
        return result
    
    def _protect_spacing_commands(self, text: str) -> tuple:
        """
        保护间距命令不被修改
        
        Args:
            text: 输入文本
            
        Returns:
            (保护后的文本, 保护项目列表)
        """
        protected_items = []
        result = text
        
        # 保护间距命令
        def protect_spacing(match):
            protected_items.append(match.group(0))
            return f'__SPACING_{len(protected_items)-1}__'
        
        # 按长度降序排序，确保长命令优先匹配
        for cmd in sorted(self.spacing_commands, key=len, reverse=True):
            pattern = re.escape(cmd)
            result = re.sub(pattern, protect_spacing, result)
        
        # 保护LaTeX换行符
        result = result.replace('\\\\', '__LATEX_NEWLINE__')
        
        return result, protected_items
    
    def _remove_redundant_spaces(self, text: str) -> str:
        """
        移除冗余空格
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        result = text
        
        # 1. 清理结构性空格
        # 花括号空格清理
        result = re.sub(r'\s*\{\s*', '{', result)
        result = re.sub(r'\s*\}', '}', result)
        
        # 方括号空格清理
        result = re.sub(r'\s*\[\s*', '[', result)
        result = re.sub(r'\s*\]', ']', result)
        
        # 圆括号空格清理
        result = re.sub(r'\s*\(\s*', '(', result)
        result = re.sub(r'\s*\)', ')', result)
        
        # 2. 清理运算符周围的空格
        operators = ['+', '-', '=', '<', '>', '*', '/', '^', '_', ',', ';', ':']
        for op in operators:
            result = re.sub(f'\\s*{re.escape(op)}\\s*', op, result)
        
        # 3. 清理LaTeX命令和参数之间的空格
        result = re.sub(r'(\\[A-Za-z]+)\s*\{', r'\1{', result)
        result = re.sub(r'(\\[A-Za-z]+)\s*\[', r'\1[', result)
        result = re.sub(r'(\\[A-Za-z]+)\s*\(', r'\1(', result)

        # 4. 特殊处理：保留某些命令后的必要空格
        # 对于需要后跟空格的命令，添加回空格
        spacing_needing_commands = ['\\quad', '\\qquad', '\\enspace']
        for cmd in spacing_needing_commands:
            # 如果命令后面直接跟字母，添加空格
            pattern = f'({re.escape(cmd)})([a-zA-Z])'
            result = re.sub(pattern, r'\1 \2', result)
        
        # 5. 清理数学字体命令内部的空格
        math_fonts = ['mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt']
        for font in math_fonts:
            pattern = f'\\\\{font}\\s*\\{{\\s*([^}}]+)\\s*\\}}'
            def clean_font_spaces(match):
                content = re.sub(r'\s+', '', match.group(1))
                return f'\\{font}{{{content}}}'
            result = re.sub(pattern, clean_font_spaces, result)

        # 6. 清理多余的连续空格
        result = re.sub(r'\s{2,}', ' ', result)
        result = re.sub(r'^\s+|\s+$', '', result)
        
        return result
    
    def _restore_protected_items(self, text: str, protected_items: List[str]) -> str:
        """
        恢复被保护的项目
        
        Args:
            text: 处理后的文本
            protected_items: 保护的项目列表
            
        Returns:
            恢复后的文本
        """
        result = text
        
        # 恢复被保护的间距命令
        for i, spacing in enumerate(protected_items):
            result = result.replace(f'__SPACING_{i}__', spacing)
        
        # 恢复LaTeX换行符
        result = result.replace('__LATEX_NEWLINE__', '\\\\')
        
        return result
    
    def validate_spacing_commands(self, text: str) -> tuple:
        """
        验证文本中的间距命令
        
        Args:
            text: 要验证的文本
            
        Returns:
            (有效的间距命令列表, 无效的间距命令列表)
        """
        # 提取所有可能的间距命令
        potential_commands = re.findall(r'\\[a-zA-Z]*space|\\[,:;!]|\\\\ |\\quad|\\qquad', text)
        
        valid_commands = []
        invalid_commands = []
        
        for cmd in potential_commands:
            if cmd in self.spacing_commands:
                valid_commands.append(cmd)
            else:
                invalid_commands.append(cmd)
        
        return valid_commands, invalid_commands
    
    def get_spacing_statistics(self, text: str) -> dict:
        """
        获取空格统计信息
        
        Args:
            text: 要分析的文本
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_chars': len(text),
            'space_chars': text.count(' '),
            'spacing_commands': 0,
            'spacing_command_types': set()
        }
        
        # 统计间距命令
        for cmd in self.spacing_commands:
            count = text.count(cmd)
            if count > 0:
                stats['spacing_commands'] += count
                stats['spacing_command_types'].add(cmd)
        
        stats['spacing_command_types'] = list(stats['spacing_command_types'])
        
        return stats
