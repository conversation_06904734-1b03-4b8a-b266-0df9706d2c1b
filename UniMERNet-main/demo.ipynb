#%% md
### 1. Image processing class: accepts formula images, outputs LaTeX code and rendered images.

#%%
import argparse
import os
import random
import sys

from IPython.display import display, Math
from PIL import Image
from rich import print as rprint
from rich.panel import Panel
from rich.rule import Rule
from rich.table import Table
from termcolor import colored
import torch

sys.path.insert(0, os.path.join(os.getcwd(), ".."))
from unimernet.common.config import Config
from unimernet.datasets.builders import *
from unimernet.models import *
from unimernet.processors import *
import unimernet.tasks as tasks
from unimernet.processors import load_processor

class ImageProcessor:
    
    def __init__(self, cfg_path, image_dir):
        self.cfg_path = cfg_path
        self.image_dir = image_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model, self.vis_processor = self.load_model_and_processor()

    def load_model_and_processor(self):
        args = argparse.Namespace(cfg_path=self.cfg_path, options=None)
        cfg = Config(args)
        task = tasks.setup_task(cfg)
        model = task.build_model(cfg).to(self.device)
        vis_processor = load_processor('formula_image_eval', cfg.config.datasets.formula_rec_eval.vis_processor.eval)

        return model, vis_processor

    def process_single_image(self, image_path):
        try:
            raw_image = Image.open(image_path)
        except IOError:
            print(f"Error: Unable to open image at {image_path}")
            return

        resized_image = self.resize_image(raw_image)
        image = self.vis_processor(raw_image).unsqueeze(0).to(self.device)
        output = self.model.generate({"image": image})
        pred = output["pred_str"][0]
        self.print_result(0, image_path, resized_image, pred)
        rprint(Rule(style="black"))

    def process_images(self):
        image_names = os.listdir(self.image_dir)
        image_paths = [os.path.join(self.image_dir, name) for name in image_names]

        for id, image_path in enumerate(image_paths):
            raw_image = Image.open(image_path)
            resized_image = self.resize_image(raw_image)
            image = self.vis_processor(raw_image).unsqueeze(0).to(self.device)
            output = self.model.generate({"image": image})
            pred = output["pred_str"][0]
            self.print_result(id, image_path, resized_image, pred)
            rprint(Rule(style="black"))

    @staticmethod
    def resize_image(image, max_len=600):
        width, height = image.size
        if max(width, height) > max_len :
            if width > height:
                scale = float(max_len) / width
                width = max_len
                height = int(height * scale)
            else:
                scale = float(max_len) / height
                height = max_len
                width = int(width * scale)

        return image.resize((width, height))

    @staticmethod
    def print_result(id, image_path, raw_image, pred):
        colors = ['red', 'green', 'yellow', 'blue', 'magenta', 'cyan']
        chosen_color = random.choice(colors)

        table = Table(show_header=True, header_style=chosen_color)
        table.add_column("Sample ID", style="dim", width=12)
        table.add_column("Image Path", style="dim", width=80)
        table.add_row(str(id), image_path)
        rprint(table)
        print(colored(f"{id}_1: Source image", chosen_color), end=" ")
        display(raw_image)
        print(colored(f'{id}_2: Rendered image from LaTeX', chosen_color), end=" ")
        render_katex(pred)
        print(colored(f'{id}_3: Predicted LaTeX code', chosen_color), end=" ")
        pred_text_panel = Panel.fit(pred, title="Predicted LaTeX", border_style=chosen_color)
        rprint(pred_text_panel)

def render_katex(latex_string, show=True):
    display(Math(latex_string))

#%%
root_path = os.path.abspath(os.getcwd())
config_path = os.path.join(root_path, "configs/demo.yaml")
image_directory = os.path.join(root_path, "asset/test_imgs")

processor = ImageProcessor(config_path, image_directory)

# Process a single image located at the specified path
processor.process_single_image(os.path.join(image_directory, '0000001.png'))

# Uncomment the following line to process all images in the specified directory
# processor.process_images()

#%%

#%%
