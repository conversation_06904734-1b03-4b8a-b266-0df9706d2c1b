\begin{array} { r l r } { \Upsilon _ { 1 , m } } & { = } & { \tau + \Theta _ { 1 , m } , } \\ { \Upsilon _ { 2 , m } } & { = } & { \left\{ \begin{array} { c } { \tau + \Theta _ { 2 , m } , \mathrm { ~ f o r ~ } - 2 n \leq m \leq 0 } \\ { 3 \tau + \Theta _ { 2 , m } , \mathrm { ~ f o r ~ } 0 < m \leq 2 n } \end{array} \right. , } \\ { \Upsilon _ { 3 , m } } & { = } & { 3 \tau + \Theta _ { 3 , m } } \end{array} | \begin{array} {r l r} {\Upsilon_{1,m}} &=& {\tau+\Theta_{1,m},} \\ {\Upsilon_{2,m}} &=& {\left{\begin{array}{c}{\tau+\Theta_{2,m},\mathrm{~for~}-2 n \leq m \leq 0}\\{3 \tau+\Theta_{2,m},\mathrm{~for~}0<m \leq 2 n} \end{array}\right.,}\\{\Upsilon_{3,m}} &=&{3 \tau+\Theta_{3,m}} \end{array}
\langle J _ { \chi } ( x _ { 1 } ) J _ { \chi } ( x _ { 2 } ) \rangle _ { s ( \psi ) } = - \sum _ { I = 1 } ^ { 6 } Y _ { 1 } [ s ( \chi ^ { I } ) ] \, Y _ { 1 } [ s ( y ^ { I } ) ] \, Y _ { 1 } [ s ( \omega ^ { I } ) ] . | \langle J_ \chi ( x_ 1 ) J_ \chi ( x_ 2 ) \rangle _ {s(\psi)} =- \sum _ {I=1} ^ 6 Y_ 1 [s(\chi^I)] ,Y_ 1 [s(y^I)] ,Y_ 1 [s(\omega^I)]
\begin{array} { r } { H = \sum _ { \mathbf q , q _ { z } } \hbar \omega _ { X } X _ { \mathbf q , q _ { z } } ^ { \dagger } X _ { \mathbf q , q _ { z } } + \sum _ { \mathbf q , q _ { z } } \hbar \omega _ { \mathbf q , q _ { z } } ^ { c } + H _ { r a d } } \end{array} | \begin{array} {r} {H=\sum_{\mathbf q,q_{z}} \hbar \omega_{X} X_{\mathbf q,q_{z}}^{\dagger} X_{\mathbf q,q_{z}}+\sum_{\mathbf q,q_{z}} \hbar \omega_{\mathbf q,q_{z}}^{c}+H_{r a d}} \end{array}
\hat { S } ^ { ( 1 ) } \; | \; \theta \; \rangle \; = \; e ^ { i \theta } \; | \; \theta \; \rangle \; . | \hat{S} ^ {(1)} ;|; \theta ; \rangle ;=;e^ {i \theta} ;|; \theta ; \rangle ;
\varepsilon y u = - \frac { 1 } { 2 } b _ { y } , | \varepsilon y u=- \frac {1} {2} b_y,
b > 0 | b> 0
\sigma ^ { i } | \sigma ^i
b _ { 0 } | b_ 0
\begin{align*}\varrho:=\varrho\left(\beta\right)=\sqrt{\frac{\underline{M}}{T}\log\left(\log^{\kappa}\left(\gamma\left(T,\beta\right)\right)\right)}>0,\end{align*} | \begin{aligned} \varrho := \varrho \left ( \beta \right ) = \sqrt{\frac{\underline{M}{T} \log \left(\log^\kappa \left(\gamma \left(T,\beta \right) \right) \right)} } > 0 , \end{aligned}
\begin{align*} F(z,v) := \sum_{n} \sum_{m} T_{n} \mathbb{P}\{D_{n} = m\} \frac{z^{n}}{n!} v^{m} = \sum_{n} \sum_{m} \mathbb{P}\{D_{n} = m\} \frac{z^{n}}{n} v^{m}.\end{align*} | \begin{aligned} F(z,v):= \sum _n \sum _m T_n \mathbb{P} {D_n=m} \frac {z^n} {n !} v^m= \sum _n \sum _m \mathbb{P} {D_n=m} \frac {z^n} {n} v^m . \end{aligned}
m _ { s } ^ { 2 } = { \frac { \partial ^ { 2 } \Gamma ^ { ( 1 ) } } { \partial \rho ^ { 2 } } } \Big \vert _ { { \rho = \bar { \rho } \atop A = 0 } } = \delta m _ { \lambda } ^ { ( 1 ) ^ { 2 } } + \delta m _ { e } ^ { ( 1 ) ^ { 2 } } | m_ {s} ^ {2} = {\frac{\partial^{2} \Gamma^{(1)}}{\partial \rho^{2}}} \Big \vert _ {\rho=\bar{\rho} \atop A=0} = \delta m_ {\lambda} ^ {(1)^{2}} + \delta m_ {e} ^ {(1)^{2}}
x _ { 1 } , x _ { 2 } , x _ { 3 } , . . . , x _ { T - 1 } , x _ { T } | x_ 1 ,x_ 2 ,x_ 3 ,. . .,x_ {T-1} ,x_T
d i m ( R ) = \ \frac { d _ { R } } { n ! } \prod _ { i = 1 } ^ { r } \frac { \lambda _ { i } ! } { ( N - i ) ! } | d i m(R)= \frac {d_R} {n !} \prod _ {i=1} ^r \frac {\lambda_i !} {(N-i) !}
0 . 0 8 = \frac { 8 } { 1 0 0 } = \frac { 2 } { 2 5 } | 0 . 0 8 = \frac {8} {1 0 0} = \frac {2} {2 5}
3 0 | 3 0
\begin{array} { r } { | \partial _ { v } ^ { m _ { 1 } } \partial _ { v ^ { \prime } } ^ { m - m _ { 1 } } \tilde { \chi } _ { 2 } ( v ) \tilde { \chi } _ { 2 } ( v ^ { \prime } ) \Phi _ { j , 1 } ^ { r e } ( v , v ^ { \prime } k ) | \leq C ( k ) \Gamma _ { s } ( m ) ( M ) ^ { m } , \quad \mathrm { ~ f ~ o ~ r ~ } \quad j = 0 , 1 . } \end{array} | \begin{array} {r} {| \partial_v^{m_1} \partial_{v^\prime}^{m-m_1} \tilde{\chi}_2(v) \tilde{\chi}_2(v^\prime) \Phi_{j,1}^{r e}(v,v^\prime k) | \leq C(k) \Gamma_s(m)(M)^m,\quad \mathrm{~f~o~r~}\quad j=0,1 .} \end{array}
\begin{align*}\lim_{k\to\infty}\|\pi_k\circ\Phi(a)-a_k\|=0,\end{align*} | \begin{aligned} \lim _ {k \to \infty} | \pi _k \circ \Phi ( a)-a_k |= 0 , \end{aligned}
\begin{align*}{\cal D}=\{u\in H^1({\mathbf R})\cap H^2({\mathbf R}\setminus{\mathbf Z}):u'(j+)-u'(j-)=Vu(j),\ j\in{\mathbf Z}\}.\end{align*} | \begin{aligned} \cal D= {u \in H^1(\mathbf{R}) \cap H^2(\mathbf{R} \setminus \mathbf{Z}):u^\prime(j+)-u^\prime(j-)=V u(j),j \in \mathbf{Z}} . \end{aligned}
\begin{align*}x_{\rm c}[n]=\begin{cases}\kappa_{\rm t}, & \mathrm{if} x[n]\geq \kappa_{\rm t},\\x[n], & \mathrm{if} \kappa_{\rm b}<x[n]< \kappa_{\rm t},\\\kappa_{\rm b}, & \mathrm{if} x[n]\leq \kappa_{\rm b}, \end{cases}\end{align*} | \begin{aligned} x_ {\rm c} [n] = \begin{cases} \kappa _ {\rm t} ,& \mathrm{if} x [n] \geq \kappa _ {\rm t} , \\ x [n] ,& \mathrm{if} \kappa _ {\rm b} <x [n] < \kappa _ {\rm t} , \\ \kappa _ {\rm b} ,& \mathrm{if} x [n] \leq \kappa _ {\rm b} , \end{cases} \end{aligned}
\frac { \mathrm { d } ( \mathrm { K E + A P E } ) } { \mathrm { d } t } = - \int _ { V } \rho ( \varepsilon _ { k } + \varepsilon _ { p } ) \, \mathrm { d } V | \frac {\mathrm{d}(\mathrm{KE+APE})} {\mathrm{d} t} =- \int _ {V} \rho ( \varepsilon _ {k} + \varepsilon _ {p} ) \, \mathrm{d} V
( n _ { r } , n _ { p } ) | ( n_r,n_p)
\begin{align*}g^* f_* \mathcal M = \varprojlim_k g^* f_* \tau_{\le k} \mathcal M = \varprojlim_k f'_* g'^* \tau_{\le k} \mathcal M = f'_* \varprojlim_k g'^* \tau_{\le k} \mathcal M = f'_* g'^* \mathcal M,\end{align*} | \begin{aligned} g^ {*} f_ {*} \mathcal{M} = \varprojlim _k g^ {*} f_ {*} \tau _ {\le k} \mathcal{M} = \varprojlim _k f_ {*} ^ \prime g^ {*} \tau _ {\le k} \mathcal{M} =f_ {*} ^ \prime \varprojlim _k g^ {*} \tau _ {\le k} \mathcal{M} =f_ {*} ^ \prime g^ {*} \mathcal{M} , \end{aligned}
\Ddot { \Sigma } \colon A ^ { 1 } \times A ^ { 3 } \to Q ( V ^ { 1 } , V ^ { 3 } ) | \Ddot { \Sigma } \colon A^ 1 \times A^ 3 \to Q(V^ 1 ,V^ 3 )
\begin{array} { r l } { \bar { F } _ { 3 \, 2 } ^ { - 3 } ( i ) = } & { { } - \frac { 1 } { 1 6 } \sqrt { 1 0 5 } \sin i ( \cos i ) ^ { 2 } + \frac { 1 } { 8 } \sqrt { 1 0 5 } \sin i \cos i } \end{array} | \begin{array} {r l} {\bar{F}_{3,2}^{-3}(i)=} & {-\frac{1}{1 6} \sqrt{1 0 5} \sin i(\cos i)^2+\frac{1}{8} \sqrt{1 0 5} \sin i \cos i} \end{array}
N = 3 2 | N= 3 2
\hat { \textbf { z } } ^ { ( \tau ) } - \textbf { z } ^ { ( \tau ) } | \hat{\textbf z} ^ {(\tau)} - \textbf { z^ } {(\tau)}
\xi ^ { ( i ) } ( x _ { + } ) \equiv \langle \lambda ^ { i } | T _ { L } , { } ~ ~ ~ ~ \bar { \xi } ^ { ( i ) } ( x _ { - } ) \equiv T _ { R } ^ { - 1 } | \lambda ^ { i } \rangle | \xi ^ {(i)} ( x_ {+} ) \equiv \langle \lambda ^i | T_L,~ ~ ~ ~ \bar{\xi} ^ {(i)} ( x_ {-} ) \equiv T_R^ {-1} | \lambda ^i \rangle
\quad c _ { n } \rightarrow 0 \quad { \mathrm { a s } } \quad n \to \infty | \quad c_n \rightarrow 0 \quad \mathrm{as} \quad n \to \infty
\sim l _ { \perp } | \sim l_ {\perp}
\begin{align*}_{B(r(\gamma))}\langle a,b\rangle=ab^* \langle a,b\rangle_{B(s(\gamma))}=a^*b.\end{align*} | \begin{aligned} _ {B(r(\gamma))} \langle a,b \rangle =a b^ {*} \langle a,b \rangle _ {B(s(\gamma))} =a^ {*} b . \end{aligned}
\begin{align*}\Phi(\eta) = r(\eta)+ \frac{1}{2}r(\eta)^{2} + \cdots + \frac{1}{\ell} r(\eta)^{\ell} - \eta^{\ell} \log \frac{r(\eta)}{\eta}.\end{align*} | \begin{aligned} \Phi ( \eta ) =r( \eta ) + \frac {1} {2} r( \eta ) ^ 2 + . . . + \frac {1} {\ell} r( \eta ) ^ \ell - \eta ^ \ell \log \frac {r(\eta)} {\eta} . \end{aligned}
\ensuremath { \texttt { n } _ { \texttt { d o f } } } | \ensuremath {\texttt n_{\texttt d o f}}
E _ { 2 } | E_ 2
- S _ { N } \cdot ( 1 + S _ { N } z ^ { - 1 } ) z ^ { N + 1 } + C _ { N } \cdot C _ { N } z ^ { N } = ( 1 - S _ { N } z ) z ^ { N } , | -S_N \cdot ( 1 +S_N z^ {-1} ) z^ {N+1} +C_N \cdot C_N z^N=( 1 -S_N z) z^N,
\begin{array} { r l } { \partial _ { t } \epsilon } & { = - \partial _ { i } \pi ^ { i } , } \\ { \partial _ { t } \pi ^ { i } } & { = - v _ { \| } ^ { 2 } \partial ^ { i } \epsilon + D _ { \pi } ^ { \perp } \, \partial ^ { 2 } \pi ^ { i } + \left( D _ { \pi } ^ { \| } - D _ { \pi } ^ { \perp } \right) \partial ^ { i } \partial _ { k } \pi ^ { k } , } \end{array} | \begin{array} {r l} {\partial_t \epsilon} & {=-\partial_i \pi^i,} \\ {\partial_t \pi^i} & {=-v_|^2 \partial^i \epsilon+D_\pi^{\perp},\partial^2 \pi^i+\left(D_\pi^|-D_\pi^{\perp} \right) \partial^i \partial_k \pi^k,} \end{array}
w h e r e Z \equiv ( k _ { 1 } + k _ { 2 } ) \phi + l _ { 1 } \psi | w h e r e Z \equiv ( k_ 1 +k_ 2 ) \phi +l_ 1 \psi
f ( x _ { 2 } ) = 2 7 | f(x_ 2 ) = 2 7
\chi _ { ( \psi ) , a i } = p _ { ( \psi ) , a i } + i g _ { i j } \bar { \psi } _ { a } ^ { j } , \quad \mathrm { a n d } \quad \chi _ { ( \bar { \psi } ) , i } ^ { a } = p _ { ( \bar { \psi } ) , i } ^ { a } + i g _ { i j } \psi ^ { a j } , | \chi _ {(\psi),a i} =p_ {(\psi),a i} +i g_ {i j} \bar{\psi} _a^j, \quad \mathrm{and} \quad \chi _ {(\bar{\psi}),i} ^a=p_ {(\bar{\psi}),i} ^a+i g_ {i j} \psi ^ {a j} ,
L = 2 4 0 | L= 2 4 0
\begin{align*}F^q(B_\phi\eta,p) = B_\phi F^q(\eta,p)\end{align*} | \begin{aligned} F^q(B_ \phi \eta ,p)=B_ \phi F^q( \eta ,p) \end{aligned}
k _ { 0 } ( c ) = k _ { 0 } ^ { * } ( 1 - c ) | k_ 0 ( c)=k_ 0 ^ {*} ( 1 -c)
\begin{align*}\alpha_{i}(f,g)=c\delta_{1,i}+\sum_{j\in f^{-1}(i)}a_{j}+\sum_{j\in f^{-1}(i)}b_{j}.\end{align*} | \begin{aligned} \alpha _i(f,g)=c \delta _ {1,i} + \sum _ {j \in f^{-1}(i)} a_j+ \sum _ {j \in f^{-1}(i)} b_j . \end{aligned}
\begin{array} { r } { \langle \boldsymbol { \mathcal { A } } _ { j , \boldsymbol { k } } ^ { \bot } | \boldsymbol { \varPsi } _ { \mathrm { d i v } , j , \boldsymbol { k } } ^ { \epsilon } \rangle = \langle \boldsymbol { \mathcal { A } } _ { j , \boldsymbol { k } } | \boldsymbol { \varPsi } _ { \mathrm { d i v } , j , \boldsymbol { k } } ^ { \epsilon } \rangle , } \end{array} | \begin{array} {r} {\langle \boldsymbol{\mathcal{A}}_{j,\boldsymbol{k}}^{\bot} | \boldsymbol{\varPsi}_{\mathrm{div},j,\boldsymbol{k}}^{\epsilon} \rangle=\langle \boldsymbol{\mathcal{A}}_{j,\boldsymbol{k}} | \boldsymbol{\varPsi}_{\mathrm{div},j,\boldsymbol{k}}^{\epsilon} \rangle,} \end{array}
E _ { e f f } ^ { ( 1 1 1 ) } | E_ {e f f} ^ {(1 1 1)}
\Phi ( x ) = \Phi _ { a , b } ( x ) T _ { 1 } ^ { a } T _ { 2 } ^ { b } ~ , | \Phi ( x)= \Phi _ {a,b} ( x) T_ 1 ^a T_ 2 ^b ~,
\begin{align*}(q-d_1)(q-d_2-2)\cdots(q-d_t-2r+2)=\prod_{i=1}^r(q-d_i-2i+2).\end{align*} | \begin{aligned} ( q-d_ 1 ) ( q-d_ 2 - 2 ) . . . ( q-d_t- 2 r+ 2 ) = \prod _ {i=1} ^r(q-d_i- 2 i+ 2 ) . \end{aligned}
G _ { N } | G_N
G | G
A = 1 + \frac { e ^ { 2 } } { 6 4 \Pi _ { e } ( A ^ { 2 } + B ^ { 2 } ) } [ - \frac { 3 2 } { \pi ^ { 2 } } B ^ { 2 } + \frac { 4 8 } { \pi ^ { 2 } } B C - \frac { 1 6 } { \pi ^ { 2 } } A D + B E + ( \frac { 1 6 } { \pi ^ { 2 } } - 4 ) A F ] | A= 1 + \frac {e^2} {6 4 \Pi_e(A^2+B^2)} [-\frac{3 2}{\pi^2} B^2+\frac{4 8}{\pi^2} B C-\frac{1 6}{\pi^2} A D+B E+(\frac{1 6}{\pi^2}-4) A F]
\begin{array} { r } { \phi _ { \jmath } ^ { R + 1 } = \frac { 1 } { 2 } \left[ \Delta x ^ { 2 } \left[ n _ { i } - n _ { e 0 } \left( 1 + \frac { q _ { e } \phi ^ { R } } { ( \kappa _ { e } - \frac { 3 } { 2 } ) k _ { B } T _ { e } } \right) ^ { - \kappa _ { e } + \frac { 1 } { 2 } } \right. \right. } \\ { \left. \left. + n _ { p 0 } \left( 1 + \frac { q _ { p } \phi ^ { R } } { ( \kappa _ { p } - \frac { 3 } { 2 } ) k _ { B } T _ { p } } \right) ^ { - \kappa _ { p } + \frac { 1 } { 2 } } \right] _ { \jmath } + \phi _ { \jmath + 1 } ^ { R } + \phi _ { \jmath - 1 } ^ { R + 1 } \right] . } \end{array} | \begin{array} {r} {\phi_\jmath^{R+1}=\frac{1}{2} \left[\Delta x^2 \left[n_i-n_{e 0} \left(1+\frac{q_e \phi^R}{(\kappa_e-\frac{3}{2}) k_B T_e} \right)^{-\kappa_e+\frac{1}{2}}\right.\right.} \\ {\left.\left.+n_{p 0} \left(1+\frac{q_p \phi^R}{(\kappa_p-\frac{3}{2}) k_B T_p} \right)^{-\kappa_p+\frac{1}{2}} \right]_\jmath+\phi_{\jmath+1}^R+\phi_{\jmath-1}^{R+1} \right] .} \end{array}
\alpha \left| g g \right\rangle + \beta \left| g e \right\rangle + \gamma \left| e g \right\rangle + \delta \left| e e \right\rangle \longrightarrow \alpha \left| g g \right\rangle + \beta \left| g e \right\rangle + \gamma \left| e g \right\rangle - \delta \left| e e \right\rangle \; . | \alpha \left | g g \right \rangle + \beta \left | g e \right \rangle + \gamma \left | e g \right \rangle + \delta \left | e e \right \rangle \longrightarrow \alpha \left | g g \right \rangle + \beta \left | g e \right \rangle + \gamma \left | e g \right \rangle - \delta \left | e e \right \rangle ;
\langle M _ { i j } M _ { k l } \rangle = \frac { 1 } { N } ( \delta _ { i l } \delta _ { k j } + \mathrm { e } ^ { - 2 \beta } L _ { i l } L _ { k j } ) , | \langle M_ {i j} M_ {k l} \rangle = \frac {1} {N} ( \delta _ {i l} \delta _ {k j} + \mathrm{e} ^ {-2 \beta} L_ {i l} L_ {k j} ) ,
\begin{align*}\mathcal{X}(t)+\int^t_{0}\mathcal{X}(\tau)d\tau\leqslant C_0\sum_{j=1}^{m}\int^t_{0}\mathcal{X}(\tau)^{\alpha_j}d\tau+C_0\sum_{k=1}^{n}\mathcal{X}(t)^{\beta_k}+C_0\sum_{k=1}^{n}\mathcal{X}(0)^{\beta_k}+C_0\mathcal{X}(0),\end{align*} | \begin{aligned} \mathcal{X} ( t)+ \int _ 0 ^t \mathcal{X} ( \tau ) d \tau \leqslant C_ 0 \sum _ {j=1} ^m \int _ 0 ^t \mathcal{X} ( \tau ) ^ {\alpha_j} d \tau +C_ 0 \sum _ {k=1} ^n \mathcal{X} ( t)^ {\beta_k} +C_ 0 \sum _ {k=1} ^n \mathcal{X} ( 0 ) ^ {\beta_k} +C_ 0 \mathcal{X} ( 0 ) , \end{aligned}
\begin{array} { r l } { \int _ { \Omega } H ( \nabla v ) ^ { p - 1 } H _ { \xi } ( \nabla v ) \nabla \left( \frac { u ^ { p } } { ( v + \varepsilon ) ^ { p - 1 } } \right) } & { + \mathtt k \int _ { \partial \Omega } \frac { u ^ { p } v ^ { p - 1 } } { ( v + \varepsilon ) ^ { p - 1 } } = \lambda \int m \frac { u ^ { p } v ^ { p - 1 } } { ( v + \varepsilon ) ^ { p - 1 } } } \\ { = } & { \lambda \int m \frac { u ^ { p } v ^ { p - 1 } } { ( v + \varepsilon ) ^ { p - 1 } } - \lambda ^ { + } ( m ) \int _ { \Omega } m u ^ { p } } \\ & { + \int _ { \Omega } H ^ { p } ( \nabla u ) + \mathtt k \int _ { \partial \Omega } u ^ { p } . } \end{array} | \begin{array} {r l} {\int_\Omega H(\nabla v)^{p-1} H_\xi(\nabla v) \nabla \left(\frac{u^p}{(v+\varepsilon)^{p-1}} \right)} & {+\mathtt{k}\int_{\partial \Omega} \frac{u^p v^{p-1}}{(v+\varepsilon)^{p-1}}=\lambda \int m \frac{u^p v^{p-1}}{(v+\varepsilon)^{p-1}}} \\ =& {\lambda \int m \frac{u^p v^{p-1}}{(v+\varepsilon)^{p-1}}-\lambda^{+}(m) \int_\Omega m u^p} \\ & {+\int_\Omega H^p(\nabla u)+\mathtt{k}\int_{\partial \Omega} u^p .} \end{array}
\delta \lambda , \delta \psi _ { r } , \delta \psi _ { i } \propto r ^ { \frac { 3 - p } { 4 } } | \delta \lambda , \delta \psi _r, \delta \psi _i \propto r^ {\frac{3-p}{4}}
| \Psi ( t ) \rangle = e ^ { - i \hat { K } ( t ) } | \Phi ( t ) \rangle | | \Psi ( t) \rangle =e^ {-i \hat{K}(t)} | \Phi ( t) \rangle
Q = { \frac { 1 } { 2 \pi } } \int ~ d x ~ ( J _ { R } - J _ { L } ) = { \frac { 1 } { 2 \pi } } \int ~ d x ~ \partial _ { x } \Phi | Q= {\frac{1}{2 \pi}} \int ~ d x ~(J_R-J_L)= {\frac{1}{2 \pi}} \int ~ d x ~ \partial _x \Phi
a ( \sigma ) | a( \sigma )
\begin{align*}\sigma_\alpha P\sigma^\alpha=s^1Ps^1+s^2Ps^2+s^3Ps^3-s^4Ps^4.\end{align*} | \begin{aligned} \sigma _ \alpha P \sigma ^ \alpha =s^ 1 P s^ 1 +s^ 2 P s^ 2 +s^ 3 P s^ 3 -s^ 4 P s^ 4 . \end{aligned}
3 p _ { 1 / 2 } ^ { 0 . 3 6 } \, 3 p _ { 3 / 2 } ^ { 0 . 6 4 } | 3 p_ {1/2} ^ {0 . 3 6} , 3 p_ {3/2} ^ {0 . 6 4}
\begin{array} { r l } { \mathbf { F } } & { { } = { \frac { d \mathbf { p } } { d t } } = { \frac { d ( m \mathbf { v } ) } { d t } } } \end{array} | \begin{array} {r l} {\mathbf{F}} & {={\frac{d \mathbf{p}}{d t}}={\frac{d(m \mathbf{v})}{d t}}} \end{array}
\psi = ( \gamma _ { 3 } \tau _ { 0 } ^ { 2 } / | \tilde { \beta } _ { 2 } | ) ^ { 1 / 2 } E | \psi =( \gamma _ 3 \tau _ 0 ^ 2 /| \tilde{\beta} _ 2 |)^ {1/2} E
F _ { 5 } = - { \frac { 4 R ^ { 2 } } { H ^ { 2 } r ^ { 5 } } } ( R ^ { 4 } + r _ { 0 } ^ { 4 } ) ^ { 1 / 2 } ( 1 + * ) \, d t \wedge d x \wedge d y \wedge d z \wedge d r \, , | F_ 5 =- {\frac{4 R^2}{H^2 r^5}} ( R^ 4 +r_ 0 ^ 4 ) ^ {1/2} ( 1 +*),d t \wedge d x \wedge d y \wedge d z \wedge d r,,
\begin{align*}m_N &= \frac{1}{N} \sum_{i=1}^N g_i + \frac{1}{N} \sum_{i=1}^N g_i^2 ( T \psi + TY_i) + \O \left( \frac{1}{N} \sum_{i=1}^N |g_i|^3 \varphi^{\xi} \frac{1}{N} \right) \\&= \frac{1}{N} \sum_{i=1}^N g_i + \frac{1}{N} \sum_{i=1}^N g_i^2 ( T \psi + Q_i [ G_{ii}^{-1} ]) + \O \left( \frac{1}{N} \sum_{i=1}^N |g_i|^3 \varphi^{\xi} \frac{1}{N} \right).\end{align*} | \begin{aligned} m_N & {=\frac{1}{N} \sum_{i=1}^N g_i+\frac{1}{N} \sum_{i=1}^N g_i^2(T \psi+T Y_i)+\O \left(\frac{1}{N} \sum_{i=1}^N | g_i |^3 \varphi^\xi \frac{1}{N} \right)} \\ & {=\frac{1}{N} \sum_{i=1}^N g_i+\frac{1}{N} \sum_{i=1}^N g_i^2(T \psi+Q_i[G_{i i}^{-1}])+\O \left(\frac{1}{N} \sum_{i=1}^N | g_i |^3 \varphi^\xi \frac{1}{N} \right) . }\end{aligned}
\begin{align*} F(X) = \sum_{i = 0}^{2^n-1} A_i X^i.\end{align*} | \begin{aligned} F(X)= \sum _ {i=0} ^ {2^n-1} A_i X^i . \end{aligned}
\begin{align*}&(n-1)!\lim_{X\to\infty}\frac{\#\{p\in Spl_X(f)\mid r_i/p<a\}}{\#Spl_X(f)}\\=&\sum_{0\le h \le n\atop 1\le l \le n-1}\sum_{k =i}^n(-1)^{h+k+n}{n\choose k}\sum_{m=1}^{n-1}{k \choose n-h-m+l}{n-k \choose m-l}M(l-ha)^{n-1},\end{align*} | \begin{aligned} &(n- 1 ) ! \lim _ {X\to\infty} \frac {\#\{p\in Spl_X(f)\mid r_i/p<a\}} {\#Spl_X(f)} \\ =& \sum _ {0\le h \le n\atop 1\le l \le n-1} \sum _ {k=i} ^n(- 1 ) ^ {h+k+n} {n\choose k} \sum _ {m=1} ^ {n-1} {k \choose n-h-m+l} {n-k \choose m-l} M(l-ha)^ {n-1} , \end{aligned}
\begin{array} { r l } { | \psi _ { \mathrm { o u t } } \rangle = \bigg [ \bigg ( \frac { C ( \phi _ { 1 } , \phi _ { 2 } ) ^ { 2 } } { 2 B ( \phi _ { 1 } , \phi _ { 2 } ) - 2 } - \frac { B ( \phi _ { 1 } , \phi _ { 2 } ) } { 2 } - \frac 1 2 \bigg ) a _ { 1 } ^ { \dagger } } & { } \\ { + \bigg ( \frac { C ( \phi _ { 1 } , \phi _ { 2 } ) ^ { 2 } } { 2 B ( \phi _ { 1 } , \phi _ { 2 } ) - 2 } - \frac { B ( \phi _ { 1 } , \phi _ { 2 } ) } { 2 } + \frac 1 2 \bigg ) a _ { 2 } ^ { \dagger } } & { \bigg ] | 0 \rangle , } \end{array} | \begin{array} {r l} {| \psi_{\mathrm{out}} \rangle=\bigg[\bigg(\frac{C(\phi_{1},\phi_{2})^{2}}{2 B(\phi_{1},\phi_{2})-2}-\frac{B(\phi_{1},\phi_{2})}{2}-\frac 1 2 \bigg) a_{1}^{\dagger}} &  \\ {+\bigg(\frac{C(\phi_{1},\phi_{2})^{2}}{2 B(\phi_{1},\phi_{2})-2}-\frac{B(\phi_{1},\phi_{2})}{2}+\frac 1 2 \bigg) a_{2}^{\dagger}} & {\bigg] | 0 \rangle,} \end{array}
x = { \frac { \alpha - 1 + { \sqrt { \frac { ( \alpha - 1 ) ( \beta - 1 ) } { \alpha + \beta - 3 } } } } { \alpha + \beta - 2 } } | x= {\frac{\alpha-1+\sqrt{\frac{(\alpha-1)(\beta-1)}{\alpha+\beta-3}}}{\alpha+\beta-2}}
E _ { D } = 2 . 4 \times 1 0 ^ { 3 } | E_D= 2 . 4 \times 1 0 ^ 3
W _ { X } = ( \eta _ { A } - \mathrm { \frac { 1 } { 2 } } m ^ { 2 } ( P _ { + } ) _ { A } ^ { B } \bar { \phi } _ { B } ) \phi ^ { A } - \phi _ { A a } ^ { * } \pi ^ { A a } - \bar { \phi } _ { A } ( \lambda ^ { A } - \mathrm { \frac { 1 } { 2 } } m ^ { 2 } ( P _ { - } ) _ { B } ^ { A } \phi ^ { B } ) . | W_X=( \eta _A- \mathrm { {\frac{1}{2}} } m^ 2 ( P_ {+} ) _A^B \bar{\phi} _B) \phi ^A- \phi _ {A a} ^ {*} \pi ^ {A a} - \bar{\phi} _A( \lambda ^A- \mathrm { {\frac{1}{2}} } m^ 2 ( P_ {-} ) _B^A \phi ^B)
\Delta t = \int _ { t _ { i } } ^ { t _ { f } } \textrm { d } t = \frac { 1 } { 2 } \int _ { s _ { i } } ^ { s _ { f } } \frac { \gamma \mathrm { d } s } { k _ { \mathrm { B } } T ( s ) - s \kappa } . | \Delta t= \int _ {t_{i}} ^ {t_{f}} \textrm{d} t= \frac {1} {2} \int _ {s_{i}} ^ {s_{f}} \frac {\gamma \mathrm{d} s} {k_{\mathrm{B}} T(s)-s \kappa}
1 1 4 | 1 1 4
\eta | \eta
+ \left| \begin{array} { l l l l } { \frac { 1 } { \left( 1 - y \right) \left( 1 - z \right) } } & { - 1 } & { 0 } & { 0 } \\ { \frac { 1 } { \left( 1 - y ^ { 2 } \right) \left( 1 - z ^ { 2 } \right) } } & { \frac { 1 } { \left( 1 - y \right) \left( 1 - z \right) } } & { - 2 } & { 0 } \\ { \frac { 1 } { \left( 1 - y ^ { 3 } \right) \left( 1 - z ^ { 3 } \right) } } & { \frac { 1 } { \left( 1 - y ^ { 2 } \right) \left( 1 - z ^ { 2 } \right) } } & { \frac { 1 } { \left( 1 - y \right) \left( 1 - z \right) } } & { - 3 } \\ { \frac { 1 } { \left( 1 - y ^ { 4 } \right) \left( 1 - z ^ { 4 } \right) } } & { \frac { 1 } { \left( 1 - y ^ { 3 } \right) \left( 1 - z ^ { 3 } \right) } } & { \frac { 1 } { \left( 1 - y ^ { 2 } \right) \left( 1 - z ^ { 2 } \right) } } & { \frac { 1 } { \left( 1 - y \right) \left( 1 - z \right) } } \end{array} \right| \frac { t ^ { 4 } } { 4 ! } | + \left | \begin{array} {l l l l} {\frac{1}{\left(1-y \right) \left(1-z \right)}} & {-1} & 0 & 0 \\ {\frac{1}{\left(1-y^2 \right) \left(1-z^2 \right)}} & {\frac{1}{\left(1-y \right) \left(1-z \right)}} & {-2} & 0 \\ {\frac{1}{\left(1-y^3 \right) \left(1-z^3 \right)}} & {\frac{1}{\left(1-y^2 \right) \left(1-z^2 \right)}} & {\frac{1}{\left(1-y \right) \left(1-z \right)}} & {-3} \\ {\frac{1}{\left(1-y^4 \right) \left(1-z^4 \right)}} & {\frac{1}{\left(1-y^3 \right) \left(1-z^3 \right)}} & {\frac{1}{\left(1-y^2 \right) \left(1-z^2 \right)}} & {\frac{1}{\left(1-y \right) \left(1-z \right)}} \end{array} \right | \frac {t^4} {4 !}
{ \cal M } ^ { 2 } ( \alpha _ { 0 } , R ) < { \cal M } ^ { 2 } ( \alpha _ { 1 } , R ) < \cdots < { \cal M } ^ { 2 } ( \alpha _ { M } , R ) \ . | \cal M^ 2 ( \alpha _ 0 ,R)< \cal M^ 2 ( \alpha _ 1 ,R)< . . . < \cal M^ 2 ( \alpha _M,R)
\begin{array} { l l } { 1 ) } & { h = 7 / 1 6 , \quad a = 7 / 1 6 , \quad m = 0 , } \\ { 2 ) } & { h = 7 / 1 6 , \quad a = 3 / 8 0 , \quad m = 0 , } \\ { 3 ) } & { h = 7 / 1 6 + x , \quad a = ( 3 / 8 0 , 7 / 1 6 ) , \quad m = 0 , } \\ { 4 ) } & { h = 7 / 1 6 + x , \quad a = 3 / 8 0 , \quad m = \sqrt { x } / 2 . } \\ \end{array} | \begin{array} {l l} {1)} & {h=7/1 6,\quad a=7/1 6,\quad m=0,} \\ {2)} & {h=7/1 6,\quad a=3/8 0,\quad m=0,} \\ {3)} & {h=7/1 6+x,\quad a=(3/8 0,7/1 6),\quad m=0,} \\ {4)} & {h=7/1 6+x,\quad a=3/8 0,\quad m=\sqrt{x}/2 .} \end{array}
\alpha | \alpha
+ | +
\Delta D - D \Delta = ( \gamma + { \bar { \gamma } } ) D + ( \varepsilon + { \bar { \varepsilon } } ) \Delta - ( { \bar { \tau } } + \pi ) \delta - ( \tau + { \bar { \pi } } ) { \bar { \delta } } \, , | \Delta D-D \Delta =( \gamma + {\bar{\gamma}} ) D+( \varepsilon + {\bar{\varepsilon}} ) \Delta -( {\bar{\tau}} + \pi ) \delta -( \tau + {\bar{\pi}} ) {\bar{\delta}} ,,
\begin{align*}L^2(Y_N)=\bigoplus_\pi m(\pi)\pi^{K(N)K_HK_\infty},\end{align*} | \begin{aligned} L^ 2 ( Y_N)= \bigoplus _ \pi m( \pi ) \pi ^ {K(N) K_H K_\infty} , \end{aligned}
\begin{align*}6|\gamma_2| \leq 2-\frac{|c_1|^2}{2}+\frac{1}{8}\sqrt{(d^2+5+2d q)^2 -16d^2(1-q^2)}=:g(d,q).\end{align*} | \begin{aligned} 6 | \gamma _ 2 | \leq 2 - \frac {| c_1 |^2} {2} + \frac {1} {8} \sqrt{(d^2+5+2 d q)^2-1 6 d^2(1-q^2)} =:g(d,q) . \end{aligned}
\begin{align*} A_n(s)=&(S_n^{[s]})^TQ_n(s),s\in\{1,\dotsc,\gamma_r\}\\ =&\sum_{i=1}^{\ell_r^*} \left(\frac{1}{f_{g((s-1)\ell_r^*+i)}-\alpha_n}W_{\theta,i}^{[s]}\right)1_{\{i\in J_r^{[s]}\}}\\ &\quad+P_{\alpha_n}(\lfloor\frac{N}{2}\rfloor).\end{align*} | \begin{aligned} A_n(s)= & {(S_n^{[s]})^T Q_n(s),s \in{1,. . .,\gamma_r}} \\ =& {\sum_{i=1}^{\ell_r^{*}} \left(\frac{1}{f_{g((s-1) \ell_r^{*}+i)}-\alpha_n} W_{\theta,i}^{[s]} \right) 1_{i \in J_r^{[s]}}} \\ & {\quad+P_{\alpha_n}(\lfloor \frac{N}{2} \rfloor) . }\end{aligned}
w _ { \alpha _ { 1 } \cdots \alpha _ { N } } ^ { \varepsilon _ { 1 } \cdots \varepsilon _ { N } } ( z _ { 1 } , \cdots , z _ { N } ) = 0 , \mathrm { ~ ~ ~ ~ i f ~ } ~ ~ ( \alpha _ { 1 } , \cdots , \alpha _ { N } ) < ( \varepsilon _ { 1 } , \cdots , \varepsilon _ { N } ) . | w_ {\alpha_1 . . . \alpha_N} ^ {\varepsilon_1 . . . \varepsilon_N} ( z_ 1 , . . . ,z_N)= 0 , \mathrm{~~~~if~} ~ ~( \alpha _ 1 , . . . , \alpha _N)<( \varepsilon _ 1 , . . . , \varepsilon _N)
a | a
\Sigma _ { A } ( \bar { p } ) \simeq \frac { i e ^ { 2 } } { ( 2 \pi ) ^ { 3 } } | e H | \int d q _ { 0 } d q _ { 3 } \int _ { 0 } ^ { \infty } d r ^ { 2 } \mathrm { e } ^ { - r ^ { 2 } } \frac { G _ { 1 } } { q ^ { 2 } } \frac { \Sigma _ { A } ( \bar { p " } ) } { \bar { p " } ^ { 2 } + \Sigma _ { A } ( \bar { p " } ) } | \Sigma _A( \bar{p} ) \simeq \frac {i e^2} {(2 \pi)^3} | e H | \int d q_ 0 d q_ 3 \int _ 0 ^ \infty d r^ 2 \mathrm{e} ^ {-r^2} \frac {G_1} {q^2} \frac {\Sigma_A(\bar{p "})} {\bar{p "}^2+\Sigma_A(\bar{p "})}
t | t
\begin{align*}\widehat{S}_{W,m}=\widehat{R}_{m-\operatorname*{Inert}W_{xx}}(S_{W})\end{align*} | \begin{aligned} \widehat { S_ } {W,m} = \widehat { R_ } {m-\operatorname*I n e r t W_{x x}} ( S_W) \end{aligned}
z | z
\begin{align*} \sum_{x\in[n]} f(T, x) = \sum_{x \in [n]} \phi_j(x) \nu_i^{2t+2} \phi_i(x) =\nu_i^{2t+2} \delta_{ij}, \end{align*} | \begin{aligned} \sum _ {x \in[n]} f(T,x)= \sum _ {x \in[n]} \phi _j(x) \nu _i^ {2 t+2} \phi _i(x)= \nu _i^ {2 t+2} \delta _ {i j} , \end{aligned}
{ \cal M } _ { c } ^ { 2 } = \left( \lambda A _ { \lambda } x + \lambda k x ^ { 2 } - v _ { 1 } v _ { 2 } \left( \lambda - \frac { g ^ { 2 } } { 2 } \right) \right) \left( \begin{array} { c c } { { \tan \beta } } & { { 1 } } \\ { { 1 } } & { { \cot \beta } } \end{array} \right) . | \cal M_c^ 2 = \left ( \lambda A_ \lambda x+ \lambda k x^ 2 -v_ 1 v_ 2 \left ( \lambda - \frac {g^2} {2} \right ) \right ) \left ( \begin{array} {c c} {\tan \beta} & 1 \\ 1 & {\cot \beta} \end{array} \right )
{ \pi a _ { 0 } ^ { 2 } = \mathrm { ~ 0 ~ . ~ 8 ~ 8 ~ } \! \times \! \mathrm { ~ 1 ~ 0 ~ } ^ { - \mathrm { ~ 1 ~ 6 ~ } } } | {\pi a_{0}^{2}=\mathrm{~0~.~8~8~} \! \times \! \mathrm{~1~0~}^{-\mathrm{~1~6~}}}
( \hat { \eta } \psi ) ( p ) = \int _ { 0 } ^ { \infty } d p ^ { \prime } [ P _ { + } ( p , p ^ { \prime } ) - P _ { - } ( p , p ^ { \prime } ) ] \psi ( p ^ { \prime } ) . | ( \hat{\eta} \psi ) ( p)= \int _ 0 ^ \infty d p^ \prime [P_{+}(p,p^\prime)-P_{-}(p,p^\prime)] \psi ( p^ \prime )
\begin{align*}\mathbf{z}=(z_{1},z_{2})\in SU(2)=S^{3}\subset\mathbb{C}^{2},\Phi_{j}(\mathbf{z})=\tilde{Z}\in\mathcal{M}_{j}\subset S^{2n+1}\subset\mathbb{C}^{n+1}\end{align*} | \begin{aligned} \mathbf{z} =(z_ 1 ,z_ 2 ) \in S U( 2 ) =S^ 3 \subset \mathbb{C} ^ 2 , \Phi _j( \mathbf{z} ) = \tilde{Z} \in \mathcal{M} _j \subset S^ {2 n+1} \subset \mathbb{C} ^ {n+1} \end{aligned}
\frac { - | \mathbf { k } | ^ { 2 } \lambda k _ { 0 } ^ { 2 } } { \{ | \mathbf { k } | ^ { 2 } - \lambda k _ { 0 } ^ { 2 } - i \varepsilon \} ( k ^ { 2 } + i \varepsilon ) } | \frac {-| \mathbf{k} |^{2} \lambda k_{0}^{2}} {\{| \mathbf{k} |^{2}-\lambda k_{0}^{2}-i \varepsilon \}(k^{2}+i \varepsilon)}
\begin{align*} \mathcal L_N:=S_N''(U^{(N)})\end{align*} | \begin{aligned} \mathcal{L} _N:=S_N^ {\prime \prime} ( U^ {(N)} ) \end{aligned}
\begin{array} { r } { \sigma ^ { ( k ) } = \frac { \alpha _ { k } R _ { k } r _ { k } } { c _ { V } \rho } = \frac { \alpha _ { k } ( p _ { k } + p _ { * k } ) } { c _ { V } \rho \theta } } \\ { = \frac { \alpha _ { k } ( p _ { + } + p _ { * k } ) } { c _ { V } \rho \theta } , \ \ k = 1 , 2 , } \\ { \langle \sigma ^ { ( k ) } \rangle = \frac { p _ { + } + \langle \alpha _ { k } p _ { * k } \rangle } { c _ { V } \rho \theta } = \gamma - 1 . } \end{array} | \begin{array} {r} {\sigma^{(k)}=\frac{\alpha_k R_k r_k}{c_V \rho}=\frac{\alpha_k(p_k+p_{*k})}{c_V \rho \theta}} \\ {=\frac{\alpha_k(p_{+}+p_{*k})}{c_V \rho \theta},k=1,2,} \\ {\langle \sigma^{(k)} \rangle=\frac{p_{+}+\langle \alpha_k p_{*k} \rangle}{c_V \rho \theta}=\gamma-1 .} \end{array}
\begin{array} { r l } { \sigma _ { i j } = } & { - 2 \mathrm { K n } \left[ \frac { 1 } { 2 } \left( \frac { \partial v _ { i } } { \partial x _ { j } } + \frac { \partial v _ { j } } { \partial x _ { i } } \right) - \frac { 1 } { 3 } \delta _ { i j } \frac { \partial v _ { \ell } } { \partial x _ { \ell } } \right] } \\ & { - 2 \alpha _ { 0 } \mathrm { K n } \left[ \frac { 1 } { 2 } \left( \frac { \partial q _ { i } } { \partial x _ { j } } + \frac { \partial q _ { j } } { \partial x _ { i } } \right) - \frac { 1 } { 3 } \delta _ { i j } \frac { \partial q _ { \ell } } { \partial x _ { \ell } } \right] , } \\ { q _ { i } = } & { - \frac { c _ { p } \mathrm { K n } } { \mathrm { P r } } \left( \frac { \partial T } { \partial x _ { i } } + \alpha _ { 0 } \frac { \partial \sigma _ { i j } } { \partial x _ { j } } \right) , } \end{array} | \begin{array} {r l} {\sigma_{i j}=} & {-2 \mathrm{Kn} \left[\frac{1}{2} \left(\frac{\partial v_{i}}{\partial x_{j}}+\frac{\partial v_{j}}{\partial x_{i}} \right)-\frac{1}{3} \delta_{i j} \frac{\partial v_{\ell}}{\partial x_{\ell}} \right]} \\ & {-2 \alpha_{0} \mathrm{Kn} \left[\frac{1}{2} \left(\frac{\partial q_{i}}{\partial x_{j}}+\frac{\partial q_{j}}{\partial x_{i}} \right)-\frac{1}{3} \delta_{i j} \frac{\partial q_{\ell}}{\partial x_{\ell}} \right],} \\ {q_{i}=} & {-\frac{c_{p} \mathrm{Kn}}{\mathrm{Pr}} \left(\frac{\partial T}{\partial x_{i}}+\alpha_{0} \frac{\partial \sigma_{i j}}{\partial x_{j}} \right),} \end{array}
S = \frac { ( d - 2 ) \Sigma _ { k } } { 1 6 \pi G } \int d t \ d r e ^ { \nu + \lambda } \left[ r ^ { d - 1 } \varphi ( 1 + \tilde { \alpha } \varphi ) + \frac { r ^ { d - 1 } } { l ^ { 2 } } \right] ^ { \prime } , | S= \frac {(d-2) \Sigma_k} {1 6 \pi G} \int d t d r e^ {\nu+\lambda} \left [r^{d-1} \varphi(1+\tilde{\alpha} \varphi)+\frac{r^{d-1}}{l^2} \right] ^ \prime ,
\begin{align*}|I(L_\alpha,L_\beta)|=O\left( m\sqrt{n}+ km\right),\end{align*} | \begin{aligned} | I(L_ \alpha ,L_ \beta ) |=O \left ( m \sqrt{n} +k m \right ) , \end{aligned}
\begin{align*}\partial_{\theta_i} \theta_j=-\theta_j \partial_{\theta_i}\hbox{ if } 1 \leq i \neq j \leq n\qquad\hbox{and}\partial_{\theta_i} \theta_i = 1\hbox{ for }1 \leq i \leq n~.\end{align*} | \begin{aligned} \partial _ {\theta_i} \theta _j=- \theta _j \partial _ {\theta_i} \mathrm{~if~} 1 \leq i \neq j \leq n \qquad \mathrm{and} \partial _ {\theta_i} \theta _i= 1 \mathrm{~for~} 1 \leq i \leq n ~ . \end{aligned}
