"""
配置管理器

实现C1需求：Token替换配置文件管理
- 读取和解析CSV配置文件
- 提供同义词映射功能
- 支持配置验证和错误处理
- 支持动态配置加载
"""

import csv
import os
from pathlib import Path
from typing import Dict, Optional, Tuple, List


class ConfigManager:
    """
    配置管理器类
    
    负责管理LaTeX规范化的所有配置，特别是同义词Token替换规则。
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为当前模块目录
        """
        if config_dir is None:
            config_dir = Path(__file__).parent
        
        self.config_dir = Path(config_dir)
        self.synonym_map: Dict[str, str] = {}
        self.csv_file_path = self.config_dir / "token_synonyms.csv"
        
        # 加载配置
        self._load_synonym_config()
    
    def _load_synonym_config(self) -> None:
        """
        加载同义词配置文件
        
        从CSV文件中读取同义词映射规则，格式：
        source_token,target_token
        """
        if not self.csv_file_path.exists():
            print(f"警告: 同义词配置文件不存在: {self.csv_file_path}")
            return
        
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as csvfile:
                csv_reader = csv.reader(csvfile)
                
                # 跳过标题行（如果存在）
                first_row = next(csv_reader, None)
                if first_row and len(first_row) >= 2:
                    # 检查是否是标题行
                    if first_row[0].lower() in ['source_token', 'source', 'synonym']:
                        pass  # 跳过标题行
                    else:
                        # 不是标题行，处理这一行
                        self._process_csv_row(first_row, 1)
                
                # 处理剩余行
                for row_num, row in enumerate(csv_reader, 2):
                    self._process_csv_row(row, row_num)
                    
        except Exception as e:
            print(f"错误: 读取同义词配置文件失败: {e}")
    
    def _process_csv_row(self, row: List[str], row_num: int) -> None:
        """
        处理CSV文件中的一行数据
        
        Args:
            row: CSV行数据
            row_num: 行号（用于错误报告）
        """
        # 跳过空行或列数不足的行
        if not row or len(row) < 2:
            return
        
        source_token = row[0].strip()
        target_token = row[1].strip()
        
        # 跳过空的条目
        if not source_token or not target_token:
            print(f"警告: 第{row_num}行包含空的token，已跳过")
            return
        
        # 检查重复定义
        if source_token in self.synonym_map:
            print(f"警告: 第{row_num}行重复定义token '{source_token}'，使用新值")
        
        self.synonym_map[source_token] = target_token
    
    def get_synonym_map(self) -> Dict[str, str]:
        """
        获取同义词映射字典
        
        Returns:
            同义词映射字典，key为源token，value为目标token
        """
        return self.synonym_map.copy()
    
    def get_sorted_synonyms(self) -> List[str]:
        """
        获取按长度降序排序的同义词列表
        
        这样可以避免短token被长token的一部分误匹配
        
        Returns:
            按长度降序排序的同义词列表
        """
        return sorted(self.synonym_map.keys(), key=len, reverse=True)
    
    def replace_token(self, token: str) -> str:
        """
        替换单个token
        
        Args:
            token: 要替换的token
            
        Returns:
            替换后的token，如果没有找到映射则返回原token
        """
        return self.synonym_map.get(token, token)
    
    def replace_tokens_in_text(self, text: str) -> Tuple[str, int]:
        """
        在文本中替换所有同义词token
        
        Args:
            text: 输入文本
            
        Returns:
            (替换后的文本, 替换次数)
        """
        if not text or not self.synonym_map:
            return text, 0
        
        result = text
        replacement_count = 0
        
        # 按长度降序排序，避免短token被误匹配
        for source_token in self.get_sorted_synonyms():
            target_token = self.synonym_map[source_token]
            
            if source_token in result:
                new_result = result.replace(source_token, target_token)
                if new_result != result:
                    replacement_count += 1
                    result = new_result
        
        return result, replacement_count
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """
        验证配置的有效性
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查配置文件是否存在
        if not self.csv_file_path.exists():
            errors.append(f"配置文件不存在: {self.csv_file_path}")
        
        # 检查是否有重复的目标token
        target_tokens = list(self.synonym_map.values())
        duplicate_targets = set([t for t in target_tokens if target_tokens.count(t) > 1])
        if duplicate_targets:
            errors.append(f"发现重复的目标token: {duplicate_targets}")
        
        # 检查是否有循环引用
        for source, target in self.synonym_map.items():
            if target in self.synonym_map and self.synonym_map[target] == source:
                errors.append(f"发现循环引用: {source} <-> {target}")
        
        return len(errors) == 0, errors
    
    def reload_config(self) -> bool:
        """
        重新加载配置文件
        
        Returns:
            是否加载成功
        """
        try:
            self.synonym_map.clear()
            self._load_synonym_config()
            return True
        except Exception as e:
            print(f"错误: 重新加载配置失败: {e}")
            return False
    
    def get_config_info(self) -> Dict[str, any]:
        """
        获取配置信息
        
        Returns:
            配置信息字典
        """
        return {
            "config_file": str(self.csv_file_path),
            "file_exists": self.csv_file_path.exists(),
            "synonym_count": len(self.synonym_map),
            "synonyms": self.synonym_map.copy()
        }
