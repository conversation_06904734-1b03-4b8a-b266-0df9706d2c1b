| 处理节点名称 | UniMERNet的处理方法 | LaTeX-OCR的处理方法 | MathNet的处理方法 | im2latexv3的处理方法 |
|------------|-------------------|-------------------|------------------|-------------------|
| mathord | 数学原子字符处理：如果字体为mathrm，对每个字符添加空格，空格字符后添加\;；否则直接添加值和空格 | √ | √ | √ |
| textord | 文本字符处理：直接添加字符值和空格 | √ | √ | √ |
| bin | 二元运算符处理：直接添加运算符值和空格 | √ | √ | √ |
| rel | 关系运算符处理：直接添加关系符值和空格 | √ | √ | √ |
| open | 开括号处理：直接添加开括号值和空格 | √ | √ | √ |
| close | 闭括号处理：直接添加闭括号值和空格 | √ | √ | √ |
| inner | 内部元素处理：直接添加元素值和空格 | √ | √ | √ |
| punct | 标点符号处理：直接添加标点值和空格 | √ | √ | √ |
| ordgroup | 有序组处理：用花括号"{}"包围内容，递归处理组内元素 | √ | √ | √ |
| text | 文本处理：用"\\mathrm { }"包围文本内容，递归处理文本主体 | √ | √ | √ |
| color | 颜色处理：创建MathML节点并设置颜色属性（实现不完整） | √ | √ | √ |
| supsub | 上下标处理：先处理基础部分，然后根据上下标类型决定是否添加花括号包围 | √ | √ | √ |
| genfrac | 通用分数处理：根据hasBarLine判断使用"\\frac"或"\\binom"，递归处理分子分母 | √ | √ | √ |
| array | 数组处理：构建完整的"\\begin{array}{对齐}...\\end{array}"结构，处理列对齐和行分隔 | 数组处理：与UniMERNet基本相同，但使用原始的空行检测逻辑（row[0].value.length > 0），未包含UniMERNet的空单元格优化 | 数组处理：显著增强版本，支持动态环境类型（array/tabular等），处理列对齐类型（align/separator），支持\\hline命令，改进的行检测逻辑（row.length > 1 \|\| row[0].value.length > 0） | √ |
| sqrt | 平方根处理：支持带指数的根号，有指数时使用"\\sqrt[指数]"，否则使用"\\sqrt" | √ | 平方根处理：支持带指数的根号，但处理方式略有简化，直接插入指数值而非递归构建 | √ |
| leftright | 左右括号对处理：使用"\\left"和"\\right"命令包围内容 | √ | √ | √ |
| accent | 重音符号处理：根据基础类型决定是否用花括号包围，非ordgroup类型需要花括号 | √ | √ | √ |
| spacing | 间距处理：空格字符转换为"~"，其他间距命令直接添加 | √ | √ | √ |
| op | 运算符处理：区分符号运算符和文本运算符，后者使用"\\operatorname"或"\\operatorname*" | √ | √ | √ |
| katex | KaTeX标记处理：创建包含"KaTeX"文本的MathML节点 | √ | √ | √ |
| font | 字体处理：将mbox/hbox统一转换为mathrm，添加字体命令前缀 | √ | √ | √ |
| delimsizing | 分隔符大小调整：添加函数名和分隔符值 | √ | √ | √ |
| styling | 样式处理：保留原始样式命令，递归处理样式内容 | √ | √ | √ |
| sizing | 大小调整处理：特殊处理\\rm命令转换为mathrm，其他大小命令保持原样 | √ | √ | √ |
| overline | 上划线处理：用"\\overline { }"包围内容 | √ | √ | √ |
| underline | 下划线处理：用"\\underline { }"包围内容 | √ | √ | √ |
| rule | 规则线处理：生成包含宽度和高度参数的"\\rule"命令 | √ | √ | √ |
| llap | 左重叠处理：添加"\\llap"命令前缀 | √ | √ | √ |
| rlap | 右重叠处理：添加"\\rlap"命令前缀 | √ | √ | √ |
| phantom | 幻影处理：用"\\phantom { }"包围内容，递归处理幻影内容 | √ | √ | √ | 