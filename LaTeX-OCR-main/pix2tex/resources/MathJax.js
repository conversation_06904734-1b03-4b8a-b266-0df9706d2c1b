document.getElementById&&document.childNodes&&document.createElement&&(window.MathJax&&MathJax.Hub||(window.MathJax?window.MathJax={AuthorConfig:window.MathJax}:window.MathJax={},MathJax.version="2.7.5",MathJax.fileversion="2.7.5",MathJax.cdnVersion="2.7.5",MathJax.cdnFileVersions={},function(BASENAME){var BASE=window.MathJax;BASE||(BASE=window.MathJax={});var PROTO=[],OBJECT=function(def){var obj=def.constructor;for(var id in obj||(obj=function(){}),def)"constructor"!==id&&def.hasOwnProperty(id)&&(obj[id]=def[id]);return obj},CONSTRUCTOR=function(){return function(){return arguments.callee.Init.call(this,arguments)}};BASE.Object=OBJECT({constructor:function(){return arguments.callee.Init.call(this,arguments)},Subclass:function(def,classdef){var obj=function(){return arguments.callee.Init.call(this,arguments)};return obj.SUPER=this,obj.Init=this.Init,obj.Subclass=this.Subclass,obj.Augment=this.Augment,obj.protoFunction=this.protoFunction,obj.can=this.can,obj.has=this.has,obj.isa=this.isa,obj.prototype=new this(PROTO),obj.prototype.constructor=obj,obj.Augment(def,classdef),obj},Init:function(args){var obj=this;return 1===args.length&&args[0]===PROTO?obj:(obj instanceof args.callee||(obj=new args.callee(PROTO)),obj.Init.apply(obj,args)||obj)},Augment:function(def,classdef){var id;if(null!=def){for(id in def)def.hasOwnProperty(id)&&this.protoFunction(id,def[id]);def.toString!==this.prototype.toString&&def.toString!=={}.toString&&this.protoFunction("toString",def.toString)}if(null!=classdef)for(id in classdef)classdef.hasOwnProperty(id)&&(this[id]=classdef[id]);return this},protoFunction:function(id,def){this.prototype[id]=def,"function"==typeof def&&(def.SUPER=this.SUPER.prototype)},prototype:{Init:function(){},SUPER:function(fn){return fn.callee.SUPER},can:function(method){return"function"==typeof this[method]},has:function(property){return void 0!==this[property]},isa:function(obj){return obj instanceof Object&&this instanceof obj}},can:function(method){return this.prototype.can.call(this,method)},has:function(property){return this.prototype.has.call(this,property)},isa:function(obj){for(var constructor=this;constructor;){if(constructor===obj)return!0;constructor=constructor.SUPER}return!1},SimpleSUPER:OBJECT({constructor:function(def){return this.SimpleSUPER.define(def)},define:function(src){var dst={};if(null!=src){for(var id in src)src.hasOwnProperty(id)&&(dst[id]=this.wrap(id,src[id]));src.toString!==this.prototype.toString&&src.toString!=={}.toString&&(dst.toString=this.wrap("toString",src.toString))}return dst},wrap:function(id,f){if("function"!=typeof f||!f.toString().match(/\.\s*SUPER\s*\(/))return f;var fn=function(){this.SUPER=fn.SUPER[id];try{var result=f.apply(this,arguments)}catch(err){throw delete this.SUPER,err}return delete this.SUPER,result};return fn.toString=function(){return f.toString.apply(f,arguments)},fn}})}),BASE.Object.isArray=Array.isArray||function(obj){return"[object Array]"===Object.prototype.toString.call(obj)},BASE.Object.Array=Array}("MathJax"),function(BASENAME){var BASE=window.MathJax;BASE||(BASE=window.MathJax={});var isArray=BASE.Object.isArray,CALLBACK=function(data){var cb=function(){return arguments.callee.execute.apply(arguments.callee,arguments)};for(var id in CALLBACK.prototype)CALLBACK.prototype.hasOwnProperty(id)&&(cb[id]=void 0!==data[id]?data[id]:CALLBACK.prototype[id]);return cb.toString=CALLBACK.prototype.toString,cb};CALLBACK.prototype={isCallback:!0,hook:function(){},data:[],object:window,execute:function(){if(!this.called||this.autoReset)return this.called=!this.autoReset,this.hook.apply(this.object,this.data.concat([].slice.call(arguments,0)))},reset:function(){delete this.called},toString:function(){return this.hook.toString.apply(this.hook,arguments)}};var ISCALLBACK=function(f){return"function"==typeof f&&f.isCallback},EVAL=function(code){return eval.call(window,code)},TESTEVAL=function(){if(EVAL("var __TeSt_VaR__ = 1"),window.__TeSt_VaR__)try{delete window.__TeSt_VaR__}catch(error){window.__TeSt_VaR__=null}else EVAL=window.execScript?function(code){BASE.__code=code,code="try {MathJax.__result = eval(MathJax.__code)} catch(err) {MathJax.__result = err}",window.execScript(code);var result=BASE.__result;if(delete BASE.__result,delete BASE.__code,result instanceof Error)throw result;return result}:function(code){BASE.__code=code,code="try {MathJax.__result = eval(MathJax.__code)} catch(err) {MathJax.__result = err}";var head=document.getElementsByTagName("head")[0];head||(head=document.body);var script=document.createElement("script");script.appendChild(document.createTextNode(code)),head.appendChild(script),head.removeChild(script);var result=BASE.__result;if(delete BASE.__result,delete BASE.__code,result instanceof Error)throw result;return result};TESTEVAL=null},USING=function(args,i){if(arguments.length>1&&(args=2===arguments.length&&"function"!=typeof arguments[0]&&arguments[0]instanceof Object&&"number"==typeof arguments[1]?[].slice.call(args,i):[].slice.call(arguments,0)),isArray(args)&&1===args.length&&"function"==typeof args[0]&&(args=args[0]),"function"==typeof args)return args.execute===CALLBACK.prototype.execute?args:CALLBACK({hook:args});if(isArray(args)){if("string"==typeof args[0]&&args[1]instanceof Object&&"function"==typeof args[1][args[0]])return CALLBACK({hook:args[1][args[0]],object:args[1],data:args.slice(2)});if("function"==typeof args[0])return CALLBACK({hook:args[0],data:args.slice(1)});if("function"==typeof args[1])return CALLBACK({hook:args[1],object:args[0],data:args.slice(2)})}else{if("string"==typeof args)return TESTEVAL&&TESTEVAL(),CALLBACK({hook:EVAL,data:[args]});if(args instanceof Object)return CALLBACK(args);if(void 0===args)return CALLBACK({})}throw Error("Can't make callback from given data")},DELAY=function(time,callback){return(callback=USING(callback)).timeout=setTimeout(callback,time),callback},WAITFOR=function(callback,signal){(callback=USING(callback)).called||(WAITSIGNAL(callback,signal),signal.pending++)},WAITEXECUTE=function(){var signals=this.signal;delete this.signal,this.execute=this.oldExecute,delete this.oldExecute;var result=this.execute.apply(this,arguments);if(ISCALLBACK(result)&&!result.called)WAITSIGNAL(result,signals);else for(var i=0,m=signals.length;i<m;i++)signals[i].pending--,signals[i].pending<=0&&signals[i].call()},WAITSIGNAL=function(callback,signals){isArray(signals)||(signals=[signals]),callback.signal?1===signals.length?callback.signal.push(signals[0]):callback.signal=callback.signal.concat(signals):(callback.oldExecute=callback.execute,callback.execute=WAITEXECUTE,callback.signal=signals)},AFTER=function(callback){(callback=USING(callback)).pending=0;for(var i=1,m=arguments.length;i<m;i++)arguments[i]&&WAITFOR(arguments[i],callback);if(0===callback.pending){var result=callback();ISCALLBACK(result)&&(callback=result)}return callback},HOOKS=MathJax.Object.Subclass({Init:function(reset){this.hooks=[],this.remove=[],this.reset=reset,this.running=!1},Add:function(hook,priority){null==priority&&(priority=10),ISCALLBACK(hook)||(hook=USING(hook)),hook.priority=priority;for(var i=this.hooks.length;i>0&&priority<this.hooks[i-1].priority;)i--;return this.hooks.splice(i,0,hook),hook},Remove:function(hook){for(var i=0,m=this.hooks.length;i<m;i++)if(this.hooks[i]===hook)return void(this.running?this.remove.push(i):this.hooks.splice(i,1))},Execute:function(){var callbacks=[{}];this.running=!0;for(var i=0,m=this.hooks.length;i<m;i++){this.reset&&this.hooks[i].reset();var result=this.hooks[i].apply(window,arguments);ISCALLBACK(result)&&!result.called&&callbacks.push(result)}return this.running=!1,this.remove.length&&this.RemovePending(),1===callbacks.length?null:2===callbacks.length?callbacks[1]:AFTER.apply({},callbacks)},RemovePending:function(){this.remove=this.remove.sort();for(var i=this.remove.length-1;i>=0;i--)this.hooks.splice(i,1);this.remove=[]}}),EXECUTEHOOKS=function(hooks,data,reset){if(!hooks)return null;isArray(hooks)||(hooks=[hooks]),isArray(data)||(data=null==data?[]:[data]);for(var handler=HOOKS(reset),i=0,m=hooks.length;i<m;i++)handler.Add(hooks[i]);return handler.Execute.apply(handler,data)},QUEUE=BASE.Object.Subclass({Init:function(){this.pending=this.running=0,this.queue=[],this.Push.apply(this,arguments)},Push:function(){for(var callback,i=0,m=arguments.length;i<m;i++)(callback=USING(arguments[i]))!==arguments[i]||callback.called||(callback=USING(["wait",this,callback])),this.queue.push(callback);return this.running||this.pending||this.Process(),callback},Process:function(queue){for(;!this.running&&!this.pending&&this.queue.length;){var callback=this.queue[0];queue=this.queue.slice(1),this.queue=[],this.Suspend();var result=callback();this.Resume(),queue.length&&(this.queue=queue.concat(this.queue)),ISCALLBACK(result)&&!result.called&&WAITFOR(result,this)}},Suspend:function(){this.running++},Resume:function(){this.running&&this.running--},call:function(){this.Process.apply(this,arguments)},wait:function(callback){return callback}}),SIGNAL=QUEUE.Subclass({Init:function(name){QUEUE.prototype.Init.call(this),this.name=name,this.posted=[],this.listeners=HOOKS(!0),this.posting=!1,this.callback=null},Post:function(message,callback,forget){if(callback=USING(callback),this.posting||this.pending)this.Push(["Post",this,message,callback,forget]);else{this.callback=callback,callback.reset(),forget||this.posted.push(message),this.Suspend(),this.posting=!0;var result=this.listeners.Execute(message);ISCALLBACK(result)&&!result.called&&WAITFOR(result,this),this.Resume(),this.posting=!1,this.pending||this.call()}return callback},Clear:function(callback){return callback=USING(callback),this.posting||this.pending?callback=this.Push(["Clear",this,callback]):(this.posted=[],callback()),callback},call:function(){this.callback(this),this.Process()},Interest:function(callback,ignorePast,priority){if(callback=USING(callback),this.listeners.Add(callback,priority),!ignorePast)for(var i=0,m=this.posted.length;i<m;i++){callback.reset();var result=callback(this.posted[i]);ISCALLBACK(result)&&i===this.posted.length-1&&WAITFOR(result,this)}return callback},NoInterest:function(callback){this.listeners.Remove(callback)},MessageHook:function(msg,callback,priority){callback=USING(callback),this.hooks||(this.hooks={},this.Interest(["ExecuteHooks",this])),this.hooks[msg]||(this.hooks[msg]=HOOKS(!0)),this.hooks[msg].Add(callback,priority);for(var i=0,m=this.posted.length;i<m;i++)this.posted[i]==msg&&(callback.reset(),callback(this.posted[i]));return callback.msg=msg,callback},ExecuteHooks:function(msg){var type=isArray(msg)?msg[0]:msg;return this.hooks[type]?this.hooks[type].Execute(msg):null},RemoveHook:function(hook){this.hooks[hook.msg].Remove(hook)}},{signals:{},find:function(name){return SIGNAL.signals[name]||(SIGNAL.signals[name]=new SIGNAL(name)),SIGNAL.signals[name]}});BASE.Callback=BASE.CallBack=USING,BASE.Callback.Delay=DELAY,BASE.Callback.After=AFTER,BASE.Callback.Queue=QUEUE,BASE.Callback.Signal=SIGNAL.find,BASE.Callback.Hooks=HOOKS,BASE.Callback.ExecuteHooks=EXECUTEHOOKS}("MathJax"),function(BASENAME){var BASE=window.MathJax;BASE||(BASE=window.MathJax={});var isSafari2="Apple Computer, Inc."===navigator.vendor&&void 0===navigator.vendorSub,sheets=0,HEAD=function(head){return document.styleSheets&&document.styleSheets.length>sheets&&(sheets=document.styleSheets.length),head||(head=document.head||document.getElementsByTagName("head")[0])||(head=document.body),head},SCRIPTS=[],REMOVESCRIPTS=function(){for(var i=0,m=SCRIPTS.length;i<m;i++)BASE.Ajax.head.removeChild(SCRIPTS[i]);SCRIPTS=[]},PATH={MathJax:"",a11y:"[MathJax]/extensions/a11y",Contrib:"https://cdn.mathjax.org/mathjax/contrib"};BASE.Ajax={loaded:{},loading:{},loadHooks:{},timeout:15e3,styleDelay:1,config:{root:"",path:PATH},params:{},STATUS:{OK:1,ERROR:-1},fileURL:function(file){for(var match;(match=file.match(/^\[([-._a-z0-9]+)\]/i))&&PATH.hasOwnProperty(match[1]);)file=(PATH[match[1]]||this.config.root)+file.substr(match[1].length+2);return file},fileName:function(url){var root=this.config.root;url.substr(0,root.length)===root&&(url="[MathJax]"+url.substr(root.length));do{var recheck=!1;for(var id in PATH)if(PATH.hasOwnProperty(id)&&PATH[id]&&url.substr(0,PATH[id].length)===PATH[id]){url="["+id+"]"+url.substr(PATH[id].length),recheck=!0;break}}while(recheck);return url},fileRev:function(file){var V=BASE.cdnFileVersions[file]||BASE.cdnVersion||"";return V&&(V="?V="+V),V},urlRev:function(file){return this.fileURL(file)+this.fileRev(file)},Require:function(file,callback){var type;if(callback=BASE.Callback(callback),file instanceof Object)for(var i in file)file.hasOwnProperty(i)&&(type=i.toUpperCase(),file=file[i]);else type=file.split(/\./).pop().toUpperCase();if(this.params.noContrib&&"[Contrib]"===file.substr(0,9))callback(this.STATUS.ERROR);else if(file=this.fileURL(file),this.loaded[file])callback(this.loaded[file]);else{var FILE={};FILE[type]=file,this.Load(FILE,callback)}return callback},Load:function(file,callback){var type;if(callback=BASE.Callback(callback),file instanceof Object)for(var i in file)file.hasOwnProperty(i)&&(type=i.toUpperCase(),file=file[i]);else type=file.split(/\./).pop().toUpperCase();if(file=this.fileURL(file),this.loading[file])this.addHook(file,callback);else{if(this.head=HEAD(this.head),!this.loader[type])throw Error("Can't load files of type "+type);this.loader[type].call(this,file,callback)}return callback},LoadHook:function(file,callback,priority){if(callback=BASE.Callback(callback),file instanceof Object)for(var i in file)file.hasOwnProperty(i)&&(file=file[i]);return file=this.fileURL(file),this.loaded[file]?callback(this.loaded[file]):this.addHook(file,callback,priority),callback},addHook:function(file,callback,priority){this.loadHooks[file]||(this.loadHooks[file]=MathJax.Callback.Hooks()),this.loadHooks[file].Add(callback,priority),callback.file=file},removeHook:function(hook){this.loadHooks[hook.file]&&(this.loadHooks[hook.file].Remove(hook),this.loadHooks[hook.file].hooks.length||delete this.loadHooks[hook.file])},Preloading:function(){for(var i=0,m=arguments.length;i<m;i++){var file=this.fileURL(arguments[i]);this.loading[file]||(this.loading[file]={preloaded:!0})}},loader:{JS:function(file,callback){var name=this.fileName(file),script=document.createElement("script"),timeout=BASE.Callback(["loadTimeout",this,file]);this.loading[file]={callback:callback,timeout:setTimeout(timeout,this.timeout),status:this.STATUS.OK,script:script},this.loading[file].message=BASE.Message.File(name),script.onerror=timeout,script.type="text/javascript",script.src=file+this.fileRev(name),this.head.appendChild(script)},CSS:function(file,callback){var name=this.fileName(file),link=document.createElement("link");link.rel="stylesheet",link.type="text/css",link.href=file+this.fileRev(name),this.loading[file]={callback:callback,message:BASE.Message.File(name),status:this.STATUS.OK},this.head.appendChild(link),this.timer.create.call(this,[this.timer.file,file],link)}},timer:{create:function(callback,node){return callback=BASE.Callback(callback),"STYLE"===node.nodeName&&node.styleSheet&&void 0!==node.styleSheet.cssText?callback(this.STATUS.OK):window.chrome&&"LINK"===node.nodeName?callback(this.STATUS.OK):isSafari2?this.timer.start(this,[this.timer.checkSafari2,sheets++,callback],this.styleDelay):this.timer.start(this,[this.timer.checkLength,node,callback],this.styleDelay),callback},start:function(AJAX,check,delay,timeout){(check=BASE.Callback(check)).execute=this.execute,check.time=this.time,check.STATUS=AJAX.STATUS,check.timeout=timeout||AJAX.timeout,check.delay=check.total=delay||0,delay?setTimeout(check,delay):check()},time:function(callback){return this.total+=this.delay,this.delay=Math.floor(1.05*this.delay+5),this.total>=this.timeout?(callback(this.STATUS.ERROR),1):0},file:function(file,status){status<0?BASE.Ajax.loadTimeout(file):BASE.Ajax.loadComplete(file)},execute:function(){this.hook.call(this.object,this,this.data[0],this.data[1])},checkSafari2:function(check,length,callback){check.time(callback)||(document.styleSheets.length>length&&document.styleSheets[length].cssRules&&document.styleSheets[length].cssRules.length?callback(check.STATUS.OK):setTimeout(check,check.delay))},checkLength:function(check,node,callback){if(!check.time(callback)){var isStyle=0,sheet=node.sheet||node.styleSheet;try{(sheet.cssRules||sheet.rules||[]).length>0&&(isStyle=1)}catch(err){err.message.match(/protected variable|restricted URI/)?isStyle=1:err.message.match(/Security error/)&&(isStyle=1)}isStyle?setTimeout(BASE.Callback([callback,check.STATUS.OK]),0):setTimeout(check,check.delay)}}},loadComplete:function(file){file=this.fileURL(file);var loading=this.loading[file];return loading&&!loading.preloaded?(BASE.Message.Clear(loading.message),clearTimeout(loading.timeout),loading.script&&(0===SCRIPTS.length&&setTimeout(REMOVESCRIPTS,0),SCRIPTS.push(loading.script)),this.loaded[file]=loading.status,delete this.loading[file],this.addHook(file,loading.callback)):(loading&&delete this.loading[file],this.loaded[file]=this.STATUS.OK,loading={status:this.STATUS.OK}),this.loadHooks[file]?this.loadHooks[file].Execute(loading.status):null},loadTimeout:function(file){this.loading[file].timeout&&clearTimeout(this.loading[file].timeout),this.loading[file].status=this.STATUS.ERROR,this.loadError(file),this.loadComplete(file)},loadError:function(file){BASE.Message.Set(["LoadFailed","File failed to load: %1",file],null,2e3),BASE.Hub.signal.Post(["file load error",file])},Styles:function(styles,callback){var styleString=this.StyleString(styles);if(""===styleString)(callback=BASE.Callback(callback))();else{var style=document.createElement("style");style.type="text/css",this.head=HEAD(this.head),this.head.appendChild(style),style.styleSheet&&void 0!==style.styleSheet.cssText?style.styleSheet.cssText=styleString:style.appendChild(document.createTextNode(styleString)),callback=this.timer.create.call(this,callback,style)}return callback},StyleString:function(styles){if("string"==typeof styles)return styles;var string="",id,style;for(id in styles)if(styles.hasOwnProperty(id))if("string"==typeof styles[id])string+=id+" {"+styles[id]+"}\n";else if(BASE.Object.isArray(styles[id]))for(var i=0;i<styles[id].length;i++)(style={})[id]=styles[id][i],string+=this.StyleString(style);else if("@media"===id.substr(0,6))string+=id+" {"+this.StyleString(styles[id])+"}\n";else if(null!=styles[id]){for(var name in style=[],styles[id])styles[id].hasOwnProperty(name)&&null!=styles[id][name]&&(style[style.length]=name+": "+styles[id][name]);string+=id+" {"+style.join("; ")+"}\n"}return string}}}("MathJax"),MathJax.HTML={Element:function(type,def,contents){var obj=document.createElement(type),id;if(def){if(def.hasOwnProperty("style")){var style=def.style;for(id in def.style={},style)style.hasOwnProperty(id)&&(def.style[id.replace(/-([a-z])/g,this.ucMatch)]=style[id])}for(id in MathJax.Hub.Insert(obj,def),def)"role"!==id&&"aria-"!==id.substr(0,5)||obj.setAttribute(id,def[id])}if(contents){MathJax.Object.isArray(contents)||(contents=[contents]);for(var i=0,m=contents.length;i<m;i++)MathJax.Object.isArray(contents[i])?obj.appendChild(this.Element(contents[i][0],contents[i][1],contents[i][2])):"script"===type?this.setScript(obj,contents[i]):obj.appendChild(document.createTextNode(contents[i]))}return obj},ucMatch:function(match,c){return c.toUpperCase()},addElement:function(span,type,def,contents){return span.appendChild(this.Element(type,def,contents))},TextNode:function(text){return document.createTextNode(text)},addText:function(span,text){return span.appendChild(this.TextNode(text))},setScript:function(script,text){if(this.setScriptBug)script.text=text;else{for(;script.firstChild;)script.removeChild(script.firstChild);this.addText(script,text)}},getScript:function(script){var text;return(""===script.text?script.innerHTML:script.text).replace(/^\s+/,"").replace(/\s+$/,"")},Cookie:{prefix:"mjx",expires:365,Set:function(name,def){var keys=[];if(def)for(var id in def)def.hasOwnProperty(id)&&keys.push(id+":"+def[id].toString().replace(/&/g,"&&"));var cookie=this.prefix+"."+name+"="+escape(keys.join("&;"));if(this.expires){var time=new Date;time.setDate(time.getDate()+this.expires),cookie+="; expires="+time.toGMTString()}try{document.cookie=cookie+"; path=/"}catch(err){}},Get:function(name,obj){obj||(obj={});var pattern=new RegExp("(?:^|;\\s*)"+this.prefix+"\\."+name+"=([^;]*)(?:;|$)"),match;try{match=pattern.exec(document.cookie)}catch(err){}if(match&&""!==match[1])for(var keys=unescape(match[1]).split("&;"),i=0,m=keys.length;i<m;i++){var value=(match=keys[i].match(/([^:]+):(.*)/))[2].replace(/&&/g,"&");"true"===value?value=!0:"false"===value?value=!1:value.match(/^-?(\d+(\.\d+)?|\.\d+)$/)&&(value=parseFloat(value)),obj[match[1]]=value}return obj}}},MathJax.Localization={locale:"en",directory:"[MathJax]/localization",strings:{ar:{menuTitle:"العربية"},ast:{menuTitle:"asturianu"},bg:{menuTitle:"български"},bcc:{menuTitle:"بلوچی"},br:{menuTitle:"brezhoneg"},ca:{menuTitle:"català"},cdo:{menuTitle:"Mìng-dĕ̤ng-ngṳ̄"},cs:{menuTitle:"čeština"},da:{menuTitle:"dansk"},de:{menuTitle:"Deutsch"},diq:{menuTitle:"Zazaki"},en:{menuTitle:"English",isLoaded:!0},eo:{menuTitle:"Esperanto"},es:{menuTitle:"español"},fa:{menuTitle:"فارسی"},fi:{menuTitle:"suomi"},fr:{menuTitle:"français"},gl:{menuTitle:"galego"},he:{menuTitle:"עברית"},ia:{menuTitle:"interlingua"},it:{menuTitle:"italiano"},ja:{menuTitle:"日本語"},kn:{menuTitle:"ಕನ್ನಡ"},ko:{menuTitle:"한국어"},lb:{menuTitle:"Lëtzebuergesch"},lki:{menuTitle:"لەکی"},lt:{menuTitle:"lietuvių"},mk:{menuTitle:"македонски"},nl:{menuTitle:"Nederlands"},oc:{menuTitle:"occitan"},pl:{menuTitle:"polski"},pt:{menuTitle:"português"},"pt-br":{menuTitle:"português do Brasil"},ru:{menuTitle:"русский"},sco:{menuTitle:"Scots"},scn:{menuTitle:"sicilianu"},sk:{menuTitle:"slovenčina"},sl:{menuTitle:"slovenščina"},sv:{menuTitle:"svenska"},th:{menuTitle:"ไทย"},tr:{menuTitle:"Türkçe"},uk:{menuTitle:"українська"},vi:{menuTitle:"Tiếng Việt"},"zh-hans":{menuTitle:"中文（简体）"},"zh-hant":{menuTitle:"汉语"}},pattern:/%(\d+|\{\d+\}|\{[a-z]+:\%\d+(?:\|(?:%\{\d+\}|%.|[^\}])*)+\}|.)/g,SPLIT:3==="axb".split(/(x)/).length?function(string,regex){return string.split(regex)}:function(string,regex){var result=[],match,last=0;for(regex.lastIndex=0;match=regex.exec(string);)result.push(string.substr(last,match.index-last)),result.push.apply(result,match.slice(1)),last=match.index+match[0].length;return result.push(string.substr(last)),result},_:function(id,phrase){return MathJax.Object.isArray(phrase)?this.processSnippet(id,phrase):this.processString(this.lookupPhrase(id,phrase),[].slice.call(arguments,2))},processString:function(string,args,domain){var i,m,isArray=MathJax.Object.isArray;for(i=0,m=args.length;i<m;i++)domain&&isArray(args[i])&&(args[i]=this.processSnippet(domain,args[i]));var parts=this.SPLIT(string,this.pattern);for(i=1,m=parts.length;i<m;i+=2){var c=parts[i].charAt(0);if(c>="0"&&c<="9")parts[i]=args[parts[i]-1],"number"==typeof parts[i]&&(parts[i]=this.number(parts[i]));else if("{"===c)if((c=parts[i].substr(1))>="0"&&c<="9")parts[i]=args[parts[i].substr(1,parts[i].length-2)-1],"number"==typeof parts[i]&&(parts[i]=this.number(parts[i]));else{var match=parts[i].match(/^\{([a-z]+):%(\d+)\|(.*)\}$/);if(match)if("plural"===match[1]){var n=args[match[2]-1];if(void 0===n)parts[i]="???";else{n=this.plural(n)-1;var plurals=match[3].replace(/(^|[^%])(%%)*%\|/g,"$1$2%").split(/\|/);n>=0&&n<plurals.length?parts[i]=this.processString(plurals[n].replace(/\uEFEF/g,"|"),args,domain):parts[i]="???"}}else parts[i]="%"+parts[i]}null==parts[i]&&(parts[i]="???")}if(!domain)return parts.join("");var snippet=[],part="";for(i=0;i<m;i++)part+=parts[i],++i<m&&(isArray(parts[i])?(snippet.push(part),snippet=snippet.concat(parts[i]),part=""):part+=parts[i]);return""!==part&&snippet.push(part),snippet},processSnippet:function(domain,snippet){for(var result=[],i=0,m=snippet.length;i<m;i++)if(MathJax.Object.isArray(snippet[i])){var data=snippet[i];if("string"==typeof data[1]){var id=data[0];MathJax.Object.isArray(id)||(id=[domain,id]);var phrase=this.lookupPhrase(id,data[1]);result=result.concat(this.processMarkdown(phrase,data.slice(2),domain))}else MathJax.Object.isArray(data[1])?result=result.concat(this.processSnippet.apply(this,data)):data.length>=3?result.push([data[0],data[1],this.processSnippet(domain,data[2])]):result.push(snippet[i])}else result.push(snippet[i]);return result},markdownPattern:/(%.)|(\*{1,3})((?:%.|.)+?)\2|(`+)((?:%.|.)+?)\4|\[((?:%.|.)+?)\]\(([^\s\)]+)\)/,processMarkdown:function(phrase,args,domain){for(var result=[],data,parts=phrase.split(this.markdownPattern),string=parts[0],i=1,m=parts.length;i<m;i+=8)parts[i+1]?(data=this.processString(parts[i+2],args,domain),MathJax.Object.isArray(data)||(data=[data]),data=[["b","i","i"][parts[i+1].length-1],{},data],3===parts[i+1].length&&(data=["b",{},data])):parts[i+3]?(data=this.processString(parts[i+4].replace(/^\s/,"").replace(/\s$/,""),args,domain),MathJax.Object.isArray(data)||(data=[data]),data=["code",{},data]):parts[i+5]?(data=this.processString(parts[i+5],args,domain),MathJax.Object.isArray(data)||(data=[data]),data=["a",{href:this.processString(parts[i+6],args),target:"_blank"},data]):(string+=parts[i],data=null),data&&((result=this.concatString(result,string,args,domain)).push(data),string=""),""!==parts[i+7]&&(string+=parts[i+7]);return result=this.concatString(result,string,args,domain)},concatString:function(result,string,args,domain){return""!=string&&(string=this.processString(string,args,domain),MathJax.Object.isArray(string)||(string=[string]),result=result.concat(string)),result},lookupPhrase:function(id,phrase,domain){domain||(domain="_"),MathJax.Object.isArray(id)&&(domain=id[0]||"_",id=id[1]||"");var load=this.loadDomain(domain);load&&MathJax.Hub.RestartAfter(load);var localeData=this.strings[this.locale];if(localeData&&localeData.domains&&domain in localeData.domains){var domainData=localeData.domains[domain];domainData.strings&&id in domainData.strings&&(phrase=domainData.strings[id])}return phrase},loadFile:function(file,data,callback){var dir;(callback=MathJax.Callback(callback),(file=data.file||file).match(/\.js$/)||(file+=".js"),file.match(/^([a-z]+:|\[MathJax\])/))||(file=(this.strings[this.locale].directory||this.directory+"/"+this.locale||"[MathJax]/localization/"+this.locale)+"/"+file);var load=MathJax.Ajax.Require(file,(function(){return data.isLoaded=!0,callback()}));return load.called?null:load},loadDomain:function(domain,callback){var load,localeData=this.strings[this.locale];if(localeData){if(!localeData.isLoaded&&(load=this.loadFile(this.locale,localeData)))return MathJax.Callback.Queue(load,["loadDomain",this,domain]).Push(callback||{});if(localeData.domains&&domain in localeData.domains){var domainData=localeData.domains[domain];if(!domainData.isLoaded&&(load=this.loadFile(domain,domainData)))return MathJax.Callback.Queue(load).Push(callback)}}return MathJax.Callback(callback)()},Try:function(fn){(fn=MathJax.Callback(fn)).autoReset=!0;try{fn()}catch(err){if(!err.restart)throw err;MathJax.Callback.After(["Try",this,fn],err.restart)}},resetLocale:function(locale){if(locale){for(locale=locale.toLowerCase();!this.strings[locale];){var dashPos=locale.lastIndexOf("-");if(-1===dashPos)return;locale=locale.substring(0,dashPos)}var remap=this.strings[locale].remap;this.locale=remap||locale,MathJax.Callback.Signal("Hub").Post(["Locale Reset",this.locale])}},setLocale:function(locale){this.resetLocale(locale),MathJax.Menu&&this.loadDomain("MathMenu")},addTranslation:function(locale,domain,definition){var data=this.strings[locale],isNew=!1;data||(data=this.strings[locale]={},isNew=!0),data.domains||(data.domains={}),domain&&(data.domains[domain]||(data.domains[domain]={}),data=data.domains[domain]),MathJax.Hub.Insert(data,definition),isNew&&MathJax.Menu.menu&&MathJax.Menu.CreateLocaleMenu()},setCSS:function(div){var locale=this.strings[this.locale];return locale&&(locale.fontFamily&&(div.style.fontFamily=locale.fontFamily),locale.fontDirection&&(div.style.direction=locale.fontDirection,"rtl"===locale.fontDirection&&(div.style.textAlign="right"))),div},fontFamily:function(){var locale=this.strings[this.locale];return locale?locale.fontFamily:null},fontDirection:function(){var locale=this.strings[this.locale];return locale?locale.fontDirection:null},plural:function(n){var locale=this.strings[this.locale];return locale&&locale.plural?locale.plural(n):1==n?1:2},number:function(n){var locale=this.strings[this.locale];return locale&&locale.number?locale.number(n):n}},MathJax.Message={ready:!1,log:[{}],current:null,textNodeBug:"Apple Computer, Inc."===navigator.vendor&&void 0===navigator.vendorSub||window.hasOwnProperty&&window.hasOwnProperty("konqueror"),styles:{"#MathJax_Message":{position:"fixed",left:"1px",bottom:"2px","background-color":"#E6E6E6",border:"1px solid #959595",margin:"0px",padding:"2px 8px","z-index":"102",color:"black","font-size":"80%",width:"auto","white-space":"nowrap"},"#MathJax_MSIE_Frame":{position:"absolute",top:0,left:0,width:"0px","z-index":101,border:"0px",margin:"0px",padding:"0px"}},browsers:{MSIE:function(browser){MathJax.Message.msieFixedPositionBug=(document.documentMode||0)<7,MathJax.Message.msieFixedPositionBug&&(MathJax.Hub.config.styles["#MathJax_Message"].position="absolute"),MathJax.Message.quirks="BackCompat"===document.compatMode},Chrome:function(browser){MathJax.Hub.config.styles["#MathJax_Message"].bottom="1.5em",MathJax.Hub.config.styles["#MathJax_Message"].left="1em"}},Init:function(styles){if(styles&&(this.ready=!0),!document.body||!this.ready)return!1;if(this.div&&null==this.div.parentNode&&(this.div=document.getElementById("MathJax_Message"),this.text=this.div?this.div.firstChild:null),!this.div){var frame=document.body;this.msieFixedPositionBug&&window.attachEvent&&((frame=this.frame=this.addDiv(document.body)).removeAttribute("id"),frame.style.position="absolute",frame.style.border=frame.style.margin=frame.style.padding="0px",frame.style.zIndex="101",frame.style.height="0px",(frame=this.addDiv(frame)).id="MathJax_MSIE_Frame",window.attachEvent("onscroll",this.MoveFrame),window.attachEvent("onresize",this.MoveFrame),this.MoveFrame()),this.div=this.addDiv(frame),this.div.style.display="none"}return this.text||(this.text=this.div.appendChild(document.createTextNode(""))),!0},addDiv:function(parent){var div=document.createElement("div");return div.id="MathJax_Message",parent.firstChild?parent.insertBefore(div,parent.firstChild):parent.appendChild(div),div},MoveFrame:function(){var body=MathJax.Message.quirks?document.body:document.documentElement,frame=MathJax.Message.frame;frame.style.left=body.scrollLeft+"px",frame.style.top=body.scrollTop+"px",frame.style.width=body.clientWidth+"px",(frame=frame.firstChild).style.height=body.clientHeight+"px"},localize:function(message){return MathJax.Localization._(message,message)},filterText:function(text,n,id){return"simple"===MathJax.Hub.config.messageStyle&&("LoadFile"===id?(this.loading||(this.loading=this.localize("Loading")+" "),text=this.loading,this.loading+="."):"ProcessMath"===id?(this.processing||(this.processing=this.localize("Processing")+" "),text=this.processing,this.processing+="."):"TypesetMath"===id&&(this.typesetting||(this.typesetting=this.localize("Typesetting")+" "),text=this.typesetting,this.typesetting+=".")),text},clearCounts:function(){delete this.loading,delete this.processing,delete this.typesetting},Set:function(text,n,clearDelay){null==n&&(n=this.log.length,this.log[n]={});var id="";if(MathJax.Object.isArray(text)){id=text[0],MathJax.Object.isArray(id)&&(id=id[1]);try{text=MathJax.Localization._.apply(MathJax.Localization,text)}catch(err){if(!err.restart)throw err;if(!err.restart.called)return null==this.log[n].restarted&&(this.log[n].restarted=0),this.log[n].restarted++,delete this.log[n].cleared,MathJax.Callback.After(["Set",this,text,n,clearDelay],err.restart),n}}return this.timer&&(clearTimeout(this.timer),delete this.timer),this.log[n].text=text,this.log[n].filteredText=text=this.filterText(text,n,id),void 0===this.log[n].next&&(this.log[n].next=this.current,null!=this.current&&(this.log[this.current].prev=n),this.current=n),this.current===n&&"none"!==MathJax.Hub.config.messageStyle&&(this.Init()?(this.textNodeBug?this.div.innerHTML=text:this.text.nodeValue=text,this.div.style.display="",this.status&&(window.status="",delete this.status)):(window.status=text,this.status=!0)),this.log[n].restarted&&(this.log[n].cleared&&(clearDelay=0),0==--this.log[n].restarted&&delete this.log[n].cleared),clearDelay?setTimeout(MathJax.Callback(["Clear",this,n]),clearDelay):0==clearDelay&&this.Clear(n,0),n},Clear:function(n,delay){null!=this.log[n].prev&&(this.log[this.log[n].prev].next=this.log[n].next),null!=this.log[n].next&&(this.log[this.log[n].next].prev=this.log[n].prev),this.current===n&&(this.current=this.log[n].next,this.text?(null==this.div.parentNode&&this.Init(),null==this.current?(this.timer&&(clearTimeout(this.timer),delete this.timer),null==delay&&(delay=600),0===delay?this.Remove():this.timer=setTimeout(MathJax.Callback(["Remove",this]),delay)):"none"!==MathJax.Hub.config.messageStyle&&(this.textNodeBug?this.div.innerHTML=this.log[this.current].filteredText:this.text.nodeValue=this.log[this.current].filteredText),this.status&&(window.status="",delete this.status)):this.status&&(window.status=null==this.current?"":this.log[this.current].text)),delete this.log[n].next,delete this.log[n].prev,delete this.log[n].filteredText,this.log[n].restarted&&(this.log[n].cleared=!0)},Remove:function(){this.text.nodeValue="",this.div.style.display="none"},File:function(file){return this.Set(["LoadFile","Loading %1",file],null,null)},Log:function(){for(var strings=[],i=1,m=this.log.length;i<m;i++)strings[i]=this.log[i].text;return strings.join("\n")}},MathJax.Hub={config:{root:"",config:[],styleSheets:[],styles:{".MathJax_Preview":{color:"#888"}},jax:[],extensions:[],preJax:null,postJax:null,displayAlign:"center",displayIndent:"0",preRemoveClass:"MathJax_Preview",showProcessingMessages:!0,messageStyle:"normal",delayStartupUntil:"none",skipStartupTypeset:!1,elements:[],positionToHash:!0,showMathMenu:!0,showMathMenuMSIE:!0,menuSettings:{zoom:"None",CTRL:!1,ALT:!1,CMD:!1,Shift:!1,discoverable:!1,zscale:"200%",renderer:null,font:"Auto",context:"MathJax",locale:null,mpContext:!1,mpMouse:!1,texHints:!0,FastPreview:null,assistiveMML:null,inTabOrder:!0,semantics:!1},errorSettings:{message:["[",["MathProcessingError","Math Processing Error"],"]"],style:{color:"#CC0000","font-style":"italic"}},ignoreMMLattributes:{}},preProcessors:MathJax.Callback.Hooks(!0),inputJax:{},outputJax:{order:{}},processSectionDelay:50,processUpdateTime:250,processUpdateDelay:10,signal:MathJax.Callback.Signal("Hub"),Config:function(def){this.Insert(this.config,def),this.config.Augment&&this.Augment(this.config.Augment)},CombineConfig:function(name,def){for(var config=this.config,id,parent,i=0,m=(name=name.split(/\./)).length;i<m;i++)config[id=name[i]]||(config[id]={}),parent=config,config=config[id];return parent[id]=config=this.Insert(def,config),config},Register:{PreProcessor:function(){return MathJax.Hub.preProcessors.Add.apply(MathJax.Hub.preProcessors,arguments)},MessageHook:function(){return MathJax.Hub.signal.MessageHook.apply(MathJax.Hub.signal,arguments)},StartupHook:function(){return MathJax.Hub.Startup.signal.MessageHook.apply(MathJax.Hub.Startup.signal,arguments)},LoadHook:function(){return MathJax.Ajax.LoadHook.apply(MathJax.Ajax,arguments)}},UnRegister:{PreProcessor:function(hook){MathJax.Hub.preProcessors.Remove(hook)},MessageHook:function(hook){MathJax.Hub.signal.RemoveHook(hook)},StartupHook:function(hook){MathJax.Hub.Startup.signal.RemoveHook(hook)},LoadHook:function(hook){MathJax.Ajax.removeHook(hook)}},getAllJax:function(element){for(var jax=[],scripts=this.elementScripts(element),i=0,m=scripts.length;i<m;i++)scripts[i].MathJax&&scripts[i].MathJax.elementJax&&jax.push(scripts[i].MathJax.elementJax);return jax},getJaxByType:function(type,element){for(var jax=[],scripts=this.elementScripts(element),i=0,m=scripts.length;i<m;i++)scripts[i].MathJax&&scripts[i].MathJax.elementJax&&scripts[i].MathJax.elementJax.mimeType===type&&jax.push(scripts[i].MathJax.elementJax);return jax},getJaxByInputType:function(type,element){for(var jax=[],scripts=this.elementScripts(element),i=0,m=scripts.length;i<m;i++)scripts[i].MathJax&&scripts[i].MathJax.elementJax&&scripts[i].type&&scripts[i].type.replace(/ *;(.|\s)*/,"")===type&&jax.push(scripts[i].MathJax.elementJax);return jax},getJaxFor:function(element){if("string"==typeof element&&(element=document.getElementById(element)),element&&element.MathJax)return element.MathJax.elementJax;if(this.isMathJaxNode(element)){for(element.isMathJax||(element=element.firstChild);element&&!element.jaxID;)element=element.parentNode;if(element)return MathJax.OutputJax[element.jaxID].getJaxFromMath(element)}return null},isJax:function(element){if("string"==typeof element&&(element=document.getElementById(element)),this.isMathJaxNode(element))return 1;if(element&&"script"===(element.tagName||"").toLowerCase()){if(element.MathJax)return element.MathJax.state===MathJax.ElementJax.STATE.PROCESSED?1:-1;if(element.type&&this.inputJax[element.type.replace(/ *;(.|\s)*/,"")])return-1}return 0},isMathJaxNode:function(element){return!!element&&(element.isMathJax||"MathJax_MathML"===(element.className||""))},setRenderer:function(renderer,type){if(renderer){var JAX=MathJax.OutputJax[renderer];if(JAX){this.config.menuSettings.renderer=renderer,null==type&&(type="jax/mml"),JAX.isUnknown&&JAX.Register(type);var jax=this.outputJax;return jax[type]&&jax[type].length&&renderer!==jax[type][0].id?(jax[type].unshift(JAX),this.signal.Post(["Renderer Selected",renderer])):null}MathJax.OutputJax[renderer]=MathJax.OutputJax({id:"unknown",version:"1.0.0",isUnknown:!0}),this.config.menuSettings.renderer="";var file="[MathJax]/jax/output/"+renderer+"/config.js";return MathJax.Ajax.Require(file,["setRenderer",this,renderer,type])}},Queue:function(){return this.queue.Push.apply(this.queue,arguments)},Typeset:function(element,callback){if(!MathJax.isReady)return null;var ec=this.elementCallback(element,callback);if(ec.count)var queue=MathJax.Callback.Queue(["PreProcess",this,ec.elements],["Process",this,ec.elements]);return queue.Push(ec.callback)},PreProcess:function(element,callback){var ec=this.elementCallback(element,callback),queue=MathJax.Callback.Queue();if(ec.count){var elements=1===ec.count?[ec.elements]:ec.elements;queue.Push(["Post",this.signal,["Begin PreProcess",ec.elements]]);for(var i=0,m=elements.length;i<m;i++)elements[i]&&queue.Push(["Execute",this.preProcessors,elements[i]]);queue.Push(["Post",this.signal,["End PreProcess",ec.elements]])}return queue.Push(ec.callback)},Process:function(element,callback){return this.takeAction("Process",element,callback)},Update:function(element,callback){return this.takeAction("Update",element,callback)},Reprocess:function(element,callback){return this.takeAction("Reprocess",element,callback)},Rerender:function(element,callback){return this.takeAction("Rerender",element,callback)},takeAction:function(action,element,callback){var ec=this.elementCallback(element,callback),elements=ec.elements,queue=MathJax.Callback.Queue(["Clear",this.signal]),state={scripts:[],start:(new Date).getTime(),i:0,j:0,jax:{},jaxIDs:[]};if(ec.count){var delay=["Delay",MathJax.Callback,this.processSectionDelay];delay[2]||(delay={}),queue.Push(["clearCounts",MathJax.Message],["Post",this.signal,["Begin "+action,elements]],["Post",this.signal,["Begin Math",elements,action]],["prepareScripts",this,action,elements,state],["Post",this.signal,["Begin Math Input",elements,action]],["processInput",this,state],["Post",this.signal,["End Math Input",elements,action]],delay,["prepareOutput",this,state,"preProcess"],delay,["Post",this.signal,["Begin Math Output",elements,action]],["processOutput",this,state],["Post",this.signal,["End Math Output",elements,action]],delay,["prepareOutput",this,state,"postProcess"],delay,["Post",this.signal,["End Math",elements,action]],["Post",this.signal,["End "+action,elements]],["clearCounts",MathJax.Message])}return queue.Push(ec.callback)},scriptAction:{Process:function(script){},Update:function(script){var jax=script.MathJax.elementJax;jax&&jax.needsUpdate()?(jax.Remove(!0),script.MathJax.state=jax.STATE.UPDATE):script.MathJax.state=jax.STATE.PROCESSED},Reprocess:function(script){var jax=script.MathJax.elementJax;jax&&(jax.Remove(!0),script.MathJax.state=jax.STATE.UPDATE)},Rerender:function(script){var jax=script.MathJax.elementJax;jax&&(jax.Remove(!0),script.MathJax.state=jax.STATE.OUTPUT)}},prepareScripts:function(action,element,state){if(!arguments.callee.disabled)for(var scripts=this.elementScripts(element),STATE=MathJax.ElementJax.STATE,i=0,m=scripts.length;i<m;i++){var script=scripts[i];script.type&&this.inputJax[script.type.replace(/ *;(.|\n)*/,"")]&&(script.MathJax&&(script.MathJax.elementJax&&script.MathJax.elementJax.hover&&MathJax.Extension.MathEvents.Hover.ClearHover(script.MathJax.elementJax),script.MathJax.state!==STATE.PENDING&&this.scriptAction[action](script)),script.MathJax||(script.MathJax={state:STATE.PENDING}),script.MathJax.error&&delete script.MathJax.error,script.MathJax.state!==STATE.PROCESSED&&state.scripts.push(script))}},checkScriptSiblings:function(script){if(!script.MathJax.checked){var config=this.config,pre=script.previousSibling;if(pre&&"#text"===pre.nodeName){var preJax,postJax,post=script.nextSibling;post&&"#text"!==post.nodeName&&(post=null),config.preJax&&("string"==typeof config.preJax&&(config.preJax=new RegExp(config.preJax+"$")),preJax=pre.nodeValue.match(config.preJax)),config.postJax&&post&&("string"==typeof config.postJax&&(config.postJax=new RegExp("^"+config.postJax)),postJax=post.nodeValue.match(config.postJax)),!preJax||config.postJax&&!postJax||(pre.nodeValue=pre.nodeValue.replace(config.preJax,preJax.length>1?preJax[1]:""),pre=null),!postJax||config.preJax&&!preJax||(post.nodeValue=post.nodeValue.replace(config.postJax,postJax.length>1?postJax[1]:"")),pre&&!pre.nodeValue.match(/\S/)&&(pre=pre.previousSibling)}config.preRemoveClass&&pre&&pre.className===config.preRemoveClass&&(script.MathJax.preview=pre),script.MathJax.checked=1}},processInput:function(state){var jax,STATE=MathJax.ElementJax.STATE,script,prev,m=state.scripts.length;try{for(;state.i<m;)if(script=state.scripts[state.i])if((prev=script.previousSibling)&&"MathJax_Error"===prev.className&&prev.parentNode.removeChild(prev),script.parentNode&&script.MathJax&&script.MathJax.state!==STATE.PROCESSED){if(script.MathJax.elementJax&&script.MathJax.state!==STATE.UPDATE)script.MathJax.state===STATE.OUTPUT&&this.saveScript(script.MathJax.elementJax,state,script,STATE);else{this.checkScriptSiblings(script);var type=script.type.replace(/ *;(.|\s)*/,""),input=this.inputJax[type];if("function"==typeof(jax=input.Process(script,state))){if(jax.called)continue;this.RestartAfter(jax)}jax=jax.Attach(script,input.id),this.saveScript(jax,state,script,STATE),this.postInputHooks.Execute(jax,input.id,script)}state.i++;var now=(new Date).getTime();now-state.start>this.processUpdateTime&&state.i<state.scripts.length&&(state.start=now,this.RestartAfter(MathJax.Callback.Delay(1)))}else state.i++;else state.i++}catch(err){return this.processError(err,state,"Input")}return state.scripts.length&&this.config.showProcessingMessages&&MathJax.Message.Set(["ProcessMath","Processing math: %1%%",100],0),state.start=(new Date).getTime(),state.i=state.j=0,null},postInputHooks:MathJax.Callback.Hooks(!0),saveScript:function(jax,state,script,STATE){if(!this.outputJax[jax.mimeType])throw script.MathJax.state=STATE.UPDATE,Error("No output jax registered for "+jax.mimeType);jax.outputJax=this.outputJax[jax.mimeType][0].id,state.jax[jax.outputJax]||(0===state.jaxIDs.length?state.jax[jax.outputJax]=state.scripts:(1===state.jaxIDs.length&&(state.jax[state.jaxIDs[0]]=state.scripts.slice(0,state.i)),state.jax[jax.outputJax]=[]),state.jaxIDs.push(jax.outputJax)),state.jaxIDs.length>1&&state.jax[jax.outputJax].push(script),script.MathJax.state=STATE.OUTPUT},prepareOutput:function(state,method){for(;state.j<state.jaxIDs.length;){var id=state.jaxIDs[state.j],JAX=MathJax.OutputJax[id];if(JAX[method])try{var result=JAX[method](state);if("function"==typeof result){if(result.called)continue;this.RestartAfter(result)}}catch(err){return err.restart||(MathJax.Message.Set(["PrepError","Error preparing %1 output (%2)",id,method],null,600),MathJax.Hub.lastPrepError=err,state.j++),MathJax.Callback.After(["prepareOutput",this,state,method],err.restart)}state.j++}return null},processOutput:function(state){var result,STATE=MathJax.ElementJax.STATE,script,m=state.scripts.length;try{for(;state.i<m;)if((script=state.scripts[state.i])&&script.parentNode&&script.MathJax&&!script.MathJax.error){var jax=script.MathJax.elementJax;if(jax){!1!==(result=MathJax.OutputJax[jax.outputJax].Process(script,state))&&(script.MathJax.state=STATE.PROCESSED,script.MathJax.preview&&(script.MathJax.preview.innerHTML="",script.MathJax.preview.style.display="none"),this.signal.Post(["New Math",jax.inputID])),state.i++;var now=(new Date).getTime();now-state.start>this.processUpdateTime&&state.i<state.scripts.length&&(state.start=now,this.RestartAfter(MathJax.Callback.Delay(this.processUpdateDelay)))}else state.i++}else state.i++}catch(err){return this.processError(err,state,"Output")}return state.scripts.length&&this.config.showProcessingMessages&&(MathJax.Message.Set(["TypesetMath","Typesetting math: %1%%",100],0),MathJax.Message.Clear(0)),state.i=state.j=0,null},processMessage:function(state,type){var m=Math.floor(state.i/state.scripts.length*100),message="Output"===type?["TypesetMath","Typesetting math: %1%%"]:["ProcessMath","Processing math: %1%%"];this.config.showProcessingMessages&&MathJax.Message.Set(message.concat(m),0)},processError:function(err,state,type){if(!err.restart){if(!this.config.errorSettings.message)throw err;this.formatError(state.scripts[state.i],err),state.i++}return this.processMessage(state,type),MathJax.Callback.After(["process"+type,this,state],err.restart)},formatError:function(script,err){var LOCALIZE=function(id,text,arg1,arg2){return MathJax.Localization._(id,text,arg1,arg2)},message=LOCALIZE("ErrorMessage","Error: %1",err.message)+"\n";(err.sourceURL||err.fileName)&&(message+="\n"+LOCALIZE("ErrorFile","file: %1",err.sourceURL||err.fileName)),(err.line||err.lineNumber)&&(message+="\n"+LOCALIZE("ErrorLine","line: %1",err.line||err.lineNumber)),message+="\n\n"+LOCALIZE("ErrorTips","Debugging tips: use %1, inspect %2 in the browser console","'unpacked/MathJax.js'","'MathJax.Hub.lastError'"),script.MathJax.error=MathJax.OutputJax.Error.Jax(message,script),script.MathJax.elementJax&&(script.MathJax.error.inputID=script.MathJax.elementJax.inputID);var errorSettings=this.config.errorSettings,errorText=LOCALIZE(errorSettings.messageId,errorSettings.message),error=MathJax.HTML.Element("span",{className:"MathJax_Error",jaxID:"Error",isMathJax:!0,id:script.MathJax.error.inputID+"-Frame"},[["span",null,errorText]]);MathJax.Ajax.Require("[MathJax]/extensions/MathEvents.js",(function(){var EVENT=MathJax.Extension.MathEvents.Event,HUB=MathJax.Hub;error.oncontextmenu=EVENT.Menu,error.onmousedown=EVENT.Mousedown,error.onkeydown=EVENT.Keydown,error.tabIndex=HUB.getTabOrder(HUB.getJaxFor(script))}));var node=document.getElementById(error.id);node&&node.parentNode.removeChild(node),script.parentNode&&script.parentNode.insertBefore(error,script),script.MathJax.preview&&(script.MathJax.preview.innerHTML="",script.MathJax.preview.style.display="none"),this.lastError=err,this.signal.Post(["Math Processing Error",script,err])},RestartAfter:function(callback){throw this.Insert(Error("restart"),{restart:MathJax.Callback(callback)})},elementCallback:function(element,callback){if(null==callback&&(MathJax.Object.isArray(element)||"function"==typeof element))try{MathJax.Callback(element),callback=element,element=null}catch(e){}null==element&&(element=this.config.elements||[]),this.isHTMLCollection(element)&&(element=this.HTMLCollection2Array(element)),MathJax.Object.isArray(element)||(element=[element]);for(var i=0,m=(element=[].concat(element)).length;i<m;i++)"string"==typeof element[i]&&(element[i]=document.getElementById(element[i]));return document.body||(document.body=document.getElementsByTagName("body")[0]),0==element.length&&element.push(document.body),callback||(callback={}),{count:element.length,elements:1===element.length?element[0]:element,callback:callback}},elementScripts:function(element){var scripts=[];if(MathJax.Object.isArray(element)||this.isHTMLCollection(element)){for(var i=0,m=element.length;i<m;i++){for(var alreadyDone=0,j=0;j<i&&!alreadyDone;j++)alreadyDone=element[j].contains(element[i]);alreadyDone||scripts.push.apply(scripts,this.elementScripts(element[i]))}return scripts}return"string"==typeof element&&(element=document.getElementById(element)),document.body||(document.body=document.getElementsByTagName("body")[0]),null==element&&(element=document.body),null!=element.tagName&&"script"===element.tagName.toLowerCase()?[element]:(scripts=element.getElementsByTagName("script"),this.msieHTMLCollectionBug&&(scripts=this.HTMLCollection2Array(scripts)),scripts)},isHTMLCollection:function(obj){return"HTMLCollection"in window&&"object"==typeof obj&&obj instanceof HTMLCollection},HTMLCollection2Array:function(nodes){if(!this.msieHTMLCollectionBug)return[].slice.call(nodes);for(var NODES=[],i=0,m=nodes.length;i<m;i++)NODES[i]=nodes[i];return NODES},Insert:function(dst,src){for(var id in src)src.hasOwnProperty(id)&&("object"!=typeof src[id]||MathJax.Object.isArray(src[id])||"object"!=typeof dst[id]&&"function"!=typeof dst[id]?dst[id]=src[id]:this.Insert(dst[id],src[id]));return dst},getTabOrder:function(script){return this.config.menuSettings.inTabOrder?0:-1},SplitList:"trim"in String.prototype?function(list){return list.trim().split(/\s+/)}:function(list){return list.replace(/^\s+/,"").replace(/\s+$/,"").split(/\s+/)}},MathJax.Hub.Insert(MathJax.Hub.config.styles,MathJax.Message.styles),MathJax.Hub.Insert(MathJax.Hub.config.styles,{".MathJax_Error":MathJax.Hub.config.errorSettings.style}),MathJax.Extension={},MathJax.Hub.Configured=MathJax.Callback({}),MathJax.Hub.Startup={script:"",queue:MathJax.Callback.Queue(),signal:MathJax.Callback.Signal("Startup"),params:{},Config:function(){if(this.queue.Push(["Post",this.signal,"Begin Config"]),MathJax.AuthorConfig&&MathJax.AuthorConfig.root&&(MathJax.Ajax.config.root=MathJax.AuthorConfig.root),this.params.locale&&(MathJax.Localization.resetLocale(this.params.locale),MathJax.Hub.config.menuSettings.locale=this.params.locale),this.params.config)for(var files=this.params.config.split(/,/),i=0,m=files.length;i<m;i++)files[i].match(/\.js$/)||(files[i]+=".js"),this.queue.Push(["Require",MathJax.Ajax,this.URL("config",files[i])]);this.queue.Push(["Config",MathJax.Hub,MathJax.AuthorConfig]),this.script.match(/\S/)&&this.queue.Push(this.script+";\n1;"),this.queue.Push(["ConfigDelay",this],["ConfigBlocks",this],[function(THIS){return THIS.loadArray(MathJax.Hub.config.config,"config",null,!0)},this],["Post",this.signal,"End Config"])},ConfigDelay:function(){var delay=this.params.delayStartupUntil||MathJax.Hub.config.delayStartupUntil;return"onload"===delay?this.onload:"configured"===delay?MathJax.Hub.Configured:delay},ConfigBlocks:function(){for(var scripts=document.getElementsByTagName("script"),queue=MathJax.Callback.Queue(),i=0,m=scripts.length;i<m;i++){var type=String(scripts[i].type).replace(/ /g,"");type.match(/^text\/x-mathjax-config(;.*)?$/)&&!type.match(/;executed=true/)&&(scripts[i].type+=";executed=true",queue.Push(scripts[i].innerHTML+";\n1;"))}return queue.Push((function(){MathJax.Ajax.config.root=MathJax.Hub.config.root}))},Cookie:function(){return this.queue.Push(["Post",this.signal,"Begin Cookie"],["Get",MathJax.HTML.Cookie,"menu",MathJax.Hub.config.menuSettings],[function(config){var SETTINGS=config.menuSettings;SETTINGS.locale&&MathJax.Localization.resetLocale(SETTINGS.locale);var renderer=config.menuSettings.renderer,jax=config.jax;if(renderer){var name="output/"+renderer;jax.sort();for(var i=0,m=jax.length;i<m&&"output/"!==jax[i].substr(0,7);i++);if(i==m-1)jax.pop();else for(;i<m;){if(jax[i]===name){jax.splice(i,1);break}i++}jax.unshift(name)}null!=SETTINGS.CHTMLpreview&&(null==SETTINGS.FastPreview&&(SETTINGS.FastPreview=SETTINGS.CHTMLpreview),delete SETTINGS.CHTMLpreview),SETTINGS.FastPreview&&!MathJax.Extension["fast-preview"]&&MathJax.Hub.config.extensions.push("fast-preview.js"),config.menuSettings.assistiveMML&&!MathJax.Extension.AssistiveMML&&MathJax.Hub.config.extensions.push("AssistiveMML.js")},MathJax.Hub.config],["Post",this.signal,"End Cookie"])},Styles:function(){return this.queue.Push(["Post",this.signal,"Begin Styles"],["loadArray",this,MathJax.Hub.config.styleSheets,"config"],["Styles",MathJax.Ajax,MathJax.Hub.config.styles],["Post",this.signal,"End Styles"])},Jax:function(){for(var config=MathJax.Hub.config,jax=MathJax.Hub.outputJax,i=0,m=config.jax.length,k=0;i<m;i++){var name=config.jax[i].substr(7);"output/"===config.jax[i].substr(0,7)&&null==jax.order[name]&&(jax.order[name]=k,k++)}var queue;return MathJax.Callback.Queue().Push(["Post",this.signal,"Begin Jax"],["loadArray",this,config.jax,"jax","config.js"],["Post",this.signal,"End Jax"])},Extensions:function(){var queue;return MathJax.Callback.Queue().Push(["Post",this.signal,"Begin Extensions"],["loadArray",this,MathJax.Hub.config.extensions,"extensions"],["Post",this.signal,"End Extensions"])},Message:function(){MathJax.Message.Init(!0)},Menu:function(){var menu=MathJax.Hub.config.menuSettings,jax=MathJax.Hub.outputJax,registered;for(var id in jax)if(jax.hasOwnProperty(id)&&jax[id].length){registered=jax[id];break}registered&&registered.length&&(menu.renderer&&menu.renderer!==registered[0].id&&registered.unshift(MathJax.OutputJax[menu.renderer]),menu.renderer=registered[0].id)},Hash:function(){if(MathJax.Hub.config.positionToHash&&document.location.hash&&document.body&&document.body.scrollIntoView){var name=decodeURIComponent(document.location.hash.substr(1)),target=document.getElementById(name);if(!target)for(var a=document.getElementsByTagName("a"),i=0,m=a.length;i<m;i++)if(a[i].name===name){target=a[i];break}if(target){for(;!target.scrollIntoView;)target=target.parentNode;(target=this.HashCheck(target))&&target.scrollIntoView&&setTimeout((function(){target.scrollIntoView(!0)}),1)}}},HashCheck:function(target){var jax=MathJax.Hub.getJaxFor(target);return jax&&MathJax.OutputJax[jax.outputJax].hashCheck&&(target=MathJax.OutputJax[jax.outputJax].hashCheck(target)),target},MenuZoom:function(){MathJax.Hub.config.showMathMenu&&(MathJax.Extension.MathMenu?setTimeout(MathJax.Callback(["loadDomain",MathJax.Localization,"MathMenu"]),1e3):setTimeout((function(){MathJax.Callback.Queue(["Require",MathJax.Ajax,"[MathJax]/extensions/MathMenu.js",{}],["loadDomain",MathJax.Localization,"MathMenu"])}),1e3),MathJax.Extension.MathZoom||setTimeout(MathJax.Callback(["Require",MathJax.Ajax,"[MathJax]/extensions/MathZoom.js",{}]),2e3))},onLoad:function(){var onload=this.onload=MathJax.Callback((function(){MathJax.Hub.Startup.signal.Post("onLoad")}));if(document.body&&document.readyState)if(MathJax.Hub.Browser.isMSIE){if("complete"===document.readyState)return[onload]}else if("loading"!==document.readyState)return[onload];return window.addEventListener?(window.addEventListener("load",onload,!1),this.params.noDOMContentEvent||window.addEventListener("DOMContentLoaded",onload,!1)):window.attachEvent?window.attachEvent("onload",onload):window.onload=onload,onload},Typeset:function(element,callback){return MathJax.Hub.config.skipStartupTypeset?function(){}:this.queue.Push(["Post",this.signal,"Begin Typeset"],["Typeset",MathJax.Hub,element,callback],["Post",this.signal,"End Typeset"])},URL:function(dir,name){return name.match(/^([a-z]+:\/\/|\[|\/)/)||(name="[MathJax]/"+dir+"/"+name),name},loadArray:function(files,dir,name,synchronous){if(files&&(MathJax.Object.isArray(files)||(files=[files]),files.length)){for(var queue=MathJax.Callback.Queue(),callback={},file,i=0,m=files.length;i<m;i++)file=this.URL(dir,files[i]),name&&(file+="/"+name),synchronous?queue.Push(["Require",MathJax.Ajax,file,callback]):queue.Push(MathJax.Ajax.Require(file,callback));return queue.Push({})}return null}},function(BASENAME){var BASE=window.MathJax,ROOT="[MathJax]",HUB=BASE.Hub,AJAX=BASE.Ajax,CALLBACK=BASE.Callback,JAX=MathJax.Object.Subclass({JAXFILE:"jax.js",require:null,config:{},Init:function(def,cdef){return 0===arguments.length?this:this.constructor.Subclass(def,cdef)()},Augment:function(def,cdef){var cObject=this.constructor,ndef={};if(null!=def){for(var id in def)def.hasOwnProperty(id)&&("function"==typeof def[id]?cObject.protoFunction(id,def[id]):ndef[id]=def[id]);def.toString!==cObject.prototype.toString&&def.toString!=={}.toString&&cObject.protoFunction("toString",def.toString)}return HUB.Insert(cObject.prototype,ndef),cObject.Augment(null,cdef),this},Translate:function(script,state){throw Error(this.directory+"/"+this.JAXFILE+" failed to define the Translate() method")},Register:function(mimetype){},Config:function(){this.config=HUB.CombineConfig(this.id,this.config),this.config.Augment&&this.Augment(this.config.Augment)},Startup:function(){},loadComplete:function(file){if("config.js"===file)return AJAX.loadComplete(this.directory+"/"+file);var queue=CALLBACK.Queue();return queue.Push(HUB.Register.StartupHook("End Config",{}),["Post",HUB.Startup.signal,this.id+" Jax Config"],["Config",this],["Post",HUB.Startup.signal,this.id+" Jax Require"],[function(THIS){return MathJax.Hub.Startup.loadArray(THIS.require,this.directory)},this],[function(config,id){return MathJax.Hub.Startup.loadArray(config.extensions,"extensions/"+id)},this.config||{},this.id],["Post",HUB.Startup.signal,this.id+" Jax Startup"],["Startup",this],["Post",HUB.Startup.signal,this.id+" Jax Ready"]),this.copyTranslate&&queue.Push([function(THIS){THIS.preProcess=THIS.preTranslate,THIS.Process=THIS.Translate,THIS.postProcess=THIS.postTranslate},this.constructor.prototype]),queue.Push(["loadComplete",AJAX,this.directory+"/"+file])}},{id:"Jax",version:"2.7.5",directory:ROOT+"/jax",extensionDir:ROOT+"/extensions"});BASE.InputJax=JAX.Subclass({elementJax:"mml",sourceMenuTitle:["Original","Original Form"],copyTranslate:!0,Process:function(script,state){var queue=CALLBACK.Queue(),file,jax=this.elementJax;BASE.Object.isArray(jax)||(jax=[jax]);for(var i=0,m=jax.length;i<m;i++)file=BASE.ElementJax.directory+"/"+jax[i]+"/"+this.JAXFILE,this.require?BASE.Object.isArray(this.require)||(this.require=[this.require]):this.require=[],this.require.push(file),queue.Push(AJAX.Require(file));file=this.directory+"/"+this.JAXFILE;var load=queue.Push(AJAX.Require(file));return load.called||(this.constructor.prototype.Process=function(){if(!load.called)return load;throw Error(file+" failed to load properly")}),(jax=HUB.outputJax["jax/"+jax[0]])&&queue.Push(AJAX.Require(jax[0].directory+"/"+this.JAXFILE)),queue.Push({})},needsUpdate:function(jax){var script=jax.SourceElement();return jax.originalText!==BASE.HTML.getScript(script)},Register:function(mimetype){HUB.inputJax||(HUB.inputJax={}),HUB.inputJax[mimetype]=this}},{id:"InputJax",version:"2.7.5",directory:JAX.directory+"/input",extensionDir:JAX.extensionDir}),BASE.OutputJax=JAX.Subclass({copyTranslate:!0,preProcess:function(state){var load,file=this.directory+"/"+this.JAXFILE;return this.constructor.prototype.preProcess=function(state){if(!load.called)return load;throw Error(file+" failed to load properly")},load=AJAX.Require(file)},Process:function(state){throw Error(this.id+" output jax failed to load properly")},Register:function(mimetype){var jax=HUB.outputJax;jax[mimetype]||(jax[mimetype]=[]),jax[mimetype].length&&(this.id===HUB.config.menuSettings.renderer||(jax.order[this.id]||0)<(jax.order[jax[mimetype][0].id]||0))?jax[mimetype].unshift(this):jax[mimetype].push(this),this.require?BASE.Object.isArray(this.require)||(this.require=[this.require]):this.require=[],this.require.push(BASE.ElementJax.directory+"/"+mimetype.split(/\//)[1]+"/"+this.JAXFILE)},Remove:function(jax){}},{id:"OutputJax",version:"2.7.5",directory:JAX.directory+"/output",extensionDir:JAX.extensionDir,fontDir:ROOT+(BASE.isPacked?"":"/..")+"/fonts",imageDir:ROOT+(BASE.isPacked?"":"/..")+"/images"}),BASE.ElementJax=JAX.Subclass({Init:function(def,cdef){return this.constructor.Subclass(def,cdef)},inputJax:null,outputJax:null,inputID:null,originalText:"",mimeType:"",sourceMenuTitle:["MathMLcode","MathML Code"],Text:function(text,callback){var script=this.SourceElement();return BASE.HTML.setScript(script,text),script.MathJax.state=this.STATE.UPDATE,HUB.Update(script,callback)},Reprocess:function(callback){var script=this.SourceElement();return script.MathJax.state=this.STATE.UPDATE,HUB.Reprocess(script,callback)},Update:function(callback){return this.Rerender(callback)},Rerender:function(callback){var script=this.SourceElement();return script.MathJax.state=this.STATE.OUTPUT,HUB.Process(script,callback)},Remove:function(keep){this.hover&&this.hover.clear(this),BASE.OutputJax[this.outputJax].Remove(this),keep||(HUB.signal.Post(["Remove Math",this.inputID]),this.Detach())},needsUpdate:function(){return BASE.InputJax[this.inputJax].needsUpdate(this)},SourceElement:function(){return document.getElementById(this.inputID)},Attach:function(script,inputJax){var jax=script.MathJax.elementJax;return script.MathJax.state===this.STATE.UPDATE?jax.Clone(this):(jax=script.MathJax.elementJax=this,script.id?this.inputID=script.id:(script.id=this.inputID=BASE.ElementJax.GetID(),this.newID=1)),jax.originalText=BASE.HTML.getScript(script),jax.inputJax=inputJax,jax.root&&(jax.root.inputID=jax.inputID),jax},Detach:function(){var script=this.SourceElement();if(script){try{delete script.MathJax}catch(err){script.MathJax=null}this.newID&&(script.id="")}},Clone:function(jax){var id;for(id in this)this.hasOwnProperty(id)&&void 0===jax[id]&&"newID"!==id&&delete this[id];for(id in jax)jax.hasOwnProperty(id)&&(void 0===this[id]||this[id]!==jax[id]&&"inputID"!==id)&&(this[id]=jax[id])}},{id:"ElementJax",version:"2.7.5",directory:JAX.directory+"/element",extensionDir:JAX.extensionDir,ID:0,STATE:{PENDING:1,PROCESSED:2,UPDATE:3,OUTPUT:4},GetID:function(){return this.ID++,"MathJax-Element-"+this.ID},Subclass:function(){var obj=JAX.Subclass.apply(this,arguments);return obj.loadComplete=this.prototype.loadComplete,obj}}),BASE.ElementJax.prototype.STATE=BASE.ElementJax.STATE,BASE.OutputJax.Error={id:"Error",version:"2.7.5",config:{},errors:0,ContextMenu:function(){return BASE.Extension.MathEvents.Event.ContextMenu.apply(BASE.Extension.MathEvents.Event,arguments)},Mousedown:function(){return BASE.Extension.MathEvents.Event.AltContextMenu.apply(BASE.Extension.MathEvents.Event,arguments)},getJaxFromMath:function(math){return(math.nextSibling.MathJax||{}).error},Jax:function(text,script){var jax=MathJax.Hub.inputJax[script.type.replace(/ *;(.|\s)*/,"")];return this.errors++,{inputJax:(jax||{id:"Error"}).id,outputJax:"Error",inputID:"MathJax-Error-"+this.errors,sourceMenuTitle:["ErrorMessage","Error Message"],sourceMenuFormat:"Error",originalText:MathJax.HTML.getScript(script),errorText:text}}},BASE.InputJax.Error={id:"Error",version:"2.7.5",config:{},sourceMenuTitle:["Original","Original Form"]}}("MathJax"),function(BASENAME){var BASE=window.MathJax;BASE||(BASE=window.MathJax={});var HUB=BASE.Hub,STARTUP=HUB.Startup,CONFIG=HUB.config,HEAD=document.head||document.getElementsByTagName("head")[0];HEAD||(HEAD=document.childNodes[0]);var scripts=(document.documentElement||document).getElementsByTagName("script");0===scripts.length&&HEAD.namespaceURI&&(scripts=document.getElementsByTagNameNS(HEAD.namespaceURI,"script"));for(var namePattern=new RegExp("(^|/)MathJax\\.js(\\?.*)?$"),i=scripts.length-1;i>=0;i--)if((scripts[i].src||"").match(namePattern)){if(STARTUP.script=scripts[i].innerHTML,RegExp.$2)for(var params=RegExp.$2.substr(1).split(/\&/),j=0,m=params.length;j<m;j++){var KV=params[j].match(/(.*)=(.*)/);KV?STARTUP.params[unescape(KV[1])]=unescape(KV[2]):STARTUP.params[params[j]]=!0}CONFIG.root=scripts[i].src.replace(/(^|\/)[^\/]*(\?.*)?$/,""),BASE.Ajax.config.root=CONFIG.root,BASE.Ajax.params=STARTUP.params;break}var AGENT=navigator.userAgent,BROWSERS={isMac:"Mac"===navigator.platform.substr(0,3),isPC:"Win"===navigator.platform.substr(0,3),isMSIE:"ActiveXObject"in window&&"clipboardData"in window,isEdge:"MSGestureEvent"in window&&"chrome"in window&&null==window.chrome.loadTimes,isFirefox:!!AGENT.match(/Gecko\//)&&!AGENT.match(/like Gecko/),isSafari:!(!AGENT.match(/ (Apple)?WebKit\//)||AGENT.match(/ like iPhone /)||window.chrome&&null!=window.chrome.app),isChrome:"chrome"in window&&null!=window.chrome.loadTimes,isOpera:"opera"in window&&null!=window.opera.version,isKonqueror:"konqueror"in window&&"KDE"==navigator.vendor,versionAtLeast:function(v){for(var bv=this.version.split("."),i=0,m=(v=new String(v).split(".")).length;i<m;i++)if(bv[i]!=v[i])return parseInt(bv[i]||"0")>=parseInt(v[i]);return!0},Select:function(choices){var browser=choices[HUB.Browser];return browser?browser(HUB.Browser):null}},xAGENT=AGENT.replace(/^Mozilla\/(\d+\.)+\d+ /,"").replace(/[a-z][-a-z0-9._: ]+\/\d+[^ ]*-[^ ]*\.([a-z][a-z])?\d+ /i,"").replace(/Gentoo |Ubuntu\/(\d+\.)*\d+ (\([^)]*\) )?/,""),MML,MO,TEXCLASS;for(var browser in HUB.Browser=HUB.Insert(HUB.Insert(new String("Unknown"),{version:"0.0"}),BROWSERS),BROWSERS)if(BROWSERS.hasOwnProperty(browser)&&BROWSERS[browser]&&"is"===browser.substr(0,2)){if("Mac"===(browser=browser.slice(2))||"PC"===browser)continue;HUB.Browser=HUB.Insert(new String(browser),BROWSERS);var VERSION,MATCH=new RegExp(".*(Version/| Trident/.*; rv:)((?:\\d+\\.)+\\d+)|.*("+browser+")"+("MSIE"==browser?" ":"/")+"((?:\\d+\\.)*\\d+)|(?:^|\\(| )([a-z][-a-z0-9._: ]+|(?:Apple)?WebKit)/((?:\\d+\\.)+\\d+)").exec(xAGENT)||["","","","unknown","0.0"];HUB.Browser.name=""!=MATCH[1]?browser:MATCH[3]||MATCH[5],HUB.Browser.version=MATCH[2]||MATCH[4]||MATCH[6];break}try{HUB.Browser.Select({Safari:function(browser){var v=parseInt(String(browser.version).split(".")[0]);v>85&&(browser.webkit=browser.version),v>=538?browser.version="8.0":v>=537?browser.version="7.0":v>=536?browser.version="6.0":v>=534?browser.version="5.1":v>=533?browser.version="5.0":v>=526?browser.version="4.0":v>=525?browser.version="3.1":v>500?browser.version="3.0":v>400?browser.version="2.0":v>85&&(browser.version="1.0"),browser.webkit=navigator.appVersion.match(/WebKit\/(\d+)\./)[1],browser.isMobile=null!=navigator.appVersion.match(/Mobile/i),browser.noContextMenu=browser.isMobile},Firefox:function(browser){if(("0.0"===browser.version||null==AGENT.match(/Firefox/))&&"Gecko"===navigator.product){var rv=AGENT.match(/[\/ ]rv:(\d+\.\d.*?)[\) ]/);if(rv)browser.version=rv[1];else{var date=(navigator.buildID||navigator.productSub||"0").substr(0,8);date>="20111220"?browser.version="9.0":date>="20111120"?browser.version="8.0":date>="20110927"?browser.version="7.0":date>="20110816"?browser.version="6.0":date>="20110621"?browser.version="5.0":date>="20110320"?browser.version="4.0":date>="20100121"?browser.version="3.6":date>="20090630"?browser.version="3.5":date>="20080617"?browser.version="3.0":date>="20061024"&&(browser.version="2.0")}}browser.isMobile=null!=navigator.appVersion.match(/Android/i)||null!=AGENT.match(/ Fennec\//)||null!=AGENT.match(/Mobile/)},Chrome:function(browser){browser.noContextMenu=browser.isMobile=!!navigator.userAgent.match(/ Mobile[ \/]/)},Opera:function(browser){browser.version=opera.version()},Edge:function(browser){browser.isMobile=!!navigator.userAgent.match(/ Phone/)},MSIE:function(browser){if(browser.isMobile=!!navigator.userAgent.match(/ Phone/),browser.isIE9=!(!document.documentMode||!window.performance&&!window.msPerformance),MathJax.HTML.setScriptBug=!browser.isIE9||document.documentMode<9,MathJax.Hub.msieHTMLCollectionBug=document.documentMode<9,document.documentMode<10&&!STARTUP.params.NoMathPlayer){try{new ActiveXObject("MathPlayer.Factory.1"),browser.hasMathPlayer=!0}catch(err){}try{if(browser.hasMathPlayer){var mathplayer=document.createElement("object");mathplayer.id="mathplayer",mathplayer.classid="clsid:32F66A20-7614-11D4-BD11-00104BD3F987",HEAD.appendChild(mathplayer),document.namespaces.add("m","http://www.w3.org/1998/Math/MathML"),browser.mpNamespace=!0,!document.readyState||"loading"!==document.readyState&&"interactive"!==document.readyState||(document.write('<?import namespace="m" implementation="#MathPlayer">'),browser.mpImported=!0)}else document.namespaces.add("mjx_IE_fix","http://www.w3.org/1999/xlink")}catch(err){}}}})}catch(err){console.error(err.message)}MathJax.Ajax.Preloading("[MathJax]/jax/element/mml/jax.js","[MathJax]/jax/element/mml/optable/Arrows.js","[MathJax]/jax/element/mml/optable/MiscMathSymbolsA.js","[MathJax]/jax/element/mml/optable/Dingbats.js","[MathJax]/jax/element/mml/optable/GeneralPunctuation.js","[MathJax]/jax/element/mml/optable/SpacingModLetters.js","[MathJax]/jax/element/mml/optable/MiscTechnical.js","[MathJax]/jax/element/mml/optable/SupplementalArrowsA.js","[MathJax]/jax/element/mml/optable/GreekAndCoptic.js","[MathJax]/jax/element/mml/optable/LetterlikeSymbols.js","[MathJax]/jax/element/mml/optable/SupplementalArrowsB.js","[MathJax]/jax/element/mml/optable/BasicLatin.js","[MathJax]/jax/element/mml/optable/MiscSymbolsAndArrows.js","[MathJax]/jax/element/mml/optable/CombDiacritMarks.js","[MathJax]/jax/element/mml/optable/GeometricShapes.js","[MathJax]/jax/element/mml/optable/MathOperators.js","[MathJax]/jax/element/mml/optable/MiscMathSymbolsB.js","[MathJax]/jax/element/mml/optable/SuppMathOperators.js","[MathJax]/jax/element/mml/optable/CombDiactForSymbols.js","[MathJax]/jax/element/mml/optable/Latin1Supplement.js","[MathJax]/extensions/MathEvents.js","[MathJax]/extensions/MathZoom.js","[MathJax]/extensions/MathMenu.js","[MathJax]/extensions/toMathML.js","[MathJax]/extensions/HelpDialog.js","[MathJax]/jax/input/TeX/config.js","[MathJax]/jax/input/TeX/jax.js","[MathJax]/jax/output/CommonHTML/config.js","[MathJax]/jax/output/CommonHTML/jax.js","[MathJax]/jax/output/CommonHTML/autoload/annotation-xml.js","[MathJax]/jax/output/CommonHTML/autoload/maction.js","[MathJax]/jax/output/CommonHTML/autoload/menclose.js","[MathJax]/jax/output/CommonHTML/autoload/mglyph.js","[MathJax]/jax/output/CommonHTML/autoload/mmultiscripts.js","[MathJax]/jax/output/CommonHTML/autoload/ms.js","[MathJax]/jax/output/CommonHTML/autoload/mtable.js","[MathJax]/jax/output/CommonHTML/autoload/multiline.js","[MathJax]/extensions/tex2jax.js","[MathJax]/extensions/TeX/AMScd.js","[MathJax]/extensions/TeX/AMSmath.js","[MathJax]/extensions/TeX/AMSsymbols.js","[MathJax]/extensions/TeX/HTML.js","[MathJax]/extensions/TeX/action.js","[MathJax]/extensions/TeX/autobold.js","[MathJax]/extensions/TeX/bbox.js","[MathJax]/extensions/TeX/boldsymbol.js","[MathJax]/extensions/TeX/cancel.js","[MathJax]/extensions/TeX/color.js","[MathJax]/extensions/TeX/enclose.js","[MathJax]/extensions/TeX/extpfeil.js","[MathJax]/extensions/TeX/mathchoice.js","[MathJax]/extensions/TeX/mediawiki-texvc.js","[MathJax]/extensions/TeX/mhchem.js","[MathJax]/extensions/TeX/newcommand.js","[MathJax]/extensions/TeX/unicode.js","[MathJax]/extensions/TeX/verb.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/fontdata.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/fontdata-extra.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/AMS-Regular.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/Caligraphic-Bold.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/Fraktur-Bold.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/Fraktur-Regular.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/Math-BoldItalic.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/SansSerif-Bold.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/SansSerif-Italic.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/SansSerif-Regular.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/Script-Regular.js","[MathJax]/jax/output/CommonHTML/fonts/TeX/Typewriter-Regular.js"),MathJax.Hub.Config({"v1.0-compatible":!1}),MathJax.ElementJax.mml=MathJax.ElementJax({mimeType:"jax/mml"},{id:"mml",version:"2.7.5",directory:MathJax.ElementJax.directory+"/mml",extensionDir:MathJax.ElementJax.extensionDir+"/mml",optableDir:MathJax.ElementJax.directory+"/mml/optable"}),MathJax.ElementJax.mml.Augment({Init:function(){if(1===arguments.length&&"math"===arguments[0].type?this.root=arguments[0]:this.root=MathJax.ElementJax.mml.math.apply(this,arguments),this.root.attr&&this.root.attr.mode){this.root.display||"display"!==this.root.attr.mode||(this.root.display="block",this.root.attrNames.push("display")),delete this.root.attr.mode;for(var i=0,m=this.root.attrNames.length;i<m;i++)if("mode"===this.root.attrNames[i]){this.root.attrNames.splice(i,1);break}}}},{INHERIT:"_inherit_",AUTO:"_auto_",SIZE:{INFINITY:"infinity",SMALL:"small",NORMAL:"normal",BIG:"big"},COLOR:{TRANSPARENT:"transparent"},VARIANT:{NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"initial",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALIGRAPHIC:"-tex-caligraphic",OLDSTYLE:"-tex-oldstyle"},FORM:{PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},LINEBREAK:{AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},LINEBREAKSTYLE:{BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},INDENTALIGN:{LEFT:"left",CENTER:"center",RIGHT:"right",AUTO:"auto",ID:"id",INDENTALIGN:"indentalign"},INDENTSHIFT:{INDENTSHIFT:"indentshift"},LINETHICKNESS:{THIN:"thin",MEDIUM:"medium",THICK:"thick"},NOTATION:{LONGDIV:"longdiv",ACTUARIAL:"actuarial",RADICAL:"radical",BOX:"box",ROUNDEDBOX:"roundedbox",CIRCLE:"circle",LEFT:"left",RIGHT:"right",TOP:"top",BOTTOM:"bottom",UPDIAGONALSTRIKE:"updiagonalstrike",DOWNDIAGONALSTRIKE:"downdiagonalstrike",UPDIAGONALARROW:"updiagonalarrow",VERTICALSTRIKE:"verticalstrike",HORIZONTALSTRIKE:"horizontalstrike",PHASORANGLE:"phasorangle",MADRUWB:"madruwb"},ALIGN:{TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},LINES:{NONE:"none",SOLID:"solid",DASHED:"dashed"},SIDE:{LEFT:"left",RIGHT:"right",LEFTOVERLAP:"leftoverlap",RIGHTOVERLAP:"rightoverlap"},WIDTH:{AUTO:"auto",FIT:"fit"},ACTIONTYPE:{TOGGLE:"toggle",STATUSLINE:"statusline",TOOLTIP:"tooltip",INPUT:"input"},LENGTH:{VERYVERYTHINMATHSPACE:"veryverythinmathspace",VERYTHINMATHSPACE:"verythinmathspace",THINMATHSPACE:"thinmathspace",MEDIUMMATHSPACE:"mediummathspace",THICKMATHSPACE:"thickmathspace",VERYTHICKMATHSPACE:"verythickmathspace",VERYVERYTHICKMATHSPACE:"veryverythickmathspace",NEGATIVEVERYVERYTHINMATHSPACE:"negativeveryverythinmathspace",NEGATIVEVERYTHINMATHSPACE:"negativeverythinmathspace",NEGATIVETHINMATHSPACE:"negativethinmathspace",NEGATIVEMEDIUMMATHSPACE:"negativemediummathspace",NEGATIVETHICKMATHSPACE:"negativethickmathspace",NEGATIVEVERYTHICKMATHSPACE:"negativeverythickmathspace",NEGATIVEVERYVERYTHICKMATHSPACE:"negativeveryverythickmathspace"},OVERFLOW:{LINBREAK:"linebreak",SCROLL:"scroll",ELIDE:"elide",TRUNCATE:"truncate",SCALE:"scale"},UNIT:{EM:"em",EX:"ex",PX:"px",IN:"in",CM:"cm",MM:"mm",PT:"pt",PC:"pc"},TEXCLASS:{ORD:0,OP:1,BIN:2,REL:3,OPEN:4,CLOSE:5,PUNCT:6,INNER:7,VCENTER:8,NONE:-1},TEXCLASSNAMES:["ORD","OP","BIN","REL","OPEN","CLOSE","PUNCT","INNER","VCENTER"],skipAttributes:{texClass:!0,useHeight:!0,texprimestyle:!0},copyAttributes:{displaystyle:1,scriptlevel:1,open:1,close:1,form:1,actiontype:1,fontfamily:!0,fontsize:!0,fontweight:!0,fontstyle:!0,color:!0,background:!0,id:!0,class:1,href:!0,style:!0},copyAttributeNames:["displaystyle","scriptlevel","open","close","form","actiontype","fontfamily","fontsize","fontweight","fontstyle","color","background","id","class","href","style"],nocopyAttributes:{fontfamily:!0,fontsize:!0,fontweight:!0,fontstyle:!0,color:!0,background:!0,id:!0,class:!0,href:!0,style:!0,xmlns:!0},Error:function(message,def){var mml=this.merror(message),dir=MathJax.Localization.fontDirection(),font=MathJax.Localization.fontFamily();return def&&(mml=mml.With(def)),(dir||font)&&(mml=this.mstyle(mml),dir&&(mml.dir=dir),font&&(mml.style.fontFamily="font-family: "+font)),mml}}),function(MML){MML.mbase=MathJax.Object.Subclass({type:"base",isToken:!1,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT},noInherit:{},noInheritAttribute:{texClass:!0},getRemoved:{},linebreakContainer:!1,Init:function(){this.data=[],!this.inferRow||1===arguments.length&&arguments[0].inferred||this.Append(MML.mrow().With({inferred:!0,notParent:!0})),this.Append.apply(this,arguments)},With:function(def){for(var id in def)def.hasOwnProperty(id)&&(this[id]=def[id]);return this},Append:function(){if(this.inferRow&&this.data.length)this.data[0].Append.apply(this.data[0],arguments);else for(var i=0,m=arguments.length;i<m;i++)this.SetData(this.data.length,arguments[i])},SetData:function(i,item){null!=item&&(item instanceof MML.mbase||(item=this.isToken||this.isChars?MML.chars(item):MML.mtext(item)),item.parent=this,item.setInherit(this.inheritFromMe?this:this.inherit)),this.data[i]=item},Parent:function(){for(var parent=this.parent;parent&&parent.notParent;)parent=parent.parent;return parent},Get:function(name,nodefault,noself){if(!noself){if(null!=this[name])return this[name];if(this.attr&&null!=this.attr[name])return this.attr[name]}var parent=this.Parent();if(parent&&null!=parent["adjustChild_"+name])return parent["adjustChild_"+name](this.childPosition(),nodefault);for(var obj=this.inherit,root=obj;obj;){var value=obj[name];if(null==value&&obj.attr&&(value=obj.attr[name]),obj.removedStyles&&obj.getRemoved[name]&&null==value&&(value=obj.removedStyles[obj.getRemoved[name]]),null!=value&&obj.noInheritAttribute&&!obj.noInheritAttribute[name]){var noInherit=obj.noInherit[this.type];if(!noInherit||!noInherit[name])return value}root=obj,obj=obj.inherit}if(!nodefault){if(this.defaults[name]===MML.AUTO)return this.autoDefault(name);if(this.defaults[name]!==MML.INHERIT&&null!=this.defaults[name])return this.defaults[name];if(root)return root.defaults[name]}return null},hasValue:function(name){return null!=this.Get(name,!0)},getValues:function(){for(var values={},i=0,m=arguments.length;i<m;i++)values[arguments[i]]=this.Get(arguments[i]);return values},adjustChild_scriptlevel:function(i,nodef){return this.Get("scriptlevel",nodef)},adjustChild_displaystyle:function(i,nodef){return this.Get("displaystyle",nodef)},adjustChild_texprimestyle:function(i,nodef){return this.Get("texprimestyle",nodef)},hasMMLspacing:function(){return!1},childPosition:function(){for(var child=this,parent=child.parent;parent.notParent;)parent=(child=parent).parent;for(var i=0,m=parent.data.length;i<m;i++)if(parent.data[i]===child)return i;return null},setInherit:function(obj){if(obj!==this.inherit&&null==this.inherit){this.inherit=obj;for(var i=0,m=this.data.length;i<m;i++)this.data[i]&&this.data[i].setInherit&&this.data[i].setInherit(obj)}},setTeXclass:function(prev){return this.getPrevClass(prev),void 0!==this.texClass?this:prev},getPrevClass:function(prev){prev&&(this.prevClass=prev.Get("texClass"),this.prevLevel=prev.Get("scriptlevel"))},updateTeXclass:function(core){core&&(this.prevClass=core.prevClass,delete core.prevClass,this.prevLevel=core.prevLevel,delete core.prevLevel,this.texClass=core.Get("texClass"))},texSpacing:function(){var prev=null!=this.prevClass?this.prevClass:MML.TEXCLASS.NONE,tex=this.Get("texClass")||MML.TEXCLASS.ORD;if(prev===MML.TEXCLASS.NONE||tex===MML.TEXCLASS.NONE)return"";prev===MML.TEXCLASS.VCENTER&&(prev=MML.TEXCLASS.ORD),tex===MML.TEXCLASS.VCENTER&&(tex=MML.TEXCLASS.ORD);var space=this.TEXSPACE[prev][tex];return(this.prevLevel>0||this.Get("scriptlevel")>0)&&space>=0?"":this.TEXSPACELENGTH[Math.abs(space)]},TEXSPACELENGTH:["",MML.LENGTH.THINMATHSPACE,MML.LENGTH.MEDIUMMATHSPACE,MML.LENGTH.THICKMATHSPACE],TEXSPACE:[[0,-1,2,3,0,0,0,1],[-1,-1,0,3,0,0,0,1],[2,2,0,0,2,0,0,2],[3,3,0,0,3,0,0,3],[0,0,0,0,0,0,0,0],[0,-1,2,3,0,0,0,1],[1,1,0,1,1,1,1,1],[1,-1,2,3,1,0,1,1]],autoDefault:function(name){return""},isSpacelike:function(){return!1},isEmbellished:function(){return!1},Core:function(){return this},CoreMO:function(){return this},childIndex:function(child){if(null!=child)for(var i=0,m=this.data.length;i<m;i++)if(child===this.data[i])return i},CoreIndex:function(){return(this.inferRow&&this.data[0]||this).childIndex(this.Core())},hasNewline:function(){if(this.isEmbellished())return this.CoreMO().hasNewline();if(this.isToken||this.linebreakContainer)return!1;for(var i=0,m=this.data.length;i<m;i++)if(this.data[i]&&this.data[i].hasNewline())return!0;return!1},array:function(){return this.inferred?this.data:[this]},toString:function(){return this.type+"("+this.data.join(",")+")"},getAnnotation:function(){return null}},{childrenSpacelike:function(){for(var i=0,m=this.data.length;i<m;i++)if(!this.data[i].isSpacelike())return!1;return!0},childEmbellished:function(){return this.data[0]&&this.data[0].isEmbellished()},childCore:function(){return this.inferRow&&this.data[0]?this.data[0].Core():this.data[0]},childCoreMO:function(){return this.data[0]?this.data[0].CoreMO():null},setChildTeXclass:function(prev){return this.data[0]&&(prev=this.data[0].setTeXclass(prev),this.updateTeXclass(this.data[0])),prev},setBaseTeXclasses:function(prev){this.getPrevClass(prev),this.texClass=null,this.data[0]?this.isEmbellished()||this.data[0].isa(MML.mi)?(prev=this.data[0].setTeXclass(prev),this.updateTeXclass(this.Core())):(this.data[0].setTeXclass(),prev=this):prev=this;for(var i=1,m=this.data.length;i<m;i++)this.data[i]&&this.data[i].setTeXclass();return prev},setSeparateTeXclasses:function(prev){this.getPrevClass(prev);for(var i=0,m=this.data.length;i<m;i++)this.data[i]&&this.data[i].setTeXclass();return this.isEmbellished()&&this.updateTeXclass(this.Core()),this}}),MML.mi=MML.mbase.Subclass({type:"mi",isToken:!0,texClass:MML.TEXCLASS.ORD,defaults:{mathvariant:MML.AUTO,mathsize:MML.INHERIT,mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT},autoDefault:function(name){if("mathvariant"===name){var mi=(this.data[0]||"").toString();return 1===mi.length||2===mi.length&&mi.charCodeAt(0)>=55296&&mi.charCodeAt(0)<56320?MML.VARIANT.ITALIC:MML.VARIANT.NORMAL}return""},setTeXclass:function(prev){this.getPrevClass(prev);var name=this.data.join("");return name.length>1&&name.match(/^[a-z][a-z0-9]*$/i)&&this.texClass===MML.TEXCLASS.ORD&&(this.texClass=MML.TEXCLASS.OP,this.autoOP=!0),this}}),MML.mn=MML.mbase.Subclass({type:"mn",isToken:!0,texClass:MML.TEXCLASS.ORD,defaults:{mathvariant:MML.INHERIT,mathsize:MML.INHERIT,mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT}}),MML.mo=MML.mbase.Subclass({type:"mo",isToken:!0,defaults:{mathvariant:MML.INHERIT,mathsize:MML.INHERIT,mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT,form:MML.AUTO,fence:MML.AUTO,separator:MML.AUTO,lspace:MML.AUTO,rspace:MML.AUTO,stretchy:MML.AUTO,symmetric:MML.AUTO,maxsize:MML.AUTO,minsize:MML.AUTO,largeop:MML.AUTO,movablelimits:MML.AUTO,accent:MML.AUTO,linebreak:MML.LINEBREAK.AUTO,lineleading:MML.INHERIT,linebreakstyle:MML.AUTO,linebreakmultchar:MML.INHERIT,indentalign:MML.INHERIT,indentshift:MML.INHERIT,indenttarget:MML.INHERIT,indentalignfirst:MML.INHERIT,indentshiftfirst:MML.INHERIT,indentalignlast:MML.INHERIT,indentshiftlast:MML.INHERIT,texClass:MML.AUTO},defaultDef:{form:MML.FORM.INFIX,fence:!1,separator:!1,lspace:MML.LENGTH.THICKMATHSPACE,rspace:MML.LENGTH.THICKMATHSPACE,stretchy:!1,symmetric:!1,maxsize:MML.SIZE.INFINITY,minsize:"0em",largeop:!1,movablelimits:!1,accent:!1,linebreak:MML.LINEBREAK.AUTO,lineleading:"1ex",linebreakstyle:"before",indentalign:MML.INDENTALIGN.AUTO,indentshift:"0",indenttarget:"",indentalignfirst:MML.INDENTALIGN.INDENTALIGN,indentshiftfirst:MML.INDENTSHIFT.INDENTSHIFT,indentalignlast:MML.INDENTALIGN.INDENTALIGN,indentshiftlast:MML.INDENTSHIFT.INDENTSHIFT,texClass:MML.TEXCLASS.REL},SPACE_ATTR:{lspace:1,rspace:2},useMMLspacing:3,hasMMLspacing:function(){return!!this.useMMLspacing||this.form&&(this.OPTABLE[this.form]||{})[this.data.join("")]},autoDefault:function(name,nodefault){var def=this.def;if(!def){if("form"===name)return this.getForm();for(var mo=this.data.join(""),forms=[this.Get("form"),MML.FORM.INFIX,MML.FORM.POSTFIX,MML.FORM.PREFIX],i=0,m=forms.length;i<m;i++){var data=this.OPTABLE[forms[i]][mo];if(data){def=this.makeDef(data);break}}def||(def=this.CheckRange(mo)),!def&&nodefault?def={}:(def||(def=MathJax.Hub.Insert({},this.defaultDef)),this.parent?this.def=def:def=MathJax.Hub.Insert({},def),def.form=forms[0])}return this.useMMLspacing&=~(this.SPACE_ATTR[name]||0),null!=def[name]?def[name]:nodefault?"":this.defaultDef[name]},CheckRange:function(mo){var n=mo.charCodeAt(0);n>=55296&&n<56320&&(n=(n-55296<<10)+(mo.charCodeAt(1)-56320)+65536);for(var i=0,m=this.RANGES.length;i<m&&this.RANGES[i][0]<=n;i++)if(n<=this.RANGES[i][1]){if(this.RANGES[i][3]){var file=MML.optableDir+"/"+this.RANGES[i][3]+".js";this.RANGES[i][3]=null,MathJax.Hub.RestartAfter(MathJax.Ajax.Require(file))}var data=MML.TEXCLASSNAMES[this.RANGES[i][2]];return data=this.OPTABLE.infix[mo]=MML.mo.OPTYPES["BIN"===data?"BIN3":data],this.makeDef(data)}return null},makeDef:function(data){null==data[2]&&(data[2]=this.defaultDef.texClass),data[3]||(data[3]={});var def=MathJax.Hub.Insert({},data[3]);return def.lspace=this.SPACE[data[0]],def.rspace=this.SPACE[data[1]],def.texClass=data[2],def.texClass===MML.TEXCLASS.REL&&(this.movablelimits||this.data.join("").match(/^[a-z]+$/i))&&(def.texClass=MML.TEXCLASS.OP),def},getForm:function(){for(var core=this,parent=this.parent,Parent=this.Parent();Parent&&Parent.isEmbellished();)core=parent,parent=Parent.parent,Parent=Parent.Parent();if(parent&&"mrow"===parent.type&&1!==parent.NonSpaceLength()){if(parent.FirstNonSpace()===core)return MML.FORM.PREFIX;if(parent.LastNonSpace()===core)return MML.FORM.POSTFIX}return MML.FORM.INFIX},isEmbellished:function(){return!0},hasNewline:function(){return this.Get("linebreak")===MML.LINEBREAK.NEWLINE},CoreParent:function(){for(var parent=this;parent&&parent.isEmbellished()&&parent.CoreMO()===this&&!parent.isa(MML.math);)parent=parent.Parent();return parent},CoreText:function(parent){if(!parent)return"";if(parent.isEmbellished())return parent.CoreMO().data.join("");for(;((parent.isa(MML.mrow)||parent.isa(MML.TeXAtom)||parent.isa(MML.mstyle)||parent.isa(MML.mphantom))&&1===parent.data.length||parent.isa(MML.munderover))&&parent.data[0];)parent=parent.data[0];return parent.isToken?parent.data.join(""):""},remapChars:{"*":"∗",'"':"″","°":"∘","²":"2","³":"3","´":"′","¹":"1"},remap:function(text,map){return text=text.replace(/-/g,"−"),map&&1===(text=text.replace(/'/g,"′").replace(/`/g,"‵")).length&&(text=map[text]||text),text},setTeXclass:function(prev){var values=this.getValues("form","lspace","rspace","fence");return this.hasMMLspacing()?(this.texClass=MML.TEXCLASS.NONE,this):(values.fence&&!this.texClass&&(values.form===MML.FORM.PREFIX&&(this.texClass=MML.TEXCLASS.OPEN),values.form===MML.FORM.POSTFIX&&(this.texClass=MML.TEXCLASS.CLOSE)),this.texClass=this.Get("texClass"),"⁡"===this.data.join("")?(prev&&(prev.texClass=MML.TEXCLASS.OP,prev.fnOP=!0),this.texClass=this.prevClass=MML.TEXCLASS.NONE,prev):this.adjustTeXclass(prev))},adjustTeXclass:function(prev){if(this.texClass===MML.TEXCLASS.NONE)return prev;if(prev?(!prev.autoOP||this.texClass!==MML.TEXCLASS.BIN&&this.texClass!==MML.TEXCLASS.REL||(prev.texClass=MML.TEXCLASS.ORD),this.prevClass=prev.texClass||MML.TEXCLASS.ORD,this.prevLevel=prev.Get("scriptlevel")):this.prevClass=MML.TEXCLASS.NONE,this.texClass!==MML.TEXCLASS.BIN||this.prevClass!==MML.TEXCLASS.NONE&&this.prevClass!==MML.TEXCLASS.BIN&&this.prevClass!==MML.TEXCLASS.OP&&this.prevClass!==MML.TEXCLASS.REL&&this.prevClass!==MML.TEXCLASS.OPEN&&this.prevClass!==MML.TEXCLASS.PUNCT)if(this.prevClass!==MML.TEXCLASS.BIN||this.texClass!==MML.TEXCLASS.REL&&this.texClass!==MML.TEXCLASS.CLOSE&&this.texClass!==MML.TEXCLASS.PUNCT){if(this.texClass===MML.TEXCLASS.BIN){for(var child=this,parent=this.parent;parent&&parent.parent&&parent.isEmbellished()&&(1===parent.data.length||"mrow"!==parent.type&&parent.Core()===child);)child=parent,parent=parent.parent;parent.data[parent.data.length-1]===child&&(this.texClass=MML.TEXCLASS.ORD)}}else prev.texClass=this.prevClass=MML.TEXCLASS.ORD;else this.texClass=MML.TEXCLASS.ORD;return this}}),MML.mtext=MML.mbase.Subclass({type:"mtext",isToken:!0,isSpacelike:function(){return!0},texClass:MML.TEXCLASS.ORD,defaults:{mathvariant:MML.INHERIT,mathsize:MML.INHERIT,mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT}}),MML.mspace=MML.mbase.Subclass({type:"mspace",isToken:!0,isSpacelike:function(){return!0},defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,width:"0em",height:"0ex",depth:"0ex",linebreak:MML.LINEBREAK.AUTO},hasDimAttr:function(){return this.hasValue("width")||this.hasValue("height")||this.hasValue("depth")},hasNewline:function(){return!this.hasDimAttr()&&this.Get("linebreak")===MML.LINEBREAK.NEWLINE}}),MML.ms=MML.mbase.Subclass({type:"ms",isToken:!0,texClass:MML.TEXCLASS.ORD,defaults:{mathvariant:MML.INHERIT,mathsize:MML.INHERIT,mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT,lquote:'"',rquote:'"'}}),MML.mglyph=MML.mbase.Subclass({type:"mglyph",isToken:!0,texClass:MML.TEXCLASS.ORD,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,alt:"",src:"",width:MML.AUTO,height:MML.AUTO,valign:"0em"}}),MML.mrow=MML.mbase.Subclass({type:"mrow",isSpacelike:MML.mbase.childrenSpacelike,inferred:!1,notParent:!1,isEmbellished:function(){for(var isEmbellished=!1,i=0,m=this.data.length;i<m;i++)if(null!=this.data[i])if(this.data[i].isEmbellished()){if(isEmbellished)return!1;isEmbellished=!0,this.core=i}else if(!this.data[i].isSpacelike())return!1;return isEmbellished},NonSpaceLength:function(){for(var n=0,i=0,m=this.data.length;i<m;i++)this.data[i]&&!this.data[i].isSpacelike()&&n++;return n},FirstNonSpace:function(){for(var i=0,m=this.data.length;i<m;i++)if(this.data[i]&&!this.data[i].isSpacelike())return this.data[i];return null},LastNonSpace:function(){for(var i=this.data.length-1;i>=0;i--)if(this.data[0]&&!this.data[i].isSpacelike())return this.data[i];return null},Core:function(){return this.isEmbellished()&&void 0!==this.core?this.data[this.core]:this},CoreMO:function(){return this.isEmbellished()&&void 0!==this.core?this.data[this.core].CoreMO():this},toString:function(){return this.inferred?"["+this.data.join(",")+"]":this.SUPER(arguments).toString.call(this)},setTeXclass:function(prev){var i,m=this.data.length;if(!this.open&&!this.close||prev&&prev.fnOP){for(i=0;i<m;i++)this.data[i]&&(prev=this.data[i].setTeXclass(prev));return this.data[0]&&this.updateTeXclass(this.data[0]),prev}for(this.getPrevClass(prev),prev=null,i=0;i<m;i++)this.data[i]&&(prev=this.data[i].setTeXclass(prev));return this.hasOwnProperty("texClass")||(this.texClass=MML.TEXCLASS.INNER),this},getAnnotation:function(name){return 1!=this.data.length?null:this.data[0].getAnnotation(name)}}),MML.mfrac=MML.mbase.Subclass({type:"mfrac",num:0,den:1,linebreakContainer:!0,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,linethickness:MML.LINETHICKNESS.MEDIUM,numalign:MML.ALIGN.CENTER,denomalign:MML.ALIGN.CENTER,bevelled:!1},adjustChild_displaystyle:function(n){return!1},adjustChild_scriptlevel:function(n){var level=this.Get("scriptlevel");return(!this.Get("displaystyle")||level>0)&&level++,level},adjustChild_texprimestyle:function(n){return n==this.den||this.Get("texprimestyle")},setTeXclass:MML.mbase.setSeparateTeXclasses}),MML.msqrt=MML.mbase.Subclass({type:"msqrt",inferRow:!0,linebreakContainer:!0,texClass:MML.TEXCLASS.ORD,setTeXclass:MML.mbase.setSeparateTeXclasses,adjustChild_texprimestyle:function(n){return!0}}),MML.mroot=MML.mbase.Subclass({type:"mroot",linebreakContainer:!0,texClass:MML.TEXCLASS.ORD,adjustChild_displaystyle:function(n){return 1!==n&&this.Get("displaystyle")},adjustChild_scriptlevel:function(n){var level=this.Get("scriptlevel");return 1===n&&(level+=2),level},adjustChild_texprimestyle:function(n){return 0===n||this.Get("texprimestyle")},setTeXclass:MML.mbase.setSeparateTeXclasses}),MML.mstyle=MML.mbase.Subclass({type:"mstyle",isSpacelike:MML.mbase.childrenSpacelike,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,inferRow:!0,defaults:{scriptlevel:MML.INHERIT,displaystyle:MML.INHERIT,scriptsizemultiplier:Math.sqrt(.5),scriptminsize:"8pt",mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,dir:MML.INHERIT,infixlinebreakstyle:MML.LINEBREAKSTYLE.BEFORE,decimalseparator:"."},adjustChild_scriptlevel:function(n){var level=this.scriptlevel;if(null==level)level=this.Get("scriptlevel");else if(String(level).match(/^ *[-+]/)){var LEVEL;level=this.Get("scriptlevel",null,!0)+parseInt(level)}return level},inheritFromMe:!0,noInherit:{mpadded:{width:!0,height:!0,depth:!0,lspace:!0,voffset:!0},mtable:{width:!0,height:!0,depth:!0,align:!0}},getRemoved:{fontfamily:"fontFamily",fontweight:"fontWeight",fontstyle:"fontStyle",fontsize:"fontSize"},setTeXclass:MML.mbase.setChildTeXclass}),MML.merror=MML.mbase.Subclass({type:"merror",inferRow:!0,linebreakContainer:!0,texClass:MML.TEXCLASS.ORD}),MML.mpadded=MML.mbase.Subclass({type:"mpadded",inferRow:!0,isSpacelike:MML.mbase.childrenSpacelike,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,width:"",height:"",depth:"",lspace:0,voffset:0},setTeXclass:MML.mbase.setChildTeXclass}),MML.mphantom=MML.mbase.Subclass({type:"mphantom",texClass:MML.TEXCLASS.ORD,inferRow:!0,isSpacelike:MML.mbase.childrenSpacelike,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,setTeXclass:MML.mbase.setChildTeXclass}),MML.mfenced=MML.mbase.Subclass({type:"mfenced",defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,open:"(",close:")",separators:","},addFakeNodes:function(){var values=this.getValues("open","close","separators");if(values.open=values.open.replace(/[ \t\n\r]/g,""),values.close=values.close.replace(/[ \t\n\r]/g,""),values.separators=values.separators.replace(/[ \t\n\r]/g,""),""!==values.open&&this.SetData("open",MML.mo(values.open).With({fence:!0,form:MML.FORM.PREFIX,texClass:MML.TEXCLASS.OPEN})),""!==values.separators){for(;values.separators.length<this.data.length;)values.separators+=values.separators.charAt(values.separators.length-1);for(var i=1,m=this.data.length;i<m;i++)this.data[i]&&this.SetData("sep"+i,MML.mo(values.separators.charAt(i-1)).With({separator:!0}))}""!==values.close&&this.SetData("close",MML.mo(values.close).With({fence:!0,form:MML.FORM.POSTFIX,texClass:MML.TEXCLASS.CLOSE}))},texClass:MML.TEXCLASS.OPEN,setTeXclass:function(prev){this.addFakeNodes(),this.getPrevClass(prev),this.data.open&&(prev=this.data.open.setTeXclass(prev)),this.data[0]&&(prev=this.data[0].setTeXclass(prev));for(var i=1,m=this.data.length;i<m;i++)this.data["sep"+i]&&(prev=this.data["sep"+i].setTeXclass(prev)),this.data[i]&&(prev=this.data[i].setTeXclass(prev));return this.data.close&&(prev=this.data.close.setTeXclass(prev)),this.updateTeXclass(this.data.open),this.texClass=MML.TEXCLASS.INNER,prev}}),MML.menclose=MML.mbase.Subclass({type:"menclose",inferRow:!0,linebreakContainer:!0,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,notation:MML.NOTATION.LONGDIV,texClass:MML.TEXCLASS.ORD},setTeXclass:MML.mbase.setSeparateTeXclasses}),MML.msubsup=MML.mbase.Subclass({type:"msubsup",base:0,sub:1,sup:2,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,subscriptshift:"",superscriptshift:"",texClass:MML.AUTO},autoDefault:function(name){return"texClass"===name?this.isEmbellished()?this.CoreMO().Get(name):MML.TEXCLASS.ORD:0},adjustChild_displaystyle:function(n){return!(n>0)&&this.Get("displaystyle")},adjustChild_scriptlevel:function(n){var level=this.Get("scriptlevel");return n>0&&level++,level},adjustChild_texprimestyle:function(n){return n===this.sub||this.Get("texprimestyle")},setTeXclass:MML.mbase.setBaseTeXclasses}),MML.msub=MML.msubsup.Subclass({type:"msub"}),MML.msup=MML.msubsup.Subclass({type:"msup",sub:2,sup:1}),MML.mmultiscripts=MML.msubsup.Subclass({type:"mmultiscripts",adjustChild_texprimestyle:function(n){return n%2==1||this.Get("texprimestyle")}}),MML.mprescripts=MML.mbase.Subclass({type:"mprescripts"}),MML.none=MML.mbase.Subclass({type:"none"}),MML.munderover=MML.mbase.Subclass({type:"munderover",base:0,under:1,over:2,sub:1,sup:2,ACCENTS:["","accentunder","accent"],linebreakContainer:!0,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,accent:MML.AUTO,accentunder:MML.AUTO,align:MML.ALIGN.CENTER,texClass:MML.AUTO,subscriptshift:"",superscriptshift:""},autoDefault:function(name){return"texClass"===name?this.isEmbellished()?this.CoreMO().Get(name):MML.TEXCLASS.ORD:"accent"===name&&this.data[this.over]?this.data[this.over].CoreMO().Get("accent"):!("accentunder"!==name||!this.data[this.under])&&this.data[this.under].CoreMO().Get("accent")},adjustChild_displaystyle:function(n){return!(n>0)&&this.Get("displaystyle")},adjustChild_scriptlevel:function(n){var level=this.Get("scriptlevel"),force=this.data[this.base]&&!this.Get("displaystyle")&&this.data[this.base].CoreMO().Get("movablelimits");return n!=this.under||!force&&this.Get("accentunder")||level++,n!=this.over||!force&&this.Get("accent")||level++,level},adjustChild_texprimestyle:function(n){return!(n!==this.base||!this.data[this.over])||this.Get("texprimestyle")},setTeXclass:MML.mbase.setBaseTeXclasses}),MML.munder=MML.munderover.Subclass({type:"munder"}),MML.mover=MML.munderover.Subclass({type:"mover",over:1,under:2,sup:1,sub:2,ACCENTS:["","accent","accentunder"]}),MML.mtable=MML.mbase.Subclass({type:"mtable",defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,align:MML.ALIGN.AXIS,rowalign:MML.ALIGN.BASELINE,columnalign:MML.ALIGN.CENTER,groupalign:"{left}",alignmentscope:!0,columnwidth:MML.WIDTH.AUTO,width:MML.WIDTH.AUTO,rowspacing:"1ex",columnspacing:".8em",rowlines:MML.LINES.NONE,columnlines:MML.LINES.NONE,frame:MML.LINES.NONE,framespacing:"0.4em 0.5ex",equalrows:!1,equalcolumns:!1,displaystyle:!1,side:MML.SIDE.RIGHT,minlabelspacing:"0.8em",texClass:MML.TEXCLASS.ORD,useHeight:1},adjustChild_displaystyle:function(){return null!=this.displaystyle?this.displaystyle:this.defaults.displaystyle},inheritFromMe:!0,noInherit:{mover:{align:!0},munder:{align:!0},munderover:{align:!0},mtable:{align:!0,rowalign:!0,columnalign:!0,groupalign:!0,alignmentscope:!0,columnwidth:!0,width:!0,rowspacing:!0,columnspacing:!0,rowlines:!0,columnlines:!0,frame:!0,framespacing:!0,equalrows:!0,equalcolumns:!0,displaystyle:!0,side:!0,minlabelspacing:!0,texClass:!0,useHeight:1}},linebreakContainer:!0,Append:function(){for(var i=0,m=arguments.length;i<m;i++)arguments[i]instanceof MML.mtr||arguments[i]instanceof MML.mlabeledtr||(arguments[i]=MML.mtr(arguments[i]));this.SUPER(arguments).Append.apply(this,arguments)},setTeXclass:MML.mbase.setSeparateTeXclasses}),MML.mtr=MML.mbase.Subclass({type:"mtr",defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,rowalign:MML.INHERIT,columnalign:MML.INHERIT,groupalign:MML.INHERIT},inheritFromMe:!0,noInherit:{mrow:{rowalign:!0,columnalign:!0,groupalign:!0},mtable:{rowalign:!0,columnalign:!0,groupalign:!0}},linebreakContainer:!0,Append:function(){for(var i=0,m=arguments.length;i<m;i++)arguments[i]instanceof MML.mtd||(arguments[i]=MML.mtd(arguments[i]));this.SUPER(arguments).Append.apply(this,arguments)},setTeXclass:MML.mbase.setSeparateTeXclasses}),MML.mtd=MML.mbase.Subclass({type:"mtd",inferRow:!0,linebreakContainer:!0,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,rowspan:1,columnspan:1,rowalign:MML.INHERIT,columnalign:MML.INHERIT,groupalign:MML.INHERIT},setTeXclass:MML.mbase.setSeparateTeXclasses}),MML.maligngroup=MML.mbase.Subclass({type:"maligngroup",isSpacelike:function(){return!0},defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,groupalign:MML.INHERIT},inheritFromMe:!0,noInherit:{mrow:{groupalign:!0},mtable:{groupalign:!0}}}),MML.malignmark=MML.mbase.Subclass({type:"malignmark",defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,edge:MML.SIDE.LEFT},isSpacelike:function(){return!0}}),MML.mlabeledtr=MML.mtr.Subclass({type:"mlabeledtr"}),MML.maction=MML.mbase.Subclass({type:"maction",defaults:{mathbackground:MML.INHERIT,mathcolor:MML.INHERIT,actiontype:MML.ACTIONTYPE.TOGGLE,selection:1},selected:function(){return this.data[this.Get("selection")-1]||MML.NULL},isEmbellished:function(){return this.selected().isEmbellished()},isSpacelike:function(){return this.selected().isSpacelike()},Core:function(){return this.selected().Core()},CoreMO:function(){return this.selected().CoreMO()},setTeXclass:function(prev){this.Get("actiontype")===MML.ACTIONTYPE.TOOLTIP&&this.data[1]&&this.data[1].setTeXclass();var selected=this.selected();return prev=selected.setTeXclass(prev),this.updateTeXclass(selected),prev}}),MML.semantics=MML.mbase.Subclass({type:"semantics",notParent:!0,isEmbellished:MML.mbase.childEmbellished,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,defaults:{definitionURL:null,encoding:null},setTeXclass:MML.mbase.setChildTeXclass,getAnnotation:function(name){var encodingList=MathJax.Hub.config.MathMenu.semanticsAnnotations[name];if(encodingList)for(var i=0,m=this.data.length;i<m;i++){var encoding=this.data[i].Get("encoding");if(encoding)for(var j=0,n=encodingList.length;j<n;j++)if(encodingList[j]===encoding)return this.data[i]}return null}}),MML.annotation=MML.mbase.Subclass({type:"annotation",isChars:!0,linebreakContainer:!0,defaults:{definitionURL:null,encoding:null,cd:"mathmlkeys",name:"",src:null}}),MML["annotation-xml"]=MML.mbase.Subclass({type:"annotation-xml",linebreakContainer:!0,defaults:{definitionURL:null,encoding:null,cd:"mathmlkeys",name:"",src:null}}),MML.math=MML.mstyle.Subclass({type:"math",defaults:{mathvariant:MML.VARIANT.NORMAL,mathsize:MML.SIZE.NORMAL,mathcolor:"",mathbackground:MML.COLOR.TRANSPARENT,dir:"ltr",scriptlevel:0,displaystyle:MML.AUTO,display:"inline",maxwidth:"",overflow:MML.OVERFLOW.LINEBREAK,altimg:"","altimg-width":"","altimg-height":"","altimg-valign":"",alttext:"",cdgroup:"",scriptsizemultiplier:Math.sqrt(.5),scriptminsize:"8px",infixlinebreakstyle:MML.LINEBREAKSTYLE.BEFORE,lineleading:"1ex",indentshift:"auto",indentalign:MML.INDENTALIGN.AUTO,indentalignfirst:MML.INDENTALIGN.INDENTALIGN,indentshiftfirst:MML.INDENTSHIFT.INDENTSHIFT,indentalignlast:MML.INDENTALIGN.INDENTALIGN,indentshiftlast:MML.INDENTSHIFT.INDENTSHIFT,decimalseparator:".",texprimestyle:!1},autoDefault:function(name){return"displaystyle"===name?"block"===this.Get("display"):""},linebreakContainer:!0,setTeXclass:MML.mbase.setChildTeXclass,getAnnotation:function(name){return 1!=this.data.length?null:this.data[0].getAnnotation(name)}}),MML.chars=MML.mbase.Subclass({type:"chars",Append:function(){this.data.push.apply(this.data,arguments)},value:function(){return this.data.join("")},toString:function(){return this.data.join("")}}),MML.entity=MML.mbase.Subclass({type:"entity",Append:function(){this.data.push.apply(this.data,arguments)},value:function(){return"#x"===this.data[0].substr(0,2)?parseInt(this.data[0].substr(2),16):"#"===this.data[0].substr(0,1)?parseInt(this.data[0].substr(1)):0},toString:function(){var n=this.value();return n<=65535?String.fromCharCode(n):(n-=65536,String.fromCharCode(55296+(n>>10))+String.fromCharCode(56320+(1023&n)))}}),MML.xml=MML.mbase.Subclass({type:"xml",Init:function(){return this.div=document.createElement("div"),this.SUPER(arguments).Init.apply(this,arguments)},Append:function(){for(var i=0,m=arguments.length;i<m;i++){var node=this.Import(arguments[i]);this.data.push(node),this.div.appendChild(node)}},Import:function(node){if(document.importNode)return document.importNode(node,!0);var nNode,i,m;if(1===node.nodeType){for(nNode=document.createElement(node.nodeName),i=0,m=node.attributes.length;i<m;i++){var attribute=node.attributes[i];attribute.specified&&null!=attribute.nodeValue&&""!=attribute.nodeValue&&nNode.setAttribute(attribute.nodeName,attribute.nodeValue),"style"===attribute.nodeName&&(nNode.style.cssText=attribute.nodeValue)}node.className&&(nNode.className=node.className)}else if(3===node.nodeType||4===node.nodeType)nNode=document.createTextNode(node.nodeValue);else{if(8!==node.nodeType)return document.createTextNode("");nNode=document.createComment(node.nodeValue)}for(i=0,m=node.childNodes.length;i<m;i++)nNode.appendChild(this.Import(node.childNodes[i]));return nNode},value:function(){return this.div},toString:function(){return this.div.innerHTML}}),MML.TeXAtom=MML.mbase.Subclass({type:"texatom",linebreakContainer:!0,inferRow:!0,notParent:!0,texClass:MML.TEXCLASS.ORD,Core:MML.mbase.childCore,CoreMO:MML.mbase.childCoreMO,isEmbellished:MML.mbase.childEmbellished,setTeXclass:function(prev){return this.data[0].setTeXclass(),this.adjustTeXclass(prev)},adjustTeXclass:MML.mo.prototype.adjustTeXclass}),MML.NULL=MML.mbase().With({type:"null"});var TEXCLASS=MML.TEXCLASS,MO={ORD:[0,0,TEXCLASS.ORD],ORD11:[1,1,TEXCLASS.ORD],ORD21:[2,1,TEXCLASS.ORD],ORD02:[0,2,TEXCLASS.ORD],ORD55:[5,5,TEXCLASS.ORD],OP:[1,2,TEXCLASS.OP,{largeop:!0,movablelimits:!0,symmetric:!0}],OPFIXED:[1,2,TEXCLASS.OP,{largeop:!0,movablelimits:!0}],INTEGRAL:[0,1,TEXCLASS.OP,{largeop:!0,symmetric:!0}],INTEGRAL2:[1,2,TEXCLASS.OP,{largeop:!0,symmetric:!0}],BIN3:[3,3,TEXCLASS.BIN],BIN4:[4,4,TEXCLASS.BIN],BIN01:[0,1,TEXCLASS.BIN],BIN5:[5,5,TEXCLASS.BIN],TALLBIN:[4,4,TEXCLASS.BIN,{stretchy:!0}],BINOP:[4,4,TEXCLASS.BIN,{largeop:!0,movablelimits:!0}],REL:[5,5,TEXCLASS.REL],REL1:[1,1,TEXCLASS.REL,{stretchy:!0}],REL4:[4,4,TEXCLASS.REL],RELSTRETCH:[5,5,TEXCLASS.REL,{stretchy:!0}],RELACCENT:[5,5,TEXCLASS.REL,{accent:!0}],WIDEREL:[5,5,TEXCLASS.REL,{accent:!0,stretchy:!0}],OPEN:[0,0,TEXCLASS.OPEN,{fence:!0,stretchy:!0,symmetric:!0}],CLOSE:[0,0,TEXCLASS.CLOSE,{fence:!0,stretchy:!0,symmetric:!0}],INNER:[0,0,TEXCLASS.INNER],PUNCT:[0,3,TEXCLASS.PUNCT],ACCENT:[0,0,TEXCLASS.ORD,{accent:!0}],WIDEACCENT:[0,0,TEXCLASS.ORD,{accent:!0,stretchy:!0}]};MML.mo.Augment({SPACE:["0em","0.1111em","0.1667em","0.2222em","0.2667em","0.3333em"],RANGES:[[32,127,TEXCLASS.REL,"BasicLatin"],[160,255,TEXCLASS.ORD,"Latin1Supplement"],[256,383,TEXCLASS.ORD],[384,591,TEXCLASS.ORD],[688,767,TEXCLASS.ORD,"SpacingModLetters"],[768,879,TEXCLASS.ORD,"CombDiacritMarks"],[880,1023,TEXCLASS.ORD,"GreekAndCoptic"],[7680,7935,TEXCLASS.ORD],[8192,8303,TEXCLASS.PUNCT,"GeneralPunctuation"],[8304,8351,TEXCLASS.ORD],[8352,8399,TEXCLASS.ORD],[8400,8447,TEXCLASS.ORD,"CombDiactForSymbols"],[8448,8527,TEXCLASS.ORD,"LetterlikeSymbols"],[8528,8591,TEXCLASS.ORD],[8592,8703,TEXCLASS.REL,"Arrows"],[8704,8959,TEXCLASS.BIN,"MathOperators"],[8960,9215,TEXCLASS.ORD,"MiscTechnical"],[9312,9471,TEXCLASS.ORD],[9472,9631,TEXCLASS.ORD],[9632,9727,TEXCLASS.ORD,"GeometricShapes"],[9984,10175,TEXCLASS.ORD,"Dingbats"],[10176,10223,TEXCLASS.ORD,"MiscMathSymbolsA"],[10224,10239,TEXCLASS.REL,"SupplementalArrowsA"],[10496,10623,TEXCLASS.REL,"SupplementalArrowsB"],[10624,10751,TEXCLASS.ORD,"MiscMathSymbolsB"],[10752,11007,TEXCLASS.BIN,"SuppMathOperators"],[11008,11263,TEXCLASS.ORD,"MiscSymbolsAndArrows"],[119808,120831,TEXCLASS.ORD]],OPTABLE:{prefix:{"∀":MO.ORD21,"∂":MO.ORD21,"∃":MO.ORD21,"∇":MO.ORD21,"∏":MO.OP,"∐":MO.OP,"∑":MO.OP,"−":MO.BIN01,"∓":MO.BIN01,"√":[1,1,TEXCLASS.ORD,{stretchy:!0}],"∠":MO.ORD,"∫":MO.INTEGRAL,"∮":MO.INTEGRAL,"⋀":MO.OP,"⋁":MO.OP,"⋂":MO.OP,"⋃":MO.OP,"⌈":MO.OPEN,"⌊":MO.OPEN,"⟨":MO.OPEN,"⟮":MO.OPEN,"⨀":MO.OP,"⨁":MO.OP,"⨂":MO.OP,"⨄":MO.OP,"⨆":MO.OP,"¬":MO.ORD21,"±":MO.BIN01,"(":MO.OPEN,"+":MO.BIN01,"-":MO.BIN01,"[":MO.OPEN,"{":MO.OPEN,"|":MO.OPEN},postfix:{"!":[1,0,TEXCLASS.CLOSE],"&":MO.ORD,"′":MO.ORD02,"‾":MO.WIDEACCENT,"⌉":MO.CLOSE,"⌋":MO.CLOSE,"⏞":MO.WIDEACCENT,"⏟":MO.WIDEACCENT,"♭":MO.ORD02,"♮":MO.ORD02,"♯":MO.ORD02,"⟩":MO.CLOSE,"⟯":MO.CLOSE,"ˆ":MO.WIDEACCENT,"ˇ":MO.WIDEACCENT,"ˉ":MO.WIDEACCENT,"ˊ":MO.ACCENT,"ˋ":MO.ACCENT,"˘":MO.ACCENT,"˙":MO.ACCENT,"˜":MO.WIDEACCENT,"̂":MO.WIDEACCENT,"¨":MO.ACCENT,"¯":MO.WIDEACCENT,")":MO.CLOSE,"]":MO.CLOSE,"^":MO.WIDEACCENT,_:MO.WIDEACCENT,"`":MO.ACCENT,"|":MO.CLOSE,"}":MO.CLOSE,"~":MO.WIDEACCENT},infix:{"":MO.ORD,"%":[3,3,TEXCLASS.ORD],"•":MO.BIN4,"…":MO.INNER,"⁄":MO.TALLBIN,"⁡":MO.ORD,"⁢":MO.ORD,"⁣":[0,0,TEXCLASS.ORD,{linebreakstyle:"after",separator:!0}],"⁤":MO.ORD,"←":MO.WIDEREL,"↑":MO.RELSTRETCH,"→":MO.WIDEREL,"↓":MO.RELSTRETCH,"↔":MO.WIDEREL,"↕":MO.RELSTRETCH,"↖":MO.RELSTRETCH,"↗":MO.RELSTRETCH,"↘":MO.RELSTRETCH,"↙":MO.RELSTRETCH,"↦":MO.WIDEREL,"↩":MO.WIDEREL,"↪":MO.WIDEREL,"↼":MO.WIDEREL,"↽":MO.WIDEREL,"⇀":MO.WIDEREL,"⇁":MO.WIDEREL,"⇌":MO.WIDEREL,"⇐":MO.WIDEREL,"⇑":MO.RELSTRETCH,"⇒":MO.WIDEREL,"⇓":MO.RELSTRETCH,"⇔":MO.WIDEREL,"⇕":MO.RELSTRETCH,"∈":MO.REL,"∉":MO.REL,"∋":MO.REL,"−":MO.BIN4,"∓":MO.BIN4,"∕":MO.TALLBIN,"∖":MO.BIN4,"∗":MO.BIN4,"∘":MO.BIN4,"∙":MO.BIN4,"∝":MO.REL,"∣":MO.REL,"∥":MO.REL,"∧":MO.BIN4,"∨":MO.BIN4,"∩":MO.BIN4,"∪":MO.BIN4,"∼":MO.REL,"≀":MO.BIN4,"≃":MO.REL,"≅":MO.REL,"≈":MO.REL,"≍":MO.REL,"≐":MO.REL,"≠":MO.REL,"≡":MO.REL,"≤":MO.REL,"≥":MO.REL,"≪":MO.REL,"≫":MO.REL,"≺":MO.REL,"≻":MO.REL,"⊂":MO.REL,"⊃":MO.REL,"⊆":MO.REL,"⊇":MO.REL,"⊎":MO.BIN4,"⊑":MO.REL,"⊒":MO.REL,"⊓":MO.BIN4,"⊔":MO.BIN4,"⊕":MO.BIN4,"⊖":MO.BIN4,"⊗":MO.BIN4,"⊘":MO.BIN4,"⊙":MO.BIN4,"⊢":MO.REL,"⊣":MO.REL,"⊤":MO.ORD55,"⊥":MO.REL,"⊨":MO.REL,"⋄":MO.BIN4,"⋅":MO.BIN4,"⋆":MO.BIN4,"⋈":MO.REL,"⋮":MO.ORD55,"⋯":MO.INNER,"⋱":[5,5,TEXCLASS.INNER],"△":MO.BIN4,"▵":MO.BIN4,"▹":MO.BIN4,"▽":MO.BIN4,"▿":MO.BIN4,"◃":MO.BIN4,"❘":MO.REL,"⟵":MO.WIDEREL,"⟶":MO.WIDEREL,"⟷":MO.WIDEREL,"⟸":MO.WIDEREL,"⟹":MO.WIDEREL,"⟺":MO.WIDEREL,"⟼":MO.WIDEREL,"⨯":MO.BIN4,"⨿":MO.BIN4,"⪯":MO.REL,"⪰":MO.REL,"±":MO.BIN4,"·":MO.BIN4,"×":MO.BIN4,"÷":MO.BIN4,"*":MO.BIN3,"+":MO.BIN4,",":[0,3,TEXCLASS.PUNCT,{linebreakstyle:"after",separator:!0}],"-":MO.BIN4,".":[3,3,TEXCLASS.ORD],"/":MO.ORD11,":":[1,2,TEXCLASS.REL],";":[0,3,TEXCLASS.PUNCT,{linebreakstyle:"after",separator:!0}],"<":MO.REL,"=":MO.REL,">":MO.REL,"?":[1,1,TEXCLASS.CLOSE],"\\":MO.ORD,"^":MO.ORD11,_:MO.ORD11,"|":[2,2,TEXCLASS.ORD,{fence:!0,stretchy:!0,symmetric:!0}],"#":MO.ORD,$:MO.ORD,".":[0,3,TEXCLASS.PUNCT,{separator:!0}],"ʹ":MO.ORD,"̀":MO.ACCENT,"́":MO.ACCENT,"̃":MO.WIDEACCENT,"̄":MO.ACCENT,"̆":MO.ACCENT,"̇":MO.ACCENT,"̈":MO.ACCENT,"̌":MO.ACCENT,"̲":MO.WIDEACCENT,"̸":MO.REL4,"―":[0,0,TEXCLASS.ORD,{stretchy:!0}],"‗":[0,0,TEXCLASS.ORD,{stretchy:!0}],"†":MO.BIN3,"‡":MO.BIN3,"⃗":MO.ACCENT,"ℑ":MO.ORD,"ℓ":MO.ORD,"℘":MO.ORD,"ℜ":MO.ORD,"∅":MO.ORD,"∞":MO.ORD,"⌅":MO.BIN3,"⌆":MO.BIN3,"⌢":MO.REL4,"⌣":MO.REL4,"〈":MO.OPEN,"〉":MO.CLOSE,"⎪":MO.ORD,"⎯":[0,0,TEXCLASS.ORD,{stretchy:!0}],"⎰":MO.OPEN,"⎱":MO.CLOSE,"─":MO.ORD,"◯":MO.BIN3,"♠":MO.ORD,"♡":MO.ORD,"♢":MO.ORD,"♣":MO.ORD,"〈":MO.OPEN,"〉":MO.CLOSE,"︷":MO.WIDEACCENT,"︸":MO.WIDEACCENT}}},{OPTYPES:MO});var OPTABLE=MML.mo.prototype.OPTABLE;OPTABLE.infix["^"]=MO.WIDEREL,OPTABLE.infix._=MO.WIDEREL,OPTABLE.prefix["∣"]=MO.OPEN,OPTABLE.prefix["∥"]=MO.OPEN,OPTABLE.postfix["∣"]=MO.CLOSE,OPTABLE.postfix["∥"]=MO.CLOSE}(MathJax.ElementJax.mml),MathJax.ElementJax.mml.loadComplete("jax.js"),MML=MathJax.ElementJax.mml,MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS,MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"↚":MO.RELACCENT,"↛":MO.RELACCENT,"↜":MO.WIDEREL,"↝":MO.WIDEREL,"↞":MO.WIDEREL,"↟":MO.WIDEREL,"↠":MO.WIDEREL,"↡":MO.RELSTRETCH,"↢":MO.WIDEREL,"↣":MO.WIDEREL,"↤":MO.WIDEREL,"↥":MO.RELSTRETCH,"↧":MO.RELSTRETCH,"↨":MO.RELSTRETCH,"↫":MO.WIDEREL,"↬":MO.WIDEREL,"↭":MO.WIDEREL,"↮":MO.RELACCENT,"↯":MO.RELSTRETCH,"↰":MO.RELSTRETCH,"↱":MO.RELSTRETCH,"↲":MO.RELSTRETCH,"↳":MO.RELSTRETCH,"↴":MO.RELSTRETCH,"↵":MO.RELSTRETCH,"↶":MO.RELACCENT,"↷":MO.RELACCENT,"↸":MO.REL,"↹":MO.WIDEREL,"↺":MO.REL,"↻":MO.REL,"↾":MO.RELSTRETCH,"↿":MO.RELSTRETCH,"⇂":MO.RELSTRETCH,"⇃":MO.RELSTRETCH,"⇄":MO.WIDEREL,"⇅":MO.RELSTRETCH,"⇆":MO.WIDEREL,"⇇":MO.WIDEREL,"⇈":MO.RELSTRETCH,"⇉":MO.WIDEREL,"⇊":MO.RELSTRETCH,"⇋":MO.WIDEREL,"⇍":MO.RELACCENT,"⇎":MO.RELACCENT,"⇏":MO.RELACCENT,"⇖":MO.RELSTRETCH,"⇗":MO.RELSTRETCH,"⇘":MO.RELSTRETCH,"⇙":MO.RELSTRETCH,"⇚":MO.WIDEREL,"⇛":MO.WIDEREL,"⇜":MO.WIDEREL,"⇝":MO.WIDEREL,"⇞":MO.REL,"⇟":MO.REL,"⇠":MO.WIDEREL,"⇡":MO.RELSTRETCH,"⇢":MO.WIDEREL,"⇣":MO.RELSTRETCH,"⇤":MO.WIDEREL,"⇥":MO.WIDEREL,"⇦":MO.WIDEREL,"⇧":MO.RELSTRETCH,"⇨":MO.WIDEREL,"⇩":MO.RELSTRETCH,"⇪":MO.RELSTRETCH,"⇫":MO.RELSTRETCH,"⇬":MO.RELSTRETCH,"⇭":MO.RELSTRETCH,"⇮":MO.RELSTRETCH,"⇯":MO.RELSTRETCH,"⇰":MO.WIDEREL,"⇱":MO.REL,"⇲":MO.REL,"⇳":MO.RELSTRETCH,"⇴":MO.RELACCENT,"⇵":MO.RELSTRETCH,"⇶":MO.WIDEREL,"⇷":MO.RELACCENT,"⇸":MO.RELACCENT,"⇹":MO.RELACCENT,"⇺":MO.RELACCENT,"⇻":MO.RELACCENT,"⇼":MO.RELACCENT,"⇽":MO.WIDEREL,"⇾":MO.WIDEREL,"⇿":MO.WIDEREL}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/Arrows.js"),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"⟦":MO.OPEN,"⟪":MO.OPEN,"⟬":MO.OPEN},postfix:{"⟧":MO.CLOSE,"⟫":MO.CLOSE,"⟭":MO.CLOSE}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/MiscMathSymbolsA.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"❲":MO.OPEN},postfix:{"❳":MO.CLOSE}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/Dingbats.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"‖":[0,0,TEXCLASS.ORD,{fence:!0,stretchy:!0}],"‘":[0,0,TEXCLASS.OPEN,{fence:!0}],"“":[0,0,TEXCLASS.OPEN,{fence:!0}]},postfix:{"‖":[0,0,TEXCLASS.ORD,{fence:!0,stretchy:!0}],"’":[0,0,TEXCLASS.CLOSE,{fence:!0}],"”":[0,0,TEXCLASS.CLOSE,{fence:!0}]}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/GeneralPunctuation.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{postfix:{"ˍ":MO.WIDEACCENT,"˚":MO.ACCENT,"˝":MO.ACCENT,"˷":MO.WIDEACCENT}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/SpacingModLetters.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{postfix:{"⎴":MO.WIDEACCENT,"⎵":MO.WIDEACCENT,"⏜":MO.WIDEACCENT,"⏝":MO.WIDEACCENT,"⏠":MO.WIDEACCENT,"⏡":MO.WIDEACCENT}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/MiscTechnical.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"⟰":MO.RELSTRETCH,"⟱":MO.RELSTRETCH,"⟻":MO.WIDEREL,"⟽":MO.WIDEREL,"⟾":MO.WIDEREL,"⟿":MO.WIDEREL}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/SupplementalArrowsA.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"϶":MO.REL}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/GreekAndCoptic.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"ⅅ":MO.ORD21,"ⅆ":[2,0,TEXCLASS.ORD]}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/LetterlikeSymbols.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"⤀":MO.RELACCENT,"⤁":MO.RELACCENT,"⤂":MO.RELACCENT,"⤃":MO.RELACCENT,"⤄":MO.RELACCENT,"⤅":MO.RELACCENT,"⤆":MO.RELACCENT,"⤇":MO.RELACCENT,"⤈":MO.REL,"⤉":MO.REL,"⤊":MO.RELSTRETCH,"⤋":MO.RELSTRETCH,"⤌":MO.WIDEREL,"⤍":MO.WIDEREL,"⤎":MO.WIDEREL,"⤏":MO.WIDEREL,"⤐":MO.WIDEREL,"⤑":MO.RELACCENT,"⤒":MO.RELSTRETCH,"⤓":MO.RELSTRETCH,"⤔":MO.RELACCENT,"⤕":MO.RELACCENT,"⤖":MO.RELACCENT,"⤗":MO.RELACCENT,"⤘":MO.RELACCENT,"⤙":MO.RELACCENT,"⤚":MO.RELACCENT,"⤛":MO.RELACCENT,"⤜":MO.RELACCENT,"⤝":MO.RELACCENT,"⤞":MO.RELACCENT,"⤟":MO.RELACCENT,"⤠":MO.RELACCENT,"⤡":MO.RELSTRETCH,"⤢":MO.RELSTRETCH,"⤣":MO.REL,"⤤":MO.REL,"⤥":MO.REL,"⤦":MO.REL,"⤧":MO.REL,"⤨":MO.REL,"⤩":MO.REL,"⤪":MO.REL,"⤫":MO.REL,"⤬":MO.REL,"⤭":MO.REL,"⤮":MO.REL,"⤯":MO.REL,"⤰":MO.REL,"⤱":MO.REL,"⤲":MO.REL,"⤳":MO.RELACCENT,"⤴":MO.REL,"⤵":MO.REL,"⤶":MO.REL,"⤷":MO.REL,"⤸":MO.REL,"⤹":MO.REL,"⤺":MO.RELACCENT,"⤻":MO.RELACCENT,"⤼":MO.RELACCENT,"⤽":MO.RELACCENT,"⤾":MO.REL,"⤿":MO.REL,"⥀":MO.REL,"⥁":MO.REL,"⥂":MO.RELACCENT,"⥃":MO.RELACCENT,"⥄":MO.RELACCENT,"⥅":MO.RELACCENT,"⥆":MO.RELACCENT,"⥇":MO.RELACCENT,"⥈":MO.RELACCENT,"⥉":MO.REL,"⥊":MO.RELACCENT,"⥋":MO.RELACCENT,"⥌":MO.REL,"⥍":MO.REL,"⥎":MO.WIDEREL,"⥏":MO.RELSTRETCH,"⥐":MO.WIDEREL,"⥑":MO.RELSTRETCH,"⥒":MO.WIDEREL,"⥓":MO.WIDEREL,"⥔":MO.RELSTRETCH,"⥕":MO.RELSTRETCH,"⥖":MO.RELSTRETCH,"⥗":MO.RELSTRETCH,"⥘":MO.RELSTRETCH,"⥙":MO.RELSTRETCH,"⥚":MO.WIDEREL,"⥛":MO.WIDEREL,"⥜":MO.RELSTRETCH,"⥝":MO.RELSTRETCH,"⥞":MO.WIDEREL,"⥟":MO.WIDEREL,"⥠":MO.RELSTRETCH,"⥡":MO.RELSTRETCH,"⥢":MO.RELACCENT,"⥣":MO.REL,"⥤":MO.RELACCENT,"⥥":MO.REL,"⥦":MO.RELACCENT,"⥧":MO.RELACCENT,"⥨":MO.RELACCENT,"⥩":MO.RELACCENT,"⥪":MO.RELACCENT,"⥫":MO.RELACCENT,"⥬":MO.RELACCENT,"⥭":MO.RELACCENT,"⥮":MO.RELSTRETCH,"⥯":MO.RELSTRETCH,"⥰":MO.RELACCENT,"⥱":MO.RELACCENT,"⥲":MO.RELACCENT,"⥳":MO.RELACCENT,"⥴":MO.RELACCENT,"⥵":MO.RELACCENT,"⥶":MO.RELACCENT,"⥷":MO.RELACCENT,"⥸":MO.RELACCENT,"⥹":MO.RELACCENT,"⥺":MO.RELACCENT,"⥻":MO.RELACCENT,"⥼":MO.RELACCENT,"⥽":MO.RELACCENT,"⥾":MO.REL,"⥿":MO.REL}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/SupplementalArrowsB.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"||":[0,0,TEXCLASS.BIN,{fence:!0,stretchy:!0,symmetric:!0}],"|||":[0,0,TEXCLASS.ORD,{fence:!0,stretchy:!0,symmetric:!0}]},postfix:{"!!":[1,0,TEXCLASS.BIN],"'":MO.ACCENT,"++":[0,0,TEXCLASS.BIN],"--":[0,0,TEXCLASS.BIN],"..":[0,0,TEXCLASS.BIN],"...":MO.ORD,"||":[0,0,TEXCLASS.BIN,{fence:!0,stretchy:!0,symmetric:!0}],"|||":[0,0,TEXCLASS.ORD,{fence:!0,stretchy:!0,symmetric:!0}]},infix:{"!=":MO.BIN4,"&&":MO.BIN4,"**":[1,1,TEXCLASS.BIN],"*=":MO.BIN4,"+=":MO.BIN4,"-=":MO.BIN4,"->":MO.BIN5,"//":[1,1,TEXCLASS.BIN],"/=":MO.BIN4,":=":MO.BIN4,"<=":MO.BIN5,"<>":[1,1,TEXCLASS.BIN],"==":MO.BIN4,">=":MO.BIN5,"@":MO.ORD11,"||":[2,2,TEXCLASS.BIN,{fence:!0,stretchy:!0,symmetric:!0}],"|||":[2,2,TEXCLASS.ORD,{fence:!0,stretchy:!0,symmetric:!0}]}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/BasicLatin.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"⭅":MO.RELSTRETCH,"⭆":MO.RELSTRETCH}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/MiscSymbolsAndArrows.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{postfix:{"̑":MO.ACCENT}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/CombDiacritMarks.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"■":MO.BIN3,"□":MO.BIN3,"▪":MO.BIN3,"▫":MO.BIN3,"▭":MO.BIN3,"▮":MO.BIN3,"▯":MO.BIN3,"▰":MO.BIN3,"▱":MO.BIN3,"▲":MO.BIN4,"▴":MO.BIN4,"▶":MO.BIN4,"▷":MO.BIN4,"▸":MO.BIN4,"▼":MO.BIN4,"▾":MO.BIN4,"◀":MO.BIN4,"◁":MO.BIN4,"◂":MO.BIN4,"◄":MO.BIN4,"◅":MO.BIN4,"◆":MO.BIN4,"◇":MO.BIN4,"◈":MO.BIN4,"◉":MO.BIN4,"◌":MO.BIN4,"◍":MO.BIN4,"◎":MO.BIN4,"●":MO.BIN4,"◖":MO.BIN4,"◗":MO.BIN4,"◦":MO.BIN4}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/GeometricShapes.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"∄":MO.ORD21,"∛":MO.ORD11,"∜":MO.ORD11,"∡":MO.ORD,"∢":MO.ORD,"∬":MO.INTEGRAL,"∭":MO.INTEGRAL,"∯":MO.INTEGRAL,"∰":MO.INTEGRAL,"∱":MO.INTEGRAL,"∲":MO.INTEGRAL,"∳":MO.INTEGRAL},infix:{"∁":[1,2,TEXCLASS.ORD],"∆":MO.BIN3,"∊":MO.REL,"∌":MO.REL,"∍":MO.REL,"∎":MO.BIN3,"∔":MO.BIN4,"∟":MO.REL,"∤":MO.REL,"∦":MO.REL,"∴":MO.REL,"∵":MO.REL,"∶":MO.REL,"∷":MO.REL,"∸":MO.BIN4,"∹":MO.REL,"∺":MO.BIN4,"∻":MO.REL,"∽":MO.REL,"∽̱":MO.BIN3,"∾":MO.REL,"∿":MO.BIN3,"≁":MO.REL,"≂":MO.REL,"≂̸":MO.REL,"≄":MO.REL,"≆":MO.REL,"≇":MO.REL,"≉":MO.REL,"≊":MO.REL,"≋":MO.REL,"≌":MO.REL,"≎":MO.REL,"≎̸":MO.REL,"≏":MO.REL,"≏̸":MO.REL,"≑":MO.REL,"≒":MO.REL,"≓":MO.REL,"≔":MO.REL,"≕":MO.REL,"≖":MO.REL,"≗":MO.REL,"≘":MO.REL,"≙":MO.REL,"≚":MO.REL,"≜":MO.REL,"≝":MO.REL,"≞":MO.REL,"≟":MO.REL,"≢":MO.REL,"≣":MO.REL,"≦":MO.REL,"≦̸":MO.REL,"≧":MO.REL,"≨":MO.REL,"≩":MO.REL,"≪̸":MO.REL,"≫̸":MO.REL,"≬":MO.REL,"≭":MO.REL,"≮":MO.REL,"≯":MO.REL,"≰":MO.REL,"≱":MO.REL,"≲":MO.REL,"≳":MO.REL,"≴":MO.REL,"≵":MO.REL,"≶":MO.REL,"≷":MO.REL,"≸":MO.REL,"≹":MO.REL,"≼":MO.REL,"≽":MO.REL,"≾":MO.REL,"≿":MO.REL,"≿̸":MO.REL,"⊀":MO.REL,"⊁":MO.REL,"⊂⃒":MO.REL,"⊃⃒":MO.REL,"⊄":MO.REL,"⊅":MO.REL,"⊈":MO.REL,"⊉":MO.REL,"⊊":MO.REL,"⊋":MO.REL,"⊌":MO.BIN4,"⊍":MO.BIN4,"⊏":MO.REL,"⊏̸":MO.REL,"⊐":MO.REL,"⊐̸":MO.REL,"⊚":MO.BIN4,"⊛":MO.BIN4,"⊜":MO.BIN4,"⊝":MO.BIN4,"⊞":MO.BIN4,"⊟":MO.BIN4,"⊠":MO.BIN4,"⊡":MO.BIN4,"⊦":MO.REL,"⊧":MO.REL,"⊩":MO.REL,"⊪":MO.REL,"⊫":MO.REL,"⊬":MO.REL,"⊭":MO.REL,"⊮":MO.REL,"⊯":MO.REL,"⊰":MO.REL,"⊱":MO.REL,"⊲":MO.REL,"⊳":MO.REL,"⊴":MO.REL,"⊵":MO.REL,"⊶":MO.REL,"⊷":MO.REL,"⊸":MO.REL,"⊹":MO.REL,"⊺":MO.BIN4,"⊻":MO.BIN4,"⊼":MO.BIN4,"⊽":MO.BIN4,"⊾":MO.BIN3,"⊿":MO.BIN3,"⋇":MO.BIN4,"⋉":MO.BIN4,"⋊":MO.BIN4,"⋋":MO.BIN4,"⋌":MO.BIN4,"⋍":MO.REL,"⋎":MO.BIN4,"⋏":MO.BIN4,"⋐":MO.REL,"⋑":MO.REL,"⋒":MO.BIN4,"⋓":MO.BIN4,"⋔":MO.REL,"⋕":MO.REL,"⋖":MO.REL,"⋗":MO.REL,"⋘":MO.REL,"⋙":MO.REL,"⋚":MO.REL,"⋛":MO.REL,"⋜":MO.REL,"⋝":MO.REL,"⋞":MO.REL,"⋟":MO.REL,"⋠":MO.REL,"⋡":MO.REL,"⋢":MO.REL,"⋣":MO.REL,"⋤":MO.REL,"⋥":MO.REL,"⋦":MO.REL,"⋧":MO.REL,"⋨":MO.REL,"⋩":MO.REL,"⋪":MO.REL,"⋫":MO.REL,"⋬":MO.REL,"⋭":MO.REL,"⋰":MO.REL,"⋲":MO.REL,"⋳":MO.REL,"⋴":MO.REL,"⋵":MO.REL,"⋶":MO.REL,"⋷":MO.REL,"⋸":MO.REL,"⋹":MO.REL,"⋺":MO.REL,"⋻":MO.REL,"⋼":MO.REL,"⋽":MO.REL,"⋾":MO.REL,"⋿":MO.REL}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/MathOperators.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"⦀":[0,0,TEXCLASS.ORD,{fence:!0,stretchy:!0}],"⦃":MO.OPEN,"⦅":MO.OPEN,"⦇":MO.OPEN,"⦉":MO.OPEN,"⦋":MO.OPEN,"⦍":MO.OPEN,"⦏":MO.OPEN,"⦑":MO.OPEN,"⦓":MO.OPEN,"⦕":MO.OPEN,"⦗":MO.OPEN,"⧼":MO.OPEN},postfix:{"⦀":[0,0,TEXCLASS.ORD,{fence:!0,stretchy:!0}],"⦄":MO.CLOSE,"⦆":MO.CLOSE,"⦈":MO.CLOSE,"⦊":MO.CLOSE,"⦌":MO.CLOSE,"⦎":MO.CLOSE,"⦐":MO.CLOSE,"⦒":MO.CLOSE,"⦔":MO.CLOSE,"⦖":MO.CLOSE,"⦘":MO.CLOSE,"⧽":MO.CLOSE},infix:{"⦁":MO.BIN3,"⦂":MO.BIN3,"⦙":MO.BIN3,"⦚":MO.BIN3,"⦛":MO.BIN3,"⦜":MO.BIN3,"⦝":MO.BIN3,"⦞":MO.BIN3,"⦟":MO.BIN3,"⦠":MO.BIN3,"⦡":MO.BIN3,"⦢":MO.BIN3,"⦣":MO.BIN3,"⦤":MO.BIN3,"⦥":MO.BIN3,"⦦":MO.BIN3,"⦧":MO.BIN3,"⦨":MO.BIN3,"⦩":MO.BIN3,"⦪":MO.BIN3,"⦫":MO.BIN3,"⦬":MO.BIN3,"⦭":MO.BIN3,"⦮":MO.BIN3,"⦯":MO.BIN3,"⦰":MO.BIN3,"⦱":MO.BIN3,"⦲":MO.BIN3,"⦳":MO.BIN3,"⦴":MO.BIN3,"⦵":MO.BIN3,"⦶":MO.BIN4,"⦷":MO.BIN4,"⦸":MO.BIN4,"⦹":MO.BIN4,"⦺":MO.BIN4,"⦻":MO.BIN4,"⦼":MO.BIN4,"⦽":MO.BIN4,"⦾":MO.BIN4,"⦿":MO.BIN4,"⧀":MO.REL,"⧁":MO.REL,"⧂":MO.BIN3,"⧃":MO.BIN3,"⧄":MO.BIN4,"⧅":MO.BIN4,"⧆":MO.BIN4,"⧇":MO.BIN4,"⧈":MO.BIN4,"⧉":MO.BIN3,"⧊":MO.BIN3,"⧋":MO.BIN3,"⧌":MO.BIN3,"⧍":MO.BIN3,"⧎":MO.REL,"⧏":MO.REL,"⧏̸":MO.REL,"⧐":MO.REL,"⧐̸":MO.REL,"⧑":MO.REL,"⧒":MO.REL,"⧓":MO.REL,"⧔":MO.REL,"⧕":MO.REL,"⧖":MO.BIN4,"⧗":MO.BIN4,"⧘":MO.BIN3,"⧙":MO.BIN3,"⧛":MO.BIN3,"⧜":MO.BIN3,"⧝":MO.BIN3,"⧞":MO.REL,"⧟":MO.BIN3,"⧠":MO.BIN3,"⧡":MO.REL,"⧢":MO.BIN4,"⧣":MO.REL,"⧤":MO.REL,"⧥":MO.REL,"⧦":MO.REL,"⧧":MO.BIN3,"⧨":MO.BIN3,"⧩":MO.BIN3,"⧪":MO.BIN3,"⧫":MO.BIN3,"⧬":MO.BIN3,"⧭":MO.BIN3,"⧮":MO.BIN3,"⧯":MO.BIN3,"⧰":MO.BIN3,"⧱":MO.BIN3,"⧲":MO.BIN3,"⧳":MO.BIN3,"⧴":MO.REL,"⧵":MO.BIN4,"⧶":MO.BIN4,"⧷":MO.BIN4,"⧸":MO.BIN3,"⧹":MO.BIN3,"⧺":MO.BIN3,"⧻":MO.BIN3,"⧾":MO.BIN4,"⧿":MO.BIN4}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/MiscMathSymbolsB.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{prefix:{"⨃":MO.OP,"⨅":MO.OP,"⨇":MO.OP,"⨈":MO.OP,"⨉":MO.OP,"⨊":MO.OP,"⨋":MO.INTEGRAL2,"⨌":MO.INTEGRAL,"⨍":MO.INTEGRAL2,"⨎":MO.INTEGRAL2,"⨏":MO.INTEGRAL2,"⨐":MO.OP,"⨑":MO.OP,"⨒":MO.OP,"⨓":MO.OP,"⨔":MO.OP,"⨕":MO.INTEGRAL2,"⨖":MO.INTEGRAL2,"⨗":MO.INTEGRAL2,"⨘":MO.INTEGRAL2,"⨙":MO.INTEGRAL2,"⨚":MO.INTEGRAL2,"⨛":MO.INTEGRAL2,"⨜":MO.INTEGRAL2,"⫼":MO.OP,"⫿":MO.OP},infix:{"⨝":MO.BIN3,"⨞":MO.BIN3,"⨟":MO.BIN3,"⨠":MO.BIN3,"⨡":MO.BIN3,"⨢":MO.BIN4,"⨣":MO.BIN4,"⨤":MO.BIN4,"⨥":MO.BIN4,"⨦":MO.BIN4,"⨧":MO.BIN4,"⨨":MO.BIN4,"⨩":MO.BIN4,"⨪":MO.BIN4,"⨫":MO.BIN4,"⨬":MO.BIN4,"⨭":MO.BIN4,"⨮":MO.BIN4,"⨰":MO.BIN4,"⨱":MO.BIN4,"⨲":MO.BIN4,"⨳":MO.BIN4,"⨴":MO.BIN4,"⨵":MO.BIN4,"⨶":MO.BIN4,"⨷":MO.BIN4,"⨸":MO.BIN4,"⨹":MO.BIN4,"⨺":MO.BIN4,"⨻":MO.BIN4,"⨼":MO.BIN4,"⨽":MO.BIN4,"⨾":MO.BIN4,"⩀":MO.BIN4,"⩁":MO.BIN4,"⩂":MO.BIN4,"⩃":MO.BIN4,"⩄":MO.BIN4,"⩅":MO.BIN4,"⩆":MO.BIN4,"⩇":MO.BIN4,"⩈":MO.BIN4,"⩉":MO.BIN4,"⩊":MO.BIN4,"⩋":MO.BIN4,"⩌":MO.BIN4,"⩍":MO.BIN4,"⩎":MO.BIN4,"⩏":MO.BIN4,"⩐":MO.BIN4,"⩑":MO.BIN4,"⩒":MO.BIN4,"⩓":MO.BIN4,"⩔":MO.BIN4,"⩕":MO.BIN4,"⩖":MO.BIN4,"⩗":MO.BIN4,"⩘":MO.BIN4,"⩙":MO.REL,"⩚":MO.BIN4,"⩛":MO.BIN4,"⩜":MO.BIN4,"⩝":MO.BIN4,"⩞":MO.BIN4,"⩟":MO.BIN4,"⩠":MO.BIN4,"⩡":MO.BIN4,"⩢":MO.BIN4,"⩣":MO.BIN4,"⩤":MO.BIN4,"⩥":MO.BIN4,"⩦":MO.REL,"⩧":MO.REL,"⩨":MO.REL,"⩩":MO.REL,"⩪":MO.REL,"⩫":MO.REL,"⩬":MO.REL,"⩭":MO.REL,"⩮":MO.REL,"⩯":MO.REL,"⩰":MO.REL,"⩱":MO.BIN4,"⩲":MO.BIN4,"⩳":MO.REL,"⩴":MO.REL,"⩵":MO.REL,"⩶":MO.REL,"⩷":MO.REL,"⩸":MO.REL,"⩹":MO.REL,"⩺":MO.REL,"⩻":MO.REL,"⩼":MO.REL,"⩽":MO.REL,"⩽̸":MO.REL,"⩾":MO.REL,"⩾̸":MO.REL,"⩿":MO.REL,"⪀":MO.REL,"⪁":MO.REL,"⪂":MO.REL,"⪃":MO.REL,"⪄":MO.REL,"⪅":MO.REL,"⪆":MO.REL,"⪇":MO.REL,"⪈":MO.REL,"⪉":MO.REL,"⪊":MO.REL,"⪋":MO.REL,"⪌":MO.REL,"⪍":MO.REL,"⪎":MO.REL,"⪏":MO.REL,"⪐":MO.REL,"⪑":MO.REL,"⪒":MO.REL,"⪓":MO.REL,"⪔":MO.REL,"⪕":MO.REL,"⪖":MO.REL,"⪗":MO.REL,"⪘":MO.REL,"⪙":MO.REL,"⪚":MO.REL,"⪛":MO.REL,"⪜":MO.REL,"⪝":MO.REL,"⪞":MO.REL,"⪟":MO.REL,"⪠":MO.REL,"⪡":MO.REL,"⪡̸":MO.REL,"⪢":MO.REL,"⪢̸":MO.REL,"⪣":MO.REL,"⪤":MO.REL,"⪥":MO.REL,"⪦":MO.REL,"⪧":MO.REL,"⪨":MO.REL,"⪩":MO.REL,"⪪":MO.REL,"⪫":MO.REL,"⪬":MO.REL,"⪭":MO.REL,"⪮":MO.REL,"⪯̸":MO.REL,"⪰̸":MO.REL,"⪱":MO.REL,"⪲":MO.REL,"⪳":MO.REL,"⪴":MO.REL,"⪵":MO.REL,"⪶":MO.REL,"⪷":MO.REL,"⪸":MO.REL,"⪹":MO.REL,"⪺":MO.REL,"⪻":MO.REL,"⪼":MO.REL,"⪽":MO.REL,"⪾":MO.REL,"⪿":MO.REL,"⫀":MO.REL,"⫁":MO.REL,"⫂":MO.REL,"⫃":MO.REL,"⫄":MO.REL,"⫅":MO.REL,"⫆":MO.REL,"⫇":MO.REL,"⫈":MO.REL,"⫉":MO.REL,"⫊":MO.REL,"⫋":MO.REL,"⫌":MO.REL,"⫍":MO.REL,"⫎":MO.REL,"⫏":MO.REL,"⫐":MO.REL,"⫑":MO.REL,"⫒":MO.REL,"⫓":MO.REL,"⫔":MO.REL,"⫕":MO.REL,"⫖":MO.REL,"⫗":MO.REL,"⫘":MO.REL,"⫙":MO.REL,"⫚":MO.REL,"⫛":MO.REL,"⫝̸":MO.REL,"⫝":MO.REL,"⫞":MO.REL,"⫟":MO.REL,"⫠":MO.REL,"⫡":MO.REL,"⫢":MO.REL,"⫣":MO.REL,"⫤":MO.REL,"⫥":MO.REL,"⫦":MO.REL,"⫧":MO.REL,"⫨":MO.REL,"⫩":MO.REL,"⫪":MO.REL,"⫫":MO.REL,"⫬":MO.REL,"⫭":MO.REL,"⫮":MO.REL,"⫯":MO.REL,"⫰":MO.REL,"⫱":MO.REL,"⫲":MO.REL,"⫳":MO.REL,"⫴":MO.BIN4,"⫵":MO.BIN4,"⫶":MO.BIN4,"⫷":MO.REL,"⫸":MO.REL,"⫹":MO.REL,"⫺":MO.REL,"⫻":MO.BIN4,"⫽":MO.BIN4,"⫾":MO.BIN3}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/SuppMathOperators.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{postfix:{"⃛":MO.ACCENT,"⃜":MO.ACCENT}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/CombDiactForSymbols.js")}(MathJax.ElementJax.mml),function(MML){var MO=MML.mo.OPTYPES,TEXCLASS=MML.TEXCLASS;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{postfix:{"°":MO.ORD,"´":MO.ACCENT,"¸":MO.ACCENT}}}),MathJax.Ajax.loadComplete(MML.optableDir+"/Latin1Supplement.js")}(MathJax.ElementJax.mml),function(HUB,HTML,AJAX,CALLBACK,LOCALE,OUTPUT,INPUT){var VERSION="2.7.5",EXTENSION=MathJax.Extension,ME=EXTENSION.MathEvents={version:"2.7.5"},SETTINGS=HUB.config.menuSettings,CONFIG={hover:500,frame:{x:3.5,y:5,bwidth:1,bcolor:"#A6D",hwidth:"15px",hcolor:"#83A"},button:{x:-6,y:-3,wx:-2},fadeinInc:.2,fadeoutInc:.05,fadeDelay:50,fadeoutStart:400,fadeoutDelay:15e3,styles:{".MathJax_Hover_Frame":{"border-radius":".25em","-webkit-border-radius":".25em","-moz-border-radius":".25em","-khtml-border-radius":".25em","box-shadow":"0px 0px 15px #83A","-webkit-box-shadow":"0px 0px 15px #83A","-moz-box-shadow":"0px 0px 15px #83A","-khtml-box-shadow":"0px 0px 15px #83A",border:"1px solid #A6D ! important",display:"inline-block",position:"absolute"},".MathJax_Menu_Button .MathJax_Hover_Arrow":{position:"absolute",cursor:"pointer",display:"inline-block",border:"2px solid #AAA","border-radius":"4px","-webkit-border-radius":"4px","-moz-border-radius":"4px","-khtml-border-radius":"4px","font-family":"'Courier New',Courier","font-size":"9px",color:"#F0F0F0"},".MathJax_Menu_Button .MathJax_Hover_Arrow span":{display:"block","background-color":"#AAA",border:"1px solid","border-radius":"3px","line-height":0,padding:"4px"},".MathJax_Hover_Arrow:hover":{color:"white!important",border:"2px solid #CCC!important"},".MathJax_Hover_Arrow:hover span":{"background-color":"#CCC!important"}}},EVENT=ME.Event={LEFTBUTTON:0,RIGHTBUTTON:2,MENUKEY:"altKey",KEY:{RETURN:13,ESCAPE:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40},Mousedown:function(event){return EVENT.Handler(event,"Mousedown",this)},Mouseup:function(event){return EVENT.Handler(event,"Mouseup",this)},Mousemove:function(event){return EVENT.Handler(event,"Mousemove",this)},Mouseover:function(event){return EVENT.Handler(event,"Mouseover",this)},Mouseout:function(event){return EVENT.Handler(event,"Mouseout",this)},Click:function(event){return EVENT.Handler(event,"Click",this)},DblClick:function(event){return EVENT.Handler(event,"DblClick",this)},Menu:function(event){return EVENT.Handler(event,"ContextMenu",this)},Handler:function(event,type,math){if(AJAX.loadingMathMenu)return EVENT.False(event);var jax=OUTPUT[math.jaxID];return event||(event=window.event),event.isContextMenu="ContextMenu"===type,jax[type]?jax[type](event,math):EXTENSION.MathZoom?EXTENSION.MathZoom.HandleEvent(event,type,math):void 0},False:function(event){return event||(event=window.event),event&&(event.preventDefault?event.preventDefault():event.returnValue=!1,event.stopPropagation&&event.stopPropagation(),event.cancelBubble=!0),!1},Keydown:function(event,math){event||(event=window.event),event.keyCode===EVENT.KEY.SPACE&&EVENT.ContextMenu(event,this)},ContextMenu:function(event,math,force){var JAX=OUTPUT[math.jaxID],jax=JAX.getJaxFromMath(math),show;if((null!=JAX.config.showMathMenu?JAX:HUB).config.showMathMenu&&("MathJax"===SETTINGS.context||force)){ME.msieEventBug&&(event=window.event||event),EVENT.ClearSelection(),HOVER.ClearHoverTimer(),jax.hover&&(jax.hover.remove&&(clearTimeout(jax.hover.remove),delete jax.hover.remove),jax.hover.nofade=!0);var MENU=MathJax.Menu,load,fn;if(MENU){if(MENU.loadingDomain)return EVENT.False(event);if(!(load=LOCALE.loadDomain("MathMenu"))){MENU.jax=jax;var source=MENU.menu.Find("Show Math As").submenu;source.items[0].name=jax.sourceMenuTitle,source.items[0].format=jax.sourceMenuFormat||"MathML",source.items[1].name=INPUT[jax.inputJax].sourceMenuTitle,source.items[5].disabled=!INPUT[jax.inputJax].annotationEncoding;var annotations=source.items[2];annotations.disabled=!0;var annotationItems=annotations.submenu.items,MathPlayer;annotationList=MathJax.Hub.Config.semanticsAnnotations;for(var i=0,m=annotationItems.length;i<m;i++){var name=annotationItems[i].name[1];jax.root&&null!==jax.root.getAnnotation(name)?(annotations.disabled=!1,annotationItems[i].hidden=!1):annotationItems[i].hidden=!0}return MENU.menu.Find("Math Settings","MathPlayer").hidden=!("NativeMML"===jax.outputJax&&HUB.Browser.hasMathPlayer),MENU.menu.Post(event)}MENU.loadingDomain=!0,fn=function(){delete MENU.loadingDomain}}else{if(AJAX.loadingMathMenu)return EVENT.False(event);AJAX.loadingMathMenu=!0,load=AJAX.Require("[MathJax]/extensions/MathMenu.js"),fn=function(){delete AJAX.loadingMathMenu,MathJax.Menu||(MathJax.Menu={})}}var ev={pageX:event.pageX,pageY:event.pageY,clientX:event.clientX,clientY:event.clientY};return CALLBACK.Queue(load,fn,["ContextMenu",EVENT,ev,math,force]),EVENT.False(event)}},AltContextMenu:function(event,math){var JAX=OUTPUT[math.jaxID],show=(null!=JAX.config.showMathMenu?JAX:HUB).config.showMathMenu;if(show){if(show=(null!=JAX.config.showMathMenuMSIE?JAX:HUB).config.showMathMenuMSIE,"MathJax"===SETTINGS.context&&!SETTINGS.mpContext&&show){if(!ME.noContextMenuBug||event.button!==EVENT.RIGHTBUTTON)return}else if(!event[EVENT.MENUKEY]||event.button!==EVENT.LEFTBUTTON)return;return JAX.ContextMenu(event,math,!0)}},ClearSelection:function(){ME.safariContextMenuBug&&setTimeout("window.getSelection().empty()",0),document.selection&&setTimeout("document.selection.empty()",0)},getBBox:function(span){span.appendChild(ME.topImg);var h=ME.topImg.offsetTop,d=span.offsetHeight-h,w=span.offsetWidth;return span.removeChild(ME.topImg),{w:w,h:h,d:d}}},HOVER=ME.Hover={Mouseover:function(event,math){if(SETTINGS.discoverable||"Hover"===SETTINGS.zoom){var from=event.fromElement||event.relatedTarget,to=event.toElement||event.target;if(from&&to&&(HUB.isMathJaxNode(from)!==HUB.isMathJaxNode(to)||HUB.getJaxFor(from)!==HUB.getJaxFor(to))){var jax=this.getJaxFromMath(math);return jax.hover?HOVER.ReHover(jax):HOVER.HoverTimer(jax,math),EVENT.False(event)}}},Mouseout:function(event,math){if(SETTINGS.discoverable||"Hover"===SETTINGS.zoom){var from=event.fromElement||event.relatedTarget,to=event.toElement||event.target;if(from&&to&&(HUB.isMathJaxNode(from)!==HUB.isMathJaxNode(to)||HUB.getJaxFor(from)!==HUB.getJaxFor(to))){var jax=this.getJaxFromMath(math);return jax.hover?HOVER.UnHover(jax):HOVER.ClearHoverTimer(),EVENT.False(event)}}},Mousemove:function(event,math){if(SETTINGS.discoverable||"Hover"===SETTINGS.zoom){var jax=this.getJaxFromMath(math);if(jax.hover)return;if(HOVER.lastX==event.clientX&&HOVER.lastY==event.clientY)return;return HOVER.lastX=event.clientX,HOVER.lastY=event.clientY,HOVER.HoverTimer(jax,math),EVENT.False(event)}},HoverTimer:function(jax,math){this.ClearHoverTimer(),this.hoverTimer=setTimeout(CALLBACK(["Hover",this,jax,math]),CONFIG.hover)},ClearHoverTimer:function(){this.hoverTimer&&(clearTimeout(this.hoverTimer),delete this.hoverTimer)},Hover:function(jax,math){if(!EXTENSION.MathZoom||!EXTENSION.MathZoom.Hover({},math)){var JAX=OUTPUT[jax.outputJax],span=JAX.getHoverSpan(jax,math),bbox=JAX.getHoverBBox(jax,span,math),show=(null!=JAX.config.showMathMenu?JAX:HUB).config.showMathMenu,dx=CONFIG.frame.x,dy=CONFIG.frame.y,dd=CONFIG.frame.bwidth;ME.msieBorderWidthBug&&(dd=0),jax.hover={opacity:0,id:jax.inputID+"-Hover"};var frame=HTML.Element("span",{id:jax.hover.id,isMathJax:!0,style:{display:"inline-block",width:0,height:0,position:"relative"}},[["span",{className:"MathJax_Hover_Frame",isMathJax:!0,style:{display:"inline-block",position:"absolute",top:this.Px(-bbox.h-dy-dd-(bbox.y||0)),left:this.Px(-dx-dd+(bbox.x||0)),width:this.Px(bbox.w+2*dx),height:this.Px(bbox.h+bbox.d+2*dy),opacity:0,filter:"alpha(opacity=0)"}}]]),button=HTML.Element("span",{isMathJax:!0,id:jax.hover.id+"Menu",className:"MathJax_Menu_Button",style:{display:"inline-block","z-index":1,width:0,height:0,position:"relative"}},[["span",{className:"MathJax_Hover_Arrow",isMathJax:!0,math:math,onclick:this.HoverMenu,jax:JAX.id,style:{left:this.Px(bbox.w+dx+dd+(bbox.x||0)+CONFIG.button.x),top:this.Px(-bbox.h-dy-dd-(bbox.y||0)-CONFIG.button.y),opacity:0,filter:"alpha(opacity=0)"}},[["span",{isMathJax:!0},"▼"]]]]);bbox.width&&(frame.style.width=button.style.width=bbox.width,frame.style.marginRight=button.style.marginRight="-"+bbox.width,frame.firstChild.style.width=bbox.width,button.firstChild.style.left="",button.firstChild.style.right=this.Px(CONFIG.button.wx)),span.parentNode.insertBefore(frame,span),show&&span.parentNode.insertBefore(button,span),span.style&&(span.style.position="relative"),this.ReHover(jax)}},ReHover:function(jax){jax.hover.remove&&clearTimeout(jax.hover.remove),jax.hover.remove=setTimeout(CALLBACK(["UnHover",this,jax]),CONFIG.fadeoutDelay),this.HoverFadeTimer(jax,CONFIG.fadeinInc)},UnHover:function(jax){jax.hover.nofade||this.HoverFadeTimer(jax,-CONFIG.fadeoutInc,CONFIG.fadeoutStart)},HoverFade:function(jax){delete jax.hover.timer,jax.hover.opacity=Math.max(0,Math.min(1,jax.hover.opacity+jax.hover.inc)),jax.hover.opacity=Math.floor(1e3*jax.hover.opacity)/1e3;var frame=document.getElementById(jax.hover.id),button=document.getElementById(jax.hover.id+"Menu");frame.firstChild.style.opacity=jax.hover.opacity,frame.firstChild.style.filter="alpha(opacity="+Math.floor(100*jax.hover.opacity)+")",button&&(button.firstChild.style.opacity=jax.hover.opacity,button.firstChild.style.filter=frame.style.filter),1!==jax.hover.opacity&&(jax.hover.opacity>0?this.HoverFadeTimer(jax,jax.hover.inc):(frame.parentNode.removeChild(frame),button&&button.parentNode.removeChild(button),jax.hover.remove&&clearTimeout(jax.hover.remove),delete jax.hover))},HoverFadeTimer:function(jax,inc,delay){jax.hover.inc=inc,jax.hover.timer||(jax.hover.timer=setTimeout(CALLBACK(["HoverFade",this,jax]),delay||CONFIG.fadeDelay))},HoverMenu:function(event){return event||(event=window.event),OUTPUT[this.jax].ContextMenu(event,this.math,!0)},ClearHover:function(jax){jax.hover.remove&&clearTimeout(jax.hover.remove),jax.hover.timer&&clearTimeout(jax.hover.timer),HOVER.ClearHoverTimer(),delete jax.hover},Px:function(m){return Math.abs(m)<.006?"0px":m.toFixed(2).replace(/\.?0+$/,"")+"px"},getImages:function(){var menu;SETTINGS.discoverable&&((new Image).src=CONFIG.button.src)}},TOUCH=ME.Touch={last:0,delay:500,start:function(event){var now=(new Date).getTime(),dblTap=now-TOUCH.last<TOUCH.delay&&TOUCH.up;TOUCH.last=now,TOUCH.up=!1,dblTap&&(TOUCH.timeout=setTimeout(TOUCH.menu,TOUCH.delay,event,this),event.preventDefault())},end:function(event){var now=(new Date).getTime();if(TOUCH.up=now-TOUCH.last<TOUCH.delay,TOUCH.timeout)return clearTimeout(TOUCH.timeout),delete TOUCH.timeout,TOUCH.last=0,TOUCH.up=!1,event.preventDefault(),EVENT.Handler(event.touches[0]||event.touch,"DblClick",this)},menu:function(event,math){return delete TOUCH.timeout,TOUCH.last=0,TOUCH.up=!1,EVENT.Handler(event.touches[0]||event.touch,"ContextMenu",math)}};HUB.Browser.Select({MSIE:function(browser){var mode=document.documentMode||0,isIE8=browser.versionAtLeast("8.0");ME.msieBorderWidthBug="BackCompat"===document.compatMode,ME.msieEventBug=browser.isIE9,ME.msieAlignBug=!isIE8||mode<8,mode<9&&(EVENT.LEFTBUTTON=1)},Safari:function(browser){ME.safariContextMenuBug=!0},Opera:function(browser){ME.operaPositionBug=!0},Konqueror:function(browser){ME.noContextMenuBug=!0}}),ME.topImg=ME.msieAlignBug?HTML.Element("img",{style:{width:0,height:0,position:"relative"},src:"about:blank"}):HTML.Element("span",{style:{width:0,height:0,display:"inline-block"}}),ME.operaPositionBug&&(ME.topImg.style.border="1px solid"),ME.config=CONFIG=HUB.CombineConfig("MathEvents",CONFIG);var SETFRAME=function(){var haze=CONFIG.styles[".MathJax_Hover_Frame"];haze.border=CONFIG.frame.bwidth+"px solid "+CONFIG.frame.bcolor+" ! important",haze["box-shadow"]=haze["-webkit-box-shadow"]=haze["-moz-box-shadow"]=haze["-khtml-box-shadow"]="0px 0px "+CONFIG.frame.hwidth+" "+CONFIG.frame.hcolor};CALLBACK.Queue(HUB.Register.StartupHook("End Config",{}),[SETFRAME],["getImages",HOVER],["Styles",AJAX,CONFIG.styles],["Post",HUB.Startup.signal,"MathEvents Ready"],["loadComplete",AJAX,"[MathJax]/extensions/MathEvents.js"])}(MathJax.Hub,MathJax.HTML,MathJax.Ajax,MathJax.Callback,MathJax.Localization,MathJax.OutputJax,MathJax.InputJax),function(HUB,HTML,AJAX,HTMLCSS,nMML){var VERSION="2.7.5",CONFIG=HUB.CombineConfig("MathZoom",{styles:{"#MathJax_Zoom":{position:"absolute","background-color":"#F0F0F0",overflow:"auto",display:"block","z-index":301,padding:".5em",border:"1px solid black",margin:0,"font-weight":"normal","font-style":"normal","text-align":"left","text-indent":0,"text-transform":"none","line-height":"normal","letter-spacing":"normal","word-spacing":"normal","word-wrap":"normal","white-space":"nowrap",float:"none","-webkit-box-sizing":"content-box","-moz-box-sizing":"content-box","box-sizing":"content-box","box-shadow":"5px 5px 15px #AAAAAA","-webkit-box-shadow":"5px 5px 15px #AAAAAA","-moz-box-shadow":"5px 5px 15px #AAAAAA","-khtml-box-shadow":"5px 5px 15px #AAAAAA",filter:"progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')"},"#MathJax_ZoomOverlay":{position:"absolute",left:0,top:0,"z-index":300,display:"inline-block",width:"100%",height:"100%",border:0,padding:0,margin:0,"background-color":"white",opacity:0,filter:"alpha(opacity=0)"},"#MathJax_ZoomFrame":{position:"relative",display:"inline-block",height:0,width:0},"#MathJax_ZoomEventTrap":{position:"absolute",left:0,top:0,"z-index":302,display:"inline-block",border:0,padding:0,margin:0,"background-color":"white",opacity:0,filter:"alpha(opacity=0)"}}}),FALSE,HOVER,EVENT;MathJax.Hub.Register.StartupHook("MathEvents Ready",(function(){EVENT=MathJax.Extension.MathEvents.Event,FALSE=MathJax.Extension.MathEvents.Event.False,HOVER=MathJax.Extension.MathEvents.Hover}));var ZOOM=MathJax.Extension.MathZoom={version:"2.7.5",settings:HUB.config.menuSettings,scrollSize:18,HandleEvent:function(event,type,math){return!(!ZOOM.settings.CTRL||event.ctrlKey)||(!(!ZOOM.settings.ALT||event.altKey)||(!(!ZOOM.settings.CMD||event.metaKey)||(!(!ZOOM.settings.Shift||event.shiftKey)||(!ZOOM[type]||ZOOM[type](event,math)))))},Click:function(event,math){if("Click"===this.settings.zoom)return this.Zoom(event,math)},DblClick:function(event,math){if("Double-Click"===this.settings.zoom||"DoubleClick"===this.settings.zoom)return this.Zoom(event,math)},Hover:function(event,math){return"Hover"===this.settings.zoom&&(this.Zoom(event,math),!0)},Zoom:function(event,math){this.Remove(),HOVER.ClearHoverTimer(),EVENT.ClearSelection();var JAX=MathJax.OutputJax[math.jaxID],jax=JAX.getJaxFromMath(math);jax.hover&&HOVER.UnHover(jax);var container=this.findContainer(math),Mw=Math.floor(.85*container.clientWidth),Mh=Math.max(document.body.clientHeight,document.documentElement.clientHeight);"visible"!==this.getOverflow(container)&&(Mh=Math.min(container.clientHeight,Mh)),Mh=Math.floor(.85*Mh);var div=HTML.Element("span",{id:"MathJax_ZoomFrame"},[["span",{id:"MathJax_ZoomOverlay",onmousedown:this.Remove}],["span",{id:"MathJax_Zoom",onclick:this.Remove,style:{visibility:"hidden",fontSize:this.settings.zscale}},[["span",{style:{display:"inline-block","white-space":"nowrap"}}]]]]),zoom=div.lastChild,span=zoom.firstChild,overlay=div.firstChild;math.parentNode.insertBefore(div,math),math.parentNode.insertBefore(math,div),span.addEventListener&&span.addEventListener("mousedown",this.Remove,!0);var eW=zoom.offsetWidth||zoom.clientWidth;if(Mw-=eW,Mh-=eW,zoom.style.maxWidth=Mw+"px",zoom.style.maxHeight=Mh+"px",this.msieTrapEventBug){var trap=HTML.Element("span",{id:"MathJax_ZoomEventTrap",onmousedown:this.Remove});div.insertBefore(trap,zoom)}if(this.msieZIndexBug){var tracker=HTML.addElement(document.body,"img",{src:"about:blank",id:"MathJax_ZoomTracker",width:0,height:0,style:{width:0,height:0,position:"relative"}});div.style.position="relative",div.style.zIndex=CONFIG.styles["#MathJax_ZoomOverlay"]["z-index"],div=tracker}var bbox=JAX.Zoom(jax,span,math,Mw,Mh);return this.msiePositionBug&&(this.msieSizeBug&&(zoom.style.height=bbox.zH+"px",zoom.style.width=bbox.zW+"px"),zoom.offsetHeight>Mh&&(zoom.style.height=Mh+"px",zoom.style.width=bbox.zW+this.scrollSize+"px"),zoom.offsetWidth>Mw&&(zoom.style.width=Mw+"px",zoom.style.height=bbox.zH+this.scrollSize+"px")),this.operaPositionBug&&(zoom.style.width=Math.min(Mw,bbox.zW)+"px"),zoom.offsetWidth>eW&&zoom.offsetWidth-eW<Mw&&zoom.offsetHeight-eW<Mh&&(zoom.style.overflow="visible"),this.Position(zoom,bbox),this.msieTrapEventBug&&(trap.style.height=zoom.clientHeight+"px",trap.style.width=zoom.clientWidth+"px",trap.style.left=parseFloat(zoom.style.left)+zoom.clientLeft+"px",trap.style.top=parseFloat(zoom.style.top)+zoom.clientTop+"px"),zoom.style.visibility="","Hover"===this.settings.zoom&&(overlay.onmouseover=this.Remove),window.addEventListener?addEventListener("resize",this.Resize,!1):window.attachEvent?attachEvent("onresize",this.Resize):(this.onresize=window.onresize,window.onresize=this.Resize),HUB.signal.Post(["math zoomed",jax]),FALSE(event)},Position:function(zoom,bbox){zoom.style.display="none";var XY=this.Resize(),x=XY.x,y=XY.y,W=bbox.mW;zoom.style.display="";var dx=-W-Math.floor((zoom.offsetWidth-W)/2),dy=bbox.Y;zoom.style.left=Math.max(dx,10-x)+"px",zoom.style.top=Math.max(dy,10-y)+"px",ZOOM.msiePositionBug||ZOOM.SetWH()},Resize:function(event){ZOOM.onresize&&ZOOM.onresize(event);var div=document.getElementById("MathJax_ZoomFrame"),overlay=document.getElementById("MathJax_ZoomOverlay"),xy=ZOOM.getXY(div),obj=ZOOM.findContainer(div);if("visible"!==ZOOM.getOverflow(obj)){overlay.scroll_parent=obj;var XY=ZOOM.getXY(obj);xy.x-=XY.x,xy.y-=XY.y,XY=ZOOM.getBorder(obj),xy.x-=XY.x,xy.y-=XY.y}return overlay.style.left=-xy.x+"px",overlay.style.top=-xy.y+"px",ZOOM.msiePositionBug?setTimeout(ZOOM.SetWH,0):ZOOM.SetWH(),xy},SetWH:function(){var overlay=document.getElementById("MathJax_ZoomOverlay");if(overlay){overlay.style.display="none";var doc=overlay.scroll_parent||document.documentElement||document.body;overlay.style.width=doc.scrollWidth+"px",overlay.style.height=Math.max(doc.clientHeight,doc.scrollHeight)+"px",overlay.style.display=""}},findContainer:function(obj){for(obj=obj.parentNode;obj.parentNode&&obj!==document.body&&"visible"===ZOOM.getOverflow(obj);)obj=obj.parentNode;return obj},getOverflow:window.getComputedStyle?function(obj){return getComputedStyle(obj).overflow}:function(obj){return(obj.currentStyle||{overflow:"visible"}).overflow},getBorder:function(obj){var size={thin:1,medium:2,thick:3},style=window.getComputedStyle?getComputedStyle(obj):obj.currentStyle||{borderLeftWidth:0,borderTopWidth:0},x=style.borderLeftWidth,y=style.borderTopWidth;return{x:x=size[x]?size[x]:parseInt(x),y:y=size[y]?size[y]:parseInt(y)}},getXY:function(div){var x=0,y=0,obj;for(obj=div;obj.offsetParent;)x+=obj.offsetLeft,obj=obj.offsetParent;for(ZOOM.operaPositionBug&&(div.style.border="1px solid"),obj=div;obj.offsetParent;)y+=obj.offsetTop,obj=obj.offsetParent;return ZOOM.operaPositionBug&&(div.style.border=""),{x:x,y:y}},Remove:function(event){var div=document.getElementById("MathJax_ZoomFrame");if(div){var JAX,jax=MathJax.OutputJax[div.previousSibling.jaxID].getJaxFromMath(div.previousSibling);if(HUB.signal.Post(["math unzoomed",jax]),div.parentNode.removeChild(div),(div=document.getElementById("MathJax_ZoomTracker"))&&div.parentNode.removeChild(div),ZOOM.operaRefreshBug){var overlay=HTML.addElement(document.body,"div",{style:{position:"fixed",left:0,top:0,width:"100%",height:"100%",backgroundColor:"white",opacity:0},id:"MathJax_OperaDiv"});document.body.removeChild(overlay)}window.removeEventListener?removeEventListener("resize",ZOOM.Resize,!1):window.detachEvent?detachEvent("onresize",ZOOM.Resize):(window.onresize=ZOOM.onresize,delete ZOOM.onresize)}return FALSE(event)}};HUB.Browser.Select({MSIE:function(browser){var mode=document.documentMode||0,isIE9=mode>=9;ZOOM.msiePositionBug=!isIE9,ZOOM.msieSizeBug=browser.versionAtLeast("7.0")&&(!document.documentMode||7===mode||8===mode),ZOOM.msieZIndexBug=mode<=7,ZOOM.msieInlineBlockAlignBug=mode<=7,ZOOM.msieTrapEventBug=!window.addEventListener,"BackCompat"===document.compatMode&&(ZOOM.scrollSize=52),isIE9&&delete CONFIG.styles["#MathJax_Zoom"].filter},Opera:function(browser){ZOOM.operaPositionBug=!0,ZOOM.operaRefreshBug=!0}}),ZOOM.topImg=ZOOM.msieInlineBlockAlignBug?HTML.Element("img",{style:{width:0,height:0,position:"relative"},src:"about:blank"}):HTML.Element("span",{style:{width:0,height:0,display:"inline-block"}}),(ZOOM.operaPositionBug||ZOOM.msieTopBug)&&(ZOOM.topImg.style.border="1px solid"),MathJax.Callback.Queue(["StartupHook",MathJax.Hub.Register,"Begin Styles",{}],["Styles",AJAX,CONFIG.styles],["Post",HUB.Startup.signal,"MathZoom Ready"],["loadComplete",AJAX,"[MathJax]/extensions/MathZoom.js"])}(MathJax.Hub,MathJax.HTML,MathJax.Ajax,MathJax.OutputJax["HTML-CSS"],MathJax.OutputJax.NativeMML),function(HUB,HTML,AJAX,CALLBACK,OUTPUT){var VERSION="2.7.5",SIGNAL=MathJax.Callback.Signal("menu");MathJax.Extension.MathMenu={version:"2.7.5",signal:SIGNAL};var _=function(id){return MathJax.Localization._.apply(MathJax.Localization,[["MathMenu",id]].concat([].slice.call(arguments,1)))},isArray=MathJax.Object.isArray,isPC=HUB.Browser.isPC,isMSIE=HUB.Browser.isMSIE,isIE9=(document.documentMode||0)>8,ROUND=isPC?null:"5px",CONFIG=HUB.CombineConfig("MathMenu",{delay:150,showRenderer:!0,showMathPlayer:!0,showFontMenu:!1,showContext:!1,showDiscoverable:!1,showLocale:!0,showLocaleURL:!1,semanticsAnnotations:{TeX:["TeX","LaTeX","application/x-tex"],StarMath:["StarMath 5.0"],Maple:["Maple"],ContentMathML:["MathML-Content","application/mathml-content+xml"],OpenMath:["OpenMath"]},windowSettings:{status:"no",toolbar:"no",locationbar:"no",menubar:"no",directories:"no",personalbar:"no",resizable:"yes",scrollbars:"yes",width:400,height:300,left:Math.round((screen.width-400)/2),top:Math.round((screen.height-300)/3)},styles:{"#MathJax_About":{position:"fixed",left:"50%",width:"auto","text-align":"center",border:"3px outset",padding:"1em 2em","background-color":"#DDDDDD",color:"black",cursor:"default","font-family":"message-box","font-size":"120%","font-style":"normal","text-indent":0,"text-transform":"none","line-height":"normal","letter-spacing":"normal","word-spacing":"normal","word-wrap":"normal","white-space":"nowrap",float:"none","z-index":201,"border-radius":"15px","-webkit-border-radius":"15px","-moz-border-radius":"15px","-khtml-border-radius":"15px","box-shadow":"0px 10px 20px #808080","-webkit-box-shadow":"0px 10px 20px #808080","-moz-box-shadow":"0px 10px 20px #808080","-khtml-box-shadow":"0px 10px 20px #808080",filter:"progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')"},"#MathJax_About.MathJax_MousePost":{outline:"none"},".MathJax_Menu":{position:"absolute","background-color":"white",color:"black",width:"auto",padding:isPC?"2px":"5px 0px",border:"1px solid #CCCCCC",margin:0,cursor:"default",font:"menu","text-align":"left","text-indent":0,"text-transform":"none","line-height":"normal","letter-spacing":"normal","word-spacing":"normal","word-wrap":"normal","white-space":"nowrap",float:"none","z-index":201,"border-radius":ROUND,"-webkit-border-radius":ROUND,"-moz-border-radius":ROUND,"-khtml-border-radius":ROUND,"box-shadow":"0px 10px 20px #808080","-webkit-box-shadow":"0px 10px 20px #808080","-moz-box-shadow":"0px 10px 20px #808080","-khtml-box-shadow":"0px 10px 20px #808080",filter:"progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')"},".MathJax_MenuItem":{padding:isPC?"2px 2em":"1px 2em",background:"transparent"},".MathJax_MenuArrow":{position:"absolute",right:".5em","padding-top":".25em",color:"#666666","font-family":isMSIE?"'Arial unicode MS'":null,"font-size":".75em"},".MathJax_MenuActive .MathJax_MenuArrow":{color:"white"},".MathJax_MenuArrow.RTL":{left:".5em",right:"auto"},".MathJax_MenuCheck":{position:"absolute",left:".7em","font-family":isMSIE?"'Arial unicode MS'":null},".MathJax_MenuCheck.RTL":{right:".7em",left:"auto"},".MathJax_MenuRadioCheck":{position:"absolute",left:isPC?"1em":".7em"},".MathJax_MenuRadioCheck.RTL":{right:isPC?"1em":".7em",left:"auto"},".MathJax_MenuLabel":{padding:isPC?"2px 2em 4px 1.33em":"1px 2em 3px 1.33em","font-style":"italic"},".MathJax_MenuRule":{"border-top":isPC?"1px solid #CCCCCC":"1px solid #DDDDDD",margin:isPC?"4px 1px 0px":"4px 3px"},".MathJax_MenuDisabled":{color:"GrayText"},".MathJax_MenuActive":{"background-color":isPC?"Highlight":"#606872",color:isPC?"HighlightText":"white"},".MathJax_MenuDisabled:focus, .MathJax_MenuLabel:focus":{"background-color":"#E8E8E8"},".MathJax_ContextMenu:focus":{outline:"none"},".MathJax_ContextMenu .MathJax_MenuItem:focus":{outline:"none"},"#MathJax_AboutClose":{top:".2em",right:".2em"},".MathJax_Menu .MathJax_MenuClose":{top:"-10px",left:"-10px"},".MathJax_MenuClose":{position:"absolute",cursor:"pointer",display:"inline-block",border:"2px solid #AAA","border-radius":"18px","-webkit-border-radius":"18px","-moz-border-radius":"18px","-khtml-border-radius":"18px","font-family":"'Courier New',Courier","font-size":"24px",color:"#F0F0F0"},".MathJax_MenuClose span":{display:"block","background-color":"#AAA",border:"1.5px solid","border-radius":"18px","-webkit-border-radius":"18px","-moz-border-radius":"18px","-khtml-border-radius":"18px","line-height":0,padding:"8px 0 6px"},".MathJax_MenuClose:hover":{color:"white!important",border:"2px solid #CCC!important"},".MathJax_MenuClose:hover span":{"background-color":"#CCC!important"},".MathJax_MenuClose:hover:focus":{outline:"none"}}}),FALSE,HOVER,KEY;HUB.Register.StartupHook("MathEvents Ready",(function(){FALSE=MathJax.Extension.MathEvents.Event.False,HOVER=MathJax.Extension.MathEvents.Hover,KEY=MathJax.Extension.MathEvents.Event.KEY}));var NAV=MathJax.Object.Subclass({Keydown:function(event,menu){switch(event.keyCode){case KEY.ESCAPE:this.Remove(event,menu);break;case KEY.RIGHT:this.Right(event,menu);break;case KEY.LEFT:this.Left(event,menu);break;case KEY.UP:this.Up(event,menu);break;case KEY.DOWN:this.Down(event,menu);break;case KEY.RETURN:case KEY.SPACE:this.Space(event,menu);break;default:return}return FALSE(event)},Escape:function(event,menu){},Right:function(event,menu){},Left:function(event,menu){},Up:function(event,menu){},Down:function(event,menu){},Space:function(event,menu){}},{}),MENU=MathJax.Menu=NAV.Subclass({version:"2.7.5",items:[],posted:!1,title:null,margin:5,Init:function(def){this.items=[].slice.call(arguments,0)},With:function(def){return def&&HUB.Insert(this,def),this},Post:function(event,parent,forceLTR){event||(event=window.event||{});var div=document.getElementById("MathJax_MenuFrame");div||(div=MENU.Background(this),delete ITEM.lastItem,delete ITEM.lastMenu,delete MENU.skipUp,SIGNAL.Post(["post",MENU.jax]),MENU.isRTL="rtl"===MathJax.Localization.fontDirection());var menu=HTML.Element("div",{onmouseup:MENU.Mouseup,ondblclick:FALSE,ondragstart:FALSE,onselectstart:FALSE,oncontextmenu:FALSE,menuItem:this,className:"MathJax_Menu",onkeydown:MENU.Keydown,role:"menu"});"contextmenu"!==event.type&&"mouseover"!==event.type||(menu.className+=" MathJax_ContextMenu"),forceLTR||MathJax.Localization.setCSS(menu);for(var i=0,m=this.items.length;i<m;i++)this.items[i].Create(menu);MENU.isMobile&&HTML.addElement(menu,"span",{className:"MathJax_MenuClose",menu:parent,ontouchstart:MENU.Close,ontouchend:FALSE,onmousedown:MENU.Close,onmouseup:FALSE},[["span",{},"×"]]),div.appendChild(menu),this.posted=!0,menu.offsetWidth&&(menu.style.width=menu.offsetWidth+2+"px");var x=event.pageX,y=event.pageY,bbox=document.body.getBoundingClientRect(),styles=window.getComputedStyle?window.getComputedStyle(document.body):{marginLeft:"0px"},bodyRight=bbox.right-Math.min(0,bbox.left)+parseFloat(styles.marginLeft);if(!x&&!y&&"clientX"in event&&(x=event.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,y=event.clientY+document.body.scrollTop+document.documentElement.scrollTop),parent){var side="left",mw=parent.offsetWidth;for(x=MENU.isMobile?30:mw-2,y=0;parent&&parent!==div;)x+=parent.offsetLeft,y+=parent.offsetTop,parent=parent.parentNode;MENU.isMobile||(MENU.isRTL&&x-mw-menu.offsetWidth>this.margin||!MENU.isRTL&&x+menu.offsetWidth>bodyRight-this.margin)&&(side="right",x=Math.max(this.margin,x-mw-menu.offsetWidth+6)),isPC||(menu.style["borderRadiusTop"+side]=0,menu.style["WebkitBorderRadiusTop"+side]=0,menu.style["MozBorderRadiusTop"+side]=0,menu.style["KhtmlBorderRadiusTop"+side]=0)}else{var node=MENU.CurrentNode()||event.target;if(("keydown"===event.type||!x&&!y)&&node){var offsetX=window.pageXOffset||document.documentElement.scrollLeft,offsetY=window.pageYOffset||document.documentElement.scrollTop,rect=node.getBoundingClientRect();x=(rect.right+rect.left)/2+offsetX,y=(rect.bottom+rect.top)/2+offsetY}x+menu.offsetWidth>bodyRight-this.margin&&(x=bodyRight-menu.offsetWidth-this.margin),MENU.isMobile&&(x=Math.max(5,x-Math.floor(menu.offsetWidth/2)),y-=20),MENU.skipUp=event.isContextMenu}menu.style.left=x+"px",menu.style.top=y+"px",document.selection&&document.selection.empty&&document.selection.empty();var oldX=window.pageXOffset||document.documentElement.scrollLeft,oldY=window.pageYOffset||document.documentElement.scrollTop;return MENU.Focus(menu),"keydown"===event.type&&(MENU.skipMouseoverFromKey=!0,setTimeout((function(){delete MENU.skipMouseoverFromKey}),CONFIG.delay)),window.scrollTo(oldX,oldY),FALSE(event)},Remove:function(event,menu){SIGNAL.Post(["unpost",MENU.jax]);var div=document.getElementById("MathJax_MenuFrame");return div&&(div.parentNode.removeChild(div),this.msieFixedPositionBug&&detachEvent("onresize",MENU.Resize)),MENU.jax.hover&&(delete MENU.jax.hover.nofade,HOVER.UnHover(MENU.jax)),MENU.Unfocus(menu),"mousedown"===event.type&&MENU.CurrentNode().blur(),FALSE(event)},Find:function(name){return this.FindN(1,name,[].slice.call(arguments,1))},FindId:function(name){return this.FindN(0,name,[].slice.call(arguments,1))},FindN:function(n,name,names){for(var i=0,m=this.items.length;i<m;i++)if(this.items[i].name[n]===name)return names.length?this.items[i].submenu?this.items[i].submenu.FindN(n,names[0],names.slice(1)):null:this.items[i];return null},IndexOf:function(name){return this.IndexOfN(1,name)},IndexOfId:function(name){return this.IndexOfN(0,name)},IndexOfN:function(n,name){for(var i=0,m=this.items.length;i<m;i++)if(this.items[i].name[n]===name)return i;return null},Right:function(event,menu){MENU.Right(event,menu)},Left:function(event,menu){MENU.Left(event,menu)},Up:function(event,menu){var node=menu.lastChild;node.menuItem.Activate(event,node)},Down:function(event,menu){var node=menu.firstChild;node.menuItem.Activate(event,node)},Space:function(event,menu){this.Remove(event,menu)}},{config:CONFIG,Remove:function(event){return MENU.Event(event,this,"Remove")},Mouseover:function(event){return MENU.Event(event,this,"Mouseover")},Mouseout:function(event){return MENU.Event(event,this,"Mouseout")},Mousedown:function(event){return MENU.Event(event,this,"Mousedown")},Mouseup:function(event){return MENU.Event(event,this,"Mouseup")},Keydown:function(event){return MENU.Event(event,this,"Keydown")},Touchstart:function(event){return MENU.Event(event,this,"Touchstart")},Touchend:function(event){return MENU.Event(event,this,"Touchend")},Close:function(event){return MENU.Event(event,this.menu||this.parentNode,this.menu?"Touchend":"Remove")},Event:function(event,menu,type,force){if(MENU.skipMouseover&&"Mouseover"===type&&!force)return FALSE(event);if(MENU.skipMouseoverFromKey&&"Mouseover"===type)return delete MENU.skipMouseoverFromKey,FALSE(event);if(MENU.skipUp){if(type.match(/Mouseup|Touchend/))return delete MENU.skipUp,FALSE(event);("Touchstart"===type||"Mousedown"===type&&!MENU.skipMousedown)&&delete MENU.skipUp}event||(event=window.event);var item=menu.menuItem;return item&&item[type]?item[type](event,menu):null},BGSTYLE:{position:"absolute",left:0,top:0,"z-index":200,width:"100%",height:"100%",border:0,padding:0,margin:0},Background:function(menu){var div=HTML.addElement(document.body,"div",{style:this.BGSTYLE,id:"MathJax_MenuFrame"},[["div",{style:this.BGSTYLE,menuItem:menu,onmousedown:this.Remove}]]),bg=div.firstChild;return MENU.msieBackgroundBug&&(bg.style.backgroundColor="white",bg.style.filter="alpha(opacity=0)"),MENU.msieFixedPositionBug?(div.width=div.height=0,this.Resize(),attachEvent("onresize",this.Resize)):bg.style.position="fixed",div},Resize:function(){setTimeout(MENU.SetWH,0)},SetWH:function(){var bg=document.getElementById("MathJax_MenuFrame");bg&&((bg=bg.firstChild).style.width=bg.style.height="1px",bg.style.width=document.body.scrollWidth+"px",bg.style.height=document.body.scrollHeight+"px")},posted:!1,active:null,GetNode:function(jax){var node=document.getElementById(jax.inputID+"-Frame");return node.isMathJax?node:node.firstChild},CurrentNode:function(){return MENU.GetNode(MENU.jax)},AllNodes:function(){for(var jaxs=MathJax.Hub.getAllJax(),nodes=[],i=0,jax;jax=jaxs[i];i++)nodes.push(MENU.GetNode(jax));return nodes},ActiveNode:function(){return MENU.active},FocusNode:function(node){MENU.active=node,node.focus()},Focus:function(menu){MENU.posted?MENU.ActiveNode().tabIndex=-1:MENU.Activate(menu),menu.tabIndex=0,MENU.FocusNode(menu)},Activate:function(event,menu){MENU.UnsetTabIndex(),MENU.posted=!0},Unfocus:function(){MENU.ActiveNode().tabIndex=-1,MENU.SetTabIndex(),MENU.FocusNode(MENU.CurrentNode()),MENU.posted=!1},MoveHorizontal:function(event,menu,move){if(event.shiftKey){var jaxs=MENU.AllNodes(),len=jaxs.length;if(0!==len){var next=jaxs[MENU.Mod(move(MENU.IndexOf(jaxs,MENU.CurrentNode())),len)];next!==MENU.CurrentNode()&&(MENU.menu.Remove(event,menu),MENU.jax=MathJax.Hub.getJaxFor(next),MENU.FocusNode(next),MENU.menu.Post(null))}}},Right:function(event,menu){MENU.MoveHorizontal(event,menu,(function(x){return x+1}))},Left:function(event,menu){MENU.MoveHorizontal(event,menu,(function(x){return x-1}))},UnsetTabIndex:function(){for(var jaxs=MENU.AllNodes(),j=0,jax;jax=jaxs[j];j++)jax.tabIndex>0&&(jax.oldTabIndex=jax.tabIndex),jax.tabIndex=-1},SetTabIndex:function(){for(var jaxs=MENU.AllNodes(),j=0,jax;jax=jaxs[j];j++)void 0!==jax.oldTabIndex?(jax.tabIndex=jax.oldTabIndex,delete jax.oldTabIndex):jax.tabIndex=HUB.getTabOrder(jax)},Mod:function(a,n){return(a%n+n)%n},IndexOf:Array.prototype.indexOf?function(A,item,start){return A.indexOf(item,start)}:function(A,item,start){for(var i=start||0,j=A.length;i<j;i++)if(item===A[i])return i;return-1},saveCookie:function(){HTML.Cookie.Set("menu",this.cookie)},getCookie:function(){this.cookie=HTML.Cookie.Get("menu")}});MathJax.Menu.NAV=NAV;var ITEM=MENU.ITEM=NAV.Subclass({name:"",node:null,menu:null,Attributes:function(def){return HUB.Insert({onmouseup:MENU.Mouseup,ondragstart:FALSE,onselectstart:FALSE,onselectend:FALSE,ontouchstart:MENU.Touchstart,ontouchend:MENU.Touchend,className:"MathJax_MenuItem",role:this.role,menuItem:this},def)},Create:function(menu){if(!this.hidden){var def=this.Attributes(),label=this.Label(def,menu);HTML.addElement(menu,"div",def,label)}},Name:function(){return _(this.name[0],this.name[1])},Mouseover:function(event,menu){menu.parentNode===MENU.ActiveNode().parentNode&&this.Deactivate(MENU.ActiveNode()),this.Activate(event,menu)},Mouseout:function(event,menu){this.Deactivate(menu)},Mouseup:function(event,menu){return this.Remove(event,menu)},DeactivateSubmenus:function(menu){for(var menus=document.getElementById("MathJax_MenuFrame").childNodes,items=ITEM.GetMenuNode(menu).childNodes,i=0,m=items.length;i<m;i++){var item=items[i].menuItem;item&&item.submenu&&item.submenu.posted&&item!==menu.menuItem&&item.Deactivate(items[i])}this.RemoveSubmenus(menu,menus)},RemoveSubmenus:function(menu,menus){for(var m=(menus=menus||document.getElementById("MathJax_MenuFrame").childNodes).length-1;m>=0&&ITEM.GetMenuNode(menu).menuItem!==menus[m].menuItem;)menus[m].menuItem.posted=!1,menus[m].parentNode.removeChild(menus[m]),m--},Touchstart:function(event,menu){return this.TouchEvent(event,menu,"Mousedown")},Touchend:function(event,menu){return this.TouchEvent(event,menu,"Mouseup")},TouchEvent:function(event,menu,type){return this!==ITEM.lastItem&&(ITEM.lastMenu&&MENU.Event(event,ITEM.lastMenu,"Mouseout"),MENU.Event(event,menu,"Mouseover",!0),ITEM.lastItem=this,ITEM.lastMenu=menu),this.nativeTouch?null:(MENU.Event(event,menu,type),!1)},Remove:function(event,menu){return(menu=menu.parentNode.menuItem).Remove(event,menu)},With:function(def){return def&&HUB.Insert(this,def),this},isRTL:function(){return MENU.isRTL},rtlClass:function(){return this.isRTL()?" RTL":""}},{GetMenuNode:function(item){return item.parentNode}});MENU.ENTRY=MENU.ITEM.Subclass({role:"menuitem",Attributes:function(def){return def=HUB.Insert({onmouseover:MENU.Mouseover,onmouseout:MENU.Mouseout,onmousedown:MENU.Mousedown,onkeydown:MENU.Keydown,"aria-disabled":!!this.disabled},def),def=this.SUPER(arguments).Attributes.call(this,def),this.disabled&&(def.className+=" MathJax_MenuDisabled"),def},MoveVertical:function(event,item,move){for(var menuNode=ITEM.GetMenuNode(item),items=[],i=0,allItems=menuNode.menuItem.items,it;it=allItems[i];i++)it.hidden||items.push(it);var index=MENU.IndexOf(items,this);if(-1!==index){var len=items.length,children=menuNode.childNodes;do{index=MENU.Mod(move(index),len)}while(items[index].hidden||!children[index].role||"separator"===children[index].role);this.Deactivate(item),items[index].Activate(event,children[index])}},Up:function(event,item){this.MoveVertical(event,item,(function(x){return x-1}))},Down:function(event,item){this.MoveVertical(event,item,(function(x){return x+1}))},Right:function(event,item){this.MoveHorizontal(event,item,MENU.Right,!this.isRTL())},Left:function(event,item){this.MoveHorizontal(event,item,MENU.Left,this.isRTL())},MoveHorizontal:function(event,item,move,rtl){var menuNode=ITEM.GetMenuNode(item);if(menuNode.menuItem===MENU.menu&&event.shiftKey&&move(event,item),!rtl){menuNode.menuItem!==MENU.menu&&this.Deactivate(item);for(var parentNodes=menuNode.previousSibling.childNodes,length=parentNodes.length;length--;){var parent=parentNodes[length];if(parent.menuItem.submenu&&parent.menuItem.submenu===menuNode.menuItem){MENU.Focus(parent);break}}this.RemoveSubmenus(item)}},Space:function(event,menu){this.Mouseup(event,menu)},Activate:function(event,menu){this.Deactivate(menu),this.disabled||(menu.className+=" MathJax_MenuActive"),this.DeactivateSubmenus(menu),MENU.Focus(menu)},Deactivate:function(menu){menu.className=menu.className.replace(/ MathJax_MenuActive/,"")}}),MENU.ITEM.COMMAND=MENU.ENTRY.Subclass({action:function(){},Init:function(name,action,def){isArray(name)||(name=[name,name]),this.name=name,this.action=action,this.With(def)},Label:function(def,menu){return[this.Name()]},Mouseup:function(event,menu){return this.disabled||(this.Remove(event,menu),SIGNAL.Post(["command",this]),this.action.call(this,event)),FALSE(event)}}),MENU.ITEM.SUBMENU=MENU.ENTRY.Subclass({submenu:null,marker:"►",markerRTL:"◄",Attributes:function(def){return def=HUB.Insert({"aria-haspopup":"true"},def),def=this.SUPER(arguments).Attributes.call(this,def)},Init:function(name,def){isArray(name)||(name=[name,name]),this.name=name;var i=1;def instanceof MENU.ITEM||(this.With(def),i++),this.submenu=MENU.apply(MENU,[].slice.call(arguments,i))},Label:function(def,menu){return this.submenu.posted=!1,[this.Name()+" ",["span",{className:"MathJax_MenuArrow"+this.rtlClass()},[this.isRTL()?this.markerRTL:this.marker]]]},Timer:function(event,menu){this.ClearTimer(),event={type:event.type,clientX:event.clientX,clientY:event.clientY},this.timer=setTimeout(CALLBACK(["Mouseup",this,event,menu]),CONFIG.delay)},ClearTimer:function(){this.timer&&clearTimeout(this.timer)},Touchend:function(event,menu){var forceout=this.submenu.posted,result=this.SUPER(arguments).Touchend.apply(this,arguments);return forceout&&(this.Deactivate(menu),delete ITEM.lastItem,delete ITEM.lastMenu),result},Mouseout:function(event,menu){this.submenu.posted||this.Deactivate(menu),this.ClearTimer()},Mouseover:function(event,menu){this.Activate(event,menu)},Mouseup:function(event,menu){return this.disabled||(this.submenu.posted?this.DeactivateSubmenus(menu):(this.ClearTimer(),this.submenu.Post(event,menu,this.ltr),MENU.Focus(menu))),FALSE(event)},Activate:function(event,menu){this.disabled||(this.Deactivate(menu),menu.className+=" MathJax_MenuActive"),this.submenu.posted||(this.DeactivateSubmenus(menu),MENU.isMobile||this.Timer(event,menu)),MENU.Focus(menu)},MoveVertical:function(event,item,move){this.ClearTimer(),this.SUPER(arguments).MoveVertical.apply(this,arguments)},MoveHorizontal:function(event,menu,move,rtl){if(rtl){if(!this.disabled)if(this.submenu.posted){var submenuNodes=ITEM.GetMenuNode(menu).nextSibling.childNodes;submenuNodes.length>0&&this.submenu.items[0].Activate(event,submenuNodes[0])}else this.Activate(event,menu)}else this.SUPER(arguments).MoveHorizontal.apply(this,arguments)}}),MENU.ITEM.RADIO=MENU.ENTRY.Subclass({variable:null,marker:isPC?"●":"✓",role:"menuitemradio",Attributes:function(def){var checked=CONFIG.settings[this.variable]===this.value?"true":"false";return def=HUB.Insert({"aria-checked":checked},def),def=this.SUPER(arguments).Attributes.call(this,def)},Init:function(name,variable,def){isArray(name)||(name=[name,name]),this.name=name,this.variable=variable,this.With(def),null==this.value&&(this.value=this.name[0])},Label:function(def,menu){var span={className:"MathJax_MenuRadioCheck"+this.rtlClass()};return CONFIG.settings[this.variable]!==this.value&&(span={style:{display:"none"}}),[["span",span,[this.marker]]," "+this.Name()]},Mouseup:function(event,menu){if(!this.disabled){for(var child=menu.parentNode.childNodes,i=0,m=child.length;i<m;i++){var item=child[i].menuItem;item&&item.variable===this.variable&&(child[i].firstChild.style.display="none")}menu.firstChild.display="",CONFIG.settings[this.variable]=this.value,MENU.cookie[this.variable]=CONFIG.settings[this.variable],MENU.saveCookie(),SIGNAL.Post(["radio button",this])}return this.Remove(event,menu),this.action&&!this.disabled&&this.action.call(MENU,this),FALSE(event)}}),MENU.ITEM.CHECKBOX=MENU.ENTRY.Subclass({variable:null,marker:"✓",role:"menuitemcheckbox",Attributes:function(def){var checked=CONFIG.settings[this.variable]?"true":"false";return def=HUB.Insert({"aria-checked":checked},def),def=this.SUPER(arguments).Attributes.call(this,def)},Init:function(name,variable,def){isArray(name)||(name=[name,name]),this.name=name,this.variable=variable,this.With(def)},Label:function(def,menu){var span={className:"MathJax_MenuCheck"+this.rtlClass()};return CONFIG.settings[this.variable]||(span={style:{display:"none"}}),[["span",span,[this.marker]]," "+this.Name()]},Mouseup:function(event,menu){return this.disabled||(menu.firstChild.display=CONFIG.settings[this.variable]?"none":"",CONFIG.settings[this.variable]=!CONFIG.settings[this.variable],MENU.cookie[this.variable]=CONFIG.settings[this.variable],MENU.saveCookie(),SIGNAL.Post(["checkbox",this])),this.Remove(event,menu),this.action&&!this.disabled&&this.action.call(MENU,this),FALSE(event)}}),MENU.ITEM.LABEL=MENU.ENTRY.Subclass({role:"menuitem",Init:function(name,def){isArray(name)||(name=[name,name]),this.name=name,this.With(def)},Label:function(def,menu){return def.className+=" MathJax_MenuLabel",[this.Name()]},Activate:function(event,menu){this.Deactivate(menu),MENU.Focus(menu)},Mouseup:function(event,menu){}}),MENU.ITEM.RULE=MENU.ITEM.Subclass({role:"separator",Attributes:function(def){return def=HUB.Insert({"aria-orientation":"vertical"},def),def=this.SUPER(arguments).Attributes.call(this,def)},Label:function(def,menu){return def.className+=" MathJax_MenuRule",null}}),MENU.About=function(event){var font=MENU.About.GetFont(),format=MENU.About.GetFormat(),jax=["MathJax.js v"+MathJax.fileversion,["br"]];jax.push(["div",{style:{"border-top":"groove 2px",margin:".25em 0"}}]),MENU.About.GetJax(jax,MathJax.InputJax,["InputJax","%1 Input Jax v%2"]),MENU.About.GetJax(jax,MathJax.OutputJax,["OutputJax","%1 Output Jax v%2"]),MENU.About.GetJax(jax,MathJax.ElementJax,["ElementJax","%1 Element Jax v%2"]),jax.push(["div",{style:{"border-top":"groove 2px",margin:".25em 0"}}]),MENU.About.GetJax(jax,MathJax.Extension,["Extension","%1 Extension v%2"],!0),jax.push(["div",{style:{"border-top":"groove 2px",margin:".25em 0"}}],["center",{},[HUB.Browser+" v"+HUB.Browser.version+(format?" — "+_(format.replace(/ /g,""),format):"")]]),MENU.About.div=MENU.Background(MENU.About);var about=HTML.addElement(MENU.About.div,"div",{id:"MathJax_About",tabIndex:0,onkeydown:MENU.About.Keydown},[["b",{style:{fontSize:"120%"}},["MathJax"]]," v"+MathJax.version,["br"],_(font.replace(/ /g,""),"using "+font),["br"],["br"],["span",{style:{display:"inline-block","text-align":"left","font-size":"80%","max-height":"20em",overflow:"auto","background-color":"#E4E4E4",padding:".4em .6em",border:"1px inset"},tabIndex:0},jax],["br"],["br"],["a",{href:"http://www.mathjax.org/"},["www.mathjax.org"]],["span",{className:"MathJax_MenuClose",id:"MathJax_AboutClose",onclick:MENU.About.Remove,onkeydown:MENU.About.Keydown,tabIndex:0,role:"button","aria-label":_("CloseAboutDialog","Close about MathJax dialog")},[["span",{},"×"]]]]);"mouseup"===event.type&&(about.className+=" MathJax_MousePost"),about.focus(),MathJax.Localization.setCSS(about);var doc=document.documentElement||{},H=window.innerHeight||doc.clientHeight||doc.scrollHeight||0;MENU.prototype.msieAboutBug?(about.style.width="20em",about.style.position="absolute",about.style.left=Math.floor((document.documentElement.scrollWidth-about.offsetWidth)/2)+"px",about.style.top=Math.floor((H-about.offsetHeight)/3)+document.body.scrollTop+"px"):(about.style.marginLeft=Math.floor(-about.offsetWidth/2)+"px",about.style.top=Math.floor((H-about.offsetHeight)/3)+"px")},MENU.About.Remove=function(event){MENU.About.div&&(document.body.removeChild(MENU.About.div),delete MENU.About.div)},MENU.About.Keydown=function(event){event.keyCode!==KEY.ESCAPE&&("MathJax_AboutClose"!==this.id||event.keyCode!==KEY.SPACE&&event.keyCode!==KEY.RETURN)||(MENU.About.Remove(event),MENU.CurrentNode().focus(),FALSE(event))},MENU.About.GetJax=function(jax,JAX,type,noTypeCheck){var info=[];for(var id in JAX)JAX.hasOwnProperty(id)&&JAX[id]&&(noTypeCheck&&JAX[id].version||JAX[id].isa&&JAX[id].isa(JAX))&&info.push(_(type[0],type[1],JAX[id].id||id,JAX[id].version));info.sort();for(var i=0,m=info.length;i<m;i++)jax.push(info[i],["br"]);return jax},MENU.About.GetFont=function(){var jax=MathJax.Hub.outputJax["jax/mml"][0]||{},font;return({SVG:"web SVG",CommonHTML:"web TeX","HTML-CSS":jax.imgFonts?"image":(jax.webFonts?"web":"local")+" "+jax.fontInUse}[jax.id]||"generic")+" fonts"},MENU.About.GetFormat=function(){var jax=MathJax.Hub.outputJax["jax/mml"][0]||{};if("HTML-CSS"===jax.id&&jax.webFonts&&!jax.imgFonts)return jax.allowWebFonts.replace(/otf/,"woff or otf")+" fonts"},MENU.Help=function(event){AJAX.Require("[MathJax]/extensions/HelpDialog.js",(function(){MathJax.Extension.Help.Dialog({type:event.type})}))},MENU.ShowSource=function(event){event||(event=window.event);var EVENT={screenX:event.screenX,screenY:event.screenY};if(MENU.jax)if("MathML"===this.format){var MML=MathJax.ElementJax.mml;if(MML&&void 0!==MML.mbase.prototype.toMathML)try{MENU.ShowSource.Text(MENU.jax.root.toMathML("",MENU.jax),event)}catch(err){if(!err.restart)throw err;CALLBACK.After([this,MENU.ShowSource,EVENT],err.restart)}else if(!AJAX.loadingToMathML)return AJAX.loadingToMathML=!0,MENU.ShowSource.Window(event),void CALLBACK.Queue(AJAX.Require("[MathJax]/extensions/toMathML.js"),(function(){delete AJAX.loadingToMathML,MML.mbase.prototype.toMathML||(MML.mbase.prototype.toMathML=function(){})}),[this,MENU.ShowSource,EVENT])}else if("Error"===this.format)MENU.ShowSource.Text(MENU.jax.errorText,event);else if(CONFIG.semanticsAnnotations[this.format]){var annotation=MENU.jax.root.getAnnotation(this.format);annotation.data[0]&&MENU.ShowSource.Text(annotation.data[0].toString())}else{if(null==MENU.jax.originalText)return void alert(_("NoOriginalForm","No original form available"));MENU.ShowSource.Text(MENU.jax.originalText,event)}},MENU.ShowSource.Window=function(event){if(!MENU.ShowSource.w){var def=[],DEF=CONFIG.windowSettings;for(var id in DEF)DEF.hasOwnProperty(id)&&def.push(id+"="+DEF[id]);MENU.ShowSource.w=window.open("","_blank",def.join(","))}return MENU.ShowSource.w},MENU.ShowSource.Text=function(text,event){var w=MENU.ShowSource.Window(event);delete MENU.ShowSource.w,text=(text=text.replace(/^\s*/,"").replace(/\s*$/,"")).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");var title=_("EqSource","MathJax Equation Source");if(MENU.isMobile)w.document.open(),w.document.write("<html><head><meta name='viewport' content='width=device-width, initial-scale=1.0' /><title>"+title+"</title></head><body style='font-size:85%'>"),w.document.write("<pre>"+text+"</pre>"),w.document.write("<hr><input type='button' value='"+_("Close","Close")+"' onclick='window.close()' />"),w.document.write("</body></html>"),w.document.close();else{w.document.open(),w.document.write("<html><head><title>"+title+"</title></head><body style='font-size:85%'>"),w.document.write("<table><tr><td><pre>"+text+"</pre></td></tr></table>"),w.document.write("</body></html>"),w.document.close();var table=w.document.body.firstChild;setTimeout((function(){var H=w.outerHeight-w.innerHeight||30,W=w.outerWidth-w.innerWidth||30,x,y,X;W=Math.max(140,Math.min(Math.floor(.5*screen.width),table.offsetWidth+W+25)),H=Math.max(40,Math.min(Math.floor(.5*screen.height),table.offsetHeight+H+25)),MENU.prototype.msieHeightBug&&(H+=35),w.resizeTo(W,H);try{X=event.screenX}catch(e){}event&&null!=X&&(x=Math.max(0,Math.min(event.screenX-Math.floor(W/2),screen.width-W-20)),y=Math.max(0,Math.min(event.screenY-Math.floor(H/2),screen.height-H-20)),w.moveTo(x,y))}),50)}},MENU.Scale=function(){var JAX=["CommonHTML","HTML-CSS","SVG","NativeMML","PreviewHTML"],m=JAX.length,SCALE=100,i,jax;for(i=0;i<m;i++)if(jax=OUTPUT[JAX[i]]){SCALE=jax.config.scale;break}var scale=prompt(_("ScaleMath","Scale all mathematics (compared to surrounding text) by"),SCALE+"%");if(scale)if(scale.match(/^\s*\d+(\.\d*)?\s*%?\s*$/))if(scale=parseFloat(scale)){if(scale!==SCALE){for(i=0;i<m;i++)(jax=OUTPUT[JAX[i]])&&(jax.config.scale=scale);MENU.cookie.scale=HUB.config.scale=scale,MENU.saveCookie(),HUB.Queue(["Rerender",HUB])}}else alert(_("NonZeroScale","The scale should not be zero"));else alert(_("PercentScale","The scale should be a percentage (e.g., 120%%)"))},MENU.Zoom=function(){MathJax.Extension.MathZoom||AJAX.Require("[MathJax]/extensions/MathZoom.js")},MENU.Renderer=function(){var jax=HUB.outputJax["jax/mml"];if(jax[0]!==CONFIG.settings.renderer){var BROWSER=HUB.Browser,message,MESSAGE=MENU.Renderer.Messages,warned;switch(CONFIG.settings.renderer){case"NativeMML":CONFIG.settings.warnedMML||(BROWSER.isChrome&&"24."!==BROWSER.version.substr(0,3)?message=MESSAGE.MML.WebKit:BROWSER.isSafari&&!BROWSER.versionAtLeast("5.0")?message=MESSAGE.MML.WebKit:BROWSER.isMSIE?BROWSER.hasMathPlayer||(message=MESSAGE.MML.MSIE):message=BROWSER.isEdge?MESSAGE.MML.WebKit:MESSAGE.MML[BROWSER],warned="warnedMML");break;case"SVG":CONFIG.settings.warnedSVG||BROWSER.isMSIE&&!isIE9&&(message=MESSAGE.SVG.MSIE)}if(message){if(message=_(message[0],message[1]),message+="\n\n",message+=_("SwitchAnyway","Switch the renderer anyway?\n\n(Press OK to switch, CANCEL to continue with the current renderer)"),MENU.cookie.renderer=jax[0].id,MENU.saveCookie(),!confirm(message))return MENU.cookie.renderer=CONFIG.settings.renderer=HTML.Cookie.Get("menu").renderer,void MENU.saveCookie();warned&&(MENU.cookie.warned=CONFIG.settings.warned=!0),MENU.cookie.renderer=CONFIG.settings.renderer,MENU.saveCookie()}HUB.Queue(["setRenderer",HUB,CONFIG.settings.renderer,"jax/mml"],["Rerender",HUB])}},MENU.Renderer.Messages={MML:{WebKit:["WebkitNativeMMLWarning","Your browser doesn't seem to support MathML natively, so switching to MathML output may cause the mathematics on the page to become unreadable."],MSIE:["MSIENativeMMLWarning","Internet Explorer requires the MathPlayer plugin in order to process MathML output."],Opera:["OperaNativeMMLWarning","Opera's support for MathML is limited, so switching to MathML output may cause some expressions to render poorly."],Safari:["SafariNativeMMLWarning","Your browser's native MathML does not implement all the features used by MathJax, so some expressions may not render properly."],Firefox:["FirefoxNativeMMLWarning","Your browser's native MathML does not implement all the features used by MathJax, so some expressions may not render properly."]},SVG:{MSIE:["MSIESVGWarning","SVG is not implemented in Internet Explorer prior to IE9 or when it is emulating IE8 or below. Switching to SVG output will cause the mathematics to not display properly."]}},MENU.AssistiveMML=function(item,restart){var AMML=MathJax.Extension.AssistiveMML;AMML?MathJax.Hub.Queue([(CONFIG.settings.assistiveMML?"Add":"Remove")+"AssistiveMathML",AMML]):restart||AJAX.Require("[MathJax]/extensions/AssistiveMML.js",["AssistiveMML",MENU,item,!0])},MENU.Font=function(){var HTMLCSS;OUTPUT["HTML-CSS"]&&document.location.reload()},MENU.Locale=function(){MathJax.Localization.setLocale(CONFIG.settings.locale),MathJax.Hub.Queue(["Reprocess",MathJax.Hub])},MENU.LoadLocale=function(){var url=prompt(_("LoadURL","Load translation data from this URL:"));url&&(url.match(/\.js$/)||alert(_("BadURL","The URL should be for a javascript file that defines MathJax translation data.  Javascript file names should end with '.js'")),AJAX.Require(url,(function(status){status!=AJAX.STATUS.OK&&alert(_("BadData","Failed to load translation data from %1",url))})))},MENU.MPEvents=function(item){var discoverable=CONFIG.settings.discoverable,MESSAGE=MENU.MPEvents.Messages;if(isIE9)!discoverable&&"Menu Events"===item.name[1]&&CONFIG.settings.mpContext&&alert(_.apply(_,MESSAGE.IE9warning));else{if(CONFIG.settings.mpMouse&&!confirm(_.apply(_,MESSAGE.IE8warning)))return delete MENU.cookie.mpContext,delete CONFIG.settings.mpContext,delete MENU.cookie.mpMouse,delete CONFIG.settings.mpMouse,void MENU.saveCookie();CONFIG.settings.mpContext=CONFIG.settings.mpMouse,MENU.cookie.mpContext=MENU.cookie.mpMouse=CONFIG.settings.mpMouse,MENU.saveCookie(),MathJax.Hub.Queue(["Rerender",MathJax.Hub])}},MENU.MPEvents.Messages={IE8warning:["IE8warning","This will disable the MathJax menu and zoom features, but you can Alt-Click on an expression to obtain the MathJax menu instead.\n\nReally change the MathPlayer settings?"],IE9warning:["IE9warning","The MathJax contextual menu will be disabled, but you can Alt-Click on an expression to obtain the MathJax menu instead."]},HUB.Browser.Select({MSIE:function(browser){var quirks="BackCompat"===document.compatMode,isIE8=browser.versionAtLeast("8.0")&&document.documentMode>7;MENU.Augment({margin:20,msieBackgroundBug:(document.documentMode||0)<9,msieFixedPositionBug:quirks||!isIE8,msieAboutBug:quirks,msieHeightBug:(document.documentMode||0)<9}),isIE9&&(delete CONFIG.styles["#MathJax_About"].filter,delete CONFIG.styles[".MathJax_Menu"].filter)},Firefox:function(browser){MENU.skipMouseover=browser.isMobile&&browser.versionAtLeast("6.0"),MENU.skipMousedown=browser.isMobile}}),MENU.isMobile=HUB.Browser.isMobile,MENU.noContextMenu=HUB.Browser.noContextMenu,MENU.CreateLocaleMenu=function(){if(MENU.menu){var menu=MENU.menu.Find("Language").submenu,items=menu.items,locales=[],LOCALE=MathJax.Localization.strings;for(var id in LOCALE)LOCALE.hasOwnProperty(id)&&locales.push(id);locales=locales.sort(),menu.items=[];for(var i=0,m=locales.length;i<m;i++){var title=LOCALE[locales[i]].menuTitle;title?title+=" ("+locales[i]+")":title=locales[i],menu.items.push(ITEM.RADIO([locales[i],title],"locale",{action:MENU.Locale}))}menu.items.push(items[items.length-2],items[items.length-1])}},MENU.CreateAnnotationMenu=function(){if(MENU.menu){var menu=MENU.menu.Find("Show Math As","Annotation").submenu,annotations=CONFIG.semanticsAnnotations;for(var a in annotations)annotations.hasOwnProperty(a)&&menu.items.push(ITEM.COMMAND([a,a],MENU.ShowSource,{hidden:!0,nativeTouch:!0,format:a}))}},HUB.Register.StartupHook("End Config",(function(){var settings,trigger;CONFIG.settings=HUB.config.menuSettings,void 0!==CONFIG.settings.showRenderer&&(CONFIG.showRenderer=CONFIG.settings.showRenderer),void 0!==CONFIG.settings.showFontMenu&&(CONFIG.showFontMenu=CONFIG.settings.showFontMenu),void 0!==CONFIG.settings.showContext&&(CONFIG.showContext=CONFIG.settings.showContext),MENU.getCookie(),MENU.menu=MENU(ITEM.SUBMENU(["Show","Show Math As"],ITEM.COMMAND(["MathMLcode","MathML Code"],MENU.ShowSource,{nativeTouch:!0,format:"MathML"}),ITEM.COMMAND(["Original","Original Form"],MENU.ShowSource,{nativeTouch:!0}),ITEM.SUBMENU(["Annotation","Annotation"],{disabled:!0}),ITEM.RULE(),ITEM.CHECKBOX(["texHints","Show TeX hints in MathML"],"texHints"),ITEM.CHECKBOX(["semantics","Add original form as annotation"],"semantics")),ITEM.RULE(),ITEM.SUBMENU(["Settings","Math Settings"],ITEM.SUBMENU(["ZoomTrigger","Zoom Trigger"],ITEM.RADIO(["Hover","Hover"],"zoom",{action:MENU.Zoom}),ITEM.RADIO(["Click","Click"],"zoom",{action:MENU.Zoom}),ITEM.RADIO(["DoubleClick","Double-Click"],"zoom",{action:MENU.Zoom}),ITEM.RADIO(["NoZoom","No Zoom"],"zoom",{value:"None"}),ITEM.RULE(),ITEM.LABEL(["TriggerRequires","Trigger Requires:"]),ITEM.CHECKBOX(HUB.Browser.isMac?["Option","Option"]:["Alt","Alt"],"ALT"),ITEM.CHECKBOX(["Command","Command"],"CMD",{hidden:!HUB.Browser.isMac}),ITEM.CHECKBOX(["Control","Control"],"CTRL",{hidden:HUB.Browser.isMac}),ITEM.CHECKBOX(["Shift","Shift"],"Shift")),ITEM.SUBMENU(["ZoomFactor","Zoom Factor"],ITEM.RADIO("125%","zscale"),ITEM.RADIO("133%","zscale"),ITEM.RADIO("150%","zscale"),ITEM.RADIO("175%","zscale"),ITEM.RADIO("200%","zscale"),ITEM.RADIO("250%","zscale"),ITEM.RADIO("300%","zscale"),ITEM.RADIO("400%","zscale")),ITEM.RULE(),ITEM.SUBMENU(["Renderer","Math Renderer"],{hidden:!CONFIG.showRenderer},ITEM.RADIO(["HTML-CSS","HTML-CSS"],"renderer",{action:MENU.Renderer}),ITEM.RADIO(["CommonHTML","Common HTML"],"renderer",{action:MENU.Renderer,value:"CommonHTML"}),ITEM.RADIO(["PreviewHTML","Preview HTML"],"renderer",{action:MENU.Renderer,value:"PreviewHTML"}),ITEM.RADIO(["MathML","MathML"],"renderer",{action:MENU.Renderer,value:"NativeMML"}),ITEM.RADIO(["SVG","SVG"],"renderer",{action:MENU.Renderer}),ITEM.RADIO(["PlainSource","Plain Source"],"renderer",{action:MENU.Renderer,value:"PlainSource"}),ITEM.RULE(),ITEM.CHECKBOX(["FastPreview","Fast Preview"],"FastPreview")),ITEM.SUBMENU("MathPlayer",{hidden:!HUB.Browser.isMSIE||!CONFIG.showMathPlayer,disabled:!HUB.Browser.hasMathPlayer},ITEM.LABEL(["MPHandles","Let MathPlayer Handle:"]),ITEM.CHECKBOX(["MenuEvents","Menu Events"],"mpContext",{action:MENU.MPEvents,hidden:!isIE9}),ITEM.CHECKBOX(["MouseEvents","Mouse Events"],"mpMouse",{action:MENU.MPEvents,hidden:!isIE9}),ITEM.CHECKBOX(["MenuAndMouse","Mouse and Menu Events"],"mpMouse",{action:MENU.MPEvents,hidden:isIE9})),ITEM.SUBMENU(["FontPrefs","Font Preference"],{hidden:!CONFIG.showFontMenu},ITEM.LABEL(["ForHTMLCSS","For HTML-CSS:"]),ITEM.RADIO(["Auto","Auto"],"font",{action:MENU.Font}),ITEM.RULE(),ITEM.RADIO(["TeXLocal","TeX (local)"],"font",{action:MENU.Font}),ITEM.RADIO(["TeXWeb","TeX (web)"],"font",{action:MENU.Font}),ITEM.RADIO(["TeXImage","TeX (image)"],"font",{action:MENU.Font}),ITEM.RULE(),ITEM.RADIO(["STIXLocal","STIX (local)"],"font",{action:MENU.Font}),ITEM.RADIO(["STIXWeb","STIX (web)"],"font",{action:MENU.Font}),ITEM.RULE(),ITEM.RADIO(["AsanaMathWeb","Asana Math (web)"],"font",{action:MENU.Font}),ITEM.RADIO(["GyrePagellaWeb","Gyre Pagella (web)"],"font",{action:MENU.Font}),ITEM.RADIO(["GyreTermesWeb","Gyre Termes (web)"],"font",{action:MENU.Font}),ITEM.RADIO(["LatinModernWeb","Latin Modern (web)"],"font",{action:MENU.Font}),ITEM.RADIO(["NeoEulerWeb","Neo Euler (web)"],"font",{action:MENU.Font})),ITEM.SUBMENU(["ContextMenu","Contextual Menu"],{hidden:!CONFIG.showContext},ITEM.RADIO(["MathJax","MathJax"],"context"),ITEM.RADIO(["Browser","Browser"],"context")),ITEM.COMMAND(["Scale","Scale All Math ..."],MENU.Scale),ITEM.RULE().With({hidden:!CONFIG.showDiscoverable,name:["","discover_rule"]}),ITEM.CHECKBOX(["Discoverable","Highlight on Hover"],"discoverable",{hidden:!CONFIG.showDiscoverable})),ITEM.SUBMENU(["Accessibility","Accessibility"],ITEM.CHECKBOX(["AssistiveMML","Assistive MathML"],"assistiveMML",{action:MENU.AssistiveMML}),ITEM.CHECKBOX(["InTabOrder","Include in Tab Order"],"inTabOrder")),ITEM.SUBMENU(["Locale","Language"],{hidden:!CONFIG.showLocale,ltr:!0},ITEM.RADIO("en","locale",{action:MENU.Locale}),ITEM.RULE().With({hidden:!CONFIG.showLocaleURL,name:["","localURL_rule"]}),ITEM.COMMAND(["LoadLocale","Load from URL ..."],MENU.LoadLocale,{hidden:!CONFIG.showLocaleURL})),ITEM.RULE(),ITEM.COMMAND(["About","About MathJax"],MENU.About),ITEM.COMMAND(["Help","MathJax Help"],MENU.Help)),MENU.isMobile&&(settings=CONFIG.settings,(trigger=MENU.menu.Find("Math Settings","Zoom Trigger").submenu).items[0].disabled=trigger.items[1].disabled=!0,"Hover"!==settings.zoom&&"Click"!=settings.zoom||(settings.zoom="None"),trigger.items=trigger.items.slice(0,4),navigator.appVersion.match(/[ (]Android[) ]/)&&MENU.ITEM.SUBMENU.Augment({marker:"»"})),MENU.CreateLocaleMenu(),MENU.CreateAnnotationMenu()})),MENU.showRenderer=function(show){MENU.cookie.showRenderer=CONFIG.showRenderer=show,MENU.saveCookie(),MENU.menu.Find("Math Settings","Math Renderer").hidden=!show},MENU.showMathPlayer=function(show){MENU.cookie.showMathPlayer=CONFIG.showMathPlayer=show,MENU.saveCookie(),MENU.menu.Find("Math Settings","MathPlayer").hidden=!show},MENU.showFontMenu=function(show){MENU.cookie.showFontMenu=CONFIG.showFontMenu=show,MENU.saveCookie(),MENU.menu.Find("Math Settings","Font Preference").hidden=!show},MENU.showContext=function(show){MENU.cookie.showContext=CONFIG.showContext=show,MENU.saveCookie(),MENU.menu.Find("Math Settings","Contextual Menu").hidden=!show},MENU.showDiscoverable=function(show){MENU.cookie.showDiscoverable=CONFIG.showDiscoverable=show,MENU.saveCookie(),MENU.menu.Find("Math Settings","Highlight on Hover").hidden=!show,MENU.menu.Find("Math Settings","discover_rule").hidden=!show},MENU.showLocale=function(show){MENU.cookie.showLocale=CONFIG.showLocale=show,MENU.saveCookie(),MENU.menu.Find("Language").hidden=!show},MathJax.Hub.Register.StartupHook("HTML-CSS Jax Ready",(function(){MathJax.OutputJax["HTML-CSS"].config.imageFont||(MENU.menu.Find("Math Settings","Font Preference","TeX (image)").disabled=!0)})),CALLBACK.Queue(HUB.Register.StartupHook("End Config",{}),["Styles",AJAX,CONFIG.styles],["Post",HUB.Startup.signal,"MathMenu Ready"],["loadComplete",AJAX,"[MathJax]/extensions/MathMenu.js"])}(MathJax.Hub,MathJax.HTML,MathJax.Ajax,MathJax.CallBack,MathJax.OutputJax),MathJax.Hub.Register.LoadHook("[MathJax]/jax/element/mml/jax.js",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,SETTINGS=MathJax.Hub.config.menuSettings;MML.mbase.Augment({toMathML:function(space){var inferred=this.inferred&&this.parent.inferRow;null==space&&(space="");var tag=this.type,attr=this.toMathMLattributes();if("mspace"===tag)return space+"<"+tag+attr+" />";for(var data=[],SPACE=this.isToken?"":space+(inferred?"":"  "),i=0,m=this.data.length;i<m;i++)this.data[i]?data.push(this.data[i].toMathML(SPACE)):this.isToken||this.isChars||data.push(SPACE+"<mrow />");return this.isToken||this.isChars?space+"<"+tag+attr+">"+data.join("")+"</"+tag+">":inferred?data.join("\n"):0===data.length||1===data.length&&""===data[0]?space+"<"+tag+attr+" />":space+"<"+tag+attr+">\n"+data.join("\n")+"\n"+space+"</"+tag+">"},toMathMLattributes:function(){var defaults="mstyle"===this.type?MML.math.prototype.defaults:this.defaults,names=this.attrNames||MML.copyAttributeNames,skip=MML.skipAttributes,copy=MML.copyAttributes,attr=[];if("math"!==this.type||this.attr&&"xmlns"in this.attr||attr.push('xmlns="http://www.w3.org/1998/Math/MathML"'),!this.attrNames)for(var id in defaults)skip[id]||copy[id]||!defaults.hasOwnProperty(id)||null!=this[id]&&this[id]!==defaults[id]&&this.Get(id,null,1)!==this[id]&&attr.push(id+'="'+this.toMathMLattribute(this[id])+'"');for(var i=0,m=names.length;i<m;i++)(1!==copy[names[i]]||defaults.hasOwnProperty(names[i]))&&(value=(this.attr||{})[names[i]],null==value&&(value=this[names[i]]),null!=value&&attr.push(names[i]+'="'+this.toMathMLquote(value)+'"'));return this.toMathMLclass(attr),attr.length?" "+attr.join(" "):""},toMathMLclass:function(attr){var CLASS=[];if(this.class&&CLASS.push(this.class),this.isa(MML.TeXAtom)&&SETTINGS.texHints){var TEXCLASS=["ORD","OP","BIN","REL","OPEN","CLOSE","PUNCT","INNER","VCENTER"][this.texClass];TEXCLASS&&(CLASS.push("MJX-TeXAtom-"+TEXCLASS),"OP"!==TEXCLASS||this.movablelimits||CLASS.push("MJX-fixedlimits"))}this.mathvariant&&this.toMathMLvariants[this.mathvariant]&&CLASS.push("MJX"+this.mathvariant),this.variantForm&&CLASS.push("MJX-variant"),CLASS.length&&attr.unshift('class="'+this.toMathMLquote(CLASS.join(" "))+'"')},toMathMLattribute:function(value){return"string"==typeof value&&value.replace(/ /g,"").match(/^(([-+])?(\d+(\.\d*)?|\.\d+))mu$/)?(RegExp.$2||"")+(1/18*RegExp.$3).toFixed(3).replace(/\.?0+$/,"")+"em":this.toMathMLvariants[value]?this.toMathMLvariants[value]:this.toMathMLquote(value)},toMathMLvariants:{"-tex-caligraphic":MML.VARIANT.SCRIPT,"-tex-caligraphic-bold":MML.VARIANT.BOLDSCRIPT,"-tex-oldstyle":MML.VARIANT.NORMAL,"-tex-oldstyle-bold":MML.VARIANT.BOLD,"-tex-mathit":MML.VARIANT.ITALIC},toMathMLquote:function(string){for(var i=0,m=(string=String(string).split("")).length;i<m;i++){var n=string[i].charCodeAt(0);if(n<=55295||57344<=n)if(n>126||n<32&&10!==n&&13!==n&&9!==n)string[i]="&#x"+n.toString(16).toUpperCase()+";";else{var c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"}[string[i]];c&&(string[i]=c)}else if(i+1<m){var trailSurrogate,codePoint=string[i+1].charCodeAt(0)-56320+(n-55296<<10)+65536;string[i]="&#x"+codePoint.toString(16).toUpperCase()+";",string[i+1]="",i++}else string[i]=""}return string.join("")}}),MML.math.Augment({toMathML:function(space,jax){var annotation;null==space&&(space=""),jax&&jax.originalText&&SETTINGS.semantics&&(annotation=MathJax.InputJax[jax.inputJax].annotationEncoding);for(var nested=this.data[0]&&this.data[0].data.length>1,tag=this.type,attr=this.toMathMLattributes(),data=[],SPACE=space+(annotation?"  "+(nested?"  ":""):"")+"  ",i=0,m=this.data.length;i<m;i++)this.data[i]?data.push(this.data[i].toMathML(SPACE)):data.push(SPACE+"<mrow />");if(0===data.length||1===data.length&&""===data[0]){if(!annotation)return"<"+tag+attr+" />";data.push(SPACE+"<mrow />")}if(annotation){nested&&(data.unshift(space+"    <mrow>"),data.push(space+"    </mrow>")),data.unshift(space+"  <semantics>");var xmlEscapedTex=jax.originalText.replace(/[&<>]/g,(function(item){return{">":"&gt;","<":"&lt;","&":"&amp;"}[item]}));data.push(space+'    <annotation encoding="'+this.toMathMLquote(annotation)+'">'+xmlEscapedTex+"</annotation>"),data.push(space+"  </semantics>")}return space+"<"+tag+attr+">\n"+data.join("\n")+"\n"+space+"</"+tag+">"}}),MML.msubsup.Augment({toMathML:function(space){var tag=this.type;null==this.data[this.sup]&&(tag="msub"),null==this.data[this.sub]&&(tag="msup");var attr=this.toMathMLattributes();delete this.data[0].inferred;for(var data=[],i=0,m=this.data.length;i<m;i++)this.data[i]&&data.push(this.data[i].toMathML(space+"  "));return space+"<"+tag+attr+">\n"+data.join("\n")+"\n"+space+"</"+tag+">"}}),MML.munderover.Augment({toMathML:function(space){var tag=this.type,base=this.data[this.base];base&&base.isa(MML.TeXAtom)&&base.movablelimits&&!base.Get("displaystyle")?(type="msubsup",null==this.data[this.under]&&(tag="msup"),null==this.data[this.over]&&(tag="msub")):(null==this.data[this.under]&&(tag="mover"),null==this.data[this.over]&&(tag="munder"));var attr=this.toMathMLattributes();delete this.data[0].inferred;for(var data=[],i=0,m=this.data.length;i<m;i++)this.data[i]&&data.push(this.data[i].toMathML(space+"  "));return space+"<"+tag+attr+">\n"+data.join("\n")+"\n"+space+"</"+tag+">"}}),MML.TeXAtom.Augment({toMathML:function(space){var attr=this.toMathMLattributes();return attr||1!==this.data[0].data.length?space+"<mrow"+attr+">\n"+this.data[0].toMathML(space+"  ")+"\n"+space+"</mrow>":space.substr(2)+this.data[0].toMathML(space)}}),MML.chars.Augment({toMathML:function(space){return(space||"")+this.toMathMLquote(this.toString())}}),MML.entity.Augment({toMathML:function(space){return(space||"")+"&"+this.toMathMLquote(this.data[0])+";\x3c!-- "+this.toString()+" --\x3e"}}),MML.xml.Augment({toMathML:function(space){return(space||"")+this.toString()}}),MathJax.Hub.Register.StartupHook("TeX mathchoice Ready",(function(){MML.TeXmathchoice.Augment({toMathML:function(space){return this.Core().toMathML(space)}})})),MathJax.Hub.Startup.signal.Post("toMathML Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/toMathML.js"),function(HUB,HTML,AJAX,OUTPUT,LOCALE){var HELP=MathJax.Extension.Help={version:"2.7.5"},STIXURL="http://www.stixfonts.org/",MENU=MathJax.Menu,FALSE,KEY;HUB.Register.StartupHook("MathEvents Ready",(function(){FALSE=MathJax.Extension.MathEvents.Event.False,KEY=MathJax.Extension.MathEvents.Event.KEY}));var CONFIG=HUB.CombineConfig("HelpDialog",{styles:{"#MathJax_Help":{position:"fixed",left:"50%",width:"auto","max-width":"90%","text-align":"center",border:"3px outset",padding:"1em 2em","background-color":"#DDDDDD",color:"black",cursor:"default","font-family":"message-box","font-size":"120%","font-style":"normal","text-indent":0,"text-transform":"none","line-height":"normal","letter-spacing":"normal","word-spacing":"normal","word-wrap":"normal","white-space":"wrap",float:"none","z-index":201,"border-radius":"15px","-webkit-border-radius":"15px","-moz-border-radius":"15px","-khtml-border-radius":"15px","box-shadow":"0px 10px 20px #808080","-webkit-box-shadow":"0px 10px 20px #808080","-moz-box-shadow":"0px 10px 20px #808080","-khtml-box-shadow":"0px 10px 20px #808080",filter:"progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')"},"#MathJax_Help.MathJax_MousePost":{outline:"none"},"#MathJax_HelpContent":{overflow:"auto","text-align":"left","font-size":"80%",padding:".4em .6em",border:"1px inset",margin:"1em 0px","max-height":"20em","max-width":"30em","background-color":"#EEEEEE"},"#MathJax_HelpClose":{position:"absolute",top:".2em",right:".2em",cursor:"pointer",display:"inline-block",border:"2px solid #AAA","border-radius":"18px","-webkit-border-radius":"18px","-moz-border-radius":"18px","-khtml-border-radius":"18px","font-family":"'Courier New',Courier","font-size":"24px",color:"#F0F0F0"},"#MathJax_HelpClose span":{display:"block","background-color":"#AAA",border:"1.5px solid","border-radius":"18px","-webkit-border-radius":"18px","-moz-border-radius":"18px","-khtml-border-radius":"18px","line-height":0,padding:"8px 0 6px"},"#MathJax_HelpClose:hover":{color:"white!important",border:"2px solid #CCC!important"},"#MathJax_HelpClose:hover span":{"background-color":"#CCC!important"},"#MathJax_HelpClose:hover:focus":{outline:"none"}}});HELP.Dialog=function(event){LOCALE.loadDomain("HelpDialog",["Post",HELP,event])},HELP.Post=function(event){this.div=MENU.Background(this);var help=HTML.addElement(this.div,"div",{id:"MathJax_Help",tabIndex:0,onkeydown:HELP.Keydown},LOCALE._("HelpDialog",[["b",{style:{fontSize:"120%"}},[["Help","MathJax Help"]]],["div",{id:"MathJax_HelpContent",tabIndex:0},[["p",{},[["MathJax","*MathJax* is a JavaScript library that allows page authors to include mathematics within their web pages.  As a reader, you don't need to do anything to make that happen."]]],["p",{},[["Browsers","*Browsers*: MathJax works with all modern browsers including IE6+, Firefox 3+, Chrome 0.2+, Safari 2+, Opera 9.6+ and most mobile browsers."]]],["p",{},[["Menu","*Math Menu*: MathJax adds a contextual menu to equations.  Right-click or CTRL-click on any mathematics to access the menu."]]],["div",{style:{"margin-left":"1em"}},[["p",{},[["ShowMath","*Show Math As* allows you to view the formula's source markup for copy & paste (as MathML or in its original format)."]]],["p",{},[["Settings","*Settings* gives you control over features of MathJax, such as the size of the mathematics, and the mechanism used to display equations."]]],["p",{},[["Language","*Language* lets you select the language used by MathJax for its menus and warning messages."]]]]],["p",{},[["Zoom","*Math Zoom*: If you are having difficulty reading an equation, MathJax can enlarge it to help you see it better."]]],["p",{},[["Accessibilty","*Accessibility*: MathJax will automatically work with screen readers to make mathematics accessible to the visually impaired."]]],["p",{},[["Fonts","*Fonts*: MathJax will use certain math fonts if they are installed on your computer; otherwise, it will use web-based fonts.  Although not required, locally installed fonts will speed up typesetting.  We suggest installing the [STIX fonts](%1).",STIXURL]]]]],["a",{href:"http://www.mathjax.org/"},["www.mathjax.org"]],["span",{id:"MathJax_HelpClose",onclick:HELP.Remove,onkeydown:HELP.Keydown,tabIndex:0,role:"button","aria-label":LOCALE._(["HelpDialog","CloseDialog"],"Close help dialog")},[["span",{},["×"]]]]]));"mouseup"===event.type&&(help.className+=" MathJax_MousePost"),help.focus(),LOCALE.setCSS(help);var doc=document.documentElement||{},H=window.innerHeight||doc.clientHeight||doc.scrollHeight||0;MENU.prototype.msieAboutBug?(help.style.width="20em",help.style.position="absolute",help.style.left=Math.floor((document.documentElement.scrollWidth-help.offsetWidth)/2)+"px",help.style.top=Math.floor((H-help.offsetHeight)/3)+document.body.scrollTop+"px"):(help.style.marginLeft=Math.floor(-help.offsetWidth/2)+"px",help.style.top=Math.floor((H-help.offsetHeight)/3)+"px")},HELP.Remove=function(event){HELP.div&&(document.body.removeChild(HELP.div),delete HELP.div)},HELP.Keydown=function(event){event.keyCode!==KEY.ESCAPE&&("MathJax_HelpClose"!==this.id||event.keyCode!==KEY.SPACE&&event.keyCode!==KEY.RETURN)||(HELP.Remove(event),MENU.CurrentNode().focus(),FALSE(event))},MathJax.Callback.Queue(HUB.Register.StartupHook("End Config",{}),["Styles",AJAX,CONFIG.styles],["Post",HUB.Startup.signal,"HelpDialog Ready"],["loadComplete",AJAX,"[MathJax]/extensions/HelpDialog.js"])}(MathJax.Hub,MathJax.HTML,MathJax.Ajax,MathJax.OutputJax,MathJax.Localization),MathJax.InputJax.TeX=MathJax.InputJax({id:"TeX",version:"2.7.5",directory:MathJax.InputJax.directory+"/TeX",extensionDir:MathJax.InputJax.extensionDir+"/TeX",config:{TagSide:"right",TagIndent:"0.8em",MultLineWidth:"85%",equationNumbers:{autoNumber:"none",formatNumber:function(n){return n},formatTag:function(n){return"("+n+")"},formatID:function(n){return"mjx-eqn-"+String(n).replace(/\s/g,"_")},formatURL:function(id,base){return base+"#"+encodeURIComponent(id)},useLabelIds:!0}},resetEquationNumbers:function(){}}),MathJax.InputJax.TeX.Register("math/tex"),MathJax.InputJax.TeX.loadComplete("config.js"),function(TEX,HUB,AJAX){var MML,NBSP=" ",_=function(id){return MathJax.Localization._.apply(MathJax.Localization,[["TeX",id]].concat([].slice.call(arguments,1)))},isArray=MathJax.Object.isArray,STACK=MathJax.Object.Subclass({Init:function(env,inner){this.global={isInner:inner},this.data=[STACKITEM.start(this.global)],env&&(this.data[0].env=env),this.env=this.data[0].env},Push:function(){var i,m,item,top;for(i=0,m=arguments.length;i<m;i++)if(item=arguments[i])if(item instanceof MML.mbase&&(item=STACKITEM.mml(item)),item.global=this.global,(top=!this.data.length||this.Top().checkItem(item))instanceof Array)this.Pop(),this.Push.apply(this,top);else if(top instanceof STACKITEM)this.Pop(),this.Push(top);else if(top)if(this.data.push(item),item.env){if(!1!==item.copyEnv)for(var id in this.env)this.env.hasOwnProperty(id)&&(item.env[id]=this.env[id]);this.env=item.env}else item.env=this.env},Pop:function(){var item=this.data.pop();return item.isOpen||delete item.env,this.env=this.data.length?this.Top().env:{},item},Top:function(n){return null==n&&(n=1),this.data.length<n?null:this.data[this.data.length-n]},Prev:function(noPop){var top=this.Top();return noPop?top.data[top.data.length-1]:top.Pop()},toString:function(){return"stack[\n  "+this.data.join("\n  ")+"\n]"}}),STACKITEM=STACK.Item=MathJax.Object.Subclass({type:"base",endError:["ExtraOpenMissingClose","Extra open brace or missing close brace"],closeError:["ExtraCloseMissingOpen","Extra close brace or missing open brace"],rightError:["MissingLeftExtraRight","Missing \\left or extra \\right"],Init:function(){this.isOpen&&(this.env={}),this.data=[],this.Push.apply(this,arguments)},Push:function(){this.data.push.apply(this.data,arguments)},Pop:function(){return this.data.pop()},mmlData:function(inferred,forceRow){return null==inferred&&(inferred=!0),1!==this.data.length||forceRow?MML.mrow.apply(MML,this.data).With(inferred?{inferred:!0}:{}):this.data[0]},checkItem:function(item){if("over"===item.type&&this.isOpen&&(item.num=this.mmlData(!1),this.data=[]),"cell"===item.type&&this.isOpen){if(item.linebreak)return!1;TEX.Error(["Misplaced","Misplaced %1",item.name])}return item.isClose&&this[item.type+"Error"]&&TEX.Error(this[item.type+"Error"]),!item.isNotStack||(this.Push(item.data[0]),!1)},With:function(def){for(var id in def)def.hasOwnProperty(id)&&(this[id]=def[id]);return this},toString:function(){return this.type+"["+this.data.join("; ")+"]"}});STACKITEM.start=STACKITEM.Subclass({type:"start",isOpen:!0,Init:function(global){this.SUPER(arguments).Init.call(this),this.global=global},checkItem:function(item){return"stop"===item.type?STACKITEM.mml(this.mmlData()):this.SUPER(arguments).checkItem.call(this,item)}}),STACKITEM.stop=STACKITEM.Subclass({type:"stop",isClose:!0}),STACKITEM.open=STACKITEM.Subclass({type:"open",isOpen:!0,stopError:["ExtraOpenMissingClose","Extra open brace or missing close brace"],checkItem:function(item){if("close"===item.type){var mml=this.mmlData();return STACKITEM.mml(MML.TeXAtom(mml))}return this.SUPER(arguments).checkItem.call(this,item)}}),STACKITEM.close=STACKITEM.Subclass({type:"close",isClose:!0}),STACKITEM.prime=STACKITEM.Subclass({type:"prime",checkItem:function(item){return"msubsup"!==this.data[0].type?[MML.msup(this.data[0],this.data[1]),item]:(this.data[0].SetData(this.data[0].sup,this.data[1]),[this.data[0],item])}}),STACKITEM.subsup=STACKITEM.Subclass({type:"subsup",stopError:["MissingScript","Missing superscript or subscript argument"],supError:["MissingOpenForSup","Missing open brace for superscript"],subError:["MissingOpenForSub","Missing open brace for subscript"],checkItem:function(item){return"open"===item.type||"left"===item.type||("mml"===item.type?(this.primes&&(2!==this.position?this.data[0].SetData(2,this.primes):item.data[0]=MML.mrow(this.primes.With({variantForm:!0}),item.data[0])),this.data[0].SetData(this.position,item.data[0]),null!=this.movesupsub&&(this.data[0].movesupsub=this.movesupsub),STACKITEM.mml(this.data[0])):void(this.SUPER(arguments).checkItem.call(this,item)&&TEX.Error(this[["","subError","supError"][this.position]])))},Pop:function(){}}),STACKITEM.over=STACKITEM.Subclass({type:"over",isClose:!0,name:"\\over",checkItem:function(item,stack){if("over"===item.type&&TEX.Error(["AmbiguousUseOf","Ambiguous use of %1",item.name]),item.isClose){var mml=MML.mfrac(this.num,this.mmlData(!1));return null!=this.thickness&&(mml.linethickness=this.thickness),(this.open||this.close)&&(mml.texWithDelims=!0,mml=TEX.fixedFence(this.open,mml,this.close)),[STACKITEM.mml(mml),item]}return this.SUPER(arguments).checkItem.call(this,item)},toString:function(){return"over["+this.num+" / "+this.data.join("; ")+"]"}}),STACKITEM.left=STACKITEM.Subclass({type:"left",isOpen:!0,delim:"(",stopError:["ExtraLeftMissingRight","Extra \\left or missing \\right"],checkItem:function(item){return"right"===item.type?STACKITEM.mml(TEX.fenced(this.delim,this.mmlData(),item.delim)):this.SUPER(arguments).checkItem.call(this,item)}}),STACKITEM.right=STACKITEM.Subclass({type:"right",isClose:!0,delim:")"}),STACKITEM.begin=STACKITEM.Subclass({type:"begin",isOpen:!0,checkItem:function(item){return"end"===item.type?(item.name!==this.name&&TEX.Error(["EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.name,item.name]),this.end?this.parse[this.end].call(this.parse,this,this.data):STACKITEM.mml(this.mmlData())):("stop"===item.type&&TEX.Error(["EnvMissingEnd","Missing \\end{%1}",this.name]),this.SUPER(arguments).checkItem.call(this,item))}}),STACKITEM.end=STACKITEM.Subclass({type:"end",isClose:!0}),STACKITEM.style=STACKITEM.Subclass({type:"style",checkItem:function(item){if(!item.isClose)return this.SUPER(arguments).checkItem.call(this,item);var mml=MML.mstyle.apply(MML,this.data).With(this.styles);return[STACKITEM.mml(mml),item]}}),STACKITEM.position=STACKITEM.Subclass({type:"position",checkItem:function(item){if(item.isClose&&TEX.Error(["MissingBoxFor","Missing box for %1",this.name]),item.isNotStack){var mml=item.mmlData();switch(this.move){case"vertical":return mml=MML.mpadded(mml).With({height:this.dh,depth:this.dd,voffset:this.dh}),[STACKITEM.mml(mml)];case"horizontal":return[STACKITEM.mml(this.left),item,STACKITEM.mml(this.right)]}}return this.SUPER(arguments).checkItem.call(this,item)}}),STACKITEM.array=STACKITEM.Subclass({type:"array",isOpen:!0,copyEnv:!1,arraydef:{},Init:function(){this.table=[],this.row=[],this.frame=[],this.hfill=[],this.SUPER(arguments).Init.apply(this,arguments)},checkItem:function(item){if(item.isClose&&"over"!==item.type){if(item.isEntry)return this.EndEntry(),this.clearEnv(),!1;if(item.isCR)return this.EndEntry(),this.EndRow(),this.clearEnv(),!1;this.EndTable(),this.clearEnv();var scriptlevel=this.arraydef.scriptlevel;delete this.arraydef.scriptlevel;var mml=MML.mtable.apply(MML,this.table).With(this.arraydef);if(4===this.frame.length?mml.frame=this.frame.dashed?"dashed":"solid":this.frame.length&&(mml.hasFrame=!0,this.arraydef.rowlines&&(this.arraydef.rowlines=this.arraydef.rowlines.replace(/none( none)+$/,"none")),mml=MML.menclose(mml).With({notation:this.frame.join(" "),isFrame:!0}),"none"==(this.arraydef.columnlines||"none")&&"none"==(this.arraydef.rowlines||"none")||(mml.padding=0)),scriptlevel&&(mml=MML.mstyle(mml).With({scriptlevel:scriptlevel})),(this.open||this.close)&&(mml=TEX.fenced(this.open,mml,this.close)),mml=STACKITEM.mml(mml),this.requireClose){if("close"===item.type)return mml;TEX.Error(["MissingCloseBrace","Missing close brace"])}return[mml,item]}return this.SUPER(arguments).checkItem.call(this,item)},EndEntry:function(){var mtd=MML.mtd.apply(MML,this.data);this.hfill.length&&(0===this.hfill[0]&&(mtd.columnalign="right"),this.hfill[this.hfill.length-1]===this.data.length&&(mtd.columnalign=mtd.columnalign?"center":"left")),this.row.push(mtd),this.data=[],this.hfill=[]},EndRow:function(){var mtr=MML.mtr;this.isNumbered&&3===this.row.length&&(this.row.unshift(this.row.pop()),mtr=MML.mlabeledtr),this.table.push(mtr.apply(MML,this.row)),this.row=[]},EndTable:function(){(this.data.length||this.row.length)&&(this.EndEntry(),this.EndRow()),this.checkLines()},checkLines:function(){if(this.arraydef.rowlines){var lines=this.arraydef.rowlines.split(/ /);lines.length===this.table.length?(this.frame.push("bottom"),lines.pop(),this.arraydef.rowlines=lines.join(" ")):lines.length<this.table.length-1&&(this.arraydef.rowlines+=" none")}if(this.rowspacing){for(var rows=this.arraydef.rowspacing.split(/ /);rows.length<this.table.length;)rows.push(this.rowspacing+"em");this.arraydef.rowspacing=rows.join(" ")}},clearEnv:function(){for(var id in this.env)this.env.hasOwnProperty(id)&&delete this.env[id]}}),STACKITEM.cell=STACKITEM.Subclass({type:"cell",isClose:!0}),STACKITEM.mml=STACKITEM.Subclass({type:"mml",isNotStack:!0,Add:function(){return this.data.push.apply(this.data,arguments),this}}),STACKITEM.fn=STACKITEM.Subclass({type:"fn",checkItem:function(item){if(this.data[0]){if(item.isOpen)return!0;if("fn"!==item.type){if("mml"!==item.type||!item.data[0])return[this.data[0],item];if(item.data[0].isa(MML.mspace))return[this.data[0],item];var mml=item.data[0];if(mml.isEmbellished()&&(mml=mml.CoreMO()),[0,0,1,1,0,1,1,0,0,0][mml.Get("texClass")])return[this.data[0],item]}return[this.data[0],MML.mo(MML.entity("#x2061")).With({texClass:MML.TEXCLASS.NONE}),item]}return this.SUPER(arguments).checkItem.apply(this,arguments)}}),STACKITEM.not=STACKITEM.Subclass({type:"not",checkItem:function(item){var mml,c;return"open"===item.type||"left"===item.type||("mml"===item.type&&item.data[0].type.match(/^(mo|mi|mtext)$/)&&1===(c=(mml=item.data[0]).data.join("")).length&&!mml.movesupsub&&1===mml.data.length?((c=STACKITEM.not.remap[c.charCodeAt(0)])?mml.SetData(0,MML.chars(String.fromCharCode(c))):mml.Append(MML.chars("̸")),item):(mml=MML.mpadded(MML.mtext("⧸")).With({width:0}),[mml=MML.TeXAtom(mml).With({texClass:MML.TEXCLASS.REL}),item]))}}),STACKITEM.not.remap={8592:8602,8594:8603,8596:8622,8656:8653,8658:8655,8660:8654,8712:8713,8715:8716,8739:8740,8741:8742,8764:8769,126:8769,8771:8772,8773:8775,8776:8777,8781:8813,61:8800,8801:8802,60:8814,62:8815,8804:8816,8805:8817,8818:8820,8819:8821,8822:8824,8823:8825,8826:8832,8827:8833,8834:8836,8835:8837,8838:8840,8839:8841,8866:8876,8872:8877,8873:8878,8875:8879,8828:8928,8829:8929,8849:8930,8850:8931,8882:8938,8883:8939,8884:8940,8885:8941,8707:8708},STACKITEM.dots=STACKITEM.Subclass({type:"dots",checkItem:function(item){if("open"===item.type||"left"===item.type)return!0;var dots=this.ldots;if("mml"===item.type&&item.data[0].isEmbellished()){var tclass=item.data[0].CoreMO().Get("texClass");tclass!==MML.TEXCLASS.BIN&&tclass!==MML.TEXCLASS.REL||(dots=this.cdots)}return[dots,item]}});var TEXDEF={Add:function(src,dst,nouser){for(var id in dst||(dst=this),src)src.hasOwnProperty(id)&&("object"!=typeof src[id]||isArray(src[id])||"object"!=typeof dst[id]&&"function"!=typeof dst[id]?dst[id]&&dst[id].isUser&&nouser||(dst[id]=src[id]):this.Add(src[id],dst[id],src[id],nouser));return dst}},STARTUP=function(){if(MML=MathJax.ElementJax.mml,HUB.Insert(TEXDEF,{letter:/[a-z]/i,digit:/[0-9.]/,number:/^(?:[0-9]+(?:\{,\}[0-9]{3})*(?:\.[0-9]*)*|\.[0-9]+)/,special:{"\\":"ControlSequence","{":"Open","}":"Close","~":"Tilde","^":"Superscript",_:"Subscript"," ":"Space","\t":"Space","\r":"Space","\n":"Space","'":"Prime","%":"Comment","&":"Entry","#":"Hash"," ":"Space","’":"Prime"},remap:{"-":"2212","*":"2217","`":"2018"},mathchar0mi:{alpha:"03B1",beta:"03B2",gamma:"03B3",delta:"03B4",epsilon:"03F5",zeta:"03B6",eta:"03B7",theta:"03B8",iota:"03B9",kappa:"03BA",lambda:"03BB",mu:"03BC",nu:"03BD",xi:"03BE",omicron:"03BF",pi:"03C0",rho:"03C1",sigma:"03C3",tau:"03C4",upsilon:"03C5",phi:"03D5",chi:"03C7",psi:"03C8",omega:"03C9",varepsilon:"03B5",vartheta:"03D1",varpi:"03D6",varrho:"03F1",varsigma:"03C2",varphi:"03C6",S:["00A7",{mathvariant:MML.VARIANT.NORMAL}],aleph:["2135",{mathvariant:MML.VARIANT.NORMAL}],hbar:["210F",{variantForm:!0}],imath:"0131",jmath:"0237",ell:"2113",wp:["2118",{mathvariant:MML.VARIANT.NORMAL}],Re:["211C",{mathvariant:MML.VARIANT.NORMAL}],Im:["2111",{mathvariant:MML.VARIANT.NORMAL}],partial:["2202",{mathvariant:MML.VARIANT.NORMAL}],infty:["221E",{mathvariant:MML.VARIANT.NORMAL}],prime:["2032",{mathvariant:MML.VARIANT.NORMAL,variantForm:!0}],emptyset:["2205",{mathvariant:MML.VARIANT.NORMAL}],nabla:["2207",{mathvariant:MML.VARIANT.NORMAL}],top:["22A4",{mathvariant:MML.VARIANT.NORMAL}],bot:["22A5",{mathvariant:MML.VARIANT.NORMAL}],angle:["2220",{mathvariant:MML.VARIANT.NORMAL}],triangle:["25B3",{mathvariant:MML.VARIANT.NORMAL}],backslash:["2216",{mathvariant:MML.VARIANT.NORMAL,variantForm:!0}],forall:["2200",{mathvariant:MML.VARIANT.NORMAL}],exists:["2203",{mathvariant:MML.VARIANT.NORMAL}],neg:["00AC",{mathvariant:MML.VARIANT.NORMAL}],lnot:["00AC",{mathvariant:MML.VARIANT.NORMAL}],flat:["266D",{mathvariant:MML.VARIANT.NORMAL}],natural:["266E",{mathvariant:MML.VARIANT.NORMAL}],sharp:["266F",{mathvariant:MML.VARIANT.NORMAL}],clubsuit:["2663",{mathvariant:MML.VARIANT.NORMAL}],diamondsuit:["2662",{mathvariant:MML.VARIANT.NORMAL}],heartsuit:["2661",{mathvariant:MML.VARIANT.NORMAL}],spadesuit:["2660",{mathvariant:MML.VARIANT.NORMAL}]},mathchar0mo:{surd:"221A",coprod:["2210",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigvee:["22C1",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigwedge:["22C0",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],biguplus:["2A04",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigcap:["22C2",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigcup:["22C3",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],int:["222B",{texClass:MML.TEXCLASS.OP}],intop:["222B",{texClass:MML.TEXCLASS.OP,movesupsub:!0,movablelimits:!0}],iint:["222C",{texClass:MML.TEXCLASS.OP}],iiint:["222D",{texClass:MML.TEXCLASS.OP}],prod:["220F",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],sum:["2211",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigotimes:["2A02",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigoplus:["2A01",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],bigodot:["2A00",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],oint:["222E",{texClass:MML.TEXCLASS.OP}],bigsqcup:["2A06",{texClass:MML.TEXCLASS.OP,movesupsub:!0}],smallint:["222B",{largeop:!1}],triangleleft:"25C3",triangleright:"25B9",bigtriangleup:"25B3",bigtriangledown:"25BD",wedge:"2227",land:"2227",vee:"2228",lor:"2228",cap:"2229",cup:"222A",ddagger:"2021",dagger:"2020",sqcap:"2293",sqcup:"2294",uplus:"228E",amalg:"2A3F",diamond:"22C4",bullet:"2219",wr:"2240",div:"00F7",odot:["2299",{largeop:!1}],oslash:["2298",{largeop:!1}],otimes:["2297",{largeop:!1}],ominus:["2296",{largeop:!1}],oplus:["2295",{largeop:!1}],mp:"2213",pm:"00B1",circ:"2218",bigcirc:"25EF",setminus:["2216",{variantForm:!0}],cdot:"22C5",ast:"2217",times:"00D7",star:"22C6",propto:"221D",sqsubseteq:"2291",sqsupseteq:"2292",parallel:"2225",mid:"2223",dashv:"22A3",vdash:"22A2",leq:"2264",le:"2264",geq:"2265",ge:"2265",lt:"003C",gt:"003E",succ:"227B",prec:"227A",approx:"2248",succeq:"2AB0",preceq:"2AAF",supset:"2283",subset:"2282",supseteq:"2287",subseteq:"2286",in:"2208",ni:"220B",notin:"2209",owns:"220B",gg:"226B",ll:"226A",sim:"223C",simeq:"2243",perp:"22A5",equiv:"2261",asymp:"224D",smile:"2323",frown:"2322",ne:"2260",neq:"2260",cong:"2245",doteq:"2250",bowtie:"22C8",models:"22A8",notChar:"29F8",Leftrightarrow:"21D4",Leftarrow:"21D0",Rightarrow:"21D2",leftrightarrow:"2194",leftarrow:"2190",gets:"2190",rightarrow:"2192",to:"2192",mapsto:"21A6",leftharpoonup:"21BC",leftharpoondown:"21BD",rightharpoonup:"21C0",rightharpoondown:"21C1",nearrow:"2197",searrow:"2198",nwarrow:"2196",swarrow:"2199",rightleftharpoons:"21CC",hookrightarrow:"21AA",hookleftarrow:"21A9",longleftarrow:"27F5",Longleftarrow:"27F8",longrightarrow:"27F6",Longrightarrow:"27F9",Longleftrightarrow:"27FA",longleftrightarrow:"27F7",longmapsto:"27FC",ldots:"2026",cdots:"22EF",vdots:"22EE",ddots:"22F1",dotsc:"2026",dotsb:"22EF",dotsm:"22EF",dotsi:"22EF",dotso:"2026",ldotp:["002E",{texClass:MML.TEXCLASS.PUNCT}],cdotp:["22C5",{texClass:MML.TEXCLASS.PUNCT}],colon:["003A",{texClass:MML.TEXCLASS.PUNCT}]},mathchar7:{Gamma:"0393",Delta:"0394",Theta:"0398",Lambda:"039B",Xi:"039E",Pi:"03A0",Sigma:"03A3",Upsilon:"03A5",Phi:"03A6",Psi:"03A8",Omega:"03A9",_:"005F","#":"0023",$:"0024","%":"0025","&":"0026",And:"0026"},delimiter:{"(":"(",")":")","[":"[","]":"]","<":"27E8",">":"27E9","\\lt":"27E8","\\gt":"27E9","/":"/","|":["|",{texClass:MML.TEXCLASS.ORD}],".":"","\\\\":"\\","\\lmoustache":"23B0","\\rmoustache":"23B1","\\lgroup":"27EE","\\rgroup":"27EF","\\arrowvert":"23D0","\\Arrowvert":"2016","\\bracevert":"23AA","\\Vert":["2016",{texClass:MML.TEXCLASS.ORD}],"\\|":["2016",{texClass:MML.TEXCLASS.ORD}],"\\vert":["|",{texClass:MML.TEXCLASS.ORD}],"\\uparrow":"2191","\\downarrow":"2193","\\updownarrow":"2195","\\Uparrow":"21D1","\\Downarrow":"21D3","\\Updownarrow":"21D5","\\backslash":"\\","\\rangle":"27E9","\\langle":"27E8","\\rbrace":"}","\\lbrace":"{","\\}":"}","\\{":"{","\\rceil":"2309","\\lceil":"2308","\\rfloor":"230B","\\lfloor":"230A","\\lbrack":"[","\\rbrack":"]"},macros:{displaystyle:["SetStyle","D",!0,0],textstyle:["SetStyle","T",!1,0],scriptstyle:["SetStyle","S",!1,1],scriptscriptstyle:["SetStyle","SS",!1,2],rm:["SetFont",MML.VARIANT.NORMAL],mit:["SetFont",MML.VARIANT.ITALIC],oldstyle:["SetFont",MML.VARIANT.OLDSTYLE],cal:["SetFont",MML.VARIANT.CALIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",MML.VARIANT.BOLD],bbFont:["SetFont",MML.VARIANT.DOUBLESTRUCK],scr:["SetFont",MML.VARIANT.SCRIPT],frak:["SetFont",MML.VARIANT.FRAKTUR],sf:["SetFont",MML.VARIANT.SANSSERIF],tt:["SetFont",MML.VARIANT.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],arcsin:["NamedFn"],arccos:["NamedFn"],arctan:["NamedFn"],arg:["NamedFn"],cos:["NamedFn"],cosh:["NamedFn"],cot:["NamedFn"],coth:["NamedFn"],csc:["NamedFn"],deg:["NamedFn"],det:"NamedOp",dim:["NamedFn"],exp:["NamedFn"],gcd:"NamedOp",hom:["NamedFn"],inf:"NamedOp",ker:["NamedFn"],lg:["NamedFn"],lim:"NamedOp",liminf:["NamedOp","lim&thinsp;inf"],limsup:["NamedOp","lim&thinsp;sup"],ln:["NamedFn"],log:["NamedFn"],max:"NamedOp",min:"NamedOp",Pr:"NamedOp",sec:["NamedFn"],sin:["NamedFn"],sinh:["NamedFn"],sup:"NamedOp",tan:["NamedFn"],tanh:["NamedFn"],limits:["Limits",1],nolimits:["Limits",0],overline:["UnderOver","00AF",null,1],underline:["UnderOver","005F"],overbrace:["UnderOver","23DE",1],underbrace:["UnderOver","23DF",1],overparen:["UnderOver","23DC"],underparen:["UnderOver","23DD"],overrightarrow:["UnderOver","2192"],underrightarrow:["UnderOver","2192"],overleftarrow:["UnderOver","2190"],underleftarrow:["UnderOver","2190"],overleftrightarrow:["UnderOver","2194"],underleftrightarrow:["UnderOver","2194"],overset:"Overset",underset:"Underset",stackrel:["Macro","\\mathrel{\\mathop{#2}\\limits^{#1}}",2],over:"Over",overwithdelims:"Over",atop:"Over",atopwithdelims:"Over",above:"Over",abovewithdelims:"Over",brace:["Over","{","}"],brack:["Over","[","]"],choose:["Over","(",")"],frac:"Frac",sqrt:"Sqrt",root:"Root",uproot:["MoveRoot","upRoot"],leftroot:["MoveRoot","leftRoot"],left:"LeftRight",right:"LeftRight",middle:"Middle",llap:"Lap",rlap:"Lap",raise:"RaiseLower",lower:"RaiseLower",moveleft:"MoveLeftRight",moveright:"MoveLeftRight",",":["Spacer",MML.LENGTH.THINMATHSPACE],":":["Spacer",MML.LENGTH.MEDIUMMATHSPACE],">":["Spacer",MML.LENGTH.MEDIUMMATHSPACE],";":["Spacer",MML.LENGTH.THICKMATHSPACE],"!":["Spacer",MML.LENGTH.NEGATIVETHINMATHSPACE],enspace:["Spacer",".5em"],quad:["Spacer","1em"],qquad:["Spacer","2em"],thinspace:["Spacer",MML.LENGTH.THINMATHSPACE],negthinspace:["Spacer",MML.LENGTH.NEGATIVETHINMATHSPACE],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],big:["MakeBig",MML.TEXCLASS.ORD,.85],Big:["MakeBig",MML.TEXCLASS.ORD,1.15],bigg:["MakeBig",MML.TEXCLASS.ORD,1.45],Bigg:["MakeBig",MML.TEXCLASS.ORD,1.75],bigl:["MakeBig",MML.TEXCLASS.OPEN,.85],Bigl:["MakeBig",MML.TEXCLASS.OPEN,1.15],biggl:["MakeBig",MML.TEXCLASS.OPEN,1.45],Biggl:["MakeBig",MML.TEXCLASS.OPEN,1.75],bigr:["MakeBig",MML.TEXCLASS.CLOSE,.85],Bigr:["MakeBig",MML.TEXCLASS.CLOSE,1.15],biggr:["MakeBig",MML.TEXCLASS.CLOSE,1.45],Biggr:["MakeBig",MML.TEXCLASS.CLOSE,1.75],bigm:["MakeBig",MML.TEXCLASS.REL,.85],Bigm:["MakeBig",MML.TEXCLASS.REL,1.15],biggm:["MakeBig",MML.TEXCLASS.REL,1.45],Biggm:["MakeBig",MML.TEXCLASS.REL,1.75],mathord:["TeXAtom",MML.TEXCLASS.ORD],mathop:["TeXAtom",MML.TEXCLASS.OP],mathopen:["TeXAtom",MML.TEXCLASS.OPEN],mathclose:["TeXAtom",MML.TEXCLASS.CLOSE],mathbin:["TeXAtom",MML.TEXCLASS.BIN],mathrel:["TeXAtom",MML.TEXCLASS.REL],mathpunct:["TeXAtom",MML.TEXCLASS.PUNCT],mathinner:["TeXAtom",MML.TEXCLASS.INNER],vcenter:["TeXAtom",MML.TEXCLASS.VCENTER],mathchoice:["Extension","mathchoice"],buildrel:"BuildRel",hbox:["HBox",0],text:"HBox",mbox:["HBox",0],fbox:"FBox",strut:"Strut",mathstrut:["Macro","\\vphantom{(}"],phantom:"Phantom",vphantom:["Phantom",1,0],hphantom:["Phantom",0,1],smash:"Smash",acute:["Accent","00B4"],grave:["Accent","0060"],ddot:["Accent","00A8"],tilde:["Accent","007E"],bar:["Accent","00AF"],breve:["Accent","02D8"],check:["Accent","02C7"],hat:["Accent","005E"],vec:["Accent","2192"],dot:["Accent","02D9"],widetilde:["Accent","007E",1],widehat:["Accent","005E",1],matrix:"Matrix",array:"Matrix",pmatrix:["Matrix","(",")"],cases:["Matrix","{","","left left",null,".1em",null,!0],eqalign:["Matrix",null,null,"right left",MML.LENGTH.THICKMATHSPACE,".5em","D"],displaylines:["Matrix",null,null,"center",null,".5em","D"],cr:"Cr","\\":"CrLaTeX",newline:"Cr",hline:["HLine","solid"],hdashline:["HLine","dashed"],eqalignno:["Matrix",null,null,"right left",MML.LENGTH.THICKMATHSPACE,".5em","D",null,"right"],leqalignno:["Matrix",null,null,"right left",MML.LENGTH.THICKMATHSPACE,".5em","D",null,"left"],hfill:"HFill",hfil:"HFill",hfilll:"HFill",bmod:["Macro",'\\mmlToken{mo}[lspace="thickmathspace" rspace="thickmathspace"]{mod}'],pmod:["Macro","\\pod{\\mmlToken{mi}{mod}\\kern 6mu #1}",1],mod:["Macro","\\mathchoice{\\kern18mu}{\\kern12mu}{\\kern12mu}{\\kern12mu}\\mmlToken{mi}{mod}\\,\\,#1",1],pod:["Macro","\\mathchoice{\\kern18mu}{\\kern8mu}{\\kern8mu}{\\kern8mu}(#1)",1],iff:["Macro","\\;\\Longleftrightarrow\\;"],skew:["Macro","{{#2{#3\\mkern#1mu}\\mkern-#1mu}{}}",3],mathcal:["Macro","{\\cal #1}",1],mathscr:["Macro","{\\scr #1}",1],mathrm:["Macro","{\\rm #1}",1],mathbf:["Macro","{\\bf #1}",1],mathbb:["Macro","{\\bbFont #1}",1],Bbb:["Macro","{\\bbFont #1}",1],mathit:["Macro","{\\it #1}",1],mathfrak:["Macro","{\\frak #1}",1],mathsf:["Macro","{\\sf #1}",1],mathtt:["Macro","{\\tt #1}",1],textrm:["Macro","\\mathord{\\rm\\text{#1}}",1],textit:["Macro","\\mathord{\\it\\text{#1}}",1],textbf:["Macro","\\mathord{\\bf\\text{#1}}",1],textsf:["Macro","\\mathord{\\sf\\text{#1}}",1],texttt:["Macro","\\mathord{\\tt\\text{#1}}",1],pmb:["Macro","\\rlap{#1}\\kern1px{#1}",1],TeX:["Macro","T\\kern-.14em\\lower.5ex{E}\\kern-.115em X"],LaTeX:["Macro","L\\kern-.325em\\raise.21em{\\scriptstyle{A}}\\kern-.17em\\TeX"]," ":["Macro","\\text{ }"],not:"Not",dots:"Dots",space:"Tilde"," ":"Tilde",begin:"BeginEnd",end:"BeginEnd",newcommand:["Extension","newcommand"],renewcommand:["Extension","newcommand"],newenvironment:["Extension","newcommand"],renewenvironment:["Extension","newcommand"],def:["Extension","newcommand"],let:["Extension","newcommand"],verb:["Extension","verb"],boldsymbol:["Extension","boldsymbol"],tag:["Extension","AMSmath"],notag:["Extension","AMSmath"],label:["Extension","AMSmath"],ref:["Extension","AMSmath"],eqref:["Extension","AMSmath"],nonumber:["Macro","\\notag"],unicode:["Extension","unicode"],color:"Color",href:["Extension","HTML"],class:["Extension","HTML"],style:["Extension","HTML"],cssId:["Extension","HTML"],bbox:["Extension","bbox"],mmlToken:"MmlToken",require:"Require"},environment:{array:["AlignedArray"],matrix:["Array",null,null,null,"c"],pmatrix:["Array",null,"(",")","c"],bmatrix:["Array",null,"[","]","c"],Bmatrix:["Array",null,"\\{","\\}","c"],vmatrix:["Array",null,"\\vert","\\vert","c"],Vmatrix:["Array",null,"\\Vert","\\Vert","c"],cases:["Array",null,"\\{",".","ll",null,".2em","T"],equation:[null,"Equation"],"equation*":[null,"Equation"],eqnarray:["ExtensionEnv",null,"AMSmath"],"eqnarray*":["ExtensionEnv",null,"AMSmath"],align:["ExtensionEnv",null,"AMSmath"],"align*":["ExtensionEnv",null,"AMSmath"],aligned:["ExtensionEnv",null,"AMSmath"],multline:["ExtensionEnv",null,"AMSmath"],"multline*":["ExtensionEnv",null,"AMSmath"],split:["ExtensionEnv",null,"AMSmath"],gather:["ExtensionEnv",null,"AMSmath"],"gather*":["ExtensionEnv",null,"AMSmath"],gathered:["ExtensionEnv",null,"AMSmath"],alignat:["ExtensionEnv",null,"AMSmath"],"alignat*":["ExtensionEnv",null,"AMSmath"],alignedat:["ExtensionEnv",null,"AMSmath"]},p_height:1.2/.85}),this.config.Macros){var MACROS=this.config.Macros;for(var id in MACROS)MACROS.hasOwnProperty(id)&&("string"==typeof MACROS[id]?TEXDEF.macros[id]=["Macro",MACROS[id]]:TEXDEF.macros[id]=["Macro"].concat(MACROS[id]),TEXDEF.macros[id].isUser=!0)}},PARSE=MathJax.Object.Subclass({Init:function(string,env){var ENV;if(this.string=string,this.i=0,this.macroCount=0,env)for(var id in ENV={},env)env.hasOwnProperty(id)&&(ENV[id]=env[id]);this.stack=TEX.Stack(ENV,!!env),this.Parse(),this.Push(STACKITEM.stop())},Parse:function(){for(var c,n;this.i<this.string.length;)(n=(c=this.string.charAt(this.i++)).charCodeAt(0))>=55296&&n<56320&&(c+=this.string.charAt(this.i++)),TEXDEF.special.hasOwnProperty(c)?this[TEXDEF.special[c]](c):TEXDEF.letter.test(c)?this.Variable(c):TEXDEF.digit.test(c)?this.Number(c):this.Other(c)},Push:function(){this.stack.Push.apply(this.stack,arguments)},mml:function(){return"mml"!==this.stack.Top().type?null:this.stack.Top().data[0]},mmlToken:function(token){return token},ControlSequence:function(c){var name=this.GetCS(),macro=this.csFindMacro(name);if(macro){isArray(macro)||(macro=[macro]);var fn=macro[0];fn instanceof Function||(fn=this[fn]),fn.apply(this,[c+name].concat(macro.slice(1)))}else TEXDEF.mathchar0mi.hasOwnProperty(name)?this.csMathchar0mi(name,TEXDEF.mathchar0mi[name]):TEXDEF.mathchar0mo.hasOwnProperty(name)?this.csMathchar0mo(name,TEXDEF.mathchar0mo[name]):TEXDEF.mathchar7.hasOwnProperty(name)?this.csMathchar7(name,TEXDEF.mathchar7[name]):TEXDEF.delimiter.hasOwnProperty("\\"+name)?this.csDelimiter(name,TEXDEF.delimiter["\\"+name]):this.csUndefined(c+name)},csFindMacro:function(name){return TEXDEF.macros.hasOwnProperty(name)?TEXDEF.macros[name]:null},csMathchar0mi:function(name,mchar){var def={mathvariant:MML.VARIANT.ITALIC};isArray(mchar)&&(def=mchar[1],mchar=mchar[0]),this.Push(this.mmlToken(MML.mi(MML.entity("#x"+mchar)).With(def)))},csMathchar0mo:function(name,mchar){var def={stretchy:!1};isArray(mchar)&&((def=mchar[1]).stretchy=!1,mchar=mchar[0]),this.Push(this.mmlToken(MML.mo(MML.entity("#x"+mchar)).With(def)))},csMathchar7:function(name,mchar){var def={mathvariant:MML.VARIANT.NORMAL};isArray(mchar)&&(def=mchar[1],mchar=mchar[0]),this.stack.env.font&&(def.mathvariant=this.stack.env.font),this.Push(this.mmlToken(MML.mi(MML.entity("#x"+mchar)).With(def)))},csDelimiter:function(name,delim){var def={};isArray(delim)&&(def=delim[1],delim=delim[0]),delim=4===delim.length?MML.entity("#x"+delim):MML.chars(delim),this.Push(this.mmlToken(MML.mo(delim).With({fence:!1,stretchy:!1}).With(def)))},csUndefined:function(name){TEX.Error(["UndefinedControlSequence","Undefined control sequence %1",name])},Variable:function(c){var def={};this.stack.env.font&&(def.mathvariant=this.stack.env.font),this.Push(this.mmlToken(MML.mi(MML.chars(c)).With(def)))},Number:function(c){var mml,n=this.string.slice(this.i-1).match(TEXDEF.number);n?(mml=MML.mn(n[0].replace(/[{}]/g,"")),this.i+=n[0].length-1):mml=MML.mo(MML.chars(c)),this.stack.env.font&&(mml.mathvariant=this.stack.env.font),this.Push(this.mmlToken(mml))},Open:function(c){this.Push(STACKITEM.open())},Close:function(c){this.Push(STACKITEM.close())},Tilde:function(c){this.Push(MML.mtext(MML.chars(" ")))},Space:function(c){},Superscript:function(c){this.GetNext().match(/\d/)&&(this.string=this.string.substr(0,this.i+1)+" "+this.string.substr(this.i+1));var primes,base,top=this.stack.Top();"prime"===top.type?(base=top.data[0],primes=top.data[1],this.stack.Pop()):(base=this.stack.Prev())||(base=MML.mi("")),base.isEmbellishedWrapper&&(base=base.data[0].data[0]);var movesupsub=base.movesupsub,position=base.sup;("msubsup"===base.type&&base.data[base.sup]||"munderover"===base.type&&base.data[base.over]&&!base.subsupOK)&&TEX.Error(["DoubleExponent","Double exponent: use braces to clarify"]),"msubsup"!==base.type&&(movesupsub?(("munderover"!==base.type||base.data[base.over])&&(base.movablelimits&&base.isa(MML.mi)&&(base=this.mi2mo(base)),base=MML.munderover(base,null,null).With({movesupsub:!0})),position=base.over):position=(base=MML.msubsup(base,null,null)).sup),this.Push(STACKITEM.subsup(base).With({position:position,primes:primes,movesupsub:movesupsub}))},Subscript:function(c){this.GetNext().match(/\d/)&&(this.string=this.string.substr(0,this.i+1)+" "+this.string.substr(this.i+1));var primes,base,top=this.stack.Top();"prime"===top.type?(base=top.data[0],primes=top.data[1],this.stack.Pop()):(base=this.stack.Prev())||(base=MML.mi("")),base.isEmbellishedWrapper&&(base=base.data[0].data[0]);var movesupsub=base.movesupsub,position=base.sub;("msubsup"===base.type&&base.data[base.sub]||"munderover"===base.type&&base.data[base.under]&&!base.subsupOK)&&TEX.Error(["DoubleSubscripts","Double subscripts: use braces to clarify"]),"msubsup"!==base.type&&(movesupsub?(("munderover"!==base.type||base.data[base.under])&&(base.movablelimits&&base.isa(MML.mi)&&(base=this.mi2mo(base)),base=MML.munderover(base,null,null).With({movesupsub:!0})),position=base.under):position=(base=MML.msubsup(base,null,null)).sub),this.Push(STACKITEM.subsup(base).With({position:position,primes:primes,movesupsub:movesupsub}))},PRIME:"′",SMARTQUOTE:"’",Prime:function(c){var base=this.stack.Prev();base||(base=MML.mi()),"msubsup"===base.type&&base.data[base.sup]&&TEX.Error(["DoubleExponentPrime","Prime causes double exponent: use braces to clarify"]);var sup="";this.i--;do{sup+=this.PRIME,this.i++,c=this.GetNext()}while("'"===c||c===this.SMARTQUOTE);sup=["","′","″","‴","⁗"][sup.length]||sup,this.Push(STACKITEM.prime(base,this.mmlToken(MML.mo(sup))))},mi2mo:function(mi){var mo=MML.mo(),id;for(id in mo.Append.apply(mo,mi.data),mo.defaults)mo.defaults.hasOwnProperty(id)&&null!=mi[id]&&(mo[id]=mi[id]);for(id in MML.copyAttributes)MML.copyAttributes.hasOwnProperty(id)&&null!=mi[id]&&(mo[id]=mi[id]);return mo.lspace=mo.rspace="0",mo.useMMLspacing&=~(mo.SPACE_ATTR.lspace|mo.SPACE_ATTR.rspace),mo},Comment:function(c){for(;this.i<this.string.length&&"\n"!=this.string.charAt(this.i);)this.i++},Hash:function(c){TEX.Error(["CantUseHash1","You can't use 'macro parameter character #' in math mode"])},Other:function(c){var def,mo;this.stack.env.font&&(def={mathvariant:this.stack.env.font}),TEXDEF.remap.hasOwnProperty(c)?(c=TEXDEF.remap[c],isArray(c)&&(def=c[1],c=c[0]),mo=MML.mo(MML.entity("#x"+c)).With(def)):mo=MML.mo(c).With(def),mo.autoDefault("stretchy",!0)&&(mo.stretchy=!1),""==mo.autoDefault("texClass",!0)&&(mo=MML.TeXAtom(mo)),this.Push(this.mmlToken(mo))},SetFont:function(name,font){this.stack.env.font=font},SetStyle:function(name,texStyle,style,level){this.stack.env.style=texStyle,this.stack.env.level=level,this.Push(STACKITEM.style().With({styles:{displaystyle:style,scriptlevel:level}}))},SetSize:function(name,size){this.stack.env.size=size,this.Push(STACKITEM.style().With({styles:{mathsize:size+"em"}}))},Color:function(name){var color=this.GetArgument(name),old=this.stack.env.color;this.stack.env.color=color;var math=this.ParseArg(name);old?this.stack.env.color:delete this.stack.env.color,this.Push(MML.mstyle(math).With({mathcolor:color}))},Spacer:function(name,space){this.Push(MML.mspace().With({width:space,mathsize:MML.SIZE.NORMAL,scriptlevel:0}))},LeftRight:function(name){this.Push(STACKITEM[name.substr(1)]().With({delim:this.GetDelimiter(name)}))},Middle:function(name){var delim=this.GetDelimiter(name);this.Push(MML.TeXAtom().With({texClass:MML.TEXCLASS.CLOSE})),"left"!==this.stack.Top().type&&TEX.Error(["MisplacedMiddle","%1 must be within \\left and \\right",name]),this.Push(MML.mo(delim).With({stretchy:!0})),this.Push(MML.TeXAtom().With({texClass:MML.TEXCLASS.OPEN}))},NamedFn:function(name,id){id||(id=name.substr(1));var mml=MML.mi(id).With({texClass:MML.TEXCLASS.OP});this.Push(STACKITEM.fn(this.mmlToken(mml)))},NamedOp:function(name,id){id||(id=name.substr(1)),id=id.replace(/&thinsp;/," ");var mml=MML.mo(id).With({movablelimits:!0,movesupsub:!0,form:MML.FORM.PREFIX,texClass:MML.TEXCLASS.OP});this.Push(this.mmlToken(mml))},Limits:function(name,limits){var op=this.stack.Prev("nopop");(!op||op.Get("texClass")!==MML.TEXCLASS.OP&&null==op.movesupsub)&&TEX.Error(["MisplacedLimits","%1 is allowed only on operators",name]);var top=this.stack.Top();"munderover"!==op.type||limits?"msubsup"===op.type&&limits&&(op=top.data[top.data.length-1]=MML.munderover.apply(MML.underover,op.data)):op=top.data[top.data.length-1]=MML.msubsup.apply(MML.subsup,op.data),op.movesupsub=!!limits,op.Core().movablelimits=!1,op.movablelimits&&(op.movablelimits=!1)},Over:function(name,open,close){var mml=STACKITEM.over().With({name:name});open||close?(mml.open=open,mml.close=close):name.match(/withdelims$/)&&(mml.open=this.GetDelimiter(name),mml.close=this.GetDelimiter(name)),name.match(/^\\above/)?mml.thickness=this.GetDimen(name):(name.match(/^\\atop/)||open||close)&&(mml.thickness=0),this.Push(mml)},Frac:function(name){var num=this.ParseArg(name),den=this.ParseArg(name);this.Push(MML.mfrac(num,den))},Sqrt:function(name){var n=this.GetBrackets(name),arg=this.GetArgument(name);"\\frac"===arg&&(arg+="{"+this.GetArgument(arg)+"}{"+this.GetArgument(arg)+"}");var mml=TEX.Parse(arg,this.stack.env).mml();mml=n?MML.mroot(mml,this.parseRoot(n)):MML.msqrt.apply(MML,mml.array()),this.Push(mml)},Root:function(name){var n=this.GetUpTo(name,"\\of"),arg=this.ParseArg(name);this.Push(MML.mroot(arg,this.parseRoot(n)))},parseRoot:function(n){var env=this.stack.env,inRoot=env.inRoot;env.inRoot=!0;var parser=TEX.Parse(n,env);n=parser.mml();var global=parser.stack.global;return(global.leftRoot||global.upRoot)&&(n=MML.mpadded(n),global.leftRoot&&(n.width=global.leftRoot),global.upRoot&&(n.voffset=global.upRoot,n.height=global.upRoot)),env.inRoot=inRoot,n},MoveRoot:function(name,id){this.stack.env.inRoot||TEX.Error(["MisplacedMoveRoot","%1 can appear only within a root",name]),this.stack.global[id]&&TEX.Error(["MultipleMoveRoot","Multiple use of %1",name]);var n=this.GetArgument(name);n.match(/-?[0-9]+/)||TEX.Error(["IntegerArg","The argument to %1 must be an integer",name]),"-"!==(n=n/15+"em").substr(0,1)&&(n="+"+n),this.stack.global[id]=n},Accent:function(name,accent,stretchy){var c=this.ParseArg(name),def={accent:!0};this.stack.env.font&&(def.mathvariant=this.stack.env.font);var mml=this.mmlToken(MML.mo(MML.entity("#x"+accent)).With(def));mml.stretchy=!!stretchy;var mo=c.isEmbellished()?c.CoreMO():c;mo.isa(MML.mo)&&(mo.movablelimits=!1),this.Push(MML.TeXAtom(MML.munderover(c,null,mml).With({accent:!0})))},UnderOver:function(name,c,stack,noaccent){var pos={o:"over",u:"under"}[name.charAt(1)],base=this.ParseArg(name);base.Get("movablelimits")&&(base.movablelimits=!1),base.isa(MML.munderover)&&base.isEmbellished()&&(base.Core().With({lspace:0,rspace:0}),base=MML.mrow(MML.mo().With({rspace:0}),base));var mml=MML.munderover(base,null,null);mml.SetData(mml[pos],this.mmlToken(MML.mo(MML.entity("#x"+c)).With({stretchy:!0,accent:!noaccent}))),stack&&(mml=MML.TeXAtom(mml).With({texClass:MML.TEXCLASS.OP,movesupsub:!0})),this.Push(mml.With({subsupOK:!0}))},Overset:function(name){var top=this.ParseArg(name),base=this.ParseArg(name);base.movablelimits=!1,this.Push(MML.mover(base,top))},Underset:function(name){var bot=this.ParseArg(name),base=this.ParseArg(name);base.movablelimits=!1,this.Push(MML.munder(base,bot))},TeXAtom:function(name,mclass){var def={texClass:mclass},mml;if(mclass==MML.TEXCLASS.OP){def.movesupsub=def.movablelimits=!0;var arg=this.GetArgument(name),match=arg.match(/^\s*\\rm\s+([a-zA-Z0-9 ]+)$/);match?(def.mathvariant=MML.VARIANT.NORMAL,mml=STACKITEM.fn(this.mmlToken(MML.mi(match[1]).With(def)))):mml=STACKITEM.fn(MML.TeXAtom(TEX.Parse(arg,this.stack.env).mml()).With(def))}else mml=MML.TeXAtom(this.ParseArg(name)).With(def);this.Push(mml)},MmlToken:function(name){var type=this.GetArgument(name),attr=this.GetBrackets(name,"").replace(/^\s+/,""),data=this.GetArgument(name),def={attrNames:[]},match;for(MML[type]&&MML[type].prototype.isToken||TEX.Error(["NotMathMLToken","%1 is not a token element",type]);""!==attr;){(match=attr.match(/^([a-z]+)\s*=\s*('[^']*'|"[^"]*"|[^ ,]*)\s*,?\s*/i))||TEX.Error(["InvalidMathMLAttr","Invalid MathML attribute: %1",attr]),null!=MML[type].prototype.defaults[match[1]]||this.MmlTokenAllow[match[1]]||TEX.Error(["UnknownAttrForElement","%1 is not a recognized attribute for %2",match[1],type]);var value=this.MmlFilterAttribute(match[1],match[2].replace(/^(['"])(.*)\1$/,"$2"));value&&("true"===value.toLowerCase()?value=!0:"false"===value.toLowerCase()&&(value=!1),def[match[1]]=value,def.attrNames.push(match[1])),attr=attr.substr(match[0].length)}this.Push(this.mmlToken(MML[type](data).With(def)))},MmlFilterAttribute:function(name,value){return value},MmlTokenAllow:{fontfamily:1,fontsize:1,fontweight:1,fontstyle:1,color:1,background:1,id:1,class:1,href:1,style:1},Strut:function(name){this.Push(MML.mpadded(MML.mrow()).With({height:"8.6pt",depth:"3pt",width:0}))},Phantom:function(name,v,h){var box=MML.mphantom(this.ParseArg(name));(v||h)&&(box=MML.mpadded(box),h&&(box.height=box.depth=0),v&&(box.width=0)),this.Push(MML.TeXAtom(box))},Smash:function(name){var bt=this.trimSpaces(this.GetBrackets(name,"")),smash=MML.mpadded(this.ParseArg(name));switch(bt){case"b":smash.depth=0;break;case"t":smash.height=0;break;default:smash.height=smash.depth=0}this.Push(MML.TeXAtom(smash))},Lap:function(name){var mml=MML.mpadded(this.ParseArg(name)).With({width:0});"\\llap"===name&&(mml.lspace="-1width"),this.Push(MML.TeXAtom(mml))},RaiseLower:function(name){var h=this.GetDimen(name),item=STACKITEM.position().With({name:name,move:"vertical"});"-"===h.charAt(0)&&(h=h.slice(1),name={raise:"\\lower",lower:"\\raise"}[name.substr(1)]),"\\lower"===name?(item.dh="-"+h,item.dd="+"+h):(item.dh="+"+h,item.dd="-"+h),this.Push(item)},MoveLeftRight:function(name){var h=this.GetDimen(name),nh="-"===h.charAt(0)?h.slice(1):"-"+h;if("\\moveleft"===name){var tmp=h;h=nh,nh=tmp}this.Push(STACKITEM.position().With({name:name,move:"horizontal",left:MML.mspace().With({width:h,mathsize:MML.SIZE.NORMAL}),right:MML.mspace().With({width:nh,mathsize:MML.SIZE.NORMAL})}))},Hskip:function(name){this.Push(MML.mspace().With({width:this.GetDimen(name),mathsize:MML.SIZE.NORMAL}))},Rule:function(name,style){var w,h,d,def={width:this.GetDimen(name),height:this.GetDimen(name),depth:this.GetDimen(name)};"blank"!==style&&(def.mathbackground=this.stack.env.color||"black"),this.Push(MML.mspace().With(def))},rule:function(name){var v=this.GetBrackets(name),w=this.GetDimen(name),h=this.GetDimen(name),mml=MML.mspace().With({width:w,height:h,mathbackground:this.stack.env.color||"black"});v&&(mml=MML.mpadded(mml).With({voffset:v}),v.match(/^\-/)?(mml.height=v,mml.depth="+"+v.substr(1)):mml.height="+"+v),this.Push(mml)},MakeBig:function(name,mclass,size){size*=TEXDEF.p_height,size=String(size).replace(/(\.\d\d\d).+/,"$1")+"em";var delim=this.GetDelimiter(name,!0);this.Push(MML.mstyle(MML.TeXAtom(MML.mo(delim).With({minsize:size,maxsize:size,fence:!0,stretchy:!0,symmetric:!0})).With({texClass:mclass})).With({scriptlevel:0}))},BuildRel:function(name){var top=this.ParseUpTo(name,"\\over"),bot=this.ParseArg(name);this.Push(MML.TeXAtom(MML.munderover(bot,null,top)).With({texClass:MML.TEXCLASS.REL}))},HBox:function(name,style){this.Push.apply(this,this.InternalMath(this.GetArgument(name),style))},FBox:function(name){this.Push(MML.menclose.apply(MML,this.InternalMath(this.GetArgument(name))).With({notation:"box"}))},Not:function(name){this.Push(STACKITEM.not())},Dots:function(name){this.Push(STACKITEM.dots().With({ldots:this.mmlToken(MML.mo(MML.entity("#x2026")).With({stretchy:!1})),cdots:this.mmlToken(MML.mo(MML.entity("#x22EF")).With({stretchy:!1}))}))},Require:function(name){var file=this.GetArgument(name).replace(/.*\//,"").replace(/[^a-z0-9_.-]/gi,"");this.Extension(null,file)},Extension:function(name,file,array){(file=TEX.extensionDir+"/"+file).match(/\.js$/)||(file+=".js"),AJAX.loaded[AJAX.fileURL(file)]||(null!=name&&delete TEXDEF[array||"macros"][name.replace(/^\\/,"")],HUB.RestartAfter(AJAX.Require(file)))},Macro:function(name,macro,argcount,def){if(argcount){var args=[];if(null!=def){var optional=this.GetBrackets(name);args.push(null==optional?def:optional)}for(var i=args.length;i<argcount;i++)args.push(this.GetArgument(name));macro=this.SubstituteArgs(args,macro)}this.string=this.AddArgs(macro,this.string.slice(this.i)),this.i=0,++this.macroCount>TEX.config.MAXMACROS&&TEX.Error(["MaxMacroSub1","MathJax maximum macro substitution count exceeded; is there a recursive macro call?"])},Matrix:function(name,open,close,align,spacing,vspacing,style,cases,numbered){var c=this.GetNext();""===c&&TEX.Error(["MissingArgFor","Missing argument for %1",name]),"{"===c?this.i++:(this.string=c+"}"+this.string.slice(this.i+1),this.i=0);var array=STACKITEM.array().With({requireClose:!0,arraydef:{rowspacing:vspacing||"4pt",columnspacing:spacing||"1em"}});cases&&(array.isCases=!0),numbered&&(array.isNumbered=!0,array.arraydef.side=numbered),(open||close)&&(array.open=open,array.close=close),"D"===style&&(array.arraydef.displaystyle=!0),null!=align&&(array.arraydef.columnalign=align),this.Push(array)},Entry:function(name){if(this.Push(STACKITEM.cell().With({isEntry:!0,name:name})),this.stack.Top().isCases){for(var string=this.string,braces=0,close=-1,i=this.i,m=string.length;i<m;){var c=string.charAt(i);"{"===c?(braces++,i++):"}"===c?0===braces?m=0:(0===--braces&&close<0&&(close=i-this.i),i++):"&"===c&&0===braces?TEX.Error(["ExtraAlignTab","Extra alignment tab in \\cases text"]):"\\"===c?string.substr(i).match(/^((\\cr)[^a-zA-Z]|\\\\)/)?m=0:i+=2:i++}var text=string.substr(this.i,i-this.i);text.match(/^\s*\\text[^a-zA-Z]/)&&close===text.replace(/\s+$/,"").length-1||(this.Push.apply(this,this.InternalMath(text,0)),this.i=i)}},Cr:function(name){this.Push(STACKITEM.cell().With({isCR:!0,name:name}))},CrLaTeX:function(name){var n;"["===this.string.charAt(this.i)&&(n=this.GetBrackets(name,"").replace(/ /g,"").replace(/,/,"."))&&!this.matchDimen(n)&&TEX.Error(["BracketMustBeDimension","Bracket argument to %1 must be a dimension",name]),this.Push(STACKITEM.cell().With({isCR:!0,name:name,linebreak:!0}));var top=this.stack.Top();if(top.isa(STACKITEM.array)){if(n&&top.arraydef.rowspacing){var rows=top.arraydef.rowspacing.split(/ /);for(top.rowspacing||(top.rowspacing=this.dimen2em(rows[0]));rows.length<top.table.length;)rows.push(this.Em(top.rowspacing));rows[top.table.length-1]=this.Em(Math.max(0,top.rowspacing+this.dimen2em(n))),top.arraydef.rowspacing=rows.join(" ")}}else n&&this.Push(MML.mspace().With({depth:n})),this.Push(MML.mspace().With({linebreak:MML.LINEBREAK.NEWLINE}))},emPerInch:7.2,pxPerInch:72,matchDimen:function(dim){return dim.match(/^(-?(?:\.\d+|\d+(?:\.\d*)?))(px|pt|em|ex|mu|pc|in|mm|cm)$/)},dimen2em:function(dim){var match=this.matchDimen(dim),m=parseFloat(match[1]||"1"),unit=match[2];return"em"===unit?m:"ex"===unit?.43*m:"pt"===unit?m/10:"pc"===unit?1.2*m:"px"===unit?m*this.emPerInch/this.pxPerInch:"in"===unit?m*this.emPerInch:"cm"===unit?m*this.emPerInch/2.54:"mm"===unit?m*this.emPerInch/25.4:"mu"===unit?m/18:0},Em:function(m){return Math.abs(m)<6e-4?"0em":m.toFixed(3).replace(/\.?0+$/,"")+"em"},HLine:function(name,style){null==style&&(style="solid");var top=this.stack.Top();if(top.isa(STACKITEM.array)&&!top.data.length||TEX.Error(["Misplaced","Misplaced %1",name]),0==top.table.length)top.frame.push("top");else{for(var lines=top.arraydef.rowlines?top.arraydef.rowlines.split(/ /):[];lines.length<top.table.length;)lines.push("none");lines[top.table.length-1]=style,top.arraydef.rowlines=lines.join(" ")}},HFill:function(name){var top=this.stack.Top();top.isa(STACKITEM.array)?top.hfill.push(top.data.length):TEX.Error(["UnsupportedHFill","Unsupported use of %1",name])},BeginEnd:function(name){var env=this.GetArgument(name),isEnd=!1;env.match(/^\\end\\/)&&(isEnd=!0,env=env.substr(5)),env.match(/\\/i)&&TEX.Error(["InvalidEnv","Invalid environment name '%1'",env]);var cmd=this.envFindName(env);cmd||TEX.Error(["UnknownEnv","Unknown environment '%1'",env]),isArray(cmd)||(cmd=[cmd]);var end=isArray(cmd[1])?cmd[1][0]:cmd[1],mml=STACKITEM.begin().With({name:env,end:end,parse:this});"\\end"===name?mml=!isEnd&&isArray(cmd[1])&&this[cmd[1][1]]?this[cmd[1][1]].apply(this,[mml].concat(cmd.slice(2))):STACKITEM.end().With({name:env}):(++this.macroCount>TEX.config.MAXMACROS&&TEX.Error(["MaxMacroSub2","MathJax maximum substitution count exceeded; is there a recursive latex environment?"]),cmd[0]&&this[cmd[0]]&&(mml=this[cmd[0]].apply(this,[mml].concat(cmd.slice(2))))),this.Push(mml)},envFindName:function(name){return TEXDEF.environment.hasOwnProperty(name)?TEXDEF.environment[name]:null},Equation:function(begin,row){return row},ExtensionEnv:function(begin,file){this.Extension(begin.name,file,"environment")},Array:function(begin,open,close,align,spacing,vspacing,style,raggedHeight){align||(align=this.GetArgument("\\begin{"+begin.name+"}"));var lines=("c"+align).replace(/[^clr|:]/g,"").replace(/[^|:]([|:])+/g,"$1");align=(align=align.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var array=STACKITEM.array().With({arraydef:{columnalign:align,columnspacing:spacing||"1em",rowspacing:vspacing||"4pt"}});return lines.match(/[|:]/)&&(lines.charAt(0).match(/[|:]/)&&(array.frame.push("left"),array.frame.dashed=":"===lines.charAt(0)),lines.charAt(lines.length-1).match(/[|:]/)&&array.frame.push("right"),lines=lines.substr(1,lines.length-2),array.arraydef.columnlines=lines.split("").join(" ").replace(/[^|: ]/g,"none").replace(/\|/g,"solid").replace(/:/g,"dashed")),open&&(array.open=this.convertDelimiter(open)),close&&(array.close=this.convertDelimiter(close)),"D"===style?array.arraydef.displaystyle=!0:style&&(array.arraydef.displaystyle=!1),"S"===style&&(array.arraydef.scriptlevel=1),raggedHeight&&(array.arraydef.useHeight=!1),this.Push(begin),array},AlignedArray:function(begin){var align=this.GetBrackets("\\begin{"+begin.name+"}");return this.setArrayAlign(this.Array.apply(this,arguments),align)},setArrayAlign:function(array,align){return"t"===(align=this.trimSpaces(align||""))?array.arraydef.align="baseline 1":"b"===align?array.arraydef.align="baseline -1":"c"===align?array.arraydef.align="center":align&&(array.arraydef.align=align),array},convertDelimiter:function(c){return c&&(c=TEXDEF.delimiter.hasOwnProperty(c)?TEXDEF.delimiter[c]:null),null==c?null:(isArray(c)&&(c=c[0]),4===c.length&&(c=String.fromCharCode(parseInt(c,16))),c)},trimSpaces:function(text){if("string"!=typeof text)return text;var TEXT=text.replace(/^\s+|\s+$/g,"");return TEXT.match(/\\$/)&&text.match(/ $/)&&(TEXT+=" "),TEXT},nextIsSpace:function(){return this.string.charAt(this.i).match(/\s/)},GetNext:function(){for(;this.nextIsSpace();)this.i++;return this.string.charAt(this.i)},GetCS:function(){var CS=this.string.slice(this.i).match(/^([a-z]+|.) ?/i);return CS?(this.i+=CS[1].length,CS[1]):(this.i++," ")},GetArgument:function(name,noneOK){switch(this.GetNext()){case"":return noneOK||TEX.Error(["MissingArgFor","Missing argument for %1",name]),null;case"}":return noneOK||TEX.Error(["ExtraCloseMissingOpen","Extra close brace or missing open brace"]),null;case"\\":return this.i++,"\\"+this.GetCS();case"{":for(var j=++this.i,parens=1;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"\\":this.i++;break;case"{":parens++;break;case"}":if(0==--parens)return this.string.slice(j,this.i-1)}TEX.Error(["MissingCloseBrace","Missing close brace"])}return this.string.charAt(this.i++)},GetBrackets:function(name,def){if("["!=this.GetNext())return def;for(var j=++this.i,parens=0;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"{":parens++;break;case"\\":this.i++;break;case"}":parens--<=0&&TEX.Error(["ExtraCloseLooking","Extra close brace while looking for %1","']'"]);break;case"]":if(0==parens)return this.string.slice(j,this.i-1)}TEX.Error(["MissingCloseBracket","Couldn't find closing ']' for argument to %1",name])},GetDelimiter:function(name,braceOK){for(;this.nextIsSpace();)this.i++;var c=this.string.charAt(this.i);if(this.i++,this.i<=this.string.length&&("\\"==c?c+=this.GetCS(name):"{"===c&&braceOK&&(this.i--,c=this.GetArgument(name).replace(/^\s+/,"").replace(/\s+$/,"")),TEXDEF.delimiter.hasOwnProperty(c)))return this.convertDelimiter(c);TEX.Error(["MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",name])},GetDimen:function(name){var dimen;if(this.nextIsSpace()&&this.i++,"{"==this.string.charAt(this.i)){if((dimen=this.GetArgument(name)).match(/^\s*([-+]?([.,]\d+|\d+([.,]\d*)?))\s*(pt|em|ex|mu|px|mm|cm|in|pc)\s*$/))return dimen.replace(/ /g,"").replace(/,/,".")}else{var match=(dimen=this.string.slice(this.i)).match(/^\s*(([-+]?([.,]\d+|\d+([.,]\d*)?))\s*(pt|em|ex|mu|px|mm|cm|in|pc)) ?/);if(match)return this.i+=match[0].length,match[1].replace(/ /g,"").replace(/,/,".")}TEX.Error(["MissingDimOrUnits","Missing dimension or its units for %1",name])},GetUpTo:function(name,token){for(;this.nextIsSpace();)this.i++;for(var j=this.i,k,c,parens=0;this.i<this.string.length;){switch(k=this.i,c=this.string.charAt(this.i++)){case"\\":c+=this.GetCS();break;case"{":parens++;break;case"}":0==parens&&TEX.Error(["ExtraCloseLooking","Extra close brace while looking for %1",token]),parens--}if(0==parens&&c==token)return this.string.slice(j,k)}TEX.Error(["TokenNotFoundForCommand","Couldn't find %1 for %2",token,name])},ParseArg:function(name){return TEX.Parse(this.GetArgument(name),this.stack.env).mml()},ParseUpTo:function(name,token){return TEX.Parse(this.GetUpTo(name,token),this.stack.env).mml()},InternalMath:function(text,level){var def=this.stack.env.font?{mathvariant:this.stack.env.font}:{},mml=[],i=0,k=0,c,match="",braces=0;if(text.match(/\\?[${}\\]|\\\(|\\(eq)?ref\s*\{/)){for(;i<text.length;)if("$"===(c=text.charAt(i++)))"$"===match&&0===braces?(mml.push(MML.TeXAtom(TEX.Parse(text.slice(k,i-1),{}).mml())),match="",k=i):""===match&&(k<i-1&&mml.push(this.InternalText(text.slice(k,i-1),def)),match="$",k=i);else if("{"===c&&""!==match)braces++;else if("}"===c)"}"===match&&0===braces?(mml.push(MML.TeXAtom(TEX.Parse(text.slice(k,i),{}).mml().With(def))),match="",k=i):""!==match&&braces&&braces--;else if("\\"===c)if(""===match&&text.substr(i).match(/^(eq)?ref\s*\{/)){var len=RegExp["$&"].length;k<i-1&&mml.push(this.InternalText(text.slice(k,i-1),def)),match="}",k=i-1,i+=len}else"("===(c=text.charAt(i++))&&""===match?(k<i-2&&mml.push(this.InternalText(text.slice(k,i-2),def)),match=")",k=i):")"===c&&")"===match&&0===braces?(mml.push(MML.TeXAtom(TEX.Parse(text.slice(k,i-2),{}).mml())),match="",k=i):c.match(/[${}\\]/)&&""===match&&(i--,text=text.substr(0,i-1)+text.substr(i));""!==match&&TEX.Error(["MathNotTerminated","Math not terminated in text box"])}return k<text.length&&mml.push(this.InternalText(text.slice(k),def)),null!=level?mml=[MML.mstyle.apply(MML,mml).With({displaystyle:!1,scriptlevel:level})]:mml.length>1&&(mml=[MML.mrow.apply(MML,mml)]),mml},InternalText:function(text,def){return text=text.replace(/^\s+/," ").replace(/\s+$/," "),MML.mtext(MML.chars(text)).With(def)},setDef:function(name,value){value.isUser=!0,TEXDEF.macros[name]=value},setEnv:function(name,value){value.isUser=!0,TEXDEF.environment[name]=value},SubstituteArgs:function(args,string){for(var text="",newstring="",c,i=0;i<string.length;)"\\"===(c=string.charAt(i++))?text+=c+string.charAt(i++):"#"===c?"#"===(c=string.charAt(i++))?text+=c:((!c.match(/[1-9]/)||c>args.length)&&TEX.Error(["IllegalMacroParam","Illegal macro parameter reference"]),newstring=this.AddArgs(this.AddArgs(newstring,text),args[c-1]),text=""):text+=c;return this.AddArgs(newstring,text)},AddArgs:function(s1,s2){return s2.match(/^[a-z]/i)&&s1.match(/(^|[^\\])(\\\\)*\\[a-z]+$/i)&&(s1+=" "),s1.length+s2.length>TEX.config.MAXBUFFER&&TEX.Error(["MaxBufferSize","MathJax internal buffer size exceeded; is there a recursive macro call?"]),s1+s2}});TEX.Augment({Stack:STACK,Parse:PARSE,Definitions:TEXDEF,Startup:STARTUP,config:{MAXMACROS:1e4,MAXBUFFER:5120},sourceMenuTitle:["TeXCommands","TeX Commands"],annotationEncoding:"application/x-tex",prefilterHooks:MathJax.Callback.Hooks(!0),postfilterHooks:MathJax.Callback.Hooks(!0),Config:function(){this.SUPER(arguments).Config.apply(this,arguments),"none"!==this.config.equationNumbers.autoNumber&&(this.config.extensions||(this.config.extensions=[]),this.config.extensions.push("AMSmath.js"))},Translate:function(script){var mml,isError=!1,math=MathJax.HTML.getScript(script),display=null!=script.type.replace(/\n/g," ").match(/(;|\s|\n)mode\s*=\s*display(;|\s|\n|$)/),data={math:math,display:display,script:script},callback=this.prefilterHooks.Execute(data);if(callback)return callback;math=data.math;try{mml=TEX.Parse(math).mml()}catch(err){if(!err.texError)throw err;mml=this.formatError(err,math,display,script),isError=!0}return mml.isa(MML.mtable)&&"inherit"===mml.displaystyle&&(mml.displaystyle=display),mml=mml.inferred?MML.apply(MathJax.ElementJax,mml.data):MML(mml),display&&(mml.root.display="block"),isError&&(mml.texError=!0),data.math=mml,this.postfilterHooks.Execute(data)||data.math},prefilterMath:function(math,displaystyle,script){return math},postfilterMath:function(math,displaystyle,script){return this.combineRelations(math.root),math},formatError:function(err,math,display,script){var message=err.message.replace(/\n.*/,"");return HUB.signal.Post(["TeX Jax - parse error",message,math,display,script]),MML.Error(message)},Error:function(message){throw isArray(message)&&(message=_.apply(_,message)),HUB.Insert(Error(message),{texError:!0})},Macro:function(name,def,argn){TEXDEF.macros[name]=["Macro"].concat([].slice.call(arguments,1)),TEXDEF.macros[name].isUser=!0},fenced:function(open,mml,close){var mrow=MML.mrow().With({open:open,close:close,texClass:MML.TEXCLASS.INNER});return mrow.Append(MML.mo(open).With({fence:!0,stretchy:!0,symmetric:!0,texClass:MML.TEXCLASS.OPEN})),"mrow"===mml.type&&mml.inferred?mrow.Append.apply(mrow,mml.data):mrow.Append(mml),mrow.Append(MML.mo(close).With({fence:!0,stretchy:!0,symmetric:!0,texClass:MML.TEXCLASS.CLOSE})),mrow},fixedFence:function(open,mml,close){var mrow=MML.mrow().With({open:open,close:close,texClass:MML.TEXCLASS.ORD});return open&&mrow.Append(this.mathPalette(open,"l")),"mrow"===mml.type?mrow.Append.apply(mrow,mml.data):mrow.Append(mml),close&&mrow.Append(this.mathPalette(close,"r")),mrow},mathPalette:function(fence,side){"{"!==fence&&"}"!==fence||(fence="\\"+fence);var D="{\\bigg"+side+" "+fence+"}",T="{\\big"+side+" "+fence+"}";return TEX.Parse("\\mathchoice"+D+T+T+T,{}).mml()},combineRelations:function(mml){var i,m,m1,m2;for(i=0,m=mml.data.length;i<m;i++)if(mml.data[i]){if(mml.isa(MML.mrow))for(;i+1<m&&(m1=mml.data[i])&&(m2=mml.data[i+1])&&m1.isa(MML.mo)&&m2.isa(MML.mo)&&m1.Get("texClass")===MML.TEXCLASS.REL&&m2.Get("texClass")===MML.TEXCLASS.REL;)m1.variantForm!=m2.variantForm||m1.Get("mathvariant")!=m2.Get("mathvariant")||m1.style!=m2.style||m1.class!=m2.class||m1.id||m2.id?(m1.rspace=m2.lspace="0pt",i++):(m1.Append.apply(m1,m2.data),mml.data.splice(i+1,1),m--);mml.data[i].isToken||this.combineRelations(mml.data[i])}}}),TEX.prefilterHooks.Add((function(data){data.math=TEX.prefilterMath(data.math,data.display,data.script)})),TEX.postfilterHooks.Add((function(data){data.math=TEX.postfilterMath(data.math,data.display,data.script)})),TEX.loadComplete("jax.js")}(MathJax.InputJax.TeX,MathJax.Hub,MathJax.Ajax),MathJax.OutputJax.CommonHTML=MathJax.OutputJax({id:"CommonHTML",version:"2.7.5",directory:MathJax.OutputJax.directory+"/CommonHTML",extensionDir:MathJax.OutputJax.extensionDir+"/CommonHTML",autoloadDir:MathJax.OutputJax.directory+"/CommonHTML/autoload",fontDir:MathJax.OutputJax.directory+"/CommonHTML/fonts",webfontDir:MathJax.OutputJax.fontDir+"/HTML-CSS",config:{matchFontHeight:!0,scale:100,minScaleAdjust:50,mtextFontInherit:!1,undefinedFamily:"STIXGeneral,'Cambria Math','Arial Unicode MS',serif",EqnChunk:MathJax.Hub.Browser.isMobile?20:100,EqnChunkFactor:1.5,EqnChunkDelay:100,linebreaks:{automatic:!1,width:"container"}}}),MathJax.Hub.config.delayJaxRegistration||MathJax.OutputJax.CommonHTML.Register("jax/mml"),MathJax.OutputJax.CommonHTML.loadComplete("config.js"),function(AJAX,HUB,HTML,CHTML){var MML,isArray=MathJax.Object.isArray,EVENT,TOUCH,HOVER,STRUTHEIGHT=1,EFUZZ=.1,HFUZZ=.025,DFUZZ=.025,STYLES={".mjx-chtml":{display:"inline-block","line-height":0,"text-indent":0,"text-align":"left","text-transform":"none","font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","letter-spacing":"normal","word-wrap":"normal","word-spacing":"normal","white-space":"nowrap",float:"none",direction:"ltr","max-width":"none","max-height":"none","min-width":0,"min-height":0,border:0,margin:0,padding:"1px 0"},".MJXc-display":{display:"block","text-align":"center",margin:"1em 0",padding:0},".mjx-chtml[tabindex]:focus, body :focus .mjx-chtml[tabindex]":{display:"inline-table"},".mjx-full-width":{"text-align":"center",display:"table-cell!important",width:"10000em"},".mjx-math":{display:"inline-block","border-collapse":"separate","border-spacing":0},".mjx-math *":{display:"inline-block","-webkit-box-sizing":"content-box!important","-moz-box-sizing":"content-box!important","box-sizing":"content-box!important","text-align":"left"},".mjx-numerator":{display:"block","text-align":"center"},".mjx-denominator":{display:"block","text-align":"center"},".MJXc-stacked":{height:0,position:"relative"},".MJXc-stacked > *":{position:"absolute"},".MJXc-bevelled > *":{display:"inline-block"},".mjx-stack":{display:"inline-block"},".mjx-op":{display:"block"},".mjx-under":{display:"table-cell"},".mjx-over":{display:"block"},".mjx-over > *":{"padding-left":"0px!important","padding-right":"0px!important"},".mjx-under > *":{"padding-left":"0px!important","padding-right":"0px!important"},".mjx-stack > .mjx-sup":{display:"block"},".mjx-stack > .mjx-sub":{display:"block"},".mjx-prestack > .mjx-presup":{display:"block"},".mjx-prestack > .mjx-presub":{display:"block"},".mjx-delim-h > .mjx-char":{display:"inline-block"},".mjx-surd":{"vertical-align":"top"},".mjx-mphantom *":{visibility:"hidden"},".mjx-merror":{"background-color":"#FFFF88",color:"#CC0000",border:"1px solid #CC0000",padding:"2px 3px","font-style":"normal","font-size":"90%"},".mjx-annotation-xml":{"line-height":"normal"},".mjx-menclose > svg":{fill:"none",stroke:"currentColor"},".mjx-mtr":{display:"table-row"},".mjx-mlabeledtr":{display:"table-row"},".mjx-mtd":{display:"table-cell","text-align":"center"},".mjx-label":{display:"table-row"},".mjx-box":{display:"inline-block"},".mjx-block":{display:"block"},".mjx-span":{display:"inline"},".mjx-char":{display:"block","white-space":"pre"},".mjx-itable":{display:"inline-table",width:"auto"},".mjx-row":{display:"table-row"},".mjx-cell":{display:"table-cell"},".mjx-table":{display:"table",width:"100%"},".mjx-line":{display:"block",height:0},".mjx-strut":{width:0,"padding-top":"1em"},".mjx-vsize":{width:0},".MJXc-space1":{"margin-left":".167em"},".MJXc-space2":{"margin-left":".222em"},".MJXc-space3":{"margin-left":".278em"},".mjx-chartest":{display:"block",visibility:"hidden",position:"absolute",top:0,"line-height":"normal","font-size":"500%"},".mjx-chartest .mjx-char":{display:"inline"},".mjx-chartest .mjx-box":{"padding-top":"1000px"},".MJXc-processing":{visibility:"hidden",position:"fixed",width:0,height:0,overflow:"hidden"},".MJXc-processed":{display:"none"},".mjx-test":{"font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","text-indent":0,"text-transform":"none","letter-spacing":"normal","word-spacing":"normal",overflow:"hidden",height:"1px"},".mjx-test.mjx-test-display":{display:"table!important"},".mjx-test.mjx-test-inline":{display:"inline!important","margin-right":"-1px"},".mjx-test.mjx-test-default":{display:"block!important",clear:"both"},".mjx-ex-box":{display:"inline-block!important",position:"absolute",overflow:"hidden","min-height":0,"max-height":"none",padding:0,border:0,margin:0,width:"1px",height:"60ex"},".mjx-test-inline .mjx-left-box":{display:"inline-block",width:0,float:"left"},".mjx-test-inline .mjx-right-box":{display:"inline-block",width:0,float:"right"},".mjx-test-display .mjx-right-box":{display:"table-cell!important",width:"10000em!important","min-width":0,"max-width":"none",padding:0,border:0,margin:0},"#MathJax_CHTML_Tooltip":{"background-color":"InfoBackground",color:"InfoText",border:"1px solid black","box-shadow":"2px 2px 5px #AAAAAA","-webkit-box-shadow":"2px 2px 5px #AAAAAA","-moz-box-shadow":"2px 2px 5px #AAAAAA","-khtml-box-shadow":"2px 2px 5px #AAAAAA",padding:"3px 4px","z-index":401,position:"absolute",left:0,top:0,width:"auto",height:"auto",display:"none"}},BIGDIMEN=1e6,MAXREMAP=5,LINEBREAKS={},CONFIG=MathJax.Hub.config;CHTML.Augment({settings:HUB.config.menuSettings,config:{styles:STYLES},Config:function(){this.require||(this.require=[]),this.SUPER(arguments).Config.call(this);var settings=this.settings;settings.scale&&(this.config.scale=settings.scale),this.require.push(this.fontDir+"/TeX/fontdata.js"),this.require.push(MathJax.OutputJax.extensionDir+"/MathEvents.js"),LINEBREAKS=this.config.linebreaks},Startup:function(){EVENT=MathJax.Extension.MathEvents.Event,TOUCH=MathJax.Extension.MathEvents.Touch,HOVER=MathJax.Extension.MathEvents.Hover,this.ContextMenu=EVENT.ContextMenu,this.Mousedown=EVENT.AltContextMenu,this.Mouseover=HOVER.Mouseover,this.Mouseout=HOVER.Mouseout,this.Mousemove=HOVER.Mousemove;var div=CHTML.addElement(document.body,"mjx-block",{style:{display:"block",width:"5in"}});return this.pxPerInch=div.offsetWidth/5,div.parentNode.removeChild(div),this.TestSpan=CHTML.Element("mjx-test",{style:{left:"1em"}},[["mjx-left-box"],["mjx-ex-box"],["mjx-right-box"]]),AJAX.Styles(this.config.styles,["InitializeCHTML",this])},InitializeCHTML:function(){if(this.getDefaultExEm(),!this.defaultEm){var ready=MathJax.Callback();return AJAX.timer.start(AJAX,(function(check){check.time(ready)?HUB.signal.Post(["CommonHTML Jax - no default em size"]):(CHTML.getDefaultExEm(),CHTML.defaultEm?ready():setTimeout(check,check.delay))}),this.defaultEmDelay,this.defaultEmTimeout),ready}},defaultEmDelay:100,defaultEmTimeout:1e3,getDefaultExEm:function(){var test=document.body.appendChild(this.TestSpan.cloneNode(!0));test.className+=" mjx-test-inline mjx-test-default",this.defaultEm=this.getFontSize(test),this.defaultEx=test.childNodes[1].offsetHeight/60,this.defaultWidth=Math.max(0,test.lastChild.offsetLeft-test.firstChild.offsetLeft-2),document.body.removeChild(test)},getFontSize:window.getComputedStyle?function(node){var style=window.getComputedStyle(node);return parseFloat(style.fontSize)}:function(node){return node.style.pixelLeft},getMaxWidth:window.getComputedStyle?function(node){var style=window.getComputedStyle(node);return"none"!==style.maxWidth?parseFloat(style.maxWidth):0}:function(node){var max=node.currentStyle.maxWidth;if("none"!==max){if(max.match(/\d*px/))return parseFloat(max);var left=node.style.left;return node.style.left=max,max=node.style.pixelLeft,node.style.left=left,max}return 0},loadFont:function(font){HUB.RestartAfter(AJAX.Require(this.fontDir+"/"+font))},fontLoaded:function(font){font.match(/-|fontdata/)||(font+="-Regular"),font.match(/\.js$/)||(font+=".js"),MathJax.Callback.Queue(["Post",HUB.Startup.signal,"CommonHTML - font data loaded for "+font],["loadComplete",AJAX,this.fontDir+"/"+font])},Element:function(type,def,content){return"mjx-"===type.substr(0,4)&&(def||(def={}),null==def.isMathJax&&(def.isMathJax=!0),def.className?def.className=type+" "+def.className:def.className=type,type="span"),this.HTMLElement(type,def,content)},addElement:function(node,type,def,content){return node.appendChild(this.Element(type,def,content))},HTMLElement:HTML.Element,ucMatch:HTML.ucMatch,setScript:HTML.setScript,getNode:function(node,type){for(var name=RegExp("\\b"+type+"\\b"),nodes=[];node;){for(var i=0,m=node.childNodes.length;i<m;i++){var child=node.childNodes[i];if(child){if(name.test(child.className))return child;""===child.id&&nodes.push(child)}}node=nodes.shift()}return null},preTranslate:function(state){var scripts=state.jax[this.id],i,m=scripts.length,script,prev,node,test,jax,ex,em,scale,maxwidth=1e5,relwidth=!1,cwidth=0,linebreak=LINEBREAKS.automatic,width=LINEBREAKS.width;for(linebreak&&((relwidth=!!width.match(/^\s*(\d+(\.\d*)?%\s*)?container\s*$/))?width=width.replace(/\s*container\s*/,""):maxwidth=this.defaultWidth,""===width&&(width="100%")),i=0;i<m;i++)if((script=scripts[i]).parentNode&&((prev=script.previousSibling)&&prev.className&&"mjx-chtml"===String(prev.className).substr(0,9)&&prev.parentNode.removeChild(prev),script.MathJax.preview&&(script.MathJax.preview.style.display="none"),jax=script.MathJax.elementJax)){if(jax.CHTML={display:"block"===jax.root.Get("display"),preview:(jax.CHTML||{}).preview},node=CHTML.Element("mjx-chtml",{id:jax.inputID+"-Frame",className:"MathJax_CHTML",isMathJax:!0,jaxID:this.id,oncontextmenu:EVENT.Menu,onmousedown:EVENT.Mousedown,onmouseover:EVENT.Mouseover,onmouseout:EVENT.Mouseout,onmousemove:EVENT.Mousemove,onclick:EVENT.Click,ondblclick:EVENT.DblClick,onkeydown:EVENT.Keydown,tabIndex:HUB.getTabOrder(jax)}),jax.CHTML.display){var NODE=CHTML.Element("mjx-chtml",{className:"MJXc-display",isMathJax:!1});NODE.appendChild(node),node=NODE}HUB.Browser.noContextMenu&&(node.ontouchstart=TOUCH.start,node.ontouchend=TOUCH.end),node.className+=" MJXc-processing",script.parentNode.insertBefore(node,script),(test=this.TestSpan.cloneNode(!0)).className+=" mjx-test-"+(jax.CHTML.display?"display":"inline"),script.parentNode.insertBefore(test,script)}for(i=0;i<m;i++)(script=scripts[i]).parentNode&&(test=script.previousSibling,(jax=script.MathJax.elementJax)&&(em=CHTML.getFontSize(test),ex=test.childNodes[1].offsetHeight/60,cwidth=Math.max(0,jax.CHTML.display?test.lastChild.offsetWidth-1:test.lastChild.offsetLeft-test.firstChild.offsetLeft-2),0!==ex&&"NaN"!==ex||(ex=this.defaultEx,cwidth=this.defaultWidth),0!==cwidth||jax.CHTML.display||(cwidth=this.defaultWidth),relwidth&&(maxwidth=cwidth),scale=this.config.matchFontHeight?ex/this.TEX.x_height/em:1,scale=Math.floor(Math.max(this.config.minScaleAdjust/100,scale)*this.config.scale),jax.CHTML.scale=scale/100,jax.CHTML.fontSize=scale+"%",jax.CHTML.outerEm=em,jax.CHTML.em=this.em=em*scale/100,jax.CHTML.ex=ex,jax.CHTML.cwidth=cwidth/this.em,jax.CHTML.lineWidth=linebreak?this.length2em(width,maxwidth/this.em,1):maxwidth));for(i=0;i<m;i++)(script=scripts[i]).parentNode&&(jax=script.MathJax.elementJax)&&(script.parentNode.removeChild(script.previousSibling),script.MathJax.preview&&(script.MathJax.preview.style.display=""));state.CHTMLeqn=state.CHTMLlast=0,state.CHTMLi=-1,state.CHTMLchunk=this.config.EqnChunk,state.CHTMLdelay=!1},Translate:function(script,state){if(script.parentNode){state.CHTMLdelay&&(state.CHTMLdelay=!1,HUB.RestartAfter(MathJax.Callback.Delay(this.config.EqnChunkDelay)));var jax=script.MathJax.elementJax,math=jax.root,node=document.getElementById(jax.inputID+"-Frame");if(node){this.getMetrics(jax),1!==this.scale&&(node.style.fontSize=jax.CHTML.fontSize),this.initCHTML(math,node),this.savePreview(script),this.CHTMLnode=node;try{math.setTeXclass(),math.toCommonHTML(node)}catch(err){for(;node.firstChild;)node.removeChild(node.firstChild);throw delete this.CHTMLnode,this.restorePreview(script),err}delete this.CHTMLnode,this.restorePreview(script),jax.CHTML.display&&(node=node.parentNode),node.className=node.className.replace(/ [^ ]+$/,""),node.className+=" MJXc-processed",script.MathJax.preview&&(jax.CHTML.preview=script.MathJax.preview,delete script.MathJax.preview),state.CHTMLeqn+=state.i-state.CHTMLi,state.CHTMLi=state.i,state.CHTMLeqn>=state.CHTMLlast+state.CHTMLchunk&&(this.postTranslate(state),state.CHTMLchunk=Math.floor(state.CHTMLchunk*this.config.EqnChunkFactor),state.CHTMLdelay=!0)}}},initCHTML:function(math,node){},savePreview:function(script){var preview=script.MathJax.preview;preview&&preview.parentNode&&(script.MathJax.tmpPreview=document.createElement("span"),preview.parentNode.replaceChild(script.MathJax.tmpPreview,preview))},restorePreview:function(script){var tmpPreview=script.MathJax.tmpPreview;tmpPreview&&(tmpPreview.parentNode.replaceChild(script.MathJax.preview,tmpPreview),delete script.MathJax.tmpPreview)},getMetrics:function(jax){var data=jax.CHTML;this.jax=jax,this.em=data.em,this.outerEm=data.outerEm,this.scale=data.scale,this.cwidth=data.cwidth,this.linebreakWidth=data.lineWidth},postTranslate:function(state){for(var scripts=state.jax[this.id],i=state.CHTMLlast,m=state.CHTMLeqn;i<m;i++){var script=scripts[i];if(script&&script.MathJax.elementJax){script.previousSibling.className=script.previousSibling.className.replace(/ [^ ]+$/,"");var data=script.MathJax.elementJax.CHTML;data.preview&&(data.preview.innerHTML="",script.MathJax.preview=data.preview,delete data.preview)}}state.CHTMLlast=state.CHTMLeqn},getJaxFromMath:function(math){math.parentNode.className.match(/MJXc-display/)&&(math=math.parentNode);do{math=math.nextSibling}while(math&&"script"!==math.nodeName.toLowerCase());return HUB.getJaxFor(math)},getHoverSpan:function(jax,math){return jax.root.CHTMLnodeElement()},getHoverBBox:function(jax,span,math){var bbox=jax.root.CHTML,em=jax.CHTML.outerEm,BBOX={w:bbox.w*em,h:bbox.h*em,d:bbox.d*em};return bbox.width&&(BBOX.width=bbox.width),BBOX},Zoom:function(jax,span,math,Mw,Mh){this.getMetrics(jax);var node=CHTML.addElement(span,"mjx-chtml",{style:{"font-size":Math.floor(100*CHTML.scale)+"%"},isMathJax:!1});CHTML.CHTMLnode=node,this.idPostfix="-zoom",jax.root.toCommonHTML(node),this.idPostfix="";var style=node.style,bbox=jax.root.CHTML;bbox.t>bbox.h&&(style.marginTop=CHTML.Em(bbox.t-bbox.h)),bbox.b>bbox.d&&(style.marginBottom=CHTML.Em(bbox.b-bbox.d)),bbox.l<0&&(style.paddingLeft=CHTML.Em(-bbox.l)),bbox.r>bbox.w&&(style.marginRight=CHTML.Em(bbox.r-bbox.w)),style.position="absolute";var zW=node.offsetWidth,zH=node.offsetHeight,mH=math.firstChild.offsetHeight,mW=math.firstChild.offsetWidth;return node.style.position="",{Y:-EVENT.getBBox(span).h,mW:mW,mH:mH,zW:zW,zH:zH}},Remove:function(jax){var node=document.getElementById(jax.inputID+"-Frame");node&&jax.CHTML.display&&(node=node.parentNode),node&&node.parentNode.removeChild(node),delete jax.CHTML},ID:0,idPostfix:"",GetID:function(){return this.ID++,this.ID},MATHSPACE:{veryverythinmathspace:1/18,verythinmathspace:2/18,thinmathspace:3/18,mediummathspace:4/18,thickmathspace:5/18,verythickmathspace:6/18,veryverythickmathspace:7/18,negativeveryverythinmathspace:-1/18,negativeverythinmathspace:-2/18,negativethinmathspace:-3/18,negativemediummathspace:-4/18,negativethickmathspace:-5/18,negativeverythickmathspace:-6/18,negativeveryverythickmathspace:-7/18,thin:.04,medium:.06,thick:.1,infinity:1e6},SPACECLASS:{thinmathspace:"MJXc-space1",mediummathspace:"MJXc-space2",thickmathspace:"MJXc-space3"},pxPerInch:96,em:16,maxStretchyParts:1e3,FONTDEF:{},TEXDEF:{x_height:.442,quad:1,num1:.676508,num2:.393732,num3:.44373,denom1:.685951,denom2:.344841,sup1:.412892,sup2:.362892,sup3:.288888,sub1:.15,sub2:.247217,sup_drop:.386108,sub_drop:.05,delim1:2.39,delim2:1,axis_height:.25,rule_thickness:.06,big_op_spacing1:.111111,big_op_spacing2:.166666,big_op_spacing3:.2,big_op_spacing4:.45,big_op_spacing5:.1,surd_height:.075,scriptspace:.05,nulldelimiterspace:.12,delimiterfactor:901,delimitershortfall:.3,min_rule_thickness:1.25},isChar:function(text){if(1===text.length)return!0;if(2!==text.length)return!1;var n=text.charCodeAt(0);return n>=55296&&n<56319},unicodeChar:function(n){return n<65535?String.fromCharCode(n):(n-=65536,String.fromCharCode(55296+(n>>10))+String.fromCharCode(56320+(1023&n)))},getUnicode:function(string){var n=string.text.charCodeAt(string.i);return string.i++,n>=55296&&n<56319&&(n=(n-55296<<10)+(string.text.charCodeAt(string.i)-56320)+65536,string.i++),n},getCharList:function(variant,n){var id,M,cache=variant.cache,nn=n;if(cache[n])return cache[n];if(n>65535&&this.FONTDATA.RemapPlane1){var nv=this.FONTDATA.RemapPlane1(n,variant);n=nv.n,variant=nv.variant}var RANGES=this.FONTDATA.RANGES,VARIANT=this.FONTDATA.VARIANT;if(n>=RANGES[0].low&&n<=RANGES[RANGES.length-1].high)for(id=0,M=RANGES.length;id<M;id++)if("alpha"!==RANGES[id].name||!variant.noLowerCase){var N=variant["offset"+RANGES[id].offset];if(N&&n>=RANGES[id].low&&n<=RANGES[id].high){RANGES[id].remap&&RANGES[id].remap[n]?n=N+RANGES[id].remap[n]:(n=n-RANGES[id].low+N,RANGES[id].add&&(n+=RANGES[id].add)),variant["variant"+RANGES[id].offset]&&(variant=VARIANT[variant["variant"+RANGES[id].offset]]);break}}return cache[nn]=this.remapChar(variant,n,0),cache[nn]},remapChar:function(variant,n,N){var list=[],VARIANT=this.FONTDATA.VARIANT;if(variant.remap&&variant.remap[n]?(n=variant.remap[n],variant.remap.variant&&(variant=VARIANT[variant.remap.variant])):this.FONTDATA.REMAP[n]&&!variant.noRemap&&(n=this.FONTDATA.REMAP[n]),isArray(n)&&(n[2]&&(N=5),variant=VARIANT[n[1]],n=n[0]),"string"==typeof n)for(var string={text:n,i:0,length:n.length};string.i<string.length;){n=this.getUnicode(string);var chars=this.getCharList(variant,n);chars&&list.push.apply(list,chars)}else variant.cache[n]?list=variant.cache[n]:variant.cache[n]=list=this.lookupChar(variant,n,N);return list},lookupChar:function(variant,n,N){for(var VARIANT=variant;variant;){for(var i=0,m=variant.fonts.length;i<m;i++){var font=this.FONTDATA.FONTS[variant.fonts[i]];"string"==typeof font&&this.loadFont(font);var C=font[n];if(C)return this.fixChar(C,n),C[5].space?[{type:"space",w:C[2],font:font}]:[{type:"char",font:font,n:n}];font.Extra&&this.findBlock(font,n)}if((variant=this.FONTDATA.VARIANT[variant.chain])&&variant.remap&&variant.remap[n]&&N++<5)return this.remapChar(variant,n,N)}return[this.unknownChar(VARIANT,n)]},fixChar:function(C,n){return 5===C.length&&(C[5]={}),null==C.c&&(C[0]/=1e3,C[1]/=1e3,C[2]/=1e3,C[3]/=1e3,C[4]/=1e3,C.c=this.unicodeChar(n)),C},findBlock:function(font,n){for(var extra=font.Extra,name=font.file,file,i=0,m=extra.length;i<m;i++)if("number"==typeof extra[i]){if(n===extra[i]){file=name;break}}else{if(n<extra[i][0])return;if(n<=extra[i][1]){file=name;break}}file&&(delete font.Extra,this.loadFont(name))},unknownChar:function(variant,n){HUB.signal.Post(["CommonHTML Jax - unknown char",n,variant]);var id="";variant.bold&&(id+="B"),variant.italic&&(id+="I");var unknown=this.FONTDATA.UNKNOWN[id||"R"];return unknown[n]||this.getUnknownChar(unknown,n),{type:"unknown",n:n,font:unknown}},getUnknownChar:function(unknown,n){var c=this.unicodeChar(n),HDW=this.getHDW(c,unknown.className);unknown[n]=[.8,.2,HDW.w,0,HDW.w,{a:Math.max(0,(HDW.h-HDW.d)/2),h:HDW.h,d:HDW.d}],unknown[n].c=c},styledText:function(variant,text){HUB.signal.Post(["CommonHTML Jax - styled text",text,variant]);var style=variant.style,id="_"+(style["font-family"]||variant.className||"");style["font-weight"]&&(id+="_"+style["font-weight"]),style["font-style"]&&(id+="_"+style["font-style"]),this.STYLEDTEXT||(this.STYLEDTEXT={}),this.STYLEDTEXT[id]||(this.STYLEDTEXT[id]={className:variant.className||""});var unknown=this.STYLEDTEXT[id];if(!unknown["_"+text]){var HDW=this.getHDW(text,variant.className||"",style);unknown["_"+text]=[.8,.2,HDW.w,0,HDW.w,{a:Math.max(0,(HDW.h-HDW.d)/2),h:HDW.h,d:HDW.d}],unknown["_"+text].c=text}return{type:"unknown",n:"_"+text,font:unknown,style:style,rscale:variant.rscale}},getHDW:function(c,name,styles){var test1=CHTML.addElement(CHTML.CHTMLnode,"mjx-chartest",{className:name},[["mjx-char",{style:styles},[c]]]),test2=CHTML.addElement(CHTML.CHTMLnode,"mjx-chartest",{className:name},[["mjx-char",{style:styles},[c,["mjx-box"]]]]);test1.firstChild.style.fontSize=test2.firstChild.style.fontSize="";var em=5*CHTML.em,H1=test1.offsetHeight,H2=test2.offsetHeight,W=test1.offsetWidth;if(CHTML.CHTMLnode.removeChild(test1),CHTML.CHTMLnode.removeChild(test2),0===H2){em=5*CHTML.defaultEm;var test=document.body.appendChild(document.createElement("div"));test.appendChild(test1),test.appendChild(test2),H1=test1.offsetHeight,H2=test2.offsetHeight,W=test1.offsetWidth,document.body.removeChild(test)}var d=(H2-1e3)/em,w,h;return{h:H1/em-d,d:d,w:W/em}},addCharList:function(node,list,bbox){for(var state={text:"",className:null,a:0},i=0,m=list.length;i<m;i++){var item=list[i];this.charList[item.type]&&this.charList[item.type](item,node,bbox,state,m)}""!==state.text&&(node.childNodes.length?this.charList.flushText(node,state):(HTML.addText(node,state.text),node.className?node.className+=" "+state.className:node.className=state.className)),bbox.b=state.flushed?0:bbox.a},charList:{char:function(item,node,bbox,state,m){var font=item.font,remap=(font.remapCombining||{})[item.n];font.className===state.className?remap=null:(state.className||remap&&""!==state.text)&&this.flushText(node,state),state.a||(state.a=font.centerline/1e3),state.a>(bbox.a||0)&&(bbox.a=state.a),state.className=font.className;var C=font[item.n];if(remap){var FONT=font;isArray(remap)&&(FONT=CHTML.FONTDATA.FONTS[remap[1]],remap=remap[0],"string"==typeof FONT&&CHTML.loadFont(FONT)),FONT[item.n]&&CHTML.fixChar(FONT[item.n],item.n),C=CHTML.fixChar(FONT[remap],remap),state.className=FONT.className}if(state.text+=C.c,bbox.h<C[0]+.025&&(bbox.t=bbox.h=C[0]+.025),bbox.d<C[1]+.025&&(bbox.b=bbox.d=C[1]+.025),bbox.l>bbox.w+C[3]&&(bbox.l=bbox.w+C[3]),bbox.r<bbox.w+C[4]&&(bbox.r=bbox.w+C[4]),bbox.w+=C[2]*(item.rscale||1),1==m&&font.skew&&font.skew[item.n]&&(bbox.skew=font.skew[item.n]),C[5]&&C[5].rfix&&(this.flushText(node,state).style.marginRight=CHTML.Em(C[5].rfix/1e3)),remap){var chr=this.flushText(node,state),r=(FONT[item.n]||font[item.n])[4]-(C[4]-C[2]);chr.style.marginLeft=CHTML.Em(-C[2]-r),r<0&&(chr.style.marginRight=CHTML.Em(-r))}},space:function(item,node,bbox,state){item.w&&(""===state.text&&(state.className=item.font.className),this.flushText(node,state).style.marginRight=CHTML.Em(item.w),bbox.w+=item.w)},unknown:function(item,node,bbox,state){this.char(item,node,bbox,state,0);var C=item.font[item.n];C[5].a&&(state.a=C[5].a,(null==bbox.a||state.a>bbox.a)&&(bbox.a=state.a)),node=this.flushText(node,state,item.style),C[2]<3&&(node.style.width=CHTML.Em(C[2]))},flushText:function(node,state,style){return node=CHTML.addElement(node,"mjx-charbox",{className:state.className,style:style},[state.text]),state.a&&(node.style.paddingBottom=CHTML.Em(state.a)),state.text="",state.className=null,state.a=0,state.flushed=!0,node}},handleText:function(node,text,variant,bbox){0===node.childNodes.length&&(CHTML.addElement(node,"mjx-char"),bbox=CHTML.BBOX.empty(bbox)),"string"==typeof variant&&(variant=this.FONTDATA.VARIANT[variant]),variant||(variant=this.FONTDATA.VARIANT[MML.VARIANT.NORMAL]);var string={text:text,i:0,length:text.length},list=[];if(variant.style&&string.length)list.push(this.styledText(variant,text));else for(;string.i<string.length;){var n=this.getUnicode(string);list.push.apply(list,this.getCharList(variant,n))}return list.length&&this.addCharList(node.firstChild,list,bbox),bbox.clean(),bbox.d<0&&(bbox.D=bbox.d,bbox.d=0),bbox.h-bbox.a&&(node.firstChild.style[bbox.h-bbox.a<0?"marginTop":"paddingTop"]=this.EmRounded(bbox.h-bbox.a)),bbox.d>-bbox.b&&(node.firstChild.style.paddingBottom=this.EmRounded(bbox.d+bbox.b)),bbox},createDelimiter:function(node,code,HW,BBOX,font){if(!code){var bbox=this.BBOX.zero();return bbox.w=bbox.r=this.TEX.nulldelimiterspace,CHTML.addElement(node,"mjx-box",{style:{width:bbox.w}}),bbox}HW instanceof Array||(HW=[HW,HW]);var hw=HW[1];HW=HW[0];for(var delim={alias:code};delim.alias;)code=delim.alias,(delim=this.FONTDATA.DELIMITERS[code])||(delim={HW:[0,this.FONTDATA.VARIANT[MML.VARIANT.NORMAL]]});delim.load&&HUB.RestartAfter(AJAX.Require(this.fontDir+"/TeX/fontdata-"+delim.load+".js"));for(var i=0,m=delim.HW.length;i<m;i++)if(delim.HW[i][0]>=HW-.01||i==m-1&&!delim.stretch)return delim.HW[i][3]&&(code=delim.HW[i][3]),(bbox=this.createChar(node,[code,delim.HW[i][1]],delim.HW[i][2]||1,font)).offset=.6*bbox.w,BBOX&&(bbox.scale=BBOX.scale,BBOX.rscale=BBOX.rscale),bbox;return delim.stretch?this["extendDelimiter"+delim.dir](node,hw,delim.stretch,BBOX,font):bbox},extendDelimiterV:function(node,H,delim,BBOX,font){node=CHTML.addElement(node,"mjx-delim-v");var tmp=CHTML.Element("span"),top,bot,mid,ext,tbox,bbox,mbox,ebox,k=1,c;tbox=this.createChar(tmp,delim.top||delim.ext,1,font),top=tmp.removeChild(tmp.firstChild),bbox=this.createChar(tmp,delim.bot||delim.ext,1,font),bot=tmp.removeChild(tmp.firstChild),mbox=ebox=CHTML.BBOX.zero();var h=tbox.h+tbox.d+bbox.h+bbox.d-.1;if(node.appendChild(top),delim.mid&&(mbox=this.createChar(tmp,delim.mid,1,font),mid=tmp.removeChild(tmp.firstChild),h+=mbox.h+mbox.d,k=2),delim.min&&H<h*delim.min&&(H=h*delim.min),H>h){ebox=this.createChar(tmp,delim.ext,1,font),ext=tmp.removeChild(tmp.firstChild);var eH=ebox.h+ebox.d,eh=eH-.1,n=Math.min(Math.ceil((H-h)/(k*eh)),this.maxStretchyParts);delim.fullExtenders?H=n*k*eh+h:eh=(H-h)/(k*n),c=ebox.d+ebox.a-eH/2,ext.style.margin=ext.style.padding="",ext.style.lineHeight=CHTML.Em(eh),ext.style.marginBottom=CHTML.Em(c-.05/k),ext.style.marginTop=CHTML.Em(-c-.05/k);for(var TEXT=ext.textContent,text="\n"+TEXT;--n>0;)TEXT+=text;ext.textContent=TEXT,node.appendChild(ext),delim.mid&&(node.appendChild(mid),node.appendChild(ext.cloneNode(!0)))}else c=(H-h-.1)/k,top.style.marginBottom=CHTML.Em(c+parseFloat(top.style.marginBottom||"0")),delim.mid&&node.appendChild(mid),bot.style.marginTop=CHTML.Em(c+parseFloat(bot.style.marginTop||"0"));node.appendChild(bot);var vbox=CHTML.BBOX({w:Math.max(tbox.w,ebox.w,bbox.w,mbox.w),l:Math.min(tbox.l,ebox.l,bbox.l,mbox.l),r:Math.max(tbox.r,ebox.r,bbox.r,mbox.r),h:H-bbox.d,d:bbox.d,t:H-bbox.d,b:bbox.d});return vbox.offset=.5*vbox.w,BBOX&&(vbox.scale=BBOX.scale,vbox.rscale=BBOX.rscale),vbox},extendDelimiterH:function(node,W,delim,BBOX,font){node=CHTML.addElement(node,"mjx-delim-h");var tmp=CHTML.Element("span"),left,right,mid,ext,ext2,lbox,rbox,mbox,ebox,k=1;lbox=this.createChar(tmp,delim.left||delim.rep,1,font),left=tmp.removeChild(tmp.firstChild),rbox=this.createChar(tmp,delim.right||delim.rep,1,font),right=tmp.removeChild(tmp.firstChild),ebox=this.createChar(tmp,delim.rep,1,font),ext=tmp.removeChild(tmp.firstChild),left.style.marginLeft=CHTML.Em(-lbox.l),right.style.marginRight=CHTML.Em(rbox.r-rbox.w),node.appendChild(left);var hbox=CHTML.BBOX.zero();hbox.h=Math.max(lbox.h,rbox.h,ebox.h),hbox.d=Math.max(lbox.D||lbox.d,rbox.D||rbox.d,ebox.D||ebox.d);var w=lbox.r-lbox.l+(rbox.r-rbox.l)-.1;if(delim.mid&&(mbox=this.createChar(tmp,delim.mid,1,font),(mid=tmp.removeChild(tmp.firstChild)).style.marginleft=CHTML.Em(-mbox.l),mid.style.marginRight=CHTML.Em(mbox.r-mbox.w),w+=mbox.r-mbox.l+.1,k=2,mbox.h>hbox.h&&(hbox.h=mbox.h),mbox.d>hbox.d&&(hbox.d=mbox.d)),delim.min&&W<w*delim.min&&(W=w*delim.min),hbox.w=hbox.r=W,W>w){var eW=ebox.r-ebox.l,ew=eW-.1,n=Math.min(Math.ceil((W-w)/(k*ew)),this.maxStretchyParts);delim.fullExtenders?W=n*k*ew+w:ew=(W-w)/(k*n);var c=(eW-ew+.1/k)/2;ext.style.marginLeft=CHTML.Em(-ebox.l-c),ext.style.marginRight=CHTML.Em(ebox.r-ebox.w+c),ext.style.letterSpacing=CHTML.Em(-(ebox.w-ew)),left.style.marginRight=CHTML.Em(lbox.r-lbox.w),right.style.marginleft=CHTML.Em(-rbox.l);for(var TEXT=ext.textContent,text=TEXT;--n>0;)TEXT+=text;ext.textContent=TEXT,node.appendChild(ext),delim.mid&&(node.appendChild(mid),ext2=node.appendChild(ext.cloneNode(!0)))}else c=(W-w-.1/k)/2,left.style.marginRight=CHTML.Em(lbox.r-lbox.w+c),delim.mid&&node.appendChild(mid),right.style.marginLeft=CHTML.Em(-rbox.l+c);return node.appendChild(right),this.adjustHeights([left,ext,mid,ext2,right],[lbox,ebox,mbox,ebox,rbox],hbox),BBOX&&(hbox.scale=BBOX.scale,hbox.rscale=BBOX.rscale),hbox},adjustHeights:function(nodes,box,bbox){var T=bbox.h,B=bbox.d;bbox.d<0&&(B=-bbox.d,bbox.D=bbox.d,bbox.d=0);for(var i=0,m=nodes.length;i<m;i++)nodes[i]&&(nodes[i].style.paddingTop=CHTML.Em(T-box[i].a),nodes[i].style.paddingBottom=CHTML.Em(B+box[i].a),nodes[i].style.marginTop=nodes[i].style.marginBottom=0)},createChar:function(node,data,scale,font){var text="",variant={fonts:[data[1]],noRemap:!0,cache:{}};if(font&&font===MML.VARIANT.BOLD&&this.FONTDATA.FONTS[data[1]+"-Bold"]&&(variant.fonts=[data[1]+"-Bold",data[1]]),"string"!=typeof data[1]&&(variant=data[1]),data[0]instanceof Array)for(var i=0,m=data[0].length;i<m;i++)text+=String.fromCharCode(data[0][i]);else text=String.fromCharCode(data[0]);data[4]&&(scale*=data[4]);var bbox=this.handleText(node,text,variant),style=node.firstChild.style;return 1!==scale&&(style.fontSize=this.Percent(scale)),data[2]&&(style.paddingLeft=this.Em(data[2]),bbox.w+=data[2],bbox.r+=data[2]),data[3]&&(style.verticalAlign=this.Em(data[3]),bbox.h+=data[3],bbox.h<0&&(bbox.h=0)),data[5]&&(style.marginTop=this.Em(data[5]),bbox.h+=data[5],bbox.t+=data[5]),data[6]&&(style.marginBottom=this.Em(data[6]),bbox.d+=data[6],bbox.b+=data[6]),bbox},length2em:function(length,size,scale){if("string"!=typeof length&&(length=length.toString()),""===length)return"";if(length===MML.SIZE.NORMAL)return 1;if(length===MML.SIZE.BIG)return 2;if(length===MML.SIZE.SMALL)return.71;if(this.MATHSPACE[length])return this.MATHSPACE[length];var match=length.match(/^\s*([-+]?(?:\.\d+|\d+(?:\.\d*)?))?(pt|em|ex|mu|px|pc|in|mm|cm|%)?/),m=parseFloat(match[1]||"1"),unit=match[2];return null==size&&(size=1),scale||(scale=1),scale=1/this.em/scale,"em"===unit?m:"ex"===unit?m*this.TEX.x_height:"%"===unit?m/100*size:"px"===unit?m*scale:"pt"===unit?m/10:"pc"===unit?1.2*m:(scale*=this.pxPerInch,"in"===unit?m*scale:"cm"===unit?m*scale/2.54:"mm"===unit?m*scale/25.4:"mu"===unit?m/18:m*size)},thickness2em:function(length,scale){var thick=CHTML.TEX.rule_thickness/(scale||1);return length===MML.LINETHICKNESS.MEDIUM?thick:length===MML.LINETHICKNESS.THIN?.67*thick:length===MML.LINETHICKNESS.THICK?1.67*thick:this.length2em(length,thick,scale)},Em:function(m){return Math.abs(m)<.001?"0":m.toFixed(3).replace(/\.?0+$/,"")+"em"},EmRounded:function(m){return m=(Math.round(m*CHTML.em)+.05)/CHTML.em,Math.abs(m)<6e-4?"0em":m.toFixed(3).replace(/\.?0+$/,"")+"em"},unEm:function(m){return parseFloat(m)},Px:function(m,M){return m*=this.em,M&&m<M&&(m=M),Math.abs(m)<.1?"0":m.toFixed(1).replace(/\.0$/,"")+"px"},Percent:function(m){return(100*m).toFixed(1).replace(/\.?0+$/,"")+"%"},Transform:function(node,trans,origin){var style=node.style;style.transform=style.WebkitTransform=style.MozTransform=style["-ms-transform"]=trans,origin&&(style.transformOrigin=style.WebkitTransformOrigin=style.MozTransformOrigin=style["-ms-transform-origin"]=origin)},arrayEntry:function(a,i){return a[Math.max(0,Math.min(i,a.length-1))]},removeStyles:["fontSize","fontFamily","fontWeight","fontStyle","fontVariant","font"]}),CHTML.BBOX=MathJax.Object.Subclass({Init:function(def){for(var id in def)def.hasOwnProperty(id)&&(this[id]=def[id])},clean:function(){-1e6===this.h&&(this.h=0),-1e6===this.d&&(this.d=0),1e6===this.l&&(this.l=0),-1e6===this.r&&(this.r=0),-1e6===this.t&&(this.t=0),-1e6===this.b&&(this.b=0),this.D&&this.d>0&&delete this.D},rescale:function(scale){this.w*=scale,this.h*=scale,this.d*=scale,this.l*=scale,this.r*=scale,this.t*=scale,this.b*=scale,this.L&&(this.L*=scale),this.R&&(this.R*=scale),this.D&&(this.D*=scale)},combine:function(cbox,x,y){cbox.X=x,cbox.Y=y;var scale=cbox.rscale;x+scale*cbox.r>this.r&&(this.r=x+scale*cbox.r),x+scale*cbox.l<this.l&&(this.l=x+scale*cbox.l),x+scale*(cbox.w+(cbox.L||0)+(cbox.R||0))>this.w&&(this.w=x+scale*(cbox.w+(cbox.L||0)+(cbox.R||0))),y+scale*cbox.h>this.h&&(this.h=y+scale*cbox.h),cbox.D&&(null==this.D||scale*cbox.D-y>this.D)&&scale*cbox.D>this.d?this.D=scale*cbox.D-y:null==cbox.D&&this.D&&delete this.D,scale*cbox.d-y>this.d&&(this.d=scale*cbox.d-y),y+scale*cbox.t>this.t&&(this.t=y+scale*cbox.t),scale*cbox.b-y>this.b&&(this.b=scale*cbox.b-y)},append:function(cbox){var scale=cbox.rscale,x=this.w;x+scale*cbox.r>this.r&&(this.r=x+scale*cbox.r),x+scale*cbox.l<this.l&&(this.l=x+scale*cbox.l),this.w+=scale*(cbox.w+(cbox.L||0)+(cbox.R||0)),scale*cbox.h>this.h&&(this.h=scale*cbox.h),cbox.D&&(null==this.D||scale*cbox.D>this.D)&&scale*cbox.D>this.d?this.D=scale*cbox.D:null==cbox.D&&this.D&&delete this.D,scale*cbox.d>this.d&&(this.d=scale*cbox.d),scale*cbox.t>this.t&&(this.t=scale*cbox.t),scale*cbox.b>this.b&&(this.b=scale*cbox.b)},updateFrom:function(cbox){this.h=cbox.h,this.d=cbox.d,this.w=cbox.w,this.r=cbox.r,this.l=cbox.l,this.t=cbox.t,this.b=cbox.b,cbox.pwidth&&(this.pwidth=cbox.pwidth),cbox.D?this.D=cbox.D:delete this.D},adjust:function(m,x,X,M){this[x]+=CHTML.length2em(m,1,this.scale),null==M?this[x]>this[X]&&(this[X]=this[x]):this[X]<M&&(this[X]=M)}},{zero:function(){return CHTML.BBOX({h:0,d:0,w:0,l:0,r:0,t:0,b:0,scale:1,rscale:1})},empty:function(bbox){return bbox||(bbox=CHTML.BBOX.zero()),bbox.h=bbox.d=bbox.r=bbox.t=bbox.b=-1e6,bbox.w=0,bbox.l=1e6,delete bbox.pwidth,bbox},styleAdjust:[["borderTopWidth","h","t"],["borderRightWidth","w","r"],["borderBottomWidth","d","b"],["borderLeftWidth","w","l",0],["paddingTop","h","t"],["paddingRight","w","r"],["paddingBottom","d","b"],["paddingLeft","w","l",0]]}),MathJax.Hub.Register.StartupHook("mml Jax Ready",(function(){(MML=MathJax.ElementJax.mml).mbase.Augment({toCommonHTML:function(node,options){return this.CHTMLdefaultNode(node,options)},CHTMLmultiline:function(){MML.mbase.CHTMLautoloadFile("multiline")},CHTMLdefaultNode:function(node,options){options||(options={}),node=this.CHTMLcreateNode(node),this.CHTML=CHTML.BBOX.empty(),this.CHTMLhandleStyle(node),this.isToken&&this.CHTMLgetVariant(),this.CHTMLhandleScale(node);for(var m=Math.max(options.minChildren||0,this.data.length),i=0;i<m;i++)this.CHTMLaddChild(node,i,options);return options.noBBox||this.CHTML.clean(),this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node},CHTMLaddChild:function(node,i,options){var child=this.data[i],cnode,type=options.childNodes;if(type instanceof Array&&(type=type[i]||"span"),child){if(type&&(node=CHTML.addElement(node,type)),cnode=child.toCommonHTML(node,options.childOptions),type&&1!==child.CHTML.rscale&&(node.style.fontSize=node.firstChild.style.fontSize,node.firstChild.style.fontSize=""),!options.noBBox){var bbox=this.CHTML,cbox=child.CHTML;bbox.append(cbox),1===this.data.length?(cbox.ic&&(bbox.ic=cbox.ic),cbox.skew&&(bbox.skew=cbox.skew)):(delete bbox.ic,delete bbox.skew),cbox.pwidth&&(bbox.pwidth=cbox.pwidth)}}else options.forceChild&&(cnode=CHTML.addElement(node,type||"mjx-box"));return cnode},CHTMLchildNode:function(node,i){return"a"===(node=node.childNodes[i]).nodeName.toLowerCase()&&(node=node.firstChild),node},CHTMLcoreNode:function(node){return this.inferRow&&this.data[0]?this.data[0].CHTMLcoreNode(node.firstChild):this.CHTMLchildNode(node,this.CoreIndex())},CHTMLstretchChildV:function(i,H,D){var data=this.data[i];if(data){var bbox=this.CHTML,dbox=data.CHTML;if(dbox.stretch||null==dbox.stretch&&data.CHTMLcanStretch("Vertical",H,D)){var w=dbox.w;dbox=data.CHTMLstretchV(H,D),bbox.w+=dbox.w-w,bbox.w>bbox.r&&(bbox.r=bbox.w),dbox.h>bbox.h&&(bbox.h=dbox.h),dbox.d>bbox.d&&(bbox.d=dbox.d),dbox.t>bbox.t&&(bbox.t=dbox.t),dbox.b>bbox.b&&(bbox.b=dbox.b)}}},CHTMLstretchChildH:function(i,W,node){var data=this.data[i];if(data){var bbox=this.CHTML,dbox=data.CHTML;if(dbox.stretch||null==dbox.stretch&&data.CHTMLcanStretch("Horizontal",W)){var w=dbox.w;dbox=data.CHTMLstretchH(this.CHTMLchildNode(node,i),W),bbox.w+=dbox.w-w,bbox.w>bbox.r&&(bbox.r=bbox.w),dbox.h>bbox.h&&(bbox.h=dbox.h),dbox.d>bbox.d&&(bbox.d=dbox.d),dbox.t>bbox.t&&(bbox.t=dbox.t),dbox.b>bbox.b&&(bbox.b=dbox.b)}}},CHTMLupdateFrom:function(bbox){this.CHTML.updateFrom(bbox),this.inferRow&&this.data[0].CHTML.updateFrom(bbox)},CHTMLcanStretch:function(direction,H,D){var stretch=!1;if(this.isEmbellished()){var core=this.Core();core&&core!==this&&(stretch=core.CHTMLcanStretch(direction,H,D))}return this.CHTML.stretch=stretch,stretch},CHTMLstretchV:function(h,d){return this.CHTMLupdateFrom(this.Core().CHTMLstretchV(h,d)),this.CHTML},CHTMLstretchH:function(node,w){return this.CHTMLupdateFrom(this.CHTMLstretchCoreH(node,w)),this.CHTML},CHTMLstretchCoreH:function(node,w){return this.Core().CHTMLstretchH(this.CHTMLcoreNode(node),w)},CHTMLcreateNode:function(node){this.CHTML||(this.CHTML={}),this.CHTML=CHTML.BBOX.zero(),this.href&&(node=CHTML.addElement(node,"a",{href:this.href,isMathJax:!0})),this.CHTMLnodeID||(this.CHTMLnodeID=CHTML.GetID());var id=(this.id||"MJXc-Node-"+this.CHTMLnodeID)+CHTML.idPostfix;return this.CHTMLhandleAttributes(CHTML.addElement(node,"mjx-"+this.type,{id:id}))},CHTMLnodeElement:function(){return this.CHTMLnodeID?document.getElementById((this.id||"MJXc-Node-"+this.CHTMLnodeID)+CHTML.idPostfix):null},CHTMLlength2em:function(length,size){return CHTML.length2em(length,size,this.CHTML.scale)},CHTMLhandleAttributes:function(node){if(this.class&&(node.className?node.className+=" "+this.class:node.className=this.class),this.attrNames)for(var copy=this.attrNames,skip=MML.nocopyAttributes,ignore=HUB.config.ignoreMMLattributes,defaults="mstyle"===this.type?MML.math.prototype.defaults:this.defaults,i=0,m=copy.length;i<m;i++){var id=copy[i];0!=ignore[id]&&(skip[id]||ignore[id]||null!=defaults[id]||void 0!==node[id])||node.setAttribute(id,this.attr[id])}return node},CHTMLhandleScale:function(node){var scale=1,parent=this.parent,pscale=parent?parent.CHTML.scale:1,values=this.getValues("scriptlevel","fontsize");values.mathsize=this.Get("mathsize",null,!this.isToken),0!==values.scriptlevel&&(values.scriptlevel>2&&(values.scriptlevel=2),scale=Math.pow(this.Get("scriptsizemultiplier"),values.scriptlevel),values.scriptminsize=CHTML.length2em(this.Get("scriptminsize"),.8,1),scale<values.scriptminsize&&(scale=values.scriptminsize)),this.removedStyles&&this.removedStyles.fontSize&&!values.fontsize&&(values.fontsize=this.removedStyles.fontSize),values.fontsize&&!this.mathsize&&(values.mathsize=values.fontsize),1!==values.mathsize&&(scale*=CHTML.length2em(values.mathsize,1,1));var variant=this.CHTMLvariant;return variant&&variant.style&&variant.style["font-family"]&&(scale*=CHTML.config.scale/100/CHTML.scale),this.CHTML.scale=scale,pscale=this.CHTML.rscale=scale/pscale,Math.abs(pscale-1)<.001&&(pscale=1),node&&1!==pscale&&(node.style.fontSize=CHTML.Percent(pscale)),scale},CHTMLhandleStyle:function(node){if(this.style){var style=node.style;style.cssText=this.style,this.removedStyles={};for(var i=0,m=CHTML.removeStyles.length;i<m;i++){var id=CHTML.removeStyles[i];style[id]&&(this.removedStyles[id]=style[id],style[id]="")}}},CHTMLhandleBBox:function(node){var BBOX=this.CHTML,style=node.style;if(1===this.data.length&&(this.data[0].CHTML||{}).pwidth?(BBOX.pwidth=this.data[0].CHTML.pwidth,BBOX.mwidth=this.data[0].CHTML.mwidth,style.width="100%"):BBOX.pwidth?(BBOX.mwidth=CHTML.Em(BBOX.w),style.width="100%"):BBOX.w<0&&(style.width="0px",style.marginRight=CHTML.Em(BBOX.w)),this.style)for(var i=0,m=CHTML.BBOX.styleAdjust.length;i<m;i++){var data=CHTML.BBOX.styleAdjust[i];data&&style[data[0]]&&BBOX.adjust(style[data[0]],data[1],data[2],data[3])}},CHTMLhandleColor:function(node){this.mathcolor?node.style.color=this.mathcolor:this.color&&(node.style.color=this.color),this.mathbackground?node.style.backgroundColor=this.mathbackground:this.background&&(node.style.backgroundColor=this.background)},CHTMLhandleSpace:function(node){if(!this.useMMLspacing){var space=this.texSpacing();""!==space&&(this.CHTML.L=this.CHTMLlength2em(space),node.className+=" "+CHTML.SPACECLASS[space])}},CHTMLhandleText:function(node,text,variant){node.firstChild&&!this.CHTML&&(this.CHTML=CHTML.BBOX.empty()),this.CHTML=CHTML.handleText(node,text,variant,this.CHTML)},CHTMLgetVariant:function(){var values=this.getValues("mathvariant","fontfamily","fontweight","fontstyle"),style;values.hasVariant=this.Get("mathvariant",!0),this.removedStyles&&((style=this.removedStyles).fontFamily&&(values.family=style.fontFamily),style.fontWeight&&(values.weight=style.fontWeight),style.fontStyle&&(values.style=style.fontStyle)),values.hasVariant||(values.fontfamily&&(values.family=values.fontfamily),values.fontweight&&(values.weight=values.fontweight),values.fontstyle&&(values.style=values.fontstyle)),values.weight&&values.weight.match(/^\d+$/)&&(values.weight=parseInt(values.weight)>600?"bold":"normal");var variant=values.mathvariant;if(this.variantForm&&(variant="-TeX-variant"),values.family&&!values.hasVariant)return!values.weight&&values.mathvariant.match(/bold/)&&(values.weight="bold"),!values.style&&values.mathvariant.match(/italic/)&&(values.style="italic"),void(this.CHTMLvariant={fonts:[],noRemap:!0,cache:{},style:{"font-family":values.family,"font-weight":values.weight||"normal","font-style":values.style||"normal"}});"bold"===values.weight?variant={normal:MML.VARIANT.BOLD,italic:MML.VARIANT.BOLDITALIC,fraktur:MML.VARIANT.BOLDFRAKTUR,script:MML.VARIANT.BOLDSCRIPT,"sans-serif":MML.VARIANT.BOLDSANSSERIF,"sans-serif-italic":MML.VARIANT.SANSSERIFBOLDITALIC}[variant]||variant:"normal"===values.weight&&(variant={bold:MML.VARIANT.normal,"bold-italic":MML.VARIANT.ITALIC,"bold-fraktur":MML.VARIANT.FRAKTUR,"bold-script":MML.VARIANT.SCRIPT,"bold-sans-serif":MML.VARIANT.SANSSERIF,"sans-serif-bold-italic":MML.VARIANT.SANSSERIFITALIC}[variant]||variant),"italic"===values.style?variant={normal:MML.VARIANT.ITALIC,bold:MML.VARIANT.BOLDITALIC,"sans-serif":MML.VARIANT.SANSSERIFITALIC,"bold-sans-serif":MML.VARIANT.SANSSERIFBOLDITALIC}[variant]||variant:"normal"===values.style&&(variant={italic:MML.VARIANT.NORMAL,"bold-italic":MML.VARIANT.BOLD,"sans-serif-italic":MML.VARIANT.SANSSERIF,"sans-serif-bold-italic":MML.VARIANT.BOLDSANSSERIF}[variant]||variant),this.CHTMLvariant=CHTML.FONTDATA.VARIANT[variant]||CHTML.FONTDATA.VARIANT[MML.VARIANT.NORMAL]},CHTMLbboxFor:function(n){return this.data[n]&&this.data[n].CHTML?this.data[n].CHTML:CHTML.BBOX.zero()},CHTMLdrawBBox:function(node,bbox){bbox||(bbox=this.CHTML);var box=CHTML.Element("mjx-box",{style:{opacity:.25,"margin-left":CHTML.Em(-(bbox.w+(bbox.R||0)))}},[["mjx-box",{style:{height:CHTML.Em(bbox.h),width:CHTML.Em(bbox.w),"background-color":"red"}}],["mjx-box",{style:{height:CHTML.Em(bbox.d),width:CHTML.Em(bbox.w),"margin-left":CHTML.Em(-bbox.w),"vertical-align":CHTML.Em(-bbox.d),"background-color":"green"}}]]);node.nextSibling?node.parentNode.insertBefore(box,node.nextSibling):node.parentNode.appendChild(box)},CHTMLnotEmpty:function(mml){for(;mml&&mml.data.length<2&&("mrow"===mml.type||"texatom"===mml.type);)mml=mml.data[0];return!!mml}},{CHTMLautoload:function(){this.constructor.Augment({toCommonHTML:MML.mbase.CHTMLautoloadFail});var file=CHTML.autoloadDir+"/"+this.type+".js";HUB.RestartAfter(AJAX.Require(file))},CHTMLautoloadFail:function(){throw Error("CommonHTML can't autoload '"+this.type+"'")},CHTMLautoloadList:{},CHTMLautoloadFile:function(name){if(MML.mbase.CHTMLautoloadList.hasOwnProperty(name))throw Error("CommonHTML can't autoload file '"+name+"'");MML.mbase.CHTMLautoloadList[name]=!0;var file=CHTML.autoloadDir+"/"+name+".js";HUB.RestartAfter(AJAX.Require(file))},CHTMLstretchV:function(h,d){return this.Core().CHTMLstretchV(h,d),this.toCommonHTML(this.CHTMLnodeElement(),{stretch:!0}),this.CHTML},CHTMLstretchH:function(node,w){return this.CHTMLupdateFrom(this.CHTMLstretchCoreH(node,w)),this.toCommonHTML(node,{stretch:!0}),this.CHTML}}),MML.chars.Augment({toCommonHTML:function(node,options){this.CHTML=null,null==options&&(options={});var text=this.toString();options.remap&&(text=options.remap(text,options.remapchars)),this.CHTMLhandleText(node,text,options.variant||this.parent.CHTMLvariant)}}),MML.entity.Augment({toCommonHTML:function(node,options){null==options&&(options={});var text=this.toString();options.remapchars&&(text=options.remap(text,options.remapchars)),this.CHTMLhandleText(node,text,options.variant||this.parent.CHTMLvariant)}}),MML.math.Augment({toCommonHTML:function(node){node=this.CHTMLdefaultNode(node),this.CHTML.w<0&&(node.parentNode.style.width="0px",node.parentNode.style.marginRight=CHTML.Em(this.CHTML.w));var alttext=this.Get("alttext");if(alttext&&!node.getAttribute("aria-label")&&node.setAttribute("aria-label",alttext),this.CHTML.pwidth)node.parentNode.style.minWidth=this.CHTML.mwidth||CHTML.Em(this.CHTML.w),node.parentNode.className="mjx-full-width "+node.parentNode.className,node.style.width=this.CHTML.pwidth;else if(!this.isMultiline&&"block"===this.Get("display")){var values=this.getValues("indentalignfirst","indentshiftfirst","indentalign","indentshift");values.indentalignfirst!==MML.INDENTALIGN.INDENTALIGN&&(values.indentalign=values.indentalignfirst),values.indentalign===MML.INDENTALIGN.AUTO&&(values.indentalign=CONFIG.displayAlign),values.indentshiftfirst!==MML.INDENTSHIFT.INDENTSHIFT&&(values.indentshift=values.indentshiftfirst),"auto"===values.indentshift&&(values.indentshift="0");var shift=this.CHTMLlength2em(values.indentshift,CHTML.cwidth);if("0"!==CONFIG.displayIndent){var indent=this.CHTMLlength2em(CONFIG.displayIndent,CHTML.cwidth);shift+=values.indentalign===MML.INDENTALIGN.RIGHT?-indent:indent}var styles=node.parentNode.parentNode.style;node.parentNode.style.textAlign=styles.textAlign=values.indentalign,shift&&(shift*=CHTML.em/CHTML.outerEm,HUB.Insert(styles,{left:{marginLeft:CHTML.Em(shift)},right:{marginRight:CHTML.Em(-shift)},center:{marginLeft:CHTML.Em(shift),marginRight:CHTML.Em(-shift)}}[values.indentalign]))}return node}}),MML.mi.Augment({toCommonHTML:function(node){node=this.CHTMLdefaultNode(node);var bbox=this.CHTML,text=this.data.join("");return null==bbox.skew||CHTML.isChar(text)||delete bbox.skew,bbox.r>bbox.w&&CHTML.isChar(text)&&!this.CHTMLvariant.noIC&&(bbox.ic=bbox.r-bbox.w,bbox.w=bbox.r,node.lastChild.style.paddingRight=CHTML.Em(bbox.ic)),node}}),MML.mn.Augment({CHTMLremapMinus:function(text){return text.replace(/^-/,"−")},toCommonHTML:function(node){node=this.CHTMLdefaultNode(node,{childOptions:{remap:this.CHTMLremapMinus}});var bbox=this.CHTML,text=this.data.join("");return null==bbox.skew||CHTML.isChar(text)||delete bbox.skew,bbox.r>bbox.w&&CHTML.isChar(text)&&!this.CHTMLvariant.noIC&&(bbox.ic=bbox.r-bbox.w,bbox.w=bbox.r,node.lastChild.style.paddingRight=CHTML.Em(bbox.ic)),node}}),MML.mo.Augment({toCommonHTML:function(node){node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLgetVariant(),this.CHTMLhandleScale(node),CHTML.BBOX.empty(this.CHTML);var values=this.getValues("displaystyle","largeop");if(values.variant=this.CHTMLvariant,values.text=this.data.join(""),""==values.text)this.fence&&(node.style.width=CHTML.Em(CHTML.TEX.nulldelimiterspace));else{this.CHTMLadjustAccent(values),this.CHTMLadjustVariant(values);for(var i=0,m=this.data.length;i<m;i++)this.CHTMLaddChild(node,i,{childOptions:{variant:values.mathvariant,remap:this.remap,remapchars:values.remapchars}});CHTML.isChar(values.text)?0===this.CHTML.w&&this.CHTML.l<0&&this.CHTMLfixCombiningChar(node):delete this.CHTML.skew,values.largeop&&this.CHTMLcenterOp(node)}return this.CHTML.clean(),this.CHTMLhandleBBox(node),this.CHTMLhandleSpace(node),this.CHTMLhandleColor(node),node},CHTMLhandleSpace:function(node){if(this.hasMMLspacing()){var values=this.getValues("scriptlevel","lspace","rspace");values.lspace=Math.max(0,this.CHTMLlength2em(values.lspace)),values.rspace=Math.max(0,this.CHTMLlength2em(values.rspace)),values.scriptlevel>0&&(this.hasValue("lspace")||(values.lspace=.15),this.hasValue("rspace")||(values.rspace=.15));for(var core=this,parent=this.Parent();parent&&parent.isEmbellished()&&parent.Core()===core;)core=parent,parent=parent.Parent(),node=core.CHTMLnodeElement();values.lspace&&(node.style.paddingLeft=CHTML.Em(values.lspace)),values.rspace&&(node.style.paddingRight=CHTML.Em(values.rspace)),this.CHTML.L=values.lspace,this.CHTML.R=values.rspace}else this.SUPER(arguments).CHTMLhandleSpace.apply(this,arguments)},CHTMLadjustAccent:function(data){var parent=this.CoreParent();if(data.parent=parent,CHTML.isChar(data.text)&&parent&&parent.isa(MML.munderover)){var over=parent.data[parent.over],under=parent.data[parent.under];over&&this===over.CoreMO()&&parent.Get("accent")?data.remapchars=CHTML.FONTDATA.REMAPACCENT:under&&this===under.CoreMO()&&parent.Get("accentunder")&&(data.remapchars=CHTML.FONTDATA.REMAPACCENTUNDER)}},CHTMLadjustVariant:function(data){var parent=data.parent,isScript=parent&&parent.isa(MML.msubsup)&&this!==parent.data[parent.base];data.largeop&&(data.mathvariant=data.displaystyle?"-largeOp":"-smallOp"),isScript&&(data.remapchars=this.remapChars,data.text.match(/['`"\u00B4\u2032-\u2037\u2057]/)&&(data.mathvariant="-TeX-variant"))},CHTMLfixCombiningChar:function(node){node=node.firstChild;var space=CHTML.Element("mjx-box",{style:{width:".25em","margin-left":"-.25em"}});node.insertBefore(space,node.firstChild)},CHTMLcenterOp:function(node){var bbox=this.CHTML,p=(bbox.h-bbox.d)/2-CHTML.TEX.axis_height;Math.abs(p)>.001&&(node.style.verticalAlign=CHTML.Em(-p)),bbox.h-=p,bbox.d+=p,bbox.r>bbox.w&&(bbox.ic=bbox.r-bbox.w,bbox.w=bbox.r,node.style.paddingRight=CHTML.Em(bbox.ic))},CHTMLcanStretch:function(direction,H,D){if(!this.Get("stretchy"))return!1;var c=this.data.join("");if(!CHTML.isChar(c))return!1;var values={text:c};this.CHTMLadjustAccent(values),values.remapchars&&(c=values.remapchars[c]||c);var stretch=(c=CHTML.FONTDATA.DELIMITERS[c.charCodeAt(0)])&&c.dir===direction.substr(0,1);return stretch&&(stretch=this.CHTML.h!==H||this.CHTML.d!==D||!!this.Get("minsize",!0)||!!this.Get("maxsize",!0))&&(this.CHTML.stretch=!0),stretch},CHTMLstretchV:function(h,d){var node=this.CHTMLnodeElement(),bbox=this.CHTML,values=this.getValues("symmetric","maxsize","minsize"),H,a=CHTML.TEX.axis_height;if(H=values.symmetric?2*Math.max(h-a,d+a):h+d,values.maxsize=this.CHTMLlength2em(values.maxsize,bbox.h+bbox.d),values.minsize=this.CHTMLlength2em(values.minsize,bbox.h+bbox.d),(H=Math.max(values.minsize,Math.min(values.maxsize,H)))!==bbox.sH){for(H!=values.minsize&&(H=[Math.max(H*CHTML.TEX.delimiterfactor/1e3,H-CHTML.TEX.delimitershortfall),H]);node.firstChild;)node.removeChild(node.firstChild);this.CHTML=bbox=CHTML.createDelimiter(node,this.data.join("").charCodeAt(0),H,bbox),bbox.sH=H instanceof Array?H[1]:H,H=values.symmetric?(bbox.h+bbox.d)/2+a:(bbox.h+bbox.d)*h/(h+d),H-=bbox.h,Math.abs(H)>.05&&(node.style.verticalAlign=CHTML.Em(H),bbox.h+=H,bbox.d-=H,bbox.t+=H,bbox.b-=H)}return this.CHTML},CHTMLstretchH:function(node,W){var bbox=this.CHTML,values=this.getValues("maxsize","minsize","mathvariant","fontweight");if(("bold"===values.fontweight||"bold"===(this.removedStyles||{}).fontWeight||parseInt(values.fontweight)>=600)&&!this.Get("mathvariant",!0)&&(values.mathvariant=MML.VARIANT.BOLD),values.maxsize=this.CHTMLlength2em(values.maxsize,bbox.w),values.minsize=this.CHTMLlength2em(values.minsize,bbox.w),(W=Math.max(values.minsize,Math.min(values.maxsize,W)))!==bbox.sW){for(;node.firstChild;)node.removeChild(node.firstChild);this.CHTML=bbox=CHTML.createDelimiter(node,this.data.join("").charCodeAt(0),W,bbox,values.mathvariant),bbox.sW=W}return this.CHTML}}),MML.mtext.Augment({CHTMLgetVariant:function(){if(CHTML.config.mtextFontInherit||"merror"===this.Parent().type){var scale=CHTML.config.scale/100/CHTML.scale,variant={cache:{},fonts:[],className:"MJXc-font-inherit",rscale:scale,style:{"font-size":CHTML.Percent(scale)}},name=this.Get("mathvariant");name.match(/bold/)&&(variant.style["font-weight"]="bold"),name.match(/italic|-tex-mathit/)&&(variant.style["font-style"]="italic"),"monospace"===name&&(variant.className+=" MJXc-monospace-font"),"double-struck"===name&&(variant.className+=" MJXc-double-struck-font"),name.match(/fraktur/)&&(variant.className+=" MJXc-fraktur-font"),name.match(/sans-serif/)&&(variant.className+=" MJXc-sans-serif-font"),name.match(/script/)&&(variant.className+=" MJXc-script-font"),this.CHTMLvariant=variant}else this.SUPER(arguments).CHTMLgetVariant.call(this)}}),MML.merror.Augment({toCommonHTML:function(node){node=this.CHTMLdefaultNode(node);var bbox=this.CHTML;return bbox.rescale(.9),bbox.h+=3/CHTML.em,bbox.h>bbox.t&&(bbox.t=bbox.h),bbox.d+=3/CHTML.em,bbox.d>bbox.b&&(bbox.b=bbox.d),bbox.w+=8/CHTML.em,bbox.r=bbox.w,bbox.l=0,node}}),MML.mspace.Augment({toCommonHTML:function(node){node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLhandleScale(node);var values=this.getValues("height","depth","width"),w=this.CHTMLlength2em(values.width),h=this.CHTMLlength2em(values.height),d=this.CHTMLlength2em(values.depth),bbox=this.CHTML;return bbox.w=bbox.r=w,bbox.h=bbox.t=h,bbox.d=bbox.b=d,bbox.l=0,w<0&&(node.style.marginRight=CHTML.Em(w),w=0),node.style.width=CHTML.Em(w),node.style.height=CHTML.Em(Math.max(0,h+d)),d&&(node.style.verticalAlign=CHTML.Em(-d)),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node}}),MML.mpadded.Augment({toCommonHTML:function(node,options){var child;options&&options.stretch?child=(node=node.firstChild).firstChild:(child=(node=this.CHTMLdefaultNode(node,{childNodes:"mjx-box",forceChild:!0})).firstChild,(node=CHTML.addElement(node,"mjx-block")).appendChild(child),CHTML.addElement(node,"mjx-strut"));var cbox=this.CHTMLbboxFor(0),values=this.getValues("width","height","depth","lspace","voffset"),x=0,y=0,w=cbox.w,h=cbox.h,d=cbox.d;child.style.width=0,child.style.margin=CHTML.Em(-h)+" 0 "+CHTML.Em(-d),""!==values.width&&(w=this.CHTMLdimen(values.width,"w",w,0)),""!==values.height&&(h=this.CHTMLdimen(values.height,"h",h,0)),""!==values.depth&&(d=this.CHTMLdimen(values.depth,"d",d,0)),""!==values.voffset&&(y=this.CHTMLdimen(values.voffset))&&(child.style.position="relative",child.style.top=CHTML.Em(-y)),""!==values.lspace&&(x=this.CHTMLdimen(values.lspace))&&(child.style.position="relative",child.style.left=CHTML.Em(x)),node.style.width=0,node.style.marginTop=CHTML.Em(h-1),node.style.padding="0 "+CHTML.Em(w)+" "+CHTML.Em(d)+" 0";var bbox=CHTML.BBOX({w:w,h:h,d:d,l:0,r:w,t:h,b:d,scale:this.CHTML.scale,rscale:this.CHTML.rscale});return bbox.combine(cbox,x,y),bbox.w=w,bbox.h=h,bbox.d=d,this.CHTML=bbox,node.parentNode},CHTMLstretchV:MML.mbase.CHTMLstretchV,CHTMLstretchH:MML.mbase.CHTMLstretchH,CHTMLdimen:function(length,d,D,m){null==m&&(m=-1e6);var match=(length=String(length)).match(/width|height|depth/),size=match?this.CHTML[match[0].charAt(0)]:d?this.CHTML[d]:0,dimen=this.CHTMLlength2em(length,size)||0;return length.match(/^[-+]/)&&null!=D&&(dimen+=D),null!=m&&(dimen=Math.max(m,dimen)),dimen}}),MML.munderover.Augment({toCommonHTML:function(node,options){var values=this.getValues("displaystyle","accent","accentunder","align"),base=this.data[this.base];if(!values.displaystyle&&null!=base&&(base.movablelimits||base.CoreMO().Get("movablelimits")))return MML.msubsup.prototype.toCommonHTML.call(this,node,stretch);var under,over,nodes=[],stretch=!1;if(options&&options.stretch)this.data[this.base]&&(base=CHTML.getNode(node,"mjx-op")),this.data[this.under]&&(under=CHTML.getNode(node,"mjx-under")),this.data[this.over]&&(over=CHTML.getNode(node,"mjx-over")),nodes[0]=base,nodes[1]=under||over,nodes[2]=over,stretch=!0;else{var types=["mjx-op","mjx-under","mjx-over"];1===this.over&&(types[1]=types[2]),node=this.CHTMLdefaultNode(node,{childNodes:types,noBBox:!0,forceChild:!0,minChildren:2}),nodes[0]=base=node.removeChild(node.firstChild),nodes[1]=under=over=node.removeChild(node.firstChild),node.firstChild&&(nodes[2]=over=node.removeChild(node.firstChild))}var boxes=[],W=this.CHTMLgetBBoxes(boxes,nodes,values),bbox=boxes[this.base],BBOX=this.CHTML;BBOX.w=W,BBOX.h=bbox.h,BBOX.d=bbox.d,bbox.h<.35&&(base.style.marginTop=CHTML.Em(bbox.h-.35)),values.accent&&bbox.h<CHTML.TEX.x_height&&(BBOX.h+=CHTML.TEX.x_height-bbox.h,base.style.marginTop=CHTML.Em(CHTML.TEX.x_height-Math.max(bbox.h,.35)),bbox.h=CHTML.TEX.x_height);var stack=base,delta=0;return bbox.ic&&(delta=1.3*bbox.ic+.05),this.data[this.over]&&(stack=this.CHTMLaddOverscript(over,boxes,values,delta,base,stretch)),this.data[this.under]?this.CHTMLaddUnderscript(under,boxes,values,delta,node,stack,stretch):stretch||node.appendChild(stack),this.CHTMLplaceBoxes(base,under,over,values,boxes),node},CHTMLgetBBoxes:function(bbox,nodes,values){var i,m=this.data.length,scale,w=-1e6,W=w;for(i=0;i<m;i++)bbox[i]=this.CHTMLbboxFor(i),bbox[i].x=bbox[i].y=0,this.data[i]&&(bbox[i].stretch=this.data[i].CHTMLcanStretch("Horizontal")),scale=i===this.base?1:bbox[i].rscale,i!==this.base&&(delete bbox[i].L,delete bbox[i].R),W=Math.max(W,scale*(bbox[i].w+(bbox[i].L||0)+(bbox[i].R||0))),!bbox[i].stretch&&W>w&&(w=W);for(-1e6===w&&(w=W),i=0;i<m;i++)bbox[i].stretch&&(scale=i===this.base?1:bbox[i].rscale,bbox[i]=this.data[i].CHTMLstretchH(nodes[i].firstChild,w/scale),bbox[i].x=bbox[i].y=0,W=Math.max(W,scale*(bbox[i].w+(bbox[i].L||0)+(bbox[i].R||0))));return bbox[this.base]||(bbox[this.base]=CHTML.BBOX.empty()),W},CHTMLaddOverscript:function(over,boxes,values,delta,base,stretch){var BBOX=this.CHTML,z1,z2,z3=CHTML.TEX.big_op_spacing5,k,obox=boxes[this.over],bbox=boxes[this.base],scale=obox.rscale;if(!stretch){var stack=CHTML.Element("mjx-stack");stack.appendChild(over),stack.appendChild(base)}return obox.D&&(obox.d=obox.D),obox.d<0&&(over.firstChild.style.verticalAlign="top",over.style.height=CHTML.Em(obox.h+obox.d)),obox.x=0,values.accent?(obox.w<.001&&(obox.x+=(obox.r-obox.l)/2),k=CHTML.TEX.rule_thickness,z3=0,bbox.skew&&(obox.x+=scale*bbox.skew,BBOX.skew=scale*bbox.skew,obox.x+scale*obox.w>BBOX.w&&(BBOX.skew+=(BBOX.w-(obox.x+scale*obox.w))/2))):(z1=CHTML.TEX.big_op_spacing1,z2=CHTML.TEX.big_op_spacing3,k=Math.max(z1,z2-Math.max(0,scale*obox.d))),obox.x+=delta/2,obox.y=BBOX.h+k+z3+scale*obox.d,k&&(over.style.paddingBottom=CHTML.Em(k/scale)),z3&&(over.style.paddingTop=CHTML.Em(z3/scale)),stack},CHTMLaddUnderscript:function(under,boxes,values,delta,node,stack,stretch){var BBOX=this.CHTML,z1,z2,z3=CHTML.TEX.big_op_spacing5,k,ubox=boxes[this.under],scale=ubox.rscale;stretch||(CHTML.addElement(node,"mjx-itable",{},[["mjx-row",{},[["mjx-cell"]]],["mjx-row"]]),node.firstChild.firstChild.firstChild.appendChild(stack),node.firstChild.lastChild.appendChild(under)),ubox.D&&(ubox.d=ubox.D),ubox.d<0&&(under.firstChild.style.verticalAlign="top",node.firstChild.style.marginBottom=CHTML.Em(ubox.d)),values.accentunder?(k=2*CHTML.TEX.rule_thickness,z3=0):(z1=CHTML.TEX.big_op_spacing2,z2=CHTML.TEX.big_op_spacing4,k=Math.max(z1,z2-scale*ubox.h)),ubox.x=-delta/2,ubox.y=-(BBOX.d+k+z3+scale*ubox.h),k&&(under.style.paddingTop=CHTML.Em(k/scale)),z3&&(under.style.paddingBottom=CHTML.Em(z3/scale))},CHTMLplaceBoxes:function(base,under,over,values,boxes){var W=this.CHTML.w,i,m=boxes.length,scale,BBOX=CHTML.BBOX.zero();BBOX.scale=this.CHTML.scale,BBOX.rscale=this.CHTML.rscale,boxes[this.base].x=boxes[this.base].y=0;var dx=1e6;for(i=0;i<m;i++){var w=(scale=i===this.base?1:boxes[i].rscale)*(boxes[i].w+(boxes[i].L||0)+(boxes[i].R||0));boxes[i].x+={left:0,center:(W-w)/2,right:W-w}[values.align],boxes[i].x<dx&&(dx=boxes[i].x)}for(i=0;i<m;i++)if(this.data[i]){var node;if(scale=i===this.base?1:boxes[i].rscale,boxes[i].x-dx)(i===this.base?base:i===this.over?over:under).style.paddingLeft=CHTML.Em((boxes[i].x-dx)/scale);BBOX.combine(boxes[i],boxes[i].x-dx,boxes[i].y)}this.CHTML=BBOX},CHTMLstretchV:MML.mbase.CHTMLstretchV,CHTMLstretchH:MML.mbase.CHTMLstretchH,CHTMLchildNode:function(node,i){var types=["mjx-op","mjx-under","mjx-over"];return 1===this.over&&(types[1]=types[2]),CHTML.getNode(node,types[i])}}),MML.msubsup.Augment({toCommonHTML:function(node,options){var values=this.getValues("displaystyle","subscriptshift","superscriptshift","texprimestyle"),base,sub,sup;if(options&&options.stretch)this.data[this.base]&&(base=CHTML.getNode(node,"mjx-base")),this.data[this.sub]&&(sub=CHTML.getNode(node,"mjx-sub")),this.data[this.sup]&&(sup=CHTML.getNode(node,"mjx-sup")),stack=CHTML.getNode(node,"mjx-stack");else{var types=["mjx-base","mjx-sub","mjx-sup"];if(1===this.sup&&(types[1]=types[2]),base=(node=this.CHTMLdefaultNode(node,{childNodes:types,noBBox:!0,forceChild:!0,minChildren:3})).childNodes[this.base],sub=node.childNodes[this.sub],sup=node.childNodes[this.sup],this.CHTMLnotEmpty(this.data[this.sub])||(node.removeChild(sub),sub=null),this.CHTMLnotEmpty(this.data[this.sup])||(node.removeChild(sup),sup=null),3===node.childNodes.length){var stack=CHTML.addElement(node,"mjx-stack");stack.appendChild(sup),stack.appendChild(sub)}}for(var boxes=[],BBOX=CHTML.BBOX.empty(this.CHTML),i=0,m=this.data.length;i<m;i++)boxes[i]=this.CHTMLbboxFor(i);var bbox=boxes[this.base]||CHTML.BBOX.empty(),sbox=boxes[this.sub],Sbox=boxes[this.sup],sscale=sub?sbox.rscale:1,Sscale=sup?Sbox.rscale:1;BBOX.combine(bbox,0,0);var ex=CHTML.TEX.x_height,s=CHTML.TEX.scriptspace,q=CHTML.TEX.sup_drop*Sscale,r=CHTML.TEX.sub_drop*sscale,u=bbox.h-q,v=bbox.d+r,delta=0,p;bbox.ic&&(BBOX.w-=bbox.ic,base.style.marginRight=CHTML.Em(-bbox.ic),delta=1.3*bbox.ic+.05);var bmml=this.data[this.base];bmml&&("mrow"!==bmml.type&&"mstyle"!==bmml.type||1!==bmml.data.length||(bmml=bmml.data[0]),"mi"!==bmml.type&&"mo"!==bmml.type||!CHTML.isChar(bmml.data.join(""))||1!==bbox.rscale||bbox.sH||bmml.Get("largeop")||(u=v=0)),values.subscriptshift=""===values.subscriptshift?0:this.CHTMLlength2em(values.subscriptshift),values.superscriptshift=""===values.superscriptshift?0:this.CHTMLlength2em(values.superscriptshift);var x=BBOX.w;if(sub&&(sbox.w+=s),sup&&(Sbox.w+=s),sup)if(sub){v=Math.max(v,CHTML.TEX.sub2);var t=CHTML.TEX.rule_thickness;u-Sscale*Sbox.d-(sscale*sbox.h-v)<3*t&&(v=3*t-u+Sscale*Sbox.d+sscale*sbox.h,(q=.8*ex-(u-Sscale*Sbox.d))>0&&(u+=q,v-=q)),u=Math.max(u,values.superscriptshift),v=Math.max(v,values.subscriptshift),sub.style.paddingRight=CHTML.Em(s/sscale),sup.style.paddingBottom=CHTML.Em(u/Sscale+v/sscale-Sbox.d-sbox.h/sscale*Sscale),sup.style.paddingLeft=CHTML.Em(delta/Sscale),sup.style.paddingRight=CHTML.Em(s/Sscale),stack.style.verticalAlign=CHTML.Em(-v),BBOX.combine(Sbox,x+delta,u),BBOX.combine(sbox,x,-v)}else p=CHTML.TEX[values.displaystyle?"sup1":values.texprimestyle?"sup3":"sup2"],u=Math.max(u,p,Sscale*Sbox.d+.25*ex,values.superscriptshift),sup.style.verticalAlign=CHTML.Em(u/Sscale),sup.style.paddingLeft=CHTML.Em(delta/Sscale),sup.style.paddingRight=CHTML.Em(s/Sscale),BBOX.combine(Sbox,x+delta,u);else sub&&(v=Math.max(v,CHTML.TEX.sub1,sscale*sbox.h-.8*ex,values.subscriptshift),sub.style.verticalAlign=CHTML.Em(-v/sscale),sub.style.paddingRight=CHTML.Em(s/sscale),BBOX.combine(sbox,x,-v));return BBOX.clean(),node},CHTMLstretchV:MML.mbase.CHTMLstretchV,CHTMLstretchH:MML.mbase.CHTMLstretchH,CHTMLchildNode:function(node,i){var types=["mjx-base","mjx-sub","mjx-sup"];return 1===this.over&&(types[1]=types[2]),CHTML.getNode(node,types[i])}}),MML.mfrac.Augment({toCommonHTML:function(node){node=this.CHTMLdefaultNode(node,{childNodes:["mjx-numerator","mjx-denominator"],childOptions:{autowidth:!0},forceChild:!0,noBBox:!0,minChildren:2});var values=this.getValues("linethickness","displaystyle","numalign","denomalign","bevelled"),isDisplay=values.displaystyle,num=node.firstChild,denom=node.lastChild,frac=CHTML.addElement(node,"mjx-box");frac.appendChild(num),frac.appendChild(denom),node.appendChild(frac),"center"!==values.numalign&&(num.style.textAlign=values.numalign),"center"!==values.denomalign&&(denom.style.textAlign=values.denomalign);var nbox=this.CHTMLbboxFor(0),dbox=this.CHTMLbboxFor(1),BBOX=CHTML.BBOX.empty(this.CHTML),nscale=nbox.rscale,dscale=dbox.rscale;values.linethickness=Math.max(0,CHTML.thickness2em(values.linethickness||"0",BBOX.scale));var mt=CHTML.TEX.min_rule_thickness/CHTML.em,a=CHTML.TEX.axis_height,t=values.linethickness,p,q,u,v;if(values.bevelled){frac.className+=" MJXc-bevelled";var delta=isDisplay?.4:.15,H=Math.max(nscale*(nbox.h+nbox.d),dscale*(dbox.h+dbox.d))+2*delta,bevel=CHTML.Element("mjx-bevel");frac.insertBefore(bevel,denom);var bbox=CHTML.createDelimiter(bevel,47,H);u=nscale*(nbox.d-nbox.h)/2+a+delta,v=dscale*(dbox.d-dbox.h)/2+a-delta,u&&(num.style.verticalAlign=CHTML.Em(u/nscale)),v&&(denom.style.verticalAlign=CHTML.Em(v/dscale)),bevel.style.marginLeft=bevel.style.marginRight=CHTML.Em(-delta/2),BBOX.combine(nbox,0,u),BBOX.combine(bbox,nscale*nbox.w-delta/2,0),BBOX.combine(dbox,nscale*nbox.w+bbox.w-delta,v),BBOX.clean()}else{if(frac.className+=" MJXc-stacked",isDisplay?(u=CHTML.TEX.num1,v=CHTML.TEX.denom1):(u=0===t?CHTML.TEX.num3:CHTML.TEX.num2,v=CHTML.TEX.denom2),0===t)p=Math.max((isDisplay?7:3)*CHTML.TEX.rule_thickness,2*mt),(q=u-nbox.d*nscale-(dbox.h*dscale-v))<p&&(u+=(p-q)/2,v+=(p-q)/2);else{p=Math.max((isDisplay?2:0)*mt+t,t/2+1.5*mt),t=Math.max(t,mt),(q=u-nbox.d*nscale-(a+t/2))<p&&(u+=p-q),(q=a-t/2-(dbox.h*dscale-v))<p&&(v+=p-q),nbox.L=nbox.R=dbox.L=dbox.R=.1;var rule=CHTML.addElement(frac,"mjx-line",{style:{"border-bottom":CHTML.Px(t*BBOX.scale,1)+" solid",top:CHTML.Em(-t/2-a)}})}BBOX.combine(nbox,0,u),BBOX.combine(dbox,0,-v),BBOX.clean(),frac.style.width=CHTML.Em(BBOX.w),num.style.width=CHTML.Em(BBOX.w/nscale),denom.style.width=CHTML.Em(BBOX.w/dscale),rule&&(rule.style.width=frac.style.width),num.style.top=CHTML.Em(-BBOX.h/nscale),denom.style.bottom=CHTML.Em(-BBOX.d/dscale),CHTML.addElement(node,"mjx-vsize",{style:{height:CHTML.Em(BBOX.h+BBOX.d),verticalAlign:CHTML.Em(-BBOX.d)}})}if(!this.texWithDelims){var space=CHTML.TEX.nulldelimiterspace;frac.style.padding="0 "+CHTML.Em(space),BBOX.l+=space,BBOX.r+=space,BBOX.w+=2*space}return node},CHTMLcanStretch:function(direction){return!1}}),MML.msqrt.Augment({toCommonHTML:function(node){var base=(node=this.CHTMLdefaultNode(node,{childNodes:["mjx-box","mjx-root"],forceChild:!0,noBBox:!0})).firstChild||CHTML.Element("mjx-box"),sqrt=CHTML.addElement(node,"mjx-box");sqrt.appendChild(base);var bbox=this.CHTMLbboxFor(0),BBOX=CHTML.BBOX.empty(this.CHTML),t=CHTML.TEX.rule_thickness,T=CHTML.TEX.surd_height,p=t,q,H;this.Get("displaystyle")&&(p=CHTML.TEX.x_height),q=t+p/4,H=bbox.h+bbox.d+q+t;var surd=CHTML.Element("mjx-surd");sqrt.insertBefore(surd,base);var sbox=CHTML.createDelimiter(surd,8730,[H-.04,H]);sbox.h+sbox.d>H&&(q=(sbox.h+sbox.d-(H-t))/2),H=bbox.h+q+t;var x=this.CHTMLaddRoot(node,sbox,sbox.h+sbox.d-H);return base.style.paddingTop=CHTML.Em(q),base.style.borderTop=CHTML.Px(T*bbox.scale,1)+" solid",sqrt.style.paddingTop=CHTML.Em(2*t-T),bbox.h+=q+2*t,BBOX.combine(sbox,x,H-sbox.h),BBOX.combine(bbox,x+sbox.w,0),BBOX.clean(),node},CHTMLaddRoot:function(){return 0},CHTMLhandleBBox:function(node){var bbox=this.CHTMLbboxFor(0);delete bbox.pwidth,this.SUPER(arguments).CHTMLhandleBBox.apply(this,arguments)}}),MML.mroot.Augment({toCommonHTML:MML.msqrt.prototype.toCommonHTML,CHTMLhandleBBox:MML.msqrt.prototype.CHTMLhandleBBox,CHTMLaddRoot:function(sqrt,sbox,d){if(this.data[1]){var BBOX=this.CHTML,bbox=this.data[1].CHTML,root=sqrt.firstChild,scale=bbox.rscale,h=this.CHTMLrootHeight(bbox,sbox,scale)-d,w=Math.min(bbox.w,bbox.r),dx=Math.max(w,sbox.offset/scale);return h&&(root.style.verticalAlign=CHTML.Em(h/scale)),dx>w&&(root.firstChild.style.paddingLeft=CHTML.Em(dx-w)),dx-=sbox.offset/scale,root.style.width=CHTML.Em(dx),BBOX.combine(bbox,0,h),dx*scale}},CHTMLrootHeight:function(bbox,sbox,scale){return.45*(sbox.h+sbox.d-.9)+sbox.offset+Math.max(0,bbox.d-.075)}}),MML.mfenced.Augment({toCommonHTML:function(node){node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLhandleScale(node),this.CHTMLaddChild(node,"open",{});for(var i=0,m=this.data.length;i<m;i++)this.CHTMLaddChild(node,"sep"+i,{}),this.CHTMLaddChild(node,i,{});this.CHTMLaddChild(node,"close",{});var H=this.CHTML.h,D=this.CHTML.d;for(this.CHTMLstretchChildV("open",H,D),i=0,m=this.data.length;i<m;i++)this.CHTMLstretchChildV("sep"+i,H,D),this.CHTMLstretchChildV(i,H,D);return this.CHTMLstretchChildV("close",H,D),this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node}}),MML.mrow.Augment({toCommonHTML:function(node,options){options=options||{},node=this.CHTMLdefaultNode(node);for(var bbox=this.CHTML,H=bbox.h,D=bbox.d,hasNegative,i=0,m=this.data.length;i<m;i++)this.CHTMLstretchChildV(i,H,D),this.data[i]&&this.data[i].CHTML&&this.data[i].CHTML.w<0&&(hasNegative=!0);return this.CHTMLlineBreaks()?(this.CHTMLmultiline(node),options.autowidth&&(node.style.width="")):(hasNegative&&bbox.w&&(node.style.width=CHTML.Em(Math.max(0,bbox.w))),bbox.w<0&&(node.style.marginRight=CHTML.Em(bbox.w))),node},CHTMLlineBreaks:function(){return!!this.parent.linebreakContainer&&(LINEBREAKS.automatic&&this.CHTML.w>CHTML.linebreakWidth||this.hasNewline())},CHTMLstretchV:function(h,d){return this.CHTMLstretchChildV(this.CoreIndex(),h,d),this.CHTML},CHTMLstretchH:function(node,w){return this.CHTMLstretchChildH(this.CoreIndex(),w,node),this.CHTML}}),MML.TeXAtom.Augment({toCommonHTML:function(node,options){if(options&&options.stretch||(node=this.CHTMLdefaultNode(node)),this.texClass===MML.TEXCLASS.VCENTER){var a=CHTML.TEX.axis_height,BBOX=this.CHTML,v=a-(BBOX.h+BBOX.d)/2+BBOX.d;Math.abs(v)>.001&&(node.style.verticalAlign=CHTML.Em(v),BBOX.h+=v,BBOX.t+=v,BBOX.d-=v,BBOX.b-=v)}return node},CHTMLstretchV:function(h,d){return this.CHTMLupdateFrom(this.Core().CHTMLstretchV(h,d)),this.toCommonHTML(this.CHTMLnodeElement(),{stretch:!0}),this.CHTML},CHTMLstretchH:function(node,w){return this.CHTMLupdateFrom(this.CHTMLstretchCoreH(node,w)),this.toCommonHTML(node,{stretch:!0}),this.CHTML}}),MML.semantics.Augment({toCommonHTML:function(node){return node=this.CHTMLcreateNode(node),this.data[0]&&(this.data[0].toCommonHTML(node),this.CHTMLupdateFrom(this.data[0].CHTML),this.CHTMLhandleBBox(node)),node}}),MML.annotation.Augment({toCommonHTML:function(node){return this.CHTMLcreateNode(node)}}),MML["annotation-xml"].Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MML.ms.Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MML.mglyph.Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MML.menclose.Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MML.maction.Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MML.mmultiscripts.Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MML.mtable.Augment({toCommonHTML:MML.mbase.CHTMLautoload}),MathJax.Hub.Register.StartupHook("onLoad",(function(){setTimeout(MathJax.Callback(["loadComplete",CHTML,"jax.js"]),0)}))})),MathJax.Hub.Register.StartupHook("End Cookie",(function(){"None"!==HUB.config.menuSettings.zoom&&AJAX.Require("[MathJax]/extensions/MathZoom.js")}))}(MathJax.Ajax,MathJax.Hub,MathJax.HTML,MathJax.OutputJax.CommonHTML),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CHTML=MathJax.OutputJax.CommonHTML;MML["annotation-xml"].Augment({toCommonHTML:function(node){var encoding=this.Get("encoding");return node=this.CHTMLdefaultNode(node,{childOptions:{encoding:encoding}}),1!==this.CHTML.rscale&&this.CHTML.rescale(1/this.CHTML.rscale),node}}),MML.xml.Augment({toCommonHTML:function(node,options){for(var bbox=this.CHTML=CHTML.BBOX.zero(),i=0,m=this.data.length;i<m;i++)node.appendChild(this.data[i].cloneNode(!0));var w=node.offsetWidth,h=node.offsetHeight,strut=CHTML.addElement(node,"mjx-hd-test",{style:{height:h+"px"}});bbox.d=bbox.b=(node.offsetHeight-h)/CHTML.em,bbox.w=bbox.r=w/CHTML.em,bbox.h=bbox.t=h/CHTML.em-bbox.d,node.removeChild(strut)}}),MathJax.Hub.Startup.signal.Post("CommonHTML annotation-xml Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/annotation-xml.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CHTML=MathJax.OutputJax.CommonHTML,currentTip,hover,clear,CONFIG=CHTML.config.tooltip=MathJax.Hub.Insert({delayPost:600,delayClear:600,offsetX:10,offsetY:5},CHTML.config.tooltip||{});MML.maction.Augment({CHTMLtooltip:CHTML.addElement(document.body,"div",{id:"MathJax_CHTML_Tooltip"}),toCommonHTML:function(node){var selected=this.Get("selection");node=this.CHTMLcreateNode(node),this.CHTML=CHTML.BBOX.empty(),this.CHTMLhandleStyle(node),this.CHTMLhandleScale(node),this.CHTMLaddChild(node,selected-1,{}),this.CHTML.clean(),this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node);var type=this.Get("actiontype");return this.CHTMLaction[type]&&this.CHTMLaction.hasOwnProperty(type)&&this.CHTMLaction[type].call(this,node,selected),node},CHTMLcoreNode:function(node){return this.CHTMLchildNode(node,0)},CHTMLaction:{toggle:function(node,selection){this.selection=selection,node.onclick=MathJax.Callback(["CHTMLclick",this,CHTML.jax]),node.style.cursor="pointer"},statusline:function(node,selection){node.onmouseover=MathJax.Callback(["CHTMLsetStatus",this]),node.onmouseout=MathJax.Callback(["CHTMLclearStatus",this]),node.onmouseover.autoReset=node.onmouseout.autoReset=!0},tooltip:function(node,selection){this.data[1]&&this.data[1].isToken?node.title=node.alt=this.data[1].data.join(""):(node.onmouseover=MathJax.Callback(["CHTMLtooltipOver",this,CHTML.jax]),node.onmouseout=MathJax.Callback(["CHTMLtooltipOut",this,CHTML.jax]),node.onmouseover.autoReset=node.onmouseout.autoReset=!0)}},CHTMLclick:function(jax,event){this.selection++,this.selection>this.data.length&&(this.selection=1);var hover=!!jax.hover;if(jax.Update(),hover){var span=document.getElementById(jax.inputID+"-Span");MathJax.Extension.MathEvents.Hover.Hover(jax,span)}return MathJax.Extension.MathEvents.Event.False(event)},CHTMLsetStatus:function(event){this.messageID=MathJax.Message.Set(this.data[1]&&this.data[1].isToken?this.data[1].data.join(""):this.data[1].toString())},CHTMLclearStatus:function(event){this.messageID&&MathJax.Message.Clear(this.messageID,0),delete this.messageID},CHTMLtooltipOver:function(jax,event){event||(event=window.event),clear&&(clearTimeout(clear),clear=null),hover&&clearTimeout(hover);var x=event.pageX,y=event.pageY;null==x&&(x=event.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,y=event.clientY+document.body.scrollTop+document.documentElement.scrollTop);var callback=MathJax.Callback(["CHTMLtooltipPost",this,jax,x+CONFIG.offsetX,y+CONFIG.offsetY]);hover=setTimeout(callback,CONFIG.delayPost)},CHTMLtooltipOut:function(jax,event){hover&&(clearTimeout(hover),hover=null),clear&&clearTimeout(clear);var callback=MathJax.Callback(["CHTMLtooltipClear",this,80]);clear=setTimeout(callback,CONFIG.delayClear)},CHTMLtooltipPost:function(jax,x,y){hover=null,clear&&(clearTimeout(clear),clear=null);var tip=this.CHTMLtooltip;if(tip.style.display="block",tip.style.opacity="",this!==currentTip){tip.style.left=x+"px",tip.style.top=y+"px",tip.innerHTML='<span class="mjx-chtml"><span class="mjx-math"></span></span>',CHTML.getMetrics(jax);try{this.data[1].toCommonHTML(tip.firstChild.firstChild)}catch(err){if(!err.restart)throw err;return tip.style.display="none",void MathJax.Callback.After(["CHTMLtooltipPost",this,jax,x,y],err.restart)}currentTip=this}},CHTMLtooltipClear:function(n){var tip=this.CHTMLtooltip;n<=0?(tip.style.display="none",tip.style.opacity=tip.style.filter="",clear=null):(tip.style.opacity=n/100,tip.style.filter="alpha(opacity="+n+")",clear=setTimeout(MathJax.Callback(["CHTMLtooltipClear",this,n-20]),50))}}),MathJax.Hub.Startup.signal.Post("CommonHTML maction Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/maction.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CHTML=MathJax.OutputJax.CommonHTML,SVGNS="http://www.w3.org/2000/svg",ARROWX=4,ARROWDX=1,ARROWY=2;MML.menclose.Augment({toCommonHTML:function(node){var values=this.getValues("notation","thickness","padding");null==values.thickness&&(values.thickness=".075em"),null==values.padding&&(values.padding=".2em");var child=(node=this.CHTMLdefaultNode(node,{childNodes:"mjx-box",forceChild:!0})).firstChild,cbox=this.CHTMLbboxFor(0),p=this.CHTMLlength2em(values.padding,1/CHTML.em),t=this.CHTMLlength2em(values.thickness,1/CHTML.em);t=Math.max(1,Math.round(t*CHTML.em))/CHTML.em;var SOLID=CHTML.Px(t)+" solid",bb={L:p,R:p,T:p,B:p,H:cbox.h+p,D:cbox.d+p,W:cbox.w+2*p};child.style.padding=CHTML.Em(p);for(var notations=MathJax.Hub.SplitList(values.notation),notation={},i=0,m=notations.length;i<m;i++)notation[notations[i]]=!0;for(var n in notation[MML.NOTATION.UPDIAGONALARROW]&&delete notation[MML.NOTATION.UPDIAGONALSTRIKE],notation)notation.hasOwnProperty(n)&&this.CHTMLnotation[n]&&this.CHTMLnotation.hasOwnProperty(n)&&this.CHTMLnotation[n].call(this,child,cbox,bb,p,t,SOLID);var BBOX=this.CHTML;return BBOX.w+=bb.L+bb.R,BBOX.r+=BBOX.L,BBOX.w>BBOX.r&&(BBOX.r=BBOX.w),BBOX.h+=bb.T,BBOX.h>BBOX.t&&(BBOX.t=BBOX.h),BBOX.d+=bb.B,BBOX.d>BBOX.b&&(BBOX.b=BBOX.d),node},CHTMLnotation:{box:function(child,cbox,bb,p,t,SOLID){p-=t,child.style.padding=CHTML.Em(p),child.style.border=SOLID},roundedbox:function(child,cbox,bb,p,t,SOLID){var r=Math.min(cbox.w,cbox.h+cbox.d+2*p)/4;CHTML.addElement(child.parentNode,"mjx-box",{style:{padding:CHTML.Em(p-t),border:SOLID,"border-radius":CHTML.Em(r),height:CHTML.Em(cbox.h+cbox.d),"vertical-align":CHTML.Em(-bb.D),width:CHTML.Em(cbox.w),"margin-left":CHTML.Em(-bb.W)}})},circle:function(child,cbox,bb,p,t,SOLID){var H=bb.H,D=bb.D,W=bb.W,svg=this.CHTMLsvg(child,bb,t);this.CHTMLsvgElement(svg.firstChild,"ellipse",{rx:CHTML.Px(W/2-t/2),ry:CHTML.Px((H+D)/2-t/2),cx:CHTML.Px(W/2),cy:CHTML.Px((H+D)/2)})},left:function(child,cbox,bb,p,t,SOLID){child.style.borderLeft=SOLID,child.style.paddingLeft=CHTML.Em(p-t)},right:function(child,cbox,bb,p,t,SOLID){child.style.borderRight=SOLID,child.style.paddingRight=CHTML.Em(p-t)},top:function(child,cbox,bb,p,t,SOLID){child.style.borderTop=SOLID,child.style.paddingTop=CHTML.Em(p-t)},bottom:function(child,cbox,bb,p,t,SOLID){child.style.borderBottom=SOLID,child.style.paddingBottom=CHTML.Em(p-t)},actuarial:function(child,cbox,bb,p,t,SOLID){child.style.borderTop=child.style.borderRight=SOLID,child.style.paddingTop=child.style.paddingRight=CHTML.Em(p-t)},madruwb:function(child,cbox,bb,p,t,SOLID){child.style.borderBottom=child.style.borderRight=SOLID,child.style.paddingBottom=child.style.paddingRight=CHTML.Em(p-t)},verticalstrike:function(child,cbox,bb,p,t,SOLID){CHTML.addElement(child.parentNode,"mjx-box",{style:{"border-left":SOLID,height:CHTML.Em(bb.H+bb.D),"vertical-align":CHTML.Em(-bb.D),width:CHTML.Em(cbox.w/2+p-t/2),"margin-left":CHTML.Em(-cbox.w/2-p-t/2)}})},horizontalstrike:function(child,cbox,bb,p,t,SOLID){CHTML.addElement(child.parentNode,"mjx-box",{style:{"border-top":SOLID,height:CHTML.Em((bb.H+bb.D)/2-t/2),"vertical-align":CHTML.Em(-bb.D),width:CHTML.Em(bb.W),"margin-left":CHTML.Em(-bb.W)}})},updiagonalstrike:function(child,cbox,bb,p,t,SOLID){var H=bb.H,D=bb.D,W=bb.W,svg=this.CHTMLsvg(child,bb,t);this.CHTMLsvgElement(svg.firstChild,"line",{x1:CHTML.Px(t/2),y1:CHTML.Px(H+D-t),x2:CHTML.Px(W-t),y2:CHTML.Px(t/2)})},downdiagonalstrike:function(child,cbox,bb,p,t,SOLID){var H=bb.H,D=bb.D,W=bb.W,svg=this.CHTMLsvg(child,bb,t);this.CHTMLsvgElement(svg.firstChild,"line",{x1:CHTML.Px(t/2),y1:CHTML.Px(t/2),x2:CHTML.Px(W-t),y2:CHTML.Px(H+D-t)})},updiagonalarrow:function(child,cbox,bb,p,t,SOLID){var H=bb.H+bb.D-t,W=bb.W-t/2,a=Math.atan2(H,W)*(-180/Math.PI).toFixed(3),R=Math.sqrt(H*H+W*W),svg=this.CHTMLsvg(child,bb,t),g=this.CHTMLsvgElement(svg.firstChild,"g",{fill:"currentColor",transform:"translate("+this.CHTMLpx(t/2)+" "+this.CHTMLpx(H+t/2)+") rotate("+a+")"}),x=4*t,dx=1*t,y=2*t;this.CHTMLsvgElement(g,"line",{x1:CHTML.Px(t/2),y1:0,x2:CHTML.Px(R-x),y2:0}),this.CHTMLsvgElement(g,"path",{d:"M "+this.CHTMLpx(R-x)+",0 L "+this.CHTMLpx(R-x-dx)+","+this.CHTMLpx(y)+"L "+this.CHTMLpx(R)+",0 L "+this.CHTMLpx(R-x-dx)+","+this.CHTMLpx(-y),stroke:"none"})},phasorangle:function(child,cbox,bb,p,t,SOLID){var P=p,H=bb.H,D=bb.D;p=(H+D)/2;var W=bb.W+p-P;bb.W=W,bb.L=p,child.style.margin="0 0 0 "+CHTML.Em(p-P);var svg=this.CHTMLsvg(child,bb,t);this.CHTMLsvgElement(svg.firstChild,"path",{d:"M "+this.CHTMLpx(p)+",1 L 1,"+this.CHTMLpx(H+D-t)+" L "+this.CHTMLpx(W)+","+this.CHTMLpx(H+D-t)})},longdiv:function(child,cbox,bb,p,t,SOLID){bb.W+=1.5*p,bb.L+=1.5*p;var H=bb.H,D=bb.D,W=bb.W;child.style.margin="0 0 0 "+CHTML.Em(1.5*p);var svg=this.CHTMLsvg(child,bb,t);this.CHTMLsvgElement(svg.firstChild,"path",{d:"M "+this.CHTMLpx(W)+",1 L 1,1 a"+this.CHTMLpx(p)+","+this.CHTMLpx((H+D)/2-t/2)+" 0 0,1 1,"+this.CHTMLpx(H+D-1.5*t)})},radical:function(child,cbox,bb,p,t,SOLID){bb.W+=1.5*p,bb.L+=1.5*p;var H=bb.H,D=bb.D,W=bb.W;child.style.margin="0 0 0 "+CHTML.Em(1.5*p);var svg=this.CHTMLsvg(child,bb,t);this.CHTMLsvgElement(svg.firstChild,"path",{d:"M 1,"+this.CHTMLpx(.6*(H+D))+" L "+this.CHTMLpx(p)+","+this.CHTMLpx(H+D)+" L "+this.CHTMLpx(2*p)+",1 L "+this.CHTMLpx(W)+",1"})}},CHTMLpx:function(m){return m*=CHTML.em,Math.abs(m)<.1?"0":m.toFixed(1).replace(/\.0$/,"")},CHTMLsvg:function(node,bbox,t){if(!svg){var svg=document.createElementNS(SVGNS,"svg");svg.style&&(svg.style.width=CHTML.Em(bbox.W),svg.style.height=CHTML.Em(bbox.H+bbox.D),svg.style.verticalAlign=CHTML.Em(-bbox.D),svg.style.marginLeft=CHTML.Em(-bbox.W)),this.CHTMLsvgElement(svg,"g",{"stroke-width":CHTML.Px(t)}),node.parentNode.appendChild(svg)}return svg},CHTMLsvgElement:function(svg,type,def){var obj=document.createElementNS(SVGNS,type);if(obj.isMathJax=!0,def)for(var id in def)def.hasOwnProperty(id)&&obj.setAttributeNS(null,id,def[id].toString());return svg.appendChild(obj),obj}}),document.createElementNS||delete MML.menclose.prototype.toCommonHTML,MathJax.Hub.Startup.signal.Post("CommonHTML menclose Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/menclose.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CHTML=MathJax.OutputJax.CommonHTML,LOCALE=MathJax.Localization;MML.mglyph.Augment({toCommonHTML:function(node,options){var values=this.getValues("src","width","height","valign","alt");if(node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLhandleScale(node),""===values.src){var index=this.Get("index");this.CHTMLgetVariant(),index&&this.CHTMLvariant.style&&this.CHTMLhandleText(node,String.fromCharCode(index),this.CHTMLvariant)}else{var bbox=this.CHTML;if(bbox.img||(bbox.img=MML.mglyph.GLYPH[values.src]),bbox.img||(bbox.img=MML.mglyph.GLYPH[values.src]={img:new Image,status:"pending"},bbox.img.img.onload=MathJax.Callback(["CHTMLimgLoaded",this]),bbox.img.img.onerror=MathJax.Callback(["CHTMLimgError",this]),bbox.img.img.src=values.src,MathJax.Hub.RestartAfter(bbox.img.img.onload)),"OK"!==bbox.img.status){var err=MML.Error(LOCALE._(["MathML","BadMglyph"],"Bad mglyph: %1",values.src));err.data[0].data[0].mathsize="75%",this.Append(err),err.toCommonHTML(node),this.data.pop(),bbox.combine(err.CHTML,0,0,1)}else{var img=CHTML.addElement(node,"img",{isMathJax:!0,src:values.src,alt:values.alt,title:values.alt}),w=values.width,h=values.height,W=bbox.img.img.width/CHTML.em,H=bbox.img.img.height/CHTML.em,WW=W,HH=H;""!==w&&(W=this.CHTMLlength2em(w,WW),H=WW?W/WW*HH:0),""!==h&&(H=this.CHTMLlength2em(h,HH),""===w&&(W=HH?H/HH*WW:0)),img.style.width=CHTML.Em(W),bbox.w=bbox.r=W,img.style.height=CHTML.Em(H),bbox.h=bbox.t=H,values.valign&&(bbox.d=bbox.b=-this.CHTMLlength2em(values.valign,HH),img.style.verticalAlign=CHTML.Em(-bbox.d),bbox.h-=bbox.d,bbox.t=bbox.h)}}return this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node},CHTMLimgLoaded:function(event,status){"string"==typeof event&&(status=event),this.CHTML.img.status=status||"OK"},CHTMLimgError:function(){this.CHTML.img.img.onload("error")}},{GLYPH:{}}),MathJax.Hub.Startup.signal.Post("CommonHTML mglyph Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/mglyph.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CHTML=MathJax.OutputJax.CommonHTML;MML.mmultiscripts.Augment({toCommonHTML:function(node,options){var stretch=(options||{}).stretch,base,bbox;stretch||(node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLgetVariant(),this.CHTMLhandleScale(node)),CHTML.BBOX.empty(this.CHTML),stretch?base=CHTML.getNode(node,"mjx-base"):(this.CHTMLaddChild(node,0,{type:"mjx-base",noBBox:!0,forceChild:!0}),base=node.firstChild),(bbox=this.CHTMLbboxFor(0)).ic&&(bbox.R-=bbox.ic,stretch||(base.style.marginRight=CHTML.Em(-bbox.ic)),delta=1.3*bbox.ic+.05);var BOX={},BBOX={};this.CHTMLgetScripts(BOX,BBOX,stretch,node);var sub=BOX.sub,sup=BOX.sup,presub=BOX.presub,presup=BOX.presup,sbox=BBOX.sub,Sbox=BBOX.sup,pbox=BBOX.presub,Pbox=BBOX.presup;stretch||this.CHTMLaddBoxes(node,base,BOX);var values=this.getValues("scriptlevel","scriptsizemultiplier"),sscale=this.Get("scriptlevel")<3?values.scriptsizemultiplier:1,ex=CHTML.TEX.x_height,s=CHTML.TEX.scriptspace,q=CHTML.TEX.sup_drop*sscale,r=CHTML.TEX.sub_drop*sscale,u=bbox.h-q,v=bbox.d+r,delta=0,p,bmml=this.data[this.base];!bmml||"mi"!==bmml.type&&"mo"!==bmml.type||!CHTML.isChar(bmml.data.join(""))||1!==bbox.rscale||bbox.sH||bmml.Get("largeop")||(u=v=0),(values=this.getValues("displaystyle","subscriptshift","superscriptshift","texprimestyle")).subscriptshift=""===values.subscriptshift?0:this.CHTMLlength2em(values.subscriptshift),values.superscriptshift=""===values.superscriptshift?0:this.CHTMLlength2em(values.superscriptshift);var dx=presub?s+pbox.w:presup?s+Pbox.w-delta:0;this.CHTML.combine(bbox,dx,0);var x=this.CHTML.w;if(sup||presup)if(sub||presub){v=Math.max(v,CHTML.TEX.sub2);var t=CHTML.TEX.rule_thickness,h=(sbox||pbox).h,d=(Sbox||Pbox).d;presub&&(h=Math.max(h,pbox.h)),presup&&(d=Math.max(d,Pbox.d)),u-d-(h-v)<3*t&&(v=3*t-u+d+h,(q=.8*ex-(u-d))>0&&(u+=q,v-=q)),u=Math.max(u,values.superscriptshift),v=Math.max(v,values.subscriptshift),sup?sub?this.CHTMLplaceSubSup(sub,sbox,sup,Sbox,x,delta,u,v,s):this.CHTMLplaceSupOnly(sup,Sbox,x,delta,u,s):sub&&this.CHTMLplaceSubOnly(sub,sbox,x,v,s),presup?presub?this.CHTMLplacePresubPresup(presub,pbox,presup,Pbox,delta,u,v,s):this.CHTMLplacePresupOnly(presup,Pbox,delta,u,s):presub&&this.CHTMLplacePresubOnly(presub,pbox,v,s)}else p=CHTML.TEX[values.displaystyle?"sup1":values.texprimestyle?"sup3":"sup2"],u=Math.max(u,p,values.superscriptshift),sup&&(u=Math.max(u,Sbox.d+.25*ex)),presup&&(u=Math.max(u,Pbox.d+.25*ex)),sup&&this.CHTMLplaceSupOnly(sup,Sbox,x,delta,u,s),presup&&this.CHTMLplacePresupOnly(presup,Pbox,delta,u,s);else v=Math.max(v,CHTML.TEX.sub1,values.subscriptshift),sub&&(v=Math.max(v,sbox.h-.8*ex)),presub&&(v=Math.max(v,pbox.h-.8*ex)),sub&&this.CHTMLplaceSubOnly(sub,sbox,x,v,s),presub&&this.CHTMLplacePresubOnly(presub,pbox,v,s);return this.CHTML.clean(),this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node},CHTMLgetScripts:function(BOX,BBOX,stretch,node){if(stretch)return BOX.sub=CHTML.getNode(node,"mjx-sub"),BOX.sup=CHTML.getNode(node,"mjx-sup"),BOX.presub=CHTML.getNode(node,"mjx-presub"),BOX.presup=CHTML.getNode(node,"mjx-presup"),BBOX.sub=this.CHTMLbbox.sub,BBOX.sup=this.CHTMLbbox.sup,BBOX.presub=this.CHTMLbbox.presub,void(BBOX.presup=this.CHTMLbbox.presup);this.CHTMLbbox=BBOX;for(var state={i:1,w:0,BOX:BOX,BBOX:BBOX},m=this.data.length,sub="sub",sup="sup";state.i<m;)if("mprescripts"===(this.data[state.i]||{}).type)state.i++,state.w=0,sub="presub",sup="presup";else{var sbox=this.CHTMLaddScript(sub,state,node),Sbox=this.CHTMLaddScript(sup,state,node),w=Math.max(sbox?sbox.rscale*sbox.w:0,Sbox?Sbox.rscale*Sbox.w:0);this.CHTMLpadScript(sub,w,sbox,state),this.CHTMLpadScript(sup,w,Sbox,state),state.w+=w}BBOX.sub&&BBOX.sub.clean(),BBOX.sup&&BBOX.sup.clean(),BBOX.presub&&BBOX.presub.clean(),BBOX.presup&&BBOX.presup.clean()},CHTMLaddScript:function(type,state,node){var BOX,BBOX,data=this.data[state.i];return data&&"none"!==data.type&&"mprescripts"!==data.type&&((BOX=state.BOX[type])||(BOX=state.BOX[type]=CHTML.addElement(node,"mjx-"+type),BBOX=state.BBOX[type]=CHTML.BBOX.empty(),state.w&&(BOX.style.paddingLeft=CHTML.Em(state.w),BBOX.w=BBOX.r=state.w,BBOX.x=state.w)),data.toCommonHTML(BOX),BBOX=data.CHTML),data&&"mprescripts"!==data.type&&state.i++,BBOX},CHTMLpadScript:function(type,w,bbox,state){bbox||(bbox={w:0,fake:1,rscale:1});var BBOX=state.BBOX[type],dx=0,dw=0;if(BBOX){if(bbox.rscale*bbox.w<w){var BOX=state.BOX[type];dw=w-bbox.rscale*bbox.w;var space=CHTML.Element("mjx-spacer",{style:{width:CHTML.Em(dw)}});"pre"!==type.substr(0,3)||bbox.fake?BOX.appendChild(space):(BOX.insertBefore(space,BOX.lastChild),dx=dw,dw=0)}bbox.fake?BBOX.w+=dx:BBOX.combine(bbox,BBOX.w+dx,0),BBOX.w+=dw}},CHTMLaddBoxes:function(node,base,BOX){var sub=BOX.sub,sup=BOX.sup,presub=BOX.presub,presup=BOX.presup;if(presub&&presup){var prestack=CHTML.Element("mjx-prestack");node.insertBefore(prestack,base),prestack.appendChild(presup),prestack.appendChild(presub)}else presub&&node.insertBefore(presub,base),presup&&node.insertBefore(presup,base);if(sub&&sup){var stack=CHTML.addElement(node,"mjx-stack");stack.appendChild(sup),stack.appendChild(sub)}else sub&&node.appendChild(sub),sup&&node.appendChild(sup)},CHTMLplaceSubOnly:function(sub,sbox,x,v,s){sub.style.verticalAlign=CHTML.Em(-v),sub.style.marginRight=CHTML.Em(s),sbox.w+=s,this.CHTML.combine(sbox,x,-v)},CHTMLplaceSupOnly:function(sup,Sbox,x,delta,u,s){sup.style.verticalAlign=CHTML.Em(u),sup.style.paddingLeft=CHTML.Em(delta),sup.style.paddingRight=CHTML.Em(s),Sbox.w+=s,this.CHTML.combine(Sbox,x+delta,u)},CHTMLplaceSubSup:function(sub,sbox,sup,Sbox,x,delta,u,v,s){sub.style.paddingRight=CHTML.Em(s),sbox.w+=s,sup.style.paddingBottom=CHTML.Em(u+v-Sbox.d-sbox.h),sup.style.paddingLeft=CHTML.Em(delta+(Sbox.x||0)),sup.style.paddingRight=CHTML.Em(s),Sbox.w+=s,sup.parentNode.style.verticalAlign=CHTML.Em(-v),this.CHTML.combine(sbox,x,-v),this.CHTML.combine(Sbox,x+delta,u)},CHTMLplacePresubOnly:function(presub,pbox,v,s){presub.style.verticalAlign=CHTML.Em(-v),presub.style.marginLeft=CHTML.Em(s),this.CHTML.combine(pbox,s,-v)},CHTMLplacePresupOnly:function(presup,Pbox,delta,u,s){presup.style.verticalAlign=CHTML.Em(u),presup.style.paddingLeft=CHTML.Em(s),presup.style.paddingRight=CHTML.Em(-delta),this.CHTML.combine(Pbox,s,u)},CHTMLplacePresubPresup:function(presub,pbox,presup,Pbox,delta,u,v,s){presub.style.paddingLeft=CHTML.Em(s),presup.style.paddingBottom=CHTML.Em(u+v-Pbox.d-pbox.h),presup.style.paddingLeft=CHTML.Em(delta+s+(Pbox.x||0)),presup.style.paddingRight=CHTML.Em(-delta),presup.parentNode.style.verticalAlign=CHTML.Em(-v),this.CHTML.combine(pbox,s,-v),this.CHTML.combine(Pbox,s+delta,u)},CHTMLstretchH:MML.mbase.CHTMLstretchH,CHTMLstretchV:MML.mbase.CHTMLstretchV}),MathJax.Hub.Startup.signal.Post("CommonHTML mmultiscripts Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/mmultiscripts.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CHTML=MathJax.OutputJax.CommonHTML;MML.ms.Augment({toCommonHTML:function(node){node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLgetVariant(),this.CHTMLhandleScale(node),CHTML.BBOX.empty(this.CHTML);var values=this.getValues("lquote","rquote","mathvariant");this.hasValue("lquote")&&'"'!==values.lquote||(values.lquote="“"),this.hasValue("rquote")&&'"'!==values.rquote||(values.rquote="”"),"“"===values.lquote&&"monospace"===values.mathvariant&&(values.lquote='"'),"”"===values.rquote&&"monospace"===values.mathvariant&&(values.rquote='"');var text=values.lquote+this.data.join("")+values.rquote;return this.CHTMLhandleText(node,text,this.CHTMLvariant),this.CHTML.clean(),this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node}}),MathJax.Hub.Startup.signal.Post("CommonHTML ms Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/ms.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CONFIG=MathJax.Hub.config,CHTML=MathJax.OutputJax.CommonHTML,SPLIT=MathJax.Hub.SplitList,LABEL=-1,BIGDIMEN=1e6;MML.mtable.Augment({toCommonHTML:function(node){var state={rows:[],labels:[],labeled:!1};node=this.CHTMLdefaultNode(node,{noBBox:!0,childOptions:state});for(var table=CHTML.Element("mjx-table");node.firstChild;)table.appendChild(node.firstChild);node.appendChild(table);var values=this.getValues("columnalign","rowalign","columnspacing","rowspacing","columnwidth","equalcolumns","equalrows","columnlines","rowlines","frame","framespacing","align","width","side","minlabelspacing","useHeight"),t=CHTML.TEX.min_rule_thickness/CHTML.em;state.t=CHTML.Px(t*this.CHTML.scale,1),this.CHTMLgetBoxSizes(values,state),this.CHTMLgetAttributes(values,state),this.CHTMLadjustCells(values,state),values.frame&&(table.style.border=state.t+" "+values.frame),this.CHTMLalignV(values,state,node),this.CHTMLcolumnWidths(values,state,node),this.CHTMLstretchCells(values,state),state.labeled&&this.CHTMLaddLabels(values,state,node,table);var BBOX=this.CHTML;return BBOX.w=BBOX.r=state.R,BBOX.h=BBOX.t=state.T-state.B,BBOX.d=BBOX.b=state.B,values.frame||BBOX.pwidth||(node.style.padding="0 "+CHTML.Em(1/6),BBOX.L=BBOX.R=1/6),this.CHTMLhandleSpace(node),this.CHTMLhandleBBox(node),this.CHTMLhandleColor(node),node},CHTMLgetBoxSizes:function(values,state){var LH=CHTML.FONTDATA.lineH*values.useHeight,LD=CHTML.FONTDATA.lineD*values.useHeight,H=[],D=[],W=[],J=-1,i,m;for(i=0,m=this.data.length;i<m;i++){var row=this.data[i],s="mtr"===row.type?0:-1;H[i]=LH,D[i]=LD;for(var j=s,M=row.data.length+s;j<M;j++){null==W[j]&&(W[j]=-1e6,j>J&&(J=j));var cbox=row.data[j-s].CHTML;cbox.h>H[i]&&(H[i]=cbox.h),cbox.d>D[i]&&(D[i]=cbox.d),cbox.w>W[j]&&(W[j]=cbox.w)}}if(values.equalrows){state.HD=!0;var HH=Math.max.apply(Math,H),DD=Math.max.apply(Math,D);for(i=0,m=H.length;i<m;i++)H[i]=HH,D[i]=DD}state.H=H,state.D=D,state.W=W,state.J=J},CHTMLgetAttributes:function(values,state){var CSPACE=SPLIT(values.columnspacing),RSPACE=SPLIT(values.rowspacing),CALIGN=SPLIT(values.columnalign),RALIGN=SPLIT(values.rowalign),CLINES=SPLIT(values.columnlines),RLINES=SPLIT(values.rowlines),CWIDTH=SPLIT(values.columnwidth),RCALIGN=[],i,m,J=state.J,M=state.rows.length-1;for(i=0,m=CSPACE.length;i<m;i++)CSPACE[i]=this.CHTMLlength2em(CSPACE[i]);for(i=0,m=RSPACE.length;i<m;i++)RSPACE[i]=this.CHTMLlength2em(RSPACE[i]);for(;CSPACE.length<J;)CSPACE.push(CSPACE[CSPACE.length-1]);for(;CALIGN.length<=J;)CALIGN.push(CALIGN[CALIGN.length-1]);for(;CLINES.length<J;)CLINES.push(CLINES[CLINES.length-1]);for(;CWIDTH.length<=J;)CWIDTH.push(CWIDTH[CWIDTH.length-1]);for(;RSPACE.length<M;)RSPACE.push(RSPACE[RSPACE.length-1]);for(;RALIGN.length<=M;)RALIGN.push(RALIGN[RALIGN.length-1]);for(;RLINES.length<M;)RLINES.push(RLINES[RLINES.length-1]);for(CALIGN[-1]="l"===values.side.substr(0,1)?"left":"right",i=0;i<=M;i++){var row=this.data[i];if(RCALIGN[i]=[],row.rowalign&&(RALIGN[i]=row.rowalign),row.columnalign)for(RCALIGN[i]=SPLIT(row.columnalign);RCALIGN[i].length<=J;)RCALIGN[i].push(RCALIGN[i][RCALIGN[i].length-1])}var FSPACE=SPLIT(values.framespacing);2!=FSPACE.length&&(FSPACE=SPLIT(this.defaults.framespacing)),FSPACE[0]=Math.max(0,this.CHTMLlength2em(FSPACE[0])),FSPACE[1]=Math.max(0,this.CHTMLlength2em(FSPACE[1])),""===values.columnlines.replace(/none/g,"").replace(/ /g,"")&&""===values.rowlines.replace(/none/g,"").replace(/ /g,"")||(values.fspace=!0),values.frame===MML.LINES.NONE?delete values.frame:values.fspace=!0,values.frame&&(FSPACE[0]=Math.max(0,FSPACE[0]),FSPACE[1]=Math.max(0,FSPACE[1])),values.fspace?(CSPACE[J]=FSPACE[0],RSPACE[M]=FSPACE[1]):CSPACE[J]=RSPACE[M]=0,CLINES[J]=RLINES[M]=MML.LINES.NONE,state.CSPACE=CSPACE,state.RSPACE=RSPACE,state.CALIGN=CALIGN,state.RALIGN=RALIGN,state.CLINES=CLINES,state.RLINES=RLINES,state.CWIDTH=CWIDTH,state.RCALIGN=RCALIGN,state.FSPACE=FSPACE},CHTMLadjustCells:function(values,state){var ROWS=state.rows,CSPACE=state.CSPACE,CLINES=state.CLINES,RSPACE=state.RSPACE,RLINES=state.RLINES,CALIGN=state.CALIGN,RALIGN=state.RALIGN,RCALIGN=state.RCALIGN;CSPACE[state.J]*=2,RSPACE[ROWS.length-1]*=2;var T="0",B,R,L,border,cbox,align,lastB=0;values.fspace&&(lastB=state.FSPACE[1],T=CHTML.Em(state.FSPACE[1])),state.RHD=[],state.RH=[];for(var i=0,m=ROWS.length;i<m;i++){var row=ROWS[i],rdata=this.data[i];if(B=RSPACE[i]/2,border=null,L="0",RLINES[i]!==MML.LINES.NONE&&""!==RLINES[i]&&(border=state.t+" "+RLINES[i]),border||CLINES[j]!==MML.LINES.NONE&&""!==CLINES[j])for(;row.length<=state.J;)row.push(CHTML.addElement(row.node,"mjx-mtd",null,[["span"]]));state.RH[i]=lastB+state.H[i],lastB=Math.max(0,B),state.RHD[i]=state.RH[i]+lastB+state.D[i],B=CHTML.Em(lastB),values.fspace&&(L=CHTML.Em(state.FSPACE[0]));for(var j=0,M=row.length;j<M;j++){var s="mtr"===rdata.type?0:-1,mtd=rdata.data[j-s]||{CHTML:CHTML.BBOX.zero()},cell=row[j].style;cbox=mtd.CHTML,R=CSPACE[j]/2,CLINES[j]!==MML.LINES.NONE&&(cell.borderRight=state.t+" "+CLINES[j],R-=1/CHTML.em/2),R=CHTML.Em(Math.max(0,R)),cell.padding=T+" "+R+" 0px "+L,border&&(cell.borderBottom=border),L=R,align=mtd.rowalign||(this.data[i]||{}).rowalign||RALIGN[i];var H=Math.max(1,cbox.h),D=Math.max(.2,cbox.d),HD=state.H[i]+state.D[i]-(H+D),child=row[j].firstChild.style;align===MML.ALIGN.TOP?(HD&&(child.marginBottom=CHTML.Em(HD)),cell.verticalAlign="top"):align===MML.ALIGN.BOTTOM?(cell.verticalAlign="bottom",HD&&(child.marginTop=CHTML.Em(HD))):align===MML.ALIGN.CENTER?(HD&&(child.marginTop=child.marginBottom=CHTML.Em(HD/2)),cell.verticalAlign="middle"):H!==state.H[i]&&(child.marginTop=CHTML.Em(state.H[i]-H)),(align=mtd.columnalign||RCALIGN[i][j]||CALIGN[j])!==MML.ALIGN.CENTER&&(cell.textAlign=align)}row.node.style.height=CHTML.Em(state.RHD[i]),T=B}CSPACE[state.J]/=2,RSPACE[ROWS.length-1]/=2},CHTMLalignV:function(values,state,node){var n,M=state.rows.length,H=state.H,D=state.D,RSPACE=state.RSPACE;"string"!=typeof values.align&&(values.align=String(values.align)),values.align.match(/(top|bottom|center|baseline|axis)( +(-?\d+))?/)?(n=parseInt(RegExp.$3||"0"),values.align=RegExp.$1,n<0&&(n+=state.rows.length+1),(n>M||n<=0)&&(n=null)):values.align=this.defaults.align;var T=0,B=0,a=CHTML.TEX.axis_height;values.fspace&&(T+=state.FSPACE[1]),values.frame&&(T+=2/CHTML.em,B+=1/CHTML.em);for(var i=0;i<M;i++){var h=H[i],d=D[i];T+=h+d+RSPACE[i],n&&(i===n-1&&(B+={top:h+d,bottom:0,center:(h+d)/2,baseline:d,axis:a+d}[values.align]+RSPACE[i]),i>=n&&(B+=h+d+RSPACE[i]))}n||(B={top:T,bottom:0,center:T/2,baseline:T/2,axis:T/2-a}[values.align]),B&&(node.style.verticalAlign=CHTML.Em(-B)),state.T=T,state.B=B},CHTMLcolumnWidths:function(values,state,node){var CWIDTH=state.CWIDTH,CSPACE=state.CSPACE,J=state.J,j,WW=0,setWidths=!1,relWidth=values.width.match(/%$/),i,m,w;if("auto"===values.width||relWidth||(WW=Math.max(0,this.CHTMLlength2em(values.width,state.R)),setWidths=!0),values.equalcolumns){if(relWidth){var p=CHTML.Percent(1/(J+1));for(j=0;j<=J;j++)CWIDTH[j]=p}else{if(w=Math.max.apply(Math,state.W),"auto"!==values.width){var S=values.fspace?state.FSPACE[0]+(values.frame?2/CHTML.em:0):0;for(j=0;j<=J;j++)S+=CSPACE[j];w=Math.max((WW-S)/(J+1),w)}for(w=CHTML.Em(w),j=0;j<=J;j++)CWIDTH[j]=w}setWidths=!0}var TW=0;values.fspace&&(TW=state.FSPACE[0]);var auto=[],fit=[],percent=[],W=[],row=state.rows[0];for(j=0;j<=J;j++)W[j]=state.W[j],"auto"===CWIDTH[j]?auto.push(j):"fit"===CWIDTH[j]?fit.push(j):CWIDTH[j].match(/%$/)?percent.push(j):W[j]=this.CHTMLlength2em(CWIDTH[j],W[j]),TW+=W[j]+CSPACE[j],row[j]&&(row[j].style.width=CHTML.Em(W[j]));values.frame&&(TW+=2/CHTML.em);var hasFit=fit.length>0;if(setWidths)if(relWidth)for(j=0;j<=J;j++)cell=row[j].style,"auto"!==CWIDTH[j]||hasFit?"fit"===CWIDTH[j]?cell.width="":CWIDTH[j].match(/%$/)?cell.width=CWIDTH[j]:cell.minWidth=cell.maxWidth=cell.width:cell.width="";else{if(WW>TW){var extra=0;for(i=0,m=percent.length;i<m;i++)j=percent[i],extra+=(w=Math.max(W[j],this.CHTMLlength2em(CWIDTH[j],WW)))-W[j],W[j]=w,row[j].style.width=CHTML.Em(w);TW+=extra}if(hasFit||(fit=auto),WW>TW&&fit.length){var dw=(WW-TW)/fit.length;for(i=0,m=fit.length;i<m;i++)W[j=fit[i]]+=dw,row[j].style.width=CHTML.Em(W[j]);TW=WW}}W[-1]=state.W[-1],state.W=W,state.R=TW,relWidth&&(node.style.width=this.CHTML.pwidth="100%",this.CHTML.mwidth=CHTML.Em(TW),node.firstChild.style.width=values.width,node.firstChild.style.margin="auto")},CHTMLstretchCells:function(values,state){for(var ROWS=state.rows,H=state.H,D=state.D,W=state.W,J=state.J,M=ROWS.length-1,i=0;i<=M;i++)for(var row=ROWS[i],rdata=this.data[i],h=H[i],d=D[i],j=0;j<=J;j++){var cell=row[j],cdata=rdata.data[j];cdata&&("V"===cdata.CHTML.stretch?cdata.CHTMLstretchV(h,d):"H"===cdata.CHTML.stretch&&cdata.CHTMLstretchH(cell,W[j]))}},CHTMLaddLabels:function(values,state,node,table){var indent=this.getValues("indentalignfirst","indentshiftfirst","indentalign","indentshift");indent.indentalignfirst!==MML.INDENTALIGN.INDENTALIGN&&(indent.indentalign=indent.indentalignfirst),indent.indentalign===MML.INDENTALIGN.AUTO&&(indent.indentalign=CONFIG.displayAlign),indent.indentshiftfirst!==MML.INDENTSHIFT.INDENTSHIFT&&(indent.indentshift=indent.indentshiftfirst),"auto"===indent.indentshift&&(indent.indentshift="0");var shift=this.CHTMLlength2em(indent.indentshift,CHTML.cwidth),labelspace,labelW=this.CHTMLlength2em(values.minlabelspacing,.8)+state.W[-1],labelshift=0,tw=state.R,dIndent=this.CHTMLlength2em(CONFIG.displayIndent,CHTML.cwidth),s=state.CALIGN[-1]===MML.INDENTALIGN.RIGHT?-1:1;indent.indentalign===MML.INDENTALIGN.CENTER?(tw+=2*(labelW-s*(shift+dIndent)),shift+=dIndent):state.CALIGN[-1]===indent.indentalign?(dIndent<0&&(labelshift=s*dIndent,dIndent=0),labelW>s*(shift+=s*dIndent)&&(shift=s*labelW),shift+=labelshift,tw+=shift*=s):(tw+=labelW-s*shift+dIndent,shift-=s*dIndent,shift*=-s);var box=CHTML.addElement(node,"mjx-box",{style:{width:"100%","text-align":indent.indentalign}});box.appendChild(table);var labels=CHTML.Element("mjx-itable");if(table.style.display="inline-table",table.style.width||(table.style.width="auto"),labels.style.verticalAlign="top",table.style.verticalAlign=CHTML.Em(state.T-state.B-state.H[0]),node.style.verticalAlign="",shift)if(indent.indentalign===MML.INDENTALIGN.CENTER)table.style.marginLeft=CHTML.Em(shift),table.style.marginRight=CHTML.Em(-shift);else{var margin="margin"+(indent.indentalign===MML.INDENTALIGN.RIGHT?"Right":"Left");table.style[margin]=CHTML.Em(shift)}"left"===state.CALIGN[-1]?(node.insertBefore(labels,box),labels.style.marginRight=CHTML.Em(-state.W[-1]-labelshift),labelshift&&(labels.style.marginLeft=CHTML.Em(labelshift))):(node.appendChild(labels),labels.style.marginLeft=CHTML.Em(-state.W[-1]+labelshift));var LABELS=state.labels,T=0;values.fspace&&(T=state.FSPACE[0]+(values.frame?1/CHTML.em:0));for(var i=0,m=LABELS.length;i<m;i++)if(LABELS[i]&&this.data[i].data[0]){labels.appendChild(LABELS[i]);var lbox=this.data[i].data[0].CHTML;(T=state.RH[i]-Math.max(1,lbox.h))&&(LABELS[i].firstChild.firstChild.style.marginTop=CHTML.Em(T)),LABELS[i].style.height=CHTML.Em(state.RHD[i])}else CHTML.addElement(labels,"mjx-label",{style:{height:CHTML.Em(state.RHD[i])}});node.style.width=this.CHTML.pwidth="100%",node.style.minWidth=this.CHTML.mwidth=CHTML.Em(Math.max(0,tw))}}),MML.mtr.Augment({toCommonHTML:function(node,options){node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLhandleScale(node),options||(options={rows:[],labels:[]});var row=[];options.rows.push(row),row.node=node,options.labels.push(null);for(var i=0,m=this.data.length;i<m;i++)row.push(this.CHTMLaddChild(node,i,options));return this.CHTMLhandleColor(node),node}}),MML.mlabeledtr.Augment({toCommonHTML:function(node,options){node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLhandleScale(node),options||(options={rows:[],labels:[]});var row=[];options.rows.push(row),row.node=node;var label=CHTML.Element("mjx-label");options.labels.push(label),this.CHTMLaddChild(label,0,options),this.data[0]&&(options.labeled=!0);for(var i=1,m=this.data.length;i<m;i++)row.push(this.CHTMLaddChild(node,i,options));return this.CHTMLhandleColor(node),node}}),MML.mtd.Augment({toCommonHTML:function(node,options){if(node=this.CHTMLdefaultNode(node,options),CHTML.addElement(node.firstChild,"mjx-strut"),this.isEmbellished()){var mo=this.CoreMO(),BBOX=this.CHTML;if(mo.CHTMLcanStretch("Vertical")?BBOX.stretch="V":mo.CHTMLcanStretch("Horizontal")&&(BBOX.stretch="H"),BBOX.stretch){var min=mo.Get("minsize",!0);if(min)if("V"===BBOX.stretch){var HD=BBOX.h+BBOX.d;if(HD){var r=this.CHTMLlength2em(min,HD)/HD;r>1&&(BBOX.h*=r,BBOX.d*=r)}}else BBOX.w=Math.max(BBOX.w,this.CHTMLlength2em(min,BBOX.w))}}return node}}),MathJax.Hub.Startup.signal.Post("CommonHTML mtable Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/mtable.js")})),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,CONFIG=MathJax.Hub.config,CHTML=MathJax.OutputJax.CommonHTML,MO=MML.mo().With({CHTML:CHTML.BBOX.empty()}),PENALTY={newline:0,nobreak:1e6,goodbreak:[-200],badbreak:[200],auto:[0],maxwidth:1.33,toobig:800,nestfactor:400,spacefactor:-100,spaceoffset:2,spacelimit:1,fence:500,close:500},ENDVALUES={linebreakstyle:"after"};MML.mbase.Augment({CHTMLlinebreakPenalty:PENALTY,CHTMLmultiline:function(node){for(var parent=this;parent.inferred||parent.parent&&"mrow"===parent.parent.type&&parent.parent.isEmbellished();)parent=parent.parent;var isTop="math"===parent.type&&"block"===parent.Get("display")||"mtd"===parent.type;parent.isMultiline=!0;var VALUES=this.getValues("linebreak","linebreakstyle","lineleading","linebreakmultchar","indentalign","indentshift","indentalignfirst","indentshiftfirst","indentalignlast","indentshiftlast");VALUES.linebreakstyle===MML.LINEBREAKSTYLE.INFIXLINEBREAKSTYLE&&(VALUES.linebreakstyle=this.Get("infixlinebreakstyle")),VALUES.lineleading=this.CHTMLlength2em(VALUES.lineleading,.5),CHTML.BBOX.empty(this.CHTML);for(var stack=CHTML.addElement(node,"mjx-stack"),state={BBOX:this.CHTML,n:0,Y:0,scale:this.CHTML.scale||1,isTop:isTop,values:{},VALUES:VALUES},align=this.CHTMLgetAlign(state,{}),shift=this.CHTMLgetShift(state,{},align),start=[],end={index:[],penalty:PENALTY.nobreak,w:0,W:shift,shift:shift,scanW:shift,nest:0},broken=!1;this.CHTMLbetterBreak(end,state,!0)&&(end.scanW>=CHTML.linebreakWidth||end.penalty===PENALTY.newline);)this.CHTMLaddLine(stack,start,end.index,state,end.values,broken),start=end.index.slice(0),broken=!0,align=this.CHTMLgetAlign(state,end.values),shift=this.CHTMLgetShift(state,end.values,align),end.W=end.shift=end.scanW=shift,end.penalty=PENALTY.nobreak;return state.isLast=!0,this.CHTMLaddLine(stack,start,[],state,ENDVALUES,broken),node.style.width=stack.style.width=this.CHTML.pwidth="100%",this.CHTML.mwidth=CHTML.Em(this.CHTML.w),this.CHTML.isMultiline=parent.CHTML.isMultiline=!0,stack.style.verticalAlign=CHTML.Em(state.d-this.CHTML.d),node},CHTMLbetterBreak:function(info,state,toplevel){if(this.isToken)return!1;if(this.isEmbellished())return info.embellished=this,this.CoreMO().CHTMLbetterBreak(info,state);if(this.linebreakContainer)return!1;var index=info.index.slice(0),i=info.index.shift(),m=this.data.length,W,w,scanW,broken=info.index.length>0,better=!1;for(null==i&&(i=-1),broken||(i++,info.W+=info.w,info.w=0),scanW=info.scanW=info.W,info.nest++;i<m&&(info.scanW<PENALTY.maxwidth*CHTML.linebreakWidth||0===info.w);){if(this.data[i]){if(this.data[i].CHTMLbetterBreak(info,state)&&(better=!0,index=[i].concat(info.index),W=info.W,w=info.w,info.penalty===PENALTY.newline))return info.index=index,info.nest&&info.nest--,!0;scanW=broken?info.scanW:this.CHTMLaddWidth(i,info,scanW)}info.index=[],i++,broken=!1}return toplevel&&better&&(MO.parent=this.parent,MO.inherit=this.inherit,MO.CHTMLbetterBreak(info,state)&&(better=!1,index=info.index)),info.nest&&info.nest--,info.index=index,better&&(info.W=W,info.w=w),better},CHTMLaddWidth:function(i,info,scanW){if(this.data[i]){var bbox=this.data[i].CHTML;scanW+=(bbox.w+(bbox.L||0)+(bbox.R||0))*(bbox.scale||1),info.W=info.scanW=scanW,info.w=0}return scanW},CHTMLaddLine:function(stack,start,end,state,values,broken){var block=CHTML.addElement(stack,"mjx-block",{},[["mjx-box"]]),line=block.firstChild,bbox=state.bbox=CHTML.BBOX.empty();state.first=broken,state.last=!0,this.CHTMLmoveLine(start,end,line,state,values),bbox.clean();var align=this.CHTMLgetAlign(state,values),shift=this.CHTMLgetShift(state,values,align,!0),dY=0;if(state.n>0){var LHD=CHTML.FONTDATA.baselineskip,leading=(null==state.values.lineleading?state.VALUES:state.values).lineleading*state.scale,Y=state.Y;state.Y-=Math.max(LHD,state.d+bbox.h+leading),dY=Y-state.Y-state.d-bbox.h}shift&&(line.style.margin="0 "+CHTML.Em(-shift)+" 0 "+CHTML.Em(shift)),align!==MML.INDENTALIGN.LEFT&&(block.style.textAlign=align),dY&&(block.style.paddingTop=CHTML.Em(dY)),state.BBOX.combine(bbox,shift,state.Y),state.d=state.bbox.d,state.values=values,state.n++},CHTMLgetAlign:function(state,values){var cur=values,prev=state.values,def=state.VALUES,align;return(align=0===state.n?cur.indentalignfirst||prev.indentalignfirst||def.indentalignfirst:state.isLast?prev.indentalignlast||def.indentalignlast:prev.indentalign||def.indentalign)===MML.INDENTALIGN.INDENTALIGN&&(align=prev.indentalign||def.indentalign),align===MML.INDENTALIGN.AUTO&&(align=state.isTop?CONFIG.displayAlign:MML.INDENTALIGN.LEFT),align},CHTMLgetShift:function(state,values,align,noadjust){var cur=values,prev=state.values,def=state.VALUES,shift;if((shift=0===state.n?cur.indentshiftfirst||prev.indentshiftfirst||def.indentshiftfirst:state.isLast?prev.indentshiftlast||def.indentshiftlast:prev.indentshift||def.indentshift)===MML.INDENTSHIFT.INDENTSHIFT&&(shift=prev.indentshift||def.indentshift),"auto"!==shift&&""!==shift||(shift="0"),shift=this.CHTMLlength2em(shift,CHTML.cwidth),state.isTop&&"0"!==CONFIG.displayIndent){var indent=this.CHTMLlength2em(CONFIG.displayIndent,CHTML.cwidth);shift+=align===MML.INDENTALIGN.RIGHT?-indent:indent}return align!==MML.INDENTALIGN.RIGHT||noadjust?shift:-shift},CHTMLmoveLine:function(start,end,node,state,values){var i=start[0],j=end[0];if(null==i&&(i=-1),null==j&&(j=this.data.length-1),i===j&&start.length>1)this.data[i].CHTMLmoveSlice(start.slice(1),end.slice(1),node,state,values,"marginLeft");else{var last=state.last;for(state.last=!1;i<j;)this.data[i]&&(start.length<=1?this.data[i].CHTMLmoveNode(node,state,values):this.data[i].CHTMLmoveSlice(start.slice(1),[],node,state,values,"marginLeft")),i++,state.first=!1,start=[];state.last=last,this.data[i]&&(end.length<=1?this.data[i].CHTMLmoveNode(node,state,values):this.data[i].CHTMLmoveSlice([],end.slice(1),node,state,values,"marginRight"))}},CHTMLmoveSlice:function(start,end,node,state,values,margin){var slice=this.CHTMLcreateSliceNode(node);return this.CHTMLmoveLine(start,end,slice,state,values),slice.style[margin]&&(slice.style[margin]=""),this.CHTML.L&&("marginLeft"!==margin?state.bbox.w+=this.CHTML.L:slice.className=slice.className.replace(/ MJXc-space\d/,"")),this.CHTML.R&&"marginRight"!==margin&&(state.bbox.w+=this.CHTML.R),0===end.length&&(node=this.CHTMLnodeElement(),this.href&&(node=node.parentNode),node.parentNode.removeChild(node),node.nextMathJaxNode.id=node.id),slice},CHTMLcreateSliceNode:function(node){var NODE=this.CHTMLnodeElement(),n=0;this.href&&(NODE=NODE.parentNode);for(var LAST=NODE;LAST.nextMathJaxNode;)LAST=LAST.nextMathJaxNode,n++;var SLICE=NODE.cloneNode(!1);return LAST.nextMathJaxNode=SLICE,SLICE.nextMathJaxNode=null,SLICE.id+="-MJX-Continue-"+n,node.appendChild(SLICE)},CHTMLmoveNode:function(line,state,values){if(!state.first&&!state.last||state.first&&state.values.linebreakstyle===MML.LINEBREAKSTYLE.BEFORE||state.last&&values.linebreakstyle===MML.LINEBREAKSTYLE.AFTER){var node=this.CHTMLnodeElement();this.href&&(node=node.parentNode),line.appendChild(node),this.CHTML.pwidth&&!line.style.width&&(line.style.width=this.CHTML.pwidth),state.last&&(node.style.marginRight=""),(state.first||state.nextIsFirst)&&(node.style.marginLeft="",this.CHTML.L=0,node.className=node.className.replace(/ MJXc-space\d/,"")),state.first&&0===this.CHTML.w?state.nextIsFirst=!0:delete state.nextIsFirst,state.bbox.combine(this.CHTML,state.bbox.w,0)}}}),MML.mfenced.Augment({CHTMLbetterBreak:function(info,state){var index=info.index.slice(0),i=info.index.shift(),m=this.data.length,W,w,scanW,broken=info.index.length>0,better=!1;if(null==i&&(i=-1),broken||(i++,info.W+=info.w,info.w=0),scanW=info.scanW=info.W,info.nest++,!this.dataI){this.dataI=[],this.data.open&&this.dataI.push("open"),m&&this.dataI.push(0);for(var j=1;j<m;j++)this.data["sep"+j]&&this.dataI.push("sep"+j),this.dataI.push(j);this.data.close&&this.dataI.push("close")}for(m=this.dataI.length;i<m&&(info.scanW<PENALTY.maxwidth*CHTML.linebreakWidth||0===info.w);){var k=this.dataI[i];if(this.data[k]){if(this.data[k].CHTMLbetterBreak(info,state)&&(better=!0,index=[i].concat(info.index),W=info.W,w=info.w,info.penalty===PENALTY.newline))return info.index=index,info.nest&&info.nest--,!0;scanW=broken?info.scanW:this.CHTMLaddWidth(i,info,scanW)}info.index=[],i++,broken=!1}return info.nest&&info.nest--,info.index=index,better&&(info.W=W,info.w=w),better},CHTMLmoveLine:function(start,end,node,state,values){var i=start[0],j=end[0];if(null==i&&(i=-1),null==j&&(j=this.dataI.length-1),i===j&&start.length>1)this.data[this.dataI[i]].CHTMLmoveSlice(start.slice(1),end.slice(1),node,state,values,"marginLeft");else{var last=state.last;state.last=!1;for(var k=this.dataI[i];i<j;)this.data[k]&&(start.length<=1?this.data[k].CHTMLmoveNode(node,state,values):this.data[k].CHTMLmoveSlice(start.slice(1),[],node,state,values,"marginLeft")),i++,k=this.dataI[i],state.first=!1,start=[];state.last=last,this.data[k]&&(end.length<=1?this.data[k].CHTMLmoveNode(node,state,values):this.data[k].CHTMLmoveSlice([],end.slice(1),node,state,values,"marginRight"))}}}),MML.msubsup.Augment({CHTMLbetterBreak:function(info,state){if(!this.data[this.base])return!1;var index=info.index.slice(0),i=info.index.shift(),W,w,scanW,broken=info.index.length>0,better=!1;return broken||(info.W+=info.w,info.w=0),scanW=info.scanW=info.W,null==i&&(this.CHTML.baseW=this.data[this.base].CHTML.w,this.CHTML.dw=this.CHTML.w-this.CHTML.baseW),this.data[this.base].CHTMLbetterBreak(info,state)&&(better=!0,index=[this.base].concat(info.index),W=info.W,w=info.w,info.penalty===PENALTY.newline&&(better=broken=!0)),broken||this.CHTMLaddWidth(this.base,info,scanW),info.scanW+=this.CHTML.dw,info.W=info.scanW,info.index=[],better&&(info.W=W,info.w=w,info.index=index),better},CHTMLmoveLine:function(start,end,node,state,values){if(this.data[this.base]){var base=CHTML.addElement(node,"mjx-base");start.length>1?this.data[this.base].CHTMLmoveSlice(start.slice(1),end.slice(1),base,state,values,"marginLeft"):end.length<=1?this.data[this.base].CHTMLmoveNode(base,state,values):this.data[this.base].CHTMLmoveSlice([],end.slice(1),base,state,values,"marginRight")}if(0===end.length){var NODE=this.CHTMLnodeElement(),stack=CHTML.getNode(NODE,"mjx-stack"),sup=CHTML.getNode(NODE,"mjx-sup"),sub=CHTML.getNode(NODE,"mjx-sub");stack?node.appendChild(stack):sup?node.appendChild(sup):sub&&node.appendChild(sub);var w=state.bbox.w,bbox;sup&&(bbox=this.data[this.sup].CHTML,state.bbox.combine(bbox,w,bbox.Y)),sub&&(bbox=this.data[this.sub].CHTML,state.bbox.combine(bbox,w,bbox.Y))}}}),MML.mmultiscripts.Augment({CHTMLbetterBreak:function(info,state){if(!this.data[this.base])return!1;var index=info.index.slice(0);info.index.shift();var W,w,scanW,broken=info.index.length>0,better=!1;broken||(info.W+=info.w,info.w=0),info.scanW=info.W;var bbox=this.CHTML,base=this.data[this.base].CHTML,dw=bbox.w-base.w-(bbox.X||0);return info.scanW+=bbox.X||0,scanW=info.scanW,this.data[this.base].CHTMLbetterBreak(info,state)&&(better=!0,index=[this.base].concat(info.index),W=info.W,w=info.w,info.penalty===PENALTY.newline&&(better=broken=!0)),broken||this.CHTMLaddWidth(this.base,info,scanW),info.scanW+=dw,info.W=info.scanW,info.index=[],better&&(info.W=W,info.w=w,info.index=index),better},CHTMLmoveLine:function(start,end,node,state,values){var NODE,BOX=this.CHTMLbbox,w;if(start.length<1){NODE=this.CHTMLnodeElement();var prestack=CHTML.getNode(NODE,"mjx-prestack"),presup=CHTML.getNode(NODE,"mjx-presup"),presub=CHTML.getNode(NODE,"mjx-presub");prestack?node.appendChild(prestack):presup?node.appendChild(presup):presub&&node.appendChild(presub),w=state.bbox.w,presup&&state.bbox.combine(BOX.presup,w+BOX.presup.X,BOX.presup.Y),presub&&state.bbox.combine(BOX.presub,w+BOX.presub.X,BOX.presub.Y)}if(this.data[this.base]){var base=CHTML.addElement(node,"mjx-base");start.length>1?this.data[this.base].CHTMLmoveSlice(start.slice(1),end.slice(1),base,state,values,"marginLeft"):end.length<=1?this.data[this.base].CHTMLmoveNode(base,state,values):this.data[this.base].CHTMLmoveSlice([],end.slice(1),base,state,values,"marginRight")}if(0===end.length){NODE=this.CHTMLnodeElement();var stack=CHTML.getNode(NODE,"mjx-stack"),sup=CHTML.getNode(NODE,"mjx-sup"),sub=CHTML.getNode(NODE,"mjx-sub");stack?node.appendChild(stack):sup?node.appendChild(sup):sub&&node.appendChild(sub),w=state.bbox.w,sup&&state.bbox.combine(BOX.sup,w,BOX.sup.Y),sub&&state.bbox.combine(BOX.sub,w,BOX.sub.Y)}}}),MML.mo.Augment({CHTMLbetterBreak:function(info,state){if(info.values&&info.values.id===this.CHTMLnodeID)return!1;var values=this.getValues("linebreak","linebreakstyle","lineleading","linebreakmultchar","indentalign","indentshift","indentalignfirst","indentshiftfirst","indentalignlast","indentshiftlast","texClass","fence");values.linebreakstyle===MML.LINEBREAKSTYLE.INFIXLINEBREAKSTYLE&&(values.linebreakstyle=this.Get("infixlinebreakstyle")),values.texClass===MML.TEXCLASS.OPEN&&info.nest++,values.texClass===MML.TEXCLASS.CLOSE&&info.nest&&info.nest--;var W=info.scanW;delete info.embellished;var w=this.CHTML.w+(this.CHTML.L||0)+(this.CHTML.R||0);if(values.linebreakstyle===MML.LINEBREAKSTYLE.AFTER&&(W+=w,w=0),W-info.shift==0&&values.linebreak!==MML.LINEBREAK.NEWLINE)return!1;var offset=CHTML.linebreakWidth-W;if(0===state.n&&(values.indentshiftfirst!==state.VALUES.indentshiftfirst||values.indentalignfirst!==state.VALUES.indentalignfirst)){var align=this.CHTMLgetAlign(state,values),shift=this.CHTMLgetShift(state,values,align);offset+=info.shift-shift}var penalty=Math.floor(offset/CHTML.linebreakWidth*1e3);penalty<0&&(penalty=PENALTY.toobig-3*penalty),values.fence&&(penalty+=PENALTY.fence),(values.linebreakstyle===MML.LINEBREAKSTYLE.AFTER&&values.texClass===MML.TEXCLASS.OPEN||values.texClass===MML.TEXCLASS.CLOSE)&&(penalty+=PENALTY.close),penalty+=info.nest*PENALTY.nestfactor;var linebreak=PENALTY[values.linebreak||MML.LINEBREAK.AUTO]||0;return MathJax.Object.isArray(linebreak)?penalty=Math.max(1,penalty+linebreak[0]*info.nest):(linebreak||offset>=0)&&(penalty=linebreak*info.nest),!(penalty>=info.penalty)&&(info.penalty=penalty,info.values=values,info.W=W,info.w=w,values.lineleading=this.CHTMLlength2em(values.lineleading,state.VALUES.lineleading),values.id=this.CHTMLnodeID,!0)}}),MML.mspace.Augment({CHTMLbetterBreak:function(info,state){if(info.values&&info.values.id===this.CHTMLnodeID)return!1;var values=this.getValues("linebreak"),linebreakValue=values.linebreak;linebreakValue&&!this.hasDimAttr()||(linebreakValue=MML.LINEBREAK.AUTO);var W=info.scanW,w=this.CHTML.w+(this.CHTML.L||0)+(this.CHTML.R||0);if(W-info.shift==0)return!1;var offset=CHTML.linebreakWidth-W,penalty=Math.floor(offset/CHTML.linebreakWidth*1e3);penalty<0&&(penalty=PENALTY.toobig-3*penalty),penalty+=info.nest*PENALTY.nestfactor;var linebreak=PENALTY[linebreakValue]||0;return linebreakValue===MML.LINEBREAK.AUTO&&w>=PENALTY.spacelimit&&!this.mathbackground&&!this.background&&(linebreak=[(w+PENALTY.spaceoffset)*PENALTY.spacefactor]),MathJax.Object.isArray(linebreak)?penalty=Math.max(1,penalty+linebreak[0]*info.nest):(linebreak||offset>=0)&&(penalty=linebreak*info.nest),!(penalty>=info.penalty)&&(info.penalty=penalty,info.values=values,info.W=W,info.w=w,values.lineleading=state.VALUES.lineleading,values.linebreakstyle="before",values.id=this.CHTMLnodeID,!0)}}),MathJax.Hub.Register.StartupHook("TeX mathchoice Ready",(function(){MML.TeXmathchoice.Augment({CHTMLbetterBreak:function(info,state){return this.Core().CHTMLbetterBreak(info,state)},CHTMLmoveLine:function(start,end,node,state,values){return this.Core().CHTMLmoveSlice(start,end,node,state,values)}})})),MML.maction.Augment({CHTMLbetterBreak:function(info,state){return this.Core().CHTMLbetterBreak(info,state)},CHTMLmoveLine:function(start,end,node,state,values){return this.Core().CHTMLmoveSlice(start,end,node,state,values)}}),MML.semantics.Augment({CHTMLbetterBreak:function(info,state){return!!this.data[0]&&this.data[0].CHTMLbetterBreak(info,state)},CHTMLmoveLine:function(start,end,node,state,values){return this.data[0]?this.data[0].CHTMLmoveSlice(start,end,node,state,values):null}}),MathJax.Hub.Startup.signal.Post("CommonHTML multiline Ready"),MathJax.Ajax.loadComplete(CHTML.autoloadDir+"/multiline.js")})),MathJax.Extension.tex2jax={version:"2.7.5",config:{inlineMath:[["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],skipTags:["script","noscript","style","textarea","pre","code","annotation","annotation-xml"],ignoreClass:"tex2jax_ignore",processClass:"tex2jax_process",processEscapes:!1,processEnvironments:!0,processRefs:!0,preview:"TeX"},ignoreTags:{br:MathJax.Hub.Browser.isMSIE&&document.documentMode<9?"\n":" ",wbr:"","#comment":""},PreProcess:function(element){this.configured||(this.config=MathJax.Hub.CombineConfig("tex2jax",this.config),this.config.Augment&&MathJax.Hub.Insert(this,this.config.Augment),void 0===this.config.previewTeX||this.config.previewTeX||(this.config.preview="none"),this.configured=!0),"string"==typeof element&&(element=document.getElementById(element)),element||(element=document.body),this.createPatterns()&&this.scanElement(element,element.nextSibling)},createPatterns:function(){var starts=[],parts=[],i,m,config=this.config;for(this.match={},i=0,m=config.inlineMath.length;i<m;i++)starts.push(this.patternQuote(config.inlineMath[i][0])),this.match[config.inlineMath[i][0]]={mode:"",end:config.inlineMath[i][1],pattern:this.endPattern(config.inlineMath[i][1])};for(i=0,m=config.displayMath.length;i<m;i++)starts.push(this.patternQuote(config.displayMath[i][0])),this.match[config.displayMath[i][0]]={mode:"; mode=display",end:config.displayMath[i][1],pattern:this.endPattern(config.displayMath[i][1])};starts.length&&parts.push(starts.sort(this.sortLength).join("|")),config.processEnvironments&&parts.push("\\\\begin\\{([^}]*)\\}"),config.processEscapes&&parts.push("\\\\*\\\\\\$"),config.processRefs&&parts.push("\\\\(eq)?ref\\{[^}]*\\}"),this.start=new RegExp(parts.join("|"),"g"),this.skipTags=new RegExp("^("+config.skipTags.join("|")+")$","i");var ignore=[];return MathJax.Hub.config.preRemoveClass&&ignore.push(MathJax.Hub.config.preRemoveClass),config.ignoreClass&&ignore.push(config.ignoreClass),this.ignoreClass=ignore.length?new RegExp("(^| )("+ignore.join("|")+")( |$)"):/^$/,this.processClass=new RegExp("(^| )("+config.processClass+")( |$)"),parts.length>0},patternQuote:function(s){return s.replace(/([\^$(){}+*?\-|\[\]\:\\])/g,"\\$1")},endPattern:function(end){return new RegExp(this.patternQuote(end)+"|\\\\.|[{}]","g")},sortLength:function(a,b){return a.length!==b.length?b.length-a.length:a==b?0:a<b?-1:1},scanElement:function(element,stop,ignore){for(var cname,tname,ignoreChild,process;element&&element!=stop;)"#text"===element.nodeName.toLowerCase()?ignore||(element=this.scanText(element)):(cname=void 0===element.className?"":element.className,tname=void 0===element.tagName?"":element.tagName,"string"!=typeof cname&&(cname=String(cname)),process=this.processClass.exec(cname),!element.firstChild||cname.match(/(^| )MathJax/)||!process&&this.skipTags.exec(tname)||(ignoreChild=(ignore||this.ignoreClass.exec(cname))&&!process,this.scanElement(element.firstChild,stop,ignoreChild))),element&&(element=element.nextSibling)},scanText:function(element){if(""==element.nodeValue.replace(/\s+/,""))return element;var match,prev,pos=0,rescan;for(this.search={start:!0},this.pattern=this.start;element;){for(rescan=null,this.pattern.lastIndex=pos,pos=0;element&&"#text"===element.nodeName.toLowerCase()&&(match=this.pattern.exec(element.nodeValue));)element=this.search.start?this.startMatch(match,element):this.endMatch(match,element);if(this.search.matched?element=this.encloseMath(element):this.search.start||(rescan=this.search),element){do{prev=element,element=element.nextSibling}while(element&&null!=this.ignoreTags[element.nodeName.toLowerCase()]);if(!element||"#text"!==element.nodeName){if(!rescan)return this.search.close?this.prevEndMatch():prev;element=rescan.open,pos=rescan.opos+rescan.olen+(rescan.blen||0),this.search={start:!0},this.pattern=this.start}}}return element},startMatch:function(match,element){var delim=this.match[match[0]];if(null!=delim)this.search={end:delim.end,mode:delim.mode,pcount:0,open:element,olen:match[0].length,opos:this.pattern.lastIndex-match[0].length},this.switchPattern(delim.pattern);else if("\\begin"===match[0].substr(0,6))this.search={end:"\\end{"+match[1]+"}",mode:"; mode=display",pcount:0,open:element,olen:0,opos:this.pattern.lastIndex-match[0].length,blen:match[1].length+3,isBeginEnd:!0},this.switchPattern(this.endPattern(this.search.end));else{if("\\ref"===match[0].substr(0,4)||"\\eqref"===match[0].substr(0,6))return this.search={mode:"",end:"",open:element,pcount:0,olen:0,opos:this.pattern.lastIndex-match[0].length},this.endMatch([""],element);var slashes=match[0].substr(0,match[0].length-1),n,span;slashes.length%2==0?(span=[slashes.replace(/\\\\/g,"\\")],n=1):(span=[slashes.substr(1).replace(/\\\\/g,"\\"),"$"],n=0),span=MathJax.HTML.Element("span",null,span);var text=MathJax.HTML.TextNode(element.nodeValue.substr(0,match.index));element.nodeValue=element.nodeValue.substr(match.index+match[0].length-n),element.parentNode.insertBefore(span,element),element.parentNode.insertBefore(text,span),this.pattern.lastIndex=n}return element},endMatch:function(match,element){var search=this.search;return match[0]==search.end?(search.close&&0!==search.pcount||(search.close=element,search.cpos=this.pattern.lastIndex,search.clen=search.isBeginEnd?0:match[0].length),0===search.pcount&&(search.matched=!0,element=this.encloseMath(element),this.switchPattern(this.start))):"{"===match[0]?search.pcount++:"}"===match[0]&&search.pcount&&search.pcount--,element},prevEndMatch:function(){this.search.matched=!0;var element=this.encloseMath(this.search.close);return this.switchPattern(this.start),element},switchPattern:function(pattern){pattern.lastIndex=this.pattern.lastIndex,this.pattern=pattern,this.search.start=pattern===this.start},encloseMath:function(element){var search=this.search,close=search.close,CLOSE,math,next;for((close=search.cpos===close.length?close.nextSibling:close.splitText(search.cpos))||(CLOSE=close=MathJax.HTML.addText(search.close.parentNode,"")),search.close=close,math=search.opos?search.open.splitText(search.opos):search.open;(next=math.nextSibling)&&next!==close;){if(null!==next.nodeValue)"#comment"===next.nodeName?math.nodeValue+=next.nodeValue.replace(/^\[CDATA\[((.|\n|\r)*)\]\]$/,"$1"):math.nodeValue+=next.nodeValue;else{var ignore=this.ignoreTags[next.nodeName.toLowerCase()];math.nodeValue+=null==ignore?" ":ignore}math.parentNode.removeChild(next)}var TeX=math.nodeValue.substr(search.olen,math.nodeValue.length-search.olen-search.clen);return math.parentNode.removeChild(math),"none"!==this.config.preview&&this.createPreview(search.mode,TeX),math=this.createMathTag(search.mode,TeX),this.search={},this.pattern.lastIndex=0,CLOSE&&CLOSE.parentNode.removeChild(CLOSE),math},insertNode:function(node){var search=this.search;search.close.parentNode.insertBefore(node,search.close)},createPreview:function(mode,tex){var previewClass=MathJax.Hub.config.preRemoveClass,preview=this.config.preview;"none"!==preview&&(this.search.close.previousSibling||{}).className!==previewClass&&("TeX"===preview&&(preview=[this.filterPreview(tex)]),preview&&(preview=MathJax.HTML.Element("span",{className:previewClass},preview),this.insertNode(preview)))},createMathTag:function(mode,tex){var script=document.createElement("script");return script.type="math/tex"+mode,MathJax.HTML.setScript(script,tex),this.insertNode(script),script},filterPreview:function(tex){return tex}},MathJax.Hub.Register.PreProcessor(["PreProcess",MathJax.Extension.tex2jax]),MathJax.Ajax.loadComplete("[MathJax]/extensions/tex2jax.js"),MathJax.Extension["TeX/AMScd"]={version:"2.7.5",config:MathJax.Hub.CombineConfig("TeX.CD",{colspace:"5pt",rowspace:"5pt",harrowsize:"2.75em",varrowsize:"1.75em",hideHorizontalLabels:!1})},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var MML=MathJax.ElementJax.mml,TEX=MathJax.InputJax.TeX,STACKITEM=TEX.Stack.Item,TEXDEF=TEX.Definitions,CONFIG=MathJax.Extension["TeX/AMScd"].config;TEXDEF.environment.CD="CD_env",TEXDEF.special["@"]="CD_arrow",TEXDEF.macros.minCDarrowwidth="CD_minwidth",TEXDEF.macros.minCDarrowheight="CD_minheight",TEX.Parse.Augment({CD_env:function(begin){return this.Push(begin),STACKITEM.array().With({arraydef:{columnalign:"center",columnspacing:CONFIG.colspace,rowspacing:CONFIG.rowspace,displaystyle:!0},minw:this.stack.env.CD_minw||CONFIG.harrowsize,minh:this.stack.env.CD_minh||CONFIG.varrowsize})},CD_arrow:function(name){var c=this.string.charAt(this.i);if(!c.match(/[><VA.|=]/))return this.Other(name);this.i++;var top=this.stack.Top();top.isa(STACKITEM.array)&&!top.data.length||(this.CD_cell(name),top=this.stack.Top());for(var arrowRow=top.table.length%2==1,n=(top.row.length+(arrowRow?0:1))%2,mml;n;)this.CD_cell(name),n--;var hdef={minsize:top.minw,stretchy:!0},vdef={minsize:top.minh,stretchy:!0,symmetric:!0,lspace:0,rspace:0};if("."===c);else if("|"===c)mml=this.mmlToken(MML.mo("∥").With(vdef));else if("="===c)mml=this.mmlToken(MML.mo("=").With(hdef));else{var arrow={">":"→","<":"←",V:"↓",A:"↑"}[c],a=this.GetUpTo(name+c,c),b=this.GetUpTo(name+c,c);if(">"===c||"<"===c){if(mml=MML.mo(arrow).With(hdef),a||(a="\\kern "+top.minw),a||b){var pad={width:"+11mu",lspace:"6mu"};mml=MML.munderover(this.mmlToken(mml)),a&&(a=TEX.Parse(a,this.stack.env).mml(),mml.SetData(mml.over,MML.mpadded(a).With(pad).With({voffset:".1em"}))),b&&(b=TEX.Parse(b,this.stack.env).mml(),mml.SetData(mml.under,MML.mpadded(b).With(pad))),CONFIG.hideHorizontalLabels&&(mml=MML.mpadded(mml).With({depth:0,height:".67em"}))}}else mml=arrow=this.mmlToken(MML.mo(arrow).With(vdef)),(a||b)&&(mml=MML.mrow(),a&&mml.Append(TEX.Parse("\\scriptstyle\\llap{"+a+"}",this.stack.env).mml()),mml.Append(arrow.With({texClass:MML.TEXCLASS.ORD})),b&&mml.Append(TEX.Parse("\\scriptstyle\\rlap{"+b+"}",this.stack.env).mml()))}mml&&this.Push(mml),this.CD_cell(name)},CD_cell:function(name){var top=this.stack.Top();(top.table||[]).length%2==0&&0===(top.row||[]).length&&this.Push(MML.mpadded().With({height:"8.5pt",depth:"2pt"})),this.Push(STACKITEM.cell().With({isEntry:!0,name:name}))},CD_minwidth:function(name){this.stack.env.CD_minw=this.GetDimen(name)},CD_minheight:function(name){this.stack.env.CD_minh=this.GetDimen(name)}})})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/AMScd.js"),MathJax.Extension["TeX/AMSmath"]={version:"2.7.5",number:0,startNumber:0,IDs:{},eqIDs:{},labels:{},eqlabels:{},refs:[]},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var MML=MathJax.ElementJax.mml,TEX=MathJax.InputJax.TeX,AMS=MathJax.Extension["TeX/AMSmath"],TEXDEF=TEX.Definitions,STACKITEM=TEX.Stack.Item,CONFIG=TEX.config.equationNumbers,COLS=function(W){for(var WW=[],i=0,m=W.length;i<m;i++)WW[i]=TEX.Parse.prototype.Em(W[i]);return WW.join(" ")},baseURL=0===document.getElementsByTagName("base").length?"":String(document.location).replace(/#.*$/,"");TEXDEF.Add({mathchar0mo:{iiiint:["2A0C",{texClass:MML.TEXCLASS.OP}]},macros:{mathring:["Accent","2DA"],nobreakspace:"Tilde",negmedspace:["Spacer",MML.LENGTH.NEGATIVEMEDIUMMATHSPACE],negthickspace:["Spacer",MML.LENGTH.NEGATIVETHICKMATHSPACE],idotsint:["MultiIntegral","\\int\\cdots\\int"],dddot:["Accent","20DB"],ddddot:["Accent","20DC"],sideset:["Macro","\\mathop{\\mathop{\\rlap{\\phantom{#3}}}\\nolimits#1\\!\\mathop{#3}\\nolimits#2}",3],boxed:["Macro","\\fbox{$\\displaystyle{#1}$}",1],tag:"HandleTag",notag:"HandleNoTag",label:"HandleLabel",ref:"HandleRef",eqref:["HandleRef",!0],substack:["Macro","\\begin{subarray}{c}#1\\end{subarray}",1],injlim:["NamedOp","inj&thinsp;lim"],projlim:["NamedOp","proj&thinsp;lim"],varliminf:["Macro","\\mathop{\\underline{\\mmlToken{mi}{lim}}}"],varlimsup:["Macro","\\mathop{\\overline{\\mmlToken{mi}{lim}}}"],varinjlim:["Macro","\\mathop{\\underrightarrow{\\mmlToken{mi}{lim}}}"],varprojlim:["Macro","\\mathop{\\underleftarrow{\\mmlToken{mi}{lim}}}"],DeclareMathOperator:"HandleDeclareOp",operatorname:"HandleOperatorName",SkipLimits:"SkipLimits",genfrac:"Genfrac",frac:["Genfrac","","","",""],tfrac:["Genfrac","","","",1],dfrac:["Genfrac","","","",0],binom:["Genfrac","(",")","0",""],tbinom:["Genfrac","(",")","0",1],dbinom:["Genfrac","(",")","0",0],cfrac:"CFrac",shoveleft:["HandleShove",MML.ALIGN.LEFT],shoveright:["HandleShove",MML.ALIGN.RIGHT],xrightarrow:["xArrow",8594,5,6],xleftarrow:["xArrow",8592,7,3]},environment:{align:["AMSarray",null,!0,!0,"rlrlrlrlrlrl",COLS([0,2,0,2,0,2,0,2,0,2,0])],"align*":["AMSarray",null,!1,!0,"rlrlrlrlrlrl",COLS([0,2,0,2,0,2,0,2,0,2,0])],multline:["Multline",null,!0],"multline*":["Multline",null,!1],split:["AMSarray",null,!1,!1,"rl",COLS([0])],gather:["AMSarray",null,!0,!0,"c"],"gather*":["AMSarray",null,!1,!0,"c"],alignat:["AlignAt",null,!0,!0],"alignat*":["AlignAt",null,!1,!0],alignedat:["AlignAt",null,!1,!1],aligned:["AlignedAMSArray",null,null,null,"rlrlrlrlrlrl",COLS([0,2,0,2,0,2,0,2,0,2,0]),".5em","D"],gathered:["AlignedAMSArray",null,null,null,"c",null,".5em","D"],subarray:["Array",null,null,null,null,COLS([0]),"0.1em","S",1],smallmatrix:["Array",null,null,null,"c",COLS([1/3]),".2em","S",1],equation:["EquationBegin","Equation",!0],"equation*":["EquationBegin","EquationStar",!1],eqnarray:["AMSarray",null,!0,!0,"rcl","0 "+MML.LENGTH.THICKMATHSPACE,".5em"],"eqnarray*":["AMSarray",null,!1,!0,"rcl","0 "+MML.LENGTH.THICKMATHSPACE,".5em"]},delimiter:{"\\lvert":["007C",{texClass:MML.TEXCLASS.OPEN}],"\\rvert":["007C",{texClass:MML.TEXCLASS.CLOSE}],"\\lVert":["2016",{texClass:MML.TEXCLASS.OPEN}],"\\rVert":["2016",{texClass:MML.TEXCLASS.CLOSE}]}},null,!0),TEX.Parse.Augment({HandleTag:function(name){var star=this.GetStar(),arg=this.trimSpaces(this.GetArgument(name)),tag=arg;star||(arg=CONFIG.formatTag(arg));var global=this.stack.global;global.tagID=tag,global.notags&&TEX.Error(["CommandNotAllowedInEnv","%1 not allowed in %2 environment",name,global.notags]),global.tag&&TEX.Error(["MultipleCommand","Multiple %1",name]),global.tag=MML.mtd.apply(MML,this.InternalMath(arg)).With({id:CONFIG.formatID(tag)})},HandleNoTag:function(name){this.stack.global.tag&&delete this.stack.global.tag,this.stack.global.notag=!0},HandleLabel:function(name){var global=this.stack.global,label=this.GetArgument(name);""!==label&&(AMS.refUpdate||(global.label&&TEX.Error(["MultipleCommand","Multiple %1",name]),global.label=label,(AMS.labels[label]||AMS.eqlabels[label])&&TEX.Error(["MultipleLabel","Label '%1' multiply defined",label]),AMS.eqlabels[label]={tag:"???",id:""}))},HandleRef:function(name,eqref){var label=this.GetArgument(name),ref=AMS.labels[label]||AMS.eqlabels[label];ref||(ref={tag:"???",id:""},AMS.badref=!AMS.refUpdate);var tag=ref.tag;eqref&&(tag=CONFIG.formatTag(tag)),this.Push(MML.mrow.apply(MML,this.InternalMath(tag)).With({href:CONFIG.formatURL(ref.id,baseURL),class:"MathJax_ref"}))},HandleDeclareOp:function(name){var limits=this.GetStar()?"":"\\nolimits\\SkipLimits",cs=this.trimSpaces(this.GetArgument(name));"\\"==cs.charAt(0)&&(cs=cs.substr(1));var op=this.GetArgument(name);op=op.replace(/\*/g,"\\text{*}").replace(/-/g,"\\text{-}"),this.setDef(cs,["Macro","\\mathop{\\rm "+op+"}"+limits])},HandleOperatorName:function(name){var limits=this.GetStar()?"":"\\nolimits\\SkipLimits",op=this.trimSpaces(this.GetArgument(name));op=op.replace(/\*/g,"\\text{*}").replace(/-/g,"\\text{-}"),this.string="\\mathop{\\rm "+op+"}"+limits+" "+this.string.slice(this.i),this.i=0},SkipLimits:function(name){var c=this.GetNext(),i=this.i;"\\"===c&&++this.i&&"limits"!==this.GetCS()&&(this.i=i)},HandleShove:function(name,shove){var top=this.stack.Top();"multline"!==top.type&&TEX.Error(["CommandInMultline","%1 can only appear within the multline environment",name]),top.data.length&&TEX.Error(["CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",name]),top.data.shove=shove},CFrac:function(name){var lr=this.trimSpaces(this.GetBrackets(name,"")),num=this.GetArgument(name),den=this.GetArgument(name),frac=MML.mfrac(TEX.Parse("\\strut\\textstyle{"+num+"}",this.stack.env).mml(),TEX.Parse("\\strut\\textstyle{"+den+"}",this.stack.env).mml());null==(lr={l:MML.ALIGN.LEFT,r:MML.ALIGN.RIGHT,"":""}[lr])&&TEX.Error(["IllegalAlign","Illegal alignment specified in %1",name]),lr&&(frac.numalign=frac.denomalign=lr),this.Push(frac)},Genfrac:function(name,left,right,thick,style){null==left&&(left=this.GetDelimiterArg(name)),null==right&&(right=this.GetDelimiterArg(name)),null==thick&&(thick=this.GetArgument(name)),null==style&&(style=this.trimSpaces(this.GetArgument(name)));var num=this.ParseArg(name),den=this.ParseArg(name),frac=MML.mfrac(num,den);if(""!==thick&&(frac.linethickness=thick),(left||right)&&(frac=TEX.fixedFence(left,frac.With({texWithDelims:!0}),right)),""!==style){var STYLE=["D","T","S","SS"][style];null==STYLE&&TEX.Error(["BadMathStyleFor","Bad math style for %1",name]),frac=MML.mstyle(frac),"D"===STYLE?(frac.displaystyle=!0,frac.scriptlevel=0):(frac.displaystyle=!1,frac.scriptlevel=style-1)}this.Push(frac)},Multline:function(begin,numbered){return this.Push(begin),this.checkEqnEnv(),STACKITEM.multline(numbered,this.stack).With({arraydef:{displaystyle:!0,rowspacing:".5em",width:TEX.config.MultLineWidth,columnwidth:"100%",side:TEX.config.TagSide,minlabelspacing:TEX.config.TagIndent}})},AMSarray:function(begin,numbered,taggable,align,spacing){return this.Push(begin),taggable&&this.checkEqnEnv(),align=(align=align.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center"),STACKITEM.AMSarray(begin.name,numbered,taggable,this.stack).With({arraydef:{displaystyle:!0,rowspacing:".5em",columnalign:align,columnspacing:spacing||"1em",rowspacing:"3pt",side:TEX.config.TagSide,minlabelspacing:TEX.config.TagIndent}})},AlignedAMSArray:function(begin){var align=this.GetBrackets("\\begin{"+begin.name+"}");return this.setArrayAlign(this.AMSarray.apply(this,arguments),align)},AlignAt:function(begin,numbered,taggable){var n,valign,align="",spacing=[];for(taggable||(valign=this.GetBrackets("\\begin{"+begin.name+"}")),(n=this.GetArgument("\\begin{"+begin.name+"}")).match(/[^0-9]/)&&TEX.Error(["PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+begin.name+"}"]);n>0;)align+="rl",spacing.push("0em 0em"),n--;if(spacing=spacing.join(" "),taggable)return this.AMSarray(begin,numbered,taggable,align,spacing);var array=this.AMSarray(begin,numbered,taggable,align,spacing);return this.setArrayAlign(array,valign)},EquationBegin:function(begin,force){return this.checkEqnEnv(),this.stack.global.forcetag=force&&"none"!==CONFIG.autoNumber,begin},EquationStar:function(begin,row){return this.stack.global.tagged=!0,row},checkEqnEnv:function(){this.stack.global.eqnenv&&TEX.Error(["ErroneousNestingEq","Erroneous nesting of equation structures"]),this.stack.global.eqnenv=!0},MultiIntegral:function(name,integral){var next=this.GetNext();if("\\"===next){var i=this.i;next=this.GetArgument(name),this.i=i,"\\limits"===next&&(integral="\\idotsint"===name?"\\!\\!\\mathop{\\,\\,"+integral+"}":"\\!\\!\\!\\mathop{\\,\\,\\,"+integral+"}")}this.string=integral+" "+this.string.slice(this.i),this.i=0},xArrow:function(name,chr,l,r){var def={width:"+"+(l+r)+"mu",lspace:l+"mu"},bot=this.GetBrackets(name),top=this.ParseArg(name),arrow=MML.mo(MML.chars(String.fromCharCode(chr))).With({stretchy:!0,texClass:MML.TEXCLASS.REL}),mml=MML.munderover(arrow);mml.SetData(mml.over,MML.mpadded(top).With(def).With({voffset:".15em"})),bot&&(bot=TEX.Parse(bot,this.stack.env).mml(),mml.SetData(mml.under,MML.mpadded(bot).With(def).With({voffset:"-.24em"}))),this.Push(mml.With({subsupOK:!0}))},GetDelimiterArg:function(name){var c=this.trimSpaces(this.GetArgument(name));return""==c?null:c in TEXDEF.delimiter?c:void TEX.Error(["MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",name])},GetStar:function(){var star="*"===this.GetNext();return star&&this.i++,star}}),STACKITEM.Augment({autoTag:function(){var global=this.global;if(!global.notag){AMS.number++,global.tagID=CONFIG.formatNumber(AMS.number.toString());var mml=TEX.Parse("\\text{"+CONFIG.formatTag(global.tagID)+"}",{}).mml();global.tag=MML.mtd(mml).With({id:CONFIG.formatID(global.tagID)})}},getTag:function(){var global=this.global,tag=global.tag;if(global.tagged=!0,global.label&&(CONFIG.useLabelIds&&(tag.id=CONFIG.formatID(global.label)),AMS.eqlabels[global.label]={tag:global.tagID,id:tag.id}),document.getElementById(tag.id)||AMS.IDs[tag.id]||AMS.eqIDs[tag.id]){var i=0,ID;do{i++,ID=tag.id+"_"+i}while(document.getElementById(ID)||AMS.IDs[ID]||AMS.eqIDs[ID]);tag.id=ID,global.label&&(AMS.eqlabels[global.label].id=ID)}return AMS.eqIDs[tag.id]=1,this.clearTag(),tag},clearTag:function(){var global=this.global;delete global.tag,delete global.tagID,delete global.label},fixInitialMO:function(data){for(var i=0,m=data.length;i<m;i++)if(data[i]&&"mspace"!==data[i].type&&("texatom"!==data[i].type||data[i].data[0]&&data[i].data[0].data.length)){data[i].isEmbellished()&&data.unshift(MML.mi());break}}}),STACKITEM.multline=STACKITEM.array.Subclass({type:"multline",Init:function(numbered,stack){this.SUPER(arguments).Init.apply(this),this.numbered=numbered&&"none"!==CONFIG.autoNumber,this.save={notag:stack.global.notag},stack.global.tagged=!numbered&&!stack.global.forcetag},EndEntry:function(){this.table.length&&this.fixInitialMO(this.data);var mtd=MML.mtd.apply(MML,this.data);this.data.shove&&(mtd.columnalign=this.data.shove),this.row.push(mtd),this.data=[]},EndRow:function(){1!=this.row.length&&TEX.Error(["MultlineRowsOneCol","The rows within the %1 environment must have exactly one column","multline"]),this.table.push(this.row),this.row=[]},EndTable:function(){if(this.SUPER(arguments).EndTable.call(this),this.table.length){var m=this.table.length-1,i,label=-1;for(this.table[0][0].columnalign||(this.table[0][0].columnalign=MML.ALIGN.LEFT),this.table[m][0].columnalign||(this.table[m][0].columnalign=MML.ALIGN.RIGHT),!this.global.tag&&this.numbered&&this.autoTag(),this.global.tag&&!this.global.notags&&(label="left"===this.arraydef.side?0:this.table.length-1,this.table[label]=[this.getTag()].concat(this.table[label])),i=0,m=this.table.length;i<m;i++){var mtr=i===label?MML.mlabeledtr:MML.mtr;this.table[i]=mtr.apply(MML,this.table[i])}}this.global.notag=this.save.notag}}),STACKITEM.AMSarray=STACKITEM.array.Subclass({type:"AMSarray",Init:function(name,numbered,taggable,stack){this.SUPER(arguments).Init.apply(this),this.numbered=numbered&&"none"!==CONFIG.autoNumber,this.save={notags:stack.global.notags,notag:stack.global.notag},stack.global.notags=taggable?null:name,stack.global.tagged=!numbered&&!stack.global.forcetag},EndEntry:function(){this.row.length%2==1&&this.fixInitialMO(this.data),this.row.push(MML.mtd.apply(MML,this.data)),this.data=[]},EndRow:function(){var mtr=MML.mtr;!this.global.tag&&this.numbered&&this.autoTag(),this.global.tag&&!this.global.notags?(this.row=[this.getTag()].concat(this.row),mtr=MML.mlabeledtr):this.clearTag(),this.numbered&&delete this.global.notag,this.table.push(mtr.apply(MML,this.row)),this.row=[]},EndTable:function(){this.SUPER(arguments).EndTable.call(this),this.global.notags=this.save.notags,this.global.notag=this.save.notag}}),STACKITEM.start.Augment({oldCheckItem:STACKITEM.start.prototype.checkItem,checkItem:function(item){if("stop"===item.type){var mml=this.mmlData(),global=this.global;if(!AMS.display||global.tag||global.tagged||global.isInner||"all"!==CONFIG.autoNumber&&!global.forcetag||this.autoTag(),global.tag){var row=[this.getTag(),MML.mtd(mml)],def={side:TEX.config.TagSide,minlabelspacing:TEX.config.TagIndent,displaystyle:"inherit"};mml=MML.mtable(MML.mlabeledtr.apply(MML,row)).With(def)}return STACKITEM.mml(mml)}return this.oldCheckItem.call(this,item)}}),TEX.prefilterHooks.Add((function(data){AMS.display=data.display,AMS.number=AMS.startNumber,AMS.eqlabels={},AMS.eqIDs={},AMS.badref=!1,AMS.refUpdate&&(AMS.number=data.script.MathJax.startNumber)})),TEX.postfilterHooks.Add((function(data){data.script.MathJax.startNumber=AMS.startNumber,AMS.startNumber=AMS.number,MathJax.Hub.Insert(AMS.IDs,AMS.eqIDs),MathJax.Hub.Insert(AMS.labels,AMS.eqlabels),AMS.badref&&!data.math.texError&&AMS.refs.push(data.script)}),100),MathJax.Hub.Register.MessageHook("Begin Math Input",(function(){AMS.refs=[],AMS.refUpdate=!1})),MathJax.Hub.Register.MessageHook("End Math Input",(function(message){if(AMS.refs.length){AMS.refUpdate=!0;for(var i=0,m=AMS.refs.length;i<m;i++)AMS.refs[i].MathJax.state=MathJax.ElementJax.STATE.UPDATE;return MathJax.Hub.processInput({scripts:AMS.refs,start:(new Date).getTime(),i:0,j:0,jax:{},jaxIDs:[]})}return null})),TEX.resetEquationNumbers=function(n,keepLabels){AMS.startNumber=n||0,keepLabels||(AMS.labels={},AMS.IDs={})},MathJax.Hub.Startup.signal.Post("TeX AMSmath Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/AMSmath.js"),MathJax.Extension["TeX/AMSsymbols"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var MML=MathJax.ElementJax.mml,TEXDEF;MathJax.InputJax.TeX.Definitions.Add({mathchar0mi:{digamma:"03DD",varkappa:"03F0",varGamma:["0393",{mathvariant:MML.VARIANT.ITALIC}],varDelta:["0394",{mathvariant:MML.VARIANT.ITALIC}],varTheta:["0398",{mathvariant:MML.VARIANT.ITALIC}],varLambda:["039B",{mathvariant:MML.VARIANT.ITALIC}],varXi:["039E",{mathvariant:MML.VARIANT.ITALIC}],varPi:["03A0",{mathvariant:MML.VARIANT.ITALIC}],varSigma:["03A3",{mathvariant:MML.VARIANT.ITALIC}],varUpsilon:["03A5",{mathvariant:MML.VARIANT.ITALIC}],varPhi:["03A6",{mathvariant:MML.VARIANT.ITALIC}],varPsi:["03A8",{mathvariant:MML.VARIANT.ITALIC}],varOmega:["03A9",{mathvariant:MML.VARIANT.ITALIC}],beth:"2136",gimel:"2137",daleth:"2138",backprime:["2035",{variantForm:!0}],hslash:"210F",varnothing:["2205",{variantForm:!0}],blacktriangle:"25B4",triangledown:["25BD",{variantForm:!0}],blacktriangledown:"25BE",square:"25FB",Box:"25FB",blacksquare:"25FC",lozenge:"25CA",Diamond:"25CA",blacklozenge:"29EB",circledS:["24C8",{mathvariant:MML.VARIANT.NORMAL}],bigstar:"2605",sphericalangle:"2222",measuredangle:"2221",nexists:"2204",complement:"2201",mho:"2127",eth:["00F0",{mathvariant:MML.VARIANT.NORMAL}],Finv:"2132",diagup:"2571",Game:"2141",diagdown:"2572",Bbbk:["006B",{mathvariant:MML.VARIANT.DOUBLESTRUCK}],yen:"00A5",circledR:"00AE",checkmark:"2713",maltese:"2720"},mathchar0mo:{dotplus:"2214",ltimes:"22C9",smallsetminus:"2216",rtimes:"22CA",Cap:"22D2",doublecap:"22D2",leftthreetimes:"22CB",Cup:"22D3",doublecup:"22D3",rightthreetimes:"22CC",barwedge:"22BC",curlywedge:"22CF",veebar:"22BB",curlyvee:"22CE",doublebarwedge:"2A5E",boxminus:"229F",circleddash:"229D",boxtimes:"22A0",circledast:"229B",boxdot:"22A1",circledcirc:"229A",boxplus:"229E",centerdot:["22C5",{variantForm:!0}],divideontimes:"22C7",intercal:"22BA",leqq:"2266",geqq:"2267",leqslant:"2A7D",geqslant:"2A7E",eqslantless:"2A95",eqslantgtr:"2A96",lesssim:"2272",gtrsim:"2273",lessapprox:"2A85",gtrapprox:"2A86",approxeq:"224A",lessdot:"22D6",gtrdot:"22D7",lll:"22D8",llless:"22D8",ggg:"22D9",gggtr:"22D9",lessgtr:"2276",gtrless:"2277",lesseqgtr:"22DA",gtreqless:"22DB",lesseqqgtr:"2A8B",gtreqqless:"2A8C",doteqdot:"2251",Doteq:"2251",eqcirc:"2256",risingdotseq:"2253",circeq:"2257",fallingdotseq:"2252",triangleq:"225C",backsim:"223D",thicksim:["223C",{variantForm:!0}],backsimeq:"22CD",thickapprox:["2248",{variantForm:!0}],subseteqq:"2AC5",supseteqq:"2AC6",Subset:"22D0",Supset:"22D1",sqsubset:"228F",sqsupset:"2290",preccurlyeq:"227C",succcurlyeq:"227D",curlyeqprec:"22DE",curlyeqsucc:"22DF",precsim:"227E",succsim:"227F",precapprox:"2AB7",succapprox:"2AB8",vartriangleleft:"22B2",lhd:"22B2",vartriangleright:"22B3",rhd:"22B3",trianglelefteq:"22B4",unlhd:"22B4",trianglerighteq:"22B5",unrhd:"22B5",vDash:"22A8",Vdash:"22A9",Vvdash:"22AA",smallsmile:["2323",{variantForm:!0}],shortmid:["2223",{variantForm:!0}],smallfrown:["2322",{variantForm:!0}],shortparallel:["2225",{variantForm:!0}],bumpeq:"224F",between:"226C",Bumpeq:"224E",pitchfork:"22D4",varpropto:"221D",backepsilon:"220D",blacktriangleleft:"25C2",blacktriangleright:"25B8",therefore:"2234",because:"2235",eqsim:"2242",vartriangle:["25B3",{variantForm:!0}],Join:"22C8",nless:"226E",ngtr:"226F",nleq:"2270",ngeq:"2271",nleqslant:["2A87",{variantForm:!0}],ngeqslant:["2A88",{variantForm:!0}],nleqq:["2270",{variantForm:!0}],ngeqq:["2271",{variantForm:!0}],lneq:"2A87",gneq:"2A88",lneqq:"2268",gneqq:"2269",lvertneqq:["2268",{variantForm:!0}],gvertneqq:["2269",{variantForm:!0}],lnsim:"22E6",gnsim:"22E7",lnapprox:"2A89",gnapprox:"2A8A",nprec:"2280",nsucc:"2281",npreceq:["22E0",{variantForm:!0}],nsucceq:["22E1",{variantForm:!0}],precneqq:"2AB5",succneqq:"2AB6",precnsim:"22E8",succnsim:"22E9",precnapprox:"2AB9",succnapprox:"2ABA",nsim:"2241",ncong:"2246",nshortmid:["2224",{variantForm:!0}],nshortparallel:["2226",{variantForm:!0}],nmid:"2224",nparallel:"2226",nvdash:"22AC",nvDash:"22AD",nVdash:"22AE",nVDash:"22AF",ntriangleleft:"22EA",ntriangleright:"22EB",ntrianglelefteq:"22EC",ntrianglerighteq:"22ED",nsubseteq:"2288",nsupseteq:"2289",nsubseteqq:["2288",{variantForm:!0}],nsupseteqq:["2289",{variantForm:!0}],subsetneq:"228A",supsetneq:"228B",varsubsetneq:["228A",{variantForm:!0}],varsupsetneq:["228B",{variantForm:!0}],subsetneqq:"2ACB",supsetneqq:"2ACC",varsubsetneqq:["2ACB",{variantForm:!0}],varsupsetneqq:["2ACC",{variantForm:!0}],leftleftarrows:"21C7",rightrightarrows:"21C9",leftrightarrows:"21C6",rightleftarrows:"21C4",Lleftarrow:"21DA",Rrightarrow:"21DB",twoheadleftarrow:"219E",twoheadrightarrow:"21A0",leftarrowtail:"21A2",rightarrowtail:"21A3",looparrowleft:"21AB",looparrowright:"21AC",leftrightharpoons:"21CB",rightleftharpoons:["21CC",{variantForm:!0}],curvearrowleft:"21B6",curvearrowright:"21B7",circlearrowleft:"21BA",circlearrowright:"21BB",Lsh:"21B0",Rsh:"21B1",upuparrows:"21C8",downdownarrows:"21CA",upharpoonleft:"21BF",upharpoonright:"21BE",downharpoonleft:"21C3",restriction:"21BE",multimap:"22B8",downharpoonright:"21C2",leftrightsquigarrow:"21AD",rightsquigarrow:"21DD",leadsto:"21DD",dashrightarrow:"21E2",dashleftarrow:"21E0",nleftarrow:"219A",nrightarrow:"219B",nLeftarrow:"21CD",nRightarrow:"21CF",nleftrightarrow:"21AE",nLeftrightarrow:"21CE"},delimiter:{"\\ulcorner":"231C","\\urcorner":"231D","\\llcorner":"231E","\\lrcorner":"231F"},macros:{implies:["Macro","\\;\\Longrightarrow\\;"],impliedby:["Macro","\\;\\Longleftarrow\\;"]}},null,!0);var REL=MML.mo.OPTYPES.REL;MathJax.Hub.Insert(MML.mo.prototype,{OPTABLE:{infix:{"⌢":REL,"⌣":REL,"△":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL,"":REL}}}),MathJax.Hub.Startup.signal.Post("TeX AMSsymbols Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/AMSsymbols.js"),MathJax.Extension["TeX/HTML"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,TEXDEF;TEX.Definitions.Add({macros:{href:"HREF_attribute",class:"CLASS_attribute",style:"STYLE_attribute",cssId:"ID_attribute"}},null,!0),TEX.Parse.Augment({HREF_attribute:function(name){var url=this.GetArgument(name),arg=this.GetArgumentMML(name);this.Push(arg.With({href:url}))},CLASS_attribute:function(name){var CLASS=this.GetArgument(name),arg=this.GetArgumentMML(name);null!=arg.class&&(CLASS=arg.class+" "+CLASS),this.Push(arg.With({class:CLASS}))},STYLE_attribute:function(name){var style=this.GetArgument(name),arg=this.GetArgumentMML(name);null!=arg.style&&(";"!==style.charAt(style.length-1)&&(style+=";"),style=arg.style+" "+style),this.Push(arg.With({style:style}))},ID_attribute:function(name){var ID=this.GetArgument(name),arg=this.GetArgumentMML(name);this.Push(arg.With({id:ID}))},GetArgumentMML:function(name){var arg=this.ParseArg(name);return arg.inferred&&1==arg.data.length?arg=arg.data[0]:delete arg.inferred,arg}}),MathJax.Hub.Startup.signal.Post("TeX HTML Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/HTML.js"),MathJax.Extension["TeX/action"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,MML=MathJax.ElementJax.mml;TEX.Definitions.Add({macros:{toggle:"Toggle",mathtip:"Mathtip",texttip:["Macro","\\mathtip{#1}{\\text{#2}}",2]}},null,!0),TEX.Parse.Augment({Toggle:function(name){for(var data=[],arg;"\\endtoggle"!==(arg=this.GetArgument(name));)data.push(TEX.Parse(arg,this.stack.env).mml());this.Push(MML.maction.apply(MML,data).With({actiontype:MML.ACTIONTYPE.TOGGLE}))},Mathtip:function(name){var arg=this.ParseArg(name),tip=this.ParseArg(name);this.Push(MML.maction(arg,tip).With({actiontype:MML.ACTIONTYPE.TOOLTIP}))}}),MathJax.Hub.Startup.signal.Post("TeX action Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/action.js"),MathJax.Extension["TeX/autobold"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX;MathJax.InputJax.TeX.prefilterHooks.Add((function(data){var span=data.script.parentNode.insertBefore(document.createElement("span"),data.script);span.visibility="hidden",span.style.fontFamily="Times, serif",span.appendChild(document.createTextNode("ABCXYZabcxyz"));var W=span.offsetWidth;span.style.fontWeight="bold",W&&span.offsetWidth===W&&(data.math="\\boldsymbol{"+data.math+"}"),span.parentNode.removeChild(span)})),MathJax.Hub.Startup.signal.Post("TeX autobold Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/autobold.js"),MathJax.Extension["TeX/bbox"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,MML=MathJax.ElementJax.mml;TEX.Definitions.Add({macros:{bbox:"BBox"}},null,!0),TEX.Parse.Augment({BBox:function(name){for(var bbox=this.GetBrackets(name,""),math=this.ParseArg(name),parts=bbox.split(/,/),def,background,style,i=0,m=parts.length;i<m;i++){var part=parts[i].replace(/^\s+/,"").replace(/\s+$/,""),match=part.match(/^(\.\d+|\d+(\.\d*)?)(pt|em|ex|mu|px|in|cm|mm)$/);if(match){def&&TEX.Error(["MultipleBBoxProperty","%1 specified twice in %2","Padding",name]);var pad=this.BBoxPadding(match[1]+match[3]);pad&&(def={height:"+"+pad,depth:"+"+pad,lspace:pad,width:"+"+2*match[1]+match[3]})}else part.match(/^([a-z0-9]+|\#[0-9a-f]{6}|\#[0-9a-f]{3})$/i)?(background&&TEX.Error(["MultipleBBoxProperty","%1 specified twice in %2","Background",name]),background=part):part.match(/^[-a-z]+:/i)?(style&&TEX.Error(["MultipleBBoxProperty","%1 specified twice in %2","Style",name]),style=this.BBoxStyle(part)):""!==part&&TEX.Error(["InvalidBBoxProperty","'%1' doesn't look like a color, a padding dimension, or a style",part])}def&&(math=MML.mpadded(math).With(def)),(background||style)&&(math=MML.mstyle(math).With({mathbackground:background,style:style})),this.Push(math)},BBoxStyle:function(styles){return styles},BBoxPadding:function(pad){return pad}}),MathJax.Hub.Startup.signal.Post("TeX bbox Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/bbox.js"),MathJax.Extension["TeX/boldsymbol"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var MML=MathJax.ElementJax.mml,TEX=MathJax.InputJax.TeX,TEXDEF=TEX.Definitions,BOLDVARIANT={};BOLDVARIANT[MML.VARIANT.NORMAL]=MML.VARIANT.BOLD,BOLDVARIANT[MML.VARIANT.ITALIC]=MML.VARIANT.BOLDITALIC,BOLDVARIANT[MML.VARIANT.FRAKTUR]=MML.VARIANT.BOLDFRAKTUR,BOLDVARIANT[MML.VARIANT.SCRIPT]=MML.VARIANT.BOLDSCRIPT,BOLDVARIANT[MML.VARIANT.SANSSERIF]=MML.VARIANT.BOLDSANSSERIF,BOLDVARIANT["-tex-caligraphic"]="-tex-caligraphic-bold",BOLDVARIANT["-tex-oldstyle"]="-tex-oldstyle-bold",TEXDEF.Add({macros:{boldsymbol:"Boldsymbol"}},null,!0),TEX.Parse.Augment({mmlToken:function(token){if(this.stack.env.boldsymbol){var variant=token.Get("mathvariant");token.mathvariant=null==variant?MML.VARIANT.BOLD:BOLDVARIANT[variant]||variant}return token},Boldsymbol:function(name){var boldsymbol=this.stack.env.boldsymbol,font=this.stack.env.font;this.stack.env.boldsymbol=!0,this.stack.env.font=null;var mml=this.ParseArg(name);this.stack.env.font=font,this.stack.env.boldsymbol=boldsymbol,this.Push(mml)}}),MathJax.Hub.Startup.signal.Post("TeX boldsymbol Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/boldsymbol.js"),MathJax.Extension["TeX/cancel"]={version:"2.7.5",ALLOWED:{color:1,mathcolor:1,background:1,mathbackground:1,padding:1,thickness:1}},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,MML=MathJax.ElementJax.mml,CANCEL=MathJax.Extension["TeX/cancel"];CANCEL.setAttributes=function(def,attr){if(""!==attr)for(var i=0,m=(attr=attr.replace(/ /g,"").split(/,/)).length;i<m;i++){var keyvalue=attr[i].split(/[:=]/);CANCEL.ALLOWED[keyvalue[0]]&&("true"===keyvalue[1]&&(keyvalue[1]=!0),"false"===keyvalue[1]&&(keyvalue[1]=!1),def[keyvalue[0]]=keyvalue[1])}return def},TEX.Definitions.Add({macros:{cancel:["Cancel",MML.NOTATION.UPDIAGONALSTRIKE],bcancel:["Cancel",MML.NOTATION.DOWNDIAGONALSTRIKE],xcancel:["Cancel",MML.NOTATION.UPDIAGONALSTRIKE+" "+MML.NOTATION.DOWNDIAGONALSTRIKE],cancelto:"CancelTo"}},null,!0),TEX.Parse.Augment({Cancel:function(name,notation){var attr=this.GetBrackets(name,""),math=this.ParseArg(name),def=CANCEL.setAttributes({notation:notation},attr);this.Push(MML.menclose(math).With(def))},CancelTo:function(name,notation){var value=this.ParseArg(name),attr=this.GetBrackets(name,""),math=this.ParseArg(name),def=CANCEL.setAttributes({notation:MML.NOTATION.UPDIAGONALSTRIKE+" "+MML.NOTATION.UPDIAGONALARROW},attr);value=MML.mpadded(value).With({depth:"-.1em",height:"+.1em",voffset:".1em"}),this.Push(MML.msup(MML.menclose(math).With(def),value))}}),MathJax.Hub.Startup.signal.Post("TeX cancel Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/cancel.js"),MathJax.Extension["TeX/color"]={version:"2.7.5",config:MathJax.Hub.CombineConfig("TeX.color",{padding:"5px",border:"2px"}),colors:{Apricot:"#FBB982",Aquamarine:"#00B5BE",Bittersweet:"#C04F17",Black:"#221E1F",Blue:"#2D2F92",BlueGreen:"#00B3B8",BlueViolet:"#473992",BrickRed:"#B6321C",Brown:"#792500",BurntOrange:"#F7921D",CadetBlue:"#74729A",CarnationPink:"#F282B4",Cerulean:"#00A2E3",CornflowerBlue:"#41B0E4",Cyan:"#00AEEF",Dandelion:"#FDBC42",DarkOrchid:"#A4538A",Emerald:"#00A99D",ForestGreen:"#009B55",Fuchsia:"#8C368C",Goldenrod:"#FFDF42",Gray:"#949698",Green:"#00A64F",GreenYellow:"#DFE674",JungleGreen:"#00A99A",Lavender:"#F49EC4",LimeGreen:"#8DC73E",Magenta:"#EC008C",Mahogany:"#A9341F",Maroon:"#AF3235",Melon:"#F89E7B",MidnightBlue:"#006795",Mulberry:"#A93C93",NavyBlue:"#006EB8",OliveGreen:"#3C8031",Orange:"#F58137",OrangeRed:"#ED135A",Orchid:"#AF72B0",Peach:"#F7965A",Periwinkle:"#7977B8",PineGreen:"#008B72",Plum:"#92268F",ProcessBlue:"#00B0F0",Purple:"#99479B",RawSienna:"#974006",Red:"#ED1B23",RedOrange:"#F26035",RedViolet:"#A1246B",Rhodamine:"#EF559F",RoyalBlue:"#0071BC",RoyalPurple:"#613F99",RubineRed:"#ED017D",Salmon:"#F69289",SeaGreen:"#3FBC9D",Sepia:"#671800",SkyBlue:"#46C5DD",SpringGreen:"#C6DC67",Tan:"#DA9D76",TealBlue:"#00AEB3",Thistle:"#D883B7",Turquoise:"#00B4CE",Violet:"#58429B",VioletRed:"#EF58A0",White:"#FFFFFF",WildStrawberry:"#EE2967",Yellow:"#FFF200",YellowGreen:"#98CC70",YellowOrange:"#FAA21A"},getColor:function(model,def){model||(model="named");var fn=this["get_"+model];return fn||this.TEX.Error(["UndefinedColorModel","Color model '%1' not defined",model]),fn.call(this,def)},get_rgb:function(rgb){var RGB="#";3!==(rgb=rgb.replace(/^\s+/,"").replace(/\s+$/,"").split(/\s*,\s*/)).length&&this.TEX.Error(["ModelArg1","Color values for the %1 model require 3 numbers","rgb"]);for(var i=0;i<3;i++){rgb[i].match(/^(\d+(\.\d*)?|\.\d+)$/)||this.TEX.Error(["InvalidDecimalNumber","Invalid decimal number"]);var n=parseFloat(rgb[i]);(n<0||n>1)&&this.TEX.Error(["ModelArg2","Color values for the %1 model must be between %2 and %3","rgb",0,1]),(n=Math.floor(255*n).toString(16)).length<2&&(n="0"+n),RGB+=n}return RGB},get_RGB:function(rgb){var RGB="#";3!==(rgb=rgb.replace(/^\s+/,"").replace(/\s+$/,"").split(/\s*,\s*/)).length&&this.TEX.Error(["ModelArg1","Color values for the %1 model require 3 numbers","RGB"]);for(var i=0;i<3;i++){rgb[i].match(/^\d+$/)||this.TEX.Error(["InvalidNumber","Invalid number"]);var n=parseInt(rgb[i]);n>255&&this.TEX.Error(["ModelArg2","Color values for the %1 model must be between %2 and %3","RGB",0,255]),(n=n.toString(16)).length<2&&(n="0"+n),RGB+=n}return RGB},get_gray:function(gray){gray.match(/^\s*(\d+(\.\d*)?|\.\d+)\s*$/)||this.TEX.Error(["InvalidDecimalNumber","Invalid decimal number"]);var n=parseFloat(gray);return(n<0||n>1)&&this.TEX.Error(["ModelArg2","Color values for the %1 model must be between %2 and %3","gray",0,1]),(n=Math.floor(255*n).toString(16)).length<2&&(n="0"+n),"#"+n+n+n},get_named:function(name){return this.colors.hasOwnProperty(name)?this.colors[name]:name},padding:function(){var pad="+"+this.config.padding,unit=this.config.padding.replace(/^.*?([a-z]*)$/,"$1"),pad2;return{width:"+"+2*parseFloat(pad)+unit,height:pad,depth:pad,lspace:this.config.padding}}},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,MML=MathJax.ElementJax.mml,STACKITEM=TEX.Stack.Item,COLOR=MathJax.Extension["TeX/color"];COLOR.TEX=TEX,TEX.Definitions.Add({macros:{color:"Color",textcolor:"TextColor",definecolor:"DefineColor",colorbox:"ColorBox",fcolorbox:"fColorBox"}},null,!0),TEX.Parse.Augment({Color:function(name){var model=this.GetBrackets(name),color=this.GetArgument(name);color=COLOR.getColor(model,color);var mml=STACKITEM.style().With({styles:{mathcolor:color}});this.stack.env.color=color,this.Push(mml)},TextColor:function(name){var model=this.GetBrackets(name),color=this.GetArgument(name);color=COLOR.getColor(model,color);var old=this.stack.env.color;this.stack.env.color=color;var math=this.ParseArg(name);old?this.stack.env.color:delete this.stack.env.color,this.Push(MML.mstyle(math).With({mathcolor:color}))},DefineColor:function(name){var cname=this.GetArgument(name),model=this.GetArgument(name),def=this.GetArgument(name);COLOR.colors[cname]=COLOR.getColor(model,def)},ColorBox:function(name){var cname=this.GetArgument(name),arg=this.InternalMath(this.GetArgument(name));this.Push(MML.mpadded.apply(MML,arg).With({mathbackground:COLOR.getColor("named",cname)}).With(COLOR.padding()))},fColorBox:function(name){var fname=this.GetArgument(name),cname=this.GetArgument(name),arg=this.InternalMath(this.GetArgument(name));this.Push(MML.mpadded.apply(MML,arg).With({mathbackground:COLOR.getColor("named",cname),style:"border: "+COLOR.config.border+" solid "+COLOR.getColor("named",fname)}).With(COLOR.padding()))}}),MathJax.Hub.Startup.signal.Post("TeX color Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/color.js"),MathJax.Extension["TeX/enclose"]={version:"2.7.5",ALLOWED:{arrow:1,color:1,mathcolor:1,background:1,mathbackground:1,padding:1,thickness:1}},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,MML=MathJax.ElementJax.mml,ALLOW=MathJax.Extension["TeX/enclose"].ALLOWED;TEX.Definitions.Add({macros:{enclose:"Enclose"}},null,!0),TEX.Parse.Augment({Enclose:function(name){var notation=this.GetArgument(name),attr=this.GetBrackets(name),math=this.ParseArg(name),def={notation:notation.replace(/,/g," ")};if(attr)for(var i=0,m=(attr=attr.replace(/ /g,"").split(/,/)).length;i<m;i++){var keyvalue=attr[i].split(/[:=]/);ALLOW[keyvalue[0]]&&(keyvalue[1]=keyvalue[1].replace(/^"(.*)"$/,"$1"),"true"===keyvalue[1]&&(keyvalue[1]=!0),"false"===keyvalue[1]&&(keyvalue[1]=!1),"arrow"===keyvalue[0]&&keyvalue[1]?def.notation=def.notation+" updiagonalarrow":def[keyvalue[0]]=keyvalue[1])}this.Push(MML.menclose(math).With(def))}}),MathJax.Hub.Startup.signal.Post("TeX enclose Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/enclose.js"),MathJax.Extension["TeX/extpfeil"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,TEXDEF=TEX.Definitions;TEXDEF.Add({macros:{xtwoheadrightarrow:["Extension","AMSmath"],xtwoheadleftarrow:["Extension","AMSmath"],xmapsto:["Extension","AMSmath"],xlongequal:["Extension","AMSmath"],xtofrom:["Extension","AMSmath"],Newextarrow:["Extension","AMSmath"]}},null,!0),MathJax.Hub.Register.StartupHook("TeX AMSmath Ready",(function(){MathJax.Hub.Insert(TEXDEF,{macros:{xtwoheadrightarrow:["xArrow",8608,12,16],xtwoheadleftarrow:["xArrow",8606,17,13],xmapsto:["xArrow",8614,6,7],xlongequal:["xArrow",61,7,7],xtofrom:["xArrow",8644,12,12],Newextarrow:"NewExtArrow"}})})),TEX.Parse.Augment({NewExtArrow:function(name){var cs=this.GetArgument(name),space=this.GetArgument(name),chr=this.GetArgument(name);cs.match(/^\\([a-z]+|.)$/i)||TEX.Error(["NewextarrowArg1","First argument to %1 must be a control sequence name",name]),space.match(/^(\d+),(\d+)$/)||TEX.Error(["NewextarrowArg2","Second argument to %1 must be two integers separated by a comma",name]),chr.match(/^(\d+|0x[0-9A-F]+)$/i)||TEX.Error(["NewextarrowArg3","Third argument to %1 must be a unicode character number",name]),cs=cs.substr(1),space=space.split(","),chr=parseInt(chr),this.setDef(cs,["xArrow",chr,parseInt(space[0]),parseInt(space[1])])}}),MathJax.Hub.Startup.signal.Post("TeX extpfeil Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/extpfeil.js"),MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var VERSION="2.7.5",MML=MathJax.ElementJax.mml,TEX=MathJax.InputJax.TeX,TEXDEF;TEX.Definitions.Add({macros:{mathchoice:"MathChoice"}},null,!0),TEX.Parse.Augment({MathChoice:function(name){var D=this.ParseArg(name),T=this.ParseArg(name),S=this.ParseArg(name),SS=this.ParseArg(name);this.Push(MML.TeXmathchoice(D,T,S,SS))}}),MML.TeXmathchoice=MML.mbase.Subclass({type:"TeXmathchoice",notParent:!0,choice:function(){if(null!=this.selection)return this.selection;if(this.choosing)return 2;this.choosing=!0;var selection=0,values=this.getValues("displaystyle","scriptlevel");selection=values.scriptlevel>0?Math.min(3,values.scriptlevel+1):values.displaystyle?0:1;for(var node=this.inherit;node&&"math"!==node.type;)node=node.inherit;return node&&(this.selection=selection),this.choosing=!1,selection},selected:function(){return this.data[this.choice()]},setTeXclass:function(prev){return this.selected().setTeXclass(prev)},isSpacelike:function(){return this.selected().isSpacelike()},isEmbellished:function(){return this.selected().isEmbellished()},Core:function(){return this.selected()},CoreMO:function(){return this.selected().CoreMO()},toHTML:function(span){return(span=this.HTMLcreateSpan(span)).bbox=this.Core().toHTML(span).bbox,span.firstChild&&span.firstChild.style.marginLeft&&(span.style.marginLeft=span.firstChild.style.marginLeft,span.firstChild.style.marginLeft=""),span},toSVG:function(){var svg=this.Core().toSVG();return this.SVGsaveData(svg),svg},toCommonHTML:function(node){return node=this.CHTMLcreateNode(node),this.CHTMLhandleStyle(node),this.CHTMLhandleColor(node),this.CHTMLaddChild(node,this.choice(),{}),node},toPreviewHTML:function(span){return span=this.PHTMLcreateSpan(span),this.PHTMLhandleStyle(span),this.PHTMLhandleColor(span),this.PHTMLaddChild(span,this.choice(),{}),span}}),MathJax.Hub.Startup.signal.Post("TeX mathchoice Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/mathchoice.js"),MathJax.Extension["TeX/mediawiki-texvc"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){MathJax.InputJax.TeX.Definitions.Add({macros:{AA:["Macro","Å"],alef:["Macro","\\aleph"],alefsym:["Macro","\\aleph"],Alpha:["Macro","\\mathrm{A}"],and:["Macro","\\land"],ang:["Macro","\\angle"],Bbb:["Macro","\\mathbb"],Beta:["Macro","\\mathrm{B}"],bold:["Macro","\\mathbf"],bull:["Macro","\\bullet"],C:["Macro","\\mathbb{C}"],Chi:["Macro","\\mathrm{X}"],clubs:["Macro","\\clubsuit"],cnums:["Macro","\\mathbb{C}"],Complex:["Macro","\\mathbb{C}"],coppa:["Macro","ϙ"],Coppa:["Macro","Ϙ"],Dagger:["Macro","\\ddagger"],Digamma:["Macro","Ϝ"],darr:["Macro","\\downarrow"],dArr:["Macro","\\Downarrow"],Darr:["Macro","\\Downarrow"],dashint:["Macro","\\unicodeInt{x2A0D}"],ddashint:["Macro","\\unicodeInt{x2A0E}"],diamonds:["Macro","\\diamondsuit"],empty:["Macro","\\emptyset"],Epsilon:["Macro","\\mathrm{E}"],Eta:["Macro","\\mathrm{H}"],euro:["Macro","€"],exist:["Macro","\\exists"],geneuro:["Macro","€"],geneuronarrow:["Macro","€"],geneurowide:["Macro","€"],H:["Macro","\\mathbb{H}"],hAar:["Macro","\\Leftrightarrow"],harr:["Macro","\\leftrightarrow"],Harr:["Macro","\\Leftrightarrow"],hearts:["Macro","\\heartsuit"],image:["Macro","\\Im"],infin:["Macro","\\infty"],Iota:["Macro","\\mathrm{I}"],isin:["Macro","\\in"],Kappa:["Macro","\\mathrm{K}"],koppa:["Macro","ϟ"],Koppa:["Macro","Ϟ"],lang:["Macro","\\langle"],larr:["Macro","\\leftarrow"],Larr:["Macro","\\Leftarrow"],lArr:["Macro","\\Leftarrow"],lrarr:["Macro","\\leftrightarrow"],Lrarr:["Macro","\\Leftrightarrow"],lrArr:["Macro","\\Leftrightarrow"],Mu:["Macro","\\mathrm{M}"],N:["Macro","\\mathbb{N}"],natnums:["Macro","\\mathbb{N}"],Nu:["Macro","\\mathrm{N}"],O:["Macro","\\emptyset"],oint:["Macro","\\unicodeInt{x222E}"],oiint:["Macro","\\unicodeInt{x222F}"],oiiint:["Macro","\\unicodeInt{x2230}"],ointctrclockwise:["Macro","\\unicodeInt{x2233}"],officialeuro:["Macro","€"],Omicron:["Macro","\\mathrm{O}"],or:["Macro","\\lor"],P:["Macro","¶"],pagecolor:["Macro","",1],part:["Macro","\\partial"],plusmn:["Macro","\\pm"],Q:["Macro","\\mathbb{Q}"],R:["Macro","\\mathbb{R}"],rang:["Macro","\\rangle"],rarr:["Macro","\\rightarrow"],Rarr:["Macro","\\Rightarrow"],rArr:["Macro","\\Rightarrow"],real:["Macro","\\Re"],reals:["Macro","\\mathbb{R}"],Reals:["Macro","\\mathbb{R}"],Rho:["Macro","\\mathrm{P}"],sdot:["Macro","\\cdot"],sampi:["Macro","ϡ"],Sampi:["Macro","Ϡ"],sect:["Macro","\\S"],spades:["Macro","\\spadesuit"],stigma:["Macro","ϛ"],Stigma:["Macro","Ϛ"],sub:["Macro","\\subset"],sube:["Macro","\\subseteq"],supe:["Macro","\\supseteq"],Tau:["Macro","\\mathrm{T}"],textvisiblespace:["Macro","␣"],thetasym:["Macro","\\vartheta"],uarr:["Macro","\\uparrow"],uArr:["Macro","\\Uparrow"],Uarr:["Macro","\\Uparrow"],unicodeInt:["Macro","\\mathop{\\vcenter{\\mathchoice{\\huge\\unicode{#1}\\,}{\\unicode{#1}}{\\unicode{#1}}{\\unicode{#1}}}\\,}\\nolimits",1],varcoppa:["Macro","ϙ"],varstigma:["Macro","ϛ"],varointclockwise:["Macro","\\unicodeInt{x2232}"],vline:["Macro","\\smash{\\large\\lvert}",0],weierp:["Macro","\\wp"],Z:["Macro","\\mathbb{Z}"],Zeta:["Macro","\\mathrm{Z}"]}})})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/mediawiki-texvc.js"),MathJax.Extension["TeX/mhchem"]?MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/mhchem.js"):(MathJax.Extension["TeX/mhchem"]={version:"2.7.5",config:MathJax.Hub.CombineConfig("TeX.mhchem",{legacy:!0})},MathJax.Extension["TeX/mhchem"].config.legacy?(MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,CE=MathJax.Object.Subclass({string:"",i:0,tex:"",TEX:"",atom:!1,sup:"",sub:"",presup:"",presub:"",Init:function(string){this.string=string},ParseTable:{"-":"Minus","+":"Plus","(":"Open",")":"Close","[":"Open","]":"Close","<":"Less","^":"Superscript",_:"Subscript","*":"Dot",".":"Dot","=":"Equal","#":"Pound",$:"Math","\\":"Macro"," ":"Space"},Arrows:{"->":"rightarrow","<-":"leftarrow","<->":"leftrightarrow","<=>":"rightleftharpoons","<=>>":"Rightleftharpoons","<<=>":"Leftrightharpoons","^":"uparrow",v:"downarrow"},Bonds:{"-":"-","=":"=","#":"\\equiv","~":"\\tripledash","~-":"\\begin{CEstack}{}\\tripledash\\\\-\\end{CEstack}","~=":"\\raise2mu{\\begin{CEstack}{}\\tripledash\\\\-\\\\-\\end{CEstack}}","~--":"\\raise2mu{\\begin{CEstack}{}\\tripledash\\\\-\\\\-\\end{CEstack}}","-~-":"\\raise2mu{\\begin{CEstack}{}-\\\\\\tripledash\\\\-\\end{CEstack}}","...":"{\\cdot}{\\cdot}{\\cdot}","....":"{\\cdot}{\\cdot}{\\cdot}{\\cdot}","->":"\\rightarrow","<-":"\\leftarrow","??":"\\text{??}"},Parse:function(){for(this.tex="",this.atom=!1;this.i<this.string.length;){var c=this.string.charAt(this.i);c.match(/[a-z]/i)?this.ParseLetter():c.match(/[0-9]/)?this.ParseNumber():this["Parse"+(this.ParseTable[c]||"Other")](c)}return this.FinishAtom(!0),this.TEX},ParseLetter:function(){this.FinishAtom(),this.Match(/^v( |$)/)?this.tex+="{\\"+this.Arrows.v+"}":(this.tex+="\\text{"+this.Match(/^[a-z]+/i)+"}",this.atom=!0)},ParseNumber:function(){var n=this.Match(/^\d+/);if(this.atom&&!this.sub)this.sub=n;else{this.FinishAtom();var match=this.Match(/^\/\d+/);if(match){var frac="\\frac{"+n+"}{"+match.substr(1)+"}";this.tex+="\\mathchoice{\\textstyle"+frac+"}{"+frac+"}{"+frac+"}{"+frac+"}"}else this.tex+=n,this.i<this.string.length&&(this.tex+="\\,")}},ParseMinus:function(c){if(!this.atom||this.i!==this.string.length-1&&" "!==this.string.charAt(this.i+1)){if(this.FinishAtom(),"->"===this.string.substr(this.i,2))return this.i+=2,void this.AddArrow("->");this.tex+="{-}"}else this.sup+=c;this.i++},ParsePlus:function(c){this.atom?this.sup+=c:(this.FinishAtom(),this.tex+=c),this.i++},ParseDot:function(c){this.FinishAtom(),this.tex+="\\cdot ",this.i++},ParseEqual:function(c){this.FinishAtom(),this.tex+="{=}",this.i++},ParsePound:function(c){this.FinishAtom(),this.tex+="{\\equiv}",this.i++},ParseOpen:function(c){this.FinishAtom();var match=this.Match(/^\([v^]\)/);match?this.tex+="{\\"+this.Arrows[match.charAt(1)]+"}":(this.tex+="{"+c,this.i++)},ParseClose:function(c){this.FinishAtom(),this.atom=!0,this.tex+=c+"}",this.i++},ParseLess:function(c){this.FinishAtom();var arrow=this.Match(/^(<->?|<=>>?|<<=>)/);arrow?this.AddArrow(arrow):(this.tex+=c,this.i++)},ParseSuperscript:function(c){if("{"===(c=this.string.charAt(++this.i))){this.i++;var m=this.Find("}");"-."===m?this.sup+="{-}{\\cdot}":m&&(this.sup+=CE(m).Parse().replace(/^\{-\}/,"-"))}else if(" "===c||""===c)this.tex+="{\\"+this.Arrows["^"]+"}",this.i++;else{var n=this.Match(/^(\d+|-\.)/);n&&(this.sup+=n)}},ParseSubscript:function(c){if("{"==this.string.charAt(++this.i))this.i++,this.sub+=CE(this.Find("}")).Parse().replace(/^\{-\}/,"-");else{var n=this.Match(/^\d+/);n&&(this.sub+=n)}},ParseMath:function(c){this.FinishAtom(),this.i++,this.tex+=this.Find(c)},ParseMacro:function(c){this.FinishAtom(),this.i++;var match=this.Match(/^([a-z]+|.)/i)||" ";if("sbond"===match)this.tex+="{-}";else if("dbond"===match)this.tex+="{=}";else if("tbond"===match)this.tex+="{\\equiv}";else if("bond"===match){var bond=this.Match(/^\{.*?\}/)||"";bond=bond.substr(1,bond.length-2),this.tex+="{"+(this.Bonds[bond]||"\\text{??}")+"}"}else"{"===match?this.tex+="{\\{":"}"===match?(this.tex+="\\}}",this.atom=!0):this.tex+=c+match},ParseSpace:function(c){this.FinishAtom(),this.i++},ParseOther:function(c){this.FinishAtom(),this.tex+=c,this.i++},AddArrow:function(arrow){var c=this.Match(/^[CT]\[/);c&&(this.i--,c=c.charAt(0));var above=this.GetBracket(c),below=this.GetBracket(c);arrow=this.Arrows[arrow],above||below?(below&&(arrow+="["+below+"]"),arrow="\\mathrel{\\x"+(arrow+="{"+above+"}")+"}"):arrow="\\long"+arrow+" ",this.tex+=arrow},FinishAtom:function(force){if(this.sup||this.sub||this.presup||this.presub){if(!force&&!this.atom){if(""===this.tex&&!this.sup&&!this.sub)return;if(!this.presup&&!this.presub&&(""===this.tex||"{"===this.tex||"}"===this.tex&&"{"===this.TEX.substr(-1)))return this.presup=this.sup,this.presub=this.sub,this.sub=this.sup="",this.TEX+=this.tex,void(this.tex="")}this.sub&&!this.sup&&(this.sup="\\Space{0pt}{0pt}{.2em}"),(this.presup||this.presub)&&"{"!==this.tex?(this.presup||this.sup||(this.presup="\\Space{0pt}{0pt}{.2em}"),this.tex="\\CEprescripts{"+(this.presub||"\\CEnone")+"}{"+(this.presup||"\\CEnone")+"}{"+("}"!==this.tex?this.tex:"")+"}{"+(this.sub||"\\CEnone")+"}{"+(this.sup||"\\CEnone")+"}"+("}"===this.tex?"}":""),this.presub=this.presup=""):(this.sup&&(this.tex+="^{"+this.sup+"}"),this.sub&&(this.tex+="_{"+this.sub+"}")),this.sup=this.sub=""}this.TEX+=this.tex,this.tex="",this.atom=!1},GetBracket:function(c){if("["!==this.string.charAt(this.i))return"";this.i++;var bracket=this.Find("]");return"C"===c?bracket="\\ce{"+bracket+"}":"T"===c&&(bracket.match(/^\{.*\}$/)||(bracket="{"+bracket+"}"),bracket="\\text"+bracket),bracket},Match:function(regex){var match=regex.exec(this.string.substr(this.i));return match&&(match=match[0],this.i+=match.length),match},Find:function(c){for(var m=this.string.length,i=this.i,braces=0;this.i<m;){var C=this.string.charAt(this.i++);if(C===c&&0===braces)return this.string.substr(i,this.i-i-1);"{"===C?braces++:"}"===C&&(braces?braces--:TEX.Error(["ExtraCloseMissingOpen","Extra close brace or missing open brace"]))}braces&&TEX.Error(["MissingCloseBrace","Missing close brace"]),TEX.Error(["NoClosingChar","Can't find closing %1",c])}});MathJax.Extension["TeX/mhchem"].CE=CE,TEX.Definitions.Add({macros:{ce:"CE",cf:"CE",cee:"CE",xleftrightarrow:["Extension","AMSmath"],xrightleftharpoons:["Extension","AMSmath"],xRightleftharpoons:["Extension","AMSmath"],xLeftrightharpoons:["Extension","AMSmath"],longrightleftharpoons:["Macro","\\stackrel{\\textstyle{{-}\\!\\!{\\rightharpoonup}}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longRightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\small\\smash\\leftharpoondown}"],longLeftrightharpoons:["Macro","\\stackrel{\\rightharpoonup}{{{\\leftharpoondown}\\!\\!\\textstyle{-}}}"],hyphen:["Macro","\\text{-}"],CEprescripts:"CEprescripts",CEnone:"CEnone",tripledash:["Macro","\\raise3mu{\\tiny\\text{-}\\kern2mu\\text{-}\\kern2mu\\text{-}}"]},environment:{CEstack:["Array",null,null,null,"r",null,"0.001em","T",1]}},null,!0),MathJax.Extension["TeX/AMSmath"]||TEX.Definitions.Add({macros:{xrightarrow:["Extension","AMSmath"],xleftarrow:["Extension","AMSmath"]}},null,!0),MathJax.Hub.Register.StartupHook("TeX AMSmath Ready",(function(){TEX.Definitions.Add({macros:{xleftrightarrow:["xArrow",8596,6,6],xrightleftharpoons:["xArrow",8652,5,7],xRightleftharpoons:["xArrow",8652,5,7],xLeftrightharpoons:["xArrow",8652,5,7]}},null,!0)})),TEX.Parse.Augment({CE:function(name){var arg=this.GetArgument(name),tex=CE(arg).Parse();this.string=tex+this.string.substr(this.i),this.i=0},CEprescripts:function(name){var presub=this.ParseArg(name),presup=this.ParseArg(name),base=this.ParseArg(name),sub=this.ParseArg(name),sup=this.ParseArg(name),MML=MathJax.ElementJax.mml;this.Push(MML.mmultiscripts(base,sub,sup,MML.mprescripts(),presub,presup))},CEnone:function(name){this.Push(MathJax.ElementJax.mml.none())}}),MathJax.Hub.Startup.signal.Post("TeX mhchem Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/mhchem.js")):(MathJax.Ajax.config.path.mhchem||(MathJax.Ajax.config.path.mhchem=MathJax.Hub.config.root+"/extensions/TeX/mhchem3"),MathJax.Callback.Queue(["Require",MathJax.Ajax,"[mhchem]/mhchem.js"],["loadComplete",MathJax.Ajax,"[MathJax]/extensions/TeX/mhchem.js"]))),MathJax.Extension["TeX/newcommand"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,TEXDEF=TEX.Definitions;TEXDEF.Add({macros:{newcommand:"NewCommand",renewcommand:"NewCommand",newenvironment:"NewEnvironment",renewenvironment:"NewEnvironment",def:"MacroDef",let:"Let"}},null,!0),TEX.Parse.Augment({NewCommand:function(name){var cs=this.trimSpaces(this.GetArgument(name)),n=this.GetBrackets(name),opt=this.GetBrackets(name),def=this.GetArgument(name);"\\"===cs.charAt(0)&&(cs=cs.substr(1)),cs.match(/^(.|[a-z]+)$/i)||TEX.Error(["IllegalControlSequenceName","Illegal control sequence name for %1",name]),n&&((n=this.trimSpaces(n)).match(/^[0-9]+$/)||TEX.Error(["IllegalParamNumber","Illegal number of parameters specified in %1",name])),this.setDef(cs,["Macro",def,n,opt])},NewEnvironment:function(name){var env=this.trimSpaces(this.GetArgument(name)),n=this.GetBrackets(name),opt=this.GetBrackets(name),bdef=this.GetArgument(name),edef=this.GetArgument(name);n&&((n=this.trimSpaces(n)).match(/^[0-9]+$/)||TEX.Error(["IllegalParamNumber","Illegal number of parameters specified in %1",name])),this.setEnv(env,["BeginEnv",[null,"EndEnv"],bdef,edef,n,opt])},MacroDef:function(name){var cs=this.GetCSname(name),params=this.GetTemplate(name,"\\"+cs),def=this.GetArgument(name);params instanceof Array?this.setDef(cs,["MacroWithTemplate",def].concat(params)):this.setDef(cs,["Macro",def,params])},Let:function(name){var cs=this.GetCSname(name),macro,c=this.GetNext();if("="===c&&(this.i++,c=this.GetNext()),"\\"===c){if(name=this.GetCSname(name),!(macro=this.csFindMacro(name)))if(TEXDEF.mathchar0mi.hasOwnProperty(name))macro=["csMathchar0mi",TEXDEF.mathchar0mi[name]];else if(TEXDEF.mathchar0mo.hasOwnProperty(name))macro=["csMathchar0mo",TEXDEF.mathchar0mo[name]];else if(TEXDEF.mathchar7.hasOwnProperty(name))macro=["csMathchar7",TEXDEF.mathchar7[name]];else{if(!TEXDEF.delimiter.hasOwnProperty("\\"+name))return;macro=["csDelimiter",TEXDEF.delimiter["\\"+name]]}}else macro=["Macro",c],this.i++;this.setDef(cs,macro)},GetCSname:function(cmd){var c,cs;return"\\"!==this.GetNext()&&TEX.Error(["MissingCS","%1 must be followed by a control sequence",cmd]),this.trimSpaces(this.GetArgument(cmd)).substr(1)},GetTemplate:function(cmd,cs){var c,params=[],n=0;c=this.GetNext();for(var i=this.i;this.i<this.string.length;){if("#"===(c=this.GetNext()))i!==this.i&&(params[n]=this.string.substr(i,this.i-i)),(c=this.string.charAt(++this.i)).match(/^[1-9]$/)||TEX.Error(["CantUseHash2","Illegal use of # in template for %1",cs]),parseInt(c)!=++n&&TEX.Error(["SequentialParam","Parameters for %1 must be numbered sequentially",cs]),i=this.i+1;else if("{"===c)return i!==this.i&&(params[n]=this.string.substr(i,this.i-i)),params.length>0?[n,params]:n;this.i++}TEX.Error(["MissingReplacementString","Missing replacement string for definition of %1",cmd])},MacroWithTemplate:function(name,text,n,params){if(n){var args=[];this.GetNext(),params[0]&&!this.MatchParam(params[0])&&TEX.Error(["MismatchUseDef","Use of %1 doesn't match its definition",name]);for(var i=0;i<n;i++)args.push(this.GetParameter(name,params[i+1]));text=this.SubstituteArgs(args,text)}this.string=this.AddArgs(text,this.string.slice(this.i)),this.i=0,++this.macroCount>TEX.config.MAXMACROS&&TEX.Error(["MaxMacroSub1","MathJax maximum macro substitution count exceeded; is there a recursive macro call?"])},BeginEnv:function(begin,bdef,edef,n,def){if(n){var args=[];if(null!=def){var optional=this.GetBrackets("\\begin{"+name+"}");args.push(null==optional?def:optional)}for(var i=args.length;i<n;i++)args.push(this.GetArgument("\\begin{"+name+"}"));bdef=this.SubstituteArgs(args,bdef),edef=this.SubstituteArgs([],edef)}return this.string=this.AddArgs(bdef,this.string.slice(this.i)),this.i=0,begin},EndEnv:function(begin,bdef,edef,n){var end="\\end{\\end\\"+begin.name+"}";return this.string=this.AddArgs(edef,end+this.string.slice(this.i)),this.i=0,null},GetParameter:function(name,param){if(null==param)return this.GetArgument(name);for(var i=this.i,j=0,hasBraces=0;this.i<this.string.length;){var c=this.string.charAt(this.i);if("{"===c)this.i===i&&(hasBraces=1),this.GetArgument(name),j=this.i-i;else{if(this.MatchParam(param))return hasBraces&&(i++,j-=2),this.string.substr(i,j);if("\\"===c){this.i++,j++,hasBraces=0;var match=this.string.substr(this.i).match(/[a-z]+|./i);match&&(this.i+=match[0].length,j=this.i-i)}else this.i++,j++,hasBraces=0}}TEX.Error(["RunawayArgument","Runaway argument for %1?",name])},MatchParam:function(param){return this.string.substr(this.i,param.length)!==param?0:param.match(/\\[a-z]+$/i)&&this.string.charAt(this.i+param.length).match(/[a-z]/i)?0:(this.i+=param.length,1)}}),TEX.Environment=function(name){TEXDEF.environment[name]=["BeginEnv",[null,"EndEnv"]].concat([].slice.call(arguments,1)),TEXDEF.environment[name].isUser=!0},MathJax.Hub.Startup.signal.Post("TeX newcommand Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/newcommand.js"),MathJax.Extension["TeX/unicode"]={version:"2.7.5",unicode:{},config:MathJax.Hub.CombineConfig("TeX.unicode",{fonts:"STIXGeneral,'Arial Unicode MS'"})},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var TEX=MathJax.InputJax.TeX,MML=MathJax.ElementJax.mml,UNICODE=MathJax.Extension["TeX/unicode"].unicode;TEX.Definitions.Add({macros:{unicode:"Unicode"}},null,!0),TEX.Parse.Augment({Unicode:function(name){var HD=this.GetBrackets(name),font;HD&&(HD.replace(/ /g,"").match(/^(\d+(\.\d*)?|\.\d+),(\d+(\.\d*)?|\.\d+)$/)?(HD=HD.replace(/ /g,"").split(/,/),font=this.GetBrackets(name)):(font=HD,HD=null));var n=this.trimSpaces(this.GetArgument(name)).replace(/^0x/,"x");n.match(/^(x[0-9A-Fa-f]+|[0-9]+)$/)||TEX.Error(["BadUnicode","Argument to \\unicode must be a number"]);var N=parseInt(n.match(/^x/)?"0"+n:n);UNICODE[N]?font||(font=UNICODE[N][2]):UNICODE[N]=[800,200,font,N],HD&&(UNICODE[N][0]=Math.floor(1e3*HD[0]),UNICODE[N][1]=Math.floor(1e3*HD[1]));var variant=this.stack.env.font,def={};font?(UNICODE[N][2]=def.fontfamily=font.replace(/"/g,"'"),variant&&(variant.match(/bold/)&&(def.fontweight="bold"),variant.match(/italic|-mathit/)&&(def.fontstyle="italic"))):variant&&(def.mathvariant=variant),def.unicode=[].concat(UNICODE[N]),this.Push(MML.mtext(MML.entity("#"+n)).With(def))}}),MathJax.Hub.Startup.signal.Post("TeX unicode Ready")})),MathJax.Hub.Register.StartupHook("HTML-CSS Jax Ready",(function(){var MML=MathJax.ElementJax.mml,FONTS=MathJax.Extension["TeX/unicode"].config.fonts,GETVARIANT=MML.mbase.prototype.HTMLgetVariant;MML.mbase.Augment({HTMLgetVariant:function(){var variant=GETVARIANT.apply(this,arguments);if(variant.unicode&&(delete variant.unicode,delete variant.FONTS),!this.unicode)return variant;variant.unicode=!0,variant.defaultFont||((variant=MathJax.Hub.Insert({},variant)).defaultFont={family:FONTS});var family=this.unicode[2];return family?family+=","+FONTS:family=FONTS,variant.defaultFont[this.unicode[3]]=[this.unicode[0],this.unicode[1],500,0,500,{isUnknown:!0,isUnicode:!0,font:family}],variant}})})),MathJax.Hub.Register.StartupHook("SVG Jax Ready",(function(){var MML=MathJax.ElementJax.mml,FONTS=MathJax.Extension["TeX/unicode"].config.fonts,GETVARIANT=MML.mbase.prototype.SVGgetVariant;MML.mbase.Augment({SVGgetVariant:function(){var variant=GETVARIANT.call(this);return variant.unicode&&(delete variant.unicode,delete variant.FONTS),this.unicode?(variant.unicode=!0,variant.forceFamily||(variant=MathJax.Hub.Insert({},variant)),variant.defaultFamily=FONTS,variant.noRemap=!0,variant.h=this.unicode[0],variant.d=this.unicode[1],variant):variant}})})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/unicode.js"),MathJax.Extension["TeX/verb"]={version:"2.7.5"},MathJax.Hub.Register.StartupHook("TeX Jax Ready",(function(){var MML=MathJax.ElementJax.mml,TEX=MathJax.InputJax.TeX,TEXDEF;TEX.Definitions.Add({macros:{verb:"Verb"}},null,!0),TEX.Parse.Augment({Verb:function(name){var c=this.GetNext(),start=++this.i;for(""==c&&TEX.Error(["MissingArgFor","Missing argument for %1",name]);this.i<this.string.length&&this.string.charAt(this.i)!=c;)this.i++;this.i==this.string.length&&TEX.Error(["NoClosingDelim","Can't find closing delimiter for %1",name]);var text=this.string.slice(start,this.i).replace(/ /g," ");this.i++,this.Push(MML.mtext(text).With({mathvariant:MML.VARIANT.MONOSPACE}))}}),MathJax.Hub.Startup.signal.Post("TeX verb Ready")})),MathJax.Ajax.loadComplete("[MathJax]/extensions/TeX/verb.js"),MathJax.OutputJax.CommonHTML.webfontDir="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.4/fonts/HTML-CSS",function(CHTML,MML,AJAX){var VERSION="2.7.5",MAIN="MathJax_Main",BOLD="MathJax_Main-Bold",ITALIC="MathJax_Math-Italic",AMS="MathJax_AMS",SIZE1="MathJax_Size1",SIZE2="MathJax_Size2",SIZE3="MathJax_Size3",SIZE4="MathJax_Size4",H="H",V="V",EXTRAH={load:"extra",dir:H},EXTRAV={load:"extra",dir:V},ARROWREP=[8722,MAIN,0,0,0,-.31,-.31],DARROWREP=[61,MAIN,0,0,0,0,.1],UNDEFINEDFAMILY=CHTML.config.undefinedFamily;MathJax.Hub.Insert(CHTML.config.styles,{".MJXc-TeX-unknown-R":{"font-family":UNDEFINEDFAMILY,"font-style":"normal","font-weight":"normal"},".MJXc-TeX-unknown-I":{"font-family":UNDEFINEDFAMILY,"font-style":"italic","font-weight":"normal"},".MJXc-TeX-unknown-B":{"font-family":UNDEFINEDFAMILY,"font-style":"normal","font-weight":"bold"},".MJXc-TeX-unknown-BI":{"font-family":UNDEFINEDFAMILY,"font-style":"italic","font-weight":"bold"}}),CHTML.TEX=CHTML.TEXDEF,CHTML.FONTDEF.TeX={version:"2.7.5",baselineskip:1.2,lineH:.8,lineD:.2,FONTS:{MathJax_AMS:"TeX/AMS-Regular.js","MathJax_Caligraphic-Bold":"TeX/Caligraphic-Bold.js",MathJax_Fraktur:"TeX/Fraktur-Regular.js","MathJax_Fraktur-Bold":"TeX/Fraktur-Bold.js","MathJax_Math-BoldItalic":"TeX/Math-BoldItalic.js",MathJax_SansSerif:"TeX/SansSerif-Regular.js","MathJax_SansSerif-Bold":"TeX/SansSerif-Bold.js","MathJax_SansSerif-Italic":"TeX/SansSerif-Italic.js",MathJax_Script:"TeX/Script-Regular.js",MathJax_Typewriter:"TeX/Typewriter-Regular.js"},UNKNOWN:{R:{className:"MJXc-TeX-unknown-R"},I:{className:"MJXc-TeX-unknown-I"},B:{className:"MJXc-TeX-unknown-B"},BI:{className:"MJXc-TeX-unknown-BI"}},VARIANT:{normal:{fonts:[MAIN,SIZE1,AMS],cache:{},offsetG:945,variantG:"italic",remap:{913:65,914:66,917:69,918:90,919:72,921:73,922:75,924:77,925:78,927:79,929:80,932:84,935:88,57696:[8594,"-TeX-vec"],8214:8741,8726:[8726,"-TeX-variant",!0],8463:[8463,"-TeX-variant",!0],8242:[39,"sans-serif-italic"],10744:[47,MML.VARIANT.ITALIC]}},bold:{fonts:[BOLD],bold:!0,cache:{},chain:"normal",offsetG:945,variantG:"bold-italic",remap:{913:65,914:66,917:69,918:90,919:72,921:73,922:75,924:77,925:78,927:79,929:80,932:84,935:88,10744:[47,"bold-italic"],57696:[8594,"-TeX-vec-bold"],8214:8741,8602:"↚",8603:"↛",8622:"↮",8653:"⇍",8654:"⇎",8655:"⇏",8708:"∄",8740:"∤",8742:"∦",8769:"≁",8775:"≇",8814:"≮",8815:"≯",8816:"≰",8817:"≱",8832:"⊀",8833:"⊁",8840:"⊈",8841:"⊉",8876:"⊬",8877:"⊭",8928:"⋠",8929:"⋡"}},italic:{fonts:[ITALIC,"MathJax_Main-Italic"],italic:!0,cache:{},chain:"normal",remap:{913:65,914:66,917:69,918:90,919:72,921:73,922:75,924:77,925:78,927:79,929:80,932:84,935:88}},"bold-italic":{fonts:["MathJax_Math-BoldItalic"],bold:!0,italic:!0,cache:{},chain:"bold",remap:{913:65,914:66,917:69,918:90,919:72,921:73,922:75,924:77,925:78,927:79,929:80,932:84,935:88}},"double-struck":{fonts:[AMS,MAIN,SIZE1],cache:{}},fraktur:{fonts:["MathJax_Fraktur"],cache:{},chain:"normal"},"bold-fraktur":{fonts:["MathJax_Fraktur-Bold"],bold:!0,cache:{},chain:"bold"},script:{fonts:["MathJax_Script"],cache:{},chain:"normal"},"bold-script":{fonts:["MathJax_Script"],bold:!0,cache:{},chain:"bold"},"sans-serif":{fonts:["MathJax_SansSerif"],cache:{},chain:"normal"},"bold-sans-serif":{fonts:["MathJax_SansSerif-Bold"],bold:!0,cache:{},chain:"bold"},"sans-serif-italic":{fonts:["MathJax_SansSerif-Italic"],italic:!0,cache:{},chain:"italic"},"sans-serif-bold-italic":{fonts:["MathJax_SansSerif-Italic"],bold:!0,italic:!0,cache:{},chain:"italic"},monospace:{fonts:["MathJax_Typewriter"],cache:{},chain:"normal"},"-tex-caligraphic":{fonts:["MathJax_Caligraphic"],offsetA:65,variantA:"italic",cache:{},chain:"normal"},"-tex-oldstyle":{fonts:["MathJax_Caligraphic"],cache:{},chain:"normal"},"-tex-mathit":{fonts:["MathJax_Main-Italic"],italic:!0,noIC:!0,cache:{},chain:"normal",remap:{913:65,914:66,917:69,918:90,919:72,921:73,922:75,924:77,925:78,927:79,929:80,932:84,935:88}},"-TeX-variant":{fonts:[AMS,MAIN,SIZE1],cache:{},remap:{8808:57356,8809:57357,8816:57361,8817:57358,10887:57360,10888:57359,8740:57350,8742:57351,8840:57366,8841:57368,8842:57370,8843:57371,10955:57367,10956:57369,988:57352,1008:57353,8726:[8726,MML.VARIANT.NORMAL,!0],8463:[8463,MML.VARIANT.NORMAL,!0]}},"-TeX-vec":{fonts:["MathJax_Vector"],cache:{}},"-TeX-vec-bold":{fonts:["MathJax_Vector-Bold"],cache:{}},"-largeOp":{fonts:[SIZE2,SIZE1,MAIN,AMS],cache:{}},"-smallOp":{fonts:[SIZE1,MAIN,AMS],cache:{}},"-tex-caligraphic-bold":{fonts:["MathJax_Caligraphic-Bold","MathJax_Main-Bold"],bold:!0,cache:{},chain:"normal",offsetA:65,variantA:"bold-italic"},"-tex-oldstyle-bold":{fonts:["MathJax_Caligraphic-Bold","MathJax_Main-Bold"],bold:!0,cache:{},chain:"normal"}},RANGES:[{name:"alpha",low:97,high:122,offset:"A",add:32},{name:"number",low:48,high:57,offset:"N"},{name:"greek",low:945,high:1014,offset:"G"}],REMAP:{10:32,8254:713,65079:9182,65080:9183,183:8901,697:8242,978:933,8710:916,8213:8212,8215:95,8226:8729,8260:47,8965:8892,8966:10846,9642:9632,9652:9650,9653:9651,9656:9654,9662:9660,9663:9661,9666:9664,9001:10216,9002:10217,12296:10216,12297:10217,10072:8739,10799:215,9723:9633,9724:9632,8450:[67,MML.VARIANT.DOUBLESTRUCK],8459:[72,MML.VARIANT.SCRIPT],8460:[72,MML.VARIANT.FRAKTUR],8461:[72,MML.VARIANT.DOUBLESTRUCK],8462:[104,MML.VARIANT.ITALIC],8464:[74,MML.VARIANT.SCRIPT],8465:[73,MML.VARIANT.FRAKTUR],8466:[76,MML.VARIANT.SCRIPT],8469:[78,MML.VARIANT.DOUBLESTRUCK],8473:[80,MML.VARIANT.DOUBLESTRUCK],8474:[81,MML.VARIANT.DOUBLESTRUCK],8475:[82,MML.VARIANT.SCRIPT],8476:[82,MML.VARIANT.FRAKTUR],8477:[82,MML.VARIANT.DOUBLESTRUCK],8484:[90,MML.VARIANT.DOUBLESTRUCK],8486:[937,MML.VARIANT.NORMAL],8488:[90,MML.VARIANT.FRAKTUR],8492:[66,MML.VARIANT.SCRIPT],8493:[67,MML.VARIANT.FRAKTUR],8496:[69,MML.VARIANT.SCRIPT],8497:[70,MML.VARIANT.SCRIPT],8499:[77,MML.VARIANT.SCRIPT],8775:8774,8988:9484,8989:9488,8990:9492,8991:9496,8708:"∄",8716:"∌",8772:"≄",8777:"≉",8802:"≢",8813:"≭",8820:"≴",8821:"≵",8824:"≸",8825:"≹",8836:"⊄",8837:"⊅",8930:"⋢",8931:"⋣",10764:"∬∬",8243:"′′",8244:"′′′",8246:"‵‵",8247:"‵‵‵",8279:"′′′′"},REMAPACCENT:{"̀":"ˋ","́":"ˊ","̂":"ˆ","̃":"˜","̄":"ˉ","̆":"˘","̇":"˙","̈":"¨","̊":"˚","̌":"ˇ","⃗":"","→":"","′":"'","‵":"`","⃐":"↼","⃑":"⇀","⃖":"←","⃡":"↔","⃰":"*","⃛":"...","⃜":"...."},REMAPACCENTUNDER:{"⃬":"⇁","⃭":"↽","⃮":"←","⃯":"→","⃛":"...","⃜":"...."},PLANE1MAP:[[119808,119833,65,MML.VARIANT.BOLD],[119834,119859,97,MML.VARIANT.BOLD],[119860,119885,65,MML.VARIANT.ITALIC],[119886,119911,97,MML.VARIANT.ITALIC],[119912,119937,65,MML.VARIANT.BOLDITALIC],[119938,119963,97,MML.VARIANT.BOLDITALIC],[119964,119989,65,MML.VARIANT.SCRIPT],[120068,120093,65,MML.VARIANT.FRAKTUR],[120094,120119,97,MML.VARIANT.FRAKTUR],[120120,120145,65,MML.VARIANT.DOUBLESTRUCK],[120172,120197,65,MML.VARIANT.BOLDFRAKTUR],[120198,120223,97,MML.VARIANT.BOLDFRAKTUR],[120224,120249,65,MML.VARIANT.SANSSERIF],[120250,120275,97,MML.VARIANT.SANSSERIF],[120276,120301,65,MML.VARIANT.BOLDSANSSERIF],[120302,120327,97,MML.VARIANT.BOLDSANSSERIF],[120328,120353,65,MML.VARIANT.SANSSERIFITALIC],[120354,120379,97,MML.VARIANT.SANSSERIFITALIC],[120432,120457,65,MML.VARIANT.MONOSPACE],[120458,120483,97,MML.VARIANT.MONOSPACE],[120488,120513,913,MML.VARIANT.BOLD],[120546,120570,913,MML.VARIANT.ITALIC],[120572,120603,945,MML.VARIANT.ITALIC],[120604,120628,913,MML.VARIANT.BOLDITALIC],[120630,120661,945,MML.VARIANT.BOLDITALIC],[120662,120686,913,MML.VARIANT.BOLDSANSSERIF],[120720,120744,913,MML.VARIANT.SANSSERIFBOLDITALIC],[120782,120791,48,MML.VARIANT.BOLD],[120802,120811,48,MML.VARIANT.SANSSERIF],[120812,120821,48,MML.VARIANT.BOLDSANSSERIF],[120822,120831,48,MML.VARIANT.MONOSPACE]],REMAPGREEK:{913:65,914:66,917:69,918:90,919:72,921:73,922:75,924:77,925:78,927:79,929:80,930:920,932:84,935:88,938:8711,970:8706,971:1013,972:977,973:1008,974:981,975:1009,976:982},RemapPlane1:function(n,variant){for(var i=0,m=this.PLANE1MAP.length;i<m&&!(n<this.PLANE1MAP[i][0]);i++)if(n<=this.PLANE1MAP[i][1]){n=n-this.PLANE1MAP[i][0]+this.PLANE1MAP[i][2],this.REMAPGREEK[n]&&(n=this.REMAPGREEK[n]),variant=this.VARIANT[this.PLANE1MAP[i][3]];break}return{n:n,variant:variant}},DELIMITERS:{40:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9115,SIZE4],ext:[9116,SIZE4],bot:[9117,SIZE4]}},41:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9118,SIZE4],ext:[9119,SIZE4],bot:[9120,SIZE4]}},47:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]]},91:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9121,SIZE4],ext:[9122,SIZE4],bot:[9123,SIZE4]}},92:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]]},93:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9124,SIZE4],ext:[9125,SIZE4],bot:[9126,SIZE4]}},123:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9127,SIZE4],mid:[9128,SIZE4],bot:[9129,SIZE4],ext:[9130,SIZE4]}},124:{dir:V,HW:[[1,MAIN]],stretch:{ext:[8739,MAIN]}},125:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9131,SIZE4],mid:[9132,SIZE4],bot:[9133,SIZE4],ext:[9130,SIZE4]}},175:{dir:H,HW:[[.59,MAIN]],stretch:{rep:[175,MAIN]}},710:{dir:H,HW:[[.517,MAIN],[.817,SIZE1],[1.335,SIZE2],[1.447+.33,SIZE3],[1.909,SIZE4]]},732:{dir:H,HW:[[.583,MAIN],[.805,SIZE1],[1.33,SIZE2],[1.443+.33,SIZE3],[1.887,SIZE4]]},8211:{dir:H,HW:[[.5,MAIN]],stretch:{rep:[8211,MAIN]}},8214:{dir:V,HW:[[.602,SIZE1],[1,MAIN,null,8741]],stretch:{ext:[8741,MAIN]}},8592:{dir:H,HW:[[1,MAIN]],stretch:{left:[8592,MAIN],rep:ARROWREP}},8593:{dir:V,HW:[[.888,MAIN]],stretch:{top:[8593,SIZE1],ext:[9168,SIZE1]}},8594:{dir:H,HW:[[1,MAIN]],stretch:{rep:ARROWREP,right:[8594,MAIN]}},8595:{dir:V,HW:[[.888,MAIN]],stretch:{ext:[9168,SIZE1],bot:[8595,SIZE1]}},8596:{dir:H,HW:[[1,MAIN]],stretch:{left:[8592,MAIN],rep:ARROWREP,right:[8594,MAIN]}},8597:{dir:V,HW:[[1.044,MAIN]],stretch:{top:[8593,SIZE1],ext:[9168,SIZE1],bot:[8595,SIZE1]}},8656:{dir:H,HW:[[1,MAIN]],stretch:{left:[8656,MAIN],rep:DARROWREP}},8657:{dir:V,HW:[[.888,MAIN]],stretch:{top:[8657,SIZE1],ext:[8214,SIZE1]}},8658:{dir:H,HW:[[1,MAIN]],stretch:{rep:DARROWREP,right:[8658,MAIN]}},8659:{dir:V,HW:[[.888,MAIN]],stretch:{ext:[8214,SIZE1],bot:[8659,SIZE1]}},8660:{dir:H,HW:[[1,MAIN]],stretch:{left:[8656,MAIN],rep:DARROWREP,right:[8658,MAIN]}},8661:{dir:V,HW:[[1.044,MAIN]],stretch:{top:[8657,SIZE1],ext:[8214,SIZE1],bot:[8659,SIZE1]}},8722:{dir:H,HW:[[.778,MAIN]],stretch:{rep:[8722,MAIN]}},8730:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[57345,SIZE4],ext:[57344,SIZE4],bot:[9143,SIZE4],fullExtenders:!0}},8739:{dir:V,HW:[[1,MAIN]],stretch:{ext:[8739,MAIN]}},8741:{dir:V,HW:[[1,MAIN]],stretch:{ext:[8741,MAIN]}},8968:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9121,SIZE4],ext:[9122,SIZE4]}},8969:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{top:[9124,SIZE4],ext:[9125,SIZE4]}},8970:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{ext:[9122,SIZE4],bot:[9123,SIZE4]}},8971:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]],stretch:{ext:[9125,SIZE4],bot:[9126,SIZE4]}},9130:{dir:V,HW:[[.32,SIZE4]],stretch:{top:[9130,SIZE4],ext:[9130,SIZE4],bot:[9130,SIZE4]}},9136:{dir:V,HW:[[.989,MAIN]],stretch:{top:[9127,SIZE4],ext:[9130,SIZE4],bot:[9133,SIZE4]}},9137:{dir:V,HW:[[.989,MAIN]],stretch:{top:[9131,SIZE4],ext:[9130,SIZE4],bot:[9129,SIZE4]}},9168:{dir:V,HW:[[.602,SIZE1],[1,MAIN,null,8739]],stretch:{ext:[8739,MAIN]}},9182:{dir:H,HW:[],stretch:{min:.9,left:[57680,SIZE4],mid:[[57683,57682],SIZE4],right:[57681,SIZE4],rep:[57684,SIZE4]}},9183:{dir:H,HW:[],stretch:{min:.9,left:[57682,SIZE4],mid:[[57681,57680],SIZE4],right:[57683,SIZE4],rep:[57684,SIZE4]}},10216:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]]},10217:{dir:V,HW:[[1,MAIN],[1.2,SIZE1],[1.8,SIZE2],[2.4,SIZE3],[3,SIZE4]]},10222:{dir:V,HW:[[.989,MAIN]],stretch:{top:[9127,SIZE4],ext:[9130,SIZE4],bot:[9129,SIZE4]}},10223:{dir:V,HW:[[.989,MAIN]],stretch:{top:[9131,SIZE4],ext:[9130,SIZE4],bot:[9133,SIZE4]}},45:{alias:8722,dir:H},94:{alias:710,dir:H},95:{alias:8211,dir:H},126:{alias:732,dir:H},713:{alias:175,dir:H},770:{alias:710,dir:H},771:{alias:732,dir:H},780:{alias:711,dir:H},818:{alias:8211,dir:H},8212:{alias:8211,dir:H},8213:{alias:8211,dir:H},8215:{alias:8211,dir:H},8254:{alias:175,dir:H},8407:{alias:8594,dir:H},8725:{alias:47,dir:V},9001:{alias:10216,dir:V},9002:{alias:10217,dir:V},9135:{alias:8211,dir:H},9472:{alias:8211,dir:H},10072:{alias:8739,dir:V},12296:{alias:10216,dir:V},12297:{alias:10217,dir:V},65079:{alias:9182,dir:H},65080:{alias:9183,dir:H},61:EXTRAH,8606:EXTRAH,8608:EXTRAH,8612:EXTRAH,8613:EXTRAV,8614:EXTRAH,8615:EXTRAV,8624:EXTRAV,8625:EXTRAV,8636:EXTRAH,8637:EXTRAH,8638:EXTRAV,8639:EXTRAV,8640:EXTRAH,8641:EXTRAH,8642:EXTRAV,8643:EXTRAV,8666:EXTRAH,8667:EXTRAH,9140:EXTRAH,9141:EXTRAH,9180:EXTRAH,9181:EXTRAH,9184:EXTRAH,9185:EXTRAH,10502:EXTRAH,10503:EXTRAH,10574:EXTRAH,10575:EXTRAV,10576:EXTRAH,10577:EXTRAV,10586:EXTRAH,10587:EXTRAH,10588:EXTRAV,10589:EXTRAV,10590:EXTRAH,10591:EXTRAH,10592:EXTRAV,10593:EXTRAV,8978:{alias:9180,dir:H},8994:{alias:9180,dir:H},8995:{alias:9181,dir:H},10229:{alias:8592,dir:H},10230:{alias:8594,dir:H},10231:{alias:8596,dir:H},10232:{alias:8656,dir:H},10233:{alias:8658,dir:H},10234:{alias:8660,dir:H},10235:{alias:8612,dir:H},10236:{alias:8614,dir:H},10237:{alias:10502,dir:H},10238:{alias:10503,dir:H},57696:{alias:8592,dir:H}}},CHTML.FONTDATA=CHTML.FONTDEF.TeX,CHTML.FONTDATA.FONTS.MathJax_Caligraphic={centerline:287,ascent:789,descent:216,skew:{65:.194,66:.139,67:.139,68:.0833,69:.111,70:.111,71:.111,72:.111,73:.0278,74:.167,75:.0556,76:.139,77:.139,78:.0833,79:.111,80:.0833,81:.111,82:.0833,83:.139,84:.0278,85:.0833,86:.0278,87:.0833,88:.139,89:.0833,90:.139},32:[0,0,250,0,0],48:[452,22,500,39,460],49:[453,0,500,86,426],50:[453,0,500,44,449],51:[452,216,500,42,456],52:[464,194,500,28,471],53:[453,216,500,50,448],54:[665,22,500,42,456],55:[463,216,500,55,485],56:[666,21,500,43,456],57:[453,216,500,42,457],65:[728,50,798,30,819],66:[705,22,657,32,664],67:[705,25,527,12,533],68:[683,0,771,19,766],69:[705,22,528,30,564],70:[683,32,719,18,829],71:[704,119,595,44,599],72:[683,48,845,18,803],73:[683,0,545,-30,642],74:[683,119,678,47,839],75:[705,22,762,32,732],76:[705,22,690,32,656],77:[705,50,1201,28,1137],78:[789,50,820,-27,979],79:[705,22,796,58,777],80:[683,57,696,19,733],81:[705,131,817,114,787],82:[682,22,848,19,837],83:[705,22,606,18,642],84:[717,68,545,34,833],85:[683,28,626,-17,687],86:[683,52,613,25,658],87:[683,53,988,25,1034],88:[683,0,713,52,807],89:[683,143,668,31,714],90:[683,0,725,37,767],160:[0,0,250,0,0]},CHTML.FONTDATA.FONTS["MathJax_Main-Bold"]={centerline:342,ascent:951,descent:267,weight:"bold",file:"TeX/Main-Bold.js",Extra:[160,168,172,[175,177],180,215,247,305,567,[710,715],[728,732],[768,780],824,[8194,8198],8201,8202,8211,8212,8216,8217,8220,8221,8224,8225,8230,8242,8407,[8463,8467],8472,8476,8501,[8592,8601],8614,8617,8618,8636,8637,8640,8641,8652,[8656,8661],[8704,8715],[8722,8730],[8733,8736],[8739,8764],8768,8771,8773,8776,8781,8784,8800,8801,8804,8805,8810,8811,8826,8827,8834,8835,8838,8839,8846,[8849,8857],[8866,8869],8872,[8900,8904],[8942,8945],[8968,8971],8994,8995,9651,9657,9661,9667,9711,[9824,9827],[9837,9839],10216,10217,[10229,10236],10815,10927,10928],skew:{305:.0319,567:.0958,8463:-.0319,8467:.128,8706:.0958},32:[0,0,250,0,0],33:[705,-1,350,89,260],34:[694,-329,603,38,492],35:[694,193,958,64,893],36:[750,56,575,64,510],37:[750,56,958,65,893],38:[705,11,894,48,836],39:[694,-329,319,74,261],40:[750,249,447,103,382],41:[750,249,447,64,343],42:[750,-306,575,73,501],43:[633,131,894,64,829],44:[171,194,319,74,258],45:[278,-166,383,13,318],46:[171,-1,319,74,245],47:[750,250,575,63,511],48:[654,10,575,45,529],49:[655,0,575,80,494],50:[654,0,575,57,517],51:[655,11,575,47,526],52:[656,0,575,32,542],53:[655,11,575,57,517],54:[655,11,575,48,526],55:[676,11,575,64,558],56:[654,11,575,48,526],57:[654,11,575,48,526],58:[444,-1,319,74,245],59:[444,194,319,74,248],60:[587,85,894,96,797],61:[393,-109,894,64,829],62:[587,85,894,96,797],63:[700,-1,543,65,478],64:[699,6,894,64,829],65:[698,0,869,40,828],66:[686,0,818,39,752],67:[697,11,831,64,766],68:[686,0,882,39,817],69:[680,0,756,39,723],70:[680,0,724,39,675],71:[697,10,904,64,845],72:[686,0,900,39,860],73:[686,0,436,25,410],74:[686,11,594,8,527],75:[686,0,901,39,852],76:[686,0,692,39,643],77:[686,0,1092,39,1052],78:[686,0,900,39,860],79:[696,10,864,64,798],80:[686,0,786,39,721],81:[696,193,864,64,805],82:[686,11,862,39,858],83:[697,11,639,64,574],84:[675,0,800,41,758],85:[686,11,885,39,845],86:[686,7,869,25,843],87:[686,7,1189,24,1164],88:[686,0,869,33,835],89:[686,0,869,19,849],90:[686,0,703,64,645],91:[750,250,319,128,293],92:[750,250,575,63,511],93:[750,250,319,25,190],94:[694,-520,575,126,448],95:[-10,61,575,0,574],96:[706,-503,575,114,338],97:[453,6,559,32,558],98:[694,6,639,29,600],99:[453,6,511,39,478],100:[694,6,639,38,609],101:[452,6,527,32,494],102:[700,0,351,40,452],103:[455,201,575,30,558],104:[694,0,639,37,623],105:[695,0,319,40,294],106:[695,200,351,-71,274],107:[694,0,607,29,587],108:[694,0,319,40,301],109:[450,0,958,37,942],110:[450,0,639,37,623],111:[452,5,575,32,542],112:[450,194,639,29,600],113:[450,194,607,38,609],114:[450,0,474,29,442],115:[453,6,454,38,414],116:[635,5,447,21,382],117:[450,6,639,37,623],118:[444,3,607,26,580],119:[444,4,831,25,805],120:[444,0,607,21,586],121:[444,200,607,23,580],122:[444,0,511,32,462],123:[750,250,575,70,504],124:[750,249,319,129,190],125:[750,250,575,70,504],126:[344,-202,575,96,478],915:[680,0,692,39,643],916:[698,0,958,56,901],920:[696,10,894,64,829],923:[698,0,806,40,765],926:[675,0,767,48,718],928:[680,0,900,39,860],931:[686,0,831,63,766],933:[697,0,894,64,829],934:[686,0,831,64,766],936:[686,0,894,64,829],937:[696,0,831,51,779]},CHTML.FONTDATA.FONTS["MathJax_Main-Italic"]={centerline:250,ascent:750,descent:250,style:"italic",32:[0,0,250,0,0],33:[716,0,307,107,380],34:[694,-379,514,176,538],35:[694,194,818,115,828],37:[750,56,818,145,847],38:[716,22,767,127,802],39:[694,-379,307,213,377],40:[750,250,409,144,517],41:[750,250,409,17,390],42:[750,-320,511,195,584],43:[557,57,767,139,753],44:[121,194,307,69,232],45:[251,-180,358,84,341],46:[121,0,307,107,231],47:[750,250,511,19,617],48:[665,21,511,110,562],49:[666,0,511,110,468],50:[666,22,511,76,551],51:[666,22,511,96,562],52:[666,194,511,46,478],53:[666,22,511,106,567],54:[665,22,511,120,565],55:[666,22,511,136,634],56:[666,21,511,99,553],57:[666,22,511,107,553],58:[431,0,307,107,308],59:[431,194,307,70,308],61:[367,-133,767,116,776],63:[716,0,511,195,551],64:[705,11,767,152,789],65:[716,0,743,58,696],66:[683,0,704,57,732],67:[705,21,716,150,812],68:[683,0,755,56,775],69:[680,0,678,54,743],70:[680,-1,653,54,731],71:[705,22,774,150,812],72:[683,0,743,54,860],73:[683,0,386,49,508],74:[683,21,525,78,622],75:[683,0,769,54,859],76:[683,0,627,54,628],77:[683,0,897,58,1010],78:[683,0,743,54,860],79:[704,22,767,149,788],80:[683,0,678,55,729],81:[704,194,767,149,788],82:[683,22,729,55,723],83:[705,22,562,74,633],84:[677,0,716,171,806],85:[683,22,743,194,860],86:[683,22,743,205,868],87:[683,22,999,205,1124],88:[683,0,743,50,825],89:[683,0,743,198,875],90:[683,0,613,80,704],91:[750,250,307,73,446],93:[750,250,307,-14,359],94:[694,-527,511,260,528],95:[-25,62,511,91,554],97:[442,11,511,101,543],98:[694,11,460,108,467],99:[441,10,460,103,469],100:[694,11,511,101,567],101:[442,10,460,107,470],102:[705,204,307,-23,450],103:[442,205,460,46,494],104:[694,11,511,69,544],105:[656,10,307,75,340],106:[656,204,307,-32,364],107:[694,11,460,69,498],108:[694,11,256,87,312],109:[442,11,818,75,851],110:[442,11,562,75,595],111:[442,11,511,103,517],112:[442,194,511,6,518],113:[442,194,460,101,504],114:[442,11,422,75,484],115:[442,11,409,76,418],116:[626,11,332,87,373],117:[441,11,537,75,570],118:[443,10,460,75,492],119:[443,11,664,75,696],120:[442,11,464,58,513],121:[441,205,486,75,522],122:[442,11,409,54,466],126:[318,-208,511,246,571],160:[0,0,250,0,0],163:[714,11,769,88,699],305:[441,10,307,75,340],567:[442,204,332,-32,327],768:[697,-500,0,-222,-74],769:[697,-500,0,-173,39],770:[694,-527,0,-251,17],771:[668,-558,0,-265,60],772:[589,-544,0,-282,54],774:[694,-515,0,-237,62],775:[669,-548,0,-165,-41],776:[669,-554,0,-251,45],778:[716,-542,0,-199,3],779:[697,-503,0,-248,65],780:[638,-502,0,-236,29],915:[680,0,627,54,705],916:[716,0,818,70,751],920:[704,22,767,149,788],923:[716,0,692,58,646],926:[677,0,664,74,754],928:[680,0,743,54,859],931:[683,0,716,80,782],933:[705,0,767,213,832],934:[683,0,716,159,728],936:[683,0,767,207,824],937:[705,0,716,100,759],8211:[285,-248,511,91,554],8212:[285,-248,1022,117,1038],8216:[694,-379,307,197,362],8217:[694,-379,307,213,377],8220:[694,-379,514,243,606],8221:[694,-379,514,176,538],8463:[695,13,540,42,562]},CHTML.FONTDATA.FONTS.MathJax_Main={centerline:314,ascent:900,descent:272,skew:{305:.0278,567:.0833,8467:.111,8472:.111,8706:.0833},32:[0,0,250,0,0],33:[716,-1,278,78,199],34:[694,-379,500,34,372],35:[694,194,833,56,777],36:[750,56,500,55,444],37:[750,56,833,56,776],38:[716,22,778,42,727],39:[694,-379,278,78,212],40:[750,250,389,94,333],41:[750,250,389,55,294],42:[750,-320,500,64,435],43:[583,82,778,56,722],44:[121,194,278,78,210],45:[252,-179,333,11,277],46:[120,0,278,78,199],47:[750,250,500,56,445],48:[666,22,500,39,460],49:[666,0,500,83,427],50:[666,0,500,50,449],51:[665,22,500,42,457],52:[677,0,500,28,471],53:[666,22,500,50,449],54:[666,22,500,42,456],55:[676,22,500,55,485],56:[666,22,500,43,457],57:[666,22,500,42,456],58:[430,0,278,78,199],59:[430,194,278,78,202],60:[540,40,778,83,694],61:[367,-133,778,56,722],62:[540,40,778,83,694],63:[705,-1,472,55,416],64:[705,11,778,56,722],65:[716,0,750,32,717],66:[683,0,708,28,651],67:[705,21,722,56,666],68:[683,0,764,27,708],69:[680,0,681,25,652],70:[680,0,653,25,610],71:[705,22,785,56,735],72:[683,0,750,25,724],73:[683,0,361,21,339],74:[683,22,514,25,465],75:[683,0,778,25,736],76:[683,0,625,25,582],77:[683,0,917,29,887],78:[683,0,750,25,724],79:[705,22,778,56,722],80:[683,0,681,27,624],81:[705,193,778,56,728],82:[683,22,736,27,732],83:[705,22,556,55,500],84:[677,0,722,36,685],85:[683,22,750,25,724],86:[683,22,750,19,730],87:[683,22,1028,18,1009],88:[683,0,750,23,726],89:[683,0,750,11,738],90:[683,0,611,55,560],91:[750,250,278,118,255],92:[750,250,500,56,444],93:[750,250,278,22,159],94:[694,-531,500,112,387],95:[-25,62,500,0,499],96:[699,-505,500,106,295],97:[448,11,500,34,493],98:[694,11,556,20,522],99:[448,11,444,34,415],100:[694,11,556,34,535],101:[448,11,444,28,415],102:[705,0,306,26,372],103:[453,206,500,29,485],104:[694,0,556,25,542],105:[669,0,278,26,255],106:[669,205,306,-55,218],107:[694,0,528,20,511],108:[694,0,278,26,263],109:[442,0,833,25,819],110:[442,0,556,25,542],111:[448,10,500,28,471],112:[442,194,556,20,522],113:[442,194,528,33,535],114:[442,0,392,20,364],115:[448,11,394,33,359],116:[615,10,389,18,333],117:[442,11,556,25,542],118:[431,11,528,19,508],119:[431,11,722,18,703],120:[431,0,528,11,516],121:[431,204,528,19,508],122:[431,0,444,28,401],123:[750,250,500,65,434],124:[750,249,278,119,159],125:[750,250,500,65,434],126:[318,-215,500,83,416],160:[0,0,250,0,0],168:[669,-554,500,95,404],172:[356,-89,667,56,611],175:[590,-544,500,69,430],176:[715,-542,500,147,352],177:[666,0,778,56,722],180:[699,-505,500,203,393],215:[491,-9,778,147,630],247:[537,36,778,56,721],305:[442,0,278,26,255],567:[442,205,306,-55,218],710:[694,-531,500,112,387],711:[644,-513,500,114,385],713:[590,-544,500,69,430],714:[699,-505,500,203,393],715:[699,-505,500,106,295],728:[694,-515,500,92,407],729:[669,-549,500,190,309],732:[668,-565,500,83,416],730:[715,-542,500,147,352],768:[699,-505,0,-394,-205],769:[699,-505,0,-297,-107],770:[694,-531,0,-388,-113],771:[668,-565,0,-417,-84],772:[590,-544,0,-431,-70],774:[694,-515,0,-408,-93],775:[669,-549,0,-310,-191],776:[669,-554,0,-405,-96],778:[715,-542,0,-353,-148],779:[701,-510,0,-378,-80],780:[644,-513,0,-386,-115],824:[716,215,0,-639,-140],915:[680,0,625,25,582],916:[716,0,833,46,786],920:[705,22,778,56,722],923:[716,0,694,32,661],926:[677,0,667,42,624],928:[680,0,750,25,724],931:[683,0,722,55,666],933:[705,0,778,55,722],934:[683,0,722,56,665],936:[683,0,778,55,722],937:[704,0,722,44,677],8194:[0,0,500,0,0],8195:[0,0,999,0,0],8196:[0,0,333,0,0],8197:[0,0,250,0,0],8198:[0,0,167,0,0],8201:[0,0,167,0,0],8202:[0,0,83,0,0],8211:[285,-248,500,0,499],8212:[285,-248,1e3,0,999],8216:[694,-379,278,64,198],8217:[694,-379,278,78,212],8220:[694,-379,500,128,466],8221:[694,-379,500,34,372],8224:[705,216,444,55,389],8225:[705,205,444,55,389],8230:[120,0,1172,78,1093],8242:[560,-43,275,30,262],8407:[714,-516,0,-471,-29],8463:[695,13,540,42,562],8465:[705,10,722,55,693],8467:[705,20,417,6,397],8472:[453,216,636,67,625],8476:[716,22,722,40,715],8501:[694,0,611,55,555],8592:[511,11,1e3,55,944],8593:[694,193,500,17,483],8594:[511,11,1e3,56,944],8595:[694,194,500,17,483],8596:[511,11,1e3,55,944],8597:[772,272,500,17,483],8598:[720,195,1e3,29,944],8599:[720,195,1e3,55,970],8600:[695,220,1e3,55,970],8601:[695,220,1e3,29,944],8614:[511,11,1e3,55,944],8617:[511,11,1126,55,1070],8618:[511,11,1126,55,1070],8636:[511,-230,1e3,55,944],8637:[270,11,1e3,55,944],8640:[511,-230,1e3,56,944],8641:[270,11,1e3,56,944],8652:[671,11,1e3,55,944],8656:[525,24,1e3,56,944],8657:[694,194,611,31,579],8658:[525,24,1e3,56,944],8659:[694,194,611,31,579],8660:[526,25,1e3,34,966],8661:[772,272,611,31,579],8704:[694,22,556,0,556],8706:[715,22,531,42,566],8707:[694,0,556,56,500],8709:[772,78,500,39,460],8711:[683,33,833,46,786],8712:[540,40,667,84,583],8713:[716,215,667,84,583],8715:[540,40,667,83,582],8722:[270,-230,778,84,694],8723:[500,166,778,56,722],8725:[750,250,500,56,445],8726:[750,250,500,56,444],8727:[465,-35,500,64,435],8728:[444,-55,500,55,444],8729:[444,-55,500,55,444],8730:[800,200,833,72,853],8733:[442,11,778,56,722],8734:[442,11,1e3,55,944],8736:[694,0,722,55,666],8739:[750,249,278,119,159],8741:[750,250,500,132,367],8743:[598,22,667,55,611],8744:[598,22,667,55,611],8745:[598,22,667,55,611],8746:[598,22,667,55,611],8747:[716,216,417,55,472],8764:[367,-133,778,55,722],8768:[583,83,278,55,222],8771:[464,-36,778,55,722],8773:[589,-22,1e3,55,722],8776:[483,-55,778,55,722],8781:[484,-16,778,55,722],8784:[670,-133,778,56,722],8800:[716,215,778,56,722],8801:[464,-36,778,56,722],8804:[636,138,778,83,694],8805:[636,138,778,83,694],8810:[568,67,1e3,56,944],8811:[567,67,1e3,55,944],8826:[539,41,778,84,694],8827:[539,41,778,83,694],8834:[540,40,778,84,694],8835:[540,40,778,83,693],8838:[636,138,778,84,694],8839:[636,138,778,83,693],8846:[598,22,667,55,611],8849:[636,138,778,84,714],8850:[636,138,778,64,694],8851:[598,0,667,61,605],8852:[598,0,667,61,605],8853:[583,83,778,56,722],8854:[583,83,778,56,722],8855:[583,83,778,56,722],8856:[583,83,778,56,722],8857:[583,83,778,56,722],8866:[694,0,611,55,555],8867:[694,0,611,55,555],8868:[668,0,778,55,723],8869:[668,0,778,55,723],8872:[750,249,867,119,811],8900:[488,-12,500,12,488],8901:[310,-190,278,78,199],8902:[486,-16,500,3,497],8904:[505,5,900,26,873],8942:[900,30,278,78,199],8943:[310,-190,1172,78,1093],8945:[820,-100,1282,133,1148],8968:[750,250,444,174,422],8969:[750,250,444,21,269],8970:[750,250,444,174,422],8971:[750,250,444,21,269],8994:[388,-122,1e3,55,944],8995:[378,-134,1e3,55,944],9136:[744,244,412,55,357],9137:[744,244,412,56,357],9651:[716,0,889,59,828],9657:[505,5,500,26,474],9661:[500,215,889,59,828],9667:[505,5,500,26,473],9711:[715,215,1e3,56,944],9824:[727,130,778,55,723],9825:[716,33,778,55,723],9826:[727,162,778,55,723],9827:[726,130,778,28,750],9837:[750,22,389,55,332],9838:[734,223,389,65,324],9839:[723,223,389,55,333],10216:[750,250,389,110,333],10217:[750,250,389,55,278],10222:[744,244,412,173,357],10223:[744,244,412,56,240],10229:[511,11,1609,55,1525],10230:[511,11,1638,84,1553],10231:[511,11,1859,55,1803],10232:[525,24,1609,56,1553],10233:[525,24,1638,56,1582],10234:[525,24,1858,56,1802],10236:[511,11,1638,55,1553],10815:[683,0,750,28,721],10927:[636,138,778,84,694],10928:[636,138,778,83,694]},CHTML.FONTDATA.FONTS["MathJax_Math-Italic"]={centerline:250,ascent:717,descent:218,style:"italic",skew:{65:.139,66:.0833,67:.0833,68:.0556,69:.0833,70:.0833,71:.0833,72:.0556,73:.111,74:.167,75:.0556,76:.0278,77:.0833,78:.0833,79:.0833,80:.0833,81:.0833,82:.0833,83:.0833,84:.0833,85:.0278,88:.0833,90:.0833,99:.0556,100:.167,101:.0556,102:.167,103:.0278,104:-.0278,108:.0833,111:.0556,112:.0833,113:.0833,114:.0556,115:.0556,116:.0833,117:.0278,118:.0278,119:.0833,120:.0278,121:.0556,122:.0556,915:.0833,916:.167,920:.0833,923:.167,926:.0833,928:.0556,931:.0833,933:.0556,934:.0833,936:.0556,937:.0833,945:.0278,946:.0833,948:.0556,949:.0833,950:.0833,951:.0556,952:.0833,953:.0556,956:.0278,957:.0278,958:.111,959:.0556,961:.0833,962:.0833,964:.0278,965:.0278,966:.0833,967:.0556,968:.111,977:.0833,981:.0833,1009:.0833,1013:.0556},32:[0,0,250,0,0],47:[716,215,778,139,638],65:[716,0,750,35,726],66:[683,0,759,35,756],67:[705,22,715,50,760],68:[683,0,828,33,803],69:[680,0,738,31,764],70:[680,0,643,31,749],71:[705,22,786,50,760],72:[683,0,831,31,888],73:[683,0,440,26,504],74:[683,22,555,57,633],75:[683,0,849,31,889],76:[683,0,681,32,647],77:[683,0,970,35,1051],78:[683,0,803,31,888],79:[704,22,763,50,740],80:[683,0,642,33,751],81:[704,194,791,50,740],82:[683,21,759,33,755],83:[705,22,613,52,645],84:[677,0,584,21,704],85:[683,22,683,60,767],86:[683,22,583,52,769],87:[683,22,944,51,1048],88:[683,0,828,26,852],89:[683,-1,581,30,763],90:[683,0,683,58,723],97:[441,10,529,33,506],98:[694,11,429,40,422],99:[442,11,433,34,429],100:[694,10,520,33,523],101:[442,11,466,39,429],102:[705,205,490,55,550],103:[442,205,477,10,480],104:[694,11,576,48,555],105:[661,11,345,21,302],106:[661,204,412,-12,403],107:[694,11,521,48,503],108:[694,11,298,38,266],109:[442,11,878,21,857],110:[442,11,600,21,580],111:[441,11,485,34,476],112:[442,194,503,-39,497],113:[442,194,446,33,460],114:[442,11,451,21,430],115:[442,10,469,53,419],116:[626,11,361,19,330],117:[442,11,572,21,551],118:[443,11,485,21,467],119:[443,11,716,21,690],120:[442,11,572,35,522],121:[442,205,490,21,496],122:[442,11,465,35,468],160:[0,0,250,0,0],915:[680,-1,615,31,721],916:[716,0,833,48,788],920:[704,22,763,50,740],923:[716,0,694,35,670],926:[677,0,742,53,777],928:[680,0,831,31,887],931:[683,0,780,58,806],933:[705,0,583,28,700],934:[683,0,667,24,642],936:[683,0,612,21,692],937:[704,0,772,80,786],945:[442,11,640,34,603],946:[705,194,566,23,573],947:[441,216,518,11,543],948:[717,10,444,36,451],949:[452,22,466,27,428],950:[704,204,438,44,471],951:[442,216,497,21,503],952:[705,10,469,35,462],953:[442,10,354,48,332],954:[442,11,576,49,554],955:[694,12,583,47,556],956:[442,216,603,23,580],957:[442,2,494,45,530],958:[704,205,438,21,443],959:[441,11,485,34,476],960:[431,11,570,19,573],961:[442,216,517,23,510],962:[442,107,363,31,405],963:[431,11,571,31,572],964:[431,13,437,18,517],965:[443,10,540,21,523],966:[442,218,654,50,618],967:[442,204,626,25,600],968:[694,205,651,21,634],969:[443,11,622,15,604],977:[705,11,591,21,563],981:[694,205,596,43,579],982:[431,10,828,19,823],1009:[442,194,517,67,510],1013:[431,11,406,40,382]},CHTML.FONTDATA.FONTS.MathJax_Size1={centerline:250,ascent:850,descent:350,32:[0,0,250,0,0],40:[850,349,458,152,422],41:[850,349,458,35,305],47:[850,349,578,55,522],91:[850,349,417,202,394],92:[850,349,578,54,522],93:[850,349,417,22,214],123:[850,349,583,105,477],125:[850,349,583,105,477],160:[0,0,250,0,0],710:[744,-551,556,-8,564],732:[722,-597,556,1,554],770:[744,-551,0,-564,8],771:[722,-597,0,-555,-2],8214:[602,0,778,257,521],8593:[600,0,667,112,555],8595:[600,0,667,112,555],8657:[599,0,778,57,721],8659:[600,-1,778,57,721],8719:[750,250,944,55,888],8720:[750,250,944,55,888],8721:[750,250,1056,56,999],8730:[850,350,1e3,111,1020],8739:[627,15,333,145,188],8741:[627,15,556,145,410],8747:[805,306,472,55,610],8748:[805,306,819,55,957],8749:[805,306,1166,55,1304],8750:[805,306,472,55,610],8896:[750,249,833,55,777],8897:[750,249,833,55,777],8898:[750,249,833,55,777],8899:[750,249,833,55,777],8968:[850,349,472,202,449],8969:[850,349,472,22,269],8970:[850,349,472,202,449],8971:[850,349,472,22,269],9168:[602,0,667,312,355],10216:[850,350,472,97,394],10217:[850,350,472,77,374],10752:[750,250,1111,56,1054],10753:[750,250,1111,56,1054],10754:[750,250,1111,56,1054],10756:[750,249,833,55,777],10758:[750,249,833,55,777]},CHTML.FONTDATA.FONTS.MathJax_Size2={centerline:249,ascent:1360,descent:862,32:[0,0,250,0,0],40:[1150,649,597,180,561],41:[1150,649,597,35,416],47:[1150,649,811,56,754],91:[1150,649,472,224,455],92:[1150,649,811,54,754],93:[1150,649,472,16,247],123:[1150,649,667,119,547],125:[1150,649,667,119,547],160:[0,0,250,0,0],710:[772,-565,1e3,-5,1004],732:[750,-611,1e3,0,999],770:[772,-565,0,-1005,4],771:[750,-611,0,-1e3,-1],8719:[950,450,1278,56,1221],8720:[950,450,1278,56,1221],8721:[950,450,1444,55,1388],8730:[1150,650,1e3,111,1020],8747:[1360,862,556,55,944],8748:[1360,862,1084,55,1472],8749:[1360,862,1592,55,1980],8750:[1360,862,556,55,944],8896:[950,450,1111,55,1055],8897:[950,450,1111,55,1055],8898:[949,450,1111,55,1055],8899:[950,449,1111,55,1055],8968:[1150,649,528,224,511],8969:[1150,649,528,16,303],8970:[1150,649,528,224,511],8971:[1150,649,528,16,303],10216:[1150,649,611,112,524],10217:[1150,649,611,85,498],10752:[949,449,1511,56,1454],10753:[949,449,1511,56,1454],10754:[949,449,1511,56,1454],10756:[950,449,1111,55,1055],10758:[950,450,1111,55,1055]},CHTML.FONTDATA.FONTS.MathJax_Size3={centerline:250,ascent:1450,descent:950,32:[0,0,250,0,0],40:[1450,949,736,209,701],41:[1450,949,736,34,526],47:[1450,949,1044,55,989],91:[1450,949,528,247,516],92:[1450,949,1044,56,988],93:[1450,949,528,11,280],123:[1450,949,750,130,618],125:[1450,949,750,131,618],160:[0,0,250,0,0],710:[772,-564,1444,-4,1447],732:[749,-610,1444,1,1442],770:[772,-564,0,-1448,3],771:[749,-610,0,-1443,-2],8730:[1450,950,1e3,111,1020],8968:[1450,949,583,246,571],8969:[1450,949,583,11,336],8970:[1450,949,583,246,571],8971:[1450,949,583,11,336],10216:[1450,950,750,126,654],10217:[1450,949,750,94,623]},CHTML.FONTDATA.FONTS.MathJax_Size4={centerline:250,ascent:1750,descent:1250,32:[0,0,250,0,0],40:[1750,1249,792,237,758],41:[1750,1249,792,33,554],47:[1750,1249,1278,56,1221],91:[1750,1249,583,269,577],92:[1750,1249,1278,56,1221],93:[1750,1249,583,5,313],123:[1750,1249,806,144,661],125:[1750,1249,806,144,661],160:[0,0,250,0,0],710:[845,-561,1889,-14,1902],732:[823,-583,1889,1,1885],770:[845,-561,0,-1903,13],771:[823,-583,0,-1888,-4],8730:[1750,1250,1e3,111,1020],8968:[1750,1249,639,269,633],8969:[1750,1249,639,5,369],8970:[1750,1249,639,269,633],8971:[1750,1249,639,5,369],9115:[1154,655,875,291,843],9116:[610,10,875,291,417],9117:[1165,644,875,291,843],9118:[1154,655,875,31,583],9119:[610,10,875,457,583],9120:[1165,644,875,31,583],9121:[1154,645,667,319,666],9122:[602,0,667,319,403],9123:[1155,644,667,319,666],9124:[1154,645,667,0,347],9125:[602,0,667,263,347],9126:[1155,644,667,0,347],9127:[899,10,889,384,718],9128:[1160,660,889,170,504],9129:[10,899,889,384,718],9130:[310,10,889,384,504],9131:[899,10,889,170,504],9132:[1160,660,889,384,718],9133:[10,899,889,170,504],9143:[935,885,1056,111,742],10216:[1750,1248,806,140,703],10217:[1750,1248,806,103,665],57344:[625,14,1056,702,742],57345:[605,14,1056,702,1076],57680:[120,213,450,-24,460],57681:[120,213,450,-10,474],57682:[333,0,450,-24,460],57683:[333,0,450,-10,474],57684:[120,0,400,-10,410]},CHTML.FONTDATA.FONTS.MathJax_Vector={centerline:257,ascent:714,descent:200,8594:[714,-516,500,29,471]},CHTML.FONTDATA.FONTS["MathJax_Vector-Bold"]={centerline:256,ascent:723,descent:210,8594:[723,-513,575,33,542]},CHTML.FONTDATA.FONTS[MAIN][8722][0]=CHTML.FONTDATA.FONTS[MAIN][43][0],CHTML.FONTDATA.FONTS[MAIN][8722][1]=CHTML.FONTDATA.FONTS[MAIN][43][1],CHTML.FONTDATA.FONTS[MAIN][8942][0]+=400,CHTML.FONTDATA.FONTS[MAIN][8945][0]+=700,CHTML.FONTDATA.FONTS[SIZE4][9130][0]-=20,CHTML.FONTDATA.FONTS[SIZE4][9130][1]+=5,CHTML.FONTDATA.FONTS[SIZE4][57684][0]+=200,CHTML.FONTDATA.FONTS[SIZE4][57684][1]+=200,CHTML.FONTDATA.FONTS[MAIN][8773][2]-=222,CHTML.FONTDATA.FONTS[MAIN][8773][5]={rfix:-222},MathJax.Hub.Register.LoadHook(CHTML.fontDir+"/TeX/Main-Bold.js",(function(){CHTML.FONTDATA.FONTS[BOLD][8773][2]-=106,CHTML.FONTDATA.FONTS[BOLD][8773][5]={rfix:-106}})),MathJax.Hub.Register.LoadHook(CHTML.fontDir+"/TeX/Typewriter-Regular.js",(function(){CHTML.FONTDATA.FONTS.MathJax_Typewriter[32][2]+=275,CHTML.FONTDATA.FONTS.MathJax_Typewriter[32][5]={rfix:275},CHTML.FONTDATA.FONTS.MathJax_Typewriter[160][2]+=275,CHTML.FONTDATA.FONTS.MathJax_Typewriter[160][5]={rfix:275}})),MathJax.Hub.Insert(CHTML.FONTDATA.FONTS[MAIN],{remapCombining:{768:715,769:714,770:710,771:732,772:713,774:728,775:729,776:168,778:730,780:711,824:[47,ITALIC],8407:[8594,"MathJax_Vector"]},8192:[0,0,500,0,0,{space:1}],8193:[0,0,1e3,0,0,{space:1}],8194:[0,0,500,0,0,{space:1}],8195:[0,0,1e3,0,0,{space:1}],8196:[0,0,333,0,0,{space:1}],8197:[0,0,250,0,0,{space:1}],8198:[0,0,167,0,0,{space:1}],8201:[0,0,167,0,0,{space:1}],8202:[0,0,100,0,0,{space:1}],8203:[0,0,0,0,0,{space:1}],8204:[0,0,0,0,0,{space:1}],8289:[0,0,0,0,0,{space:1}],8290:[0,0,0,0,0,{space:1}],8291:[0,0,0,0,0,{space:1}],8292:[0,0,0,0,0,{space:1}],61152:[0,0,-575,0,0,{space:1}],61153:[0,0,-300,0,0,{space:1}],61160:[0,0,25,0,0,{space:1}]}),MathJax.Hub.Insert(CHTML.FONTDATA.FONTS["MathJax_Main-Italic"],{remapCombining:{768:[715,MAIN],769:[714,MAIN],770:[710,MAIN],771:[732,MAIN],772:[713,MAIN],774:[728,MAIN],775:[729,MAIN],776:[168,MAIN],778:[730,MAIN],780:[711,MAIN],824:[47,"MathJax_Vector"]}}),MathJax.Hub.Insert(CHTML.FONTDATA.FONTS["MathJax_Main-Bold"],{remapCombining:{768:715,769:714,770:710,771:732,772:713,774:728,775:729,776:168,778:730,780:711,824:[47,"MathJax_Math-BoldItalic"],8407:[8594,"MathJax_Vector-Bold"]}}),CHTML.FONTDATA.familyName=function(font){var names=((font=font.replace(/^MathJax_/,""))+"-Regular").split(/-/),suffix;return"MJXc-TeX-"+(names[0].toLowerCase().replace(/(?:igraphic|serif|writer|tur|tor)$/,"")+"-"+names[1].replace(/[^A-Z]/g,""))},function(){var STYLES=CHTML.config.styles,FONTS=CHTML.FONTDATA.FONTS,OTFDIR=AJAX.fileURL(CHTML.webfontDir+"/TeX/otf"),EOTDIR=AJAX.fileURL(CHTML.webfontDir+"/TeX/eot"),WOFFDIR=AJAX.fileURL(CHTML.webfontDir+"/TeX/woff"),faces=[];for(var name in FONTS)if(FONTS.hasOwnProperty(name)){var family=CHTML.FONTDATA.familyName(name),FAMILY=family,variant=(name+"-Regular").split(/-/)[1];FONTS[name].className=family;var font={"font-family":family};name=name.replace(/-.*/,""),font.src="Regular"===variant?"local('"+name+"'), local('"+name+"-Regular')":"local('"+name+" "+variant+"'), local('"+name+"-"+variant+"')",faces.push(font),"Regular"!==variant&&(font={"font-family":family+"x",src:"local('"+name+"')"},variant.match(/Bold/)&&(font["font-weight"]="bold"),variant.match(/Italic/)&&(font["font-style"]="italic"),FAMILY+=","+family+"x",faces.push(font)),font={"font-family":family+"w","src /*1*/":"url('"+EOTDIR+"/"+name+"-"+variant+".eot')","src /*2*/":["url('"+WOFFDIR+"/"+name+"-"+variant+".woff') format('woff')","url('"+OTFDIR+"/"+name+"-"+variant+".otf') format('opentype')"].join(", ")},faces.push(font),FAMILY+=","+family+"w",STYLES["."+family]={"font-family":FAMILY}}faces.length&&(STYLES["@font-face"]=faces)}(),CHTML.fontLoaded("TeX/fontdata")}(MathJax.OutputJax.CommonHTML,MathJax.ElementJax.mml,MathJax.Ajax),function(CHTML){var VERSION="2.7.5",DELIMITERS=CHTML.FONTDATA.DELIMITERS,MAIN="MathJax_Main",BOLD="MathJax_Main-Bold",AMS="MathJax_AMS",SIZE1="MathJax_Size1",SIZE4="MathJax_Size4",H="H",V="V",ARROWREP=[8722,MAIN,0,0,0,-.31,-.31],DARROWREP=[61,MAIN,0,0,0,0,.1],delim={61:{dir:H,HW:[[.767,MAIN]],stretch:{rep:[61,MAIN]}},8606:{dir:H,HW:[[1,AMS]],stretch:{left:[8606,AMS],rep:ARROWREP}},8608:{dir:H,HW:[[1,AMS]],stretch:{right:[8608,AMS],rep:ARROWREP}},8612:{dir:H,HW:[],stretch:{min:1,left:[8592,MAIN],rep:ARROWREP,right:[8739,SIZE1,0,-.05,.9]}},8613:{dir:V,HW:[],stretch:{min:.6,bot:[8869,BOLD,0,0,.75],ext:[9168,SIZE1],top:[8593,SIZE1]}},8614:{dir:H,HW:[[1,MAIN]],stretch:{left:[8739,SIZE1,-.09,-.05,.9],rep:ARROWREP,right:[8594,MAIN]}},8615:{dir:V,HW:[],stretch:{min:.6,top:[8868,BOLD,0,0,.75],ext:[9168,SIZE1],bot:[8595,SIZE1]}},8624:{dir:V,HW:[[.722,AMS]],stretch:{top:[8624,AMS],ext:[9168,SIZE1,.097]}},8625:{dir:V,HW:[[.722,AMS]],stretch:{top:[8625,AMS,.27],ext:[9168,SIZE1]}},8636:{dir:H,HW:[[1,MAIN]],stretch:{left:[8636,MAIN],rep:ARROWREP}},8637:{dir:H,HW:[[1,MAIN]],stretch:{left:[8637,MAIN],rep:ARROWREP}},8638:{dir:V,HW:[[.888,AMS]],stretch:{top:[8638,AMS,.12,0,1.1],ext:[9168,SIZE1]}},8639:{dir:V,HW:[[.888,AMS]],stretch:{top:[8639,AMS,.12,0,1.1],ext:[9168,SIZE1]}},8640:{dir:H,HW:[[1,MAIN]],stretch:{right:[8640,MAIN],rep:ARROWREP}},8641:{dir:H,HW:[[1,MAIN]],stretch:{right:[8641,MAIN],rep:ARROWREP}},8642:{dir:V,HW:[[.888,AMS]],stretch:{bot:[8642,AMS,.12,0,1.1],ext:[9168,SIZE1]}},8643:{dir:V,HW:[[.888,AMS]],stretch:{bot:[8643,AMS,.12,0,1.1],ext:[9168,SIZE1]}},8666:{dir:H,HW:[[1,AMS]],stretch:{left:[8666,AMS],rep:[8801,MAIN]}},8667:{dir:H,HW:[[1,AMS]],stretch:{right:[8667,AMS],rep:[8801,MAIN]}},9140:{dir:H,HW:[],stretch:{min:.5,left:[9484,AMS,0,-.1],rep:[8722,MAIN,0,.35],right:[9488,AMS,0,-.1]}},9141:{dir:H,HW:[],stretch:{min:.5,left:[9492,AMS,0,.26],rep:[8722,MAIN,0,0,0,.25],right:[9496,AMS,0,.26]}},9180:{dir:H,HW:[[.778,AMS,0,8994],[1,MAIN,0,8994]],stretch:{left:[57680,SIZE4],rep:[57684,SIZE4],right:[57681,SIZE4]}},9181:{dir:H,HW:[[.778,AMS,0,8995],[1,MAIN,0,8995]],stretch:{left:[57682,SIZE4],rep:[57684,SIZE4],right:[57683,SIZE4]}},9184:{dir:H,HW:[],stretch:{min:1.25,left:[714,MAIN,-.1],rep:[713,MAIN,0,.13],right:[715,MAIN],fullExtenders:!0}},9185:{dir:H,HW:[],stretch:{min:1.5,left:[715,MAIN,-.1,.1],rep:[713,MAIN],right:[714,MAIN,-.1,.1],fullExtenders:!0}},10502:{dir:H,HW:[],stretch:{min:1,left:[8656,MAIN],rep:DARROWREP,right:[8739,SIZE1,0,-.1]}},10503:{dir:H,HW:[],stretch:{min:.7,left:[8872,AMS,0,-.12],rep:DARROWREP,right:[8658,MAIN]}},10574:{dir:H,HW:[],stretch:{min:.5,left:[8636,MAIN],rep:ARROWREP,right:[8640,MAIN]}},10575:{dir:V,HW:[],stretch:{min:.5,top:[8638,AMS,.12,0,1.1],ext:[9168,SIZE1],bot:[8642,AMS,.12,0,1.1]}},10576:{dir:H,HW:[],stretch:{min:.5,left:[8637,MAIN],rep:ARROWREP,right:[8641,MAIN]}},10577:{dir:V,HW:[],stretch:{min:.5,top:[8639,AMS,.12,0,1.1],ext:[9168,SIZE1],bot:[8643,AMS,.12,0,1.1]}},10586:{dir:H,HW:[],stretch:{min:1,left:[8636,MAIN],rep:ARROWREP,right:[8739,SIZE1,0,-.05,.9]}},10587:{dir:H,HW:[],stretch:{min:1,left:[8739,SIZE1,-.05,-.05,.9],rep:ARROWREP,right:[8640,MAIN]}},10588:{dir:V,HW:[],stretch:{min:.7,bot:[8869,BOLD,0,0,.75],ext:[9168,SIZE1],top:[8638,AMS,.12,0,1.1]}},10589:{dir:V,HW:[],stretch:{min:.7,top:[8868,BOLD,0,0,.75],ext:[9168,SIZE1],bot:[8642,AMS,.12,0,1.1]}},10590:{dir:H,HW:[],stretch:{min:1,left:[8637,MAIN],rep:ARROWREP,right:[8739,SIZE1,0,-.05,.9]}},10591:{dir:H,HW:[],stretch:{min:1,left:[8739,SIZE1,-.05,-.05,.9],rep:ARROWREP,right:[8641,MAIN]}},10592:{dir:V,HW:[],stretch:{min:.7,bot:[8869,BOLD,0,0,.75],ext:[9168,SIZE1],top:[8639,AMS,.12,0,1.1]}},10593:{dir:V,HW:[],stretch:{min:.7,top:[8868,BOLD,0,0,.75],ext:[9168,SIZE1],bot:[8643,AMS,.12,0,1.1]}}};for(var id in delim)delim.hasOwnProperty(id)&&(DELIMITERS[id]=delim[id]);CHTML.fontLoaded("TeX/fontdata-extra")}(MathJax.OutputJax.CommonHTML),MathJax.Hub.Register.StartupHook("CommonHTML Jax Ready",(function(){var CHTML,font;CHTML=MathJax.OutputJax.CommonHTML,font="MathJax_AMS",CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:270,ascent:1003,descent:463,32:[0,0,250,0,0],65:[701,1,722,17,703],66:[683,1,667,11,620],67:[702,19,722,39,684],68:[683,1,722,16,688],69:[683,1,667,12,640],70:[683,1,611,12,584],71:[702,19,778,39,749],72:[683,1,778,14,762],73:[683,1,389,20,369],74:[683,77,500,6,478],75:[683,1,778,22,768],76:[683,1,667,12,640],77:[683,1,944,17,926],78:[683,20,722,20,702],79:[701,19,778,34,742],80:[683,1,611,16,597],81:[701,181,778,34,742],82:[683,1,722,16,705],83:[702,12,556,28,528],84:[683,1,667,33,635],85:[683,19,722,16,709],86:[683,20,722,0,719],87:[683,19,1e3,5,994],88:[683,1,722,16,705],89:[683,1,722,16,704],90:[683,1,667,29,635],107:[683,1,556,17,534],160:[0,0,250,0,0],165:[683,0,750,11,738],174:[709,175,947,32,915],240:[749,21,556,42,509],295:[695,13,540,42,562],710:[845,-561,2333,-14,2346],732:[899,-628,2333,1,2330],770:[845,-561,0,-2347,13],771:[899,-628,0,-2332,-3],989:[605,85,778,55,719],1008:[434,6,667,37,734],8245:[560,-43,275,12,244],8463:[695,13,540,42,562],8487:[684,22,722,44,675],8498:[695,1,556,55,497],8502:[763,21,667,-22,687],8503:[764,43,444,-22,421],8504:[764,43,667,54,640],8513:[705,23,639,37,577],8592:[437,-64,500,64,422],8594:[437,-64,500,58,417],8602:[437,-60,1e3,56,942],8603:[437,-60,1e3,54,942],8606:[417,-83,1e3,56,944],8608:[417,-83,1e3,55,943],8610:[417,-83,1111,56,1031],8611:[417,-83,1111,79,1054],8619:[575,41,1e3,56,964],8620:[575,41,1e3,35,943],8621:[417,-83,1389,57,1331],8622:[437,-60,1e3,56,942],8624:[722,0,500,56,444],8625:[722,0,500,55,443],8630:[461,1,1e3,17,950],8631:[460,1,1e3,46,982],8634:[650,83,778,56,722],8635:[650,83,778,56,721],8638:[694,194,417,188,375],8639:[694,194,417,41,228],8642:[694,194,417,188,375],8643:[694,194,417,41,228],8644:[667,0,1e3,55,944],8646:[667,0,1e3,55,944],8647:[583,83,1e3,55,944],8648:[694,193,833,83,749],8649:[583,83,1e3,55,944],8650:[694,194,833,83,749],8651:[514,14,1e3,55,944],8652:[514,14,1e3,55,944],8653:[534,35,1e3,54,942],8654:[534,37,1e3,32,965],8655:[534,35,1e3,55,943],8666:[611,111,1e3,76,944],8667:[611,111,1e3,55,923],8669:[417,-83,1e3,56,943],8672:[437,-64,1334,64,1251],8674:[437,-64,1334,84,1251],8705:[846,21,500,56,444],8708:[860,166,556,55,497],8709:[587,3,778,54,720],8717:[440,1,429,102,456],8722:[270,-230,500,84,417],8724:[766,93,778,57,722],8726:[430,23,778,91,685],8733:[472,-28,778,56,722],8736:[694,0,722,55,666],8737:[714,20,722,55,666],8738:[551,51,722,55,666],8739:[430,23,222,91,131],8740:[750,252,278,-21,297],8741:[431,23,389,55,331],8742:[750,250,500,-20,518],8756:[471,82,667,24,643],8757:[471,82,667,23,643],8764:[365,-132,778,55,719],8765:[367,-133,778,56,722],8769:[467,-32,778,55,719],8770:[463,-34,778,55,720],8774:[652,155,778,54,720],8776:[481,-50,778,55,719],8778:[579,39,778,51,725],8782:[492,-8,778,56,722],8783:[492,-133,778,56,722],8785:[609,108,778,56,722],8786:[601,101,778,15,762],8787:[601,102,778,14,762],8790:[367,-133,778,56,722],8791:[721,-133,778,56,722],8796:[859,-133,778,56,723],8806:[753,175,778,83,694],8807:[753,175,778,83,694],8808:[752,286,778,82,693],8809:[752,286,778,82,693],8812:[750,250,500,74,425],8814:[708,209,778,82,693],8815:[708,209,778,82,693],8816:[801,303,778,82,694],8817:[801,303,778,82,694],8818:[732,228,778,56,722],8819:[732,228,778,56,722],8822:[681,253,778,44,734],8823:[681,253,778,83,694],8828:[580,153,778,83,694],8829:[580,154,778,82,694],8830:[732,228,778,56,722],8831:[732,228,778,56,722],8832:[705,208,778,82,693],8833:[705,208,778,82,693],8840:[801,303,778,83,693],8841:[801,303,778,82,691],8842:[635,241,778,84,693],8843:[635,241,778,82,691],8847:[539,41,778,83,694],8848:[539,41,778,64,714],8858:[582,82,778,57,721],8859:[582,82,778,57,721],8861:[582,82,778,57,721],8862:[689,0,778,55,722],8863:[689,0,778,55,722],8864:[689,0,778,55,722],8865:[689,0,778,55,722],8872:[694,0,611,55,555],8873:[694,0,722,55,666],8874:[694,0,889,55,833],8876:[695,1,611,-55,554],8877:[695,1,611,-55,554],8878:[695,1,722,-55,665],8879:[695,1,722,-55,665],8882:[539,41,778,83,694],8883:[539,41,778,83,694],8884:[636,138,778,83,694],8885:[636,138,778,83,694],8888:[408,-92,1111,55,1055],8890:[431,212,556,57,500],8891:[716,0,611,55,555],8892:[716,0,611,55,555],8901:[189,0,278,55,222],8903:[545,44,778,55,720],8905:[492,-8,778,146,628],8906:[492,-8,778,146,628],8907:[694,22,778,55,722],8908:[694,22,778,55,722],8909:[464,-36,778,56,722],8910:[578,21,760,83,676],8911:[578,22,760,83,676],8912:[540,40,778,84,694],8913:[540,40,778,83,693],8914:[598,22,667,55,611],8915:[598,22,667,55,611],8916:[736,22,667,56,611],8918:[541,41,778,82,693],8919:[541,41,778,82,693],8920:[568,67,1333,56,1277],8921:[568,67,1333,55,1277],8922:[886,386,778,83,674],8923:[886,386,778,83,674],8926:[734,0,778,83,694],8927:[734,0,778,82,694],8928:[801,303,778,82,693],8929:[801,303,778,82,694],8934:[730,359,778,55,719],8935:[730,359,778,55,719],8936:[730,359,778,55,719],8937:[730,359,778,55,719],8938:[706,208,778,82,693],8939:[706,208,778,82,693],8940:[802,303,778,82,693],8941:[801,303,778,82,693],8994:[378,-122,778,55,722],8995:[378,-143,778,55,722],9416:[709,175,902,8,894],9484:[694,-306,500,55,444],9488:[694,-306,500,55,444],9492:[366,22,500,55,444],9496:[366,22,500,55,444],9585:[694,195,889,0,860],9586:[694,195,889,0,860],9632:[689,0,778,55,722],9633:[689,0,778,55,722],9650:[575,20,722,84,637],9651:[575,20,722,84,637],9654:[539,41,778,83,694],9660:[576,19,722,84,637],9661:[576,19,722,84,637],9664:[539,41,778,83,694],9674:[716,132,667,56,611],9733:[694,111,944,49,895],10003:[706,34,833,84,749],10016:[716,22,833,48,786],10731:[716,132,667,56,611],10846:[813,97,611,55,555],10877:[636,138,778,83,694],10878:[636,138,778,83,694],10885:[762,290,778,55,722],10886:[762,290,778,55,722],10887:[635,241,778,82,693],10888:[635,241,778,82,693],10889:[761,387,778,57,718],10890:[761,387,778,57,718],10891:[1003,463,778,83,694],10892:[1003,463,778,83,694],10901:[636,138,778,83,694],10902:[636,138,778,83,694],10933:[752,286,778,82,693],10934:[752,286,778,82,693],10935:[761,294,778,57,717],10936:[761,294,778,57,717],10937:[761,337,778,57,718],10938:[761,337,778,57,718],10949:[753,215,778,84,694],10950:[753,215,778,83,694],10955:[783,385,778,82,693],10956:[783,385,778,82,693],57350:[430,23,222,-20,240],57351:[431,24,389,-20,407],57352:[605,85,778,55,719],57353:[434,6,667,37,734],57356:[752,284,778,82,693],57357:[752,284,778,82,693],57358:[919,421,778,82,694],57359:[801,303,778,82,694],57360:[801,303,778,82,694],57361:[919,421,778,82,694],57366:[828,330,778,82,694],57367:[752,332,778,82,694],57368:[828,330,778,82,694],57369:[752,333,778,82,693],57370:[634,255,778,84,693],57371:[634,254,778,82,691]},CHTML.fontLoaded("TeX/"+font.substr(8)),function(CHTML){var font="MathJax_Caligraphic-Bold";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:315,ascent:840,descent:211,weight:"bold",skew:{65:.224,66:.16,67:.16,68:.0958,69:.128,70:.128,71:.128,72:.128,73:.0319,74:.192,75:.0639,76:.16,77:.16,78:.0958,79:.128,80:.0958,81:.128,82:.0958,83:.16,84:.0319,85:.0958,86:.0319,87:.0958,88:.16,89:.0958,90:.16},32:[0,0,250,0,0],48:[460,17,575,46,528],49:[461,0,575,80,494],50:[460,0,575,51,517],51:[461,211,575,48,525],52:[469,194,575,32,542],53:[461,211,575,57,517],54:[660,17,575,48,526],55:[476,211,575,64,558],56:[661,17,575,48,526],57:[461,210,575,48,526],65:[751,49,921,39,989],66:[705,17,748,40,740],67:[703,20,613,20,599],68:[686,0,892,20,885],69:[703,16,607,37,627],70:[686,30,814,17,930],71:[703,113,682,50,671],72:[686,48,987,20,946],73:[686,0,642,-27,746],74:[686,114,779,53,937],75:[703,17,871,40,834],76:[703,17,788,41,751],77:[703,49,1378,38,1353],78:[840,49,937,-24,1105],79:[703,17,906,63,882],80:[686,67,810,20,846],81:[703,146,939,120,905],82:[686,17,990,20,981],83:[703,16,696,25,721],84:[720,69,644,38,947],85:[686,24,715,-10,771],86:[686,77,737,25,774],87:[686,77,1169,25,1206],88:[686,-1,817,56,906],89:[686,164,759,36,797],90:[686,0,818,46,853],160:[0,0,250,0,0]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_Fraktur-Bold";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:259,ascent:740,descent:223,weight:"bold",32:[0,0,250,0,0],33:[689,12,349,107,241],34:[695,-432,254,10,231],38:[696,16,871,44,839],39:[695,-436,250,80,158],40:[737,186,459,134,347],41:[735,187,459,105,326],42:[692,-449,328,40,277],43:[598,82,893,56,837],44:[107,191,328,118,253],45:[275,-236,893,54,833],46:[102,15,328,103,237],47:[721,182,593,41,550],48:[501,12,593,42,533],49:[489,0,593,54,548],50:[491,-2,593,44,563],51:[487,193,593,31,523],52:[495,196,593,13,565],53:[481,190,593,19,518],54:[704,12,593,48,547],55:[479,197,593,54,591],56:[714,5,593,45,542],57:[487,195,593,29,549],58:[457,12,255,57,197],59:[458,190,255,56,211],61:[343,-168,582,22,559],63:[697,14,428,40,422],65:[686,31,847,29,827],66:[684,31,1044,57,965],67:[676,32,723,72,726],68:[683,29,982,31,896],69:[686,29,783,74,728],70:[684,146,722,17,727],71:[687,29,927,74,844],72:[683,126,851,6,752],73:[681,25,655,32,623],74:[680,141,652,-8,616],75:[681,26,789,20,806],76:[683,28,786,30,764],77:[683,32,1239,27,1232],78:[679,30,983,26,973],79:[726,30,976,12,881],80:[688,223,977,33,943],81:[726,83,976,12,918],82:[688,28,978,31,978],83:[685,31,978,82,905],84:[686,30,790,31,802],85:[688,39,851,18,871],86:[685,29,982,25,966],87:[683,30,1235,26,1240],88:[681,35,849,32,835],89:[688,214,984,34,878],90:[677,148,711,-4,624],91:[740,130,257,36,226],93:[738,132,257,14,208],94:[734,-452,590,1,584],97:[472,32,603,80,586],98:[690,32,590,86,504],99:[473,26,464,87,424],100:[632,28,589,-1,511],101:[471,27,472,81,428],102:[687,222,388,35,372],103:[472,208,595,17,541],104:[687,207,615,89,507],105:[686,25,331,3,327],106:[682,203,332,-19,238],107:[682,25,464,34,432],108:[681,24,337,100,312],109:[476,31,921,16,900],110:[473,28,654,5,608],111:[482,34,609,107,515],112:[557,207,604,-1,519],113:[485,211,596,87,515],114:[472,26,460,13,453],115:[479,34,523,-23,481],116:[648,27,393,43,407],117:[472,32,589,9,603],118:[546,27,604,56,507],119:[549,32,918,55,815],120:[471,188,459,8,441],121:[557,221,589,60,512],122:[471,214,461,-7,378],160:[0,0,250,0,0],8216:[708,-411,254,53,187],8217:[692,-394,254,58,193],58113:[630,27,587,64,512],58114:[693,212,394,37,408],58115:[681,219,387,36,384],58116:[473,212,593,67,531],58117:[684,27,393,33,387],58120:[679,220,981,32,875],58121:[717,137,727,17,633]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_Fraktur";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:258,ascent:740,descent:224,32:[0,0,250,0,0],33:[689,12,296,91,204],34:[695,-432,215,8,196],38:[698,11,738,49,733],39:[695,-436,212,69,134],40:[737,186,389,114,293],41:[735,187,389,89,276],42:[692,-449,278,33,234],43:[598,82,756,47,709],44:[107,191,278,99,213],45:[275,-236,756,46,706],46:[102,15,278,87,200],47:[721,182,502,34,466],48:[492,13,502,42,456],49:[468,2,502,47,460],50:[474,-1,502,60,484],51:[473,182,502,39,429],52:[476,191,502,10,481],53:[458,184,502,47,440],54:[700,13,502,45,471],55:[468,181,502,37,498],56:[705,10,502,40,461],57:[469,182,502,28,466],58:[457,12,216,50,168],59:[458,189,216,47,179],61:[368,-132,756,54,725],63:[693,11,362,46,357],65:[696,26,718,22,708],66:[691,27,884,48,820],67:[685,24,613,59,607],68:[685,27,832,27,745],69:[685,24,663,86,634],70:[686,153,611,11,612],71:[690,26,785,66,710],72:[666,133,720,1,644],73:[686,26,554,30,532],74:[686,139,552,-10,522],75:[680,27,668,17,682],76:[686,26,666,33,644],77:[692,27,1050,27,1048],78:[686,25,832,27,825],79:[729,27,827,12,744],80:[692,218,828,28,804],81:[729,69,827,11,782],82:[686,26,828,27,824],83:[692,27,829,66,756],84:[701,27,669,34,676],85:[697,27,646,-25,665],86:[686,26,831,26,825],87:[686,27,1046,32,1054],88:[688,27,719,28,709],89:[686,218,833,27,740],90:[729,139,602,11,532],91:[740,130,278,117,278],93:[738,131,278,-4,160],94:[734,-452,500,0,495],97:[470,35,500,66,497],98:[685,31,513,87,442],99:[466,29,389,72,359],100:[609,33,499,13,428],101:[467,30,401,70,364],102:[681,221,326,30,323],103:[470,209,504,17,455],104:[688,205,521,77,434],105:[673,20,279,14,267],106:[672,208,281,-9,196],107:[689,25,389,24,362],108:[685,20,280,98,276],109:[475,26,767,8,753],110:[475,22,527,20,514],111:[480,28,489,67,412],112:[541,212,500,12,430],113:[479,219,489,60,419],114:[474,21,389,17,387],115:[478,29,443,-18,406],116:[640,20,333,27,348],117:[474,23,517,9,513],118:[530,28,512,55,434],119:[532,28,774,45,688],120:[472,188,389,10,363],121:[528,218,499,45,431],122:[471,214,391,-7,314],160:[0,0,250,0,0],8216:[708,-410,215,45,158],8217:[692,-395,215,49,163],58112:[683,32,497,75,430],58113:[616,30,498,35,432],58114:[680,215,333,29,339],58115:[679,224,329,28,318],58116:[471,214,503,52,449],58117:[686,20,333,26,315],58118:[577,21,334,29,347],58119:[475,22,501,10,514]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_Math-BoldItalic";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:255,ascent:725,descent:216,weight:"bold",style:"italic",skew:{65:.16,66:.0958,67:.0958,68:.0639,69:.0958,70:.0958,71:.0958,72:.0639,73:.128,74:.192,75:.0639,76:.0319,77:.0958,78:.0958,79:.0958,80:.0958,81:.0958,82:.0958,83:.0958,84:.0958,85:.0319,88:.0958,90:.0958,99:.0639,100:.192,101:.0639,102:.192,103:.0319,104:-.0319,108:.0958,111:.0639,112:.0958,113:.0958,114:.0639,115:.0639,116:.0958,117:.0319,118:.0319,119:.0958,120:.0319,121:.0639,122:.0639,915:.0958,916:.192,920:.0958,923:.192,926:.0958,928:.0639,931:.0958,933:.0639,934:.0958,936:.0639,937:.0958,945:.0319,946:.0958,948:.0639,949:.0958,950:.0958,951:.0639,952:.0958,953:.0639,956:.0319,957:.0319,958:.128,959:.0639,961:.0958,962:.0958,964:.0319,965:.0319,966:.0958,967:.0639,968:.128,977:.0958,981:.0958,1009:.0958,1013:.0639},32:[0,0,250,0,0],47:[711,210,894,160,733],65:[711,0,869,45,839],66:[686,0,866,43,853],67:[703,17,817,55,855],68:[686,0,938,43,914],69:[680,0,810,43,825],70:[680,0,689,43,809],71:[703,16,887,56,854],72:[686,0,982,43,1027],73:[686,0,511,30,573],74:[686,17,631,42,694],75:[686,0,971,43,1003],76:[686,0,756,43,711],77:[686,0,1142,43,1219],78:[686,0,950,43,1027],79:[703,17,837,53,815],80:[686,0,723,43,847],81:[703,194,869,53,815],82:[686,17,872,43,881],83:[703,17,693,63,714],84:[675,0,637,22,772],85:[686,16,800,63,877],86:[686,16,678,62,886],87:[686,17,1093,61,1207],88:[686,0,947,38,953],89:[686,0,675,40,876],90:[686,0,773,68,805],97:[452,8,633,38,607],98:[694,8,521,45,513],99:[451,8,513,40,509],100:[694,8,610,38,612],101:[452,8,554,42,509],102:[701,201,568,64,624],103:[452,202,545,0,540],104:[694,8,668,45,642],105:[694,8,405,24,367],106:[694,202,471,-12,456],107:[694,8,604,45,578],108:[694,8,348,27,296],109:[452,8,1032,24,1006],110:[452,8,713,24,687],111:[452,8,585,39,576],112:[452,194,601,-23,593],113:[452,194,542,38,550],114:[452,8,529,24,500],115:[451,8,531,57,476],116:[643,7,415,21,387],117:[452,8,681,24,655],118:[453,8,567,24,540],119:[453,8,831,24,796],120:[452,8,659,43,599],121:[452,202,590,24,587],122:[452,8,555,34,539],160:[0,0,250,0,0],915:[680,0,657,43,777],916:[711,0,958,59,904],920:[702,17,867,54,844],923:[711,0,806,44,776],926:[675,0,841,62,867],928:[680,0,982,43,1026],931:[686,0,885,69,902],933:[703,0,671,32,802],934:[686,0,767,29,737],936:[686,0,714,22,790],937:[703,0,879,93,886],945:[452,8,761,39,712],946:[701,194,660,28,637],947:[451,211,590,5,617],948:[725,8,522,39,513],949:[461,17,529,36,481],950:[711,202,508,48,521],951:[452,211,600,24,600],952:[702,8,562,40,554],953:[452,8,412,38,386],954:[452,8,668,45,642],955:[694,13,671,40,652],956:[452,211,708,33,682],957:[452,2,577,38,608],958:[711,201,508,23,490],959:[452,8,585,39,576],960:[444,8,682,23,674],961:[451,211,612,34,603],962:[451,105,424,33,457],963:[444,8,686,35,677],964:[444,13,521,23,610],965:[453,8,631,24,604],966:[452,216,747,53,703],967:[452,201,718,32,685],968:[694,202,758,24,732],969:[453,8,718,24,691],977:[701,8,692,24,656],981:[694,202,712,51,693],982:[444,8,975,23,961],1009:[451,194,612,75,603],1013:[444,7,483,44,450]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_SansSerif-Bold";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:250,ascent:750,descent:250,weight:"bold",32:[0,0,250,0,0],33:[694,0,367,110,256],34:[694,-442,558,37,420],35:[694,193,917,61,855],36:[750,56,550,49,488],37:[750,56,1029,61,966],38:[716,22,831,47,769],39:[694,-442,306,80,226],40:[750,249,428,79,366],41:[750,250,428,61,348],42:[750,-293,550,67,482],43:[617,116,856,61,794],44:[146,106,306,80,226],45:[273,-186,367,12,305],46:[146,0,306,80,226],47:[750,249,550,61,488],48:[715,22,550,43,506],49:[716,-1,550,76,473],50:[716,0,550,46,495],51:[716,22,550,46,503],52:[694,0,550,31,518],53:[694,22,550,37,494],54:[716,22,550,46,503],55:[695,11,550,46,503],56:[715,22,550,46,503],57:[716,22,550,46,503],58:[458,0,306,80,226],59:[458,106,306,80,226],61:[407,-94,856,61,794],63:[705,0,519,61,457],64:[704,11,733,61,671],65:[694,0,733,42,690],66:[694,-1,733,92,671],67:[704,11,703,61,647],68:[694,-1,794,92,732],69:[691,0,642,92,595],70:[691,0,611,92,564],71:[705,11,733,61,659],72:[694,0,794,92,702],73:[694,0,331,85,246],74:[694,22,519,46,427],75:[694,0,764,92,701],76:[694,0,581,92,534],77:[694,0,978,92,886],78:[694,0,794,92,702],79:[716,22,794,62,731],80:[694,0,703,92,641],81:[716,106,794,62,732],82:[694,0,703,92,654],83:[716,22,611,49,549],84:[688,0,733,40,692],85:[694,22,764,92,672],86:[694,-1,733,27,705],87:[694,0,1039,24,1014],88:[694,0,733,37,694],89:[694,0,733,24,708],90:[694,0,672,61,616],91:[750,250,343,79,318],93:[750,250,343,24,263],94:[694,-537,550,108,441],95:[-23,110,550,0,549],97:[475,11,525,31,472],98:[694,10,561,54,523],99:[475,11,489,37,457],100:[694,11,561,37,507],101:[474,10,511,30,480],102:[705,0,336,29,381],103:[469,206,550,17,534],104:[694,0,561,53,508],105:[695,0,256,46,208],106:[695,205,286,-71,232],107:[694,0,531,63,496],108:[694,0,256,54,201],109:[469,0,867,53,815],110:[468,0,561,53,508],111:[474,11,550,32,518],112:[469,194,561,54,523],113:[469,194,561,37,507],114:[469,0,372,54,356],115:[474,10,422,30,396],116:[589,10,404,20,373],117:[458,11,561,52,508],118:[458,0,500,26,473],119:[458,0,744,24,719],120:[458,0,500,24,475],121:[458,205,500,29,473],122:[458,0,476,31,442],126:[344,-198,550,92,457],160:[0,0,250,0,0],305:[458,0,256,54,201],567:[458,205,286,-71,232],768:[694,-537,0,-458,-218],769:[694,-537,0,-334,-93],770:[694,-537,0,-442,-109],771:[694,-548,0,-458,-93],772:[660,-560,0,-474,-77],774:[694,-552,0,-470,-80],775:[695,-596,0,-356,-194],776:[695,-595,0,-459,-91],778:[694,-538,0,-365,-119],779:[694,-537,0,-440,-94],780:[657,-500,0,-442,-109],915:[691,0,581,92,534],916:[694,0,917,60,856],920:[716,22,856,62,793],923:[694,0,672,41,630],926:[688,0,733,46,686],928:[691,0,794,92,702],931:[694,0,794,61,732],933:[715,0,856,62,793],934:[694,0,794,62,732],936:[694,0,856,61,794],937:[716,0,794,49,744],8211:[327,-240,550,0,549],8212:[327,-240,1100,0,1099],8216:[694,-443,306,81,226],8217:[694,-442,306,80,226],8220:[694,-443,558,138,520],8221:[694,-442,558,37,420]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_SansSerif-Italic";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:250,ascent:750,descent:250,style:"italic",32:[0,0,250,0,0],33:[694,0,319,110,355],34:[694,-471,500,133,472],35:[694,194,833,87,851],36:[750,56,500,56,565],37:[750,56,833,165,815],38:[716,22,758,71,747],39:[694,-471,278,190,335],40:[750,250,389,104,491],41:[750,250,389,2,390],42:[750,-306,500,156,568],43:[583,83,778,108,775],44:[98,125,278,63,209],45:[259,-186,333,51,332],46:[98,0,278,90,209],47:[750,250,500,6,600],48:[678,22,500,88,549],49:[678,0,500,88,451],50:[678,0,500,50,551],51:[678,22,500,56,544],52:[656,0,500,62,521],53:[656,22,500,50,555],54:[678,22,500,94,548],55:[656,11,500,143,596],56:[678,22,500,77,554],57:[677,22,500,77,545],58:[444,0,278,90,282],59:[444,125,278,63,282],61:[370,-130,778,88,796],63:[704,0,472,173,536],64:[705,10,667,120,707],65:[694,0,667,28,638],66:[694,0,667,90,696],67:[705,10,639,124,719],68:[694,0,722,88,747],69:[691,0,597,86,688],70:[691,0,569,86,673],71:[705,11,667,125,730],72:[694,0,708,86,768],73:[694,0,278,87,338],74:[694,22,472,46,535],75:[694,0,694,88,785],76:[694,0,542,87,516],77:[694,0,875,92,929],78:[694,0,708,88,766],79:[716,22,736,118,763],80:[694,0,639,88,690],81:[716,125,736,118,763],82:[694,0,646,88,698],83:[716,22,556,54,609],84:[688,0,681,165,790],85:[694,22,688,131,747],86:[694,0,667,161,799],87:[694,0,944,161,1076],88:[694,0,667,14,758],89:[694,0,667,151,810],90:[694,0,611,55,702],91:[750,250,289,41,425],93:[750,250,289,-31,353],94:[694,-527,500,190,533],95:[-38,114,500,50,565],97:[461,10,481,61,473],98:[694,11,517,75,539],99:[460,11,444,75,499],100:[694,10,517,73,588],101:[460,11,444,71,472],102:[705,0,306,94,494],103:[455,206,500,12,568],104:[694,0,517,73,513],105:[680,0,239,74,315],106:[680,204,267,-96,336],107:[694,0,489,76,543],108:[694,0,239,74,311],109:[455,0,794,73,790],110:[454,0,517,73,513],111:[461,11,500,69,523],112:[455,194,517,34,538],113:[455,194,517,72,538],114:[455,0,342,74,424],115:[461,11,383,35,436],116:[571,11,361,97,410],117:[444,10,517,90,537],118:[444,0,461,108,540],119:[444,0,683,108,762],120:[444,0,461,1,537],121:[444,205,461,1,540],122:[444,0,435,28,494],126:[327,-193,500,199,560],160:[0,0,250,0,0],305:[444,0,239,74,258],567:[444,204,267,-96,286],768:[694,-527,0,-270,-87],769:[694,-527,0,-190,63],770:[694,-527,0,-310,33],771:[677,-543,0,-301,60],772:[631,-552,0,-314,64],774:[694,-508,0,-284,73],775:[680,-576,0,-180,-54],776:[680,-582,0,-273,40],778:[693,-527,0,-227,-2],779:[694,-527,0,-287,63],780:[654,-487,0,-283,60],915:[691,0,542,87,646],916:[694,0,833,42,790],920:[715,22,778,119,804],923:[694,0,611,28,582],926:[688,0,667,42,765],928:[691,0,708,86,768],931:[694,0,722,55,813],933:[716,0,778,173,843],934:[694,0,722,124,743],936:[694,0,778,171,854],937:[716,0,722,44,769],8211:[312,-236,500,50,565],8212:[312,-236,1e3,50,1065],8216:[694,-471,278,190,336],8217:[694,-471,278,190,335],8220:[694,-471,500,274,614],8221:[694,-471,500,133,472]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_SansSerif";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:250,ascent:750,descent:250,32:[0,0,250,0,0],33:[694,0,319,110,208],34:[694,-471,500,32,325],35:[694,194,833,56,777],36:[750,56,500,44,444],37:[750,56,833,56,776],38:[716,22,758,42,702],39:[694,-471,278,89,188],40:[750,250,389,74,333],41:[750,250,389,55,314],42:[750,-306,500,63,436],43:[583,82,778,56,722],44:[98,125,278,89,188],45:[259,-186,333,11,277],46:[98,0,278,90,188],47:[750,250,500,56,445],48:[678,22,500,39,460],49:[678,0,500,83,430],50:[677,0,500,42,449],51:[678,22,500,42,457],52:[656,0,500,28,471],53:[656,21,500,33,449],54:[677,22,500,42,457],55:[656,11,500,42,457],56:[678,22,500,43,456],57:[677,22,500,42,457],58:[444,0,278,90,188],59:[444,125,278,89,188],61:[370,-130,778,56,722],63:[704,0,472,55,416],64:[704,11,667,56,612],65:[694,0,667,28,638],66:[694,0,667,90,610],67:[705,11,639,59,587],68:[694,0,722,88,666],69:[691,0,597,86,554],70:[691,0,569,86,526],71:[704,11,667,59,599],72:[694,0,708,86,621],73:[694,0,278,87,191],74:[694,22,472,42,388],75:[694,0,694,88,651],76:[694,0,542,87,499],77:[694,0,875,92,782],78:[694,0,708,88,619],79:[715,22,736,55,680],80:[694,0,639,88,583],81:[715,125,736,55,680],82:[694,0,646,88,617],83:[716,22,556,44,500],84:[688,0,681,36,644],85:[694,22,688,87,600],86:[694,0,667,14,652],87:[694,0,944,14,929],88:[694,0,667,14,652],89:[694,0,667,3,663],90:[694,0,611,55,560],91:[750,250,289,94,266],93:[750,250,289,22,194],94:[694,-527,500,78,421],95:[-38,114,500,0,499],97:[460,10,481,38,407],98:[694,11,517,75,482],99:[460,10,444,34,415],100:[694,10,517,33,441],101:[461,10,444,28,415],102:[705,0,306,27,347],103:[455,206,500,28,485],104:[694,0,517,73,443],105:[680,0,239,67,171],106:[680,205,267,-59,192],107:[694,0,489,76,471],108:[694,0,239,74,164],109:[455,0,794,73,720],110:[455,0,517,73,443],111:[460,10,500,28,471],112:[455,194,517,75,483],113:[455,194,517,33,441],114:[455,0,342,74,327],115:[460,10,383,28,360],116:[571,10,361,18,333],117:[444,10,517,73,443],118:[444,0,461,14,446],119:[444,0,683,14,668],120:[444,0,461,0,460],121:[444,204,461,14,446],122:[444,0,435,28,402],126:[327,-193,500,83,416],160:[0,0,250,0,0],305:[444,0,239,74,164],567:[444,205,267,-59,192],768:[694,-527,0,-417,-199],769:[694,-527,0,-302,-84],770:[694,-527,0,-422,-79],771:[677,-543,0,-417,-84],772:[631,-552,0,-431,-70],774:[694,-508,0,-427,-74],775:[680,-576,0,-302,-198],776:[680,-582,0,-397,-104],778:[694,-527,0,-319,-99],779:[694,-527,0,-399,-84],780:[654,-487,0,-422,-79],915:[691,0,542,87,499],916:[694,0,833,42,790],920:[716,21,778,56,722],923:[694,0,611,28,582],926:[688,0,667,42,624],928:[691,0,708,86,621],931:[694,0,722,55,666],933:[716,0,778,55,722],934:[694,0,722,55,666],936:[694,0,778,55,722],937:[716,0,722,44,677],8211:[312,-236,500,0,499],8212:[312,-236,1e3,0,999],8216:[694,-471,278,90,189],8217:[694,-471,278,89,188],8220:[694,-471,500,174,467],8221:[694,-471,500,32,325]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_Script";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:211,ascent:735,descent:314,skew:{65:.389,66:.194,67:.278,68:.111,69:.139,70:.222,71:.25,72:.333,73:.333,74:.417,75:.361,76:.306,77:.444,78:.389,79:.167,80:.222,81:.278,82:.194,83:.333,84:.222,85:.25,86:.222,87:.25,88:.278,89:.194,90:.306},32:[0,0,250,0,0],65:[717,8,803,35,1016],66:[708,28,908,31,928],67:[728,26,666,26,819],68:[708,31,774,68,855],69:[707,8,562,46,718],70:[735,36,895,39,990],71:[717,37,610,12,738],72:[717,36,969,29,1241],73:[717,17,809,59,946],74:[717,314,1052,92,1133],75:[717,37,914,29,1204],76:[717,17,874,14,1035],77:[721,50,1080,30,1216],78:[726,36,902,29,1208],79:[707,8,738,96,805],80:[716,37,1013,90,1031],81:[717,17,883,54,885],82:[717,17,850,-2,887],83:[708,36,868,29,1016],84:[735,37,747,92,996],85:[717,17,800,55,960],86:[717,17,622,56,850],87:[717,17,805,46,1026],88:[717,17,944,103,1131],89:[716,17,710,57,959],90:[717,16,821,83,1032],160:[0,0,250,0,0]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML),function(CHTML){var font="MathJax_Typewriter";CHTML.FONTDATA.FONTS[font]={className:CHTML.FONTDATA.familyName(font),centerline:233,ascent:694,descent:229,32:[0,0,250,0,0],33:[622,0,525,206,320],34:[623,-333,525,122,402],35:[611,0,525,36,489],36:[694,82,525,58,466],37:[694,83,525,35,489],38:[622,11,525,28,490],39:[611,-287,525,175,349],40:[694,82,525,166,437],41:[694,82,525,87,358],42:[520,-90,525,68,456],43:[531,-81,525,38,487],44:[140,139,525,173,353],45:[341,-271,525,57,468],46:[140,-1,525,193,332],47:[694,83,525,58,466],48:[621,10,525,42,482],49:[622,-1,525,99,450],50:[622,-1,525,52,472],51:[622,11,525,44,479],52:[624,-1,525,29,495],53:[611,10,525,52,472],54:[622,11,525,45,479],55:[627,10,525,44,480],56:[621,10,525,45,479],57:[622,11,525,46,479],58:[431,-1,525,193,332],59:[431,139,525,175,337],60:[557,-55,525,57,468],61:[417,-195,525,38,487],62:[557,-55,525,57,468],63:[617,0,525,62,462],64:[617,6,525,44,481],65:[623,-1,525,28,496],66:[611,-1,525,17,482],67:[622,11,525,40,484],68:[611,-1,525,16,485],69:[611,-1,525,19,502],70:[611,-1,525,22,490],71:[622,11,525,38,496],72:[611,-1,525,16,508],73:[611,-1,525,72,452],74:[611,11,525,57,479],75:[611,-1,525,18,495],76:[611,0,525,25,488],77:[611,-1,525,12,512],78:[611,0,525,20,504],79:[621,10,525,56,468],80:[611,-1,525,19,480],81:[621,138,525,56,468],82:[611,11,525,16,522],83:[622,11,525,52,472],84:[611,-1,525,26,498],85:[611,11,525,-3,528],86:[611,7,525,19,505],87:[611,7,525,12,512],88:[611,-1,525,28,495],89:[611,-1,525,20,505],90:[611,-1,525,48,481],91:[694,82,525,214,483],92:[694,83,525,58,466],93:[694,82,525,41,310],94:[611,-460,525,96,428],95:[-25,95,525,57,468],96:[681,-357,525,176,350],97:[439,6,525,48,524],98:[611,6,525,4,492],99:[440,6,525,66,466],100:[611,6,525,31,520],101:[440,6,525,48,464],102:[617,-1,525,35,437],103:[442,229,525,28,509],104:[611,-1,525,4,520],105:[612,-1,525,72,462],106:[612,228,525,48,376],107:[611,-1,525,13,507],108:[611,-1,525,51,474],109:[436,-1,525,-12,536],110:[436,-1,525,4,520],111:[440,6,525,52,472],112:[437,221,525,4,492],113:[437,221,525,34,545],114:[437,-1,525,24,487],115:[440,6,525,72,458],116:[554,6,525,25,448],117:[431,5,525,4,520],118:[431,4,525,24,500],119:[431,4,525,16,508],120:[431,-1,525,29,495],121:[431,228,525,26,500],122:[431,-1,525,34,475],123:[694,83,525,50,475],124:[694,82,525,228,297],125:[694,83,525,49,475],126:[611,-466,525,87,437],127:[612,-519,525,104,421],160:[0,0,250,0,0],305:[431,-1,525,72,462],567:[431,228,525,48,376],768:[611,-485,0,-409,-195],769:[611,-485,0,-331,-117],770:[611,-460,0,-429,-97],771:[611,-466,0,-438,-88],772:[577,-500,0,-452,-74],774:[611,-504,0,-446,-79],776:[612,-519,0,-421,-104],778:[619,-499,0,-344,-182],780:[577,-449,0,-427,-99],915:[611,0,525,25,488],916:[623,0,525,35,489],920:[621,10,525,56,468],923:[623,-1,525,30,495],926:[611,-1,525,33,491],928:[611,-1,525,16,508],931:[611,-1,525,40,484],933:[622,-1,525,38,486],934:[611,-1,525,41,483],936:[611,-1,525,37,487],937:[622,-1,525,32,492],2018:[611,-287,525,175,349],2019:[681,-357,525,176,350],8242:[623,-334,525,211,313]},CHTML.fontLoaded("TeX/"+font.substr(8))}(MathJax.OutputJax.CommonHTML)})),HUB.Browser.Select(MathJax.Message.browsers),BASE.AuthorConfig&&"function"==typeof BASE.AuthorConfig.AuthorInit&&BASE.AuthorConfig.AuthorInit(),HUB.queue=BASE.Callback.Queue(),HUB.queue.Push(["Post",STARTUP.signal,"Begin"],["Config",STARTUP],["Cookie",STARTUP],["Styles",STARTUP],["Message",STARTUP],(function(){var queue;return BASE.Callback.Queue(STARTUP.Jax(),STARTUP.Extensions()).Push({})}),["Menu",STARTUP],STARTUP.onLoad(),(function(){MathJax.isReady=!0}),["Typeset",STARTUP],["Hash",STARTUP],["MenuZoom",STARTUP],["Post",STARTUP.signal,"End"])}("MathJax")));