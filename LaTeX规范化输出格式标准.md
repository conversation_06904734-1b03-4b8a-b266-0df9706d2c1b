# LaTeX规范化输出格式标准

## 概述

经过LaTeX规范化脚本处理后，输出的LaTeX字符串将遵循统一的格式标准，确保代码的一致性和可读性。本文档详细说明了规范化后的LaTeX格式规范。

## 1. 空格处理规范

### 1.1 基本空格规则

**清理的空格：**
- 花括号前后：`{ content }` → `{content}`
- 方括号前后：`[ content ]` → `[content]`
- 圆括号前后：`( content )` → `(content)`
- 运算符前后：`a + b` → `a+b`
- 标点符号前后：`a , b` → `a,b`

**保留的空格：**
- LaTeX命令后：`\frac{1}{2} a` → `\frac{1}{2} a`
- 希腊字母后：`\alpha a` → `\alpha a`
- 数学符号后：`\sum a` → `\sum a`

### 1.2 强制空格命令

**保护的LaTeX间距命令：**
```latex
\,      # 小间距
\:      # 中间距
\;      # 大间距
\!      # 负间距
\       # 强制空格（反斜杠+空格）
\quad   # 1em间距
\qquad  # 2em间距
\enspace # 0.5em间距
\thinspace # 小间距
\medspace # 中间距
\thickspace # 大间距
\negthinspace # 负小间距
\negmedspace # 负中间距
\negthickspace # 负大间距
```

### 1.3 需要尾随空格的命令

以下命令后会自动添加空格：
```latex
\bf, \it, \rm, \sf, \tt, \sc, \em, \sl
\tiny, \scriptsize, \footnotesize, \small
\normalsize, \large, \Large, \LARGE, \huge, \Huge
\displaystyle, \textstyle, \scriptstyle, \scriptscriptstyle
\quad, \qquad, \enspace, \thinspace
\negthinspace, \medspace, \negmedspace
\thickspace, \negthickspace
\hline, \midrule, \toprule, \bottomrule
\cline, \cr, \tabularnewline
\noindent, \indent, \newline
\linebreak, \pagebreak, \clearpage
\LaTeX, \TeX, \BibTeX
```

## 2. 数学函数处理规范

### 2.1 函数名统一化

所有数学函数统一使用`\mathrm{}`格式：

**三角函数：**
```latex
\sin → \mathrm{sin}
\cos → \mathrm{cos}
\tan → \mathrm{tan}
\cot → \mathrm{cot}
\sec → \mathrm{sec}
\csc → \mathrm{csc}
```

**双曲函数：**
```latex
\sinh → \mathrm{sinh}
\cosh → \mathrm{cosh}
\tanh → \mathrm{tanh}
\coth → \mathrm{coth}
```

**反三角函数：**
```latex
\arcsin → \mathrm{arcsin}
\arccos → \mathrm{arccos}
\arctan → \mathrm{arctan}
```

**指数和对数函数：**
```latex
\ln → \mathrm{ln}
\lg → \mathrm{lg}
\log → \mathrm{log}
\exp → \mathrm{exp}
```

**其他数学函数：**
```latex
\min → \mathrm{min}
\max → \mathrm{max}
\deg → \mathrm{deg}
\arg → \mathrm{arg}
\dim → \mathrm{dim}
\ker → \mathrm{ker}
\hom → \mathrm{hom}
\mod → \mathrm{mod}
```

### 2.2 \operatorname处理

JavaScript输出的`\operatorname{函数名}`格式会被转换为`\mathrm{函数名}`：

```latex
\operatorname{sin} → \mathrm{sin}
\operatorname{cos} → \mathrm{cos}
\operatorname{tan} → \mathrm{tan}
# ... 等等
```

## 3. 括号处理规范

### 3.1 空括号清理

**删除的空括号：**
```latex
{} → (删除)
{ } → (删除)
{  } → (删除)
```

**保留的空括号：**
- 在align环境中的空括号会被保留，由专门的`fix_align_environment_braces`函数处理

### 3.2 嵌套括号简化

**双重括号简化：**
```latex
{{content}} → {content}
\sqrt{{3}} → \sqrt{3}
\mathrm{{sin}} → \mathrm{sin}
\frac{{1}}{{2}} → \frac{1}{2}
```

### 3.3 括号补全

**自动补全缺失的括号：**
```latex
\frac{1 2 → \frac{1}{2}
\sqrt{3 → \sqrt{3}
\mathrm{sin → \mathrm{sin}
```

## 4. 环境处理规范

### 4.1 数学环境统一化

**环境转换（可选，由unify_environments配置控制）：**
```latex
\begin{split}...\end{split} → \begin{aligned}...\end{aligned}
\begin{align}...\end{align} → \begin{aligned}...\end{aligned}
\begin{alignat}...\end{alignat} → \begin{aligned}...\end{aligned}
\begin{eqnarray}...\end{eqnarray} → \begin{aligned}...\end{aligned}
```

### 4.2 矩阵环境处理

**矩阵命令替换：**
```latex
\pmatrix → \mypmatrix
\matrix → \mymatrix
```

**小矩阵统一化：**
```latex
\begin{smallmatrix}...\end{smallmatrix} → \begin{matrix}...\end{matrix}
```

### 4.3 数组环境处理

**数组格式标准化：**
```latex
\begin{array}{lcr} → \begin{array}{lcr}
\begin{array}{c} → \begin{array}{c}
```

## 5. 字体命令处理规范

### 5.1 字体命令统一化

**\rm命令转换：**
```latex
{\rm{content}} → \mathrm{content}
{ \rm{content}} → \mathrm{content}
\rm{content} → \mathrm{content}
```

**mbox和hbox转换：**
```latex
\mbox{content} → \mathrm{content}
\hbox{content} → \mathrm{content}
```

### 5.2 字体命令清理

**移除的字体命令：**
```latex
\lowercase → (删除)
\uppercase → (删除)
\raggedright → (删除)
\arraybackslash → (删除)
```

## 6. 特殊符号处理规范

### 6.1 连字符展开

**连字符标准化：**
```latex
--- → - - -
-- → - -
```

### 6.2 省略号统一化

**省略号标准化：**
```latex
\ldots → . . .
\cdots → . . .
\vdots → . . .
\ddots → . . .
\hdots → . . .
\dotsc → . . .
\dotsi → . . .
\dotsm → . . .
\dotso → . . .
\dotsb → . . .
… → . . .
```

### 6.3 颜色命令移除

**移除的颜色相关命令：**
```latex
\colorbox{red}{content} → content
\color{blue}{content} → content
\textcolor{green}{content} → content
\cellcolor{yellow}{content} → content
```

## 7. 命令处理规范

### 7.1 间距命令处理

**特殊间距命令转换：**
```latex
\~ → (空格)
\> → (空格)
$ → (空格)
```

### 7.2 标签命令移除

**移除的标签命令：**
```latex
\label{eq:example} → (删除)
```

### 7.3 注释处理

**注释清理：**
```latex
% 这是注释 → (删除整行)
a + b % 行内注释 → a + b
```

## 8. 换行符处理规范

### 8.1 结构化环境中的换行符

**保留换行符的环境：**
- `\begin{matrix}...\end{matrix}`
- `\begin{array}...\end{array}`
- `\begin{align}...\end{align}`
- `\begin{cases}...\end{cases}`
- `\begin{alignat}...\end{alignat}`

### 8.2 非结构化环境中的换行符

**换行符转换：**
```latex
\\ → \, (小间距)
```

## 9. 输出格式示例

### 9.1 简单数学表达式

**输入：**
```latex
$a + b = c$
```

**输出：**
```latex
a+b=c
```

### 9.2 分数表达式

**输入：**
```latex
$\frac{1}{2} + \frac{3}{4}$
```

**输出：**
```latex
\frac{1}{2} +\frac{3}{4}
```

### 9.3 数学函数

**输入：**
```latex
$\sin(x) + \cos(y) + \operatorname{tan}(z)$
```

**输出：**
```latex
\mathrm{sin}(x) +\mathrm{cos}(y) +\mathrm{tan}(z)
```

### 9.4 矩阵表达式

**输入：**
```latex
$\begin{pmatrix} a & b \\ c & d \end{pmatrix}$
```

**输出：**
```latex
\begin{pmatrix} a & b \\ c & d \end{pmatrix}
```

### 9.5 复杂表达式

**输入：**
```latex
$\begin{align*} S = \frac{1}{2} \int d\eta \, d^3{\bf k} \left[ \vert v_k^{\prime} \vert^2 -k^2 \vert v_k \vert^2 + \frac{a^{\prime \prime}}{a} \vert v_k \vert^2 \right] \end{align*}$
```

**输出：**
```latex
\begin{align*} S =\frac{1}{2} \int d\eta \, d^3{\bf k} \left[ \vert v_k^{\prime} \vert^2 -k^2 \vert v_k \vert^2 +\frac{a^{\prime \prime}}{a} \vert v_k \vert^2 \right] \end{align*}
```

## 10. 配置选项影响

### 10.1 remove_trailing选项

**启用时：**
- 移除公式尾部的间距命令
- 移除装饰性命令
- 清理尾部标点符号

### 10.2 enable_synonym_replacement选项

**启用时：**
- 根据`token_unify.csv`配置文件替换同义词
- 统一不同表达方式的相同含义

### 10.3 unify_environments选项

**启用时：**
- 将各种对齐环境统一为`aligned`
- 将小矩阵环境统一为`matrix`

## 11. 质量保证

### 11.1 鲁棒性保证

- 处理失败时返回原始字符串
- 完善的异常处理机制
- 保护特殊命令不被错误修改

### 11.2 一致性保证

- 统一的空格处理策略
- 标准化的数学函数格式
- 一致的括号使用规范

### 11.3 可读性保证

- 合理的空格分隔
- 清晰的命令结构
- 规范的格式标准

这个规范化标准确保了LaTeX代码的一致性和可读性，为图片转LaTeX码模型的训练提供了高质量的标准化数据。 