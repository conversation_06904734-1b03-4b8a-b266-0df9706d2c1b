| 处理对象 | 处理方式 | MathNet处理规则 | LaTeX_OCR处理规则 | UniMERNet处理规则 |
|---------|---------|---------|----------|----------|
| 换行符处理 | 字符清理 | 删除`\r`字符，替换为空格 | 删除`\r`字符，替换为空格 | √ |
| 注释处理 | 字符清理 | 移除以`%`开头的注释行，删除行内`%`后的内容 | 移除以`%`开头的注释行，删除行内`%`后的内容 | √ |
| 空间命令 | 字符替换 | `\~` → 空格，`\>` → 空格，`$` → 空格 | `\~` → 空格，`\>` → 空格，`$` → 空格 | √ |
| 标签命令 | 命令移除 | 移除`\label{.*?}`标签命令 | 移除`\label{.*?}`标签命令 | √ |
| 换行控制 | 条件处理 | 在非matrix/cases/array/begin环境中：`\\` → `\,` | 在非matrix/cases/array/begin环境中：`\\` → `\,` | √ |
| 字体命令 | 命令规范化 | `{\\rm` → `\mathrm{`，`{ \\rm` → `\mathrm{`，`\rm{` → `\mathrm{` | `{\\rm` → `\mathrm{`，`{ \\rm` → `\mathrm{`，`\rm{` → `\mathrm{` | √ |
| 数学环境 | 环境统一化 | 无处理 | 无处理 | `split`, `align`, `alignedat`, `alignat`, `eqnarray`及星号版本 → `aligned` |
| 矩阵环境 | 环境统一化 | 无处理 | 无处理 | `smallmatrix`及星号版本 → `matrix` |
| 数学运算符 | 命令简化 | 无处理 | 无处理 | `\operatorname{函数名}` → `\函数名` |
| 标准数学函数 | 函数识别 | 无处理 | 无处理 | 识别29个预定义函数：`arccos`, `arcsin`, `arctan`, `arg`, `cos`, `cosh`, `cot`, `coth`, `csc`, `deg`, `det`, `dim`, `exp`, `gcd`, `hom`, `inf`, `injlim`, `ker`, `lg`, `lim`, `liminf`, `limsup`, `ln`, `log`, `max`, `min`, `Pr`, `projlim`, `sec`, `sin`, `sinh`, `sup`, `tan`, `tanh` |
| 矩阵命令 | 命令替换 | 无处理 | 无处理 | `\pmatrix` → `\mypmatrix`, `\matrix` → `\mymatrix` |
| 对齐命令 | 命令移除 | 无处理 | 无处理 | 移除`\raggedright`, `\arraybackslash`, `\lowercase`, `\uppercase` |
| 空间命令格式化 | 格式标准化 | 无处理 | 无处理 | `\hspace {数值 单位}` → `\hspace{数值单位}` |
| 数组环境格式化 | 格式标准化 | 无处理 | 无处理 | `\begin{array} { 对齐参数 }` → `\begin{array}{对齐参数}` |
| 连字符 | 字符拆分 | 无处理 | 无处理 | `--` → `- -`, `---` → `- - -` |
| 省略号 | 字符统一化 | 无处理 | 无处理 | `…`, `\ldots`, `\hdots`, `\cdots`, `\dots`, `\dotsc`, `\dotsi`, `\dotsm`, `\dotso`, `\dotsb`, `\mathellipsis` → `. . .` |
| 函数字符拆分 | 字符级拆分 | 无处理 | 无处理 | 25个数学函数拆分为字符：`\sin` → `\mathrm { s i n }`等 |
| 特殊令牌 | 令牌合并/替换 | 无处理 | 无处理 | `\string xxx` → `\stringxxx`; 移除`\operatorname *`中的`*`; 移除`\lefteqn`; `\footnote` → `^`等 |
| 单参数令牌 | 括号补全 | 无处理 | 无处理 | `\hat \lambda` → `\hat {\lambda}` (16个令牌) |
| 不可见单参数令牌 | 括号补全 | 无处理 | 无处理 | `\mathrm x` → `\mathrm {x}` (19个令牌) |
| 双参数令牌 | 括号补全 | 无处理 | 无处理 | `\frac {\lambda} 2` → `\frac {\lambda} {2}` |
| 特殊参数令牌 | 括号补全 | 无处理 | 无处理 | `\sqrt A B` → `\sqrt {A} B`; `\sqrt [A] B` → `\sqrt [A] {B}` |
| 文本命令空格 | 空格保护 | 保护`\operatorname`, `\mathrm`, `\text`, `\mathbf`等命令内的空格 | 保护`\operatorname`, `\mathrm`, `\text`, `\mathbf`等命令内的空格 | √ |
| 多余空格 | 空格压缩 | 移除非反斜杠后的连续空格，保护特定命令空格 | 移除非反斜杠后的连续空格，保护特定命令空格 | 迭代压缩字母与非字母间空格：非字母-非字母、非字母-字母、字母-非字母的空格 |
| 尾部清理 | 命令移除 | 无处理 | 无处理 | 移除公式尾部的空间命令和标点符号 |
| 多行处理 | 结构扁平化 | 无处理 | 无处理 | 提取数组环境内容，处理配对括号，移除换行符 |
| 最终输出 | 编码修复 | 修复编码问题和特殊字符 | 修复编码问题和特殊字符 | √ |
| 处理对象 | 处理方式 | MathNet处理规则 | LaTeX_OCR处理规则 | UniMERNet处理规则 |
|---------|---------|---------|----------|----------|
| TikZ图形 | 内容过滤 | 无处理 | 拒绝包含'tikz'的公式 | 无处理 |
| 引用标签 | 标签移除 | 移除`\ref`, `\cite`, `\label`, `\eqref`标签 | 移除`\ref`, `\cite`, `\label`, `\eqref`标签 | √ |
| 编号控制 | 命令移除 | 移除`\notag`, `\nonumber`命令 | 移除`\notag`, `\nonumber`命令 | √ |
| 换行符清理 | 字符移除 | 移除换行符`\n` | 移除换行符`\n` | √ |
| 外部空白 | 空白清理 | 移除公式首尾的空间命令：`\,`, `~`, `\ `, `\thinspace`, `\!`, `\:`, `\;`, `\enspace`, `\quad`, `\qquad`, `\hspace{...}`, `\hfill` | 移除公式首尾的空间命令：`\,`, `~`, `\ `, `\thinspace`, `\!`, `\:`, `\;`, `\enspace`, `\quad`, `\qquad`, `\hspace{...}`, `\hfill` | √ |
| 括号检查 | 语法验证 | 检查大括号配对，移除外层多余括号 | 检查大括号配对，移除外层多余括号 | √ |
| 新命令过滤 | 内容过滤 | 拒绝包含'newcommand'的公式 | 拒绝包含'newcommand'的公式 | √ |
| 最小长度 | 长度验证 | 过滤长度小于最小字符数的公式 | 过滤长度小于最小字符数的公式 | √ |
| 令牌拆分 | 字符级处理 | 拆分复合令牌：`\left(`, `\right)`, `,\\;`, `,\\qquad`等 | 无处理 | 无处理 |
| 令牌替换 | 标准化处理 | 根据token_types2.csv进行令牌替换和移除 | 无处理 | 无处理 |
| 空括号移除 | 结构清理 | 移除空的`{ }`括号对 | 无处理 | 无处理 |
| 非LaTeX命令拆分 | 字符拆分 | 将包含`\`但不以`\`开头的令牌拆分为单个字符 | 无处理 | 无处理 |
| 数组处理 | 结构优化 | 处理数组环境，移除单行数组，标准化数组定义 | 无处理 | 无处理 |
| MathType过滤 | 内容过滤 | 移除包含"MathType!"的公式 | 无处理 | 无处理 |
| 首字符检查 | 语法验证 | 移除以`_`或`^`开头的公式 | 无处理 | 无处理 |
| 分数括号补全 | 语法补全 | 为`\frac`命令自动补全括号 | 无处理 | 无处理 |
| 上下标括号补全 | 语法补全 | 为`_`和`^`命令自动补全括号 | 无处理 | 无处理 |
| 上下标排序 | 顺序标准化 | 统一上下标顺序：先上标后下标 | 无处理 | 无处理 |
| 样式元素移除 | 内容清理 | 移除文本样式元素 | 无处理 | 无处理 |
| 多余括号移除 | 结构优化 | 移除不必要的括号 | 无处理 | 无处理 |
| 公式过滤 | 条件移除 | 移除包含特定令牌的完整公式 | 无处理 | 无处理 |
| 图像尺寸过滤 | 尺寸验证 | 过滤过大尺寸的图像（默认最大宽度500px，高度160px） | 无处理 | 无处理 |
| 令牌数量过滤 | 长度验证 | 过滤令牌数量过多的公式（默认最大150个令牌） | 无处理 | 无处理 |
| 解析失败过滤 | 质量验证 | 移除无法解析的公式 | 无处理 | 无处理 | 