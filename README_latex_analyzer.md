# LaTeX Token长度和复杂度分析器

## 简介

这是一个专门用于分析LaTeX公式token长度和复杂程度的Python脚本。它可以对LaTeX代码进行全面的统计分析，生成详细的报告和可视化图表。

## 功能特性

### 🔍 多维度分析
- **Token长度分析**: 字符数、空格分割token数、LaTeX命令数、数学符号数
- **复杂度评估**: 嵌套深度、命令复杂度、环境复杂度、综合复杂度评分
- **统计分析**: 均值、中位数、标准差、最值等描述性统计
- **分级分类**: 将公式按复杂度分为简单、中等、复杂、极复杂四个等级

### 📊 可视化输出
- Token长度分布直方图
- 复杂度分布直方图
- 复杂度等级饼图
- 字符数vs复杂度散点图

### 📄 多格式报告
- 详细的文本分析报告
- CSV格式的详细数据
- 可视化图表(PNG格式)

## 安装依赖

```bash
pip install matplotlib seaborn pandas numpy
```

## 使用方法

### 基本用法

```bash
python latex_analyzer.py input_file.txt
```

### 高级用法

```bash
# 指定输出文件前缀
python latex_analyzer.py input_file.txt --output-prefix my_analysis

# 不生成可视化图表
python latex_analyzer.py input_file.txt --no-viz
```

### 参数说明

- `input_file`: 输入的LaTeX文件路径（必需）
- `--output-prefix`: 输出文件前缀（可选，默认为'latex_analysis'）
- `--no-viz`: 不生成可视化图表（可选）

## 输出文件

运行脚本后会生成以下文件：

1. **`{prefix}_report.txt`**: 详细的分析报告
2. **`{prefix}_detailed.csv`**: 每个公式的详细分析数据
3. **`latex_complexity_analysis.png`**: 可视化图表

## 复杂度评分系统

### 评分公式
```
复杂度分数 = 空格token数×0.5 + 命令数×1.0 + 嵌套深度×2.0 + 命令复杂度 + 环境复杂度
```

### 命令权重分类
- **简单命令** (权重1): sin, cos, alpha, beta等基础数学符号
- **中等命令** (权重2): frac, sqrt, operatorname等格式化命令
- **复杂命令** (权重3): int, sum, prod, lim等积分求和命令
- **环境命令** (权重4): matrix, align, array等环境命令

### 复杂度等级
- **简单**: 复杂度分数 ≤ 25百分位数
- **中等**: 25百分位数 < 复杂度分数 ≤ 50百分位数
- **复杂**: 50百分位数 < 复杂度分数 ≤ 75百分位数
- **极复杂**: 复杂度分数 > 75百分位数

## 示例

### 输入文件格式
```latex
\sin(x) + \cos(y) = \frac{1}{2}
\begin{align} x^2 + y^2 = z^2 \end{align}
\sqrt{a^2 + b^2}
\int_{0}^{1} f(x) dx
```

### 输出报告示例
```
============================================================
LaTeX公式Token长度和复杂度分析报告
============================================================

1. 基本统计信息
------------------------------
总公式数量: 4
有效公式数量: 4
空公式数量: 0

2. Token长度分析
------------------------------
字符数统计:
  平均值: 28.50
  中位数: 26.50
  标准差: 12.34
  最小值: 16
  最大值: 45

...
```

## 技术细节

### Token化方法
1. **字符级别**: 统计总字符数
2. **空格分割**: 按空格分割计算基础token数
3. **命令提取**: 使用正则表达式提取LaTeX命令
4. **符号统计**: 统计数学符号和变量

### 嵌套深度计算
- 识别各种括号类型: `()`, `[]`, `{}`
- 处理LaTeX转义括号: `\{`, `\}`, `\[`, `\]`
- 计算最大嵌套层数

### 环境识别
- 自动识别`\begin{...}\end{...}`环境
- 根据环境类型分配不同复杂度权重
- 支持matrix、align、array等常见数学环境

## 注意事项

1. **文件编码**: 确保输入文件使用UTF-8编码
2. **内存使用**: 大文件分析可能消耗较多内存
3. **可视化**: 如果无法生成图表，请检查matplotlib安装
4. **中文显示**: 可视化图表中的中文需要系统支持相应字体

## 错误处理

- 文件不存在时会显示错误信息
- 空文件或无效LaTeX代码会被跳过
- 可视化失败不会影响其他分析结果

## 扩展性

脚本设计具有良好的扩展性，可以轻松添加：
- 新的复杂度评估指标
- 更多的LaTeX命令识别
- 不同的可视化图表类型
- 其他输出格式支持

## 版本信息

- **版本**: 1.0
- **Python要求**: 3.6+
- **依赖库**: matplotlib, seaborn, pandas, numpy

## 许可证

本项目采用MIT许可证，可自由使用和修改。
