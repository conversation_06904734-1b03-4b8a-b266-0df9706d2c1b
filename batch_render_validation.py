#!/usr/bin/env python3
"""
LaTeX批量渲染验证脚本
从labels.json中均匀抽样1000个LaTeX公式，进行规范化前后的渲染对比

功能：
1. 从labels.json中均匀抽样1000个LaTeX公式
2. 对每个公式进行规范化处理
3. 生成渲染前后的对比文件
4. 验证规范化是否影响渲染效果

使用方法：
python batch_render_validation.py
"""

import json
import random
import os
import subprocess
import tempfile
from pathlib import Path
from normalize_optimized import normalize_latex_string
import time

# 配置参数
SAMPLE_SIZE = 1000  # 抽样数量
OUTPUT_FILE = "render_comparison.txt"  # 输出文件名
LABELS_FILE = "labels.json"  # 标签文件

def load_labels():
    """加载labels.json文件"""
    try:
        with open(LABELS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载labels.json失败: {e}")
        return {}

def sample_latex_formulas(labels, sample_size):
    """从labels中均匀抽样LaTeX公式"""
    if len(labels) <= sample_size:
        # 如果总数小于等于抽样数量，返回全部
        return list(labels.items())
    
    # 均匀抽样
    keys = list(labels.keys())
    sampled_keys = random.sample(keys, sample_size)
    return [(key, labels[key]) for key in sampled_keys]

def create_latex_document(latex_formula):
    """创建完整的LaTeX文档"""
    return f"""\\documentclass[12pt]{{article}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}
\\usepackage{{mathtools}}
\\usepackage{{geometry}}
\\geometry{{margin=1in}}
\\begin{{document}}
\\begin{{equation}}
{latex_formula}
\\end{{equation}}
\\end{{document}}"""

def render_latex(latex_formula, timeout=30):
    """
    渲染LaTeX公式
    
    Args:
        latex_formula: LaTeX公式字符串
        timeout: 超时时间（秒）
    
    Returns:
        (success: bool, error_message: str)
    """
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建LaTeX文档
            latex_doc = create_latex_document(latex_formula)
            tex_file = temp_path / "formula.tex"
            
            with open(tex_file, 'w', encoding='utf-8') as f:
                f.write(latex_doc)
            
            # 编译LaTeX文档
            cmd = f'pdflatex -interaction=nonstopmode -output-directory="{temp_path}" "{tex_file}"'
            
            result = subprocess.run(
                cmd, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                encoding='utf-8',
                errors='replace'
            )
            
            # 检查是否生成了PDF文件
            pdf_file = temp_path / "formula.pdf"
            if pdf_file.exists() and result.returncode == 0:
                return True, ""
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                return False, error_msg
                
    except subprocess.TimeoutExpired:
        return False, "渲染超时"
    except Exception as e:
        return False, f"渲染异常: {str(e)}"

def validate_normalization():
    """主验证函数"""
    print("开始LaTeX批量渲染验证...")
    print(f"抽样数量: {SAMPLE_SIZE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print("-" * 80)
    
    # 1. 加载labels.json
    print("1. 加载labels.json...")
    labels = load_labels()
    if not labels:
        print("❌ 无法加载labels.json文件")
        return
    
    print(f"✅ 成功加载 {len(labels)} 个LaTeX公式")
    
    # 2. 均匀抽样
    print("2. 进行均匀抽样...")
    sampled_formulas = sample_latex_formulas(labels, SAMPLE_SIZE)
    print(f"✅ 成功抽样 {len(sampled_formulas)} 个公式")
    
    # 3. 处理每个公式
    print("3. 开始处理公式...")
    
    results = []
    success_count = 0
    fail_count = 0
    
    for i, (filename, original_latex) in enumerate(sampled_formulas, 1):
        print(f"处理进度: {i}/{len(sampled_formulas)}", end="\r")
        
        # 规范化处理
        try:
            normalized_latex = normalize_latex_string(original_latex)
        except Exception as e:
            normalized_latex = original_latex
            print(f"\n⚠️ 规范化失败: {e}")
        
        # 渲染原始公式
        original_success, original_error = render_latex(original_latex)
        
        # 渲染规范化后的公式
        normalized_success, normalized_error = render_latex(normalized_latex)
        
        # 记录结果
        result = {
            'filename': filename,
            'original_latex': original_latex,
            'normalized_latex': normalized_latex,
            'original_render_success': original_success,
            'normalized_render_success': normalized_success,
            'original_error': original_error,
            'normalized_error': normalized_error,
            'render_consistent': original_success == normalized_success
        }
        
        results.append(result)
        
        # 统计成功/失败数量
        if original_success and normalized_success:
            success_count += 1
        elif not original_success and not normalized_success:
            success_count += 1
        else:
            fail_count += 1
    
    print(f"\n✅ 处理完成!")
    
    # 4. 生成对比文件
    print("4. 生成对比文件...")
    
    with open(OUTPUT_FILE, 'w', encoding='utf-8-sig') as f:
        f.write("LaTeX Normalization Render Validation Report\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"Sample Size: {SAMPLE_SIZE}\n")
        f.write(f"Total Formulas: {len(labels)}\n")
        f.write(f"Render Consistency: {success_count}/{len(results)} ({success_count/len(results)*100:.2f}%)\n")
        f.write(f"Render Inconsistency: {fail_count}/{len(results)} ({fail_count/len(results)*100:.2f}%)\n")
        f.write(f"Generated Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("Detailed Comparison Results:\n")
        f.write("-" * 80 + "\n\n")
        
        for i, result in enumerate(results, 1):
            f.write(f"Formula {i}: {result['filename']}\n")
            f.write(f"Original LaTeX: {result['original_latex']}\n")
            f.write(f"Normalized LaTeX: {result['normalized_latex']}\n")
            f.write(f"Original Render: {'SUCCESS' if result['original_render_success'] else 'FAILED'}")
            if not result['original_render_success']:
                f.write(f" ({result['original_error']})")
            f.write("\n")
            f.write(f"Normalized Render: {'SUCCESS' if result['normalized_render_success'] else 'FAILED'}")
            if not result['normalized_render_success']:
                f.write(f" ({result['normalized_error']})")
            f.write("\n")
            f.write(f"Render Consistency: {'CONSISTENT' if result['render_consistent'] else 'INCONSISTENT'}\n")
            f.write("-" * 60 + "\n\n")
    
    print(f"✅ 对比文件已生成: {OUTPUT_FILE}")
    
    # 5. 输出统计结果
    print("\n" + "=" * 80)
    print("验证结果统计:")
    print(f"总处理公式数: {len(results)}")
    print(f"渲染一致性: {success_count} ({success_count/len(results)*100:.2f}%)")
    print(f"渲染不一致: {fail_count} ({fail_count/len(results)*100:.2f}%)")
    print("=" * 80)
    
    if fail_count == 0:
        print("🎉 所有公式的渲染效果在规范化前后保持一致！")
    else:
        print(f"⚠️ 有 {fail_count} 个公式的渲染效果在规范化前后不一致，请查看详细报告。")

def main():
    """主函数"""
    # 设置随机种子以确保结果可重现
    random.seed(42)
    
    # 检查labels.json是否存在
    if not os.path.exists(LABELS_FILE):
        print(f"❌ 找不到文件: {LABELS_FILE}")
        return
    
    # 执行验证
    validate_normalization()

if __name__ == "__main__":
    main() 