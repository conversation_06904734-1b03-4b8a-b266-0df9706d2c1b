#!/usr/bin/env python3
"""
测试运算符空格修复效果
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from latex_normalizer import LaTeXNormalizer


def test_operator_spacing():
    """测试运算符空格修复"""
    print("=" * 80)
    print("运算符空格修复测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        # 二元运算符测试
        ("times运算符", "J_1 \\times J_1"),
        ("cdot运算符", "a \\cdot b"),
        ("plus运算符", "a + b"),
        ("minus运算符", "a - b"),
        ("pm运算符", "a \\pm b"),
        ("oplus运算符", "a \\oplus b"),
        
        # 关系运算符测试
        ("等号", "a = b"),
        ("不等号", "a \\neq b"),
        ("小于", "a < b"),
        ("大于", "a > b"),
        ("leq运算符", "a \\leq b"),
        ("geq运算符", "a \\geq b"),
        
        # 复杂表达式测试
        ("复杂表达式1", "a \\times b + c = d"),
        ("复杂表达式2", "x \\cdot y \\neq z"),
        ("复杂表达式3", "\\alpha \\oplus \\beta \\leq \\gamma"),
        
        # 原问题样本
        ("原问题样本", "( J _ { 1 } \\cdot J _ { 1 } ) _ { 0 } = ( J _ { 1 } ^ { 2 } ) _ { 0 }"),
        ("原问题完整", "( J _ { 1 } \\cdot J _ { 1 } ) _ { 0 } = ( J _ { 1 } ^ { 2 } ) _ { 0 } = \\sum _ { m _ { 1 } } \\sum _ { m _ { 2 } } \\: \\langle 1 , m _ { 1 } ; 1 , m _ { 2 } | 0 , 0 \\rangle \\; J _ { 1 , m _ { 1 } } J _ { 1 , m _ { 2 } }"),
        
        # 上下标中的运算符（应该不添加空格）
        ("上下标中的运算符", "x^{a+b}"),
        ("分数中的运算符", "\\frac{a+b}{c-d}"),
    ]
    
    for desc, latex_input in test_cases:
        try:
            latex_output = normalizer.normalize(latex_input)
            has_changed = latex_input != latex_output
            
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  规范后: {latex_output}")
            print(f"  状态: {'✓ 已规范化' if has_changed else '─ 无变化'}")
            
            # 检查特定问题
            if "\\times" in latex_input:
                if "\\times J" in latex_output or "\\timesJ" in latex_output:
                    print("  ⚠️  问题: \\times后面空格丢失")
                elif "\\times " in latex_output:
                    print("  ✓ \\times后面有正确的空格")
                else:
                    print("  ? \\times空格情况需要检查")
            
            if "\\cdot" in latex_input:
                if "\\cdot J" in latex_output or "\\cdotJ" in latex_output:
                    print("  ⚠️  问题: \\cdot后面空格丢失")
                elif "\\cdot " in latex_output:
                    print("  ✓ \\cdot后面有正确的空格")
            
            # 检查其他运算符
            operators = ["\\times", "\\cdot", "+", "-", "=", "\\neq", "<", ">", "\\leq", "\\geq"]
            for op in operators:
                if op in latex_input:
                    # 检查运算符后是否有适当的分隔
                    if op + " " in latex_output or (op in latex_output and not any(op + char in latex_output for char in "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")):
                        continue  # 正常情况
                    else:
                        # 检查是否有运算符直接连接字母的情况
                        import re
                        pattern = re.escape(op) + r'[a-zA-Z]'
                        if re.search(pattern, latex_output):
                            print(f"  ⚠️  问题: {op}后面可能缺少空格")
            
        except Exception as e:
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  错误: {e}")
            print(f"  状态: ✗ 处理失败")


def test_specific_problem():
    """测试原始问题"""
    print("\n" + "=" * 80)
    print("原始问题专项测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    # 原始问题
    problem_input = "( J _ { 1 } \\cdot J _ { 1 } ) _ { 0 } = ( J _ { 1 } ^ { 2 } ) _ { 0 } = \\sum _ { m _ { 1 } } \\sum _ { m _ { 2 } } \\: \\langle 1 , m _ { 1 } ; 1 , m _ { 2 } | 0 , 0 \\rangle \\; J _ { 1 , m _ { 1 } } J _ { 1 , m _ { 2 } }"
    expected_issue = "(J_1 \\timesJ_1)_0"  # 这是有问题的输出
    
    print("原始问题测试:")
    print(f"输入: {problem_input}")
    
    try:
        result = normalizer.normalize(problem_input)
        print(f"输出: {result}")
        
        print("\n详细检查:")
        
        # 检查\\times后面的空格
        if "\\times J" in result:
            print("✓ \\times后面有正确的空格")
        elif "\\timesJ" in result:
            print("✗ \\times后面空格丢失")
        else:
            print("? 没有找到\\times，可能被处理为其他符号")
        
        # 检查\\cdot后面的空格
        if "\\cdot J" in result:
            print("✓ \\cdot后面有正确的空格")
        elif "\\cdotJ" in result:
            print("✗ \\cdot后面空格丢失")
        else:
            print("? 没有找到\\cdot")
        
        # 检查等号周围的空格
        if " = " in result:
            print("✓ 等号周围有适当的空格")
        elif "=" in result:
            print("? 等号存在但空格情况需要检查")
        
    except Exception as e:
        print(f"处理失败: {e}")


if __name__ == "__main__":
    test_operator_spacing()
    test_specific_problem()
