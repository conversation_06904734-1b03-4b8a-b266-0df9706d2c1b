| 处理类型 | 处理前 | 处理后 | 备注（涵盖对象/正则/特殊说明） | UniMERNet | MathNet | TexTeller | LaTeX_OCR | im2latexv3 |
|----------|--------|--------|-------------------------------|----------|---------|----------|----------|----------|
| **=== 预处理阶段（AST节点处理前）===** | | | | | | | | |
| 空白命令标准化 | `a \hskip 1 . 5 cm b`<br>`\hskip1.5cm` | `a \hspace{1.5cm} b`<br>`\hspace{1.5cm}` | 使用perl正则：`hskip<数值>(cm\|in\|pt\|mm\|em)` → `hspace{<数值><单位>}` | √ | √ | 不一致：`\vspace{}` 完全移除 | - | √ |
| 字符编码清理 | `\r` | ` ` (空格) | 回车符替换为空格 | √ | √ | √ | √ | √ |
| 注释移除 | `a + b % 这是注释`<br>`% 整行注释` | `a + b`<br>`` (移除) | % 开头的注释内容完全移除，使用`line.split('%')[0]` | √ | √ | √ | √ | √ |
| 空白符号处理 | `\~`<br>`\>`<br>`$`<br>`\label{eq1}` | ` ` (空格)<br>` ` (空格)<br>` ` (空格)<br>`` (移除) | 特殊空白符号统一处理，循环300次替换 | √ | √ | √ | √ | √ |
| 非矩阵\\处理 | `a \\ b` (非矩阵环境) | `a \, b` | 非矩阵环境中的换行符替换为小空格，检查matrix/cases/array/begin环境 | √ | √ | 不一致：环境中 `\\` → `\\\n`（换行） | √ | √ |
| 字体命令标准化 | `{\rm ABC}`<br>`\mbox{text}`<br>`\hbox{text}` | `\mathrm{ABC}`<br>`\mathrm{text}`<br>`\mathrm{text}` | rm格式统一，mbox/hbox在AST中转换为mathrm | √ | √ | 不一致：`\mbox{}`、`\hbox{}` → 内容（移除命令） | √ | √ |
| 数学环境标准化 | `\begin{split}`<br>`\begin{align}`<br>`\begin{eqnarray}`<br>`\begin{smallmatrix}` | `\begin{aligned}`<br>`\begin{aligned}`<br>`\begin{aligned}`<br>`\begin{matrix}` | 对齐环境：split、align、alignedat、alignat、eqnarray及其*版本统一为aligned；矩阵环境：小矩阵环境统一为标准矩阵 | √ | 不一致：保持原始环境名称，支持array和tabular环境 | √ | √ | 不一致：保持原始环境名称，支持array和tabular环境 |
| 命令转换宏展开 | `\def\command{内容}`<br>`\let\command=\other` | `\newcommand*{\command}{内容}`<br>`\newcommand*{\command}{\other}` | 宏定义命令标准化转换 | - | - | - | √ | - |
| 括号替换宏展开 | `{外层{内层}}` | `{外层Ḋ内层Ḍ}` | 嵌套括号临时替换，Ḋ→{，Ḍ→} | - | - | - | √ | - |
| 命令展开宏处理 | `\newcommand{\cmd}[1]{定义#1}`<br>`\newcommand{\cmd}[2][默认]{定义#1#2}` | `定义参数值`<br>`定义默认值参数值` | 自定义命令宏展开处理 | - | - | - | √ | - |
| 命令标准化 | `\pmatrix{a}`<br>`\matrix{a}` | `\mypmatrix{a}`<br>`\mymatrix{a}` | 兼容自定义渲染 | √ | - | - | - | - |
| 对齐命令去除 | `a \raggedright b` | `a  b` | `\raggedright`、`\arraybackslash` | √ | - | - | - | - |
| 大小写命令去除 | `\lowercase{A}` | `{A}` | `\lowercase`、`\uppercase` | √ | - | - | - | - |
| begin/end合并 | `\begin {tabular}` | `\begin{tabular}` | 适用于 `tabular`、`array` 环境 | √ | - | √ | √ | - |
| 结构token合并 | `\cmidrule ( l { 3 p t } r { 3 p t } ) { 1 - 1 }` | `\cmidrule(l{3pt}r{3pt}){1-1}` | `\cmidrule`、`\cline` 及其参数 | √ | - | - | - | - |
| 特殊符号标准化 | `a -- b`<br>`a --- b`<br>`a … b`<br>`a \dots b` | `a - - b`<br>`a - - - b`<br>`a . . . b`<br>`a . . . b` | 字符映射：`--` → `- -`；`---` → `- - -`；`…` → `. . .`；`\dots` → `. . .`；`\ldots` → `. . .`；`\hdots` → `. . .`；`\cdots` → `. . .`；`\dddot` → `. . .`；`\dotsc` → `. . .`；`\dotsi` → `. . .`；`\dotsm` → `. . .`；`\dotso` → `. . .`；`\dotsb` → `. . .`；`\mathellipsis` → `. . .` | √ | - | - | - | - |
| 数学函数标准化 | `a \sin b`<br>`a \cos b`<br>`a \tanh b` | `a \mathrm { s i n } b`<br>`a \mathrm { c o s } b`<br>`a \mathrm { t a n h } b` | 字符映射：`\sin` → `\mathrm { s i n }`；`\cos` → `\mathrm { c o s }`；`\tan` → `\mathrm { t a n }`；`\tanh` → `\mathrm { t a n h }`；`\cosh` → `\mathrm { c o s h }`；`\sinh` → `\mathrm { s i n h }`；`\coth` → `\mathrm { c o t h }`；`\arcsin` → `\mathrm { a r c s i n }`；`\arccos` → `\mathrm { a r c c o s }`；`\arctan` → `\mathrm { a r c t a n }`；`\exp` → `\mathrm { e x p }`；`\log` → `\mathrm { l o g }`；`\ln` → `\mathrm { l n }`；`\lg` → `\mathrm { l g }`；`\cot` → `\mathrm { c o t }`；`\mod` → `\mathrm { m o d }`；`\bmod` → `\mathrm { m o d }`；`\pmod` → `\mathrm { m o d }`；`\min` → `\mathrm { m i n }`；`\max` → `\mathrm { m a x }`；`\ker` → `\mathrm { k e r }`；`\hom` → `\mathrm { h o m }`；`\sec` → `\mathrm { s e c }`；`\scs` → `\mathrm { s c s }`；`\csc` → `\mathrm { c s c }`；`\deg` → `\mathrm { d e g }`；`\arg` → `\mathrm { a r g }`；`\dim` → `\mathrm { d i m }`；`\ex` → `\mathrm { e x }` | √ | - | - | 不一致：`\sin` → `\sin`，`\cos` → `\cos` 等直接还原，而非转换为 `\mathrm` 形式 | - |
| token合并 | `\string a`<br>`\big (` | `\stringa`<br>`\big(` | `\string`、`\big`、`\Big`、`\bigg`、`\Bigg` 及其后括号/符号 | √ | - | 不一致：`\big` 等 → 内容（移除命令） | - | - |
| operatorname简化 | `\operatorname * {sin}` | `\operatorname {sin}` | 去除 `*` | √ | √ | √ | √ | √ |
| 特定命令去除 | `\lefteqn{A}` | `{A}` | `\lefteqn` | √ | - | - | - | - |
| 特定命令替换 | `\footnote x` | `^ x` | `\footnote` 替换为 `^` | √ | - | - | - | - |
| 特殊token合并 | `\' e` | `\'e` | 仅当后面不是 `{` | √ | - | - | - | - |
| 颜色命令去除 | `\color{red}{A}`<br>`\textcolor[rgb]{0,1,0}{A}` | `{A}` | `\color`、`\colorbox`、`\textcolor`、`\cellcolor` | √ | - | - | - | - |
| 表格layout合并 | `[ -1.5ex ]` | `[-1.5ex]` | 仅表格，`[\-.0-9 ]+[exptcm ]+` | √ | - | - | - | - |
| parbox合并 | `\parbox { 3cm }` | `\parbox{3cm}` | `\parbox` 后参数合并 | √ | - | - | - | - |
| raisebox合并 | `\raisebox { -1.5ex } [ 0pt ] {` | `\raisebox{-1.5ex}[0pt]{` | `\raisebox` 后参数合并 | √ | - | 不一致：`\raisebox{}` → 内容（移除命令） | - | - |
| char合并 | `{ \char 39 }` | `{\char39}` | `\char` 后参数合并 | √ | - | - | - | - |
| 规则类命令空格处理 | `\rule {1pt} {2pt}`<br>`\specialrule {1pt} {2pt} {2pt}` | `\rule{1pt}{2pt}`<br>`\specialrule{1pt}{2pt}{2pt}` | `\rule` 和 `\specialrule` 后参数去空格合并 | √ | √ | - | - | √ |
| **=== 后处理阶段（AST节点处理后）===** | | | | | | | | |
| operatorname反向 | `\operatorname{sin}`<br>`\operatorname{log}` | `\sin`<br>`\log` | 已知函数名从operatorname形式还原 | √ | 不一致：MathNet保持`\operatorname{}`形式不还原 | √ | √ | 不一致：保持`\operatorname{}`和`\operatorname*{}`形式不还原 |
| 特殊符号替换 | `SSSSSS`<br>`S S S S S S` | `$`<br>`$` | 连续S或空格分隔S替换为美元符号 | √ | √ | 不一致：美元符号处理：`$内容$` → `内容` | √ | √ |
| 详细标签移除 | `\ref{标签}`<br>`\cite{引用}`<br>`\eqref{标签}` | `` (移除)<br>`` (移除)<br>`` (移除) | 引用标签全面移除，包括ref、cite、eqref | - | - | - | √ | - |
| 公式清理过滤 | `\n`<br>`\notag`<br>`\nonumber`<br>`包含tikz的公式`<br>`长度<1字符的公式`<br>`以\结尾的公式`<br>`包含newcommand的公式` | `` (移除)<br>`` (移除)<br>`` (移除)<br>`` (过滤)<br>`` (过滤)<br>`` (过滤)<br>`` (过滤) | 换行符移除、标签标记移除、特殊公式类型过滤 | - | - | - | √ | - |
| 格式修复处理 | `\\ \end{array}` | `\end{array}` | 数组环境结尾格式修复 | - | - | - | √ | - |
| 短公式过滤 | `长度少于5个token的公式`<br>`包含\label的公式`<br>`解析失败的公式` | `` (移除)<br>`` (移除)<br>`` (移除) | 短公式和问题公式过滤规则 | - | - | - | √ | - |
| Token分割规则 | `\left(`<br>`\right)`<br>`,\;`<br>`\rm\bf` | `[\left, (]`<br>`[\right, )]`<br>`[,, \;]`<br>`[\rm, \bf]` | 复合token按语义分割，包含21种\left和\right组合 | √ | √ | - | - | - |
| 手动修正加载 | `错误公式` | `修正后公式` | 支持加载手动修正文件，覆盖原始公式或标记删除 | - | √ | - | - | - |
| Token替换规则 | `特定token组合` | `标准化token` | 使用token_types2.csv配置文件进行token替换和标准化 | - | √ | - | - | - |
| Token移除规则 | `指定的无用token` | `` (移除) | 根据配置文件移除标记为类型3的token | - | √ | - | - | - |
| 空括号移除 | `{ }` | `` (删除) | 移除空的大括号对 | √ | √ | - | - | - |
| 非LaTeX命令分割 | `不以\开头且包含\的token` | `按字符分割` | 非标准LaTeX命令按字符分割处理 | √ | √ | - | - | - |
| 数组环境处理 | `\begin{array}{cc} a & b \\ c & d \end{array}` | `\begin{array} { c c } a & b \\ c & d \end{array}` | 专门的数组环境解析和重构，支持列定义标准化 | - | √ | - | - | - |
| MathType检测移除 | `包含"M a t h T y p e !"的公式` | `` (完全删除) | 检测并移除MathType公式 | √ | √ | - | - | - |
| 首Token检查 | `_开头的公式`<br>`^开头的公式` | `` (删除) | 移除以上下标开头的公式 | √ | √ | - | - | - |
| 括号自动添加 | `\frac a b`<br>`x_1`<br>`y^2` | `\frac { a } { b }`<br>`x _ { 1 }`<br>`y ^ { 2 }` | 为\frac、上下标自动添加必要的大括号 | - | √ | - | - | - |
| 上下标顺序规范化 | `x^2_1`<br>`x_1^2` | `x_{1}^{2}`<br>`x_{1}^{2}` | 统一为先下标后上标的顺序，复杂的括号匹配算法 | √ | √ | - | - | - |
| 样式元素移除 | `样式相关token` | `` (移除) | 移除配置文件中标记的样式相关token | - | √ | - | - | - |
| 不必要括号移除 | `{ a }`<br>`\sin { x }` | `a`<br>`\sin x` | 复杂的括号移除逻辑，考虑前后token类型 | - | √ | - | - | - |
| 样式增强规则 | `字母/希腊字母`<br>`大写字母`<br>`大写字母和数字1` | `\boldsymbol{<字符>}`<br>`\mathcal{<字母>}`<br>`\mathbb{<字符>}` | 可选的样式增强：粗体、花体、黑板粗体 | √ | - | - | - | - |
| ASCII检查过滤 | `非ASCII字符` | `` (过滤) | 检查并过滤非ASCII字符的token | √ | - | - | - | - |
| 公式质量过滤 | `包含特定token的公式` | `` (过滤) | 移除包含配置文件中标记为类型6的token的完整公式 | - | √ | - | - | - |
| 图像尺寸过滤 | `过大尺寸图像` | `` (过滤) | 过滤过大尺寸的图像（默认最大宽度500px，高度160px） | - | √ | - | - | - |
| 令牌数量过滤 | `令牌数量过多的公式` | `` (过滤) | 过滤令牌数量过多的公式（默认最大150个令牌） | - | √ | - | - | - |
| 空公式过滤 | `空白或无内容公式` | `` (过滤) | 过滤长度为0或解析后为空的公式 | - | √ | - | - | - |
| 解析失败过滤 | `无法解析的公式` | `` (移除) | 移除KaTeX无法解析的公式，输出错误信息 | - | √ | - | - | - |
| 基本符号过滤 | `不包含\、_、^、(、)、{、}的公式` | `` (过滤) | 必须包含基本LaTeX符号的公式才保留 | - | - | - | √ | - |
| 空格清理 | `多余空白字符` | `标准化空格` | 移除不必要空白，保护特定命令内空格 | - | - | - | √ | - |
| TikZ图形过滤 | `包含tikz的公式` | `` (过滤) | 拒绝包含'tikz'的公式 | - | - | - | √ | - |
| 格式规范化 | `LaTeX代码` | `格式化代码` | LaTeX代码格式化：缩进、换行、空格标准化 | - | - | √ | - | - |
| 忽略块处理 | `% tex-fmt: skip等指令` | `保持原样` | 支持`% tex-fmt: skip`、`% tex-fmt: off`、`% tex-fmt: on`指令 | - | - | √ | - | - |
| 逐字环境处理 | `verbatim等环境` | `保护内容` | 保护verbatim、Verbatim、lstlisting、minted、comment环境 | - | - | √ | - | - |
| 行分割处理 | `特定命令` | `自动换行` | 在特定命令前自动换行：`\begin`, `\end`, `\item`, `\section`等 | - | - | √ | - | - |
| 行包装处理 | `长行内容` | `自动换行` | 支持行长度限制和自动换行（默认80字符） | - | - | √ | - | - |
| 缩进管理 | `代码结构` | `自动缩进` | 自动缩进管理，支持制表符和空格缩进 | - | - | √ | - | - |
| 注释处理 | `注释内容` | `格式化注释` | 注释格式标准化，支持行内和行间注释 | - | - | √ | - | - |
| KaTeX兼容性 | `不支持的LaTeX命令` | `KaTeX兼容格式` | 移除不支持的LaTeX命令，转换为KaTeX兼容格式 | - | - | √ | - | - |
| 样式命令处理 | `\mathbf{A}`<br>`\emph{text}` | `\bm{A}`<br>`\textit{text}` | `\mathbf` → `\bm`，`\emph` → `\textit`，移除`\bm`等 | - | - | √ | - | - |
| 大小命令处理 | `\Huge{A}`<br>`\footnotesize{B}` | `\Huge{A}`<br>`\footnotesize{B}` | 保留字体大小命令：`\Huge`, `\huge`, `\LARGE`等 | - | - | √ | - | - |
| 分隔符命令处理 | `\left(`<br>`\big[` | `(`<br>`[` | 移除`\left`, `\right`, `\big`, `\Big`等分隔符命令 | - | - | √ | - | - |
| 空间命令处理 | `\,`<br>`\!` | `简化形式` | 简化`\,`, `\!`, `\;`, `\:`等空间命令 | - | - | √ | - | - |
| 文本命令合并 | `\text{a}\text{b}` | `\text{ab}` | 合并连续的`\text{}`命令 | - | - | √ | - | - |
| 美元符号处理 | `$公式$` | `公式` | 移除公式周围的美元符号 | - | - | √ | - | - |
| 多余空格清理 | `多个连续空格` | `单个空格` | 压缩多个连续空格为单个空格 | - | - | √ | - | - |
| 公式提取 | `\begin{equation}...\end{equation}` | `公式内容` | 仅提取`\begin{equation}...\end{equation}`环境中的公式 | - | - | √ | - | √ |
| 长度过滤 | `公式长度` | `过滤结果` | 过滤长度在40-1024字节之间的公式 | - | - | √ | - | √ |
| 内容过滤 | `包含特定关键词的公式` | `` (移除) | 移除包含特定关键词的公式：`%`, `\label`, `\cite`, `\ref`, `\pageref`, `\footnote`, `\ope`, `\Bar`, `\D`, `\wt`, `\ww`等 | - | - | √ | - | √ |
| 短公式过滤 | `令牌数量少于3个的公式` | `` (移除) | 移除令牌数量少于3个的公式 | - | - | √ | - | √ |
| 重复公式移除 | `重复的公式` | `` (移除) | 移除重复的公式 | - | - | √ | - | - |
| 编码处理 | `UTF-8编码问题` | `修复编码` | 处理UTF-8编码问题，忽略编码错误 | - | - | √ | - | - |
| 换行符清理 | `\n`<br>`\r` | `` (移除) | 移除公式中的换行符`\n`和回车符`\r` | - | - | √ | - | √ |
| 空白清理 | `公式首尾空白` | `` (移除) | 移除公式首尾的空白字符 | - | - | √ | - | √ |
