# UniMERNet LaTeX规范化流程详解

## 概述

UniMERNet项目采用多阶段的LaTeX处理管道，结合Python和JavaScript脚本，使用KaTeX库进行核心的AST解析和渲染。整个流程将功能相同但写法多样的LaTeX代码统一成标准的、唯一的表示形式。

## 完整数据流程和调用关系

```
原始LaTeX字符串
    ↓
[应用层] latex2bbox_color.py 调用
    ↓
[Python] tokenize_latex() - 环境统一预处理
    ↓
[JavaScript] preprocess_formula_initial.js - AST解析和规范化
    ↓
[Python] normalize_latex() - 后处理和函数展开
    ↓
最终规范化LaTeX字符串
```

**重要说明**: `normalize_latex()`函数是在`tokenize_latex()`完成JavaScript处理后，由应用层代码（如`latex2bbox_color.py`）单独调用的，不是在`tokenize_latex()`内部调用的。

## 详细处理步骤

### 阶段1: Python预处理 (tokenize_latex.py)

**输入示例**: `\begin{align} \sin(x) + \cos(y) = \frac{1}{2} \end{align}`

**处理步骤**:
1. **输入验证**: 检查LaTeX代码是否为空
2. **类型检测**: 自动判断是公式(formula)还是表格(tabular)
3. **环境统一**:
   - `split`, `align`, `alignedat`, `alignat`, `eqnarray` → `aligned`
   - `smallmatrix` → `matrix`
4. **调用JavaScript处理器**: 通过shell命令调用Node.js脚本进行AST处理
5. **操作符规范化**: 将`\operatorname{sin}`转换为`\sin`
6. **结果验证**: 检查JavaScript处理是否成功

**输出示例**: `\begin{aligned} \sin(x) + \cos(y) = \frac{1}{2} \end{aligned}`

### 阶段2: JavaScript AST处理 (preprocess_formula_initial.js)

**核心功能**: 使用KaTeX将LaTeX解析为AST，然后重新渲染为标准化LaTeX

#### 2.1 预处理阶段
- 移除注释 (`%` 开头的内容)
- 清理特殊字符 (`\~`, `\>`, `$`)
- 移除标签 (`\label{...}`)
- 统一`\rm`格式为`\mathrm`

#### 2.2 AST解析阶段
使用`katex.__parse()`将LaTeX字符串解析为抽象语法树

#### 2.3 AST渲染阶段 (核心规范化)
通过`groupTypes`对象处理不同类型的AST节点:

**mathord节点** (数学字符):
```javascript
// 输入: 字符'x'
// 输出: "x "
// mathrm字体: 每个字符单独处理并添加空格
```

**supsub节点** (上下标):
```javascript
// 输入: x^2_1
// 输出: "x ^ { 2 } _ { 1 }"
// 自动为非组表达式添加括号
```

**genfrac节点** (分数):
```javascript
// 输入: 分数AST节点
// 输出: "\frac { 1 } { 2 }" 或 "\binom { n } { k }"
// 根据hasBarLine判断是分数还是二项式
```

**array节点** (矩阵/数组):
```javascript
// 输入: 矩阵AST节点
// 输出: "\begin{array} { l l } a & b \\ c & d \end{array}"
// 处理列对齐、行分隔符、空单元格清理
```

**sqrt节点** (根号):
```javascript
// 输入: 根号AST节点
// 输出: "\sqrt { expr }" 或 "\sqrt [ n ] { expr }"
```

**leftright节点** (定界符):
```javascript
// 输入: 定界符AST节点
// 输出: "\left ( expr \right )"
```

**accent节点** (重音符号):
```javascript
// 输入: 重音AST节点
// 输出: "\hat { x }" 或 "\tilde { expr }"
```

**op节点** (操作符):
```javascript
// 输入: 操作符AST节点
// 输出: "∑" 或 "\operatorname { name }"
```

**输出示例**: `\begin{aligned} \mathrm { s i n } ( x ) + \mathrm { c o s } ( y ) = \frac { 1 } { 2 } \end{aligned}`

### 阶段3: Python后处理 (normalize_latex.py)

**核心功能**: 进一步的规范化和函数名展开

#### 3.1 基础清理
- 移除对齐命令: `\raggedright`, `\arraybackslash`
- 移除大小写命令: `\lowercase`, `\uppercase`
- 矩阵命令替换: `\pmatrix` → `\mypmatrix`

#### 3.2 符号展开
**省略号统一**:
```python
# 输入: \ldots, \cdots, \dots
# 输出: . . .
```

**函数名展开**:
```python
# 输入: \sin
# 输出: \mathrm { s i n }

# 输入: \cos
# 输出: \mathrm { c o s }
```

#### 3.3 Token合并和括号处理
- **Token合并**: `\big (` → `\big(`, `\string abc` → `\stringabc`
- **括号补全**: 为单参数命令自动添加缺失的括号
- **特殊命令处理**: `\operatorname*` → `\operatorname`, 移除`\lefteqn`
- **重音符号合并**: `\' e` → `\'e`
- **布局命令合并**: `\rule { 1pt } { 2pt }` → `\rule{1pt}{2pt}`
- **颜色命令移除**: 清理原有颜色命令为后续颜色标注做准备

**最终输出示例**: 
```latex
\begin{aligned} \mathrm { s i n } ( x ) + \mathrm { c o s } ( y ) = \frac { 1 } { 2 } \end{aligned}
```

## 关键规范化规则总结

### 1. 结构统一
- **环境标准化**: 所有对齐环境 → `aligned`
- **矩阵标准化**: 所有矩阵环境 → 统一格式
- **分组标准化**: 统一使用`{...}`进行分组

### 2. 函数展开
- **三角函数**: `\sin` → `\mathrm { s i n }`
- **对数函数**: `\log` → `\mathrm { l o g }`
- **省略号**: `\ldots` → `. . .`

### 3. 空格规范化
- 所有tokens之间用单个空格分隔
- 特殊结构内部保持适当间距
- `\mathrm`字体中的字符单独分隔

### 4. AST节点处理
- **上下标**: 自动添加必要的括号
- **分数**: 统一为`\frac`或`\binom`格式
- **数组**: 标准化列对齐和行分隔符

## 数据格式变化示例

**原始输入**:
```latex
\begin{split}
\sin x + \cos y &= \frac12 \\
\log z &= \ldots
\end{split}
```

**阶段1后**:
```latex
\begin{aligned}
\sin x + \cos y &= \frac12 \\
\log z &= \ldots
\end{aligned}
```

**阶段2后**:
```latex
\begin{aligned} \sin ( x ) + \cos ( y ) & = \frac { 1 } { 2 } \\ \log ( z ) & = \ldots \end{aligned}
```

**最终输出**:
```latex
\begin{aligned} \mathrm { s i n } ( x ) + \mathrm { c o s } ( y ) & = \frac { 1 } { 2 } \\ \mathrm { l o g } ( z ) & = . . . \end{aligned}
```

## 迁移建议

1. **模块化使用**: 可以选择性使用某些处理阶段
2. **规则扩展**: 基于现有正则表达式规则库进行扩展
3. **AST定制**: 修改`groupTypes`对象来定制AST处理逻辑
4. **错误处理**: 添加更完善的错误恢复机制

这套规范化系统的核心优势在于其系统性和完整性，通过AST解析确保了结构层面的一致性，通过大量正则规则确保了细节层面的标准化。
