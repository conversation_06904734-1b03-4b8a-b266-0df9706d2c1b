const path = require('path');
var katex = require(path.join(__dirname,"third_party/katex/katex.js"))
options = require(path.join(__dirname,"third_party/katex/src/Options.js"))
var readline = require('readline');
var rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: false
});

rl.on('line', function(line){
    a = line

    // 注释处理
    if (line[0] == "%") {
        line = line.substr(1, line.length - 1);
    }

    line = line.replace(/\\%/g, '__ESCAPED_PERCENT__');
    line = line.split('%')[0];
    line = line.replace(/__ESCAPED_PERCENT__/g, '\\%');

    // 特殊字符预处理
    line = line.split('\\~').join(' ');

    // 换行符处理
    if (line.indexOf("matrix") == -1 && line.indexOf("cases")==-1 &&
        line.indexOf("array")==-1 && line.indexOf("align")==-1 &&
        line.indexOf("begin")==-1)  {
        for (var i = 0; i < 300; i++) {
            line = line.replace(/\\\\/, "\\,");
        }
    }

    line = line + " "

    try {
        if (process.argv[2] == "tokenize") {
            var tree = katex.__parse(line, {});
            console.log(global_str.replace(/\\label { .*? }/, ""));
        } else {
            // \rm命令统一化
            for (var i = 0; i < 300; ++i) {
                line = line.replace(/{\\rm/, "\\mathrm{");
                line = line.replace(/{ \\rm/, "\\mathrm{");
                line = line.replace(/\\rm{/, "\\mathrm{");
            }

            var tree = katex.__parse(line, {});
            buildExpression(tree, new options({}));

            // 恢复$符号
            for (var i = 0; i < 300; ++i) {
                norm_str = norm_str.replace('SSSSSS', '$');
                norm_str = norm_str.replace(' S S S S S S', '$');
            }

            console.log(norm_str.replace(/\\label { .*? }/, ""));
        }
    } catch (e) {
        console.error(line);
        console.error(norm_str);
        console.error(e);
        console.log();
    }

    global_str = ""
    norm_str = ""
})

norm_str = ""
var groupTypes = {};

// 数学普通字符处理
groupTypes.mathord = function(group, options) {
    if (options.font == "mathrm"){
        for (i = 0; i < group.value.length; ++i ) {
            if (group.value[i] == " ") {
                norm_str = norm_str + group.value[i] + "\; ";
            } else {
                norm_str = norm_str + group.value[i] + " ";
            }
        }
    } else {
        norm_str = norm_str + group.value + " ";
    }
};

// 文本字符处理 - 简化版本，移除text模式特殊处理
groupTypes.textord = function(group, options) {
    norm_str = norm_str + group.value + " ";
};

groupTypes.bin = function(group) {
    norm_str = norm_str + group.value + " ";
};

groupTypes.rel = function(group) {
    norm_str = norm_str + group.value + " ";
};

groupTypes.open = function(group) {
    norm_str = norm_str + group.value + " ";
};

groupTypes.close = function(group) {
    norm_str = norm_str + group.value + " ";
};

groupTypes.inner = function(group) {
    norm_str = norm_str + group.value + " ";
};

groupTypes.punct = function(group) {
    norm_str = norm_str + group.value + " ";
};

// 有序组处理 - 简化版本
groupTypes.ordgroup = function(group, options) {
    buildExpression(group.value, options);
};

// 文本模式处理 - 简化版本，统一处理
groupTypes.text = function(group, options) {
    norm_str = norm_str + "\\text{";
    buildExpression(group.value.body, options);
    norm_str = norm_str + "}";
};

groupTypes.color = function(group, options) {
    var inner = buildExpression(group.value.value, options);
    var node = new mathMLTree.MathNode("mstyle", inner);
    node.setAttribute("mathcolor", group.value.color);
    return node;
};

// 上下标处理
groupTypes.supsub = function(group, options) {
    buildGroup(group.value.base, options);

    if (group.value.sub) {
        norm_str = norm_str + "_ ";
        if (group.value.sub.type != 'ordgroup') {
            norm_str = norm_str + " { ";
            buildGroup(group.value.sub, options);
            norm_str = norm_str + "} ";
        } else {
            buildGroup(group.value.sub, options);
        }
    }

    if (group.value.sup) {
        norm_str = norm_str + "^ ";
        if (group.value.sup.type != 'ordgroup') {
            var isSingleCommand = false;
            if (group.value.sup.type === 'atom' && group.value.sup.text) {
                isSingleCommand = true;
            } else if (group.value.sup.type === 'textord' && group.value.sup.text) {
                isSingleCommand = true;
            }
            
            if (isSingleCommand) {
                buildGroup(group.value.sup, options);
            } else {
                norm_str = norm_str + " { ";
                buildGroup(group.value.sup, options);
                norm_str = norm_str + "} ";
            }
        } else {
            buildGroup(group.value.sup, options);
        }
    }
};

// 广义分数处理
groupTypes.genfrac = function(group, options) {
    if (!group.value.hasBarLine) {
        norm_str = norm_str + "\\binom ";
    } else {
        norm_str = norm_str + "\\frac ";
    }

    norm_str = norm_str + "{ ";
    buildGroup(group.value.numer, options);
    norm_str = norm_str + "} ";
    
    norm_str = norm_str + "{ ";
    buildGroup(group.value.denom, options);
    norm_str = norm_str + "} ";
};

// 数组/矩阵处理
groupTypes.array = function(group, options) {
    var envName = "array";

    if (group.value && group.value.envName) {
        envName = group.value.envName;
    } else {
        var currentInput = a || "";
        if (currentInput.indexOf("\\begin{cases}") !== -1) {
            envName = "cases";
        } else if (currentInput.indexOf("\\begin{aligned}") !== -1) {
            envName = "aligned";
        } else if (currentInput.indexOf("\\begin{align") !== -1) {
            envName = "aligned";
        } else if (currentInput.indexOf("\\begin{matrix}") !== -1) {
            envName = "matrix";
        } else if (currentInput.indexOf("\\begin{pmatrix}") !== -1) {
            envName = "pmatrix";
        } else if (currentInput.indexOf("\\begin{bmatrix}") !== -1) {
            envName = "bmatrix";
        } else if (currentInput.indexOf("\\begin{vmatrix}") !== -1) {
            envName = "vmatrix";
        } else if (currentInput.indexOf("\\begin{Vmatrix}") !== -1) {
            envName = "Vmatrix";
        }
    }

    norm_str = norm_str + "\\begin{" + envName + "}";

    if (envName === "array") {
        norm_str = norm_str + " { ";
        if (group.value.cols) {
            group.value.cols.map(function(start) {
                if (start && start.align) {
                    norm_str = norm_str + start.align + " ";
                }
            });
        } else {
            group.value.body[0].map(function(start) {
                norm_str = norm_str + "l ";
            });
        }
        norm_str = norm_str + "} ";
    } else {
        norm_str = norm_str + " ";
    }

    var totalRows = group.value.body.length;
    var isAlignEnvironment = (envName === "align" || envName === "aligned");

    group.value.body.map(function(row, rowIndex) {
        if (row.some(cell => cell.value.length > 0)) {
            out = row.map(function(cell) {
                if (isAlignEnvironment) {
                    var isSimpleCell = cell.value && cell.value.length === 1;
                    var isComplexCell = false;
                    
                    if (isSimpleCell) {
                        var element = cell.value[0];
                        isComplexCell = (element.type === 'genfrac' || element.type === 'sqrt' || 
                                       element.type === 'supsub' || element.type === 'accent' ||
                                       element.type === 'ordgroup');
                    }
                    
                    if (isSimpleCell && !isComplexCell) {
                        buildExpression(cell.value, options);
                    } else {
                        buildGroup(cell, options);
                    }
                } else {
                    buildGroup(cell, options);
                }

                if (norm_str.length > 4
                    && norm_str.substring(norm_str.length-4, norm_str.length) == "{ } ") {
                    norm_str = norm_str.substring(0, norm_str.length-4);
                }
                norm_str = norm_str + "& ";
            });

            norm_str = norm_str.substring(0, norm_str.length-2);

            var shouldAddRowSeparator = false;
            if (envName === "array") {
                shouldAddRowSeparator = (rowIndex < totalRows - 1);
            } else {
                shouldAddRowSeparator = (totalRows > 1 && rowIndex < totalRows - 1);
            }

            if (shouldAddRowSeparator) {
                norm_str = norm_str + "\\\\ ";
            } else {
                norm_str = norm_str + " ";
            }
        }
    });

    norm_str = norm_str + "\\end{" + envName + "} ";
};

// 根号处理
groupTypes.sqrt = function(group, options) {
    if (group.value.index) {
        norm_str = norm_str + "\\sqrt [ ";
        buildExpression(group.value.index.value, options);
        norm_str = norm_str + "] ";
        buildGroup(group.value.body, options);
    } else {
        norm_str = norm_str + "\\sqrt ";
        buildGroup(group.value.body, options);
    }
};

// 左右定界符处理
groupTypes.leftright = function(group, options) {
    norm_str = norm_str + "\\left" + group.value.left + " ";
    buildExpression(group.value.body, options);
    norm_str = norm_str + "\\right" + group.value.right + " ";
};

// 重音符号处理
groupTypes.accent = function(group, options) {
    if (group.value.base.type != 'ordgroup') {
        norm_str = norm_str + group.value.accent + " { ";
        buildGroup(group.value.base, options);
        norm_str = norm_str + "} ";
    } else {
        norm_str = norm_str + group.value.accent + " ";
        buildGroup(group.value.base, options);
    }
};

// 间距处理 - 简化版本，移除text模式特殊处理
groupTypes.spacing = function(group, options) {
    if (group.value == " ") {
        norm_str = norm_str + "~ ";
    } else {
        norm_str = norm_str + group.value + " ";
    }
};

// 操作符处理
groupTypes.op = function(group) {
    if (group.value.symbol) {
        norm_str = norm_str + group.value.body;

        if (group.value.alwaysHandleSupSub === true) {
            if (group.value.limits === true) {
                norm_str = norm_str + "\\limits ";
            } else if (group.value.limits === false) {
                norm_str = norm_str + "\\nolimits ";
            } else {
                norm_str = norm_str + " ";
            }
        } else {
            norm_str = norm_str + " ";
        }
    } else {
        var opName = group.value.body;
        var cleanOpName = opName.replace(/^\\/, '');
        
        var mathFunctions = ['sin', 'cos', 'tan', 'cot', 'sec', 'csc', 'sinh', 'cosh', 'tanh', 'coth',
                           'arcsin', 'arccos', 'arctan', 'ln', 'lg', 'log', 'exp', 'min', 'max', 'mod',
                           'deg', 'arg', 'dim', 'ker', 'hom', 'lim', 'sup', 'inf', 'det', 'gcd', 'Pr'];
        
        if (mathFunctions.indexOf(cleanOpName) !== -1) {
            norm_str = norm_str + "\\" + cleanOpName + " ";
        } else {
            if (group.value.limits == false) {
                norm_str = norm_str + "\\operatorname { ";
            } else {
                norm_str = norm_str + "\\operatorname* { ";
            }

            for (i = 1; i < group.value.body.length; ++i ) {
                norm_str = norm_str + group.value.body[i] + " ";
            }
            norm_str = norm_str + "} ";
        }
    }
};

groupTypes.katex = function(group) {
    var node = new mathMLTree.MathNode(
        "mtext", [new mathMLTree.TextNode("KaTeX")]);
    return node;
};

// 字体处理
groupTypes.font = function(group, options) {
    var font = group.value.font;

    if (font == "mbox" || font == "hbox") {
        font = "text";
    }

    norm_str = norm_str + "\\" + font + " ";
    buildGroup(group.value.body, options.withFont(font));
};

groupTypes.delimsizing = function(group) {
    norm_str = norm_str + group.value.funcName + " " + group.value.value + " ";
};

groupTypes.styling = function(group, options) {
    norm_str = norm_str + " " + group.value.original + " ";
    buildExpression(group.value.value, options);
};

groupTypes.sizing = function(group, options) {
    if (group.value.original == "\\rm") {
        norm_str = norm_str + "\\mathrm { ";
        buildExpression(group.value.value, options.withFont("mathrm"));
        norm_str = norm_str + "} ";
    } else {
        norm_str = norm_str + " " + group.value.original + " ";
        buildExpression(group.value.value, options);
    }
};

groupTypes.overline = function(group, options) {
    norm_str = norm_str + "\\overline { ";
    buildGroup(group.value.body, options);
    norm_str = norm_str + "} ";
};

groupTypes.underline = function(group, options) {
    norm_str = norm_str + "\\underline { ";
    buildGroup(group.value.body, options);
    norm_str = norm_str + "} ";
};

groupTypes.rule = function(group) {
    norm_str = norm_str + "\\rule { " + group.value.width.number + " " + group.value.width.unit + " } { " + group.value.height.number + " " + group.value.height.unit + " } ";
};

groupTypes.llap = function(group, options) {
    norm_str = norm_str + "\\llap ";
    buildGroup(group.value.body, options);
};

groupTypes.rlap = function(group, options) {
    norm_str = norm_str + "\\rlap ";
    buildGroup(group.value.body, options);
};

groupTypes.phantom = function(group, options, prev) {
    norm_str = norm_str + "\\phantom { ";
    buildExpression(group.value.value, options);
    norm_str = norm_str + "} ";
};

var buildExpression = function(expression, options) {
    for (var i = 0; i < expression.length; i++) {
        var group = expression[i];
        buildGroup(group, options);
    }
};

var buildGroup = function(group, options) {
    if (groupTypes[group.type]) {
        groupTypes[group.type](group, options);
    } else {
        throw new ParseError(
            "Got group of unknown type: '" + group.type + "'");
    }
}; 