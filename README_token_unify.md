# LaTeX Token同义词替换功能

## 功能说明

在LaTeX规范化流程中新增了同义词token替换功能，用于将多个同义词token统一替换为标准token，方便管理和维护。

## 处理流程

### 原流程
1. 输入LaTeX字符串
2. JavaScript脚本处理（tokenize）
3. normalize函数处理
4. 输出规范化结果

### 新流程
1. 输入LaTeX字符串
2. JavaScript脚本处理（tokenize）
3. **同义词token替换**
4. normalize函数处理
5. 输出规范化结果

## 配置文件

### 文件位置
`token_unify.csv` - 与 `normalize.py` 在同一目录下

### 文件格式
CSV格式，两列：
- 第一列：同义词token
- 第二列：标准token

### 示例内容
```csv
\Alpha,\alpha
\Beta,\beta
\Gamma,\gamma
\Pi,\pi
\Sigma,\sigma
\Psi,\psi
\varepsilon,\epsilon
\vartheta,\theta
\operatorname{sin},\mathrm{sin}
\operatorname{cos},\mathrm{cos}
```

## 使用方法

### 1. 配置处理选项
在 `normalize.py` 的配置区设置：
```python
CONFIG = {
    'enable_synonym_replacement': True,  # 启用同义词替换功能
    'unify_environments': False,         # 是否统一对齐环境为aligned/matrix
    # 其他配置...
}
```

### 2. 配置同义词映射
编辑 `token_unify.csv` 文件，添加需要替换的同义词映射规则。

### 3. 运行脚本
```bash
python normalize.py
```

### 4. 模式选择
- `tokenize` 模式：tokenize → 同义词替换 → 输出
- `normalize` 模式：tokenize → 同义词替换 → normalize → 输出

## 特性

1. **可配置开关**：
   - `enable_synonym_replacement`：控制是否启用同义词替换
   - `unify_environments`：控制是否统一对齐环境（align* → aligned → array）
2. **自动跳过**：如果配置文件不存在，自动跳过替换步骤继续处理
3. **错误容忍**：如果配置文件格式错误，跳过错误行继续处理
4. **智能替换**：按同义词长度降序排序，避免短token被误匹配
5. **环境保持**：默认保持原始LaTeX环境不变
6. **详细日志**：显示替换统计信息和处理结果

## 注意事项

1. **唯一性**：不允许一个token出现在多个映射规则中
2. **完整性**：确保CSV文件中的token格式正确
3. **顺序性**：长token会优先匹配，避免冲突
4. **兼容性**：不修改现有的normalize函数，保持向后兼容

## 示例

### 输入
```latex
\Pi ^ { \Nu } ( \Sigma , 0 ) \Psi [ z ] = i \Alpha { \bf D } _ { \Nu } ( \Sigma ) \Psi [ z ]
```

### 同义词替换后
```latex
\pi ^ { \nu } ( \sigma , 0 ) \psi [ z ] = i \alpha { \bf D } _ { \nu } ( \sigma ) \psi [ z ]
```

### 最终输出（normalize模式）
```latex
\pi^{ \nu}( \sigma,0) \psi[z]=i \alpha{\bf D}_{ \nu}( \sigma) \psi[z]
```

## 扩展

您可以根据需要在 `token_unify.csv` 中添加更多的同义词映射规则，支持：
- 希腊字母大小写统一
- 变体符号标准化
- 函数名格式统一
- 其他自定义token替换
