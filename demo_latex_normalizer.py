#!/usr/bin/env python3
"""
LaTeX规范化器演示脚本

展示基于AST的模块化LaTeX规范化系统的功能和效果。
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from latex_normalizer import LaTeXNormalizer


def demo_fr1_spacing_normalization():
    """演示FR1：空格规范化"""
    print("=" * 80)
    print("FR1 演示：空格规范化")
    print("=" * 80)
    print("移除对渲染无影响的冗余空格，保留有实际渲染意义的空格命令")
    print()
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        ("基本空格移除", "\\frac {a} {b}", "移除花括号前后的空格"),
        ("运算符空格", "a + b", "移除运算符周围的空格"),
        ("括号空格", "( x )", "移除括号内的空格"),
        ("保留间距命令", "a \\quad b", "保留\\quad间距命令"),
        ("保留负间距", "a \\! b", "保留\\!负间距命令"),
        ("复杂表达式", "\\sin ( x + y ) \\, dx", "综合空格处理"),
    ]
    
    for desc, input_latex, explanation in test_cases:
        result = normalizer.spacing_normalizer.normalize(input_latex)
        print(f"{desc}:")
        print(f"  输入: {input_latex}")
        print(f"  输出: {result}")
        print(f"  说明: {explanation}")
        print()


def demo_fr2_structure_normalization():
    """演示FR2：结构规范化"""
    print("=" * 80)
    print("FR2 演示：结构规范化")
    print("=" * 80)
    print("将功能类似的LaTeX环境统一为单一标准形式")
    print()
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        ("对齐环境统一", "\\begin{align} x = y \\end{align}", "align → aligned"),
        ("分割环境统一", "\\begin{split} a = b \\end{split}", "split → aligned"),
        ("带星号环境", "\\begin{align*} x = y \\end{align*}", "align* → aligned"),
        ("小矩阵统一", "\\begin{smallmatrix} 1 & 2 \\end{smallmatrix}", "smallmatrix → matrix"),
        ("保留其他矩阵", "\\begin{pmatrix} 1 & 2 \\end{pmatrix}", "pmatrix保持不变"),
    ]
    
    for desc, input_latex, explanation in test_cases:
        result = normalizer.structure_normalizer.normalize(input_latex)
        print(f"{desc}:")
        print(f"  输入: {input_latex}")
        print(f"  输出: {result}")
        print(f"  说明: {explanation}")
        print()


def demo_fr3_token_normalization():
    """演示FR3：Token规范化"""
    print("=" * 80)
    print("FR3 演示：Token规范化")
    print("=" * 80)
    print("统一渲染效果一致但源码写法不同的Token")
    print()
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        ("函数名简化", "\\operatorname{sin} x", "选择token数量最少的版本"),
        ("mathrm简化", "\\mathrm{cos} y", "统一为标准函数命令"),
        ("同义符号替换", "\\gets", "基于配置文件的同义词替换"),
        ("复合替换", "\\operatorname{tan} \\gets \\operatorname{log}", "多种规范化组合"),
    ]
    
    for desc, input_latex, explanation in test_cases:
        result = normalizer.token_normalizer.normalize(input_latex)
        print(f"{desc}:")
        print(f"  输入: {input_latex}")
        print(f"  输出: {result}")
        print(f"  说明: {explanation}")
        print()


def demo_complete_normalization():
    """演示完整规范化流程"""
    print("=" * 80)
    print("完整规范化流程演示")
    print("=" * 80)
    print("AST处理 → 结构规范化 → Token规范化 → 空格规范化")
    print()
    
    normalizer = LaTeXNormalizer()
    
    complex_examples = [
        "\\operatorname{sin} \\frac {a} {b} + \\begin{align} x = y \\end{align}",
        "\\mathrm{cos} ( x + y ) \\gets \\begin{split} a = b \\\\ c = d \\end{split}",
        "\\begin{smallmatrix} 1 & 2 \\\\ 3 & 4 \\end{smallmatrix} \\quad \\operatorname{det} A",
        "\\frac { \\operatorname{sin} x } { \\operatorname{cos} y } \\neq \\tan z"
    ]
    
    for i, input_latex in enumerate(complex_examples, 1):
        print(f"示例 {i}:")
        print(f"  原始: {input_latex}")
        
        # 显示各阶段处理结果
        ast_result = input_latex
        if normalizer.ast_processor.is_available():
            success, ast_result = normalizer.ast_processor.process_latex(input_latex)
            if success:
                print(f"  AST处理: {ast_result}")
        
        structure_result = normalizer.structure_normalizer.normalize(ast_result)
        print(f"  结构规范化: {structure_result}")
        
        token_result = normalizer.token_normalizer.normalize(structure_result)
        print(f"  Token规范化: {token_result}")
        
        final_result = normalizer.spacing_normalizer.normalize(token_result)
        print(f"  最终结果: {final_result}")
        
        # 分析改进效果
        analysis = normalizer.analyze(input_latex)
        print(f"  长度变化: {analysis['normalization_preview']['length_change']}")
        print(f"  复杂度降低: {analysis['token_stats'].get('simplification_potential', 0)} 个token可简化")
        print()


def demo_configuration_system():
    """演示配置系统"""
    print("=" * 80)
    print("配置系统演示")
    print("=" * 80)
    print("外部CSV配置文件管理同义词替换规则")
    print()
    
    normalizer = LaTeXNormalizer()
    
    # 显示配置信息
    config_info = normalizer.config_manager.get_config_info()
    print(f"配置文件路径: {config_info['config_file']}")
    print(f"同义词数量: {config_info['synonym_count']}")
    print()
    
    # 显示部分同义词映射
    print("同义词映射示例:")
    synonym_map = normalizer.config_manager.get_synonym_map()
    for i, (source, target) in enumerate(list(synonym_map.items())[:5]):
        print(f"  {source} → {target}")
    print(f"  ... 共 {len(synonym_map)} 个映射")
    print()
    
    # 演示同义词替换
    test_text = "\\operatorname{sin} x + \\gets y \\neq z"
    result, count = normalizer.config_manager.replace_tokens_in_text(test_text)
    print(f"同义词替换演示:")
    print(f"  输入: {test_text}")
    print(f"  输出: {result}")
    print(f"  替换次数: {count}")


def demo_system_analysis():
    """演示系统分析功能"""
    print("=" * 80)
    print("系统分析功能演示")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    # 系统验证
    is_valid, errors = normalizer.validate_system()
    print(f"系统状态: {'正常' if is_valid else '有警告'}")
    if errors:
        print("警告信息:")
        for error in errors:
            print(f"  - {error}")
    print()
    
    # 系统信息
    info = normalizer.get_system_info()
    print("系统组件状态:")
    for component, status in info['components'].items():
        print(f"  {component}: {'✓' if status else '✗'}")
    print()
    
    # 分析示例
    test_latex = "\\operatorname{sin} \\frac {a} {b} + \\begin{align} x = y \\end{align}"
    analysis = normalizer.analyze(test_latex)
    
    print("LaTeX分析示例:")
    print(f"  输入长度: {analysis['input_length']} 字符")
    print(f"  空格字符: {analysis['spacing_stats']['space_chars']} 个")
    print(f"  环境数量: {analysis['structure_stats']['total_environments']} 个")
    print(f"  函数token: {analysis['token_stats']['total_function_tokens']} 个")
    print(f"  可简化token: {analysis['token_stats']['simplification_potential']} 个")


def main():
    """主演示函数"""
    print("LaTeX规范化器功能演示")
    print("基于AST的模块化LaTeX规范化系统")
    print("实现FR1(空格规范化)、FR2(结构规范化)、FR3(Token规范化)")
    print()
    
    try:
        demo_system_analysis()
        demo_fr1_spacing_normalization()
        demo_fr2_structure_normalization()
        demo_fr3_token_normalization()
        demo_configuration_system()
        demo_complete_normalization()
        
        print("=" * 80)
        print("演示完成")
        print("=" * 80)
        print("系统特点:")
        print("✓ 基于AST处理，确保语法正确性")
        print("✓ 模块化设计，易于扩展和维护")
        print("✓ 外部配置文件，灵活管理同义词规则")
        print("✓ 完善的验证和分析功能")
        print("✓ 遵循fail-fast原则，错误处理完善")
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
