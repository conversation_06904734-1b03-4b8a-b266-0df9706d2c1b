#!/usr/bin/env python3
"""
测试希腊字母空格修复效果
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from latex_normalizer import LaTeXNormalizer


def test_greek_letter_spacing():
    """测试希腊字母空格修复"""
    print("=" * 80)
    print("希腊字母空格修复测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    test_cases = [
        # 希腊字母基础测试
        ("alpha基础", "\\alpha g"),
        ("beta基础", "\\beta x"),
        ("gamma基础", "\\gamma y"),
        ("delta基础", "\\delta z"),
        ("epsilon基础", "\\epsilon t"),
        ("nu基础", "\\nu k"),
        
        # 多个希腊字母
        ("多个希腊字母", "\\alpha g \\nu"),
        ("复杂希腊字母", "\\alpha \\beta \\gamma"),
        
        # 分数中的希腊字母
        ("分数中的希腊字母", "\\frac{\\alpha g}{\\beta h}"),
        ("复杂分数", "\\frac{\\alpha g \\nu}{2}"),
        
        # 上下标中的希腊字母
        ("上下标中", "x_{\\alpha g}"),
        ("复杂上下标", "y^{\\alpha \\beta}_{\\gamma \\delta}"),
        
        # 原问题样本
        ("原问题样本", "e = \\frac { \\alpha g \\nu } { 2 } ( 1 - \\omega ) ."),
        
        # 其他数学符号
        ("partial", "\\frac{\\partial f}{\\partial x}"),
        ("nabla", "\\nabla \\cdot \\vec{F}"),
        ("infty", "\\lim_{x \\to \\infty} f(x)"),
        
        # 大写希腊字母
        ("大写希腊字母", "\\Gamma \\Delta \\Theta"),
        
        # 混合测试
        ("混合测试", "\\alpha x + \\beta y = \\gamma z"),
    ]
    
    for desc, latex_input in test_cases:
        try:
            latex_output = normalizer.normalize(latex_input)
            has_changed = latex_input != latex_output
            
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  规范后: {latex_output}")
            print(f"  状态: {'✓ 已规范化' if has_changed else '─ 无变化'}")
            
            # 检查特定问题
            if "\\alpha g" in latex_input:
                if "\\alphag" in latex_output:
                    print("  ✗ 问题: \\alpha和g连在一起了")
                elif "\\alpha g" in latex_output or "\\alpha " in latex_output:
                    print("  ✓ \\alpha后面有正确的空格")
                else:
                    print("  ? \\alpha空格情况需要检查")
            
            if "\\nu" in latex_input:
                # 检查\\nu后面是否有适当的分隔
                if "\\nu}" in latex_output or "\\nu " in latex_output or latex_output.endswith("\\nu"):
                    print("  ✓ \\nu后面有适当的分隔")
                elif any(f"\\nu{char}" in latex_output for char in "abcdefghijklmnopqrstuvwxyz"):
                    print("  ✗ 问题: \\nu后面缺少空格")
            
            # 检查其他希腊字母
            greek_letters = ["\\alpha", "\\beta", "\\gamma", "\\delta", "\\epsilon", "\\nu", "\\omega"]
            for letter in greek_letters:
                if letter in latex_input:
                    # 检查是否有字母直接连接的情况
                    import re
                    pattern = re.escape(letter) + r'[a-zA-Z]'
                    if re.search(pattern, latex_output):
                        print(f"  ✗ 问题: {letter}后面可能缺少空格")
            
        except Exception as e:
            print(f"\n{desc}:")
            print(f"  规范前: {latex_input}")
            print(f"  错误: {e}")
            print(f"  状态: ✗ 处理失败")


def test_specific_problem():
    """测试原始问题"""
    print("\n" + "=" * 80)
    print("原始问题专项测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    # 原始问题
    problem_input = "e = \\frac { \\alpha g \\nu } { 2 } ( 1 - \\omega ) ."
    
    print("原始问题测试:")
    print(f"输入: {problem_input}")
    
    try:
        result = normalizer.normalize(problem_input)
        print(f"输出: {result}")
        
        print("\n详细检查:")
        
        # 检查\\alpha g \\nu的处理
        if "\\alphag\\nu" in result:
            print("✗ 严重问题: \\alpha g \\nu变成了\\alphag\\nu")
        elif "\\alpha g \\nu" in result:
            print("✓ 完美: \\alpha g \\nu保持正确")
        elif "\\alpha g\\nu" in result:
            print("⚠️ 部分问题: \\alpha后有空格但\\nu前没有")
        elif "\\alphag \\nu" in result:
            print("⚠️ 部分问题: \\alpha和g连在一起了")
        else:
            print("? 需要手动检查\\alpha g \\nu的处理")
        
        # 检查分数内部的空格处理
        if "\\frac{\\alpha g \\nu}{2}" in result:
            print("✓ 分数内部空格处理正确")
        elif "\\frac{\\alphag\\nu}{2}" in result:
            print("✗ 分数内部空格处理错误")
        else:
            print("? 分数内部空格处理需要检查")
        
        # 检查整体结构
        if "e=" in result and "(1-\\omega)" in result:
            print("✓ 整体结构处理正确")
        else:
            print("? 整体结构需要检查")
        
    except Exception as e:
        print(f"处理失败: {e}")


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 80)
    print("边界情况测试")
    print("=" * 80)
    
    normalizer = LaTeXNormalizer()
    
    edge_cases = [
        # 希腊字母在不同位置
        ("开头", "\\alpha + b"),
        ("结尾", "a + \\beta"),
        ("中间", "a \\gamma b"),
        
        # 希腊字母与运算符
        ("与加号", "\\alpha + \\beta"),
        ("与乘号", "\\alpha \\times \\beta"),
        ("与等号", "\\alpha = \\beta"),
        
        # 希腊字母与括号
        ("与括号", "\\alpha (x + y)"),
        ("在括号内", "(\\alpha + \\beta)"),
        
        # 希腊字母与数字
        ("与数字", "\\alpha 2"),
        ("数字与希腊字母", "2 \\alpha"),
        
        # 复杂嵌套
        ("复杂嵌套", "\\frac{\\alpha x^{\\beta y}}{\\gamma z}"),
    ]
    
    for desc, latex_input in edge_cases:
        try:
            latex_output = normalizer.normalize(latex_input)
            print(f"\n{desc}:")
            print(f"  输入: {latex_input}")
            print(f"  输出: {latex_output}")
            
            # 简单检查是否有明显的连接问题
            import re
            if re.search(r'\\[a-zA-Z]+[a-zA-Z]', latex_output):
                print("  ⚠️ 可能存在命令与字母连接的问题")
            else:
                print("  ✓ 看起来正常")
                
        except Exception as e:
            print(f"\n{desc}:")
            print(f"  输入: {latex_input}")
            print(f"  错误: {e}")


if __name__ == "__main__":
    test_greek_letter_spacing()
    test_specific_problem()
    test_edge_cases()
