const path = require('path');
var katex = require(path.join(__dirname,"third_party/katex/katex.js"))
options = require(path.join(__dirname,"third_party/katex/src/Options.js"))
var readline = require('readline');
var rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: false
});


// 全局变量用于跟踪处理状态
var processingContext = {
    inEnvironment: false,
    environmentStack: [],
    currentEnvironment: null,
    nestedLevel: 0
};

// 环境检测和预处理函数
function detectAndPreprocessEnvironments(line) {
    // 检测所有LaTeX环境
    var envPattern = /\\begin\{([^}]+)\}/g;
    var endPattern = /\\end\{([^}]+)\}/g;

    var environments = [];
    var match;

    // 收集所有环境信息
    while ((match = envPattern.exec(line)) !== null) {
        environments.push({
            type: 'begin',
            name: match[1],
            position: match.index
        });
    }

    while ((match = endPattern.exec(line)) !== null) {
        environments.push({
            type: 'end',
            name: match[1],
            position: match.index
        });
    }

    // 按位置排序
    environments.sort((a, b) => a.position - b.position);

    // 更新处理上下文
    processingContext.inEnvironment = environments.length > 0;
    processingContext.environments = environments;

    return line;
}

// 改进的预处理函数
function improvedPreprocessing(line) {
    // 步骤1: 环境检测
    line = detectAndPreprocessEnvironments(line);

    // 步骤2: 智能换行符处理
    line = smartLineBreakHandling(line);

    // 步骤3: 复杂结构预处理
    line = preprocessComplexStructures(line);

    return line;
}

// 智能换行符处理
function smartLineBreakHandling(line) {
    // 如果包含结构化环境，保持换行符
    var structuredEnvs = ['matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix',
                         'cases', 'array', 'align', 'aligned', 'split', 'gather'];

    var hasStructuredEnv = structuredEnvs.some(env => line.includes(env));

    if (!hasStructuredEnv && !line.includes('\\begin')) {
        // 非结构化内容：将\\替换为适当的间距
        line = line.replace(/\\\\/g, '\\,');
    }

    return line;
}

// 复杂结构预处理
function preprocessComplexStructures(line) {
    // 处理嵌套的花括号
    line = balanceBraces(line);

    // 处理特殊的数学函数
    line = preprocessMathFunctions(line);

    // 处理复杂的上下标
    line = preprocessSupSub(line);

    return line;
}

// 花括号平衡检查和修复
function balanceBraces(line) {
    var braceCount = 0;
    var result = '';

    for (var i = 0; i < line.length; i++) {
        var char = line[i];
        if (char === '{') {
            braceCount++;
        } else if (char === '}') {
            braceCount--;
        }
        result += char;
    }

    // 如果花括号不平衡，尝试修复
    while (braceCount > 0) {
        result += '}';
        braceCount--;
    }

    return result;
}

// 数学函数预处理
function preprocessMathFunctions(line) {
    // 标准化常见的数学函数表示
    var mathFunctions = ['sin', 'cos', 'tan', 'cot', 'sec', 'csc', 'sinh', 'cosh', 'tanh', 'coth',
                        'arcsin', 'arccos', 'arctan', 'ln', 'lg', 'log', 'exp', 'min', 'max', 'det', 'lim'];

    mathFunctions.forEach(func => {
        // 将 \operatorname{func} 和 \mathrm{func} 统一为 \func
        var operatorPattern = new RegExp('\\\\operatorname\\s*\\{\\s*' + func + '\\s*\\}', 'g');
        var mathrmPattern = new RegExp('\\\\mathrm\\s*\\{\\s*' + func + '\\s*\\}', 'g');

        line = line.replace(operatorPattern, '\\' + func);
        line = line.replace(mathrmPattern, '\\' + func);
    });

    return line;
}

// 上下标预处理
function preprocessSupSub(line) {
    // 处理复杂的上下标表达式，确保正确的花括号
    // 这里可以添加更复杂的逻辑来处理嵌套的上下标
    return line;
}

rl.on('line', function(line){
    // JavaScript AST处理器主函数 - 改进版
    // 数据流: Python输入 → 智能预处理 → KaTeX解析 → 增强AST渲染 → 标准化输出

    a = line  // 保存原始输入用于调试和错误恢复

    try {
        // 步骤1: 注释处理 - 移除LaTeX注释，但保护转义的%
        if (line[0] == "%") {
            line = line.substr(1, line.length - 1);  // 移除行首%注释标记
        }

        // 保护转义的百分号 \%
        line = line.replace(/\\%/g, '__ESCAPED_PERCENT__');
        line = line.split('%')[0];  // 移除行内%注释及其后内容
        // 恢复转义的百分号
        line = line.replace(/__ESCAPED_PERCENT__/g, '\\%');

        // 步骤2: 特殊字符预处理
        line = line.split('\\~').join(' ');  // 将\~替换为空格

        // 步骤3: 清理不需要的LaTeX命令和符号
        line = line.replace(/\\label\{.*?\}/g, ""); // 移除\label{...}标签
        line = line.replace(/\\tag\{.*?\}/g, "");   // 移除\tag{...}标签
        line = line.replace(/\\nonumber/g, "");     // 移除\nonumber命令

        // 步骤4: 改进的预处理
        line = improvedPreprocessing(line);

        // 步骤5: 添加结尾空格确保解析正确
        line = line + " "

        // global_str: tokenized版本 (在parser.js中构建)
        // norm_str: normalized版本 (在下面的renderer中构建)

        // 步骤6: 模式选择 - tokenize模式或normalize模式
        if (process.argv[2] == "tokenize") {
            // tokenize模式: 仅解析为tokens
            var tree = katex.__parse(line, {});
            console.log(global_str.replace(/\\label { .*? }/, ""));
        } else {
            // normalize模式: 完整的AST处理和规范化

            // 步骤6.1: \rm命令统一化 - 改进版本
            line = standardizeRmCommands(line);

            // 步骤6.2: KaTeX AST解析 - 增强错误处理
            var tree;
            try {
                tree = katex.__parse(line, {
                    throwOnError: false,
                    errorColor: "#cc0000",
                    macros: {}
                });
            } catch (parseError) {
                // 解析失败时的降级处理
                console.error("Parse error for input: " + line);
                console.error("Error: " + parseError.message);

                // 尝试简化输入后重新解析
                var simplifiedLine = fallbackSimplification(line);
                try {
                    tree = katex.__parse(simplifiedLine, {throwOnError: false});
                } catch (fallbackError) {
                    // 如果仍然失败，输出原始输入
                    console.log(a);
                    return;
                }
            }

            // 步骤6.3: AST渲染为标准化LaTeX - 增强版本
            try {
                buildExpression(tree, new options({}));
            } catch (renderError) {
                console.error("Render error: " + renderError.message);
                console.log(a);  // 输出原始输入作为降级
                return;
            }

            // 步骤6.4: 后处理 - 恢复特殊符号和清理
            norm_str = postProcessNormalized(norm_str);

            // 步骤6.5: 最终验证和输出
            var finalResult = validateAndCleanOutput(norm_str);
            console.log(finalResult);
        }
    } catch (e) {
        // 顶级错误处理: 提供更详细的错误信息
        console.error("Top-level error processing: " + a);
        console.error("Current norm_str: " + norm_str);
        console.error("Error details: " + e.message);
        console.error("Stack trace: " + e.stack);

        // 降级输出原始输入
        console.log(a);
    }

    // 步骤7: 重置全局变量为下一行处理做准备
    global_str = ""
    norm_str = ""
    processingContext = {
        inEnvironment: false,
        environmentStack: [],
        currentEnvironment: null,
        nestedLevel: 0
    };
})

// 标准化\rm命令的改进函数
function standardizeRmCommands(line) {
    // 更精确的\rm命令替换
    var rmPatterns = [
        [/{\\rm\s+([^}]+)}/g, '\\mathrm{$1}'],
        [/{\s*\\rm\s+([^}]+)}/g, '\\mathrm{$1}'],
        [/\\rm\s*{([^}]+)}/g, '\\mathrm{$1}'],
        [/\\rm\s+([a-zA-Z]+)/g, '\\mathrm{$1}']
    ];

    rmPatterns.forEach(([pattern, replacement]) => {
        line = line.replace(pattern, replacement);
    });

    return line;
}

// 降级简化函数
function fallbackSimplification(line) {
    // 移除可能导致解析问题的复杂结构
    var simplified = line;

    // 移除复杂的环境
    simplified = simplified.replace(/\\begin\{[^}]+\}.*?\\end\{[^}]+\}/g, '');

    // 移除复杂的命令
    simplified = simplified.replace(/\\[a-zA-Z]+\*?(\[[^\]]*\])?(\{[^}]*\})?/g, '');

    // 保留基本的数学符号
    simplified = simplified.replace(/[^a-zA-Z0-9+\-=<>(){}[\]\\,.\s]/g, '');

    return simplified || 'x';  // 如果完全为空，返回简单的x
}

// 后处理规范化结果
function postProcessNormalized(str) {
    // 恢复特殊符号
    for (var i = 0; i < 10; i++) {  // 减少循环次数提高性能
        str = str.replace(/SSSSSS/g, '$');
        str = str.replace(/\s*S\s*S\s*S\s*S\s*S\s*S\s*/g, '$');
    }

    // 清理多余的空格
    str = str.replace(/\s+/g, ' ');
    str = str.replace(/\s*{\s*/g, '{');
    str = str.replace(/\s*}\s*/g, '}');

    // 移除标签
    str = str.replace(/\\label\s*\{[^}]*\}/g, '');
    str = str.replace(/\\tag\s*\{[^}]*\}/g, '');

    return str.trim();
}

// 验证和清理输出
function validateAndCleanOutput(str) {
    // 基本的语法验证
    var braceCount = (str.match(/\{/g) || []).length - (str.match(/\}/g) || []).length;

    // 如果花括号不平衡，尝试修复
    if (braceCount > 0) {
        str += '}' .repeat(braceCount);
    } else if (braceCount < 0) {
        str = '{'.repeat(-braceCount) + str;
    }

    // 最终清理
    str = str.replace(/\s+/g, ' ').trim();

    return str;
}

// 智能环境名称确定函数 - 修复版本
function determineEnvironmentName(group, originalInput) {
    var envName = "aligned";  // 默认环境

    // 优先级1: 检查AST节点中的环境信息
    if (group.value && group.value.envName) {
        envName = group.value.envName;
    }
    // 优先级2: 从原始输入推断 - 修复环境识别
    else if (originalInput) {
        var envPatterns = [
            // 特殊环境，保持原样
            ['cases', /\\begin\{cases\}/],
            ['array', /\\begin\{array\}/],

            // 矩阵环境，保持原样（除了smallmatrix）
            ['pmatrix', /\\begin\{pmatrix\}/],
            ['bmatrix', /\\begin\{bmatrix\}/],
            ['vmatrix', /\\begin\{vmatrix\}/],
            ['Vmatrix', /\\begin\{Vmatrix\}/],
            ['matrix', /\\begin\{matrix\}/],

            // smallmatrix统一为matrix
            ['matrix', /\\begin\{smallmatrix\}/],

            // 对齐环境统一为aligned
            ['aligned', /\\begin\{aligned\}/],
            ['aligned', /\\begin\{align\*?\}/],  // align和align*
            ['aligned', /\\begin\{split\}/],
            ['aligned', /\\begin\{gather\*?\}/],  // gather和gather*
            ['aligned', /\\begin\{alignedat\}/],
            ['aligned', /\\begin\{alignat\}/],
            ['aligned', /\\begin\{eqnarray\*?\}/]  // eqnarray和eqnarray*
        ];

        for (var i = 0; i < envPatterns.length; i++) {
            var name = envPatterns[i][0];
            var pattern = envPatterns[i][1];
            if (pattern.test(originalInput)) {
                envName = name;
                break;
            }
        }
    }

    return envName;
}

// 根据内容推断环境类型
function inferEnvironmentFromContent(bodyArray) {
    if (!Array.isArray(bodyArray) || bodyArray.length === 0) {
        return "aligned";
    }

    var firstRow = bodyArray[0];
    if (!Array.isArray(firstRow)) {
        return "aligned";
    }

    var columnCount = firstRow.length;
    var rowCount = bodyArray.length;

    // 如果是方形且列数较少，可能是矩阵
    if (rowCount === columnCount && columnCount <= 4) {
        return "matrix";
    }

    // 如果只有两列且第二列很简单，可能是cases
    if (columnCount === 2) {
        var secondColumnSimple = bodyArray.every(row =>
            row.length >= 2 && isSimpleExpression(row[1])
        );
        if (secondColumnSimple) {
            return "cases";
        }
    }

    return "aligned";
}

// 检查表达式是否简单
function isSimpleExpression(expr) {
    if (!expr || !expr.value) return true;

    // 简单表达式：只包含基本的数学符号和文本
    return expr.value.length <= 3;
}

// 处理环境特定属性
function handleEnvironmentSpecificAttributes(envName, group, options) {
    if (envName === "array") {
        // array环境需要列对齐信息
        norm_str += "{";
        if (group.value.cols && Array.isArray(group.value.cols)) {
            group.value.cols.forEach(col => {
                if (col && col.align) {
                    norm_str += col.align;
                } else {
                    norm_str += "c";  // 默认居中对齐
                }
            });
        } else {
            // 根据第一行推断列数
            var firstRow = group.value.body[0];
            if (Array.isArray(firstRow)) {
                for (var i = 0; i < firstRow.length; i++) {
                    norm_str += "c";
                }
            }
        }
        norm_str += "}";
    }

    // 处理行数据
    processArrayRows(group.value.body, envName, options);
}

// 处理数组行数据 - 改进版本
function processArrayRows(bodyArray, envName, options) {
    if (!Array.isArray(bodyArray)) return;

    var totalRows = bodyArray.length;
    var processedRows = 0;

    bodyArray.forEach((row, rowIndex) => {
        if (!Array.isArray(row)) return;

        // 检查行是否有实际内容
        var hasContent = row.some(cell =>
            cell && cell.value && cell.value.length > 0
        );

        if (hasContent) {
            processedRows++;

            // 处理行中的每个单元格
            row.forEach((cell, cellIndex) => {
                if (cell) {
                    processCellContent(cell, envName, options);
                }

                // 添加列分隔符（除了最后一列）
                if (cellIndex < row.length - 1) {
                    norm_str += "&";
                }
            });

            // 添加行分隔符（除了最后一行）
            if (shouldAddRowSeparator(envName, rowIndex, totalRows, processedRows)) {
                norm_str += "\\\\";
            }
        }
    });

    // 结束环境
    norm_str += "\\end{" + envName + "}";
}

// 处理单元格内容 - 修复版本
function processCellContent(cell, envName, options) {
    // 根据环境类型调整处理方式
    var cellOptions = Object.assign({}, options, {
        inArray: true,
        arrayEnv: envName
    });

    // 对于aligned环境，特殊处理以避免不必要的花括号
    if (envName === 'aligned') {
        if (cell.type === 'ordgroup' && cell.value && Array.isArray(cell.value)) {
            // 对于aligned环境中的ordgroup，直接处理其内容，不添加花括号
            buildExpression(cell.value, cellOptions);
        } else {
            buildGroup(cell, cellOptions);
        }
    } else {
        // 其他环境的正常处理
        if (cell.type === 'ordgroup' && cell.value && cell.value.length === 1) {
            // 简单单元格，直接处理内容
            var element = cell.value[0];
            if (element.type === 'mathord' || element.type === 'textord') {
                buildGroup(element, cellOptions);
            } else {
                buildGroup(cell, cellOptions);
            }
        } else {
            buildGroup(cell, cellOptions);
        }
    }
}

// 判断是否应该添加行分隔符 - 修复版本
function shouldAddRowSeparator(envName, rowIndex, totalRows, processedRows) {
    // 对于所有矩阵和对齐环境，只要不是最后一行就添加行分隔符
    var matrixEnvs = ['matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix'];
    var alignEnvs = ['aligned', 'cases', 'array'];

    if (matrixEnvs.includes(envName) || alignEnvs.includes(envName)) {
        return rowIndex < totalRows - 1;
    }

    // 默认情况：不是最后一行就添加
    return rowIndex < totalRows - 1;
}



// LaTeX AST到LaTeX渲染器 (增强版)
// 核心功能: 将KaTeX解析的AST重新渲染为标准化的LaTeX字符串
norm_str = ""

// 全局上下文管理器
var renderingContext = {
    inSupSub: false,        // 是否在上下标中
    inFrac: false,          // 是否在分数中
    inSqrt: false,          // 是否在根号中
    inArray: false,         // 是否在数组/矩阵中
    inFont: false,          // 是否在字体命令中
    inAccent: false,        // 是否在重音符号中
    arrayEnv: null,         // 当前数组环境类型
    depth: 0,               // 嵌套深度
    lastNodeType: null,     // 上一个节点类型
    needsSpace: false       // 是否需要添加空格
};

// 上下文管理函数
function pushContext(contextType, value) {
    renderingContext[contextType] = value;
    renderingContext.depth++;
}

function popContext(contextType) {
    renderingContext[contextType] = false;
    renderingContext.depth = Math.max(0, renderingContext.depth - 1);
}

// 智能空格管理 - 修复版本
function shouldAddSpace(currentNodeType, nextNodeType) {
    // 在特定上下文中不添加空格
    if (renderingContext.inSupSub || renderingContext.inFrac ||
        renderingContext.inSqrt || renderingContext.inFont) {
        return false;
    }

    // 特定节点类型后不需要空格
    var noSpaceAfter = ['open', 'punct'];  // 移除了'bin'和'rel'
    if (noSpaceAfter.includes(currentNodeType)) {
        return false;
    }

    // 特定节点类型前不需要空格
    var noSpaceBefore = ['close', 'punct'];  // 移除了'bin'和'rel'
    if (nextNodeType && noSpaceBefore.includes(nextNodeType)) {
        return false;
    }

    // 二元运算符和关系运算符需要空格来分隔token
    if (currentNodeType === 'bin' || currentNodeType === 'rel') {
        return true;
    }

    return true;
}

function addSpaceIfNeeded(nodeType) {
    if (shouldAddSpace(nodeType, null)) {
        norm_str += " ";
    }
    renderingContext.lastNodeType = nodeType;
}

var groupTypes = {};

// 数学普通字符处理 (变量、数字等) - 改进版
groupTypes.mathord = function(group, options) {
    // 输入: AST节点包含字符值和字体选项
    // 输出: 规范化的字符序列

    if (!group.value) return;

    if (options && options.font === "mathrm") {
        // mathrm字体: 特殊处理空格和字符
        for (var i = 0; i < group.value.length; i++) {
            if (group.value[i] === " ") {
                // 在mathrm中，空格转换为适当的间距
                if (!renderingContext.inSupSub) {
                    norm_str += "\\,";  // 小间距
                }
            } else {
                norm_str += group.value[i];
            }
        }
    } else {
        // 其他字体: 直接输出值，不添加多余空格
        norm_str += group.value;
    }

    // 智能空格管理
    addSpaceIfNeeded('mathord');
};

// 文本字符处理 - 改进版
groupTypes.textord = function(group, options) {
    // 处理普通文本字符
    if (group.value) {
        norm_str += group.value;
        addSpaceIfNeeded('textord');
    }
};

// 二元运算符处理 (+, -, *, 等) - 修复版本
groupTypes.bin = function(group) {
    // 二元运算符需要适当的空格来分隔token
    if (group.value) {
        norm_str += group.value;
        // 二元运算符后需要空格，除非在特定上下文中
        if (!renderingContext.inSupSub && !renderingContext.inFrac) {
            addSpaceIfNeeded('bin');
        }
    }
};

// 关系运算符处理 (=, <, >, 等) - 修复版本
groupTypes.rel = function(group) {
    // 关系运算符需要适当的空格来分隔token
    if (group.value) {
        norm_str += group.value;
        // 关系运算符后需要空格，除非在特定上下文中
        if (!renderingContext.inSupSub && !renderingContext.inFrac) {
            addSpaceIfNeeded('rel');
        }
    }
};

// 开括号处理 (, [, {, 等 - 改进版
groupTypes.open = function(group) {
    if (group.value) {
        norm_str += group.value;
        // 开括号后不需要空格
    }
};

// 闭括号处理 ), ], }, 等 - 改进版
groupTypes.close = function(group) {
    if (group.value) {
        norm_str += group.value;
        addSpaceIfNeeded('close');
    }
};

// 内部元素处理 - 改进版
groupTypes.inner = function(group) {
    if (group.value) {
        norm_str += group.value;
        addSpaceIfNeeded('inner');
    }
};

// 标点符号处理 - 改进版
groupTypes.punct = function(group) {
    if (group.value) {
        norm_str += group.value;
        // 标点符号后通常需要空格
        addSpaceIfNeeded('punct');
    }
};

// 有序组处理 (智能化版本)
groupTypes.ordgroup = function(group, options) {
    // 输入: 包含多个子表达式的有序组
    // 输出: { 子表达式... } 或直接输出（根据上下文决定）

    if (!group.value || !Array.isArray(group.value)) {
        return;
    }

    // 智能花括号判断
    var needsBraces = shouldOrdGroupHaveBraces(group, options);

    if (needsBraces) {
        norm_str += "{";
    }

    // 处理组内表达式，传递上下文信息
    var groupOptions = Object.assign({}, options, {
        inOrdGroup: true,
        ordGroupBraced: needsBraces
    });

    buildExpression(group.value, groupOptions);

    if (needsBraces) {
        norm_str += "}";
    }

    // 智能空格管理
    if (!renderingContext.inSupSub && !renderingContext.inFrac) {
        addSpaceIfNeeded('ordgroup');
    }
};

// 判断ordgroup是否需要花括号的智能函数
function shouldOrdGroupHaveBraces(group, options) {
    if (!group.value || group.value.length === 0) {
        return false;
    }

    // 在特定上下文中的判断
    if (renderingContext.inSupSub) {
        // 上下标中：多个元素或复杂单元素需要花括号
        if (group.value.length > 1) return true;
        if (group.value.length === 1) {
            return isComplexNode(group.value[0]);
        }
        return false;
    }

    if (renderingContext.inFrac || renderingContext.inSqrt) {
        // 分数或根号中：通常不需要额外花括号
        return false;
    }

    if (renderingContext.inFont || renderingContext.inAccent) {
        // 字体或重音中：通常不需要额外花括号
        return false;
    }

    // 默认情况：多个元素需要花括号
    return group.value.length > 1;
}

// 判断节点是否复杂的辅助函数
function isComplexNode(node) {
    if (!node) return false;

    var complexTypes = ['supsub', 'genfrac', 'sqrt', 'accent', 'font', 'array', 'leftright'];
    return complexTypes.includes(node.type);
}

// 文本模式处理 - 改进版
groupTypes.text = function(group, options) {
    // 输入: 文本模式的AST节点
    // 输出: \text{文本内容} 或 \mathrm{文本内容}

    if (!group.value || !group.value.body) {
        return;
    }

    // 使用\text而不是\mathrm，更符合语义
    norm_str += "\\text{";

    // 设置文本模式上下文
    pushContext('inFont', true);
    var textOptions = Object.assign({}, options, {
        font: 'text',
        inText: true
    });

    buildExpression(group.value.body, textOptions);

    popContext('inFont');
    norm_str += "}";

    addSpaceIfNeeded('text');
};

// 颜色处理 (通常在规范化中忽略)
groupTypes.color = function(group, options) {
    // 输入: 颜色AST节点
    // 输出: MathML节点 (在LaTeX规范化中不常用)
    var inner = buildExpression(group.value.value, options);

    var node = new mathMLTree.MathNode("mstyle", inner);
    node.setAttribute("mathcolor", group.value.color);

    return node;
};

// 上下标处理 (完全重写版本) - 解决所有已知问题
groupTypes.supsub = function(group, options) {
    // 输入: 包含base、sup(上标)、sub(下标)的AST节点
    // 输出: base_sub^sup 或 base^sup 或 base_sub

    if (!group.value) return;

    // 步骤1: 处理基础表达式
    buildGroup(group.value.base, options);

    // 步骤2: 处理下标 (LaTeX标准顺序：下标在前)
    if (group.value.sub) {
        norm_str += "_";
        renderSupSubContent(group.value.sub, options, 'sub');
    }

    // 步骤3: 处理上标
    if (group.value.sup) {
        norm_str += "^";
        renderSupSubContent(group.value.sup, options, 'sup');
    }

    // 智能空格管理：只在非上下标上下文中添加空格
    if (!renderingContext.inSupSub) {
        addSpaceIfNeeded('supsub');
    }
};

// 渲染上下标内容的专用函数
function renderSupSubContent(node, options, type) {
    if (!node) return;

    // 设置上下标上下文
    var wasInSupSub = renderingContext.inSupSub;
    pushContext('inSupSub', true);

    var supSubOptions = Object.assign({}, options, {
        inSupSub: true,
        supSubType: type
    });

    // 智能花括号判断
    var needsBraces = needsSupSubBraces(node);

    if (needsBraces) {
        norm_str += "{";
    }

    // 渲染内容
    if (node.type === 'ordgroup') {
        renderOrdGroupInSupSub(node, supSubOptions);
    } else {
        buildGroup(node, supSubOptions);
    }

    if (needsBraces) {
        norm_str += "}";
    }

    // 恢复上下文
    renderingContext.inSupSub = wasInSupSub;
    if (!wasInSupSub) {
        popContext('inSupSub');
    }
}

// 判断上下标是否需要花括号的精确函数
function needsSupSubBraces(node) {
    if (!node) return false;

    // 单个简单字符或数字不需要花括号
    if (node.type === 'mathord' || node.type === 'textord') {
        // 只有单个字符且不是复杂Unicode字符
        return node.value && (node.value.length > 1 || /[^\w\d]/.test(node.value));
    }

    // ordgroup的精确判断
    if (node.type === 'ordgroup') {
        if (!node.value || node.value.length === 0) return false;

        // 单个简单元素不需要花括号
        if (node.value.length === 1) {
            var element = node.value[0];
            if (element.type === 'mathord' || element.type === 'textord') {
                return element.value && element.value.length > 1;
            }
            return isComplexNode(element);
        }

        // 多个元素需要花括号
        return true;
    }

    // 复杂结构总是需要花括号
    return isComplexNode(node);
}

// 在上下标中渲染ordgroup的专用函数
function renderOrdGroupInSupSub(node, options) {
    if (!node.value || !Array.isArray(node.value)) {
        return;
    }

    // 在上下标中，紧凑地处理元素
    for (var i = 0; i < node.value.length; i++) {
        var element = node.value[i];
        buildGroup(element, options);

        // 在上下标中，只在必要时添加空格
        if (i < node.value.length - 1) {
            var current = element;
            var next = node.value[i + 1];

            // 只在运算符后添加空格
            if (current.type === 'bin' || current.type === 'rel') {
                norm_str += " ";
            }
            // 或者在函数名后添加空格
            else if (current.type === 'op' && next.type !== 'open') {
                norm_str += " ";
            }
        }
    }
}

// 广义分数处理 (分数和二项式系数) - 完全重写版本
groupTypes.genfrac = function(group, options) {
    // 输入: 包含分子、分母和是否有分数线的AST节点
    // 输出: \frac{分子}{分母} 或 \binom{上}{下}

    if (!group.value) return;

    // 确定分数类型
    var command = group.value.hasBarLine ? "\\frac" : "\\binom";
    norm_str += command;

    // 设置分数上下文
    var wasInFrac = renderingContext.inFrac;
    pushContext('inFrac', true);

    var fracOptions = Object.assign({}, options, {
        inFrac: true
    });

    // 处理分子/上部
    norm_str += "{";
    renderFracContent(group.value.numer, fracOptions);
    norm_str += "}";

    // 处理分母/下部
    norm_str += "{";
    renderFracContent(group.value.denom, fracOptions);
    norm_str += "}";

    // 恢复上下文
    renderingContext.inFrac = wasInFrac;
    if (!wasInFrac) {
        popContext('inFrac');
    }

    addSpaceIfNeeded('genfrac');
};

// 渲染分数内容的专用函数
function renderFracContent(node, options) {
    if (!node) return;

    if (node.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容，避免双重花括号
        if (node.value && Array.isArray(node.value)) {
            buildExpression(node.value, options);
        }
    } else {
        buildGroup(node, options);
    }
}

// 数组/矩阵处理 (核心结构化处理) - 增强版本
groupTypes.array = function(group, options) {
    // 输入: 包含行列数据和对齐信息的数组AST节点
    // 输出: \begin{环境名}{对齐} 行数据... \end{环境名}

    // 步骤1: 智能确定环境名称 - 改进的环境识别
    var envName = determineEnvironmentName(group, a);

    // 步骤2: 验证环境数据完整性
    if (!group.value || !group.value.body || !Array.isArray(group.value.body)) {
        console.error("Invalid array structure");
        norm_str += "\\text{Invalid array}";
        return;
    }

    // 步骤3: 开始环境
    norm_str = norm_str + "\\begin{" + envName + "}";

    // 步骤4: 处理环境特定的属性
    handleEnvironmentSpecificAttributes(envName, group, options);

};

// 根号处理 (平方根和n次根) - 改进版本
groupTypes.sqrt = function(group, options) {
    // 输入: 根号AST节点，可能包含指数和被开方数
    // 输出: \sqrt[n]{expr} 或 \sqrt{expr}

    if (!group.value) return;

    if (group.value.index) {
        // n次根: \sqrt[n]{expr}
        norm_str += "\\sqrt[";

        // 处理根指数，在方括号中不需要额外的花括号
        if (group.value.index.value && Array.isArray(group.value.index.value)) {
            buildExpression(group.value.index.value, options);
        } else {
            buildGroup(group.value.index, options);
        }

        norm_str += "]";
    } else {
        // 平方根: \sqrt{expr}
        norm_str += "\\sqrt";
    }

    // 设置根号上下文
    var wasInSqrt = renderingContext.inSqrt;
    pushContext('inSqrt', true);

    var sqrtOptions = Object.assign({}, options, {
        inSqrt: true
    });

    // 处理被开方数
    norm_str += "{";
    renderSqrtContent(group.value.body, sqrtOptions);
    norm_str += "}";

    // 恢复上下文
    renderingContext.inSqrt = wasInSqrt;
    if (!wasInSqrt) {
        popContext('inSqrt');
    }

    addSpaceIfNeeded('sqrt');
};

// 渲染根号内容的专用函数
function renderSqrtContent(node, options) {
    if (!node) return;

    if (node.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容
        if (node.value && Array.isArray(node.value)) {
            buildExpression(node.value, options);
        }
    } else {
        buildGroup(node, options);
    }
}

// 左右定界符处理 (括号、方括号等的自动调整大小) - 修复版本
groupTypes.leftright = function(group, options) {
    // 输入: 包含左定界符、内容、右定界符的AST节点
    // 输出: \left( 内容 \right) 或类似结构

    if (!group.value) return;

    // 检查是否是矩阵环境，如果是则不添加left/right
    var isMatrixEnvironment = checkIfMatrixEnvironment(group.value.body);

    if (isMatrixEnvironment) {
        // 对于矩阵环境，直接处理内容，不添加left/right
        var matrixOptions = Object.assign({}, options, {
            inMatrix: true
        });

        if (group.value.body && Array.isArray(group.value.body)) {
            buildExpression(group.value.body, matrixOptions);
        }
    } else {
        // 非矩阵环境，正常处理left/right
        // 步骤1: 添加左定界符
        norm_str += "\\left" + (group.value.left || ".");

        // 步骤2: 处理定界符内的表达式
        var leftRightOptions = Object.assign({}, options, {
            inLeftRight: true
        });

        if (group.value.body && Array.isArray(group.value.body)) {
            buildExpression(group.value.body, leftRightOptions);
        }

        // 步骤3: 添加右定界符
        norm_str += "\\right" + (group.value.right || ".");
    }

    addSpaceIfNeeded('leftright');
};

// 检查是否是矩阵环境的函数
function checkIfMatrixEnvironment(body) {
    if (!body || !Array.isArray(body) || body.length === 0) {
        return false;
    }

    // 查找第一个array类型的节点
    for (var i = 0; i < body.length; i++) {
        var node = body[i];
        if (node && node.type === 'array') {
            // 检查原始输入中是否包含矩阵环境
            var matrixEnvs = ['pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix', 'matrix', 'smallmatrix'];
            for (var j = 0; j < matrixEnvs.length; j++) {
                if (a && a.includes('\\begin{' + matrixEnvs[j] + '}')) {
                    return true;
                }
            }
        }
    }

    return false;
}

// 重音符号处理 (帽子、波浪线等) - 改进版本
groupTypes.accent = function(group, options) {
    // 输入: 重音符号AST节点，包含重音类型和基础表达式
    // 输出: \hat{x} 或 \tilde{expr} 等

    if (!group.value || !group.value.accent || !group.value.base) {
        return;
    }

    // 输出重音命令
    norm_str += group.value.accent + "{";

    // 设置重音上下文
    var wasInAccent = renderingContext.inAccent;
    pushContext('inAccent', true);

    var accentOptions = Object.assign({}, options, {
        inAccent: true
    });

    // 处理重音基础表达式
    renderAccentContent(group.value.base, accentOptions);

    norm_str += "}";

    // 恢复上下文
    renderingContext.inAccent = wasInAccent;
    if (!wasInAccent) {
        popContext('inAccent');
    }

    addSpaceIfNeeded('accent');
};

// 渲染重音内容的专用函数
function renderAccentContent(node, options) {
    if (!node) return;

    if (node.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容
        if (node.value && Array.isArray(node.value)) {
            buildExpression(node.value, options);
        }
    } else {
        buildGroup(node, options);
    }
}

// 间距处理 (空格和各种间距命令) - 改进版本
groupTypes.spacing = function(group, options) {
    // 输入: 间距AST节点，包含间距类型和值
    // 输出: 标准化的间距命令

    if (!group.value) return;

    if (group.value === " ") {
        // 空格的智能处理
        if (options && (options.font === "text" || options.inText)) {
            // 文本模式下保持原始空格
            norm_str += " ";
        } else if (renderingContext.inSupSub) {
            // 上下标中通常不需要空格
            // 不添加任何内容
        } else {
            // 其他模式转换为不换行空格
            norm_str += "~";
        }
    } else {
        // 其他间距命令直接输出，这些是有意义的LaTeX间距命令
        norm_str += group.value;

        // 间距命令后通常需要空格，除非在特定上下文中
        if (!renderingContext.inSupSub && !renderingContext.inFrac) {
            addSpaceIfNeeded('spacing');
        }
    }
};

// 操作符处理 (求和、积分、极限等大型操作符) - 增强版本
groupTypes.op = function(group, options) {
    // 输入: 操作符AST节点，可能是符号或操作符名称
    // 输出: 符号或\operatorname{name}，可能包含\limits修饰符

    if (group.value.symbol) {
        // 符号类操作符: 输出符号 (如∑, ∫等)
        norm_str += group.value.body;

        // 处理limits修饰符
        handleLimitsModifier(group);
    } else {
        // 命名操作符: 智能处理数学函数和自定义操作符
        processNamedOperator(group, options);
    }
};

// 处理limits修饰符
function handleLimitsModifier(group) {
    // 只有当显式设置了limits修饰符时才输出
    if (group.value.alwaysHandleSupSub === true) {
        if (group.value.limits === true) {
            norm_str += "\\limits ";
        } else if (group.value.limits === false) {
            norm_str += "\\nolimits ";
        } else {
            norm_str += " ";
        }
    } else {
        // 没有显式设置limits，保持默认行为
        norm_str += " ";
    }
}

// 处理命名操作符
function processNamedOperator(group, options) {
    var opName = group.value.body;
    var cleanOpName = opName.replace(/^\\/, '');

    // 扩展的数学函数列表
    var mathFunctions = [
        // 三角函数
        'sin', 'cos', 'tan', 'cot', 'sec', 'csc',
        // 双曲函数
        'sinh', 'cosh', 'tanh', 'coth',
        // 反三角函数
        'arcsin', 'arccos', 'arctan', 'arccot', 'arcsec', 'arccsc',
        // 对数函数
        'ln', 'lg', 'log', 'exp',
        // 极值函数
        'min', 'max', 'sup', 'inf', 'lim', 'limsup', 'liminf',
        // 其他函数
        'mod', 'deg', 'arg', 'dim', 'ker', 'hom', 'det', 'gcd', 'lcm', 'Pr'
    ];

    if (mathFunctions.includes(cleanOpName)) {
        // 标准数学函数：直接输出简化形式
        norm_str += "\\" + cleanOpName;

        // 检查后面是否需要空格
        if (!options || !options.inSupSub) {
            norm_str += " ";
        }
    } else {
        // 自定义操作符：使用\operatorname包装
        outputCustomOperator(group, cleanOpName);
    }
}

// 输出自定义操作符
function outputCustomOperator(group, cleanOpName) {
    if (group.value.limits === false) {
        // 无上下限的操作符
        norm_str += "\\operatorname{";
    } else {
        // 有上下限的操作符 (使用*版本)
        norm_str += "\\operatorname*{";
    }

    // 输出操作符名称，改进字符处理
    if (cleanOpName) {
        norm_str += cleanOpName;
    } else {
        // 如果没有清理的名称，使用原始body（跳过反斜杠）
        for (var i = 1; i < group.value.body.length; i++) {
            norm_str += group.value.body[i];
        }
    }

    norm_str += "} ";
}

// KaTeX标识处理 (通常不在规范化中使用)
groupTypes.katex = function(group) {
    // 输入: KaTeX标识AST节点
    // 输出: MathML文本节点 (在LaTeX规范化中很少用到)
    var node = new mathMLTree.MathNode(
        "mtext", [new mathMLTree.TextNode("KaTeX")]);

    return node;
};



// 字体处理 (数学字体命令) - 完全重写版本
groupTypes.font = function(group, options) {
    // 输入: 字体AST节点，包含字体类型和内容
    // 输出: \mathbf{...}, \mathrm{...}, \mathcal{...}等

    if (!group.value || !group.value.font || !group.value.body) {
        return;
    }

    var font = group.value.font;

    // 字体名称标准化
    var fontMap = {
        'mbox': 'text',
        'hbox': 'text',
        'rm': 'mathrm'
    };

    font = fontMap[font] || font;

    // 输出字体命令
    norm_str += "\\" + font + "{";

    // 设置字体上下文
    var wasInFont = renderingContext.inFont;
    pushContext('inFont', true);

    // 创建字体选项，避免withFont方法调用错误
    var fontOptions = Object.assign({}, options, {
        font: font,
        inFont: true
    });

    // 处理字体内容
    renderFontContent(group.value.body, fontOptions);

    norm_str += "}";

    // 恢复上下文
    renderingContext.inFont = wasInFont;
    if (!wasInFont) {
        popContext('inFont');
    }

    addSpaceIfNeeded('font');
};

// 渲染字体内容的专用函数
function renderFontContent(node, options) {
    if (!node) return;

    if (node.type === 'ordgroup') {
        // 对于ordgroup，直接处理其内容
        if (node.value && Array.isArray(node.value)) {
            buildExpression(node.value, options);
        }
    } else {
        buildGroup(node, options);
    }
}

// 定界符大小调整处理 (\big, \Big, \bigg等)
groupTypes.delimsizing = function(group) {
    // 输入: 定界符大小调整AST节点
    // 输出: \big(, \Big[, \bigg\{等
    var children = [];

    // 输出函数名和定界符值
    // 例: \big + ( → \big(
    norm_str = norm_str + group.value.funcName + " " + group.value.value + " ";
};

// 样式处理 (数学样式命令)
groupTypes.styling = function(group, options) {
    // 输入: 样式AST节点，包含样式类型和内容
    // 输出: \displaystyle, \textstyle, \scriptstyle等

    // 输出原始样式命令
    norm_str = norm_str + " " + group.value.original + " ";

    // 处理样式内的表达式
    buildExpression(group.value.value, options);
};

// 大小调整处理 (字体大小命令) - 修复版本
groupTypes.sizing = function(group, options) {
    // 输入: 大小调整AST节点，包含大小类型和内容
    // 输出: 标准化的大小命令

    if (!group.value) return;

    var original = group.value.original;

    // 处理老式字体命令
    if (original === "\\rm") {
        // \rm转换为\mathrm
        norm_str += "\\mathrm{";
        var rmOptions = Object.assign({}, options, {
            font: "mathrm",
            inFont: true
        });
        buildExpression(group.value.value, rmOptions);
        norm_str += "}";
    } else if (original === "\\bf") {
        // \bf转换为\mathbf
        norm_str += "\\mathbf{";
        var bfOptions = Object.assign({}, options, {
            font: "mathbf",
            inFont: true
        });
        buildExpression(group.value.value, bfOptions);
        norm_str += "}";
    } else if (original === "\\it") {
        // \it转换为\mathit
        norm_str += "\\mathit{";
        var itOptions = Object.assign({}, options, {
            font: "mathit",
            inFont: true
        });
        buildExpression(group.value.value, itOptions);
        norm_str += "}";
    } else if (original === "\\sf") {
        // \sf转换为\mathsf
        norm_str += "\\mathsf{";
        var sfOptions = Object.assign({}, options, {
            font: "mathsf",
            inFont: true
        });
        buildExpression(group.value.value, sfOptions);
        norm_str += "}";
    } else {
        // 其他大小命令: \tiny, \small, \large等，保持原样
        norm_str += original;
        buildExpression(group.value.value, options);
    }

    addSpaceIfNeeded('sizing');
};

// 上划线处理
groupTypes.overline = function(group, options) {
    // 输入: 上划线AST节点，包含需要加上划线的表达式
    // 输出: \overline{表达式}
    norm_str = norm_str + "\\overline { ";

    // 处理上划线内的表达式
    buildGroup(group.value.body, options);
    norm_str = norm_str + "} ";
};

// 下划线处理
groupTypes.underline = function(group, options) {
    // 输入: 下划线AST节点，包含需要加下划线的表达式
    // 输出: \underline{表达式}
    norm_str = norm_str + "\\underline { ";

    // 处理下划线内的表达式
    buildGroup(group.value.body, options);
    norm_str = norm_str + "} ";
};

// 规则线处理 (绘制指定尺寸的线条)
groupTypes.rule = function(group) {
    // 输入: 规则线AST节点，包含宽度和高度信息
    // 输出: \rule{宽度}{高度}

    // 格式化输出: \rule { 宽度数值 单位 } { 高度数值 单位 }
    norm_str = norm_str + "\\rule { " + group.value.width.number + " " + group.value.width.unit + " } { " + group.value.height.number + " " + group.value.height.unit + " } ";
};

// 左重叠处理 (左对齐重叠)
groupTypes.llap = function(group, options) {
    // 输入: 左重叠AST节点
    // 输出: \llap{内容} - 内容向左重叠，不占用水平空间
    norm_str = norm_str + "\\llap ";
    buildGroup(group.value.body, options);
};

// 右重叠处理 (右对齐重叠)
groupTypes.rlap = function(group, options) {
    // 输入: 右重叠AST节点
    // 输出: \rlap{内容} - 内容向右重叠，不占用水平空间
    norm_str = norm_str + "\\rlap ";
    buildGroup(group.value.body, options);
};

// 幻影处理 (占位但不显示)
groupTypes.phantom = function(group, options, prev) {
    // 输入: 幻影AST节点，包含需要隐藏但保留空间的表达式
    // 输出: \phantom{表达式} - 表达式不可见但占用空间
    norm_str = norm_str + "\\phantom { ";

    // 处理幻影内的表达式
    buildExpression(group.value.value, options);
    norm_str = norm_str + "} ";
};

/**
 * 表达式构建函数 - AST渲染的核心递归函数 (增强版)
 *
 * 功能: 遍历AST节点列表，逐个调用相应的处理函数
 * 数据流: AST节点数组 → 逐个处理 → 累积到norm_str
 *
 * @param {Array} expression - AST节点数组
 * @param {Object} options - 渲染选项(字体、样式等)
 */
var buildExpression = function(expression, options) {
    if (!expression || !Array.isArray(expression)) {
        return;
    }

    // 遍历表达式中的每个AST节点
    for (var i = 0; i < expression.length; i++) {
        var group = expression[i];
        if (group) {
            // 递归处理每个节点
            buildGroup(group, options);
        }
    }
};

/**
 * 单个AST节点处理函数 - 分发器 (增强版)
 *
 * 功能: 根据AST节点类型调用相应的groupTypes处理函数
 * 数据流: AST节点 → 类型识别 → 调用对应处理函数 → 更新norm_str
 *
 * @param {Object} group - 单个AST节点
 * @param {Object} options - 渲染选项
 */
var buildGroup = function(group, options) {
    if (!group || !group.type) {
        return;
    }

    try {
        if (groupTypes[group.type]) {
            // 调用对应类型的处理函数
            groupTypes[group.type](group, options);
        } else {
            // 未知节点类型的降级处理
            console.warn("Unknown group type: '" + group.type + "', attempting fallback");

            // 尝试基本的文本输出
            if (group.value) {
                norm_str += group.value;
                addSpaceIfNeeded('unknown');
            }
        }
    } catch (error) {
        // 节点处理错误的降级处理
        console.error("Error processing group type '" + group.type + "':", error);

        // 尝试输出基本信息
        if (group.value) {
            norm_str += group.value;
        }
    }
};


